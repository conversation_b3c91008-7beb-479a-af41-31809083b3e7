const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

/** 
 * Passive plugin that checks if clickjacking protection using the X-Frame-Options/Content-Security-Policy 
 * header is enabled on a page
 */
class ClickjackingHeaderCheck extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the ClickjackingHeaderCheck plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-clickjacking-header-check'
    }

    /**
     * Will check that X-Frame-Options header and Content-Security-Policy header is set
     * X-Frame-Options header has to be set to one of the allowed values: DENY, SAMEORIGIN
     * See: {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options}
     * 
     * Content-Security-Policy Header should have frame-ancestors set
     * See: {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors}
     * 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //Plugin request scan scope - this scan, means to report one only in entire scan
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //Dont report more then one AA Alert
        if (pluginDataForRequest.clickjackingVulnFound) return;

        //Dont report more then one 404 - AA Alert
        if (pluginStorageScanScope.duplicatealert404) return;

        if (!/get|post/i.test(attack.httpRequest.method)) return;

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        if (!/^(?:200|201|202|203|206|404)$/.test(statusCode)) return;

        let ResBody = _.get(attack, 'result.resp.body', '')
        if (!ResBody.trim()) return;
        if (ResBody.length < 30 && !/[<>]/.test(ResBody) && !/=\s?('|")(\/\w|http)/.test(ResBody)) return;


        let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        if (/application\/json|text\/plain|application\/octet-stream|multipart\/form-data|application\/x-www-form-urlencoded|audio\/.*|video\/.*|image\/(?!svg\+xml).*|font\/.*/i.test(RescontentType)) return;


        //condition to skip for the custom error pages
        const responseBody = ResBody.toLowerCase();

        // Check each category of error messages
        const errorCategories = [
            RegExpVari.ErrorMessages.HttpErrors,
            RegExpVari.ErrorMessages.SecurityErrors,
            RegExpVari.ErrorMessages.SessionErrors,
            RegExpVari.ErrorMessages.SystemErrors,
            RegExpVari.ErrorMessages.WafErrors,
            RegExpVari.ErrorMessages.GeneralErrors
        ];

        // Early return if any error message is found
        for (const category of errorCategories) {
            if (category.some(error => responseBody.includes(error))) {
                return;
            }
        }

        // Check for WAF servers in headers
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

        if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
            return;
        }
        
        // Avoid FP
        if (statusCode == 404 && /URL(?: that)? No Longer Exists/i.test(ResBody)) return;

        if (attack.attackArea != 'original-crawler-request' || attack.pluginName != 'Original Crawler Request') {
            try {
                if (attack.vector && attack.vector.length > 0) {
                    let regexp = new RegExp(_.escapeRegExp(attack.vector))
                    if (!regexp.test(ResBody) || !ResBody.includes('<script>haikumsg(326)</script>')) {
                        return
                    }
                }
                // Adding the condition to make sure that Host values are same in the original and attack requests
                let att_host = _.get(attack, 'httpRequest.headers.Host')
                let orig_host = _.get(attack, 'originalRequest.httpRequest.headers.Host')
                if (att_host != orig_host) {
                    return
                }
            } catch (e) { return }
        }

        let uri = new URL(attack.href)
        //RegExp: pathname created by haiku for checking vuln
        let pathname_RegExp = /\/jsmol2wp\/php\/jsmol\.php|\/owa\/auth\/x\.js|\/DSWAPutTest\.txt/i
        if (pathname_RegExp.test(uri.pathname)) return;


        let xFrameOptions = _.get(attack, 'result.resp.httpResponse.headers["x-frame-options"]', '').toLowerCase();
        let cspHeader = _.get(attack, 'result.resp.httpResponse.headers["content-security-policy"]', '').toLowerCase();

        // Define insecure or weak values
        let xfoWeakValues = /allow-from|allowall/i; // "ALLOW-FROM" is deprecated, "ALLOWALL" is weak
        let cspWeakValues = /frame-ancestors\s+(\*|https?:\/\/\*\.)/i; // Allows all or any subdomain

        // Check if Clickjacking Protection is Missing or Weak
        let xfoVulnerable = !/deny|sameorigin/i.test(xFrameOptions) || xfoWeakValues.test(xFrameOptions);
        let cspVulnerable = !/frame-ancestors\s+('none'|self|https?:\/\/[^;]+)/i.test(cspHeader) || cspWeakValues.test(cspHeader);

        if (xfoVulnerable && cspVulnerable) {
            let details = {
                result: `Vulnerable: Page is missing or has weak X-Frame-Options (${xFrameOptions || "missing"}) and frame-ancestors CSP (${cspHeader || "missing"}), making it potentially vulnerable.\n Impact: Attackers may be able to embed this page in an iframe and trick users into interacting with hidden UI elements, leading to possible Clickjacking attacks.`
            };

            // Add this vulnerability to the list
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details);
            pluginDataForRequest.clickjackingVulnFound = true;

            // Check for duplicate alert when status code is 404
            if (statusCode === 404) {
                pluginStorageScanScope.duplicateAlert404 = true;
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "Host"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["x-frame-options", "content-security-policy"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

module.exports = ClickjackingHeaderCheck