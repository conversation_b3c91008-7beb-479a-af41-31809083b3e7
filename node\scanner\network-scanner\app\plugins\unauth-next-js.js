const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const { checkLogin } = require('./loginChecker')
const request = require('request')
const logger = require('../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class UnauthNextJS extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-unauth-next-js'
        this.vulnerabilityID_blackbox = 'ID-unauth-next-js-blackbox-test'
        this.authRegex = /password|passwd?|pwd|(?:login|auth|user)_pass|user_secret|passcode/i;

    }

    getAttackVectors() {
        return AttackerVectors
    }

    getAttackableEvents() {
        return ['http-headers']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['x-middleware-subrequest']
            })
        }
    }

    async performNetworkAttack(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.authnextjsfound) return false;
        let requestBody = _.get(attack, 'httpRequest.body', '')
        if (this.authRegex.test(attack.httpRequest.uri) || this.authRegex.test(requestBody)) return false;

        return await super.performNetworkAttack(attack)
    }

    async processAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 0);
        if (statusCode != 200) return;

        let ReqBody = _.get(attack, 'httpRequest.body', '')
        if (attack.attackArea == 'original-crawler-request' && attack.pluginName == 'Original Crawler Request') {
            if (!pluginStorageScanScope.isAuthScan && (this.authRegex.test(ReqBody) || this.authRegex.test(attack.href))) {
                let isAuthPage = await this.isAuthPage(attack);
                if (isAuthPage.success) {
                    pluginStorageScanScope.isAuthScan = true;
                    return;
                }
            }
        }

        // If the request is for a password/login page, then we don't need to check for the vulnerability
        let passwdPage = this.authRegex.test(ReqBody) || this.authRegex.test(attack.href)
        if (passwdPage) return;

        let responseBody = _.get(attack, "result.resp.body").trim();
        let checkVersion = false;
        if (pluginStorageScanScope.isAuthScan && attack.pluginName == this.getName() && !pluginStorageScanScope.authnextjsfound) {
            checkVersion = true;
            const headers = _.get(attack, 'result.resp.httpResponse.headers', {});
            for (const header of nextjs_res_headers) {
                if (headers[header]) {
                    if (header == 'x-powered-by' && !/Next\.js/i.test(headers[header])) {
                        continue;
                    }
                    let details = `Improper Authorization found in Next.js application. Response Header: ${header}: ${headers[header]}`
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                    pluginStorageScanScope.authnextjsfound = true; // Mark the scan as completed and no more scans will be performed.
                    return true
                }
            }

            if (responseBody.length === 0 || responseBody.length < 20) return;
            for (const pattern of nextjs_body_patterns) {
                if (pattern.test(responseBody)) {
                    if (pattern.toString().includes('__NEXT_DATA__') && !/("buildId"\s*:\s*"\w+"\s*,\s*"assetPrefix"\s*:\s*"\S+"|"runtime"\s*:\s*"edge"|"nextExport"\s*:\s*true|"gsp"\s*:\s*true|"gssp"\s*:\s*true|"isFallback"\s*:\s*false|"autoExport"\s*:\s*true)/i.test(responseBody)) {
                        continue;
                    }
                    let details = `Improper Authorization found in Next.js application. Context: ${responseBody.match(pattern)[0]}`
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                    pluginStorageScanScope.authnextjsfound = true; // Mark the scan as completed and no more scans will be performed.
                    return true
                }
            }
        }
        //If Vuln not detected using the above code, then we will check the version
        if (checkVersion || !pluginStorageScanScope.isAuthScan || !pluginStorageScanScope.authnextjsfound) {
            // BlackBox Testing.
            // Below is the code for the nextjs version detection
            if (responseBody.length === 0 || responseBody.length < 20) return;
            for (const pattern of nextjs_body_patterns) {
                if (pattern.test(responseBody)) {
                    if (pattern.toString().includes('__NEXT_DATA__') && !/("buildId"\s*:\s*"\w+"\s*,\s*"assetPrefix"\s*:\s*"\S+"|"runtime"\s*:\s*"edge"|"nextExport"\s*:\s*true|"gsp"\s*:\s*true|"gssp"\s*:\s*true|"isFallback"\s*:\s*false|"autoExport"\s*:\s*true)/i.test(responseBody)) {
                        continue;
                    }

                    // https://hellobpcl.in/_next/static/chunks/main-c424f846cc3cb03d.js
                    // <script src="/_next/static/chunks/main-c424f846cc3cb03d.js" defer=""></script>
                    if (/<script src="(?:https?:\/\/[^\/])?\/_next\/static\/chunks\/main-[^.]+\.js"/i.test(responseBody)) {
                        if (attack.hostname.includes('indusface.com ')) { // The log enable for https://docs.indusface.com/en
                            logger.info(`Found Next.js script tag`, HaikuUtils.getMetadataForLog(attack));
                        }
                        let scriptUrl = responseBody.match(/<script src="((?:https?:\/\/[^\/])?\/_next\/static\/chunks\/main-[^.]+\.js)"/i)[1];
                        if (!scriptUrl.includes('http')) {
                            let url = new URL(attack.href);
                            scriptUrl = `${url.origin}${scriptUrl}`;
                        }
                        let getVersion = await this.getVersion(scriptUrl);
                        if (getVersion) {
                            /**
                             * Mitigation: To mitigate this vulnerability, it is recommended to update your Next.js application to the latest patched versions:
                             * Next.js 15.x: Update to version 15.2.3 or later
                             * Next.js 14.x: Update to version 14.2.25 or later
                             * Next.js 13.x: Update to version 13.5.9 or later
                             * Next.js 12.x: Update to version 12.3.5 or later
                             */
                            if ((getVersion.startsWith('15.') && getVersion < '15.2.3') || (getVersion.startsWith('14.') && getVersion < '14.2.25') || (getVersion.startsWith('13.') && getVersion < '13.5.9') || getVersion < '12.3.5') {
                                let details = `Possible to Improper Authorization vulnerability in Next.js application. We have detected that the Next.js version: ${getVersion}. It is vulnerable to Improper Authorization vulnerability CVE-2025-29927. Please update your Next.js application to the latest patched versions.`
                                this.addVulnerabilitytoResult(attack, this.vulnerabilityID_blackbox, details)
                                pluginStorageScanScope.authnextjsfound = true; // Mark the scan as completed and no more scans will be performed.
                                return;
                            }
                        }
                    }
                }
            }
        }
    }
    async isAuthPage(attack) {
        return new Promise((resolve) => {
            let result = checkLogin(attack);
            if (result) {
                resolve(result); // Return the result
            } else {
                resolve(false); // Return false if the result is not successful 
            }
        });
    }

    async getVersion(url) {
        return new Promise((resolve) => {
            try {
                request.get(url, (error, response, body) => {
                    if (error) {
                        resolve(null);
                    } else {
                        if (response && response.statusCode == 200) {
                            //t.render=he,t.renderError=ve,t.emitter=t.router=t.version=void 0,r(40037); //wont match
                            //t.version="12.0.10"; //match
                            let version = null;
                            if (body.includes('t.version')) {
                                version = body.match(/[^=]\s*t\.version\s*[:=]\s*"([\w\.\-]+)"/i)
                                if (version) {
                                    resolve(version[1]);
                                }
                            }
                            else if (/version\s*:\s*function\(/.test(body)) {
                                let getVarName = body.match(/[^=]version\s*:\s*function\(\)\s*\{\s*return\s*(\w+)\s*\}/i)
                                if (getVarName) {
                                    let varName = getVarName[1];
                                    // version:function(){return q} q="13.4.19"
                                    let regex = new RegExp(`[^=]\\s*${varName}\\s*[:=]\\s*"([\\w\\.\\-]+)"`);
                                    version = body.match(regex);
                                    if (version) {
                                        resolve(version[1]);
                                    }
                                }
                            }
                        } else {
                            resolve(null);
                        }
                    }
                });
            } catch (error) {
                resolve(null);
            }
        });
    }
}

// nextjs response headers to confirm the Next.js application
const nextjs_res_headers = [
    'x-powered-by',             // Older Next.js versions may expose this
    'x-nextjs-cache',           // Next.js caching mechanism
    'x-nextjs-data',            // Next.js dynamic data fetching
    'x-nextjs-redirect',        // Next.js internal redirect handling
    'x-nextjs-matched-path',    // Used in Next.js route matching
    'x-middleware-next'         // Middleware processing in Next.js
];


// nextjs response body content to confirm the Next.js application
const nextjs_body_patterns = [
    // ✅ Next.js Head Count (Most Reliable)
    /<meta name="next-head-count" content="\d+"/,
    /<div id="__next">/i,

    // ✅ Next.js Static & Data Paths (Avoid generic matches)
    /href=["']\/_next\/static\//i,
    /src=["']\/_next\/static\//i,
    /href=["']\/_next\/data\//i,
    /src=["']\/_next\/data\//i,

    // ✅ Next.js __NEXT_DATA__ structure (Most Reliable)
    /"__NEXT_DATA__"\s*:\s*{/i,

    // ✅ Next.js Page Routing Pattern (Ensures it's used within Next.js structure)
    /"page"\s*:\s*"\/[^"]*"\s*,\s*"query"\s*:\s*{[^}]*}/i
];

const AttackerVectors = [
    //below is the exact file vector to be looked upon
    `true`,
    `1`,
    `middleware`,
    `src/middleware`,
    `middleware:middleware:middleware:middleware:middleware`,
    `src/middleware:src/middleware:src/middleware:src/middleware:src/middleware`,
    `pages/_middleware`,
    `pages/dashboard/_middleware`,
    `pages/dashboard/panel/_middleware`,
]

module.exports = UnauthNextJS