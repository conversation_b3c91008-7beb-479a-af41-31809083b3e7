const debug = require('debug')('ASPNETTraceMethodPlugin')
const VectorResponseAttack = require('./vector-response-attack')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/** 
 * VectorResponse style plugin that checks for ASP NET Trace Method Enabled
 */
class ASPNETTraceMethodPlugin extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-ASPNET-TRACE-method-enabled'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            maxPathComponents: 1,
            clearQueryParams: true,
            addSlashBeforeAttack: true,
        });
    }
    getAttackVectors() {
        return CRDVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        /**
        * server:Microsoft-IIS\/8.5
        * x-aspnet-version:4.0.30319
        * x-powered-by:ASP.NET
        * */
        let currserver = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        let frameworkdts = _.get(attack, 'result.resp.httpResponse.headers.x-powered-by', '')
        let aspnetdts = _.get(attack, 'result.resp.httpResponse.headers.x-aspnet-version', '')
        let aspnetmvcdts = _.get(attack, 'result.resp.httpResponse.headers.x-aspnetmvc-version', '')
        if (/IIS/i.test(currserver) || /asp/i.test(frameworkdts) || aspnetdts.length > 0 || aspnetmvcdts.length > 0) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    /**
      
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let bodycheck = attack.result.resp.body
        if (/<td><h1>Application\sTrace/.test(bodycheck) && /<td>Physical\sDirectory:/.test(bodycheck)
            && /<th>Time\sof\sRequest/.test(bodycheck) && /<th>Status\sCode/.test(bodycheck)
            && /Microsoft\s.NET\sFramework/.test(bodycheck)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['Application', 'Physical', 'Microsoft NET']);
    }
}

// vectors & matches ...
const CRDVectors = [
    `trace.axd`,
    `Trace.axd`,
]

module.exports = ASPNETTraceMethodPlugin