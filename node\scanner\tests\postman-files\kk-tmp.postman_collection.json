{"info": {"_postman_id": "3c9d44b8-05dd-47b7-a486-33d6242e64ab", "name": "kk-tmp", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "test-folder", "item": [{"name": "auth-req", "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "aa", "value": "{{test-coll-var}}", "type": "text"}, {"key": "bb", "value": "{{test-global}}", "type": "text"}]}, "url": {"raw": "https://xxx.yyy.zzz/auth-req?fromvar={{global}}", "protocol": "https", "host": ["xxx", "yyy", "zzz"], "path": ["auth-req"], "query": [{"key": "fromvar", "value": "{{global}}"}]}}, "response": []}]}, {"name": "unauth req", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [], "url": {"raw": "https://xxx.yyy.zzz/a-get-req?a=b", "protocol": "https", "host": ["xxx", "yyy", "zzz"], "path": ["a-get-req"], "query": [{"key": "a", "value": "b"}]}}, "response": []}, {"name": "for <PERSON><PERSON><PERSON>", "request": {"method": "GET", "header": [], "url": null}, "response": []}], "auth": {"type": "apikey", "apikey": [{"key": "in", "value": "query", "type": "string"}, {"key": "value", "value": "theval", "type": "string"}, {"key": "key", "value": "thekey", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "global", "value": "88"}, {"key": "test-coll-var", "value": "test"}]}