// RPC server 
RMQ = require('../common/lib/rabbitmq')
QueueMessage = require('../common/lib/queue-message')
msgQ = new RMQ('scanner')
msgQ.init()

msgQ.consume('request', (msg) => {
    msgQ.ack(msg);
    console.log('got msg', msg);
    // crack open the msg and perform whatever action we need to 
    let requestMsg = QueueMessage.createMessage(msg)
    theReply = {
        msg: 'this is the reply',
        themqMsgIs: msg
    }
    setTimeout(() => {
        // msgQ.reply(msg, new Buffer('this is the reply'))
        requestMsg.rpcReply(msgQ, theReply)
    }, 3 * 1000 + Math.random() * 10)
})

// RPC client
RMQ = require('./common/lib/rabbitmq')
GetSitemapUrlsRpc = require('../common/lib/messages/get-sitemap-urls-rpc')
msgQ = new RMQ('scanner')
msgQ.init()

// pp = msgQ.request('scanner', 'request.test', new Buffer('test req'))
getSiteMaps = new GetSitemapUrlsRpc({
    req: 'this is teh request'
})
pp = getSiteMaps.rpcRequest(msgQ)
pp.then((rep) => {
    console.log(rep)
})
msgQ.outstandingRequests

// ---- 
// Direct scanner test by simulating crawler
// ---- 

RMQ = require('./common/lib/rabbitmq')
msgQ = new RMQ('crawler')
msgQ.init()

config = {
    msgQ: msgQ
}
EventEmitter = require('events')
scnr = new EventEmitter
scnr.config = config

ANR = require('./app/plugins/network/send-network-action-to-scanner')
aa = new ANR(scnr)

nw_req = {
    "id": "301",
    "scanlog_id": 999,
    "scanner": "haiku",
    "networkActions": [{
        "url": "http://www.webscantest.com/datastore/search_double_by_name.php",
        "method": "POST",
        "headers": {
            "Host": "www.webscantest.com",
            "Connection": "keep-alive",
            "Content-Length": "9",
            "Cache-Control": "max-age=0",
            "Origin": "http://www.webscantest.com",
            "Upgrade-Insecure-Requests": "1",
            "Content-Type": "application/x-www-form-urlencoded",
            "User-Agent": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "Referer": "http://www.webscantest.com/datastore/search_double_by_name.php",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8",
            "Cookie": "TEST_SESSIONID=njepb3hocg7ki7ckk391pk8lr0; NB_SRVID=srv140717"
        },
        "body": "name=Rake",
        "key": "kk"
    }]
}

scnr.emit('network-action', nw_req)