const fs = require('fs')
const dayjs = require('dayjs')
const customParseFormat = require('dayjs/plugin/customParseFormat')
dayjs.extend(customParseFormat)
const glob = require('glob')
const csvStringify = require('csv-stringify/lib/sync')
const lineByLine = require('n-readlines');

const maxUnprocessedLines = 6000        // how many log lines tp process at one time, this many K will be rad from log file in one chunk

// globals.
let entriesProcessed = 0
let entriesWritten = 0
let filesWritten = 0
let fullResult = []
let summary = {
    totalBytes: 0,
    upstreamTotalBytes: 0,
    invalidLines: 0,
    overhead: 0,
    upOverhead: 0,
    minTime: Date.now(),
    maxTime: -1
}

let parseAcessLogLine = parseApacheAcessLogLine

// From: Datapipeline, logparser/service/AccessLogParser.java
PREV_ACCESS_RX = /^([\d.]+)(?::\d+)*.*?\[(.+)?\] \"(.*?)" (\d+).*\   "\s(\d+) (\d+)\s*(\d*)/i
ACCESS_RX = /^([\\d.]+)(?::\\d+)*.*?\\[(.+)?\\] \\\"(.*?)\" (\\d+).*\\\"\\s(\\d+) (\\d+)\\s*(\\d*)/i
PREV_ACCESS_IPV6_RX = /^([a-fA-F0-9:]+)(?::\d+)*.*?\[(.+)?\] \"(.*?)" (\d+).*\"\s(\d+) (\d+)\s*(\d*)/i
ACCESS_IPV6_RX = /^([a-fA-F0-9:]+)(?::\\d+)*.*?\\[(.+)?\\] \\\"(.*?)\" (\\d+).*\\\"\\s(\\d+) (\\d+)\\s*(\\d*)/i
METHOD_URI_RX = /(\w+) (\S+)/i
//log_format main '$remote_addr :- [$time_local] $ssl_preread_server_name $protocol $status $bytes_received $bytes_sent $upstream_bytes_sent $upstream_bytes_received';
NGINX_ACCESS_RX = /^([\d.]+).*?\[(.+)?\] (.+?) (?:HTTP\S+|TCP) (\d+)\s+(\d+)\s+(\d+)(?:\s+(\d+)\s+(\d+))?/i

// Adapted from: Datapipeline, logparser/service/AccessLogParser.java, isIPV6()
function isIPV6(line) {
    let posColon = line.indexOf(':')
    return posColon != -1 && posColon < line.indexOf(' -')
}

// Adapted from: Datapipeline, logparser/service/AccessLogParser.java, getBaseMatch()
function parseTimestamp(rawTimeStamp) {
    let parsedDate = dayjs(rawTimeStamp, 'DD/MMM/YYYY:HH:mm:ss ZZ')
    return parsedDate.isValid() ? parsedDate.toDate() : null
}

// Adapted From: Datapipeline, logparser/service/AccessLogParser.java, getBaseMatch()
// will parse a single access log line and get teh basic info eg. not the sessionIDs
// currently: clientIP,rawTimeStamp,timeStamp,rawReqLine,method,uri,responseCode,inputBytes,outputBytes,timeTaken
function prev_parseApacheAcessLogLine(line) {
    let matcher = isIPV6(line) ? ACCESS_IPV6_RX.exec(line) : ACCESS_RX.exec(line)
    if (!matcher) {
        return null
    }

    let accessLogEntry = {}
    accessLogEntry.clientIP = matcher[1]
    accessLogEntry.rawTimeStamp = matcher[2]
    accessLogEntry.timeStamp = parseTimestamp(accessLogEntry.rawTimeStamp)
    accessLogEntry.rawReqLine = matcher[3]
    let matchInner = METHOD_URI_RX.exec(accessLogEntry.rawReqLine)
    if (matchInner) {
        accessLogEntry.method = matchInner[1]
        accessLogEntry.uri = matchInner[2]
    }
    accessLogEntry.responseCode = matcher[4]
    accessLogEntry.inputBytes = +matcher[5]
    accessLogEntry.outputBytes = +matcher[6]
    accessLogEntry.totalBytes = accessLogEntry.inputBytes + accessLogEntry.outputBytes
    accessLogEntry.timeTaken = matcher[7]

    // Summary stuff
    // keep track of min and max times
    if (accessLogEntry.timeStamp && summary.minTime > accessLogEntry.timeStamp.valueOf()) { summary.minTime = accessLogEntry.timeStamp.valueOf() }
    if (accessLogEntry.timeStamp && summary.maxTime < accessLogEntry.timeStamp.valueOf()) { summary.maxTime = accessLogEntry.timeStamp.valueOf() }

    // function tcpOverhead(bytes) {
    //     return bytes > 0 ? (1 + Math.floor(bytes / 1460)) * 40 : 40
    // }
    // summary.overhead += tcpOverhead(accessLogEntry.inputBytes)
    // summary.overhead += tcpOverhead(accessLogEntry.outputBytes)

    return accessLogEntry
}

// Adapted From: Datapipeline, logparser/service/AccessLogParser.java, getBaseMatch()
// will parse a single access log line and get teh basic info eg. not the sessionIDs
// currently: clientIP,rawReqLine,method,uri,responseCode,inputBytes,outputBytes,timeTaken
// changed to use new log format i.e. the [response-code 304] style
/*
*/
function parseApacheAcessLogLine(line) {
    let accessLogEntry = {}
    accessLogEntry.clientIP = /\[client (.+)?\]/.exec(line)[1]
    accessLogEntry.rawTimeStamp = +/\[time (\d+)\]/.exec(line)[1]
    accessLogEntry.timeStamp = new Date(accessLogEntry.rawTimeStamp)
    accessLogEntry.rawReqLine = /\[request-line (.+)?\]/.exec(line)[1]
    let matchInner = METHOD_URI_RX.exec(accessLogEntry.rawReqLine)
    if (matchInner) {
        accessLogEntry.method = matchInner[1]
        accessLogEntry.uri = matchInner[2]
    }
    accessLogEntry.responseCode = /\[response-code (\d+)\]/.exec(line)[1]
    accessLogEntry.inputBytes = +/\[bytes-in (\d+)\]/.exec(line)[1]
    accessLogEntry.outputBytes = +/\[bytes-out (\d+)\]/.exec(line)[1]
    accessLogEntry.totalBytes = accessLogEntry.inputBytes + accessLogEntry.outputBytes
    accessLogEntry.timeTaken = /\[response-time (\d+)\]/.exec(line)[1]

    // Summary stuff
    // keep track of min and max times
    if (accessLogEntry.timeStamp && summary.minTime > accessLogEntry.timeStamp.valueOf()) { summary.minTime = accessLogEntry.timeStamp.valueOf() }
    if (accessLogEntry.timeStamp && summary.maxTime < accessLogEntry.timeStamp.valueOf()) { summary.maxTime = accessLogEntry.timeStamp.valueOf() }

    // function tcpOverhead(bytes) {
    //     return bytes > 0 ? (1 + Math.floor(bytes / 1460)) * 40 : 40
    // }
    // summary.overhead += tcpOverhead(accessLogEntry.inputBytes)
    // summary.overhead += tcpOverhead(accessLogEntry.outputBytes)

    return accessLogEntry
}



//log_format main '$remote_addr :- [$time_local] $ssl_preread_server_name $protocol $status $bytes_received $bytes_sent $upstream_bytes_sent $upstream_bytes_received';
// upstream is optional.
function parseNginxAcessLogLine(line) {
    let matcher = NGINX_ACCESS_RX.exec(line)
    if (!matcher) {
        return null
    }

    let accessLogEntry = {}
    accessLogEntry.clientIP = matcher[1]
    accessLogEntry.rawTimeStamp = matcher[2]
    accessLogEntry.timeStamp = parseTimestamp(accessLogEntry.rawTimeStamp)
    accessLogEntry.server = matcher[3]
    accessLogEntry.responseCode = matcher[4]

    // sent, received bytes
    accessLogEntry.rcvdBytes = +matcher[5]
    accessLogEntry.sentBytes = +matcher[6]
    accessLogEntry.totalBytes = accessLogEntry.rcvdBytes + accessLogEntry.sentBytes

    //upstream sent, received bytes
    accessLogEntry.upSentBytes = +matcher[7] || 0
    accessLogEntry.upRcvdBytes = +matcher[8] || 0
    accessLogEntry.upstreamTotalBytes = accessLogEntry.upSentBytes + accessLogEntry.upRcvdBytes

    // Summary stuff
    // keep track of min and max times
    if (accessLogEntry.timeStamp && summary.minTime > accessLogEntry.timeStamp.valueOf()) { summary.minTime = accessLogEntry.timeStamp.valueOf() }
    if (accessLogEntry.timeStamp && summary.maxTime < accessLogEntry.timeStamp.valueOf()) { summary.maxTime = accessLogEntry.timeStamp.valueOf() }

    function tcpOverhead(bytes) {
        return bytes > 0 ? (1 + Math.floor(bytes / 1460)) * 40 : 40
    }
    summary.overhead += tcpOverhead(accessLogEntry.rcvdBytes)
    summary.overhead += tcpOverhead(accessLogEntry.sentBytes)
    summary.upOverhead += tcpOverhead(accessLogEntry.upRcvdBytes)
    summary.upOverhead += tcpOverhead(accessLogEntry.upSentBytes)

    return accessLogEntry
}

// parse file into array of access log lines entries
function parseFile(file, filter) {
    const liner = new lineByLine(file, { readChunk: 256 * 1024 });   // 256K chunks
    let unprocessedLines = []
    let linesProcessed = 0
    let linesAfterFiltering = 0
    let line
    while (line = liner.next()) {
        unprocessedLines.push(line.toString('ascii'))
        if (unprocessedLines.length >= maxUnprocessedLines) {
            linesProcessed += unprocessedLines.length
            linesAfterFiltering += processLines(unprocessedLines.map(parseAcessLogLine))
            unprocessedLines = []
        }
    }

    // remaining part
    if (unprocessedLines.length) {
        linesProcessed += unprocessedLines.length
        linesAfterFiltering += processLines(unprocessedLines.map(parseAcessLogLine))
    }
    console.log(`\n\tTotal lines processed ${linesProcessed}, after filtering ${linesAfterFiltering}`)
}

// process parsed liog entries - this is a chunk of the file since files can be large
function processLines(accessLogEntries) {
    console.log(`\tprocessed ${accessLogEntries.length} lines`)
    entriesProcessed += accessLogEntries.length

    let nullLines = accessLogEntries.reduce((a, e) => e ? a : a + 1, 0)
    summary.invalidLines += nullLines

    // filter
    accessLogEntries = accessLogEntries.filter(e => e) // always filter out null
    if (!isNoFiltering) {
        accessLogEntries = accessLogEntries.filter(filterLogLines)
    }
    fullResult = fullResult.concat(...accessLogEntries)
    console.log(`\tafter filtering, added ${accessLogEntries.length} lines`)

    // Write csv file if we exceeded max enteries per csv
    if (fullResult.length >= maxRowsPerCsv) {
        // Run any summarizing needed. Updates global summary
        summarize()

        // write CSV (will change globals like fullResult & filesWritten)
        writeCsvFile()
    }

    return accessLogEntries.length
}

// filter log line eg. to get only certain time or only certain IPs
// not generalized to take filters from command line. Customize by writing filter code
function filterLogLines(accessLogEntry) {
    // filter: don't consider 406, 403 status codes
    const skipStatuCodes = ["406", "403"]
    //return !_matchStatusCodes(accessLogEntry, skipStatuCodes)

    // filter: 17:00-18:00, one uri, one ip
    const afterWhen = new Date('2023-03-24T05:50:00.000Z').valueOf()
    const beforeWhen = new Date('2023-03-24T05:51:00.000Z').valueOf()
    // const uriPattern = /services\/oauth\/token/i
    // const IPs = ['************']

    return !_matchStatusCodes(accessLogEntry, skipStatuCodes) &&
        _after(accessLogEntry, afterWhen) &&
        _before(accessLogEntry, beforeWhen) //&&
    // _matchUri(accessLogEntry, uriPattern) &&
    // _matchIP(accessLogEntry, IPs)

    // filter: 17:00-18:00, one uri
    // const afterWhen = new Date('2020-11-12 17:00:00 Z').valueOf()
    // const beforeWhen = new Date('2020-11-12 18:00:00 Z').valueOf()
    // const uriPattern = /services\/oauth\/token/i

    // return _after(accessLogEntry, afterWhen) &&
    //     _before(accessLogEntry, beforeWhen)
    //     _matchUri(accessLogEntry, uriPattern)

    // helper filter functions
    function _after(entry, when) {
        return entry.timeStamp.valueOf() >= when
    }

    function _before(entry, when) {
        return entry.timeStamp.valueOf() < when
    }

    // uri
    function _matchUri(entry, pat) {
        return pat.test(entry.uri)
    }

    // ips
    function _matchIP(entry, IPs) {
        return IPs.includes(entry.clientIP)
    }

    // skip status codes
    function _matchStatusCodes(entry, statusCodesToSkip) {
        return statusCodesToSkip.includes(entry.responseCode)
    }
}

// Summmazrize single values in
function summarize() {
    summary.totalBytes = fullResult.reduce((acc, entry) => acc + entry.totalBytes, summary.totalBytes)
    summary.upstreamTotalBytes = fullResult.reduce((acc, entry) => acc + entry.upstreamTotalBytes, summary.upstreamTotalBytes)
}

// -- set up the csv conversion options
// Excel uses very defined formats while parsing csv.
function toExcelParsableDate(d) {
    return d.toISOString().replace(/T/, ' ').replace(/\..+/, '')
}
const csvOptions = {
    header: true,
    cast: {
        date: toExcelParsableDate
    }
}

// Write CSV file. USES GLOBALS
function writeCsvFile() {
    entriesWritten += fullResult.length
    filesWritten++
    if (!isDryRun) {
        fs.writeFileSync(`./${csvFilePrefix}_${filesWritten}.csv`, csvStringify(fullResult, csvOptions))
    }
    fullResult = []
}

// --- Main Code ---
if (!process.argv[2]) {
    console.log("Must pass argument with filespec of log files to process eg '../accesslogs/2020-11-12*.log'")
    process.exit(1)
}

// parse arguments
let argv = require('minimist')(process.argv.slice(2), {
    boolean: true,
    alias: {
        max: "maxRowsPerCsv",
        prefix: "csvPrefix",
        dryrun: "dry-run",
        f: "filespec",
        l: "listoffiles"
    }
})

// excel max is 1M. Keeping as 100K here. Need to increase node heap if we do the 1M row thing
const maxRowsPerCsv = argv.maxRowsPerCsv || 100000
const csvFilePrefix = argv.csvPrefix || 'parsed'
const isDryRun = !!argv['dry-run']
const isNoFiltering = !!argv['nofilter']
const isNginx = !!argv['nginx']
const start = Date.now()
console.log(`running with args: --maxRowsPerCsv=${maxRowsPerCsv} --csvPrefix=${csvFilePrefix} ${argv.filespec ? '--filespec' + argv.filespec : ''} ${argv.listoffiles ? '--listoffiles=' + argv.listoffiles : ''} ${isNginx ? '--nginx' : ''} ${isDryRun ? '--dry-run' : ''} ${isNoFiltering ? '--nofilter' : ''}`)

if (isNginx) {
    parseAcessLogLine = parseNginxAcessLogLine
}

// Parse access logs and write csv
let files = []
if (argv.filespec) {
    files = glob.sync(argv.filespec, { nodir: true })
} else if (argv.listoffiles) {
    files = fs.readFileSync(argv.listoffiles, 'utf8').split('\n')
    if (!files[files.length - 1]) { files.pop() } // sometimes get an empty line at the end depending on how the file is created
}

let totalFiles = files.length
for (let i = 0; i < totalFiles; i++) {
    console.log(`processing ${files[i]} : ${i + 1} of ${totalFiles}`)

    // parse access log
    let accessLogEntries = parseFile(files[i])
    // console.log(`\tprocessed ${accessLogEntries.length} lines`)
    // entriesProcessed += accessLogEntries.length

    // let nullLines = accessLogEntries.reduce((a, e) => e ? a : a + 1, 0)
    // summary.invalidLines += nullLines

    // // filter
    // accessLogEntries = accessLogEntries.filter(e => e) // always filter out null
    // if (!isNoFiltering) {
    //     accessLogEntries = accessLogEntries.filter(filterLogLines)
    // }
    // fullResult = fullResult.concat(...accessLogEntries)
    // console.log(`\tafter filtering, added ${accessLogEntries.length} lines`)

    // // Write csv file if we exceeded max enteries per csv
    // if (fullResult.length >= maxRowsPerCsv) {
    //     // Run any summarizing needed. Updates global summary
    //     summarize()

    //     // write CSV (will change globals like fullResult & filesWritten)
    //     writeCsvFile()
    // }
}

// write CSV for remainder
if (fullResult.length > 0) {
    // Run any summarizing needed. Updates global summary
    summarize()

    // write CSV (will change globals like fullResult & filesWritten)
    writeCsvFile()
}

const end = Date.now()
let durSecs = ((end - start) / 1000).toFixed(2)
let fileinfo = filesWritten ? `in files ${csvFilePrefix}_1 to ${csvFilePrefix}_${filesWritten} of ${maxRowsPerCsv} rows each ` : 'no files written'
console.log('\n----- SUMMARY -----')
console.log(`Files processed ${totalFiles}, lines processed: ${entriesProcessed}, rows added ${entriesWritten}, ${fileinfo} in ${durSecs} seconds`)
console.log(summary)
isDryRun && console.log('\n**DRY RUN** No files written ')