const debug = require('debug')('PossibleBackupFile')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');
const request = require('request');

class PossibleBackupFile extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-possible-backup-file'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            // skipRoot: true,
            maxPathComponents: 4,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    getAttackVectors() {
        return PBFVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
        if (/get/i.test(attack.httpRequest.method) && statusCode == 200) {
            if (attack.vector == 'dummyVector') {
                // vector = true
                attack.vector = ''
                attack.param = ''
                attack.httpRequest = attack.originalRequest.httpRequest
                return await super.performNetworkAttack(attack)
            }
            const fileRegExp = /\/(backup|web|bpm|bin)/i
            let uriRaw = new URL(attack.httpRequest.uri)
            if (!/\/\.\w+$/.test(attack.httpRequest.uri)) {
                if (/\.\w+$/.test(attack.param) && !fileRegExp.test(attack.vector)) {
                    uriRaw.pathname = attack.param.replace(/\.\w+$/, attack.vector)
                    attack.httpRequest.uri = uriRaw.href
                    return await super.performNetworkAttack(attack)
                }
                if (!/\.\w+$/.test(attack.param) && fileRegExp.test(attack.vector)) {
                    uriRaw.pathname = uriRaw.pathname.replace('//', '/')
                    attack.httpRequest.uri = uriRaw.href
                    return await super.performNetworkAttack(attack)
                }
                if (/\/\w+$/.test(attack.param)) {
                    return await super.performNetworkAttack(attack)
                }
            }
            else if (/\/\.\w+$/.test(attack.httpRequest.uri) && attack.param == '/') {
                let oriURL = new URL(attack.originalRequest.httpRequest.uri)
                if (!/\.\w+$/.test(oriURL.pathname)) {
                    return await super.performNetworkAttack(attack)
                }
            }
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (attack.pluginName == this.getName()) {
            if (attack.vector == '' && details.length > 0) {
                vector = true
                return true
            }
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            if (statusCode == 200 && redirects == 0) {
                /**
                 * Content-Type: text/plain
                 * Content-Disposition: attachment; filename="backup.txt"
                */
                let possibleCType = /(application\/json|text\/(?:xml|html))/i

                let CType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
                let CDisposition = _.get(attack, 'result.resp.httpResponse.headers["content-disposition"]', false)
                if (possibleCType.test(CType) && !CDisposition) {
                    return false
                }
                if (possibleCType.test(CType) && CDisposition) {
                    let Extensions = attack.vector.match(/\.\w+/g)[0]
                    if (CDisposition.includes(Extensions)) {
                        return true
                    }
                    else {
                        return false
                    }
                }
                if (/text\/plain|application\/json/i.test(CType) && /\.(?:b?zip|rar|gz|7z|tar|tgz|bz2|x?z|lz|zst)/i.test(attack.vector)) { return false }//Avoid FP


                let ResBody = _.get(attack, "result.resp.body")
                if (/(?:src|href)="https:\/\/cdnjs.cloudflare.com|(?:src|href)="https:\/\/cdn./i.test(ResBody)) { return false } //Avoid CDN                                      
                // if (/<title>/i.test(ResBody) && /\.(?:b?zip|rar|gz|7z|tar|tgz)/i.test(attack.vector)) { return false }//Avoid Custom page content-type: text/html
                let AttCLength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', "0")
                let OriCLength = _.get(attack, 'originalRequest.httpResponse.headers.content-length', "0")
                if (OriCLength > 0 && AttCLength > 0 && OriCLength == AttCLength) { return false }

                // The following Request Filename(has Error Content) present in response body, wont report vuln
                //https://kmp.dlf.in
                const regexpexcludeURL = /\/static\/js\/main\.4aece2cf\.js/i
                if (attack.hostname.includes('dlf.in') && regexpexcludeURL.test(ResBody)) { return false }

                //condition to skip for the custom error pages
                const responseBody = ResBody.toLowerCase();

                // Check each category of error messages
                const errorCategories = [
                    RegExpVari.ErrorMessages.HttpErrors,
                    RegExpVari.ErrorMessages.SecurityErrors,
                    RegExpVari.ErrorMessages.SessionErrors,
                    RegExpVari.ErrorMessages.SystemErrors,
                    RegExpVari.ErrorMessages.WafErrors,
                    RegExpVari.ErrorMessages.GeneralErrors
                ];

                // Early return if any error message is found
                for (const category of errorCategories) {
                    if (category.some(error => responseBody.includes(error))) {
                        return false;
                    }
                }

                // Check for WAF servers in headers
                let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
                ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

                if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
                    return false;
                }

                if (/(?:Invalid|Incorrect) (?:Username|Password|Username or Password)|Login failed|Authentication Failed|The username or password you have entered is incorrect|(?:account|user)[\w\-\:\s]+(?:locked|disabled|inactive)|(?:locked|disabled|inactive)[\w\-\:\s]+(?:account|user)|You are being rate limited|(?:log|sign)(?:-|_| )?(?:in|up|off|out).*?<\/(?:title|h\d|p)/i.test(ResBody)) {
                    return false
                }

                if (/(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/i.test(ResBody)) {
                    return false
                }

                if (ResBody.length > 0 && /\w/.test(ResBody)) {
                    return true
                }
            }
            return false
        }
        return false
    }

    async processAttackResponse(attack) {
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == 200 && attack.originalRequest.httpRequest.uri != attack.httpRequest.uri && attack.httpRequest.method == "GET") {
            let ResBody = _.get(attack, "result.resp.body")
            let ResCL = Buffer.from(ResBody, 'utf-8').length
            let NewResponse = []
            let uri = new URL(attack.href)
            uri.pathname = uri.pathname.replace(/\/\w+\./, '/haiku.')
            uri.pathname = uri.pathname.replace(/\/\.\w+/, '/.haiku')
            let uriRaw = uri.href
            let newheader = attack.httpRequest.headers
            const options = {
                headers: newheader,
                url: uriRaw,
                rejectUnauthorized: false,
                // timeouTrue: 30000
            };
            NewResponse = await this.FalseAtkGETRequest(options)
            if (NewResponse && NewResponse != 'NotFound' && NewResponse.statusCode != 408) {
                if (statusCode != NewResponse.statusCode) {
                    details.push({ result: `Link: ${attack.httpRequest.uri}` })
                    // this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                    // arr.push({ result: attack.httpRequest.uri })
                }
                else if (ResCL > 0) {
                    if (NewResponse.resbody.includes('haiku')) { return }
                    if (/\�/i.test(NewResponse.resbody)) { return }
                    let FlAtkResCL = Buffer.from(NewResponse.resbody, 'utf-8').length
                    if (ResCL != FlAtkResCL) {
                        details.push({ result: `Link: ${attack.httpRequest.uri}` })
                        // this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        // arr.push({ result: attack.httpRequest.uri })
                    }
                }
            }
        }
        else if (vector && details.length > 0) {
            let count = 0
            let list = []
            for (let ele of details) {
                if (JSON.stringify(ele).includes(attack.hostname)) {
                    list[count] = details[count]
                }
                count++
            }
            if (list.length > 0) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, list)
                details = []
            }
        }
    }

    FalseAtkGETRequest(options) {
        return new Promise((resolve) => {
            try {
                request.get(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }

    /* onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        // HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    } */
}
// let count = 0
let details = []
let vector = false
// let arr = []

// `.gz`,`.tar.gz`,`.bzip`,`.7z`,`.tar`,`.tgz`,`.bzip`,`.bzip2`,`.zip`,`.ZIP`,`.rar` are the vectors to download zip.
// vectors & matches ...
const PBFVectors = [
    // common backup file extensions 
    `.bak`,
    `.tmp`,
    `.gho`,
    `.bkp`,
    `.sav`,
    `.backup`,
    `.old`,
    `.skb`,
    `.spf`,
    `.bkp`,
    `.BAK`,
    `.OLD`,
    `.temp`,
    `.inc`,
    `.copy`,
    `.swp`,
    `.cs`,
    `.vb`,

    // common backup file
    `/backup.zip`,
    `/web.zip`,
    `/bpm.zip`,
    `/web.rar`,
    `/backup.rar`,
    `/bpm.rar`,
    `/bin.zip`,
    // `.ansa`,

    // common compressed file extensions
    `.zip`,
    `.rar`,
    `.7z`,
    `.tar`,
    `.gz`,
    `.bz2`,
    `.xz`,
    `.z`,
    `.lz`,
    `.lzma`,
    `.lzo`,
    `.zst`,
    '.tar.gz',
    '.bzip',
    '.tgz',
    '.bzip',
    '.bzip2',
    '.ZIP',
    '.RAR',
    `.war`,
    // `/0x1a.zip`,//this is not attack vector, used to make final report
    `dummyVector`,//this is not attack vector, used to make final report
]

module.exports = PossibleBackupFile