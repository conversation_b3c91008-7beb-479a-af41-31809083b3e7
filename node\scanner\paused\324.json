{"frozenOn": "2025-06-11T10:13:39.532Z", "version": "4.0", "serviceId": 98650, "crawler": {"nextScreenshotId": 2, "seenLoadActions": [], "processedSeedUrls": false, "pluginData": {"filterItems": {"seenLinkItems": ["https://127.0.0.1:5000/", "https://127.0.0.1:5000/resource", "https://127.0.0.1:5000/resources", "https://127.0.0.1:5000/portalservermb", "https://127.0.0.1:5000/portalservermb/login", "https://127.0.0.1:5000/login"], "seenFormItems": []}, "coreHeuristics": {"seenNonLinkFingerprints": []}, "crawlMetrics": {"actionsPerformed": 0, "totalRequestsSent": 0, "maxDepth": 0, "skipBeforeTrigger": 0, "uniqueStates": 0, "attackableNetworkActions": 0, "pagesCrawled": [], "networkRequests": [], "actionMetrics": {}, "totalCrawlDurationMins": 5, "foundResources": ["https://127.0.0.1:5000/"]}, "logNetworkActions": {"seenNetworkRequests": []}}}, "crawlContext": {"version": "2.0", "allContextStates": [{"contextStateSeq": 0, "children": [], "depth": 0, "path": [], "parentSeq": -1, "actions": [{"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/", "initial, mustRunAtInit: true"]}], "seq", true]}, {"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/resource/", "initial, mustRunAtInit: true"]}], "seq", true]}, {"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/resources/", "initial, mustRunAtInit: true"]}], "seq", true]}, {"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/portalservermb/", "initial, mustRunAtInit: true"]}], "seq", true]}, {"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/portalservermb/login", "initial, mustRunAtInit: true"]}], "seq", true]}, {"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/login", "initial, mustRunAtInit: true"]}], "seq", true]}, {"serliaziedName": "./datastructure\\action-list.js", "args": ["initial actions", [{"serliaziedName": "./datastructure\\load-action.js", "args": ["[initial]", "https://127.0.0.1:5000/portalservermb", "initial, mustRunAtInit: false"]}], "seq", false]}], "interestingItems": {"serverData": {}}, "dupInfo": {"minActionSimilarity": -1, "maxActionSimilarity": -1, "avgActionSimilarity": -1, "wavgActionSimilarity": -1, "countActionSimilarity": 0, "dupThreshold": -1, "correctGuesses": 0}, "curProcIndex": -1, "state": "created", "actionsResultingInThisState": {"100": []}}], "crawlStateContext": {"currentContext": 0, "bfsQueue": []}}}