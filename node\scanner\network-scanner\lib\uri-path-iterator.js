const debug = require('debug')('UriPathIterator')
const URL = require('url').URL
const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = 'BaseURI'
const HaikuUtils = require('../../common/lib/haiku-utils')
/**
 * Delegate that knows how to tamper URI path. This will be used to try various 'well-known' 
 * paths for seeding crawl or looking for default pages.
 * Given an URI eg. http://www.xyz.com/a/b/c/d?kk=hh, this will iterate:
 * END: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/c/d/[vectors]?kk=hh
 * ROOT: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/[vectors]?kk=hh
 * sub paths based on depth: 
 *  depth 1: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/[vectors]?kk=hh
 *  depth 2: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/[vectors]?kk=hh
 */
class UriPathIterator extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     * @param {URIPathOptions} options Options to control URI path iterator
     */
    constructor(request, scanStore, options) {
        super(request, scanStore, _ParameterType, options)
        this.originalUrl = new URL(request.httpRequest.uri)
        this.appendSlashAtEnd = this.originalUrl.pathname.endsWith('/') ? '/' : ''
        if (this.originalUrl.pathname.endsWith('/')) {
            this.originalUrl.pathname = this.originalUrl.pathname.slice(0, -1) // remove / at end since its controlled by flags
        }
        this.curAttackVector = ''

        // set up the scan store if needed
        if (!this.scanStore.pathsAttacked) {
            this.scanStore.pathsAttacked = []
        }

        // When we add paths to already attacked, keep track of the crawler request so that we can allow
        // those paths for that crawler request. By the way delegates work, attacked paths are tracked by the delegate
        // and other plugins attacking the same crawler request should be allowed to instead of being blocked because 
        // we have already attacked the URI path.
        if (!this.scanStore.allowedRequests) {
            this.scanStore.allowedRequests = {}
        }
        if (!this.scanStore.allowedRequests[this.getOriginalRequest().httpRequest.haikuKey]) {
            this.scanStore.allowedRequests[this.getOriginalRequest().httpRequest.haikuKey] = []
        }


        this.pathToAttack = undefined
    }

    /**
     * Set options for this parameterized request.
     * @param {parameterizedDelegateOptions} options 
     * @override
     */
    setOptions(options) {
        super.setOptions(options)

        // see if we need to clear the query params
        if (this.options.clearQueryParams) {
            this.originalUrl.search = ''
        }

        // dont append slash at end automatically
        switch (this.options.haveSlashAfterAttack) {
            case 'never':
                this.appendSlashAtEnd = ''
                break
            case 'always':
                this.appendSlashAtEnd = '/'
                break

            case 'original':
                // nothing to do, appendSlashAtEnd is already set up
                break

            default:
                debug(`Unknown option for haveSlashAfterAttack : ${this.options.haveSlashAfterAttack}`)
        }

    }
    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }
    /**
     * 
     * @param {currAttackVector} attackVector The uri attack Vector which will be in the raw format.
     */
    doEncodeChars(attackVector) {
        attackVector = attackVector.replace(/ /g, '%20')
        attackVector = attackVector.replace(/\#/g, '%23')
        attackVector = attackVector.replace(/\n/g, '%0A')
        /**
         * Below condition is kept as few vulns like tomcat rce with url as 
         * "http://localhost//cgi-bin/hello.bat?&ver" will not trigger vulnerability if '?' is encoded
         * We always need raw form of url to trigger the attack. Hence if we are clearing the query params
         * then will not perform any encoding but if query params are not cleared then it will encode
         */
        if (!this.options.clearQueryParams) {
            attackVector = attackVector.replace(/\?/g, '%3F')
        }
        return attackVector
    }

    /**
     * generator for iterating paths
     * Given an URI eg. http://www.xyz.com/a/b/c/d?kk=hh, this will iterate:
     * END: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/c/d/[vectors]?kk=hh
     * ROOT: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/[vectors]?kk=hh
     * sub paths based on depth: 
     *  depth 1: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/[vectors]?kk=hh
     *  depth 2: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/[vectors]?kk=hh
     * @override
     */
    * getIterator() {
        // set up the path components
        let pathsToIterate = new Set() // to keep paths unique as we are building it up
        let maxPathComponentsLimit = this.options.maxPathComponents // may be adjusted for skip root, always iterate end

        // sub paths based on depth: 
        //  depth 1: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/[vectors]?kk=hh
        //  depth 2: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/[vectors]?kk=hh
        let pathComponents = this.originalUrl.pathname.split('/')

        // remove the root & end since they are controlled by the skipRoot & alwaysIterateEnd attrs
        // ROOT: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/[vectors]?kk=hh
        if (!this.options.skipRoot) {
            pathsToIterate.add('/')
            maxPathComponentsLimit++ // root does not count in maxPathComponets limit
        }
        // remove root, splitting on / gives 1st component as '' eg. /a/b gives ['','a','b'] 
        // causing issues in building pathToAttack below, 
        pathComponents.shift()

        // END: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/c/d/[vectors]?kk=hh
        if (this.options.alwaysIterateEnd && pathComponents.length > 0) {
            pathsToIterate.add(_.clone(this.originalUrl.pathname))
            pathComponents.pop() // remove end
            maxPathComponentsLimit++ // end does not count in maxPathComponets limit
        }

        let pathToAttack = ''
        if (this.options.maxPathComponents) { // do we want any path compnents
            for (let component of pathComponents) {
                pathToAttack += '/' + component
                pathsToIterate.add(pathToAttack)
            }
        }

        // filter paths
        let iteratePaths = Array.from(pathsToIterate);

        // skip already attacked paths except those that we are allowed to attack for this crawler request
        let haikuKey = this.getOriginalRequest().httpRequest.haikuKey
        iteratePaths = iteratePaths.filter((v) => this.scanStore.allowedRequests[haikuKey].includes(v) || !this.scanStore.pathsAttacked.includes(v))

        // remove not interested extensions
        if (!HaikuUtils.isNullOrUndefined(this.options.ignoreExtension) && (this.options.ignoreExtension.length > 0)) {
            iteratePaths = iteratePaths.filter(this.filterValues.bind(this))
        }

        // limit to the max path components
        if (iteratePaths.length > maxPathComponentsLimit) {
            iteratePaths = iteratePaths.filter((v, index) => index < maxPathComponentsLimit)
        }

        // add paths attacked and alowed requests immediately before yielding otherwise we could
        // double iterate depending on the order plugins work in.
        for (let pathToAttack of iteratePaths) {
            // all plugins can attack this path when attacking thie crawler request
            if (!this.scanStore.allowedRequests[haikuKey].includes(pathToAttack)) {
                this.scanStore.allowedRequests[haikuKey].push(pathToAttack)
            }
            // we have attacked this path
            if (!this.scanStore.pathsAttacked.includes(pathToAttack)) {
                this.scanStore.pathsAttacked.push(pathToAttack)
            }
        }

        // now get to the business -> iterate the paths
        for (let pathToAttack of iteratePaths) {
            this.pathToAttack = pathToAttack
            yield {
                name: pathToAttack,
                val: '',
                resetRequired: false
            }
        }
    }

    /**
     * modify a parameter
     * @override
     */
    modifyParam(param, value, encoding) {
        // @todo - can move setting of the path to attack here, like:
        // this.pathToAttack = param
        this.curAttackVector = value
    }

    /**
     * get the modified HTTP request
     * @override
     */

    getHttpRequest(encoding) {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)
        let parsedUrl = new URL(this.originalUrl)

        // set up path preceding attack as /a/b or /a/b/ (root is always /)
        let pathname = this.pathToAttack



        // add attack and trailing / if needed
        if (encoding == 'uri') {
            if (pathname.length > 1 && this.options.addSlashBeforeAttack) {
                pathname += '/'
            }
            pathname += this.curAttackVector + this.appendSlashAtEnd
            if (this.options.collapsePathSeps) {
                pathname = pathname.replace(/\/\/+/g, '/')
            }

            parsedUrl.pathname = pathname
        } else {
            let strPath = pathname
            parsedUrl.pathname = pathname
            pathname = this.doEncodeChars(this.curAttackVector) + this.appendSlashAtEnd
            if (this.options.collapsePathSeps) {
                pathname = pathname.replace(/\/\/+/g, '/')
            }
            let symobjArrays = Object.getOwnPropertySymbols(parsedUrl)
            let index = symobjArrays.findIndex(obj => obj.toString() == "Symbol(context)")
            if (strPath == '/') {
                parsedUrl[symobjArrays[index]].path = []
            }
            parsedUrl[symobjArrays[index]].path.push(pathname)
            req.rawURl = parsedUrl;
        }
        req.uri = parsedUrl.href
        return req
    }
    /**
     * reset the request back to the original request to prepare for next set of modifications
     * @override
     */
    reset() {
        this.curAttackVector = ''
    }


    /**
     * filter out configured igonerd extension
     * 
     */
    filterValues(value) {
        let exts = this.options.ignoreExtension
        let values = value.split(".")
        let extension = values[values.length - 1]
        let contains = exts.includes(extension)
        return !contains
    }
}


module.exports = UriPathIterator