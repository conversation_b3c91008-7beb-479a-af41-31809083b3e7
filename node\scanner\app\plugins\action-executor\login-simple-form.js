const utils = require('../../ifc-utils.js')
const ActionList = require('../../datastructure/action-list.js')
const ClickAction = require('../../datastructure/click-action.js')
const TypeAction = require('../../datastructure/type-action.js')

class SimpleFormLogin {
    constructor(actionExecutor) {
        this.config = actionExecutor.config
        this.failedLogins = 0

        // merge the plugin specific config
        this.mergeConfig()

        // event handlers
        actionExecutor.on('pre-action-execute', this.preActionExecute.bind(this))
        actionExecutor.on('post-action-execute', this.postActionExecute.bind(this))
    }

    mergeConfig() {
        if (!this.config.siteConfig || !this.config.siteConfig.pluginData || !this.config.siteConfig.pluginData.simpleLogin) {
            return
        }

        // plugin data for "simple login" plugin
        if (!this.config.pluginData.simpleLogin) {
            this.config.pluginData.simpleLogin = {}
        }
        let pluginData = this.config.pluginData.simpleLogin
        let sitePluginData = this.config.siteConfig.pluginData.simpleLogin

        if (sitePluginData.retry) {
            pluginData.retry = sitePluginData.retry
        }

        if (sitePluginData.username) {
            pluginData.username = sitePluginData.username
        }
        if (sitePluginData.password) {
            pluginData.password = sitePluginData.password
        }

        this.retry = pluginData.retry
        this.username = pluginData.username
        this.password = pluginData.password
    }

    async preActionExecute(action, executionContext) {
        await this.checkForLoginPage(executionContext) 
    }

    async postActionExecute(action, executionContext) {
        await this.checkForLoginPage(executionContext)
    }

    async checkForLoginPage(executionContext) {
        if (!this.username) {
            return null
        }

        if (this.failedLogins >= this.retry) {
            utils.log('skipping login due to too many failed logins')
            return null
        }

        // if we see a password type field 
        // ... and something that looks like an username field
        // ...and something like a login button
        // give it a shot.
        let jscode = `indusfaceRenderer.getLoginInfo()`
        let loginInfo = await utils.timedPromise(executionContext.browser.webContents.executeJavaScript(jscode, true),null)
        if (!loginInfo) {
            return false
        }

        let actionList = new ActionList('heuristic simplified login')

        // fill in all password fields with password
        if(loginInfo.passwords) {
            for (let pwdField of loginInfo.passwords) {
                actionList.addAction(new TypeAction(pwdField, this.password, 'password'))
            }
        }

        // fill in all username fields with user name
        if(loginInfo.usernames) {
            for (let unField of loginInfo.usernames) {
                actionList.addAction(new TypeAction(unField, this.username, 'username'))
            }
        }

        // click on login
        actionList.addAction(new ClickAction(loginInfo.action, 'login-button'))

        // take the immediate action
        await executionContext.executer.triggerAction(actionList, executionContext.executer.getNoPluginContext(executionContext))
        executionContext.pluginTookAction = true

        if(!loginInfo.usernames && !loginInfo.passwords) {
            //Page has only simple login action so plugin took that action only. No need to other actions like username/password actions.
            return false;
        }
        
        // check if login worked. Due to the ! logic, if promise times out, the timed out value is true
        // i.e. if promise times out, resolve to true so that loginSucceeded will be false
        let url = executionContext.browser.webContents.getURL();
        let finalLoginInfo = await utils.timedPromise(executionContext.browser.webContents.executeJavaScript(jscode, true), true);

        let loginSucceeded = !finalLoginInfo;

        if(finalLoginInfo && !finalLoginInfo.usernames && !finalLoginInfo.passwords) {
            //After login is successful page has only single login action button. Consider such event as login sucessfull.
            loginSucceeded = true;
        }

        // add the username and password info to login info
        loginInfo.username = this.username
        loginInfo.password = this.password

        if (!loginSucceeded) {
            this.failedLogins++
        }
        else {
            this.failedLogins = 0;
            //Check for additional login steps required after login success. i.e. Stay signed in page.
            try {
                actionList = null;
                let additionalSteps = await utils.timedPromise(executionContext.browser.webContents.executeJavaScript(`indusfaceRenderer.getMultiStepElementAfterLogin()`, true),null);

                if(additionalSteps && additionalSteps.yesButton) {
                    actionList = new ActionList('heuristic multistep login');
                    actionList.addAction(new ClickAction(additionalSteps.yesButton, 'yes-button'));
                }

                if(actionList) {
                    await executionContext.executer.triggerAction(actionList, executionContext.executer.getNoPluginContext(executionContext))
                }
            } catch (error) {
                utils.log(`Unable to get additionalSteps after successful login ${error.message}`);
            }
        }

        executionContext.scanner.emit('login-status', loginSucceeded, loginInfo, 'simplified-login')

        return true // took action.
    }

}

module.exports = SimpleFormLogin