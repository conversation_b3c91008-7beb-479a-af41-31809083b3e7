const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const cookieParse = require('cookie-parse');
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Weak Session ID plugin strategy: If any original requests cookie does fulfill the checks for weak session
 * then they are marked as vulnerable
 */
class WeakSessionID extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.VulnerabilityID = 'ID-cookie-weak-sessionid'
    }

    //update for the regex part

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        // if cookie present in original request then only call process attack
        if (attack.attackArea == "original-crawler-request") {
            if (attack.originalRequest.httpRequest.headers.Cookie != undefined) {
                if (/phpsessid|user_token|JSESSIONID|ASP\.NET_SessionId|OutlookSession|ASPSESSIONID|Sessionid|log_session_id|svSession|ASP\.NET_SessionIPartner|HRA_Session|laravel_session|_jsuid/i.test(attack.originalRequest.httpRequest.headers.Cookie))
                    return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.weaksessionIdVulnerabilityFound) {
            return
        }

        let sessionCookie = []
        let fullCookie = _.get(attack, 'originalRequest.httpRequest.headers.Cookie', {})
        let parsedCookies = cookieParse.parse(fullCookie)


        //check all cookies for respective weakness in session id's
        for (let cookie in parsedCookies) {
            if (/^TS[\da-f]+$/i.test(cookie)) continue; //Skip to check because The TS* cookie pattern is used across multiple F5 customers.

            if (!/licenseno|_gat|_ga|_gid|/.test(cookie)) {

                let i = parsedCookies[cookie];
                if (/phpsessid|user_token|JSESSIONID|ASP\.NET_SessionId|OutlookSession|ASPSESSIONID|Sessionid|log_session_id|svSession|ASP\.NET_SessionIPartner|HRA_Session|laravel_session|_jsuid|TokenId|/i.test(cookie)) {

                    //check if any of the cookie values are null/alive/true/false

                    //check for alphanumeric+special chars in sessionid
                    if (/^(?=.*[\d])(?=.*[a-zA-Z])(?=.*[_+\-:.])([\w_+\-:.]+)$/.test(i) && i.length < 5) {
                        sessionCookie.push({ name: cookie, value: i, wholeCookie: fullCookie })
                    }
                    //check for alphanumeric only chars in sessionid
                    else if (/^(?=.*[\d])(?=.*[a-zA-Z])([\w]+)$/.test(i) && i.length < 5) {
                        sessionCookie.push({ name: cookie, value: i, wholeCookie: fullCookie })
                    }
                    //check for numbers only in sessionid
                    else if (/^\d+$/.test(i) && i.length < 10) {
                        sessionCookie.push({ name: cookie, value: i, wholeCookie: fullCookie })
                    }
                    //check for alphabets only in sessionid
                    else if (/^[a-zA-Z]+$/.test(i) && i.length < 5) {
                        sessionCookie.push({ name: cookie, value: i, wholeCookie: fullCookie })
                    }
                }
            }
        }

        if (sessionCookie && sessionCookie.length > 0) {
            let vulns = {
                details: sessionCookie
            }
            this.addVulnerabilitytoResult(attack, this.VulnerabilityID, vulns)
            pluginDataForRequest.weaksessionIdVulnerabilityFound = true
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        let vulndts = _.get(attack, `result.vulns.${vulnID}.details`, null);

        if (vulndts.length && vulndts.length > 0) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers.Cookie', `text`, [vulndts[0].details]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
    }
}
module.exports = WeakSessionID