const debug = require('debug')('XSSAttack')
const querystring = require('querystring')
const VectorResponseAttack = require('./vector-response-attack')
const jsdom = require("jsdom")
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')


class XSSNSAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-xss-ns-injection'
        this.vulnHtmlInjID = 'ID-html-ns-injection'

        // replace all (###) in vectors with a random number
        // used to call HaikuUtils.getRandomInt(1000, 9999) but that screws up the missing vulns
        // storage since the vector is part of the key.
        this.randomNumber = HaikuUtils.getRandomInt(1000, 9999)

        // fix up the attack vectors
        this.xssVectors = _xssVectors.map(s => s.replace(/(###)/g, this.randomNumber))
        this.htmlInjVectors = _htmlInjVectors.map(s => s.replace(/(###)/g, this.randomNumber))
        this.allAttackVectors = [...this.xssVectors, ...this.htmlInjVectors]
    }

    /**
     * get array of XSS & HTML Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return this.allAttackVectors
    }

    /**
     * We are done with param only if XSS & html inj found.
     * @param {attack} attack
     * @returns {boolean} true -> dont try remaining vectors & encodings for this parameter 
     * @override
     */
    doneWithThisParam(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        return _.get(pluginDataForRequest, `[${attack.param}].xssFound`) && _.get(pluginDataForRequest, `[${attack.param}].htmlInjFound`)
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
            })
        }
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        // let base class decide first
        if (!super.wantProcessAttackResponse(attack)) {
            return false
        }

        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return false
        }

        // slight optimization - only process if 'haiku' found in the body
        let rawHtml = _.get(attack, 'result.resp.body')
        if (!/haikumsg/i.test(rawHtml)) {
            return false
        }

        return true
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    async processAttackResponse(attack) {

        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if ((pluginDataForRequest.XSSFound || _.get(pluginDataForRequest, `['${attack.param}'].xssFound`)) && (pluginDataForRequest.HTMLInjFound || _.get(pluginDataForRequest, `['${attack.param}'].htmlInjFound`))) { return }

        // Hook the haikumsg method
        let msgTriggered = false
        let haikumsg = ((msg) => {
            if (msg == this.randomNumber || msg == this.getMetadata(attack).haikuMsgCheckNumber) {
                msgTriggered = true
            }
        }).bind(this)

        // parse HTML
        const virtualConsole = new jsdom.VirtualConsole().sendTo(console, {
            omitJSDOMErrors: true
        }) // dont log internal page errors
        let rawHtml = _.get(attack, 'result.resp.body')
        let dom = new jsdom.JSDOM(rawHtml, {
            url: attack.href,
            virtualConsole,
            runScripts: "dangerously",
            beforeParse(window) {
                window.haikumsg = haikumsg
            }
        })

        let vulnXSSFound = []
        let vulnHTMLInjFound = []
        let details = {
            xssFound: [],
            htmlInjFound: []
        }

        if (!this.htmlInjVectors.includes(attack.vector)) {
            await HaikuUtils.sleep(500) // give 1/2 second for any script (including timer based ones) to execute

            let document = dom.window.document
            // >> If our hook invoked on document load (onload/script tag) => XSS
            if (msgTriggered) {
                // this can only happen with script tag or a auto triggered event like onload
                let xpath = `/html//*[@*[contains(.,"haikumsg")]]|/html//script[contains(.,"haikumsg")]`
                this.addvulnerabilityDetails(xpath, dom, details.xssFound);
            }

            if (!msgTriggered) {
                let regexp = new RegExp(_.escapeRegExp(attack.vector))
                vulnXSSFound = this.checkBodyForVuln(attack, regexp, this.vulnerabilityID, {
                    maxMatchesToReturn: 1,
                    addVulnerabilitytoResult: false
                })

            }
        }

        // Look for Elements with our class by querying DOM.
        let xpath = `/html/body//*[@class[contains(.,"haikumsg")]]`
        this.addvulnerabilityDetails(xpath, dom, details.htmlInjFound)

        if (!this.xssVectors.includes(attack.vector)) {
            let regexp = new RegExp(_.escapeRegExp(attack.vector))
            vulnHTMLInjFound = this.checkBodyForVuln(attack, regexp, this.vulnHtmlInjID, {
                maxMatchesToReturn: 1,
                addVulnerabilitytoResult: false
            })
        }

        // ensure that the jsdom object is cleaned up (eg. stop all timers)
        dom.window.close()

        if (!pluginDataForRequest.XSSFound && vulnXSSFound.details.length > 0) {
            let xss_dts = []
            xss_dts.push({
                context: vulnXSSFound.details,
                Body_Content: vulnXSSFound.context
            })

            if (xss_dts) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, xss_dts)
                pluginDataForRequest.XSSFound = true
            }
        }

        if (!pluginDataForRequest.HTMLInjFound && vulnHTMLInjFound.details.length > 0) {
            let htmlinj_dts = []
            htmlinj_dts.push({
                context: vulnHTMLInjFound.details,
                Body_Content: vulnHTMLInjFound.context
            })

            if (htmlinj_dts) {
                this.addVulnerabilitytoResult(attack, this.vulnHtmlInjID, htmlinj_dts)
                pluginDataForRequest.HTMLInjFound = true
            }
        }

        if (!_.get(pluginDataForRequest, `['${attack.param}'].xssFound`) && details.xssFound.length > 0) {
            this.addVulnerabilitytoResult(attack, 'ID-xss-ns-injection', details.xssFound)
            _.set(pluginDataForRequest, `['${attack.param}'].xssFound`, true)
        }

        if (!_.get(pluginDataForRequest, `['${attack.param}'].htmlInjFound`) && details.htmlInjFound.length > 0) {
            this.addVulnerabilitytoResult(attack, 'ID-html-ns-injection', details.htmlInjFound)
            _.set(pluginDataForRequest, `['${attack.param}'].htmlInjFound`, true)
        }
    }

    /**
     * Use XPATH to search the DOM and add vulnerability context - basically enough of the Element's
     * HTML for context
     * @param {string} xpath Path used to search DOM for vulnerable elements
     * @param {jsdom} dom The dom to search (parsed HTML)
     * @param {array} vulnsFound Array where details of vulnerability will be stored
     */
    addvulnerabilityDetails(xpath, dom, vulnsFound) {
        let document = dom.window.document

        let elemsIter = document.evaluate(xpath, document, null, dom.window.XPathResult.ANY_TYPE);
        let elem = elemsIter.iterateNext();
        while (elem) {
            vulnsFound.push({
                context: elem.outerHTML.substr(0, this.contextBytes)
            });
            elem = elemsIter.iterateNext();
        }
    }
}

// Attack vectors.
// for future...

const _xssVectors = [
    `%FF%FE%3C%73%63%72%69%70%74%3E%68%61%69%6B%75%6D%73%67%28%33%32%36%29%3C%2F%73%63%72%69%70%74%3E`, //Blocked under xss injection rule id 94
    `%FF%FE%3C%73%63%72%69%70%74%3E%68%61%69%6B%75%6D%73%67%28%33%32%36%29%3C%2F%73%63%72%69%70%74%3E=1`, //Blocked under xss injection rule id 94
    `<<<<<<<<haikumsg"test'###>>>>>`, 
    `<<<<<<<<<haikumsg"test'###>>>>>=1`,
    `<<<<<<<<haikumsg""test'###>>>>>`,
    `<<<<<<<<<haikumsg""test'###>>>>>=1`,
    `>>>>>>>>>>haikumsg"test'###<<<<<=1`,
    
]

const _htmlInjVectors = [
    `<<<<"haikumsg###%20>>>`, 
    `<"haikumsg###%20>`,
    `<"haikumsg###%0A>`,
    `<"haikumsg###>`,
    `<""haikumsg###%20>`,
    `<""haikumsg###%0A>`,
    `<""haikumsg###>`,
    `<"HAIKIUMSG###>`,
]

module.exports = XSSNSAttack