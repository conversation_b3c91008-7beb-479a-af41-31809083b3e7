const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

class MixedContent extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.activeVulnerabilityID = 'ID-active-mixed-content-vulnerability'
        this.passiveVulnerabilityID = 'ID-passive-mixed-content-vulnerability'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {
        //If HSTS is present in response header below vulnerability check ll be skipped
        let HSTSHeaderVal = _.get(originalRequest, 'result.resp.httpResponse.headers["strict-transport-security"]');
        // check if below condition are met, then only call processAttackResponse
        if (originalRequest.attackArea == "original-crawler-request" && originalRequest.href.startsWith("https") && !HSTSHeaderVal) {
            return true
        }
        return false
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {

        // need to load content only if its https and below content should be loaded and 
        // need to check if content is loaded from scan url only
        // <link href=
        // <script src=
        // <img src=
        // <a href=
        // and should not be inside comment
        // for active mixed content it should be js, css files and for passive remaining content like img

        let details = {
            URI: originalRequest.result.req.options.uri
        }

        // code to fetch whole response body
        let $ = _.get(originalRequest, 'result.resp.httpResponse.cheerio')
        // code to fetch all comments in the body
        let comments = $('*').contents().filter((i, e) => {
            return e.type == "comment";
        }).get();


        let url = _.get(originalRequest, 'httpRequest.uri')
        let parsedUrl = new URL(url)

        let _this = this;
        // Below code to iterate over all tags and check for vuln
        for (let tags of tagsToCheck) {
            let tagElements = $(tags.tag)
            if (tagElements.length >= 1) {
                let testData = "<" + tags.tag

                // code to check if any of the tags are commented then skip and start with another tag
                for (let t = 0; t < comments.length; t++) {
                    let lineOfComment = comments[t];
                    if (lineOfComment.data.includes(testData)) {
                        continue
                    }
                }
            }

            // if tag not present inside comment then check for vulnerability
            tagElements.each(function (index, el) {
                let val = $(el).attr(tags.attr)
                if ((val != null || val != undefined) && val.startsWith("http:") && val.includes(parsedUrl.host)) {
                    if (/\.js|\.css/i.test(val) && !(/\.jsp/i.test(val))) {
                        _this.addVulnerabilitytoResult(originalRequest, _this.activeVulnerabilityID, details)
                    }
                    if (/\.jpg|\.jpeg|\.bmp|\.gif|\.png|\.pdf|\.doc|\.docx|\.xls|\.xlsx|\.woff|\.woff2|\.ttf|\.svg|\.wmv|\.mpeg|\.ico|\.pps|\.ppt|\.swf|\.txt|\.xml/i.test(val)) {
                        _this.addVulnerabilitytoResult(originalRequest, _this.passiveVulnerabilityID, details)
                    }
                }
            })
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.activeVulnerabilityID && vulnID != this.passiveVulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
    }
}

const tagsToCheck = [
    { tag: 'a', attr: 'href' },
    { tag: 'link', attr: 'href' },
    { tag: 'img', attr: 'src' },
    { tag: 'script', attr: 'src' }]

module.exports = MixedContent