{"info": {"_postman_id": "9d5a2318-ee0a-4266-bc4b-67b1f45ce895", "name": "Swagger Petstore", "description": "This is a sample server Petstore server.  You can find out more about Swagger at [http://swagger.io](http://swagger.io) or on [irc.freenode.net, #swagger](http://swagger.io/irc/).  For this sample, you can use the api key `special-key` to test the authorization filters.\n\nContact Support:\n Email: <EMAIL>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "pet", "item": [{"name": "{pet Id}", "item": [{"name": "Find pet by ID", "request": {"auth": {"type": "apikey", "apikey": [{"key": "key", "value": "api_key", "type": "string"}, {"key": "value", "value": true, "type": "boolean"}, {"key": "in", "value": "header", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId", "value": "79414423", "type": "string", "description": "(Required) ID of pet to return"}]}, "description": "Returns a single pet"}, "response": [{"name": "Invalid ID supplied", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: apikey", "type": "text/plain"}, "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: apikey", "type": "text/plain"}, "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"name\": \"doggie\",\n \"photoUrls\": [\n  \"laborum esse Excepteur ut\",\n  \"eu dolo\"\n ],\n \"id\": 30973048,\n \"category\": {\n  \"id\": 65574654,\n  \"name\": \"occaecat veniam\"\n },\n \"tags\": [\n  {\n   \"id\": -5541178,\n   \"name\": \"commodo officia elit ad\"\n  },\n  {\n   \"id\": -19261292,\n   \"name\": \"Ut adipisicing\"\n  }\n ],\n \"status\": \"sold\"\n}"}, {"name": "Pet not found", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: apikey", "type": "text/plain"}, "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Updates a pet in the store with form data", "request": {"auth": {"type": "oauth2"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "velit Ut nisi do", "description": "Updated name of the pet"}, {"key": "status", "value": "<PERSON><PERSON> proident", "description": "Updated status of the pet"}]}, "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId", "value": "79414423", "type": "string", "description": "(Required) ID of pet that needs to be updated"}]}}, "response": [{"name": "Invalid input", "originalRequest": {"method": "POST", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "name", "value": "velit Ut nisi do", "description": {"content": "Updated name of the pet", "type": "text/plain"}}, {"key": "status", "value": "<PERSON><PERSON> proident", "description": {"content": "Updated status of the pet", "type": "text/plain"}}]}, "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Method Not Allowed", "code": 405, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Deletes a pet", "request": {"auth": {"type": "oauth2"}, "method": "DELETE", "header": [{"key": "api_key", "value": "reprehenderit anim ut eiusmod"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId", "value": "79414423", "type": "string", "description": "(Required) Pet id to delete"}]}}, "response": [{"name": "Pet not found", "originalRequest": {"method": "DELETE", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}, {"key": "api_key", "value": "reprehenderit anim ut eiusmod"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Invalid ID supplied", "originalRequest": {"method": "DELETE", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}, {"key": "api_key", "value": "reprehenderit anim ut eiusmod"}], "url": {"raw": "{{baseUrl}}/pet/:petId", "host": ["{{baseUrl}}"], "path": ["pet", ":petId"], "variable": [{"key": "petId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "uploads an image", "request": {"auth": {"type": "oauth2"}, "method": "POST", "header": [{"key": "Content-Type", "value": "multipart/form-data"}], "body": {"mode": "formdata", "formdata": [{"key": "additionalMetadata", "value": "deserunt in", "description": "Additional data to pass to server", "type": "text"}, {"key": "file", "value": "consequat velit cillum dolore", "description": "file to upload", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/pet/:petId/uploadImage", "host": ["{{baseUrl}}"], "path": ["pet", ":petId", "uploadImage"], "variable": [{"key": "petId", "value": "79414423", "type": "string", "description": "(Required) ID of pet to update"}]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "body": {"mode": "formdata", "formdata": [{"key": "additionalMetadata", "value": "deserunt in", "description": {"content": "Additional data to pass to server", "type": "text/plain"}, "type": "text"}, {"key": "file", "value": "consequat velit cillum dolore", "description": {"content": "file to upload", "type": "text/plain"}, "type": "text"}]}, "url": {"raw": "{{baseUrl}}/pet/:petId/uploadImage", "host": ["{{baseUrl}}"], "path": ["pet", ":petId", "uploadImage"], "variable": [{"key": "petId"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"code\": -17739554,\n \"type\": \"consequat amet Lo<PERSON>\",\n \"message\": \"eu ut dolore qui\"\n}"}]}]}, {"name": "Add a new pet to the store", "request": {"auth": {"type": "oauth2"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"doggie\",\n    \"photoUrls\": [\n        \"esse do magna elit cillum\",\n        \"consequat laborum\"\n    ],\n    \"id\": 65926811,\n    \"category\": {\n        \"id\": 52804458,\n        \"name\": \"nostrud velit aute\"\n    },\n    \"tags\": [\n        {\n            \"id\": 49134111,\n            \"name\": \"cupidatat ut\"\n        },\n        {\n            \"id\": 50059342,\n            \"name\": \"enim\"\n        }\n    ],\n    \"status\": \"pending\"\n}", "_haiku_attack": "{\n    \"name\": \"doggie\",\n    \"photoUrls\": [\n        \"esse do magna elit cillum\",\n        \"consequat laborum\"\n    ],\n    \"id\": 65926811,\n    \"category\": {\n        \"id\": 52804458,\n        \"name\": \"nostrud velit aute\"\n    },\n    \"tags\": [\n        {\n            \"id\": 49134111,\n            \"name\": \"cupidatat ut\"\n        },\n        {\n            \"id\": 50059342,\n            \"name\": \"enim\"\n        }\n    ],\n    \"status\": \"pending\"\n}"}, "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "response": [{"name": "Invalid input", "originalRequest": {"method": "POST", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "status": "Method Not Allowed", "code": 405, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "haiku-annotate add pet sql", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"doggie\",\n    \"photoUrls\": [\n        \"esse do magna elit cillum\",\n        \"consequat laborum\"\n    ],\n    \"id\": 65926811,\n    \"category\": {\n        \"id\": 52804458,\n        \"name\": \"nostrud velit aute\"\n    },\n    \"tags\": [\n        {\n            \"id\": 49134111,\n            \"name\": \"cupidatat ut\"\n        },\n        {\n            \"id\": 50059342,\n            \"name\": \"enim\"\n        }\n    ],\n    \"status\": \"pending\"\n}"}, "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "_postman_previewlanguage": "json", "header": null, "cookie": [], "body": "{\n    \"propsToIterate\": {\n        \"body\": [\n            \"name\",\n            \"id\",\n            \"tags[1].id\"\n        ]\n    }\n}"}]}, {"name": "Update an existing pet", "request": {"auth": {"type": "oauth2"}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"doggie\",\n    \"photoUrls\": [\n        \"laborum esse Excepteur ut\",\n        \"eu dolo\"\n    ],\n    \"id\": 30973048,\n    \"category\": {\n        \"id\": 65574654,\n        \"name\": \"occaecat veniam\"\n    },\n    \"tags\": [\n        {\n            \"id\": -5541178,\n            \"name\": \"commodo officia elit ad\"\n        },\n        {\n            \"id\": -19261292,\n            \"name\": \"Ut adipisicing\"\n        }\n    ],\n    \"status\": \"sold\"\n}"}, "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "response": [{"name": "Invalid ID supplied", "originalRequest": {"method": "PUT", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Validation exception", "originalRequest": {"method": "PUT", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "status": "Method Not Allowed", "code": 405, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Pet not found", "originalRequest": {"method": "PUT", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet", "host": ["{{baseUrl}}"], "path": ["pet"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Finds Pets by status", "request": {"auth": {"type": "oauth2"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/pet/findByStatus?status=available&status=available", "host": ["{{baseUrl}}"], "path": ["pet", "findByStatus"], "query": [{"key": "status", "value": "available", "description": "(Required) Status values that need to be considered for filter"}, {"key": "status", "value": "available", "description": "(Required) Status values that need to be considered for filter"}]}, "description": "Multiple status values can be provided with comma separated strings"}, "response": [{"name": "Invalid status value", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByStatus?status=available&status=available", "host": ["{{baseUrl}}"], "path": ["pet", "findByStatus"], "query": [{"key": "status", "value": "available"}, {"key": "status", "value": "available"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByStatus?status=available&status=available", "host": ["{{baseUrl}}"], "path": ["pet", "findByStatus"], "query": [{"key": "status", "value": "available"}, {"key": "status", "value": "available"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n {\n  \"name\": \"doggie\",\n  \"photoUrls\": [\n   \"Duis ad do mini\",\n   \"anim est deserunt\"\n  ],\n  \"id\": -7719727,\n  \"category\": {\n   \"id\": 74248402,\n   \"name\": \"adipisicing dolore non in\"\n  },\n  \"tags\": [\n   {\n    \"id\": -29442841,\n    \"name\": \"quis proident\"\n   },\n   {\n    \"id\": -57776947,\n    \"name\": \"eiusmod in reprehenderit ut minim\"\n   }\n  ],\n  \"status\": \"available\"\n },\n {\n  \"name\": \"doggie\",\n  \"photoUrls\": [\n   \"cupidatat enim reprehenderit\",\n   \"exercitation elit do\"\n  ],\n  \"id\": 30324756,\n  \"category\": {\n   \"id\": -51296804,\n   \"name\": \"ut deserunt Duis in\"\n  },\n  \"tags\": [\n   {\n    \"id\": 46635345,\n    \"name\": \"ullamco consectetur ut\"\n   },\n   {\n    \"id\": -50200318,\n    \"name\": \"esse Ut laborum id\"\n   }\n  ],\n  \"status\": \"sold\"\n }\n]"}]}, {"name": "Finds Pets by tags", "request": {"auth": {"type": "oauth2"}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/pet/findByTags?tags=sint&tags=anim in officia", "host": ["{{baseUrl}}"], "path": ["pet", "findByTags"], "query": [{"key": "tags", "value": "sint", "description": "(Required) Tags to filter by"}, {"key": "tags", "value": "anim in officia", "description": "(Required) Tags to filter by"}]}, "description": "Multiple tags can be provided with comma separated strings. Use tag1, tag2, tag3 for testing."}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByTags?tags=id in aliquip ea&tags=esse labore occaecat exercitation", "host": ["{{baseUrl}}"], "path": ["pet", "findByTags"], "query": [{"key": "tags", "value": "id in aliquip ea"}, {"key": "tags", "value": "esse labore occaecat exercitation"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n {\n  \"name\": \"doggie\",\n  \"photoUrls\": [\n   \"Duis ad do mini\",\n   \"anim est deserunt\"\n  ],\n  \"id\": -7719727,\n  \"category\": {\n   \"id\": 74248402,\n   \"name\": \"adipisicing dolore non in\"\n  },\n  \"tags\": [\n   {\n    \"id\": -29442841,\n    \"name\": \"quis proident\"\n   },\n   {\n    \"id\": -57776947,\n    \"name\": \"eiusmod in reprehenderit ut minim\"\n   }\n  ],\n  \"status\": \"available\"\n },\n {\n  \"name\": \"doggie\",\n  \"photoUrls\": [\n   \"cupidatat enim reprehenderit\",\n   \"exercitation elit do\"\n  ],\n  \"id\": 30324756,\n  \"category\": {\n   \"id\": -51296804,\n   \"name\": \"ut deserunt Duis in\"\n  },\n  \"tags\": [\n   {\n    \"id\": 46635345,\n    \"name\": \"ullamco consectetur ut\"\n   },\n   {\n    \"id\": -50200318,\n    \"name\": \"esse Ut laborum id\"\n   }\n  ],\n  \"status\": \"sold\"\n }\n]"}, {"name": "Invalid tag value", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: oauth2", "type": "text/plain"}, "key": "Authorization", "value": "<token>"}], "url": {"raw": "{{baseUrl}}/pet/findByTags?tags=id in aliquip ea&tags=esse labore occaecat exercitation", "host": ["{{baseUrl}}"], "path": ["pet", "findByTags"], "query": [{"key": "tags", "value": "id in aliquip ea"}, {"key": "tags", "value": "esse labore occaecat exercitation"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "store", "item": [{"name": "order", "item": [{"name": "{order Id}", "item": [{"name": "Find purchase order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId", "value": "5", "type": "string", "description": "(Required) ID of pet that needs to be fetched"}]}, "description": "For valid response try integer IDs with value >= 1 and <= 10. Other values will generated exceptions"}, "response": [{"name": "Order not found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "successful operation", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"id\": -83336720,\n \"petId\": -13451873,\n \"quantity\": 28062009,\n \"shipDate\": \"2008-12-09T07:56:54.979Z\",\n \"status\": \"delivered\",\n \"complete\": true\n}"}, {"name": "Invalid ID supplied", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Delete purchase order by ID", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId", "value": "3127555", "type": "string", "description": "(Required) ID of the order that needs to be deleted"}]}, "description": "For valid response try integer IDs with positive integer value. Negative or non-integer values will generate API errors"}, "response": [{"name": "Order not found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Invalid ID supplied", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/store/order/:orderId", "host": ["{{baseUrl}}"], "path": ["store", "order", ":orderId"], "variable": [{"key": "orderId"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Place an order for a pet", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": -83336720,\n    \"petId\": -13451873,\n    \"quantity\": 28062009,\n    \"shipDate\": \"2008-12-09T07:56:54.979Z\",\n    \"status\": \"delivered\",\n    \"complete\": true\n}"}, "url": {"raw": "{{baseUrl}}/store/order", "host": ["{{baseUrl}}"], "path": ["store", "order"]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": -83336720,\n    \"petId\": -13451873,\n    \"quantity\": 28062009,\n    \"shipDate\": \"2008-12-09T07:56:54.979Z\",\n    \"status\": \"delivered\",\n    \"complete\": true\n}"}, "url": {"raw": "{{baseUrl}}/store/order", "host": ["{{baseUrl}}"], "path": ["store", "order"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"id\": -83336720,\n \"petId\": -13451873,\n \"quantity\": 28062009,\n \"shipDate\": \"2008-12-09T07:56:54.979Z\",\n \"status\": \"delivered\",\n \"complete\": true\n}"}, {"name": "Invalid Order", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": -83336720,\n    \"petId\": -13451873,\n    \"quantity\": 28062009,\n    \"shipDate\": \"2008-12-09T07:56:54.979Z\",\n    \"status\": \"delivered\",\n    \"complete\": true\n}"}, "url": {"raw": "{{baseUrl}}/store/order", "host": ["{{baseUrl}}"], "path": ["store", "order"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Returns pet inventories by status", "request": {"auth": {"type": "apikey", "apikey": [{"key": "key", "value": "api_key", "type": "string"}, {"key": "value", "value": true, "type": "boolean"}, {"key": "in", "value": "header", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/store/inventory", "host": ["{{baseUrl}}"], "path": ["store", "inventory"]}, "description": "Returns a map of status codes to quantities"}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [{"description": {"content": "Added as a part of security scheme: apikey", "type": "text/plain"}, "key": "api_key", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/store/inventory", "host": ["{{baseUrl}}"], "path": ["store", "inventory"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{}"}]}]}, {"name": "user", "item": [{"name": "{username}", "item": [{"name": "Get user by user name", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username", "value": "reprehenderit anim ut eiusmod", "type": "string", "description": "(Required) The name that needs to be fetched. Use user1 for testing. "}]}}, "response": [{"name": "User not found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Invalid username supplied", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "successful operation", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"id\": -48579772,\n \"username\": \"labore nostrud aute\",\n \"firstName\": \"dolore amet deserunt eiusmod\",\n \"lastName\": \"voluptate U<PERSON>\",\n \"email\": \"dolore quis non dolore\",\n \"password\": \"est elit\",\n \"phone\": \"aliquip laborum eu esse\",\n \"userStatus\": 89436119\n}"}]}, {"name": "Updated user", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": -48579772,\n    \"username\": \"labore nostrud aute\",\n    \"firstName\": \"dolore amet deserunt eiusmod\",\n    \"lastName\": \"voluptate U<PERSON>\",\n    \"email\": \"dolore quis non dolore\",\n    \"password\": \"est elit\",\n    \"phone\": \"aliquip laborum eu esse\",\n    \"userStatus\": 89436119\n}"}, "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username", "value": "reprehenderit anim ut eiusmod", "type": "string", "description": "(Required) name that need to be updated"}]}, "description": "This can only be done by the logged in user."}, "response": [{"name": "Invalid user supplied", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": -48579772,\n    \"username\": \"labore nostrud aute\",\n    \"firstName\": \"dolore amet deserunt eiusmod\",\n    \"lastName\": \"voluptate U<PERSON>\",\n    \"email\": \"dolore quis non dolore\",\n    \"password\": \"est elit\",\n    \"phone\": \"aliquip laborum eu esse\",\n    \"userStatus\": 89436119\n}"}, "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "User not found", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": -48579772,\n    \"username\": \"labore nostrud aute\",\n    \"firstName\": \"dolore amet deserunt eiusmod\",\n    \"lastName\": \"voluptate U<PERSON>\",\n    \"email\": \"dolore quis non dolore\",\n    \"password\": \"est elit\",\n    \"phone\": \"aliquip laborum eu esse\",\n    \"userStatus\": 89436119\n}"}, "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Delete user", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username", "value": "reprehenderit anim ut eiusmod", "type": "string", "description": "(Required) The name that needs to be deleted"}]}, "description": "This can only be done by the logged in user."}, "response": [{"name": "Invalid username supplied", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "User not found", "originalRequest": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/:username", "host": ["{{baseUrl}}"], "path": ["user", ":username"], "variable": [{"key": "username"}]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Create user", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": -48579772,\n    \"username\": \"labore nostrud aute\",\n    \"firstName\": \"dolore amet deserunt eiusmod\",\n    \"lastName\": \"voluptate U<PERSON>\",\n    \"email\": \"dolore quis non dolore\",\n    \"password\": \"est elit\",\n    \"phone\": \"aliquip laborum eu esse\",\n    \"userStatus\": 89436119\n}"}, "url": {"raw": "{{baseUrl}}/user", "host": ["{{baseUrl}}"], "path": ["user"]}, "description": "This can only be done by the logged in user."}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": -48579772,\n    \"username\": \"labore nostrud aute\",\n    \"firstName\": \"dolore amet deserunt eiusmod\",\n    \"lastName\": \"voluptate U<PERSON>\",\n    \"email\": \"dolore quis non dolore\",\n    \"password\": \"est elit\",\n    \"phone\": \"aliquip laborum eu esse\",\n    \"userStatus\": 89436119\n}"}, "url": {"raw": "{{baseUrl}}/user", "host": ["{{baseUrl}}"], "path": ["user"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Creates list of users with given input array", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n    {\n        \"id\": 81310232,\n        \"username\": \"ea reprehenderit est et\",\n        \"firstName\": \"Ut do id\",\n        \"lastName\": \"aliqua\",\n        \"email\": \"commodo veniam\",\n        \"password\": \"amet nostrud\",\n        \"phone\": \"ipsum consectetur\",\n        \"userStatus\": -82545484\n    },\n    {\n        \"id\": 52399122,\n        \"username\": \"adipisicing et aliqua aliquip\",\n        \"firstName\": \"incididunt in\",\n        \"lastName\": \"nisi incididunt adipisicin\",\n        \"email\": \"ullamco off\",\n        \"password\": \"consectetur nisi ad aliquip\",\n        \"phone\": \"aliquip \",\n        \"userStatus\": -67551108\n    }\n]"}, "url": {"raw": "{{baseUrl}}/user/createWithArray", "host": ["{{baseUrl}}"], "path": ["user", "createWithArray"]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/user/createWithArray", "host": ["{{baseUrl}}"], "path": ["user", "createWithArray"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Creates list of users with given input array", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "[\n    {\n        \"id\": 37974193,\n        \"username\": \"mollit\",\n        \"firstName\": \"reprehen\",\n        \"lastName\": \"nulla ea et\",\n        \"email\": \"ad\",\n        \"password\": \"labore ullamco eiusmod\",\n        \"phone\": \"quis aliqua nisi\",\n        \"userStatus\": 20065668\n    },\n    {\n        \"id\": -60203902,\n        \"username\": \"dolor\",\n        \"firstName\": \"occaecat incididunt\",\n        \"lastName\": \"in sunt enim Ut Excepteur\",\n        \"email\": \"consectetur officia proident exercitation\",\n        \"password\": \"sit fugiat ea\",\n        \"phone\": \"ad ut\",\n        \"userStatus\": -12912581\n    }\n]"}, "url": {"raw": "{{baseUrl}}/user/createWithList", "host": ["{{baseUrl}}"], "path": ["user", "createWithList"]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/user/createWithList", "host": ["{{baseUrl}}"], "path": ["user", "createWithList"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Logs user into the system", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/login?username=reprehenderit anim ut eiusmod&password=reprehenderit anim ut eiusmod", "host": ["{{baseUrl}}"], "path": ["user", "login"], "query": [{"key": "username", "value": "reprehenderit anim ut eiusmod", "description": "(Required) The user name for login"}, {"key": "password", "value": "reprehenderit anim ut eiusmod", "description": "(Required) The password for login in clear text"}]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/login?username=reprehenderit anim ut eiusmod&password=reprehenderit anim ut eiusmod", "host": ["{{baseUrl}}"], "path": ["user", "login"], "query": [{"key": "username", "value": "reprehenderit anim ut eiusmod"}, {"key": "password", "value": "reprehenderit anim ut eiusmod"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "X-Expires-After", "value": "1956-08-30T20:21:32.967Z", "description": "date in UTC when token expires"}, {"key": "X-Rate-Limit", "value": "79414423", "description": "calls per hour allowed by the user"}, {"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "\"reprehenderit anim ut eiusmod\""}, {"name": "Invalid username/password supplied", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/login?username=reprehenderit anim ut eiusmod&password=reprehenderit anim ut eiusmod", "host": ["{{baseUrl}}"], "path": ["user", "login"], "query": [{"key": "username", "value": "reprehenderit anim ut eiusmod"}, {"key": "password", "value": "reprehenderit anim ut eiusmod"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Logs out current logged in user session", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/logout", "host": ["{{baseUrl}}"], "path": ["user", "logout"]}}, "response": [{"name": "successful operation", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/user/logout", "host": ["{{baseUrl}}"], "path": ["user", "logout"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "https://petstore.swagger.io/v2"}]}