const debug = require('debug')('SendVulnToWAS')
const fs = require('fs')
const mkdirp = require('mkdirp')
const BasePlugin = require('../base-plugin')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const logger = require('../../../../common/lib/haiku-logger')
const ssAPI = require('../../../../common/lib/sooper-scheduler-api')
const _ = require('lodash')
const ExternalResource = require('../../../../app/plugins/core/external-resource')

/**
 * Plugin that will append vulnerability info to a file 
 */
class SendVulnToWAS extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the netork scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // temp till output plugin ready
        this.vulnsOutPath = './vulns'
        mkdirp(this.vulnsOutPath)

        // event handler
        this.networkScanner.on('vulnerability-found', this.appendVulnToFile.bind(this))
        this.alertmd5 = 0;
    }

    /**
     * get the standardized vulnerability as described in document @link: <need link here>
     * @param {Object} vuln Object describing attack and vulns found.
     */
    getStandardizedVulnObject(vuln) {
        let standardizedVuln = {
            // common info
            scanId: vuln.scanId,
            scanlogId: vuln.scanlogId,
            founddate: vuln.founddate || (new Date).toISOString(),
            key: HaikuUtils.getIGWKey(vuln),
            uriGroupingKey: 'none', // placeholder

            // original request
            original: {},

            // attack
            attack: {},

            // vulnerabilities found
            vulns: vuln.vulns
        }

        // attack
        let attack = standardizedVuln.attack

        // copy the attack properties over
        let attackProperties = ['name', 'hostname', 'href', 'area', 'type', 'param', 'vector', 'encoding']
        for (let prop of attackProperties) {
            attack[prop] = _.get(vuln, 'attack.' + prop)
        }
        attack.httpRequest = this.getHttpRequest(vuln, 'attack');
        attack.httpResponse = this.getHttpResponse(vuln, 'attack');

        // original request
        let original = standardizedVuln.original
        original.scanner = _.get(vuln, 'attack.originalRequest.scanner')
        original.haikuResourceType = _.get(vuln, 'attack.originalRequest.httpRequest.haikuResourceType')
        original.haikuKey = _.get(vuln, 'attack.originalRequest.httpRequest.haikuKey')
        original.httpRequest = this.getHttpRequest(vuln, 'attack.originalRequest');

        // if the attack was the original crawler request, original request response is the attack response 
        original.httpResponse = (attack.area == 'original-crawler-request') ? attack.httpResponse : this.getHttpResponse(vuln, 'attack.originalRequest')

        // generate grouping Key - used by WAS to group vulns found
        standardizedVuln.uriGroupingKey = HaikuUtils.generateGroupingKeyForRequest(original.httpRequest)

        // add result to vulns
        // ** 
        // this code is a copy of 'send-vuln-to-soc.js::appendVulnToFile() 
        // not bothering to change/refactor this for now since we plan to replace that API 
        //**
        for (let haikuVulnId of Object.keys(standardizedVuln.vulns)) {
            let thisVuln = standardizedVuln.vulns[haikuVulnId]
            let vulnInfo = _.get(this.getConfig(standardizedVuln).Plugins[thisVuln.foundBy], `vulnerabilities[${haikuVulnId}]`, {})

            // common & vulnerability info
            thisVuln.productionReady = vulnInfo.productionReady || thisVuln.productionReady
            thisVuln.vulnerabilityId = vulnInfo.igwId
            thisVuln.result = HaikuUtils.getIGWResult(standardizedVuln, haikuVulnId)
        }

        standardizedVuln.rootActionCount = _.get(vuln, 'attack.httpRequest.crawlerBookmark.rootActionCount', 0);   

        return standardizedVuln
    }

    /**
     * Get standardized httpRequest 
     * @param {Object} vuln object describing attack and vulnerabilities
     * @param {string} srcProperty path from vuln object containing the httpRequest
     */
    getHttpRequest(vuln, srcProperty) {
        let httpRequestProperties = ['method', 'headers', 'body', 'uri', 'name']
        let httpReq = {}
        for (let prop of httpRequestProperties) {
            httpReq[prop] = _.get(vuln, srcProperty + '.httpRequest.' + prop);
        }

        // Add http request line
        try {
            let parsedUrl = new URL(httpReq['uri'])
            let httpVersion = 'HTTP/1.1'
            httpReq['requestLine'] = httpReq['method'] + ' ' + parsedUrl.pathname + parsedUrl.search + ' ' + httpVersion

            // add host header if it is not already there
            if (!httpReq['headers']['host'] && !httpReq['headers']['Host']) {
                // before setting Host header do a deep clone so that it does not alter original request
                // this fix is done for avoing host and Host in report
                httpReq = _.cloneDeep(httpReq)
                httpReq['headers']['Host'] = parsedUrl.host
            }
        } catch (err) {
            logger.log('error', `sendVulnToWas::getHttpRequest(): ${err.toString()}`, HaikuUtils.getMetadataForLog(vuln))
        }

        return httpReq
    }

    /**
     * Get standardized httpResponse
     * @param {Object} vuln object describing attack and vulnerabilities
     * @param {string} srcProperty path from vuln object containing the httpResponse
     */
    getHttpResponse(vuln, srcProperty) {
        // only return an objevt if we had got a response
        if (!_.get(vuln, srcProperty + '.httpResponse')) {
            return null
        }

        let httpResponseProperties = ['statusCode', 'statusMessage', 'headers', 'body']
        let httpResp = {}
        for (let prop of httpResponseProperties) {
            httpResp[prop] = _.get(vuln, srcProperty + '.httpResponse.' + prop);
        }

        // Add http response line
        let httpVersion = httpResp['httpVersion'] || 'HTTP/1.1'
        httpResp['responseLine'] = httpVersion + ' ' + httpResp['statusCode'] + ' ' + httpResp['statusMessage']

        return httpResp
    }

    /** 
     * @param {vuln} vuln Information on vulnerablities found in one network request
     */
    async appendVulnToFile(vuln) {
        // if we find vulnerabilities, send event
        logger.log('info', `VULNERABILITY FOUND count=(${Object.keys(vuln.vulns).length}) - ${JSON.stringify(Object.getOwnPropertyNames(vuln.vulns))}`, HaikuUtils.getMetadataForLog(vuln))

        // temporarily write both the raw vuln object & the standardized vuln object
        let hostname = _.get(vuln, 'attack.hostname') || 'GLOBAL-ERROR'
        fs.appendFile(`${this.vulnsOutPath}/${hostname}-vulns-${vuln.scanlogId}.json`, JSON.stringify(vuln), (err) => {
            if (err) {
                logger.log('error', `could not append vulnerability to file: ${err}`,HaikuUtils.getMetadataForLog(vuln))
            }
        })

        try {
            let stdVuln = this.getStandardizedVulnObject(_.cloneDeep(vuln));

            // Pre update alertDetails before receiving response from WAS API for alertmd5 details to avoid vuln.vulns by network-scanner updateFoundVulns function
            for (let haikuVulnId of Object.keys(stdVuln.vulns)) {
                try {
                    if(this.networkScanner.runningScans && this.networkScanner.runningScans[vuln.scanId]) {
                        let scanData = this.networkScanner.runningScans[vuln.scanId];
                        scanData.alertDetails = scanData.alertDetails || {};
                        let keys = HaikuUtils.getRevalidationAndAttackKey(vuln);
                                            
                        if(keys) {
                            let {
                                revalidationKey,
                                attackKey
                            } = keys;
                            scanData.alertDetails[revalidationKey] = scanData.alertDetails[revalidationKey] || {};
                            scanData.alertDetails[revalidationKey][attackKey] = scanData.alertDetails[revalidationKey][attackKey] || {};
                            scanData.alertDetails[revalidationKey][attackKey][haikuVulnId] = scanData.alertDetails[revalidationKey][attackKey][haikuVulnId] || {};
                            logger.log('info', `Pre updated alertDetails revalidationKey = ${revalidationKey} | attackKey = ${attackKey} | vulnId = ${haikuVulnId}`, HaikuUtils.getMetadataForLog(vuln));
                        }
                        else {
                            logger.log('error', `Pre update alertDetails error unable to getRevalidationAndAttackKey`, HaikuUtils.getMetadataForLog(vuln))
                        }
                    }
                    else {
                        logger.log('error', `Pre update alertDetails error unable to get scanData`, HaikuUtils.getMetadataForLog(vuln))
                    }
                } catch (error) {
                    logger.log('error', `Pre update alertDetails error: ${error.toString()}`, HaikuUtils.getMetadataForLog(vuln))
                }
            }

            let replayScanInfo = this.getConfig(vuln.scanId).ScannerSettings.replayScanInfo;
            let privilegeStatus = await this.getPrivilegeStatus(stdVuln);

            // Call WAS API. WAS API prefers to get one vuln at a time
            for (let haikuVulnId of Object.keys(stdVuln.vulns)) {
                let thisVuln = _.cloneDeep(stdVuln);
                thisVuln.vulns = {};
                thisVuln.vulns[haikuVulnId] = stdVuln.vulns[haikuVulnId];
                thisVuln.privilegeStatus = privilegeStatus;
                let response = await ssAPI.vulnerabilityFoundV2(thisVuln);
                
                if(!replayScanInfo.isReplayScan) {
                    try {
                        //Comment this code in debug mode
                        if(response.status && response.status.toLowerCase() == 'ok') {
                            if(response.result[haikuVulnId]) {
                                try {
                                    let keys = HaikuUtils.getRevalidationAndAttackKey(vuln);
                                    let revalidationKey = keys ? keys.revalidationKey : null;
                                    let attackKey = keys ? keys.attackKey : null;

                                    if(this.networkScanner.runningScans && this.networkScanner.runningScans[vuln.scanId]) {
                                        let scanData = this.networkScanner.runningScans[vuln.scanId];
                                        scanData.alertDetails = scanData.alertDetails || {};
                                        let keys = HaikuUtils.getRevalidationAndAttackKey(vuln);
                                        
                                        if(revalidationKey && attackKey) {
                                            scanData.alertDetails[revalidationKey] = scanData.alertDetails[revalidationKey] || {};
                                            scanData.alertDetails[revalidationKey][attackKey] = scanData.alertDetails[revalidationKey][attackKey] || {};
                                            scanData.alertDetails[revalidationKey][attackKey][haikuVulnId] = scanData.alertDetails[revalidationKey][attackKey][haikuVulnId] || {};
                                            scanData.alertDetails[revalidationKey][attackKey][haikuVulnId].uniqueId = response.result[haikuVulnId].uniqueId;
                                            scanData.alertDetails[revalidationKey][attackKey][haikuVulnId].alertmd5 = response.result[haikuVulnId].alertmd5;
                                            logger.log('info', `Updated alertmd5 in alertDetails revalidationKey = ${revalidationKey} | attackKey = ${attackKey} | vulnId = ${haikuVulnId} | alertmd5 = ${response.result[haikuVulnId].alertmd5}`, HaikuUtils.getMetadataForLog(vuln));
                                        }
                                        else {
                                            logger.log('error', `Unable to getRevalidationAndAttackKey`, HaikuUtils.getMetadataForLog(vuln))
                                        }
                                    }
                                    else {
                                        logger.log('error', `Unable to get scanData when updating alertmd5`, HaikuUtils.getMetadataForLog(vuln));
                                    }
    
                                    if(vuln.vulns[haikuVulnId]) {
                                        vuln.vulns[haikuVulnId].uniqueId = response.result[haikuVulnId].uniqueId;
                                        vuln.vulns[haikuVulnId].alertmd5 = response.result[haikuVulnId].alertmd5;
                                        logger.log('info', `Updated alertmd5 in vulnData revalidationKey = ${revalidationKey} | attackKey = ${attackKey} | vulnId = ${haikuVulnId} | alertmd5 = ${response.result[haikuVulnId].alertmd5}`, HaikuUtils.getMetadataForLog(vuln));
                                    }
                                    else {
                                        logger.log('error', `Unable to update alertmd5 in vulnData because vulnData is not available for vulnId = ${haikuVulnId}`, HaikuUtils.getMetadataForLog(vuln));
                                    }
                                } catch (error) {
                                    logger.log('error', `Unable to update alertmd5 in alertDetails: ${error.toString()}`, HaikuUtils.getMetadataForLog(vuln))
                                }
                            }
                            else {
                                //Log error for tracking missing vuln during replay scan. Possible reason why it can not replayed because
                                //alertmd5 was not return by WAS.
                                logger.log('error', `WAS replied vuln without alertmd5: ${JSON.stringify(response)}`, HaikuUtils.getMetadataForLog(vuln))
                                logger.log('info', `WAS replied vuln without alertmd5: Vuln Data: ${JSON.stringify(vuln)}`, HaikuUtils.getMetadataForLog(vuln))
                            }
                        }
                        else {
                            //Log error for tracking missing vuln during replay scan. Possible reason why it can not replayed because
                            //alertmd5 was not return by WAS.
                            logger.log('error', `WAS replied error: ${JSON.stringify(response)}`, HaikuUtils.getMetadataForLog(vuln))
                            logger.log('info', `WAS replied error: Vuln Data: ${JSON.stringify(vuln)}`, HaikuUtils.getMetadataForLog(vuln))
                        }
                    } catch (error) {
                        logger.log('error', `Unable to update alertmd5, reason: ${error.toString()}`, HaikuUtils.getMetadataForLog(vuln))
                        logger.log('info', `Unable to update alertmd5, WAS Response: ${JSON.stringify(response)}`, HaikuUtils.getMetadataForLog(vuln))
                        logger.log('info', `Unable to update alertmd5, Vuln Data: ${JSON.stringify(vuln)}`, HaikuUtils.getMetadataForLog(vuln))
                    }

                    // //Uncomment below code in debug mode
                    // if(response.status && response.status.toLowerCase() == 'ok') {
                    //     if(response.body.vulns[haikuVulnId]) {
                    //         try {
                    //             this.alertmd5++;
                    //             if(this.networkScanner.runningScans && this.networkScanner.runningScans[vuln.scanId]) {
                    //                 let scanData = this.networkScanner.runningScans[vuln.scanId];
                    //                 scanData.alertDetails = scanData.alertDetails || {};
                    //                 let keys = HaikuUtils.getRevalidationAndAttackKey(vuln);
                                    
                    //                 if(keys) {
                    //                     let {
                    //                         revalidationKey,
                    //                         attackKey
                    //                     } = keys;
                    //                     scanData.alertDetails[revalidationKey] = scanData.alertDetails[revalidationKey] || {};
                    //                     scanData.alertDetails[revalidationKey][attackKey] = scanData.alertDetails[revalidationKey][attackKey] || {};
                    //                     scanData.alertDetails[revalidationKey][attackKey][haikuVulnId] = scanData.alertDetails[revalidationKey][attackKey][haikuVulnId] || {};
                    //                     scanData.alertDetails[revalidationKey][attackKey][haikuVulnId].alertmd5 = 'alertmd5_' + this.alertmd5;
                    //                 }
                    //             }

                    //             if(vuln.vulns[haikuVulnId]) {
                    //                 vuln.vulns[haikuVulnId].alertmd5 = 'alertmd5_' + this.alertmd5;
                    //             }
                    //         } catch (error) {
                    //             logger.log('error', `Unable to update alertmd5 in alertDetails: ${error}`, HaikuUtils.getMetadataForLog(vuln))
                    //         }
                    //     }
                    // }
                }
            }

            // also write to disk
            fs.appendFile(`${this.vulnsOutPath}/${hostname}-std-vulns-${vuln.scanlogId}.json`, JSON.stringify(stdVuln), (err) => {
                if (err) {
                    logger.log('error', `could not append standardized vulnerability to file: ${err}`, HaikuUtils.getMetadataForLog(vuln))
                }
            })
        } catch (e) {
            logger.log('error', `Exception -> could not append standardized vulnerability to file: ${e}`, HaikuUtils.getMetadataForLog(vuln))
        }
    }

    /**
     * 
     * @param {Object} vuln Vulnerability object for which privilege status need to be identified. 
     * @returns {Boolean} If true then vulnerability protected by session. If false then vulnerability is not protected by session 
     */
    async getPrivilegeStatus(vuln) {
        try {
            let statusCode = _.get(vuln, 'attack.httpResponse.statusCode');

            if(statusCode == 200) {
                let httpRequest = _.cloneDeep(_.get(vuln, 'attack.httpRequest'));
                HaikuUtils.clearSession(httpRequest);
                let result = await ExternalResource.makeRequest(httpRequest.method, httpRequest.uri, httpRequest.headers, httpRequest.body);

                //Return privilege as true if uri is not accessible without session. Hence protected by privilege.
                if(!result.error && result.response && result.response.statusCode != 200) {
                    return true;
                }
            }
        } catch (error) {
            logger.log('error', `Unable to getPrivilegeStatus: ${error.toString()}`, HaikuUtils.getMetadataForLog(vuln))
        }

        return false;
    }
}

module.exports = SendVulnToWAS