const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:UpdateSessionInfo')


/** 
 * Message from crawler -> scanner sending the latest session info
 * @extends QueueMessage
 */
class UpdateSessionInfo extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }

    /**
     * @typedef {Object} UpdateSessionInfoMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request
     * @param {sessionInfo} sessionInfo as returned by {@link HaikuUtils.getSessionInfo}
     */
    /**
     * @param {UpdateSessionInfoMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner'
        this.routingKey = 'request.update-session-info'
        this.msgType = UpdateSessionInfo.msgType
    }

}

module.exports = UpdateSessionInfo