const debug = require('debug')('NetworkAttack')
const fs = require('fs')
const BasePlugin = require('./base-plugin')
const cheerio = require("cheerio")
const _ = require('lodash')
const URL = require('url').URL
const HTTPRequestMonster = require('../../lib/http-request')
const logger = require('../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const s3Utils = require('../../../common/lib/s3-utils')

const defaultOptionsForCheck = {
    minMatchLength: 1,
    maxMatchesToReturn: 5,
    addVulnerabilitytoResult: true
}

/**
 * base class for all network based attacks. Common way to send attack and event on response
 * Also takes care of collecting info about attack & vulnerability
 * 
 */
class NetworkAttack extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
        _.templateSettings.interpolate = /{{([\s\S]+?)}}/g;
        this.oobAttackPathPrefix = 'scanner/oob-attacks/';
        this.replayHttpResponsePathPrefix = 'scanner/replayHttpResponse/';
    }

    /**
     * Sets up event handlers and provides good default implementation for them
     * Sub classes should override the event handlers if  they need custom behaviour. Most 
     * of these will call a method (conventially named process<eventname> for on<eventname>
     * handler)
     */
    setupEventHandlers() {
        super.setupEventHandlers()
        this.networkScanner.on('attack-response', this.onAttackResponse.bind(this))
    }

    // -- default event handlers
    /**
     * Default, do nothing processAttackResponse. subclasses should override if they need to check
     * @param {attack} attack the attack that was performed
     */
    processAttackResponse(attack) {
        // default does nothing
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     */
    wantProcessAttackResponse(attack) {
        // default returns true so plugin's processttackResponse() will be called
        return true
    }

    /**
     * default attack-response handler
     * @param {attack} attack the attack that was performed
     */
    async onAttackResponse(attack) {
        // see if this plugin is prohibited from checking responses
        // dio it here directly since a plugin disabled in config should not count towards the stats
        if (!this.canProcessAttackResponse(attack)) {
            return false
        }

        let start = process.hrtime()
        let processResponse = true // we are optimists

        let resp = attack.result.resp

        // don't process when there is an error in the network request
        if (resp.err) {
            processResponse = false
        }

        // **********
        // sanity check that we will remove after a few week of testing - this is insurance...
        // **********
        if (resp.isDisallowedContentType) {
            logger.log('info', 'Whoa Whoa Whoa - call Karthik, something screwed up with isDisallowedContentType', HaikuUtils.getMetadataForLog(attack))
            processResponse = false
        }

        // Check if the request was only meant to be processed by certain plugins
        if (processResponse) {
            if (!this.canAttackResource(attack)) {
                processResponse = false
            }
        }

        // Since we allow some plugins to be interested in certain content-types, 
        // check if this plugin wants this content-type
        if (processResponse) {
            // get this plugin specific allowed types or the default.
            let allowedContentTypes = this.getMetadata(attack).allowedContentTypes || this.getConfig(attack).HTTPRequestMonster.allowedContentTypes

            // does this plugin allow this content-type?
            let fullResponse = _.get(attack, 'result.resp.fullResponse')
            let contentType = _.get(fullResponse, 'headers[content-type]', 'text/html')
            processResponse = allowedContentTypes.test(contentType)
        }

        if (processResponse) {
            // if we have hit the max reportable limit for all vulns in this plugin, don't process
            // later we can decide if it makes more sense to find all and report only the max i.e.
            // in the last one say '... and nnn more'
            processResponse = this.vulnerabilitiesCanBeReported(attack)
        }

        if (processResponse) {
            try {
                // see if plugin wants to process this attack response.
                processResponse = this.wantProcessAttackResponse(attack)
                if (processResponse) {
                    let ret = this.processAttackResponse(attack)
                    if (Promise.resolve(ret) == ret) { // is it a promise
                        attack.processResponsePromises.push(ret)
                    }
                }
            } catch (err) {
                logger.log('info', `Whoa Whoa Whoa -  Plugin ${this.getName()} threw exception in processAttackResponse !!! Ignoring. ${err.toString()} `, HaikuUtils.getMetadataForLog(attack))
            }
        }

        let end = process.hrtime(start)
        let timeTakenMillis = (end[0] * 1000) + (end[1] / 1e6)
        if (this.networkScanner.runningScans[attack.httpRequest.scanId]) {
            let stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.pluginTimings
            let name = this.getName()
            if (stats[name]) {
                stats[name] = stats[name] + timeTakenMillis
            } else {
                stats[name] = timeTakenMillis
            }
        }
    }

    /**
     * check if this plugin wants to attack this resource type.
     * @param {attack} attack The attackthat is to be performed or has just been performed
     */
    canAttackResource(attack) {
        let resourcestoAttack = this.getMetadata(attack).resourcesToAttack || this.getConfig(attack).DefaultPluginSettings.resourcesToAttack
        let curResourceType = _.get(attack, 'originalRequest.httpRequest.haikuResourceType', 'core') // unknown resource type -> play safe and attack.
        return resourcestoAttack.includes(curResourceType)
    }

    /**
     * @param {Object} attack attack object to get the site specific metadat from.
     * @param {string} vulnID Haiku specific vuln ID (like 'ID-sql-injection') 
     * @returns {bool} true if this vulnerability ID is ok to show to customers. This setting is 
     * vulnerability specific in the config
     */
    isProductionReady(attack, vulnID) {
        return !!_.get(this.getMetadata(attack), `vulnerabilities['${vulnID}'].productionReady`)
    }

    /**
     * @param {Object} attack attack object to get the site specific metadat from.
     * @param {string} vulnID Haiku specific vuln ID (like 'ID-sql-injection') 
     * @returns {bool} true if this vulnerability ID is ok to be attack in replay scan. This setting is 
     * vulnerability specific in the config
     */
    canAttackInReplayScan(attack, vulnID) {
        return !!_.get(this.getMetadata(attack), `vulnerabilities['${vulnID}'].canAttackInReplayScan`)
    }

    /**
     * 
     * @param {attack} attack The attack object 
     * @param {string} vulnID Haiku specific vuln ID (like 'ID-sql-injection') 
     * @param {object} details Details about the vulnerability
     */
    addVulnerabilitytoResult(attack, vulnID, details) {
        // check if this is a replay scan and if so, add the attack info against alertmd5 so that we can report it to WAS portal
        try {
            let scanMetaData = HaikuUtils.getMetadataForLog(attack);

            if (scanMetaData && scanMetaData.scanId > 0) {
                let replayScanInfo = this.getConfig(scanMetaData.scanId).ScannerSettings.replayScanInfo;
                
                // calculate alertmd5 for the replaying vulnerability so we can store attack information in scanData
                // which then can be reported at end of scan to WAS portal - Ref WAS-970
                if (replayScanInfo.isReplayScan) {
                    let scanData = this.networkScanner.runningScans[scanMetaData.scanId];

                    if (scanData) {
                        let vulnObject = HaikuUtils.getVulnerabilityByVulnerabilityName(vulnID);

                        if (vulnObject) {
                            let igWKey = HaikuUtils.getIGWKey({
                                attack: attack
                            });

                            let alertmd5 = HaikuUtils.getAlertMD5(scanMetaData.serviceId, vulnObject.igwId, igWKey);
                            let chmVulnsToReplay = _.get(replayScanInfo, `chmVulnsToReplay`);

                            if(chmVulnsToReplay && !chmVulnsToReplay[vulnID]) {
                                logger.log('info', `not reporting vulnerability for ${vulnID} since during replay scan it was not found in chmVulnsToReplay`, HaikuUtils.getMetadataForLog(attack));
                                return;
                            }

                            scanData.alertmd5Payload = scanData.alertmd5Payload || {};
                            scanData.alertmd5Payload[alertmd5] = scanData.alertmd5Payload[alertmd5] || [];
                            let responseBody = _.get(attack, 'result.resp.body', '');
                            let responseFileName = '';
                            let folderPath = this.replayHttpResponsePathPrefix + scanMetaData.scanId;
                            
                            //Store response body in s3/efs storage & return path prefix which can updated in alertmd5Payload
                            if(responseBody) {
                                // running count of saved responses as unique id
                                let savedResponseCnt = this.networkScanner.getScanInfo(scanMetaData.scanId).savedResponseCnt++
                                responseFileName = `attack-${savedResponseCnt}.body`;
                                s3Utils.upload(folderPath, responseFileName, responseBody);
                            }

                            let vuln = {};
                            vuln.vulns = {};
                            vuln.vulns[vulnID] = {
                                foundBy: this.getName(),
                                productionReady: this.isProductionReady(attack, vulnID),
                                canAttackInReplayScan: this.canAttackInReplayScan(attack, vulnID),
                                details: details
                            }

                            let result = HaikuUtils.getIGWResult(vuln, vulnID);

                            scanData.alertmd5Payload[alertmd5].push({
                                vulnName: vulnID,
                                vulnerabilityId: vulnObject.igwId,
                                uri: attack.href,
                                method: _.get(attack, 'httpRequest.method'),
                                key: igWKey,
                                attackArea: attack.attackArea,
                                pluginName: attack.pluginName,
                                param: attack.param,
                                paramVal: attack.paramVal,
                                vector: attack.vector,
                                encoding: attack.encoding,
                                attackType: attack.attackType,
                                headers: _.get(attack, 'httpRequest.headers'),
                                body: _.get(attack, 'httpRequest.body'),
                                statusCode: _.get(attack, 'result.resp.fullResponse.statusCode', 0),
                                responseHeader: _.get(attack, 'result.resp.fullResponse.headers', null),
                                responseBody: responseBody ? {
                                    folderPath: folderPath,
                                    fileName: responseFileName
                                } : '',
                                result: result
                            });
                        }
                        else {
                            logger.log('error', `Unable to find vulnerability object for ${vulnID}`, HaikuUtils.getMetadataForLog(attack))
                        }
                    }
                }
            }
        } catch (error) {
            logger.log('error', `Unable to add vulnerability to result: ${error.toString()}`, HaikuUtils.getMetadataForLog(attack))
        }   

        // check if we should add this to list of vulnerabilities.
        let {
            okToReport,
            foundVulnCount,
            maxVulnsToReport
        } = this.shouldReportVulnerability(attack, vulnID)

        if (okToReport) {
            // check if we have already reported this vuln and track key if we found a new unique/reportable one.
            let scanSpecificPluginStore = this.getPluginScopedStore(attack, 'this-scan')

            // get the unique key for this attack.
            let keyUri = new URL(_.get(attack, 'originalRequest.httpRequest.uri') || attack.href || _.get(attack, 'httpRequest.uri') || 'http://no.uri.in.attack')
            let attackedParam = attack.param || ''
            if (attack.attackArea == 'BaseURI') {
                // if it is attack type 'BaseURI' create href as origin + param and nuke param 
                // like 'http://www.xyz.com/a/b/c', param=''
                let url = new URL(attack.href);
                url.pathname = attackedParam;
                keyUri = url;
                attackedParam = '';
            }
            let {
                params
            } = HaikuUtils.splitIntoParamsAndValues(attack.httpRequest)
            let key = attack.httpRequest.method + '-' + HaikuUtils.canonacalizeHost(keyUri) + HaikuUtils.pathnameSansSession(keyUri.pathname) + '?' + params.join('&') + '||' + HaikuUtils.stringifyParam(attackedParam)
            let foundKeys = _.get(scanSpecificPluginStore, `['${vulnID}'].foundKeys`) || []
            if (foundKeys.includes(key)) {
                okToReport = false // already reported - don't report again
            } else {
                foundKeys.push(key)
            }
            _.set(scanSpecificPluginStore, `['${vulnID}'].foundKeys`, foundKeys)

            // do additional check for IG key so that we don't see any discrepency between what we report
            // and alerts in the WAS portal
            attack.area = attack.attackArea
            let igKey = HaikuUtils.getIGWKey({
                attack
            })
            let foundIgKeys = _.get(scanSpecificPluginStore, `['${vulnID}'].foundIgKeys`) || []
            if (foundIgKeys.includes(igKey)) {
                if (okToReport) {
                    logger.log(`skipping vulnerability at IG key check : Haiku key ${key}, ig key ${igKey}`)
                }
                okToReport = false // already reported - don't report again
            } else {
                foundIgKeys.push(igKey)
            }
            _.set(scanSpecificPluginStore, `['${vulnID}'].foundIgKeys`, foundIgKeys)
        }

        if (okToReport) {
            // add this vulnerability to list of vulnerabilities.
            let autoPOC = _.get(attack, `result.vulns.${vulnID}.vulnInfo`, null);

            let vulnInfo = {
                foundBy: this.getName(),
                productionReady: this.isProductionReady(attack, vulnID),
                canAttackInReplayScan: this.canAttackInReplayScan(attack, vulnID),
                details: details,
                autoPOC: autoPOC ? autoPOC : []
            }
            
            _.set(attack, `result.vulns[${vulnID}]`, vulnInfo)
            
            if(details.foundVulnInResponseBody) {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', 'text', _.isArray(details.details) ? details.details : [details.details], `following attack vectors has been found in attack response body. vectors=> ${details.details}`);
            }
            
            this.onAutoPOC(attack, vulnID);
        } else {
            logger.log('info', `not reporting vulnerability for ${vulnID} since it's disallowed in config OR found = ${foundVulnCount} > max allowed = ${maxVulnsToReport} OR already reported this vuln`, HaikuUtils.getMetadataForLog(attack))
        }
    }

    /**
     * Checks if we have already reported max vulnerabilities for given vuln ID
     * @param {attack} attack The attack object 
     * @param {string} vulnID Haiku specific vuln ID (like 'ID-sql-injection') 
     */
    shouldReportVulnerability(attack, vulnID) {
        let scanSpecificPluginStore = this.getPluginScopedStore(attack, 'this-scan')
        let foundKeys = _.get(scanSpecificPluginStore, `['${vulnID}'].foundKeys`)
        let foundVulnCount = foundKeys ? foundKeys.length : 0

        // Check if the vulnerability is disabled altogether
        let disable = _.get(this.getMetadata(attack), `vulnerabilities['${vulnID}'].disable`, false);
        if (disable === true) {
            logger.log('info', `not reporting vulnerability for ${vulnID} because disable flag is set to true`, HaikuUtils.getMetadataForLog(attack));
            return { okToReport: false, foundVulnCount, maxVulnsToReport: 0 };
        }

        // Check if the vulnerability is disabled based on status codes
        let statusCodes = _.get(this.getMetadata(attack), `vulnerabilities['${vulnID}'].disableOnStatusCodes`, []);

        if(_.isArray(statusCodes) && !_.isEmpty(statusCodes)) {
            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 200);
            
            if (this.isStatusCodeDisabled(statusCode, statusCodes)) {
                return { okToReport: false, foundVulnCount, maxVulnsToReport: 0 };
            }
        }

        // check if we should add this to list of vulnerabilities.
        let maxVulnsToReport = _.get(this.getMetadata(attack).vulnerabilities[vulnID], 'maxInstancesOfEachVulnToReport');
        maxVulnsToReport = maxVulnsToReport || this.getConfig(attack).DefaultPluginSettings.maxInstancesOfEachVulnToReport;

        // check if plugin is prohibited reporting this vulnerability by config setting
        let siteSpecificReportVuln = _.get(this.getMetadata(attack).vulnerabilities[vulnID], 'reportVulnerability')
        let okToReport = _.isBoolean(siteSpecificReportVuln) ?
            siteSpecificReportVuln :
            this.getConfig(attack).DefaultPluginSettings.reportVulnerability

        // also check if we have reached the max vulns to report
        okToReport = okToReport && (maxVulnsToReport == -1 || foundVulnCount < maxVulnsToReport)
        return {
            okToReport,
            foundVulnCount,
            maxVulnsToReport
        };
    }

    /**
     * Checks if all vulnerabilities detected by this plugin have reached the maximum allowed
     * to report. This can be used to optimize attack/response
     * @param {attack} attack The attack object 
     */
    vulnerabilitiesCanBeReported(attack) {
        let atLeastOneVulnIDNeedsToBeChecked = false
        for (let vulnID of Object.keys(this.getMetadata(attack).vulnerabilities)) {
            if (this.shouldReportVulnerability(attack, vulnID).okToReport) {
                atLeastOneVulnIDNeedsToBeChecked = true
                break
            }
        }

        return atLeastOneVulnIDNeedsToBeChecked
    }

    /**
     * @typedef {Object} attack
     * @property {httpRequest} attack.httpRequest The http request i.e. attack
     * @property {httpResult} attack.result HTTP result i.e. attack repsonse.
     * @property {Number} attack.scanId Scan ID (aa alert ID)
     * @property {Number} attack.scanlogId Scan log ID of this scan
     * @property {String} attack.pluginName Plugin/Attack Name like SQL Injection/XSS. filled in from attack plugin name
     * @property {String} attack.hostname Hostname (domain) being attacked
     * @property {String} attack.href Full URI of attack
     * @property {String} attack.attackArea What is being attacked eg. POST parameter, UTI Path 
     * @property {String} attack.attackType Plugin dependent eg. param, value
     * @property {String} attack.param Parameter being attacked for parameterized attacks
     * @property {String} attack.vector The attack vector used
     * @property {String} attack.encoding Which encoding was used for the attack
     * @property {Object[]} attack.vulns Associative array (by plugin name/attack name) of vulnerabilities found. Filled in by vulnerability checking plugins 
     */

    /** 
     * Performs a network attack and emits the attack-response event with response. 
     * Vulnerability chekcing plugins should check and update {@link attack.vulns}
     * @param {attack} attack The attack to perform
     */
    async performNetworkAttack(attack) {
        // see if this plugin is allowed to send attack
        if (!this.allowedToAttack(attack)) {
            return false
        }

        // Should we keep response even when vulnerablity is not found? this is to keep tabs on the revalidation
        let revalidationPluginName = _.get(attack, 'originalRequest.revalidationInfo.attack.name')
        let keepResponseForRevalidationVerification = (
            this.getName() == revalidationPluginName &&
            attack.attackArea == _.get(attack, 'originalRequest.revalidationInfo.attack.area') &&
            attack.param == _.get(attack, 'originalRequest.revalidationInfo.attack.param')
        )

        if (keepResponseForRevalidationVerification) {
            // go through all the revalidation info to see if we have any (previously) successful attck
            // with that combination of vector, encoding, attack type
            keepResponseForRevalidationVerification = false
            let successfulAttacks = _.get(attack, 'originalRequest.revalidationInfo.successfulAttacks', {})
            for (let attackKey of Object.keys(successfulAttacks)) {
                let attackInfo = successfulAttacks[attackKey]
                if (attackInfo &&
                    attack.vector == attackInfo.vector &&
                    attack.encoding == attackInfo.encoding &&
                    attack.attackType == attackInfo.type) {
                    keepResponseForRevalidationVerification = true
                    break
                }
            }
        }

        // tracking the request counter for every scan
        let scanSpecificPluginStore = this.getPluginScopedStore(attack, 'this-scan')
        let attackCount = scanSpecificPluginStore.currentAttackCount

        let scanlogId = attack.httpRequest.scanlog_id;

        if (attackCount == undefined) {
            attackCount = 0;
        }
        attackCount = attackCount + 1;

        attack.attackCount = attackCount;
        _.set(scanSpecificPluginStore, `['${scanlogId}'].scanlogIdKeys`, attackCount)

        // add the scan ID to the request
        attack.httpRequest.scanlog_id = attack.originalRequest.scanlog_id
        attack.httpRequest.scanId = attack.originalRequest.scanId
        attack.pluginName = this.getName()

        //To do onbefore attack
        let onBeforeAttack = _.get(attack.originalRequest, "httpRequest.annotations.haikuEvents.onBeforeAttack");

        if(onBeforeAttack) {
            try {
                onBeforeAttack(attack.originalRequest.httpRequest, this.getHaikuScanScriptHelper(attack.originalRequest.scanId));
            }
            catch(err) {
                logger.log('error', `performNetworkAttack, onBeforeAttack ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
            }
        }

        //Update script variables
        let scriptVariables = _.get(this.networkScanner.getScanInfo(attack.originalRequest.scanId), "annotatedRequests.scriptVariables");

        if(scriptVariables) {
            HaikuUtils.updateScriptVariables(attack, scriptVariables);
            this.networkScanner.annotateRequest(attack.originalRequest);
        }

        // set up allowed content-type handler if one not provided by plugin AND plugin allows 
        // custom content-types in config.
        if (!attack.httpRequest.allowContentType && this.getMetadata(attack).allowedContentTypes) {
            attack.httpRequest.allowContentType = this.getAllowContentTypeCheck(this.getMetadata(attack).allowedContentTypes)
        }

        // Add anything we need to for every request eg. specific headers.
        HaikuUtils.addAdditionalPerRequestHeaders(attack.httpRequest.headers, _.get(this.getConfig(attack), 'ScannerSettings.addToRequest'))
        
        try {
            if (this.networkScanner.runningScans[attack.httpRequest.scanId]) {

                this.networkScanner.runningScans[attack.httpRequest.scanId].attackRequestId = this.networkScanner.runningScans[attack.httpRequest.scanId].attackRequestId + 1;
                attack.httpRequest.attackRequestId = this.networkScanner.runningScans[attack.httpRequest.scanId].attackRequestId;
            }
        } catch (error) {
            logger.log('error', `Unable to generate attackRequestId: ${error.toString()}`, HaikuUtils.getMetadataForLog(attack))
        }

        let reqPromise = this.networkScanner.getHttpRequestMonster().queueRequest(attack.httpRequest)
        if (!reqPromise) {
            return false
        }

        // sent a request, update stats
        try {
            // HACK HACK HACK - should do via events
            if (this.networkScanner.runningScans[attack.httpRequest.scanId]) {
                let stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.byCrawlerReq[attack.httpRequest.haikuKey]
                if (stats) {
                    stats.numHttpRequests = stats.numHttpRequests + 1
                    stats.firstHttpReqSent = stats.firstHttpReqSent || new Date()
                    stats.lastHttpReqSent = new Date()
                }

                stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.byPlugin
                if (stats[this.getName()]) {
                    stats[this.getName()] = stats[this.getName()] + 1
                } else {
                    stats[this.getName()] = 1
                }

                stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.byAttackArea
                let attackArea = attack.attackArea || 'other'
                if (stats[attackArea]) {
                    stats[attackArea] = stats[attackArea] + 1
                } else {
                    stats[attackArea] = 1
                }
                // HACK HACK HACK - should do via events
            } else {
                debug(`scanid = ${attack.httpRequest.scanId} no longer exists`)
            }
        } catch (err) {
            debug(`tell Karthik something went wrong in updating stats ${err.toString()}`)
        }

        attack.result = await reqPromise
        this.updateStats(attack);
        let resp = attack.result.resp
        if (resp.err) {
            logger.log('error', `request failed: ${resp.err}`, HaikuUtils.getMetadataForLog(attack))
        }
        logger.log('info', `response status -- code: ${resp && resp.fullResponse ? resp.fullResponse.statusCode : '*error*'}`, HaikuUtils.getMetadataForLog(attack))

        try {
            if (this.networkScanner.runningScans[attack.httpRequest.scanId]) {
                let scanId = attack.httpRequest.scanId;
                let scanData = this.networkScanner.runningScans[scanId];
                let replayScanInfo = this.networkScanner.getConfig(attack.httpRequest.scanId).ScannerSettings.replayScanInfo;
    
                if(replayScanInfo && replayScanInfo.isReplayScan && scanData.storeAttackInfoInReplayScan) {
                    let attackRequestId = attack.httpRequest.attackRequestId;
    
                    if(attackRequestId) {
                        scanData.replayAttackInfo = scanData.replayAttackInfo || {};
                        scanData.replayAttackInfo[`${scanId}_${attackRequestId}`] = {
                            httpRequest: {
                                method: attack.httpRequest.method,
                                uri: attack.httpRequest.uri,
                                headers: attack.httpRequest.headers,
                                haikuKey: attack.httpRequest.haikuKey,
                                attackArea: attack.attackArea,
                                pluginName: attack.pluginName,
                                param: attack.param,
                                paramVal: attack.paramVal,
                                vector: attack.vector,
                                body: attack.httpRequest.body,
                                encoding: attack.encoding
                            },
                            responseCode: `${resp && resp.fullResponse ? resp.fullResponse.statusCode : '*error*'}`
                        }
                    }
                }
            }   
        } catch (error) {
            logger.log('error', `Unable to update attack info in replayScanInfo: ${error.toString()}`, HaikuUtils.getMetadataForLog(attack))
        }

        // add some metadata from response
        attack.hostname = _.get(attack, 'result.req.parsedURL.hostname')
        attack.href = _.get(attack, 'result.req.parsedURL.href')
        attack.result.err = resp.err
        attack.result.isDisallowedContentType = resp.isDisallowedContentType
        attack.result.vulns = {}

        // below try catch is primarily to ensure that both the response-processing events are always called
        // so that things stay in synch.
        this.networkScanner.emit('response-processing-started', attack.httpRequest.scanId, attack)
        try {
            if (resp.isDisallowedContentType) {
                // skip disallowed content-types
                let fullResponse = _.get(attack, 'result.resp.fullResponse')
                logger.log('info', `Request ${attack.href} : not allowed content-type ${fullResponse.headers ? fullResponse.headers['content-type'] : 'unknown'}`, HaikuUtils.getMetadataForLog(attack))
            } else {
                // add accessor for cheerio (lazy parsing) only when someone needs it
                Object.defineProperty(attack.result.resp.httpResponse, 'cheerio', {
                    get: function () {
                        delete this.cheerio;
                        return this.cheerio = cheerio.load(this.body, {
                            xmlMode: true,
                            decodeEntities: false
                        })
                    },
                    configurable: true
                })

                // send results so they can be checked against vulnerability pattern
                // everything like skipping errors, invalid content-type etc. is handled in the
                // onAttackResponse handler
                attack.processResponsePromises = []
                this.networkScanner.emit('attack-response', attack)
                let reqId = _.get(attack, 'originalRequest.requestId')
                if (attack.processResponsePromises.length) {
                    let start = process.hrtime()
                    // wait for all promises to be resolved.
                    try {
                        let ret = await HaikuUtils.timedPromise(
                            Promise.all(attack.processResponsePromises),
                            this.getConfig(attack).DefaultPluginSettings.maxTimeToProcessAttackResponse
                        );
                    } catch (err) {
                        logger.log('error', `performNetworkAttack, awaiting processResponsePromises using Promise.all() ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
                    }

                    let end = process.hrtime(start)
                    let timeTakenMillis = (end[0] * 1000) + (end[1] / 1e6)
                    if (this.networkScanner.runningScans[attack.httpRequest.scanId]) {
                        let stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.pluginTimings
                        let name = 'viewstateOrXss'
                        if (stats[name]) {
                            stats[name] = stats[name] + timeTakenMillis
                        } else {
                            stats[name] = timeTakenMillis
                        }

                        if (stats[attack.pluginName]) {
                            stats[attack.pluginName] = stats[attack.pluginName] + timeTakenMillis
                        } else {
                            stats[attack.pluginName] = timeTakenMillis
                        }
                    }
                }

                // get rid of cheerio to save memory. It hangs out often in the original request since that stays till
                // all atacks on a crawler request are done
                Object.defineProperty(attack.result.resp.httpResponse, 'cheerio', {
                    get: function () {
                        return '<deleted after response processing ... cannot use >'
                    },
                    configurable: true
                })
            }

            if (Object.keys(attack.result.vulns).length || keepResponseForRevalidationVerification || attack.uploadAttackToS3) {
                // create the vulnerability found object with all the info that could be needed
                let fullResponse = _.get(attack, 'result.resp.fullResponse')

                let vuln = {
                    // common info
                    scanId: attack.originalRequest.scanId,
                    scanlogId: attack.originalRequest.scanlog_id,

                    // attack (request)
                    attack: {
                        attackCount: attack.attackCount,
                        method: attack.httpRequest.method,
                        name: attack.pluginName,
                        hostname: attack.hostname,
                        originalRequest: attack.originalRequest,
                        href: attack.href,
                        area: attack.attackArea,
                        type: attack.attackType,
                        param: attack.param,
                        vector: attack.vector,
                        encoding: attack.encoding,
                        headers: fullResponse ? fullResponse.req.getHeaders() : null, //attack.result.resp.fullResponse.req._header,
                        body: attack.httpRequest.body,
                        httpRequest: attack.httpRequest,
                        autoPOC: attack.autoPOC,
                        uuid: attack.uuid,
                        uploadAttackToS3: attack.uploadAttackToS3,
                        productionReady: attack.productionReady
                    },
                    // response
                    response: {
                        headers: fullResponse ? fullResponse.headers : null,
                        statusCode: fullResponse ? fullResponse.statusCode : null,
                        statusMessage: fullResponse ? fullResponse.statusMessage : null,
                        httpVersion: fullResponse ? fullResponse.httpVersion : null,
                        body: _.get(attack, 'result.resp.body')
                    },
                    // vulnerabilities
                    vulns: attack.result.vulns
                }
                vuln.attack.httpResponse = vuln.response

                if(attack.uploadAttackToS3) {
                    // write body to persistent storage and put link as body
                    // Save to S3 before converting attack response into storage
                    await this.saveAttackInfoToS3(vuln.attack);
                }

                if(Object.keys(attack.result.vulns).length || keepResponseForRevalidationVerification) {
                    let respType = 'attack'
                    let storageKey = await this.networkScanner.storeResponseBody(vuln.scanId, respType, vuln.attack.httpResponse.body)
                    if (storageKey) {
                        vuln.attack.httpResponse.body = storageKey
                    }

                    if (Object.keys(attack.result.vulns).length) {
                        this.networkScanner.emit('vulnerability-found', vuln)
                    } else if (keepResponseForRevalidationVerification) {
                        this.networkScanner.emit('revalidation-no-vulnerability-found', vuln)
                    }
                }                
            }
        } catch (err) {
            logger.log('error', `processing attack response failed: ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
        }
        this.networkScanner.emit('response-processing-completed', attack.httpRequest.scanId, attack)

        return true
    }

    updateStats(attack) {
        try {
            let stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.byCrawlerReq[attack.httpRequest.haikuKey];
            stats.totalRoundTripTimeSec = stats.totalRoundTripTimeSec || 0;
            stats.avgRoundTripTimeSec = stats.avgRoundTripTimeSec || 0;

            if(attack.result.req.roundTripTimeSec) {
                stats.totalRoundTripTimeSec = stats.totalRoundTripTimeSec + attack.result.req.roundTripTimeSec;
                stats.avgRoundTripTimeSec = parseFloat((stats.totalRoundTripTimeSec / stats.numHttpRequests).toFixed(2));
            }
        } catch (error) {
            
        }

        try {
            let stats = this.networkScanner.runningScans[attack.httpRequest.scanId].stats.byCrawlerReq[attack.httpRequest.haikuKey];
            stats.customerTotalRoundTripTimeSec = stats.customerTotalRoundTripTimeSec || 0;
            stats.customerAvgRoundTripTimeSec = stats.customerAvgRoundTripTimeSec || 0;

            if(attack.result.req.customerRoundTripTimeSec) {
                stats.customerTotalRoundTripTimeSec = stats.customerTotalRoundTripTimeSec + attack.result.req.customerRoundTripTimeSec;
                stats.customerAvgRoundTripTimeSec = parseFloat((stats.customerTotalRoundTripTimeSec / stats.numHttpRequests).toFixed(2));
            }
        } catch (error) {
            
        }
    }

    /**
     * Is this plugin allowed to perform the attack - may be disallowed due to config/revalidation.
     * @param {attack} attack The attack that's about to be performed
     */
    allowedToAttack(attack) {
        // see if this plugin is prohibited from sending attacks by config
        if (!this.canAttack(attack)) {
            logger.log('info', `Not sending attack for plugin '${this.getName()}' -> disallowed by config`, HaikuUtils.getMetadataForLog(attack))
            return false
        }

        // see if we are revalidating and we need to skip this attack
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        let revalidationPluginName = _.get(attack, 'originalRequest.revalidationInfo.attack.name')
        if (!this.getMetadata(attack).bypassRevalidationCheck) {
            if (revalidationPluginName && this.getName() != revalidationPluginName) {
                logger.log('info', `Not sending attack for plugin '${this.getName()}' since it is not the same as the attack being revalidated '${revalidationPluginName}'`, HaikuUtils.getMetadataForLog(attack))
                return false
            }
        }

        // See if we can attack this type of crawler request
        if (!this.canAttackResource(attack)) {
            logger.log('info', `Not sending attack for plugin '${this.getName()}' since it cannot attack this resource type '${attack.originalRequest.httpRequest.haikuResourceType}' (${attack.originalRequest.httpRequest.uri})`, HaikuUtils.getMetadataForLog(attack))
            return false
        }

        // all good.
        return true
    }

    /**
     * get a function that will check response content-type as reuiqred by http request monster. Using this
     * instead of annonymous function directly at performNetworkAttack() to avoid a closure with large data
     * @param {RegExp} pluginAllowedContentTypes Allowed content types as per plugin config.
     */
    getAllowContentTypeCheck(pluginAllowedContentTypes) {
        return (res) => {
            return HTTPRequestMonster.contentTypeMatchesPattern(res, pluginAllowedContentTypes);
        };
    }

    /**
     * 
     * @param {attack} attack the attack just performed
     * @param {RegExp} regexp Regexp to use to check
     * @param {string} vulnerabilityID The Haiku vulnerabilityID being checked for 
     */
    checkBodyForVuln(attack, regexp, vulnerabilityID, options) {
        let body = _.get(attack, 'result.resp.body')
        if (!body) {
            return false
        }
        regexp.lastIndex = 0 // reset the last index.

        // set up options
        let opts = _.cloneDeep(defaultOptionsForCheck)
        Object.assign(opts, options)
        opts.getMultipleMatches = regexp.flags.includes('g')

        let vuln = {
            contextBytes: this.getConfig(attack).DefaultPluginSettings.contextBytes
        }

        let details = []
        let res = regexp.exec(body)
        while (res) {
            if (res[0].length > opts.minMatchLength) {
                // for first match, get matched string with bytes of context before and after it
                if (0 == details.length) {
                    let start = res.index - vuln.contextBytes
                    start = start < 0 ? 0 : start
                    let contextLength = res[0].length + 2 * vuln.contextBytes

                    // defensive check so we dont get very large context
                    if (contextLength > this.getConfig(attack).DefaultPluginSettings.maxContextChars) {
                        logger.log('info', `Trimming large context string. Match length: ${res[0].length}`, HaikuUtils.getMetadataForLog(attack))

                        contextLength = this.getConfig(attack).DefaultPluginSettings.maxContextChars
                    }
                    vuln.context = HaikuUtils.getSubString(body, start, contextLength)
                }
                details.push(res[0])
            }

            // see if we have exceeded the # of matches.
            if (details.length >= opts.maxMatchesToReturn) {
                break
            }

            // if global flag is set, get all vulns, otherwise we are done

            if (opts.getMultipleMatches) {
                res = regexp.exec(body)
            } else {
                break
            }
        }

        if (details.length) { // foud anything?
            details = _.uniq(details)
            vuln.details = opts.getMultipleMatches ? details : details[0]
            if (opts.addVulnerabilitytoResult) {
                vuln.foundVulnInResponseBody = true;
                this.addVulnerabilitytoResult(attack, vulnerabilityID, vuln)
            }
        }
        return details.length ? vuln : false
    }
    /**
     * @param  {httpRequest} httpRequest the request to be analised
     */
    isAspDotNetRequest(httpRequest) {
        let reqURL = new URL(httpRequest.httpRequest.uri)
        return (reqURL.pathname.indexOf('.aspx') >= 0 ? true : reqURL.pathname.indexOf('.asmx') >= 0 ? true : reqURL.pathname.indexOf('.master') >= 0 ? true :
            reqURL.pathname.indexOf('.svc') >= 0 ? true : reqURL.pathname.indexOf('.net') >= 0 ? true : reqURL.pathname.indexOf('.asax') >= 0 ? true :
                reqURL.pathname.indexOf('.ascx') >= 0 ? true : reqURL.pathname.indexOf('.ashx') >= 0 ? true : reqURL.pathname.indexOf('.axd') >= 0 ? true :
                    reqURL.pathname.indexOf('.browser') >= 0 ? true : reqURL.pathname.indexOf('.msgx') >= 0 ? true : false)
    }

    /**
     * Add default autoPOC details to attack object by framework.  
     * @param {Object} attack attack object for which autoPOC details need to added.  
     * @param {*} vulnID vulnerability id on which autoPOC details required
     */
    onAutoPOC(attack, vulnID) {
        switch (attack.attackArea) {
            case 'JSONPost':
            case 'FormEncodedPost':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal], `orignal request body ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, encodeURIComponent(attack.vector)], `attack request body ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);
                }
                break;
            case 'PostBody':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, [attack.param], `orignal request body ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param], `attack request body ${attack.param} param value tampered`);
                }
                break;
            case 'XMLPost':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, [attack.param], `orignal request ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param], `attack request body ${attack.param} param value tampered`);
                }
                break;
            case 'HTTPHeaders':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.headers', `param`, [attack.param], `orignal request header ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, [attack.param], `attack request header ${attack.param} param value tampered`);
                }
                break;
            case 'HTTPMethods':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, [attack.param], `orignal request method ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param], `attack request method ${attack.param} param value tampered`);
                }
                break;
            case 'BaseURI':
                {
                    let emptyUriPath = attack.param.length < 2 ? true : false;
                    let attackText = emptyUriPath ? `${attack.param}${attack.vector}` :  `${attack.param}/${attack.vector}`;
                    attackText = attack.encoding == 'uri' ? encodeURI(attackText) : attackText;
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `text`, [attack.param], `orignal request uri ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `text`, [attackText], `attack request uri ${attack.param} param value tampered`);
                }
                break;
            case 'UriQueryParameters':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `text`, [`${attack.param}=${attack.paramVal}`], `orignal request query ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `text`, [`${attack.param}=${encodeURIComponent(attack.vector)}`], `attack request query ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);
                }
                break;
            case 'Cookies':
                {
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.headers.cookie', `text`, [`${attack.param}=${attack.paramVal}`], `orignal request cookie ${attack.param} param value will be attack`);
                    HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers.cookie', `text`, [`${attack.param}=${attack.vector}`], `attack request cookie ${attack.param} param value tampered with vector ${attack.vector}` );
                }
                break;
            default:
                break;
        }
    }

    /**
     * Save attack information to s3  
     */
     async saveAttackInfoToS3(attack) {
        if (attack.uploadAttackToS3 && this.vulnerabilityID) { 
            if(HaikuUtils.isNullOrUndefined(attack)) {
                logger.log('info', `Can't save attack info to s3 for plugin '${this.getName()}' & vuln '${this.vulnerabilityID}', Reason: attack value is not provided.`);
                return;
            }
    
            if(HaikuUtils.isNullOrUndefined(this.vulnerabilityID)) {
                logger.log('info', `Can't save attack info to s3 for plugin '${this.getName()}' & vuln '${this.vulnerabilityID}', Reason: vulnerabilityID is not provided.`);
                return;
            }
    
            if(HaikuUtils.isNullOrUndefined(attack.uuid)) {
                logger.log('info', `Can't save attack info to s3 for plugin '${this.getName()}' & vuln '${this.vulnerabilityID}', Reason: uuid is not provided.`);
                return;
            }
    
            try {
                let vuln = this.getVulnObject(attack.httpRequest.scanId, this.vulnerabilityID);
    
                if(vuln) {
                    attack.vulnId = vuln.igwId;
                    attack.productionReady = vuln.productionReady;
                    let formattedAttack = HaikuUtils.getStandardizedAttackObject(attack);
                    await s3Utils.upload(this.oobAttackPathPrefix + attack.httpRequest.scanlog_id, `${attack.uuid}_${attack.httpRequest.scanlog_id}.json`, JSON.stringify(formattedAttack));
                }
            } catch (err) {
                logger.log('error', `Unable to save attack info to s3 for plugin '${this.getName()}', Reason: ${err.toString()}`)
            }
        }
    }

    /**
     * Get whether attack being processed is revalidation attack or not
     * @param {Object} attack attack object
     * @returns {boolean} true if revalidation attack, false otherwise
     */
    isRevalidationAttack(attack) {
        if(attack && attack.originalRequest && attack.originalRequest.revalidationInfo) {
            return true;
        }

        return false;
    }

    isStatusCodeDisabled(statusCode, statusCodes) {
        try {
            for (let code of statusCodes) {
                if (typeof code === 'number' && code === statusCode) {
                    return true;
                } else if (typeof code === 'string') {
                    let [start, end] = code.split('-').map(Number);
                    if (statusCode >= start && statusCode <= end) {
                        return true;
                    }
                }
            }    
        } catch (error) {
            logger.log('error', `isStatusCodeDisabled: ${error.toString()}`);
        }
        
        return false;
    }
}

module.exports = NetworkAttack