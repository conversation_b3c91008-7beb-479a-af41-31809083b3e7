//DOM Location manipulation
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require('cheerio')

const excludePaths = /\/blog\/|\/doc\/|\/docs\/|\/documentation\/|\/help\/|\/support\/|\/faq\/|\/about\/|\/contact\/|\/privacy\/|\/terms\/|\/legal\/|\/sitemap\/|\/robots\.txt|\/humans\.txt|\/security\.txt/i
class DOMLocationManipulation extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-dom-location-manipulation'
    }

    getAttackVectors() {
        return locationVectors
    }

    /**
     * Initialize the parameterized delegate
     * @param {parameterizedDelegate} parameterizedDelegate The parameterized delegate to initialize
     */
    initParameterizedDelegate(parameterizedDelegate) {
        parameterizedDelegate.setOptions({
            headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'X-Forwarded-Host', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded', 'Cookie', 'Accept-Language', 'X-Forwarded-For', 'Location']
        })
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers']
    }

    async performNetworkAttack(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.DOMLocationVuln) return false

        const url = _.get(attack, "httpRequest.uri")
        if (excludePaths.test(url)) return false

        return await super.performNetworkAttack(attack)
    }

    /**
     * Check if the response contains a DOM location manipulation vulnerability
     * @param {string} responseBody The response body to check
     * @returns {boolean} True if a vulnerability is found, false otherwise
     */

    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.DOMLocationVuln) {
            return
        }

        let responseBody = _.get(attack, "result.resp.body")
        if (!responseBody || typeof responseBody !== 'string') {
            return
        }

        // Quick pre-check for any location-related code
        const locationPattern = /(?:window|document|location|self|top|parent)\.(?:href|host|hostname|search|pathname|origin|assign|replace|reload|pathname|protocol|port|hash)\s*=\s*['"]([^'"]*)['"]/i;
        if (!locationPattern.test(responseBody)) {
            return;
        }

        // Find all possible locations where DOM manipulation can occur
        let tagsDts = responseBody.match(/< ?script>[\w\W]+?<\/script>|< ?(?:a|form|input|base|button|div|span|p|iframe|embed|object) .+?>|< ?(?:onclick|onload|onerror|onmouseover|onmouseout|onfocus|onblur|onchange|onsubmit|onreset|onselect|onkeydown|onkeypress|onkeyup|ondblclick|onmousedown|onmouseup|onmousemove|onmouseenter|onmouseleave|onwheel|onscroll|onresize|onabort|oncanplay|oncanplaythrough|oncuechange|ondurationchange|onemptied|onended|onerror|onloadeddata|onloadedmetadata|onloadstart|onpause|onplay|onplaying|onprogress|onratechange|onseeked|onseeking|onstalled|onsuspend|ontimeupdate|onvolumechange|onwaiting)="[^"]*"/ig)
        if (tagsDts && tagsDts.length > 0) {
            let page = cheerio.load(responseBody, {
                baseURI: attack.href
            });

            const processLocationMatches = (content, element) => {
                const locationMatches = content.match(locationPattern);
                if (locationMatches) {
                    const value = locationMatches[1];
                    // Only check if the value contains any of our attack vectors
                    const matchedVector = locationVectors.find(vector => value.includes(vector));
                    if (matchedVector) {
                        this.reportDOMLocationVuln(attack, [{
                            dom_element: element,
                            values: value
                        }]);
                        pluginDataForRequest.DOMLocationVuln = true;
                        return true;
                    }
                }
                return false;
            };

            for (const ele of tagsDts) {
                try {
                    // Quick pre-check for location-related code in the element
                    if (!locationPattern.test(ele)) {
                        continue;
                    }

                    const element = page(ele);

                    // Check for location manipulation in script tags
                    if (/< ?script/i.test(ele)) {
                        const scriptContent = element.prop('innerHTML');
                        if (scriptContent && processLocationMatches(scriptContent, ele)) {
                            return;
                        }
                    }
                    // Check for location manipulation in event handlers
                    else if (/on\w+=/i.test(ele)) {
                        const eventHandlerName = /on\w+/i.exec(ele)[0];
                        const eventHandler = element.prop(eventHandlerName);
                        if (eventHandler && processLocationMatches(eventHandler, `${ele}, event handler: ${eventHandler}`)) {
                            return;
                        }
                    }
                    // Check for location manipulation in other elements
                    else {
                        // Check all relevant attributes
                        for (const attr of ['href', 'src', 'action']) {
                            if (attr === 'action' && !element.is('form')) continue;
                            const value = element.prop(attr);
                            if (value) {
                                const matchedVector = locationVectors.find(vector => value.includes(vector));
                                if (matchedVector) {
                                    this.reportDOMLocationVuln(attack, [{
                                        dom_element: ele,
                                        values: value
                                    }]);
                                    pluginDataForRequest.DOMLocationVuln = true;
                                    return;
                                }
                            }
                        }
                    }
                } catch (e) {
                    continue;
                }
            }
        }
    }

    reportDOMLocationVuln(attack, domloc_dts) {
        const detailsString = domloc_dts.map(dt =>
            `DOM Element: ${dt.dom_element}, Value: ${dt.values}`
        ).join('; ');

        const vulnDetails = `DOM Location Manipulation Vulnerability detected: ${detailsString}`
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnDetails)
    }
}

// Optimized DOM Location manipulation vectors
const locationVectors = [
    // Core JavaScript protocol vectors - one from each category is sufficient
    'javascript:alert(1)',  // Basic alert
    'javascript:void(0)',   // Basic void
    'javascript:eval(1)',   // Basic eval

    // Core Data protocol vectors - one from each category
    'data:text/html,<script>alert(1)</script>',  // Basic data URL
    'data:application/x-javascript,alert(1)',    // Alternative data URL

    // Critical About protocol vectors - only the most relevant ones
    'about:blank',
    'about:config',
    'about:plugins',
    'about:debugging',

    // Critical File protocol vectors - only the most sensitive paths
    'file:///etc/passwd',
    'file:///c:/windows/win.ini',

    // Critical VBScript vector - one is sufficient
    'vbscript:alert(1)',

    // Critical combined vectors - one from each category
    'about:blank#javascript:alert(1)',
    'about:blank#data:text/html,<script>alert(1)</script>'
]

module.exports = DOMLocationManipulation