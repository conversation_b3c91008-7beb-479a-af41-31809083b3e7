const debug = require('debug')('MixedContent')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

class MixedContent extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-mixed-content-vulnerability'
    }

    /**
     * forms attack request and performs network attack
     * @param {request} request new request received from crawler
     * @override
     */
    processNewRequest(request) {
        let attack = {
            httpRequest: this.getHttpRequest(request),
            originalRequest: request,
        }
        this.performNetworkAttack(attack)
    }

    // get the modified request
    getHttpRequest(request) {
        let req = _.cloneDeep(request.httpRequest)
        if (req.uri.startsWith("https")) {
            req.uri = req.uri.replace("https", "http")
        }
        else {
            req.uri = req.uri.replace("http", "https")
        }
        return req
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        //here result for vulnerability in each case
        if (attack.pluginName != this.getName()) {
            return
        }
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
        let check = attack.result.resp.httpResponse.statusCode
        if (check == 200 && redirect == 0) {
            let details = {
                URI: attack.result.req.options.uri
            }

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

module.exports = MixedContent