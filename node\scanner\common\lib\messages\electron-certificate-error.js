const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:ElectronCertificateError')


/** 
 * Message from crawler -> scanner specifying a new netwok request to attack.
 * @extends QueueMessage
 */
class ElectronCertificateError extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }

    /**
     * @typedef {Object} ElectronCertificateErrorMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request (should be haiku)
     * @property {string} chromiumNetErr error from: {@link https://cs.chromium.org/chromium/src/net/base/net_error_list.h}, eg. net::ERR_CERT_COMMON_NAME_INVALID
     * @property {Certificate} partialCert server certificate as per {@link https://electronjs.org/docs/api/structures/certificate} except that
     * we dont pass certtificate data so 'data' and 'issuerCert' are removed.
     */
    /**
     * @param {ElectronCertificateErrorMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner'
        this.routingKey = 'request.electron-cert-error'
        this.msgType = ElectronCertificateError.msgType
    }
}

module.exports = ElectronCertificateError