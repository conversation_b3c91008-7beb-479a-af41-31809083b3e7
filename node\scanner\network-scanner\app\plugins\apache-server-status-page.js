const debug = require('debug')('ApacheServerStatusPage')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class ApacheServerStatusPage extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-server-status-page'
    }
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            clearQueryParams: true,
        });
    }
    getAttackVectors() {
        return ASSPVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /* async performNetworkAttack(attack) {
        // always perform the initial attack
        let currserver = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (/Apache/i.test(currserver)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    } */

    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        // check for content-type for mitigating FPs and fix '/' thing as well.
        let stacode = attack.result.resp.httpResponse.statusCode
        if (stacode == 200 && attack.result.resp.body.length >= 10) {
            let mybody = attack.result.resp.body
            // creating array as res and storing all instance of match. Checked with multiple IPs.
            if (/<title>apache\sstatus<\/title>/i.test(mybody) && /apache\sserver\sstatus\sfor/i.test(mybody) && /(pid|cpu|request)/i.test(mybody) && /(server\suptime:|cpu\susage:)/i.test(mybody))
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.httpRequest.uri)
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['apache', 'status', 'cpu', 'server']);
    }
}

// vectors & matches ...
const ASSPVectors = [
    `server-status`,
    `status`,

]

module.exports = ApacheServerStatusPage