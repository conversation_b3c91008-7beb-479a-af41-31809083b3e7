[{"scanId": "324", "scanlog_id": 324, "scanner": "haiku", "serviceId": 3241, "mainUrl": "https://demotest.testapptrana.net/", "httpRequest": {"url": "https://demotest.testapptrana.net/xvwa/vulnerabilities/sqli_blind/", "method": "POST", "headers": {"Connection": "close", "Content-Length": "37", "Cache-Control": "max-age=0", "Origin": "https://demotest.testapptrana.net/", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Content-Type": "application/x-www-form-urlencoded", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "https://demotest.testapptrana.net/", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6"}, "body": "search=1&item=", "key": "kk", "resourceType": "mainFrame"}}, {"scanId": "325", "scanlog_id": 324, "scanner": "haiku", "serviceId": 3242, "mainUrl": "https://demo.testfire.net/", "httpRequest": {"url": "https://demo.testfire.net/doLogin", "method": "POST", "headers": {"Connection": "close", "Content-Length": "37", "Cache-Control": "max-age=0", "Origin": "https://demo.testfire.net/", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Content-Type": "application/x-www-form-urlencoded", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "https://demo.testfire.net/", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6"}, "body": "uid=admin&passw=admin&btnSubmit=Login", "key": "kk", "resourceType": "mainFrame"}}, {"scanId": "326", "scanlog_id": 324, "scanner": "haiku", "serviceId": 3243, "mainUrl": "https://demotest.testapptrana.net/", "httpRequest": {"url": "https://demotest.testapptrana.net/dvwa/dvwa/vulnerabilities/sqli_blind/?id=1&Submit=Submit", "method": "GET", "headers": {"Connection": "close", "Cache-Control": "max-age=0", "Origin": "https://demotest.testapptrana.net/", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "https://demotest.testapptrana.net/", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6", "Cookie": "security=low; sess_map=aeqrfsvebyzbabazcccbucvqdafszwequqeafyczffvwyacwtcsbxquuezyffazfsxuadvcdfvceuwzuaeurvaxzeazzryuazccwyxfbyyytrudeswyqcrdvvtadquqabvvaycuetqexyarrvdyrxxqqfqwefwzdssyqdyaerfstayxq; PHPSESSID=lhdgoitackge9qqqgo78qc6v00"}, "resourceType": "mainFrame"}}, {"scanId": "1999", "scanlog_id": 999, "scanner": "haiku", "serviceId": 324, "mainUrl": "http://demo.testfire.net", "httpRequest": {"url": "http://demo.testfire.net/default.aspx?content=personal.htm", "method": "GET", "headers": {"Cache-Control": "max-age=0", "Origin": "http://demo.testfire.net", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "http://demo.testfire.net", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6", "Cookie": "ASP.NET_SessionId=1rioqif4jfzpgn2cedgss555; amSessionId=************"}, "resourceType": "mainFrame"}}, {"scanId": "2999", "scanlog_id": 999, "scanner": "haiku", "serviceId": 324, "mainUrl": "http://www.webscantest.com", "httpRequest": {"url": "http://www.webscantest.com/datastore/search_double_by_name.php", "method": "POST", "headers": {"Host": "www.webscantest.com", "Connection": "keep-alive", "Content-Length": "9", "Cache-Control": "max-age=0", "Origin": "http://www.webscantest.com", "Upgrade-Insecure-Requests": "1", "Content-Type": "application/x-www-form-urlencoded", "User-Agent": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "http://www.webscantest.com/datastore/search_double_by_name.php", "Accept-Encoding": "gzip, deflate", "Accept-Language": "en-GB,en-US;q=0.9,en;q=0.8", "Cookie": "TEST_SESSIONID=njepb3hocg7ki7ckk391pk8lr0; NB_SRVID=srv140717"}, "body": "name=<PERSON><PERSON>", "key": "kk", "resourceType": "mainFrame"}}, {"scanId": "1999", "scanlog_id": 999, "scanner": "haiku", "mainUrl": "http://demo.testfire.net", "httpRequest": {"url": "http://demo.testfire.net/bank/login.aspx", "method": "POST", "headers": {"Connection": "close", "Content-Length": "37", "Cache-Control": "max-age=0", "Origin": "http://demo.testfire.net", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Content-Type": "application/x-www-form-urlencoded", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "http://demo.testfire.net", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6"}, "body": "uid=admin&passw=admin&btnSubmit=Login", "key": "kk", "resourceType": "mainFrame"}}, {"scanId": "1999", "scanlog_id": 999, "scanner": "haiku", "mainUrl": "http://demo.testfire.net", "httpRequest": {"url": "http://demo.testfire.net/search.aspx?txtSearch=ss", "method": "GET", "headers": {"Cache-Control": "max-age=0", "Origin": "http://demo.testfire.net", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "http://demo.testfire.net", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6", "Cookie": "ASP.NET_SessionId=1rioqif4jfzpgn2cedgss555; amSessionId=************"}, "resourceType": "mainFrame"}}, {"scanId": "1999", "scanlog_id": 999, "scanner": "haiku", "mainUrl": "http://demo.testfire.net", "httpRequest": {"url": "http://demo.testfire.net/bank/account.aspx", "method": "POST", "headers": {"Cache-Control": "max-age=0", "Cookie": "ASP.NET_SessionId=1rioqif4jfzpgn2cedgss555; amSessionId=************; amUserInfo=UserName=anNtaXRo&Password=ZGVtbzEyMzQ=; amUserId=*********; amCreditOffer=CardType=Gold&Limit=10000&Interest=7.9", "Origin": "http://demo.testfire.net", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Content-Type": "application/x-www-form-urlencoded", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "http://demo.testfire.net", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6"}, "body": "listAccounts=*********0", "resourceType": "mainFrame"}}, {"scanId": "6999", "scanlog_id": 6999, "scanner": "haiku", "mainUrl": "http://testphp.vulnweb.com", "httpRequest": {"url": "http://testphp.vulnweb.com/AJAX/infotitle.php", "method": "POST", "headers": {"Cache-Control": "max-age=0", "Cookie": "ASP.NET_SessionId=1rioqif4jfzpgn2cedgss555; amSessionId=************; amUserInfo=UserName=anNtaXRo&Password=ZGVtbzEyMzQ=; amUserId=*********; amCreditOffer=CardType=Gold&Limit=10000&Interest=7.9", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36", "Content-Type": "application/x-www-form-urlencoded", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8", "Referer": "http://testphp.vulnweb.com/AJAX/index.php", "Accept-Language": "en-US,en;q=0.8,zu;q=0.6"}, "body": "id=1", "resourceType": "mainFrame"}}]