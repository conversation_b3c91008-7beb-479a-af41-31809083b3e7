const utils = require('../../ifc-utils.js')
const logger = require('../../../common/lib/haiku-logger')
const CreateAnimatedCrawlViz = require('../../../common/lib/messages/create-animated-crawl-viz')
const fs = require('fs')
const _ = require('lodash')
const path = require('path')

class Screenshot {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        // merge config
        this.mergeConfig()
        let pluginData = this.config.pluginData.screenshot
        this.takeScreenshots = pluginData.takeScreenshots
        this.createAnimatedViz = pluginData.createAnimatedViz

        // event handlers
        this.screenshotID = 0 // keep track of this in 'got-interesting-items' so 'unique-state' handler can use it
        scanner.on('got-interesting-items', this.onGotInterestingItems.bind(this))
        scanner.on('unique-state', this.onUniqueState.bind(this))
        scanner.on('crawl-end-processing-done', this.onCrawlEndProcessingDone.bind(this))
    }

    // does not affect ret (1st param)
    onGotInterestingItems(_, interestingItems, crawlState) {
        // save screenshot ID  number in context - TODO: CHANGE TO CONTEXT ID
        if (this.takeScreenshots == 'all') {
            this.scanner.addPendingOperation(this._writeScreenshot(interestingItems, crawlState))
        }
    }

    onUniqueState(_, interestingItems, crawlState) {
        if (this.takeScreenshots == 'unique') {
            this.scanner.addPendingOperation(this._writeScreenshot(interestingItems, crawlState))
        }
    }


    /**
     * Called when crawl processing i.e. crawl loop & end of crawl processing like uploading files
     * is done. We will send a message to kick off screenshot -> animated GIF &  consolidating JSON
     * files worker
     */
    onCrawlEndProcessingDone() {
        if (!this.createAnimatedViz) {
            return
        }

        // send message to kick off worker
        let msgContent = {
            scanId: this.config.scanId,
            scanlogId: this.config.scanLogId
        }

        let msg = new CreateAnimatedCrawlViz(msgContent)
        msg.publish(this.config.msgQ)
    }

    /**
     * merge screenshot plugin's site specific config
     */
    mergeConfig() {
        let sitePluginData = _.get(this.config, 'siteConfig.pluginData.screenshot')
        if (!sitePluginData) {
            return
        }

        // pull in site specific config
        let pluginData = this.config.pluginData.screenshot
        if (!_.isUndefined(sitePluginData.takeScreenshots)) {
            pluginData.takeScreenshots = sitePluginData.takeScreenshots
        }
        if (!_.isUndefined(sitePluginData.createAnimatedViz)) {
            pluginData.createAnimatedViz = sitePluginData.createAnimatedViz
        }
    }

    // internal methods.
    async _writeScreenshot(interestingItems, crawlState) {
        return Promise.all(
            [
                this._writeScreenshotImage(interestingItems.serverData.screenshotID),
                this._writeScreenshotMetadata(interestingItems, crawlState)
            ])
    }

    async _writeScreenshotImage(screenshotID) {
        let filename = `${this.config.scrPath}/crawler-screenshot-image-${screenshotID}.png`
        let browser = this.scanner.browser
        try {
            let img = await browser.webContents.capturePage()
            // don't wait for the file write to finish, since we got the
            // browser snapshot, we can go ahead with crawling.
            if (!img.isEmpty()) {
                fs.writeFile(filename, img.toPNG(), (err) => {
                    if (err) {
                        logger.log('error', `could not write screenshot image: ${err.toString()}`)
                    }
                })
            }
        } catch (err) {
            logger.log('error', `could not write screenshot image: ${err.toString()}`)
        }
    }

    async _writeScreenshotMetadata(interestingItems, crawlState) {
        let metadata = {
            location: interestingItems.location,
            stateId: crawlState.currentContext.getId(),
            actionId: crawlState.currentContext.curActionId(),
            fingerprint: interestingItems.serverData.fingerprint,
            tokensForFingerprinting: interestingItems.serverData.tokensForFingerprinting,
            crawlerBookmark: crawlState.getCrawlerBookmark()
        }
        let filename = `${this.config.scrPath}/crawler-screenshot-metadata-${interestingItems.serverData.screenshotID}.json`
        // don't wait for the file write to finish
        fs.writeFile(filename, JSON.stringify(metadata), (err) => {
            if (err) {
                logger.log('error', `could not write screenshot metadata: ${err.toString()}`)
            }
        })
    }


}

module.exports = Screenshot