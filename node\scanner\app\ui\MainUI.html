<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Application Scanner</title>
    <script src="https://code.jquery.com/jquery-3.2.1.min.js">
    </script>
</head>

<body>
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/bulma/0.4.0/css/bulma.css" media="screen"
    />

    <!-- bulma stuff -->
    <script>
        console.log( 'ui viz is:', uiViz)
        $(function () {
            $('#start-crawl').click(function () {
                url = $('#crawl-url').val()
                maxDepth = $('#max-depth').val()
                maxActions = $('#max-actions').val()
                useFrozen = $('#use-frozen')[0].checked
                args = {
                    'url': url,
                    'maxDepth': maxDepth,
                    'maxActions': maxActions,
                    'useFrozen': useFrozen
                }
//                $('#main-form').fadeOut()
                console.log( 'ui viz is:', uiViz)
                $('#viz').slideDown(() => {
                    uiViz.sendToMain('start-crawl', args)
                })
            })
        })
    </script>

    <!-- form -->
    <article class="message">
        <div class="message-header">
            <p>Application scanner</p>
        </div>
    </article>
    <div class="columns">
        <div class="column is-12">
            <form id='main-form'>
                <div class="field is-horizontal is-narrow">
                    <div class="field-label is-normal">
                        <label class="label">Max Depth</label>
                    </div>
                    <div class="field">
                        <p class="control">
                            <input id="max-depth" class="input" type="number" placeholder="20" value="20">
                        </p>
                    </div>
                    <div class="field-label is-normal">
                        <label class="label">Max Actions</label>
                    </div>
                    <div class="field">
                        <p class="control">
                            <input id="max-actions" class="input" type="number" placeholder="50" value="50">
                        </p>
                    </div>
                </div>

                <div class="field is-horizontal">
                    <div class="field-label is-normal">
                        <label class="label">URL</label>
                    </div>
                    <div class="field-body">
                        <div class="field is-grouped">
                            <p class="control is-expanded">
                                <input id="crawl-url" class="input" type="url" placeholder="http://matsya.avataara.in" value="http://matsya.avataara.in">
                            </p>
                        </div>

                        <div class="field is-grouped">
                            <p class="control">
                                <label class="checkbox">
                                <input id="use-frozen" type="checkbox" name='use-frozen' checked="1"/>
                                Resume</label>
                            </p>
                        </div>

                        <div class="field is-grouped">
                            <p class="control">
                                <a id="start-crawl" class="button is-primary" value="start-crawl">Start Crawl</a>
                            </p>
                        </div>

                    </div>
                </div>

            </form>
        </div>
    </div>

    <!--UI-->
    <div id='viz' style="display:none">
        <p class="notification is-info">
            scan started....
        </p>
    </div>

    <!-- bulma stuff -->
</body>

</html>
