const BasePlugin = require('../base-plugin')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const LoginDelegate = require('../../../lib/login-delegate')

class LoginRequest extends BasePlugin {

    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }

    /**
     * New request received from crawler - Kick off paramter iteration if request is viable
     * @param {request} originalRequest untampered request
     * @override
     */
    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http headers
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area')
            if (originalRequest.revalidationInfo && LoginDelegate.ParameterType != attackArea) {
                logger.log('info', `Login Request - not an interesting request since request being revalidated is attacking '${attackArea}'`)
                return
            }
        }

        // only process login request
        let httpRequest = originalRequest.httpRequest
        if (originalRequest.httpResponse.err || !LoginDelegate.isLoginRequest(httpRequest)) {
            logger.log('info',`Login Request - not an interesting request ${originalRequest.httpRequest.method} ${originalRequest.httpRequest.uri}  err=${originalRequest.httpResponse.err}`)
            return
        }

        // Create object that can iterate and manipulate login params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan')
        let options = this.getMetadata(originalRequest).options;
        let createLoginDelegate = function () {
            return new LoginDelegate(originalRequest, scanStore,options)
        }
        this.networkScanner.emit('login-request', createLoginDelegate)
    }

} 

module.exports = LoginRequest