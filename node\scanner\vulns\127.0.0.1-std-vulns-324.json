{"scanId":324,"scanlogId":324,"founddate":"2025-06-11T10:56:18.866Z","key":"http://127.0.0.1:5000/test?input=||input","uriGroupingKey":"GET-127.0.0.1:5000/test?input={...}","original":{"scanner":"haiku","haikuResourceType":"core","haikuKey":"GET-127.0.0.1:5000/test?input","httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36","Host":"127.0.0.1:5000"},"uri":"http://127.0.0.1:5000/test?input=defaultText","requestLine":"GET /test?input=defaultText HTTP/1.1"},"httpResponse":{"statusCode":200,"statusMessage":"OK","headers":{"content-type":"text/html; charset=utf-8","content-length":"1016","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Wed, 11 Jun 2025 10:55:46 GMT"},"body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"original-2.body\"}","responseLine":"HTTP/1.1 200 OK"}},"attack":{"name":"CSS Injection","hostname":"127.0.0.1","href":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","area":"UriQueryParameters","type":"value","param":"input","vector":"<style>body { color: red; } /*haikutest*/</style>","encoding":"uri","httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36","Host":"127.0.0.1:5000"},"uri":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","requestLine":"GET /test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E HTTP/1.1"},"httpResponse":{"statusCode":200,"statusMessage":"OK","headers":{"content-type":"text/html; charset=utf-8","content-length":"1131","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Wed, 11 Jun 2025 10:55:46 GMT"},"body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"attack-3.body\"}","responseLine":"HTTP/1.1 200 OK"}},"vulns":{"ID-css-injection":{"foundBy":"CSS Injection","productionReady":false,"canAttackInReplayScan":true,"details":"Potential CSS Injection Detected. Payload: <style>body { color: red; } /*haikutest*/</style>","autoPOC":[{"type":"original","path":"httpRequest.uri","highlightType":"text","details":["input=defaultText"],"description":"orignal request query input param value will be attack"},{"type":"attack","path":"httpRequest.uri","highlightType":"text","details":["input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"],"description":"attack request query input param value tampered with vector %3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"}],"vulnerabilityId":"405111","result":"Potential CSS Injection Detected. Payload: <style>body { color: red; } /*haikutest*/</style>"}},"rootActionCount":2}{"scanId":324,"scanlogId":324,"founddate":"2025-06-12T13:01:22.940Z","key":"http://127.0.0.1:5000/test?input=||input","uriGroupingKey":"GET-127.0.0.1:5000/test?input={...}","original":{"scanner":"haiku","haikuResourceType":"core","haikuKey":"GET-127.0.0.1:5000/test?input","httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36","Host":"127.0.0.1:5000"},"uri":"http://127.0.0.1:5000/test?input=defaultText","requestLine":"GET /test?input=defaultText HTTP/1.1"},"httpResponse":{"statusCode":200,"statusMessage":"OK","headers":{"content-type":"text/html; charset=utf-8","content-length":"1016","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Thu, 12 Jun 2025 13:01:03 GMT"},"body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"original-2.body\"}","responseLine":"HTTP/1.1 200 OK"}},"attack":{"name":"CSS Injection","hostname":"127.0.0.1","href":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","area":"UriQueryParameters","type":"value","param":"input","vector":"<style>body { color: red; } /*haikutest*/</style>","encoding":"uri","httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36","Host":"127.0.0.1:5000"},"uri":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","requestLine":"GET /test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E HTTP/1.1"},"httpResponse":{"statusCode":200,"statusMessage":"OK","headers":{"content-type":"text/html; charset=utf-8","content-length":"1131","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Thu, 12 Jun 2025 13:01:03 GMT"},"body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"attack-3.body\"}","responseLine":"HTTP/1.1 200 OK"}},"vulns":{"ID-css-injection":{"foundBy":"CSS Injection","productionReady":false,"canAttackInReplayScan":true,"details":"Potential CSS Injection Detected.\nPayload: <style>body { color: red; } /*haikutest*/</style>\nDetection Method: Style Attribute","autoPOC":[{"type":"original","path":"httpRequest.uri","highlightType":"text","details":["input=defaultText"],"description":"orignal request query input param value will be attack"},{"type":"attack","path":"httpRequest.uri","highlightType":"text","details":["input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"],"description":"attack request query input param value tampered with vector %3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"}],"vulnerabilityId":"405111","result":"Potential CSS Injection Detected.\nPayload: <style>body { color: red; } /*haikutest*/</style>\nDetection Method: Style Attribute"}},"rootActionCount":2}