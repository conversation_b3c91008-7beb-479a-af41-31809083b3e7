const debug = require('debug')('VectorResponseAttack')
const NetworkAttack = require('./network-attack')
const fs = require('fs')
const _ = require('lodash')
const ParameterizedDelegate = require('../../lib/parameterized-delegate')
const logger = require('../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../common/lib/haiku-utils')
let uuidv4 = require('uuid/v4'); // random uuid

// simple 'send vector - check response' style of attack on parametreized requests.
class VectorResponseAttack extends NetworkAttack {
    /**
     * simple helper to make it easier for plugins that don't need to know about ParameterizedDelegate
     * 
     * defines the identity vector i.e. vector that denotes that the parameter should be set to what it was
     * originally before any tampering.
     */
    static get identityVector() {
        return ParameterizedDelegate.identityVector
    }

    constructor(networkScanner, config) {
        super(networkScanner, config)

        // event handlers added to by derived classes
        this.events = this.getAttackableEvents()
        for (let event of this.events) {
            networkScanner.on(event, this.attackParameterizedRequest.bind(this))
        }

        // never mess with some params as that will just end up causing request to fail.
        this.inviolateParamRegex = /__EVENTARGUMENT|__VIEWSTATE|__EVENTVALIDATION|__EVENTTARGET|phpsessid|user_token/i
    }

    /**
     * Base implementation iterates the encodings returned by parameterized Delegate
     * @param {baseAttack} baseAttack base attack - attack object without vector, encoding.
     * @param {parameter} parameter the parameter (name,value) being tampered. value is the untampered value that could 
     * be used to detect encoding.
     * @param {string} parameterizedDelegate the encoding being tried now
     */
    * getEncodingIterator(baseAttack, parameter, parameterizedDelegate) {
        let encodings = []
        try {
            encodings = parameterizedDelegate.getEncodings(parameter.name, parameter.value)
        } catch (err) {
            logger.log('error', `getting encodings - ${err}`, HaikuUtils.getMetadataForLog(baseAttack))
        }

        // iterate over all encodings
        for (let encoding of encodings) {
            try {
                yield encoding
            } catch (err) {
                logger.log('error', `iterating encoding - ${err}`, HaikuUtils.getMetadataForLog(baseAttack))
            }
        }
    }

    /**
     * Base implementation iterates the vectors returned by getAttackVectors(). 
     * Either this or getAttackVectors() has to be implemented by subclass
     * @param {baseAttack} baseAttack base attack - attack object without vector, encoding.
     * @param {string} encoding the encoding being tried now
     */
    * getVectorIterator(baseAttack, encoding) {
        let attackVectors = []
        try {
            attackVectors = this._getAttackVectors(baseAttack)
        } catch (err) {
            logger.log('error', `getting vectors - ${err}`, HaikuUtils.getMetadataForLog(baseAttack))
        }

        // iterate over attack vectors
        for (let vector of attackVectors) {
            try {
                yield vector
            } catch (err) {
                logger.log('error', `iterating vectors - ${err}`, HaikuUtils.getMetadataForLog(baseAttack))
            }
        }
    }


    /**
     * Iterate any additional vectors provided in config AND built-in vectors that plugin provides in that
     * order since site specific are likely to be more relevant
     * Either getAttackVectors() or getVectorIterator() has to be implemented by subclass
     * 
     * @param {baseAttack} baseAttack base attack - attack object without vector, encoding.
     */
    * _getAttackVectors(baseAttack) {
        // First iterate the request specific vectors
        let requestSpecificVectors = _.get(this.getPluginScopedStore(baseAttack), "vectors");

        if(requestSpecificVectors) {
            for (let vector of requestSpecificVectors) {
                yield vector
            }
        }
        
        // first iterate the site specific vectors since they are likely to be more relevant
        let siteSpecificVectors = this.getMetadata(baseAttack).siteSpecificVectors || []
        for (let vector of siteSpecificVectors) {
            yield vector
        }

        // now iterate the default vectors 
        if (!this.getMetadata(baseAttack).skipDefaultVectors) {
            for (let vector of this.getAttackVectors(baseAttack)) {
                yield vector
            }
        }
    }

    /**
     * Modify the param - the default implementaion just calls the parametrizedDelegate method.
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @param {parameter} parameter parameter to modify
     * @param {string} vector change parameter to this
     * @param {encoding} encoding encoding to use to modify param
     */
    modifyParam(parameterizedDelegate, parameter, vector, encoding) {
        if (vector == VectorResponseAttack.identityVector) {
            parameterizedDelegate.reset()
        } else {
            parameterizedDelegate.modifyParam(parameter.name, vector, encoding);
        }
    }

    /**
     * get array of attack vectors.
     * Either this or getVectorIterator() has to be implemented by subclass
     */
    getAttackVectors() {
        throw new TypeError('One of getAttackVectors() or getVectorIterator() must be implemented by subclass')
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @abstract
     */
    getAttackableEvents() {
        throw new TypeError('must be implemented by subclass')
    }


    /**
     * Can be overridden by attack plugins. This is used to move to next parameter if attack sent by a
     * particular attack plugin succeeded => can move on to the next parameter.
     * @param {attack} attack
     * @returns {boolean} true -> dont try remaining vectors & encodings for this parameter 
     */
    doneWithThisParam(attack) {
        try {
            let replayScanInfo = this.getConfig(attack.httpRequest.scanId).ScannerSettings.replayScanInfo;
            let attackAllVectors = this.getConfig(attack.httpRequest.scanId).ScannerSettings.attackAllVectors;
            
            //During replay scan, even if vulnerability found against vector, keep trying remaining vectors to get full WAF coverage 
            //specific to that vulnerability
            if(replayScanInfo.isReplayScan) {
                return false;
            }

            //SIGDEV - For WAF full coverage test, even if vulnerability found against vector, keep trying remaining vectors to get full WAF coverage 
            if(attackAllVectors) {
                return false;
            }
        } catch (error) {
            logger.log('info', `Error occured during replayScan in doneWithThisParam method reason: ${error.toString()}`, HaikuUtils.getMetadataForLog(attack))
        }

        // found this specific vulnerability (eg. sql inj) in this parameter, try next param.
        let vulns = _.get(attack, 'result.vulns', {})
        let vulnsToFindUsingThisPlugin = Object.keys(this.getMetadata(attack).vulnerabilities)
        let vulnsFoundInThisAttack = Object.keys(vulns)
        return _.difference(vulnsToFindUsingThisPlugin, vulnsFoundInThisAttack).length == 0
    }

    /**
     * set up options to customzize the parametrized delegate
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     */
    initParameterizedDelegate(parameterizedDelegate) {
        // nothing to do in base - defaults are fine
    }

    /**
     * Perform attack loop. Loops through parameters attacking each one.
     * @param {function} createParameterizedDelegate Funtion to create the deletegate that can iterate 
     * parameters based on protocol
     */
    async attackParameterizedRequest(createParameterizedDelegate) {
        try {
            // create the delegate
            let parameterizedDelegate = createParameterizedDelegate()
            this.initParameterizedDelegate(parameterizedDelegate)

            // set up base attack with params we already know about
            let baseAttack = {
                originalRequest: parameterizedDelegate.getOriginalRequest(),
                attackArea: parameterizedDelegate.getParameterType(),
            }

            // see if this plugin is allowed to send attack
            if (!this.allowedToAttack(baseAttack)) {
                return false
            }

            let iterator = parameterizedDelegate.getIterator()
            let parameters = iterator.next()

            // try every parameter
            while (!parameters.done) {
                let parameter = parameters.value

                // see if we should process this param
                if (this.okToProcessParameter(parameter, parameterizedDelegate)) {
                    logger.log('info', `trying parameter ${parameter.name}`, HaikuUtils.getMetadataForLog(baseAttack))

                    // try every attack vector
                    // update base attack with params we already know about
                    baseAttack.param = parameter.name
                    baseAttack.paramVal = parameter.val
                    baseAttack.attackType = parameterizedDelegate.getAttackType()

                    await this.attackParameter(baseAttack, parameterizedDelegate, parameter);

                } else {
                    logger.log('info', `skipping parameter ${parameter.name}`, HaikuUtils.getMetadataForLog(baseAttack))
                }

                // next parameter after cleaning up the parameter we tampered with
                parameters = iterator.next()
                parameterizedDelegate.reset()
            }
        } catch (err) {
            logger.log('error', `caught error : ${err.toString()}`)
        }
    }

    /**
     * @typedef {Object} parameterToAttack
     * @property {string} parameterToAttack.name parameter name
     * @property {string} parameterToAttack.value current (original) parameter value
     */

    /**
     * returns true if we can attack this parameter. Base class method allows all except the .net viewstate params and filters
     * out to only the attacked param for revalidation case.
     * @param {parameterToAttack} parameter parameter being atatcked
     * @param {Object} parameterizedDelegate delegate used to modify the parameter
     */
    okToProcessParameter(parameter, parameterizedDelegate) {
        //Do not attack parameter which doesn't exist 
        if (HaikuUtils.isNullOrUndefined(parameter.val)) {
            return false;
        }
        
        let revalidationInfo = parameterizedDelegate.getOriginalRequest().revalidationInfo
        if (revalidationInfo && revalidationInfo.attack.param != parameter.name) {
            return false
        }

        let originalRequest = parameterizedDelegate.getOriginalRequest()
        if (this.getMetadata(originalRequest).attackOnlyParameterMatchingRegex) {
            return this.getMetadata(originalRequest).attackOnlyParameterMatchingRegex.test(parameter.name);
        }
        if (this.getMetadata(originalRequest).skipParameterMatchingRegex) {
            let skipParam = this.getMetadata(originalRequest).skipParameterMatchingRegex.test(parameter.name);
            if (skipParam) {
                return false
            }
        }
        let okToProcess = !this.inviolateParamRegex.test(parameter.name);
        if (!okToProcess) {
            if (this.getMetadata(originalRequest).forceAllowParameterRegex) {
                return this.getMetadata(originalRequest).forceAllowParameterRegex.test(parameter.name);
            }
        }
        return okToProcess

    }

    /**
     * Perform attack on one parameter
     *  iterate encodings
     *      iterate vectors
     *          tamper param
     *          send attack
     *          check if done
     *          reset    
     * @param {attack} baseAttack base attack set up to attack one with one parameter
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @param {object} parameter parameter being tampered
     */
    async attackParameter(baseAttack, parameterizedDelegate, parameter) {
        // loop through all teh encodings
        let encodingsGen = this.getEncodingIterator(baseAttack, parameter, parameterizedDelegate)
        let encodingsIt = encodingsGen.next()
        while (!encodingsIt.done) {
            let encoding = encodingsIt.value
            let vectorsGen = this.getVectorIterator(baseAttack, encoding)
            let vectorsIt = vectorsGen.next();
            while (!vectorsIt.done) {
                let vector = vectorsIt.value;
                // attack metadata
                let attack = _.clone(baseAttack);
                attack.vector = vector;
                attack.encoding = encoding;

                // Generate/update template values for the attack
                this.templatizeAttackVectors(attack);

                // about to run attack
                logger.log('info', `\t${this.constructor.name} -> ${parameterizedDelegate.getParameterType()}, ${parameter.name}, ${parameterizedDelegate.getAttackType()} - trying vector ${attack.vector}, encoding : ${encoding}`, HaikuUtils.getMetadataForLog(baseAttack));

                // tamper param
                this.modifyParam(parameterizedDelegate, parameter, attack.vector, encoding);

                // Update Content-Type for file uploads
                if (attack.attackArea === 'FileUpload' && attack.vector) {
                    // Get appropriate Content-Type based on the file extension
                    let contentType = HaikuUtils.getContentTypeFromFilePath(attack.vector);
                    attack.originalRequest.httpRequest.parsedData.files[attack.param].contentType = contentType;
                }

                attack.httpRequest = await parameterizedDelegate.getHttpRequest(encoding)

                //Call onAutoPOC for OOB plugins
                if(attack.uploadAttackToS3 && this.vulnerabilityID) {
                    this.onAutoPOC(attack, this.vulnerabilityID);
                }

                //Save attacks to S3. i.e. OOB attacks
                await this.saveAttackInfoToS3(attack);

                this.updatePluginHeader(attack);

                // send attack, will also trigger the response checks etc.
                let result = await this.performNetworkAttack(attack);
                if (this.doneWithThisParam(attack)) {
                    return true // done with this param
                }
                // go to next vector
                vectorsIt = vectorsGen.next();
            }

            // eg. when attacking param names, reset is always required
            if (parameter.resetRequired) {
                parameterizedDelegate.reset();
            }
            encodingsIt = encodingsGen.next()
        }

        return false // no vuln found.
    }

    /**
     * Update attack vector template values
     * @param {Object} attack Attack object for which vector template values need to be updated
     */
     templatizeAttackVectors(attack) {
        let uuid = uuidv4();
        let uploadAttackToS3 = this._getBooleanProperty(attack, 'uploadAttackToS3');
        let canAttackInOOB = this._getBooleanProperty(attack, 'canAttackInOOB');

        // Generate/update template values for the attack
        if (attack.attackArea != 'original-crawler-request' && canAttackInOOB) {
            try {
                if (this.vulnerabilityID) {
                    let serviceId = this.networkScanner.getScanInfo(attack.originalRequest.scanId).serviceId;
                    let vulnObj = this.getVulnObject(attack.originalRequest.scanId, this.vulnerabilityID);
                    attack.vector = attack.vector.replace('{{scannerVector}}', `${uuid}.${serviceId}.${attack.originalRequest.scanId}.${attack.originalRequest.scanlog_id}.${vulnObj.igwId}`);

                    let parsedUrl = new URL(attack.originalRequest.httpRequest.uri)
                    attack.name = this.getName();
                    attack.hostname = parsedUrl.hostname;
                    attack.href = parsedUrl.href;
                    attack.uuid = uuid;
                    attack.uploadAttackToS3 = uploadAttackToS3;
                    attack.canAttackInOOB = canAttackInOOB;
                }
                else {
                    logger.log('error', `templatizeAttackVectors, no vulnerabilityID defined for plugin "${this.pluginName}"`, HaikuUtils.getMetadataForLog(attack))
                }
            }
            catch (err) {
                logger.log('error', `templatizeAttackVectors, unable to set dynamic/template vector ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
            }
        }
    }

    /**
     * Update plugin path in attack request header when running scan for full WAF coverage
     */
    updatePluginHeader(attack) {
        try {
            let attackAllVectors = this.getConfig(attack.httpRequest.scanId).ScannerSettings.attackAllVectors;

            if(attackAllVectors) {
                attack.httpRequest.headers['PluginName'] = this.pluginName;
                attack.httpRequest.headers['PluginFile'] = this.pluginFile.replace('.js', '');
                attack.httpRequest.headers['PluginIds'] = _.join(this.pluginIds, ',');
            }
        } catch (err) {
            logger.log('error', `Unable to update plugin path in attack request header - ${err}`, HaikuUtils.getMetadataForLog(attack));
        }
    }
}

module.exports = VectorResponseAttack