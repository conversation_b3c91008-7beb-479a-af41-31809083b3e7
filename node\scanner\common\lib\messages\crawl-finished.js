const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:CrawlFinished')

/** 
 * crawler -> scanner indicating crawl has started
 * @extends QueueMessage
*/
class CrawlFinished extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} crawlFinishedMsgContent
     * @property {Number} scanId Scan ID (session ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request (haiku)
     * @property {string} mainUrl Full URI that is the root of the scan/crawl
     */
    /**
     * @param {crawlFinishedMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner'
        this.routingKey = 'request.crawl-finished'
        this.msgType = CrawlFinished.msgType
    }
}

module.exports = CrawlFinished