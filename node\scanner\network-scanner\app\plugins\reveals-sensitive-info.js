const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class RevealSensitiveInfo extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityIDInfo = 'ID-reveals-sensitive-info' //353793
        this.vulnerabilityIDLow = 'ID-reveals-sensitive-low'  //352049

    }

    /* wantProcessAttackResponse(attack) {
        let ExcludeURL = /\/blog\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i
        if (ExcludeURL.test(attack.href)) {
            return false
        }
        let OriReqHref = _.get(attack, "originalRequest.httpRequest.uri")
        if (attack.attackArea == "original-crawler-request" &&) { }
        let ResBody = _.get(attack, "result.resp.body", '')
        if (ResBody.length > 5) {
            let args_regexp = /(\w*( |-|_)?(number|no)|\w*(\b|-|_)(mobile|contact|phone|blood_group|(credit|debit|pan)(_|-)?(card|id)|(user|emp|employee)(_|-)?(id|name|Input)|uname|uid|user|(account|ac)(_|-)?(id)?|password|passwd|pwd|api(_|-)?key)) ?(=|" ?:) ?('|")(?! \+ |\+)[^'"<>${}\n\?=&\/%\[\]]+\17/i
            if (args_regexp.test(ResBody)) {
                return true
            }
            // return false
        }
        return false
    } */

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.SenInfobodyFound) {
            return
        }

        let ExcludeURL = /\/blog\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i
        if (ExcludeURL.test(attack.href)) {
            return
        }

        let uri = new URL(attack.href)

        //RegExp: pathname created by haiku for checking vuln
        let pathname_RegExp = /\/jsmol2wp\/php\/jsmol\.php|\/owa\/auth\/x\.js|\/DSWAPutTest\.txt/i
        if (pathname_RegExp.test(uri.pathname)) { return }

        //Exclude Docs commonly using by bank
        /* Scanned URL
        https://bcmsuat.hdfcbank.com/assets/i18n/enOne.json
        https://indusbusinessloansgrid.indusind.com/assets/i18n/customerapp/en.json
         */
        if (/\/i18n\//i.test(uri.pathname) && /\w\.json/i.test(uri.pathname)) { return }

        let argname = []
        let userdetails_low = []
        let userdetails_info = []
        let args_regexp = /(\w*( |-|_)?(number|no)|\w*(\b|-|_)(mobile|contact|phone|blood_group|(credit|debit|pan)(_|-)?(card|id|no)|pan|email|vehEng|vehChs|(user|emp|employee)(_|-)?(id|name|Input)|uname|uid|user|(account|ac)(_|-)?(id)?|password|passwd|pwd|api(_|-)?key)|ProposerPAN|kyc_id|customerId) ?(=|" ?:) ?('|")(?! \+ |\+)[^'"<>${}\n\?=&\/%\[\]]+\17|"password\\":\\".+?\\"/ig

        let ReqBody = _.get(attack, 'originalRequest.httpRequest.body', '')
        let checkreqBody = /(?:(credit|debit|pan)(_|-)?(card|id|no)|\b(account|ac)(_|-)?(id)?|api(_|-)?key|ProposerPAN|kyc_id) ?(=|" ?:) ?('|")(?! \+ |\+)[^'"<>${}\n\?=&\/%\[\]]+\9/ig

        let ResBody = _.get(attack, "result.resp.body", '')
        let OriReqHref = _.get(attack, "originalRequest.httpRequest.uri")

        let sensData = /jquery\-[\d\.]+/ig
        if (attack.attackArea == "original-crawler-request" && sensData.test(OriReqHref)) {
            userdetails_low.push({ result: OriReqHref + '\n' })
        }

        if (sensData.test(ResBody)) {
            let Jargval = ResBody.match(sensData)[0]
            userdetails_low.push({ result: Jargval + '\n' })
        }

        if (args_regexp.test(ResBody) || checkreqBody.test(ReqBody)) {
            // let args_regexp = /(\w*( |-|_)?(number|no)|\w*(\b|-|_)(mobile|contact|phone|blood_group|(credit|debit|pan)(_|-)?(card|id)|(user|emp|employee)(_|-)?(id|name|Input)|uname|uid|user|(account|ac)(_|-)?(id)?|password|passwd|pwd|api(_|-)?key)) ?(=|" ?:) ?('|")(?! \+ |\+)[^'"<>${}\n\?=&\/%\[\]]+\17/ig

            // below regex for exclude the argvalue - RHS
            let argvalregexp = /( |\b|_)(?:pan|otp|credit|debit|(set)?(user|employee)(_|-| )?(id|name|Input)|user|(account|ac)_?(id)?|(set)?password|api|mobile|contact|phone|nil|(in)?valid|defaultText|No|none|Choose|select|yes|(91)?**********|fax|cell|indusface.com|true)\b| = ?"|N\/A|special characters|required|null|haiku(msg)?|undefined|false|name|\benter|try again|Alert|string|number|forgot|function|\*\*\*\*\*/i

            //RHS - exclude
            let argnameregexp = /page(-|_| )?(no|number)?|support|hello|reachus|customer|care|cust_serv_number|material|appnumber|helpline|model/i
            // /(?:\b|_)(?:token|session|user_?(?:id|name|Input)|uname|uid|user|email_?(id)?|(?:mobile|(?:tele)?phone|card)_?(number)?|password|passwd|pwd|api(?:_|-)key) ?(?:=|:) ?(?:'|").+?(?:'|")/ig

            // let ResBody = _.get(attack, "result.resp.body", '')

            try {
                if (ResBody.match(args_regexp) != null) {
                    argname = ResBody.match(args_regexp)
                }
                if (ReqBody.match(checkreqBody) != null) {
                    argname += ',' + ReqBody.match(checkreqBody)
                    argname = argname.split(',')
                }
                if (argname.length > 0) {
                    for (let argval of argname) {
                        if (!new RegExp(_.escapeRegExp(argval)).test(userdetails_info.map(v => v.result).join()) && !new RegExp(_.escapeRegExp(argval)).test(userdetails_low.map(v => v.result).join())) { //avoid duplicate
                            let currval = argval.split(/=|:/)
                            if (currval[0].length > 2 && !argnameregexp.test(currval[0]) && currval[1].length > 5 && currval[1].length < 25 && !/null/i.test(currval[1]) && !/\bUA-\d+-\d/i.test(currval[1]) && !argvalregexp.test(currval[1]) && !currval[1].includes('.txt') && currval[1].replace(/"|\s|'/g, '').length > 2) {
                                if (/api/i.test(currval[0])) {
                                    userdetails_info.push({ result: argval + '\n' })
                                }
                                else if (/mobile|contact|phone/i.test(currval[0]) && currval[1].length < 40) {
                                    if (/('|")[\d\-\+\(\)]{8,20}\1/.test(currval[1]) && !/"(\+(91)?-?)?(1-?8|22)|"\(022/.test(currval[1]) && !currval[1].includes('0000000')) {
                                        userdetails_info.push({ result: argval + '\n' })
                                    }
                                    else { continue }
                                }
                                else if (/no|number|account/i.test(currval[0]) && !/mobile|contact|phone|pan/i.test(currval[0]) && currval[1].length < 40) {
                                    if (/('|")[a-z0-9\-]+('|")/i.test(currval[1]) && /\w{5,}/.test(currval[1])) {
                                        if (/account|ac(_|-)?(?:no|number|id)/i.test(currval[0]) && !/\/(docs?|blog)\//i.test(uri.pathname) && !/\.txt$/i.test(uri.pathname)) {
                                            userdetails_low.push({ result: argval + '\n' })
                                        }
                                        else {
                                            userdetails_info.push({ result: argval + '\n' })
                                        }
                                    }
                                    else { continue }
                                }
                                else if (/user(-|_)?(name|id)?/i.test(currval[0]) && !/tomcat|test|foo|bar/i.test(currval[1]) && !/\/(docs?|blog)\//i.test(uri.pathname) && !/\.txt$/i.test(uri.pathname) && currval[1].length < 40) {
                                    userdetails_low.push({ result: argval + '\n' })
                                }
                                else if (/(credit|debit)(_|-)?(?:card|id|no)/i.test(argname)) {
                                    userdetails_low.push({ result: argval + '\n' })
                                }
                                else if (/(\bpan)(_|-)?(?:card|id|no)|proposerpan|\bpan\b/i.test(currval[0])&&/\w{10,15}/.test(currval[1])) {
                                    userdetails_low.push({ result: argval + '\n' })
                                }
                                else if (currval[1].length < 40) {
                                    userdetails_info.push({ result: argval + '\n' })
                                }
                            }
                        }
                    }
                }
            }
            catch (e) {
                return
            }
        }
        if (userdetails_info.length > 0) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityIDInfo, userdetails_info)
            pluginDataForRequest.SenInfobodyFound = true
        }
        if (userdetails_low.length > 0) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityIDLow, userdetails_low)
            pluginDataForRequest.SenInfobodyFound = true
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID == this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
    }
}

module.exports = RevealSensitiveInfo 