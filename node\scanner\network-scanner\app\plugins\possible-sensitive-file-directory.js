const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')
// const RegExpVari = require('./generic-regexp');

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class PossibleSensitiveContent extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.PSCvulnerabilityID = 'ID-possible-sensitive-file-or-directory'
        this.HTAvulnerabilityID = 'ID-readable-htaccess'
        // this.DPDvulnerabilityID = 'ID-webserver-default-page-detected' Moved to default-web-page.js plugin
        this.CDvulnerabilityID = 'ID-permissive-cross-domain-policy'
        this.clientaccesspolicyID = 'ID-client-access-policy-FD'

        this.DPDVulnRegex = new RegExp(
            /(?:(?:test|simple)[.\s]{1,10}page[.\s]{1,10}(?:for)?[.\sa-z*]{1,10}apache|apache.server|default.web.page|ibm.http.server|<title>\s*apache2.*default.*<\/title>|<title>\s*iis\d[^<]*<\/title>|<title>iis\swindows\sserver<\/title>)/ig)
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            clearQueryParams: true,
            haveSlashAfterAttack: 'never'
        });
    }

    getAttackVectors() {
        return PSFDVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    async processAttackResponse(attack) {
        let statusCode = _.get(attack, "result.resp.httpResponse.statusCode");
        let redirectsFollowed = _.get(attack, "result.resp.httpResponse.redirectsFollowed");
        let responseBody = _.get(attack, "result.resp.body").trim();
        if (responseBody.length == 0) return;

        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(responseBody)) return;
        const allowedstatusCode = [200, 409];

        if (allowedstatusCode.includes(statusCode) && responseBody.length >= 20 && redirectsFollowed == 0) {
            // this.checkDPDvuln(attack, responseBody) Moved to default-web-page.js plugin
            this.checkHTAccessLeakage(attack, responseBody)
            this.checkCrossDomain(attack, responseBody)
            this.checkClientAccessPolicy(attack, responseBody)
            this.checkPhpMyAdmin(attack, responseBody)
            this.checkManualDir(attack, responseBody)
            this.checkServerLogs(attack, responseBody)
            this.checkelmahlog(attack, responseBody)
            this.checkHtdocsExposure(attack, responseBody)
            this.checkErrorLogsExposure(attack, responseBody)
            this.checkConfigFile(attack, responseBody)
            this.checkDebugFile(attack, responseBody)
            this.checkNginxConf(attack, responseBody)
            this.checkWebXml(attack, responseBody)
            this.checkWpAdmin(attack, responseBody)
            this.checkWebConsole(attack, responseBody)
            this.checkServerInfo(attack, responseBody)
            this.checkPhpInfo(attack, responseBody)
            this.checkPhpInfoInResBody(attack, responseBody)
            this.checkWebConfig(attack, responseBody)
            this.checkSetupPhp(attack, responseBody)
            this.checklocalstart(attack, responseBody)
            this.checkLocalStart(attack, responseBody)
            this.checkGitConfig(attack, responseBody)
            this.checkDockerfile(attack, responseBody)
            this.checkRequestHeaderExample(attack, responseBody)
            this.checkServlets(attack, responseBody)
            this.check_vti_inf(attack, responseBody)
            this.checkMetrics(attack, responseBody)
            this.checkTrace(attack, responseBody)
        }
    }

    extractContext(responseBody, match) {
        const contextLength = 10; // Number of words before and after the match
        const regex = new RegExp(`(?:\\S+\\s+){0,${contextLength}}\\S*${match}\\S*(?:\\s+\\S+){0,${contextLength}}`, 'gi');
        const matchContext = responseBody.match(regex);

        return matchContext ? matchContext[0] : match;
    }

    /* checkDPDvuln(attack, responseBody) { // Moved to default-web-page.js plugin
        if (!this.getPluginScopedStore(attack).DPDvulnFound && !/\/icons/i.test(attack.httpRequest.uri)) { // Exclude /icons directory
            const matches = responseBody.match(this.DPDVulnRegex); // Get all matches from the responseBody
            if (matches) {
                const context = this.extractContext(responseBody, matches[0]);
                this.addVulnerabilitytoResult(attack, this.DPDvulnerabilityID, `Matched content: ${context}`);
                this.getPluginScopedStore(attack).DPDvulnFound = true;
            }
            // Detects specific vulnerabilities based on URL and response body patterns
            else if (/\/(?:loginmodulewebapi|manager)\/|\/readme\.html|startPage|authentication/i.test(attack.href)) {
                const patterns = [
                    /Web Api is Created for Login Module Project using asp\.net Technology/i,
                    /please examine the file conf\/tomcat-users\.xml in your installation/i,
                    /ASP\.NET is a free web framework for building great websites and web applications using HTML, CSS, and JavaScript\./i,
                    />wp-admin\/install\.php</i,
                    /SAP Library contains the complete documentation for SAP NetWeaver Application Server Java. You can access it by choosing SAP NetWeaver/i,
                    /Welcome to Keycloak/i
                ];

                const match = patterns.some(pattern => pattern.test(responseBody));
                if (match) {
                    const context = this.extractContext(responseBody, match[0]);
                    if (context) {
                        this.addVulnerabilitytoResult(attack, this.DPDvulnerabilityID, `Exposed DPD vulnerability Found, Context: "${context}"`);
                        this.getPluginScopedStore(attack).DPDvulnFound = true;
                    }
                }
            }
        }
    } */

    checkHTAccessLeakage(attack, responseBody) {
        if (/\/\.?(?:[\w\-]*htaccess[\w\-]*)\.?(?:txt|bak|backup|old|save|~)?/i.test(attack.href)) {
            const match = responseBody.match(/\b(?:RewriteEngine|RewriteRule|RewriteCond|ErrorDocument|Redirect\s301|RedirectMatch|DirectoryIndex|<FilesMatch|AuthUserFile|AuthGroupFile|AuthType|AllowOverride|Require|Options|SetEnv|Deny\sfrom|Order\sallow,deny)\b/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.HTAvulnerabilityID, `Sensitive file Found, Context: "${context}"`);
            }
        }
    }

    checkCrossDomain(attack, responseBody) {
        // Check if the request is for any crossdomain.xml file (including variations)
        if (/\/crossdomain\.xml(?:\b|_)/i.test(attack.href)) {
            // Check for insecure wildcard policy in XML response
            const match = responseBody.match(/\b(?:allow-access-from|allow-http-request-headers-from)\s*domain\s*=\s*["']\*["']/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.CDvulnerabilityID, `Insecure wildcard policy found in crossdomain.xml, Context: "${context}"`);
            }
        }
    }

    checkClientAccessPolicy(attack, responseBody) {
        // Detects insecure clientaccesspolicy.xml file to report vulnerability
        if (/\/\.?(?:clientaccesspolicy(?:[\w\-]*)?)\.xml/i.test(attack.href)) {
            const match = responseBody.match(/\b(?:domain\s+uri\s*=\s*"(\*|https?:\/\/\*)")/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.clientaccesspolicyID, `Sensitive file Found, Context: "${context}"`);
            }
        }
    }

    checkPhpMyAdmin(attack, responseBody) {
        // Detects exposed phpMyAdmin instance and reports vulnerability
        if (!this.getPluginScopedStore(attack).PSC2 && /\/phpmyadmin(?:[\w\-]*)?/i.test(attack.href)) {
            const match = responseBody.match(/<title>\s*phpmyadmin\s*<\/title>/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Sensitive service Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC2 = true;
            }
        }
    }


    checkManualDir(attack, responseBody) {
        // Detects exposed manual directories and reports vulnerability
        if (!this.getPluginScopedStore(attack).PSC3 && /\/manual(?:\/|$|\?|#)/i.test(attack.href)) {
            const match = responseBody.match(/Apache\sHTTP\sServer\sVersion/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Sensitive directory Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC3 = true;
            }
        }
    }


    checkServerLogs(attack, responseBody) {
        if (!this.getPluginScopedStore(attack).PSC4 && /(?:\b|_)logs?(?:\b|_)/i.test(attack.href)) {
            const isJsonLog = responseBody.match(/{"messages":\s?{"messages":\s?\[/ig);
            const isPlaintextLog = responseBody.match(/\d{4}[-\/]\d{2}[-\/]\d{2}.*?\d{2}[:.h]\d{2}[:.]\d{2}/ig);

            if (isJsonLog || isPlaintextLog) {
                const context = this.extractContext(responseBody, isJsonLog ? isJsonLog[0] : isPlaintextLog[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Sensitive Log File Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC4 = true;
            }
        }
    }


    checkelmahlog(attack, responseBody) {
        // ELMAH log detection - reported from CS team
        if (!this.getPluginScopedStore(attack).PSC5 && /\/elmah(?:\/|\.axd)/i.test(attack.href)) {
            const hasTitleMatch = responseBody.match(/<title>[\w\W]*error(?:-|_| )?log[\w\W]*?<\/title>/ig);
            const hasDateMatch = responseBody.match(/(?:\d{4}[-\/]\d{1,2}[-\/]\d{1,2}|\d{1,2}[-\/]\w{3}[-\/]\d{4}|\d{1,2}[-\/]\d{1,2}[-\/]\d{4}|\d{4}[-\/]\w{3}[-\/]\d{1,2})/);
            const hasTimeMatch = responseBody.match(/\d{1,2}[:h]\d{2}[.:]\d{2}/ig);

            if (hasTitleMatch && hasDateMatch && hasTimeMatch) {
                const context = this.extractContext(responseBody, hasTitleMatch[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Sensitive ELMAH Log Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC5 = true;
            }
        }
    }


    checkHtdocsExposure(attack, responseBody) {
        if (!this.getPluginScopedStore(attack).PSC6 && /\/htdocs(?:\b|_)/i.test(attack.httpRequest.uri)) {
            const hasDirectoryListing = /index\s+of|parent\s+directory/i.test(responseBody);
            if (hasDirectoryListing) {
                const context = this.extractContext(responseBody, hasDirectoryListing[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed htdocs Directory Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC6 = true;
            }
        }
    }

    checkErrorLogsExposure(attack, responseBody) {
        if (!this.getPluginScopedStore(attack) && /\/_errors(?:\b|_)/i.test(attack.httpRequest.uri)) {
            // Regex for error logs (stack traces, timestamps, and error messages)
            const errorLogPattern = /(?:fatal\s+error|warning:|exception\sin|traceback\s\(most\sc|error\slog|stack\s(?:trace|overflow)|\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})/gi;

            if (errorLogPattern.test(responseBody)) {
                const context = this.extractContext(responseBody, responseBody.match(errorLogPattern)[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Potentially Exposed _errors Directory with Logs, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC7 = true;
            }
        }
    }


    checkConfigFile(attack, responseBody) {
        // Detects exposed configuration files (config.php, config.inc) but excludes core config
        if (!this.getPluginScopedStore(attack).PSC_Config) {
            const isConfigFile = /(?:config)\.(?:php|inc)/i.test(attack.href);
            const isExcludedCoreConfig = /\/core\/config/i.test(attack.href);

            if (isConfigFile && !isExcludedCoreConfig) {
                // Detects PHP code, database credentials, or sensitive settings
                const configPatterns = /<\?php|\$MYSQL_HOST|DIR_MEDIA|\*\*\s*MYSQL\s+settings\s*\*\*|\$cfg|Server:|Servers\s+configuration|\$config|(?:\$|define\(')((?:db_?)?(?:password|database|server|username|name|host))/gi;
                const match = responseBody.match(configPatterns);

                if (match) {
                    const context = this.extractContext(responseBody, match[0]);
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed Configuration File Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC_Config = true;
                }
            }
        }
    }

    checkDebugFile(attack, responseBody) {
        // Detects exposed debug files (debug.inc, debug.log)
        if (!this.getPluginScopedStore(attack).PSC_Debug) {
            const isDebugFile = /(?:debug)\.(?:inc|log)/i.test(attack.href);

            if (isDebugFile) {
                // Detects debug logs, error traces, and system paths
                const debugPatterns = /(error|debug)\s?(log|trace)|exception|stack\s?trace|file\s?path:|fatal\s?error|database\s?error/gi;
                const match = responseBody.match(debugPatterns);

                if (match) {
                    const context = this.extractContext(responseBody, match[0]);
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed Debug File Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC_Debug = true;
                }
            }
        }
    }


    checkNginxConf(attack, responseBody) {
        // Detects exposed nginx.conf files or related configurations
        if (/\/(?:nginx\.conf|nginx\.config|\.nginx|nginx.*\.(?:bak|old|txt|backup))(?:\b|_)/i.test(attack.href)) {
            const sensitivePatterns = /error\.log|worker_(?:processes|connections)|nginx\.pid|mime\.types|server_name|keepalive_timeout|document_root|server_port|proxy_pass|ssl_certificate_key|access_log/gi;
            const match = responseBody.match(sensitivePatterns);

            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed Nginx Configuration Found, Context: "${context}"`);
            }
        }
    }


    checkWebXml(attack, responseBody) {
        // Detects exposed web.xml files
        if (!this.getPluginScopedStore(attack).PSC8 && /\/web\.xml(?:\b|_)/i.test(attack.href)) {
            const sensitivePatterns = /<web-app\sxmlns|<servlet>|<\/web-app>|<security-constraint>|<servlet-mapping>|<init-param>|jdbc\/\w+/ig;
            const match = responseBody.match(sensitivePatterns);

            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed web.xml Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC8 = true;
            }
        }
    }


    checkWpAdmin(attack, responseBody) {
        // Detects exposed wp-admin/setup-config.php
        if (!this.getPluginScopedStore(attack).PSC9 && /\/wp-admin\/setup-config\.php(?:\b|_)/i.test(attack.href)) {
            const match = responseBody.match(/wp-config.php|@package\sWordPress|define\('WP_INSTALLING|define\('WP_SETUP_CONFIG',\s?true\)/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed WordPress Setup File Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC9 = true;
            }
        }
    }

    checkWebConsole(attack, responseBody) {
        // Detects exposed JBoss Management Console
        if (!this.getPluginScopedStore(attack).PSC10 && /\/web-console\/ServerInfo\.jsp(?:\b|_)/i.test(attack.href)) {
            const match = responseBody.match(/<title>JBoss\sManagement\sConsole\s-\sServer\sInformation<\/title>|<h3>JBoss&trade;\sApplication\sServer<\/h3>|JBoss&trade;\sManagement\sConsole/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed JBoss Management Console Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC10 = true;
            }
        }
    }

    checkServerInfo(attack, responseBody) {
        // Detects exposed Apache server-info page
        if (!this.getPluginScopedStore(attack).PSC11 && /\/server-info(?:\b|_)/i.test(attack.href)) {
            const match = responseBody.match(/Server\sInformation|Apache(?:\/\d+\.\d+\.\d+)?|<h1>Apache\sStatus<\/h1>/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed Apache Server Info Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC11 = true;
            }
        }
    }

    checkPhpInfo(attack, responseBody) {
        // Detects exposed phpinfo or php.ini files
        if (!this.getPluginScopedStore(attack).PSC12 && /\/(?:(get)?(php)?info\.php|php\.ini)(?:\b|_)/i.test(attack.href)) {
            const match = responseBody.match(/(?:<title>[^>]*php[ _-]?info[^<]*<\/title>|<tr><td class="\w">(?:System|PHP Version|Loaded Configuration File|DOCUMENT_ROOT)\s*<\/td><td class="\w">)/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed PHP Info Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC12 = true;
            }
        }
    }

    checkPhpInfoInResBody(attack, responseBody) {
        if (!this.getPluginScopedStore(attack).PSC12) {
            // Detects exposed phpinfo or php.ini files
            const match1 = responseBody.match(/<tr><td class="\w">(?:X-Powered-By|expose_php|file_uploads|error_log|upload_max_filesize)\s*<\/td><td class="\w">/ig);
            const match2 = responseBody.match(/(?:<title>[^>]*php[ _-]?info[^<]*<\/title>|<tr><td class="\w">(?:System|PHP Version|Loaded Configuration File|DOCUMENT_ROOT)\s*<\/td><td class="\w">)/ig)
            const match3 = responseBody.match(/<tr><td class="\w">PHP Extension(?: Build)?\s*<\/td><td class="\w">[^<]+<\/td><\/tr>/ig)
            if (match1 && match2 && match3) {
                const context = this.extractContext(responseBody, match1[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed PHP Info Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC12 = true;
            }
        }
    }

    checkWebConfig(attack, responseBody) {
        // Detects exposed web.config (IIS configuration files)
        if (!this.getPluginScopedStore(attack).PSC13 && /\/web\.config(?:\b|_)/i.test(attack.href)) {
            const match = responseBody.match(/configuration|system\.webServer/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed IIS Web Config Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC13 = true;
            }
        }
    }

    checkSetupPhp(attack, responseBody) {
        // Detects exposed setup.php files
        if (!this.getPluginScopedStore(attack).PSC14 && /\/setup\.php(?:\b|_)/i.test(attack.href)) {
            const match = responseBody.match(/<title>\s*setup\s*<\/title>/ig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed Setup.php Found, Context: "${context}"`);
                this.getPluginScopedStore(attack).PSC14 = true;
            }
        }
    }

    checklocalstart(attack, responseBody) {
        //for localstart.asp file found report vuln
        if (!this.getPluginScopedStore(attack).PSC15 && attack.href.indexOf('/localstart.asp') != -1) {
            if ((/^\<% @Language = "VBScript" %\>/i.test(responseBody) && /Dim oFS,oFSPath/i.test(responseBody))) {
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `URL: ${attack.href}`)
            } else if ((/^\<%@ Language = "VBScript" %\>/i.test(responseBody)) && /Dim oFS, oFSPath/i.test(responseBody)) {
                this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `URL: ${attack.href}`)
                this.getPluginScopedStore(attack).PSC15 = true
            }
        }
    }

    checkLocalStart(attack, responseBody) {
        // Detects exposed localstart.asp files
        if (!this.getPluginScopedStore(attack).PSC15 && /\/localstart\.asp(?:\b|_)/i.test(attack.href)) {
            const vbScriptPattern = /^\<%@?\s*Language\s*=\s*"VBScript"\s*%\>/i;
            const dimPattern = /Dim\s+oFS,\s*oFSPath/i;

            const match = responseBody.match(vbScriptPattern);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                if (context && dimPattern.test(responseBody)) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed localstart.asp Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC15 = true;
                }
            }
        }
    }

    checkGitConfig(attack, responseBody) {
        // Detects exposed .git/config files
        if (!this.getPluginScopedStore(attack).PSC16 && /\.git\/?config(?:\b|_)/i.test(attack.href)) {
            const isValidGitConfig = /\[core\][\s\S]*repositoryformatversion\s*=\s*\d+/i;
            const hasUserConfig = /\[user\][\s\S]*(name|email)\s*=\s*.+/i;

            const match = responseBody.match(isValidGitConfig);
            if (match) {
                const context = this.extractContext(responseBody, match[0]);
                if (context && hasUserConfig.test(responseBody)) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed .git/config Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC16 = true;
                }
            }
        }
    }

    checkDockerfile(attack, responseBody) {
        // Detects exposed Dockerfile files
        if (!this.getPluginScopedStore(attack).PSC17 && /\/Dockerfile(?:\b|_)/i.test(attack.href)) {
            const addPattern = /ADD\s+apache-config\.conf/i;
            const envPattern = /ENV\s+APACHE_PID_FILE\s+\/var\/run\/apache2\.pid/i;
            const runPattern = /RUN\s+a2enmod\s+rewrite/i;

            const matchAdd = responseBody.match(addPattern);
            const matchEnv = responseBody.match(envPattern);
            const matchRun = responseBody.match(runPattern);

            if (matchAdd || matchEnv || matchRun) {
                const context = this.extractContext(responseBody, matchAdd ? matchAdd[0] : matchEnv ? matchEnv[0] : matchRun[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed Dockerfile Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC17 = true;
                }
            }
        }
    }

    checkRequestHeaderExample(attack, responseBody) {
        // Detects exposed RequestHeaderExample files
        if (!this.getPluginScopedStore(attack).PSC18 && /\/RequestHeaderExample(?:\b|_)/i.test(attack.href)) {
            const headerPattern = /x-forwarded-for|host/i;
            const userAgentPattern = /user-agent/i;

            const matchHeader = responseBody.match(headerPattern);
            const matchUserAgent = responseBody.match(userAgentPattern);

            if (matchHeader && matchUserAgent) {
                const context = this.extractContext(responseBody, matchHeader[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed RequestHeaderExample Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC18 = true;
                }
            }
        }
    }

    checkServlets(attack, responseBody) {
        // Detects exposed servlets files
        if (!this.getPluginScopedStore(attack).PSC19 && /\/servlets(?:\b|_)/i.test(attack.href)) {
            const servletPattern = /Servlet Examples with Code/i;
            const detailsPattern = /Request Headers|Request Parameters|Cookies/i;

            const matchServlet = responseBody.match(servletPattern);
            const matchDetails = responseBody.match(detailsPattern);

            if (matchServlet && matchDetails) {
                const context = this.extractContext(responseBody, matchServlet[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed servlets Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC19 = true;
                }
            }
        }
    }

    check_vti_inf(attack, responseBody) {
        // Detects exposed _vti_inf.html files
        if (!this.getPluginScopedStore(attack).PSC20 && /\/_vti_inf\.html(?:\b|_)/i.test(attack.href)) {
            const servletPattern = /Servlet Examples with Code/i;
            const vtiPattern = /_vti_bin\/(?:shtml\.dll\/_vti_rpc|_vti_aut\/author\.dll|_vti_adm\/admin\.dll)/gi;

            const matchServlet = responseBody.match(servletPattern);
            const matchVti = responseBody.match(vtiPattern);

            if (matchServlet && matchVti) {
                const context = this.extractContext(responseBody, matchServlet[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed _vti_inf.html Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC20 = true;
                }
            }
        }
    }

    checkMetrics(attack, responseBody) {
        // Detects exposed metrics files
        if (!this.getPluginScopedStore(attack).PSC21 && /\/metrics(?:\b|_)/i.test(attack.href)) {
            const errorPattern = /login_error_count 0|lead_add_(?:request|error)_count\s?{\s?client/gi;
            const statusPattern = /status_code=/i;
            const methodPattern = /method=/i;
            const pathPattern = /path=/i;

            const matchError = responseBody.match(errorPattern);
            const matchStatus = responseBody.match(statusPattern);
            const matchMethod = responseBody.match(methodPattern);
            const matchPath = responseBody.match(pathPattern);

            if (matchError) {
                const context = this.extractContext(responseBody, matchError[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed metrics Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC21 = true;
                }
            } else if (matchStatus && matchMethod && matchPath) {
                const context = this.extractContext(responseBody, matchStatus[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed metrics Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC21 = true;
                }
            }
        }
    }

    checkTrace(attack, responseBody) {
        // Detects exposed trace.axd files
        if (!this.getPluginScopedStore(attack).PSC22 && /\/trace\.axd(?:\b|_)/i.test(attack.href)) {
            const tracePattern = /<system\.web>\n?<trace localOnly=/i;
            const configPattern = /<configuration>/i;

            const matchTrace = responseBody.match(tracePattern);
            const matchConfig = responseBody.match(configPattern);

            if (matchTrace && matchConfig) {
                const context = this.extractContext(responseBody, matchTrace[0]);
                if (context) {
                    this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, `Exposed trace.axd Found, Context: "${context}"`);
                    this.getPluginScopedStore(attack).PSC22 = true;
                }
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == this.PSCvulnerabilityID || vulnID == this.HTAvulnerabilityID || vulnID == this.DPDvulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        }
    }
}

// vectors & matches ...
const PSFDVectors = [
    `Crossdomain.xml`,
    `crossdomain.xml`,
    `.htaccess`,
    `access_log`,
    `Access_log/`,
    `test.aspx`,
    `test.asp`,
    `server-info`,
    `error_log`,
    `htdocs`,
    `index.html`,
    `index.php`,
    `logs/`,
    `log/`,
    `log.txt`,
    `logs.txt`,
    `manual`,
    `php.ini`,
    `wp-admin/setup-config.php`,
    `config.php`,
    `web.config`,
    `debug.inc`,
    `web-console/ServerInfo.jsp`,
    `info.php`,
    `phpinfo.php`,
    `GetPhpInfo.php`,
    `getphpinfo.php`,
    `web.xml`,
    `phpmyadmin`,
    `localstart.asp`,
    // `backup.zip`,
    // `backup.rar`,
    `clientaccesspolicy.xml`,
    `Clientaccesspolicy.xml`,
    `laravel.log`, //Axis
    // `web.zip`,
    // `web.rar`
    `elmah`, //csTeam    
    `.git/config`,
    '.gitconfig',
    `loginmodulewebapi`,
    `manager`,
    `readme.html`,
    `Dockerfile`,
    `htaccess.txt`,
    `RequestHeaderExample`,
    `servlets/`,
    `_vti_inf.html`,
    `metrics`,
    `trace.axd`,
    `database-config.inc`,
    `config.inc`,
    `nginx.conf`,
    `nginx.conf.j2`
]

module.exports = PossibleSensitiveContent