const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

// Global configurations and patterns
const GLOBAL_CONFIG = {
    // Compiled regex patterns for better performance
    patterns: {
        excludeURL: /\.(?:txt|md|js|css|jpg|png|gif|ico|svg|woff|ttf|eot|map)$|\/(?:phpinfo\.php|info\.php|blog|docs?|learning|node_modules|vendor|assets|static)\//i,
        cdn: /cloudfront|akamai|fastly|cloudflare|incapsula/i,
        internal: /(?:192\.168|10\.|172\.(?:1[6-9]|2[0-9]|3[0-1]))\./i,
        secureCache: /max-age=\d+|no-cache|no-store|must-revalidate|proxy-revalidate|s-maxage=\d+|private|immutable/i
    },

    // Valid status codes for HHI detection - using Set for O(1) lookup
    allowedStatusCodes: new Set([200, 201, 202, 203, 204, 205, 206, 207, 208, 229, 301, 302, 303, 304, 305, 306, 307, 308]),

    // Security headers that might prevent exploitation - using Set for O(1) lookup
    securityHeaders: new Set([
        'strict-transport-security',
        'content-security-policy',
        'x-frame-options',
        'x-content-type-options'
    ]),

    // Cache-related headers - using Map for O(1) lookup
    cacheHeaders: new Map([
        ['control', 'cache-control'],
        ['pragma', 'pragma'],
        ['age', 'age'],
        ['xCache', 'x-cache'],
        ['xCacheLookup', 'x-cache-lookup'],
        ['vary', 'vary']
    ]),

    // Secure cache directives - using Set for O(1) lookup
    secureCacheDirectives: {
        exact: new Set(['no-cache', 'no-store', 'private', 'immutable', 'must-revalidate', 'proxy-revalidate']),
        maxAge: 300, // 5 minutes
    },

    // Combined headers for all proxy-related checks
    headers: {
        // Core proxy headers that can be used for bypass or exploitation
        proxy: {
            // Headers that can be used for proxy bypass
            bypass: new Set([
                'x-host',
                'x-original-host',
                'x-backend-server',
                'via',
                'x-forwarded-server',
                'x-forwarded-host',
                'x-redirect',
                'x-forwarded-for',
                'x-http-host-override',
                'forwarded-for',
                'client-ip',
                'x-real-ip',
                'x-forwarded-proto',
                'x-forwarded-port',
                'x-forwarded-ssl',
                'x-url-scheme',
                'x-http-method-override',
                'x-http-method',
                'x-method-override',
                'x-requested-with',
                'x-requested-by',
                'x-requested-for',
                'x-requested-from',
                'x-requested-via',
                'x-requested-via-ip',
                'x-requested-via-host',
                'x-requested-via-server',
                'x-requested-via-proto',
                'x-requested-via-port',
                'x-requested-via-ssl',
                'x-requested-via-method',
                'x-requested-via-url',
                'x-requested-via-path',
                'x-requested-via-query',
                'x-requested-via-fragment',
                'x-requested-via-origin',
                'x-requested-via-referer',
                'x-requested-via-user-agent',
                'x-requested-via-cookie',
                'x-requested-via-authorization',
                'x-requested-via-proxy-authorization',
                'x-requested-via-proxy-authenticate',
                'x-requested-via-proxy-connection',
                'x-requested-via-proxy-keep-alive'
            ]),

            // Headers that can be used for exploitation
            exploitation: new Set([
                'server',
                'via',
                'x-backend-server',
                'x-forwarded-server',
                'location',
                'x-authenticated-user',
                'x-forwarded-host',
                'x-original-host'
            ]),
        },

        // Security headers
        security: new Set([
            'strict-transport-security',
            'content-security-policy',
            'x-frame-options',
            'x-content-type-options'
        ]),

        // Cache headers
        cache: new Map([
            ['control', 'cache-control'],
            ['pragma', 'pragma'],
            ['age', 'age'],
            ['xCache', 'x-cache'],
            ['xCacheLookup', 'x-cache-lookup'],
            ['vary', 'vary']
        ])
    }
}

/**
 * Final Severity Recommendations
🔥 Critical: HHI + SSRF (internal access, metadata leaks)
🔥 High: HHI + Open Redirect, HHI + Cache Poisoning
⚠️ Medium-High: HHI + Proxy Bypass (if allowing unauthorized access)
⚠️ Medium: HHI + Information Disclosure (backend details, headers)
*/
class HostHeaderAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Core vulnerability IDs
        this.vulnerabilityID = 'ID-host-header-injection'
        this.HHICriticalVulnID = 'ID-hhi-critical'
        this.SSRFVulnID = 'ID-ssrf'
        this.WebCachePoisoning = 'ID-web-cache-poisoning'
        this.ProxyBypassVulnID = 'ID-proxy-bypass'
    }

    // Utility methods for validation
    validateRegexp(regexp) {
        return regexp && regexp instanceof RegExp
    }

    validateHeaders(headers) {
        return headers && typeof headers === 'object'
    }

    validateHeaderValue(header, value) {
        return header && value && typeof value === 'string' && value.length > 0
    }

    validateResponse(attack) {
        return attack.result?.resp?.httpResponse
    }

    getResponseHeaders(attack) {
        return this.validateResponse(attack) ?
            _.get(attack, 'result.resp.httpResponse.headers', {}) : {}
    }

    getResponseBody(attack) {
        return this.validateResponse(attack) ?
            _.get(attack, 'result.resp.httpResponse.body', '') : ''
    }

    getStatusCode(attack) {
        return this.validateResponse(attack) ?
            _.get(attack, 'result.resp.httpResponse.statusCode', '') : ''
    }

    getAttackVectors() {
        return maliciousHosts
    }

    getAttackableEvents() {
        return ['http-headers']
    }

    async performNetworkAttack(attack) {
        if (GLOBAL_CONFIG.patterns.excludeURL.test(attack.httpRequest.uri)) return false

        let pluginStorageScanScope = this.getPluginScopedStore(attack)
        if (pluginStorageScanScope.HHIVulnFound) return false

        try {
            const uri = new URL(attack.httpRequest.uri)
            const port = _.get(uri, 'port')
            const host = _.get(attack, 'httpRequest.headers.Host')

            if (host === attack.vector && port) {
                attack.httpRequest.headers.Host = `${attack.vector}:${port}`
            }

            const originalUri = new URL(attack.originalRequest.httpRequest.uri)
            let protocolChanged = false
            let portChanged = false

            // Check 1: Original request without any changes
            attack.httpRequest.uri = originalUri.toString()
            await super.performNetworkAttack(attack)

            // Check 2: Protocol modification (remove 's' from https)
            if (originalUri.protocol === 'https:') {
                protocolChanged = true
                originalUri.protocol = 'http:'
                attack.httpRequest.uri = originalUri.toString()
                await super.performNetworkAttack(attack)
            }
            if (protocolChanged) { // Reset protocol to original
                originalUri.protocol = 'https:'
                attack.httpRequest.uri = originalUri.toString()
            }

            // Check 3: Port modification (if port exists, try different port)
            if (originalUri.port) {
                portChanged = true
                const originalPort = originalUri.port
                // Try common alternative ports
                const alternativePorts = ['80', '443', '8080', '8443']
                for (const altPort of alternativePorts) {
                    if (altPort !== originalPort) {
                        originalUri.port = altPort
                        attack.httpRequest.uri = originalUri.toString()
                        await super.performNetworkAttack(attack)
                    }
                }
                // Reset port to original
                originalUri.port = originalPort
                attack.httpRequest.uri = originalUri.toString()
            }

            // Check 4: Path modification (add/remove trailing slash)
            if (originalUri.pathname.endsWith('/')) {
                originalUri.pathname = originalUri.pathname.slice(0, -1)
            } else {
                originalUri.pathname = originalUri.pathname + '/'
            }
            attack.httpRequest.uri = originalUri.toString()
            await super.performNetworkAttack(attack)

        } catch (e) {
            return false
        }
    }

    initParameterizedDelegate(parameterizedDelegate) {
        parameterizedDelegate.setOptions({
            headersToIterate: injectionHeaders
        })
    }

    processAttackResponse(attack) {
        if (!attack || attack.pluginName !== this.getName()) return
        let uri = attack.httpRequest.uri
        let oriUri = attack.originalRequest.httpRequest.uri
        const pluginStorage = this.getPluginScopedStore(attack)
        const statusCode = this.getStatusCode(attack)

        // Early validation checks
        if (!GLOBAL_CONFIG.allowedStatusCodes.has(statusCode)) return
        if (!attack.originalRequest?.httpRequest?.uri) return

        // Prepare attack vector for regex
        const vector = attack.vector?.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape regex special chars
        if (!vector) return

        const regexp = new RegExp(vector, 'i')
        if (regexp.test(attack.originalRequest.httpRequest.uri)) return

        // Check server header for CDN/internal indicators
        const serverHeader = _.get(this.getResponseHeaders(attack), 'server', '')
        if (serverHeader) {
            if (GLOBAL_CONFIG.patterns.cdn.test(serverHeader)) return
            if (GLOBAL_CONFIG.patterns.internal.test(serverHeader) && !attack.vector.includes('***********')) return
        }

        // Process different types of vulnerabilities
        if (!pluginStorage.HHIVulnFound) {
            this.checkResponseHeaders(attack, regexp)
        }

        if (!pluginStorage.SSRFVulnFound) {
            this.checkSSRF(attack)
        }
    }

    checkResponseHeaders(attack, regexp) {
        if (!this.validateRegexp(regexp)) return

        const responseHeaders = this.getResponseHeaders(attack)
        if (!this.validateHeaders(responseHeaders)) return

        const pluginStorage = this.getPluginScopedStore(attack)

        // Check redirects and location headers first
        if (!pluginStorage.HHIVulnFound && !pluginStorage.HHICriticalVulnFound) {
            // Precheck for valid redirect information
            const redirectUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')

            // Only proceed if we have valid redirect information
            if (regexp.test(redirectUri)) {
                this.checkRedirects(attack, regexp)
            }
        }
        if (!pluginStorage.HHIVulnFound) {
            const locationHeader = responseHeaders['location'] || responseHeaders['Location']
            if (regexp.test(locationHeader)) {
                this.checkLocationHeader(attack, responseHeaders)
            }
        }

        // Process each vulnerable header
        for (const header of vulnerableHeaders) {
            const headerValue = responseHeaders[header]
            if (!this.validateHeaderValue(header, headerValue)) continue

            // Check for cache poisoning in Set-Cookie headers
            if (header.toLowerCase() === 'set-cookie' && !pluginStorage.WebCachePoisoningVulnFound) {
                this.checkSetCookieHeaders(attack, headerValue)
                continue
            }

            // Check for host header injection
            if (!pluginStorage.HHIVulnFound && regexp.test(headerValue)) {
                this.checkHostHeaderInjection(attack, header, headerValue)
            }

            // Check for proxy bypass
            if (!pluginStorage.ProxyBypassVulnFound &&
                GLOBAL_CONFIG.headers.proxy.bypass.has(header.toLowerCase()) &&
                regexp.test(headerValue)) {
                this.checkProxyBypass(attack, header, headerValue, regexp)
            }
        }
    }

    checkSSRF(attack) {
        if (!attack || !attack.vector) return

        const responseHeaders = _.get(attack, 'result.resp.httpResponse.headers', {})
        const responseBody = _.get(attack, 'result.resp.httpResponse.body', '')
        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')

        if (!responseBody) return

        // SSRF indicators categorized by type and severity
        const ssrfIndicators = {
            // Cloud Metadata Services (Critical)
            aws: {
                patterns: [
                    /\/latest\/meta-data\//i,
                    /\/latest\/dynamic\//i,
                    /\/latest\/user-data\//i,
                    /"Code"\s*:\s*"Success"/i,
                    /"accountId"\s*:/i,
                    /"instanceId"\s*:/i,
                    /"region"\s*:/i,
                    /"availabilityZone"\s*:/i,
                    /"instanceType"\s*:/i,
                    /"amiId"\s*:/i
                ],
                evidence: "AWS metadata service response detected",
                severity: 'Critical'
            },
            gcp: {
                patterns: [
                    /\/computeMetadata\/v1\//i,
                    /\/computeMetadata\/v1beta1\//i,
                    /"projectId"\s*:/i,
                    /"zone"\s*:/i,
                    /"instanceId"\s*:/i,
                    /"machineType"\s*:/i,
                    /"serviceAccounts"\s*:/i
                ],
                evidence: "GCP metadata service response detected",
                severity: 'Critical'
            },
            // Internal Services (High)
            internal: {
                patterns: [
                    /EC2Metadata/i,
                    /InstanceMetadata/i,
                    /MetadataService/i,
                    /"internal"\s*:\s*true/i,
                    /"private"\s*:\s*true/i,
                    /"local"\s*:\s*true/i,
                    /"localhost"\s*:/i,
                    /"127\.0\.0\.1"/i,
                    /"::1"/i
                ],
                evidence: "Internal service response detected",
                severity: 'High'
            },
            // Common Error Messages (Medium)
            errors: {
                patterns: [
                    /EC2MetadataError/i,
                    /MetadataServiceError/i,
                    /InstanceMetadataError/i,
                    /"error"\s*:\s*"internal"/i,
                    /"error"\s*:\s*"private"/i,
                    /"error"\s*:\s*"local"/i
                ],
                evidence: "Internal service error response detected",
                severity: 'Medium'
            }
        }

        // Check headers for SSRF indicators first
        const ssrfHeaders = ['server', 'via', 'x-backend-server', 'x-powered-by', 'x-forwarded-server']
        for (const header of ssrfHeaders) {
            if (!header) continue
            const headerValue = responseHeaders[header]
            if (!headerValue) continue

            // Check for internal IPs or metadata service with context
            if (GLOBAL_CONFIG.patterns.internal.test(headerValue) ||
                headerValue.includes('metadata') ||
                headerValue.includes('internal') ||
                headerValue.includes('localhost') ||
                headerValue.includes('127.0.0.1') ||
                headerValue.includes('***************')) {

                // Additional context check to reduce FPs
                const hasContext = Object.values(ssrfIndicators).some(indicator =>
                    indicator.patterns.some(pattern => pattern.test(responseBody))
                )

                if (hasContext) {
                    this.reportSSRF(attack, `SSRF detected in ${header} header with supporting evidence: ${headerValue}`)
                    return
                }
            }
        }

        // Look for URLs in response body that match our attack vector
        try {
            const validSSRF = new RegExp('https?://' + attack.vector, 'i')
            const ssrfMatches = responseBody.match(validSSRF)

            if (ssrfMatches) {
                // Check each matched URL
                for (const match of ssrfMatches) {
                    try {
                        const url = new URL(match)
                        if (url.hostname === attack.vector) {
                            let evidence = []
                            let severity = 'Medium'
                            let hasCriticalIndicator = false

                            // Check for cloud metadata services first with precise patterns
                            if (attack.vector === '***************') {
                                for (const pattern of ssrfIndicators.aws.patterns) {
                                    if (pattern.test(responseBody)) {
                                        evidence.push(ssrfIndicators.aws.evidence)
                                        severity = 'Critical'
                                        hasCriticalIndicator = true
                                        break
                                    }
                                }
                            } else if (attack.vector === 'metadata.google.internal') {
                                for (const pattern of ssrfIndicators.gcp.patterns) {
                                    if (pattern.test(responseBody)) {
                                        evidence.push(ssrfIndicators.gcp.evidence)
                                        severity = 'Critical'
                                        hasCriticalIndicator = true
                                        break
                                    }
                                }
                            }

                            // If no critical indicators found, check other categories
                            if (!hasCriticalIndicator) {
                                for (const [category, indicator] of Object.entries(ssrfIndicators)) {
                                    // Skip if we already found critical indicators
                                    if (hasCriticalIndicator && indicator.severity !== 'Critical') continue

                                    for (const pattern of indicator.patterns) {
                                        if (pattern.test(responseBody)) {
                                            evidence.push(`${category}: ${indicator.evidence}`)
                                            if (indicator.severity === 'Critical') {
                                                severity = 'Critical'
                                                hasCriticalIndicator = true
                                            } else if (indicator.severity === 'High' && severity !== 'Critical') {
                                                severity = 'High'
                                            }
                                            break
                                        }
                                    }
                                }
                            }

                            // Additional context checks to reduce FPs
                            const hasMultipleIndicators = evidence.length > 1
                            const hasStatusCodeMatch = statusCode === 200 || statusCode === 403
                            const hasHeaderMatch = Object.values(responseHeaders).some(h =>
                                h && h.includes(attack.vector)
                            )

                            // Report only if we have strong evidence
                            if (evidence.length > 0 && (hasMultipleIndicators || hasStatusCodeMatch || hasHeaderMatch)) {
                                const result = `Potential SSRF detected (${severity}):\n` +
                                    `Reflected Host: ${match}\n` +
                                    `Evidence:\n- ${evidence.join('\n- ')}\n` +
                                    `Response Snippet: ${responseBody.substring(0, 200)}...`
                                this.reportSSRF(attack, result)
                                return
                            }
                        }
                    } catch (e) {
                        continue
                    }
                }
            }
        } catch (e) {
            return
        }

        // Check for specific status codes that might indicate SSRF
        if (statusCode === 403 || statusCode === 401) {
            const hasInternalIndicators = Object.values(responseHeaders).some(headerValue =>
                headerValue && (
                    GLOBAL_CONFIG.patterns.internal.test(headerValue) ||
                    headerValue.includes('internal') ||
                    headerValue.includes('private') ||
                    headerValue.includes('localhost') ||
                    headerValue.includes('127.0.0.1')
                )
            )

            // Additional context check to reduce FPs
            const hasBodyIndicators = Object.values(ssrfIndicators).some(indicator =>
                indicator.patterns.some(pattern => pattern.test(responseBody))
            )

            if (hasInternalIndicators && hasBodyIndicators) {
                this.reportSSRF(attack, `Potential SSRF detected (status code ${statusCode}) with internal resource indicators`)
            }
        }
    }

    checkRedirects(attack, regexp) {
        if (!attack || !this.validateRegexp(regexp)) return

        const redirectUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
        const redirectStatusCode = _.get(attack, 'result.resp.httpResponse.redirects[0].statusCode', '')

        if (!redirectUri || !regexp.test(redirectUri)) return

        try {
            const redirectUrl = new URL(redirectUri)
            if (redirectUrl.hostname === attack.vector) {
                const originalUrl = new URL(attack.originalRequest.httpRequest.uri)
                const currentUrl = new URL(attack.httpRequest.uri)

                // Check if protocol was changed in performNetworkAttack
                const isProtocolChange = redirectUrl.protocol !== originalUrl.protocol
                const isPortChange = redirectUrl.port !== originalUrl.port
                const hasSecurityHeaders = this.hasStrongSecurityHeaders(attack)

                if (!this.getPluginScopedStore(attack).HHICriticalVulnFound && (isProtocolChange || isPortChange)) {
                    this.reportCriticalHHI(attack, `Critical Host Header Injection detected: \n` +
                        `Redirected URL: ${redirectUri}; \n` +
                        `Status Code: ${redirectStatusCode}; \n` +
                        `Protocol/Port Change: ${isProtocolChange ? 'Yes' : 'No'}/${isPortChange ? 'Yes' : 'No'}; \n` +
                        `Original Protocol: ${originalUrl.protocol}; \n` +
                        `Current Protocol: ${currentUrl.protocol}; \n` +
                        `Injected value: ${attack.vector}; \n` +
                        `Original URL: ${attack.originalRequest.httpRequest.uri}; \n` +
                        `Risk: Critical - Injected Host header reflected in redirect. Protocol/Port manipulation possible.`)
                } else if (hasSecurityHeaders) {
                    this.reportHHI(attack, `Potential Host Header Injection detected (Low Risk): \n` +
                        `Redirected URL: ${redirectUri}; \n` +
                        `Status Code: ${redirectStatusCode}; \n` +
                        `Injected value: ${attack.vector}; \n` +
                        `Original URL: ${attack.originalRequest.httpRequest.uri}; \n` +
                        `Security headers are present but redirect should be reviewed`)
                } else {
                    this.reportHHI(attack, `Host Header Injection detected: \n` +
                        `Redirected URL: ${redirectUri}; \n` +
                        `Status Code: ${redirectStatusCode}; \n` +
                        `Injected value: ${attack.vector}; \n` +
                        `Original URL: ${attack.originalRequest.httpRequest.uri}; \n` +
                        `Risk: Medium - Injected Host header reflected in redirect`)
                }
            }
        } catch (e) {
            return
        }
    }

    checkLocationHeader(attack, responseHeaders) {
        if (!attack || !responseHeaders) return
        if (this.getPluginScopedStore(attack).HHIVulnFound) return

        const locationHeader = responseHeaders['location'] || responseHeaders['Location']
        if (!locationHeader) return

        try {
            // Only check full URLs in location header
            if (!locationHeader.startsWith('http')) return

            const locationUrl = new URL(locationHeader)
            if (locationUrl.hostname !== attack.vector) return

            const originalUrl = new URL(attack.originalRequest.httpRequest.uri)
            const isProtocolChange = locationUrl.protocol !== originalUrl.protocol
            const isPortChange = locationUrl.port !== originalUrl.port
            const hasSecurityHeaders = this.hasStrongSecurityHeaders(attack)

            if (isProtocolChange || isPortChange) {
                this.reportHHI(attack, `Critical Host Header Injection detected in location header:\n` +
                    `Location: ${locationHeader}\n` +
                    `Protocol/Port Change: ${isProtocolChange ? 'Yes' : 'No'}/${isPortChange ? 'Yes' : 'No'}`)
            } else if (hasSecurityHeaders) {
                this.reportHHI(attack, `Potential Host Header Injection detected in location header (Low Risk):\n` +
                    `Location: ${locationHeader}\n` +
                    `Security headers are present but location header should be reviewed`)
            } else {
                this.reportHHI(attack, `Host Header Injection detected in location header: ${locationHeader}`)
            }
        } catch (e) {
            return
        }
    }

    checkSetCookieHeaders(attack, cookieHeaders) {
        if (!attack || !cookieHeaders) return

        const headers = _.get(attack, 'result.resp.httpResponse.headers', {})
        const cookieArray = Array.isArray(cookieHeaders) ? cookieHeaders : [cookieHeaders]

        for (const cookieHeader of cookieArray) {
            if (!cookieHeader || cookieHeader.length === 0) continue

            if (cookieHeader.includes('domain=')) {
                const cookieDomain = /domain=.+?(?:;|$)/i
                const cookieDomainMatch = cookieHeader.match(cookieDomain)

                if (cookieDomainMatch && cookieDomainMatch[0].includes(attack.vector)) {
                    const isVulnerable = this.checkCacheVulnerability(attack)
                    const secureCookie = cookieHeader.toLowerCase().includes('secure') ||
                        cookieHeader.toLowerCase().includes('httponly')

                    if (secureCookie && isVulnerable) {
                        this.reportCachePoisoning(attack, `Potential Cache Poisoning detected in Set-Cookie header (Low Risk): ${cookieHeader}\n` +
                            `Cache-Control: ${headers['cache-control'] || ''}\n` +
                            `Cookie has secure attributes but may still be cached`)
                    } else if (isVulnerable) {
                        this.reportCachePoisoning(attack, `Cache Poisoning detected in Set-Cookie header: ${cookieHeader}\n` +
                            `Cache-Control: ${headers['cache-control'] || ''}\n` +
                            `Cookie lacks secure attributes and may be cached`)
                    }
                }
            }
        }
    }

    checkHostHeaderInjection(attack, header, headerValue) {
        if (!attack || !header || !headerValue) return

        const hasSecurityHeaders = this.hasStrongSecurityHeaders(attack)

        if (hasSecurityHeaders) {
            this.reportHHI(attack, `Potential Host Header Injection detected in ${header} header (Low Risk):\n` +
                `Header: ${headerValue}\n` +
                `Security headers are present but header should be reviewed`)
        } else {
            this.reportHHI(attack, `Host Header Injection detected in ${header} header: ${headerValue}`)
        }
    }

    isProxyBypassExploitable(attack, header, headerValue) {
        if (!this.validateResponse(attack)) return false

        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Check for payload in proxy-related headers
        const headerPayloadFound = Array.from(GLOBAL_CONFIG.headers.proxy.exploitation).some(headerName => {
            const headerValue = responseHeaders[headerName]
            return headerValue && headerValue.includes(attack.vector)
        })

        // Check for payload in response body
        const bodyPayloadIndicators = [
            `server: ${attack.vector}`,
            `backend: ${attack.vector}`,
            `host: ${attack.vector}`,
            `user: ${attack.vector}`,
            `connected to ${attack.vector}`,
            `welcome to ${attack.vector}`,
            `authenticated as ${attack.vector}`
        ]

        const bodyPayloadFound = bodyPayloadIndicators.some(indicator =>
            responseBody && responseBody.toLowerCase().includes(indicator.toLowerCase())
        )

        // Check for specific exploitation scenarios
        const exploitationScenarios = {
            serverIdentification: this.checkServerIdentification(attack, headerValue),
            authenticationBypass: this.checkAuthenticationBypass(attack, headerValue),
            routingBypass: this.checkRoutingBypass(attack, headerValue),
            proxyChainManipulation: this.checkProxyChainManipulation(attack, headerValue)
        }

        return headerPayloadFound || bodyPayloadFound || Object.values(exploitationScenarios).some(result => result)
    }

    checkServerIdentification(attack, headerValue) {
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Check headers
        const serverHeaders = ['server', 'x-backend-server', 'via']
        const headerMatch = serverHeaders.some(header =>
            responseHeaders[header]?.includes(headerValue)
        )

        // Check body
        const bodyMatch = responseBody?.toLowerCase().includes(`server: ${headerValue.toLowerCase()}`) ||
            responseBody?.toLowerCase().includes(`backend: ${headerValue.toLowerCase()}`)

        return headerMatch || bodyMatch
    }

    checkAuthenticationBypass(attack, headerValue) {
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Check headers
        const authHeaders = ['x-authenticated-user', 'x-user', 'x-auth-user']
        const headerMatch = authHeaders.some(header =>
            responseHeaders[header]?.includes(headerValue)
        )

        // Check body
        const bodyMatch = responseBody?.toLowerCase().includes(`user: ${headerValue.toLowerCase()}`) ||
            responseBody?.toLowerCase().includes(`authenticated as ${headerValue.toLowerCase()}`)

        return headerMatch || bodyMatch
    }

    checkRoutingBypass(attack, headerValue) {
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Check headers
        const routingHeaders = ['location', 'x-forwarded-host', 'x-original-host']
        const headerMatch = routingHeaders.some(header =>
            responseHeaders[header]?.includes(headerValue)
        )

        // Check body
        const bodyMatch = responseBody?.toLowerCase().includes(`redirecting to ${headerValue.toLowerCase()}`) ||
            responseBody?.toLowerCase().includes(`forwarding to ${headerValue.toLowerCase()}`)

        return headerMatch || bodyMatch
    }

    checkProxyChainManipulation(attack, headerValue) {
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Check headers
        const headerMatch = Array.from(GLOBAL_CONFIG.headers.proxy.exploitation).some(header =>
            responseHeaders[header]?.includes(headerValue)
        )

        // Check body
        const bodyMatch = responseBody?.toLowerCase().includes(`via: ${headerValue.toLowerCase()}`) ||
            responseBody?.toLowerCase().includes(`forwarded: ${headerValue.toLowerCase()}`)

        return headerMatch || bodyMatch
    }

    checkProxyBypass(attack, header, headerValue, regexp) {
        if (!attack || !header || !headerValue || !this.validateRegexp(regexp)) return

        // Check if this is a proxy bypass header
        const isProxyBypassHeader = GLOBAL_CONFIG.headers.proxy.bypass.has(header.toLowerCase())
        if (!isProxyBypassHeader) return

        // Get all proxy-related headers that are present in the response
        const allProxyHeaders = Array.from(GLOBAL_CONFIG.headers.proxy.bypass)
            .filter(h => _.get(attack, `result.resp.httpResponse.headers[${h}]`))
            .map(h => _.get(attack, `result.resp.httpResponse.headers[${h}]`))

        // Check if the attack vector appears in any proxy bypass headers
        const hasProxyBypass = allProxyHeaders.some(h => regexp.test(h))
        if (!hasProxyBypass) return

        // Check for security controls
        const securityControls = this.analyzeSecurityControls(attack)

        // Check if the header is actually being used in a way that could lead to exploitation
        const isHeaderExploitable = this.isHeaderExploitable(attack, header, headerValue)

        // Additional check for proxy bypass specific exploitation
        const isProxyBypassExploitable = this.isProxyBypassExploitable(attack, header, headerValue)

        // Only report if we have a real vulnerability
        if (isProxyBypassExploitable) {
            const message = this.generateProxyBypassMessage(attack, header, headerValue, securityControls)
            this.reportProxyBypass(attack, message)
        }
    }

    generateProxyBypassMessage(attack, header, headerValue, securityControls) {
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        let message = `Proxy Bypass detected in ${header} header: ${headerValue}\n\n`

        // Add header-based findings
        const headerFindings = []
        if (responseHeaders['server']?.includes(headerValue)) {
            headerFindings.push('Server identification affected')
        }
        if (responseHeaders['via']?.includes(headerValue)) {
            headerFindings.push('Proxy chain affected')
        }
        if (responseHeaders['location']?.includes(headerValue)) {
            headerFindings.push('Routing affected')
        }
        if (responseHeaders['x-authenticated-user']?.includes(headerValue)) {
            headerFindings.push('Authentication affected')
        }

        if (headerFindings.length > 0) {
            message += 'Header-based findings:\n'
            headerFindings.forEach(finding => message += `- ${finding}\n`)
        }

        // Add body-based findings
        const bodyFindings = []
        if (responseBody?.includes(`server: ${headerValue}`)) {
            bodyFindings.push('Server information exposed')
        }
        if (responseBody?.includes(`user: ${headerValue}`)) {
            bodyFindings.push('User information exposed')
        }
        if (responseBody?.includes(`backend: ${headerValue}`)) {
            bodyFindings.push('Backend information exposed')
        }

        if (bodyFindings.length > 0) {
            message += '\nBody-based findings:\n'
            bodyFindings.forEach(finding => message += `- ${finding}\n`)
        }

        // Add security controls information
        if (securityControls.score > 0) {
            message += '\nSecurity controls present:\n'
            securityControls.details.forEach(detail => message += `- ${detail}\n`)
        }

        return message
    }

    analyzeSecurityControls(attack) {
        if (!this.validateResponse(attack)) return { score: 0, details: [] }

        const responseHeaders = this.getResponseHeaders(attack)
        const details = []
        let score = 0

        // Check CSP
        const csp = responseHeaders['content-security-policy'] || ''
        if (csp) {
            const cspScore = this.hasStrongCSP(attack) ? 2 : 1
            score += cspScore
            details.push(`CSP: ${cspScore}/2`)
        }

        // Check Cache Control
        const cacheControl = responseHeaders['cache-control'] || ''
        if (cacheControl) {
            const cacheScore = this.hasStrongCacheControl(attack) ? 2 : 1
            score += cacheScore
            details.push(`Cache Control: ${cacheScore}/2`)
        }

        // Check HSTS
        const hsts = responseHeaders['strict-transport-security'] || ''
        if (hsts) {
            const hstsScore = hsts.includes('max-age=') && hsts.includes('includeSubDomains') ? 2 : 1
            score += hstsScore
            details.push(`HSTS: ${hstsScore}/2`)
        }

        // Check Cookies
        const cookies = responseHeaders['set-cookie']
        if (cookies) {
            const cookieScore = this.hasSecureCookies(attack) ? 2 : 1
            score += cookieScore
            details.push(`Cookies: ${cookieScore}/2`)
        }

        return { score, details }
    }

    assessProxyBypassRisk(securityControls, isHeaderExploitable, isProxyBypassExploitable, attack, header, headerValue) {
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Calculate risk score
        let riskScore = 0
        const riskFactors = []

        // Security controls (max 8 points)
        riskScore += securityControls.score

        // Header exploitability (max 4 points)
        if (isHeaderExploitable) {
            riskScore += 2
            riskFactors.push('Header is used in exploitable way')
        }
        if (isProxyBypassExploitable) {
            riskScore += 2
            riskFactors.push('Proxy bypass is possible')
        }

        // Additional risk factors
        if (responseHeaders['server']?.includes(headerValue)) {
            riskScore += 1
            riskFactors.push('Header affects server identification')
        }
        if (responseHeaders['via']?.includes(headerValue)) {
            riskScore += 1
            riskFactors.push('Header affects proxy chain')
        }
        if (responseBody?.includes(headerValue)) {
            riskScore += 1
            riskFactors.push('Header reflected in response body')
        }

        // Determine if we should report and at what severity
        const shouldReport = riskScore >= 4 // Minimum threshold for reporting
        let severity = 'Low'
        if (riskScore >= 8) severity = 'High'
        else if (riskScore >= 6) severity = 'Medium'

        // Construct message
        const message = `Proxy Bypass ${severity} Risk detected in ${header} header: ${headerValue}\n` +
            `Risk Score: ${riskScore}/12\n` +
            `Security Controls: ${securityControls.details.join(', ')}\n` +
            `Risk Factors:\n- ${riskFactors.join('\n- ')}`

        return { shouldReport, message, severity }
    }

    hasStrongSecurityHeaders(attack) {
        if (!this.validateResponse(attack)) return false
        const responseHeaders = this.getResponseHeaders(attack)

        // Check for essential security headers
        const requiredHeaders = {
            'strict-transport-security': (value) => value.includes('max-age=') && value.includes('includeSubDomains'),
            'x-content-type-options': (value) => value.toLowerCase() === 'nosniff',
            'content-security-policy': (value) => this.hasStrongCSP(attack),
            'cache-control': (value) => this.hasStrongCacheControl(attack)
        }

        // Check if all required headers are present and properly configured
        return Object.entries(requiredHeaders).every(([header, validator]) => {
            const value = responseHeaders[header.toLowerCase()]
            return value && validator(value)
        })
    }

    hasStrongCSP(attack) {
        if (!this.validateResponse(attack)) return false
        const responseHeaders = this.getResponseHeaders(attack)
        const csp = responseHeaders['content-security-policy'] || ''

        // Check for strong CSP directives that would prevent exploitation
        const strongDirectives = [
            "default-src 'self'",
            "script-src 'self'",
            "frame-ancestors 'none'",
            "form-action 'self'",
            "base-uri 'self'",
            "block-all-mixed-content",
            "upgrade-insecure-requests"
        ]

        return strongDirectives.every(directive =>
            csp.toLowerCase().includes(directive.toLowerCase())
        )
    }

    hasStrongCacheControl(attack) {
        if (!this.validateResponse(attack)) return false
        const responseHeaders = this.getResponseHeaders(attack)
        const cacheControl = responseHeaders['cache-control'] || ''

        // Check for strong cache control directives
        const strongDirectives = [
            'no-cache',
            'no-store',
            'must-revalidate',
            'private',
            'max-age=0'
        ]

        return strongDirectives.every(directive =>
            cacheControl.toLowerCase().includes(directive.toLowerCase())
        )
    }

    hasSecureCookies(attack) {
        if (!this.validateResponse(attack)) return false
        const responseHeaders = this.getResponseHeaders(attack)
        const setCookie = responseHeaders['set-cookie']

        if (!setCookie) return true // No cookies is also secure

        const cookieArray = Array.isArray(setCookie) ? setCookie : [setCookie]

        // Check if all cookies have secure attributes
        return cookieArray.every(cookie => {
            const lowerCookie = cookie.toLowerCase()
            return lowerCookie.includes('secure') &&
                lowerCookie.includes('httponly') &&
                (lowerCookie.includes('samesite=strict') || lowerCookie.includes('samesite=lax'))
        })
    }

    isHeaderExploitable(attack, header, headerValue) {
        if (!this.validateResponse(attack)) return false

        // Check if the header is being used in a way that could lead to exploitation
        const responseHeaders = this.getResponseHeaders(attack)
        const responseBody = this.getResponseBody(attack)

        // Check if the header is being used in redirects
        const locationHeader = responseHeaders['location'] || ''
        if (locationHeader && locationHeader.includes(headerValue)) {
            return true
        }

        // Check if the header is being used in cookies
        const setCookieHeader = responseHeaders['set-cookie'] || ''
        if (setCookieHeader && setCookieHeader.includes(headerValue)) {
            return true
        }

        // Check if the header is being used in the response body
        if (responseBody && responseBody.includes(headerValue)) {
            return true
        }

        // Check if the header is being used in other security-sensitive headers
        const securityHeaders = ['content-security-policy', 'x-frame-options', 'x-content-type-options']
        for (const securityHeader of securityHeaders) {
            if (responseHeaders[securityHeader] && responseHeaders[securityHeader].includes(headerValue)) {
                return true
            }
        }

        return false
    }

    checkCacheVulnerability(attack) {
        if (!attack) return false

        let isVulnerable = true
        const headers = _.get(attack, 'result.resp.httpResponse.headers', {})

        // Check cache control headers
        for (const [key, headerName] of GLOBAL_CONFIG.headers.cache) {
            const headerValue = headers[headerName]
            if (!headerValue) continue

            if (key === 'control') {
                const directives = headerValue.split(',').map(d => d.trim().toLowerCase())
                const hasSecureDirective = directives.some(d => {
                    if (GLOBAL_CONFIG.secureCacheDirectives.exact.has(d)) return true

                    if (d.startsWith('max-age=') || d.startsWith('s-maxage=')) {
                        const maxAge = parseInt(d.split('=')[1])
                        return maxAge < GLOBAL_CONFIG.secureCacheDirectives.maxAge
                    }

                    return false
                })

                if (hasSecureDirective) {
                    isVulnerable = false
                    break
                }
            } else if (key === 'pragma' && headerValue.toLowerCase().includes('no-cache')) {
                isVulnerable = false
                break
            } else if (key === 'vary') {
                const varyHeaders = headerValue.toLowerCase().split(',').map(h => h.trim())
                if (varyHeaders.includes('host') || varyHeaders.includes('cookie')) {
                    isVulnerable = false
                    break
                }
            }
        }

        return isVulnerable
    }

    reportHHI(attack, result) {
        if (!attack || !result) return

        // Validate the attack object
        if (!this.validateResponse(attack)) return

        // Check for false positives
        const responseHeaders = this.getResponseHeaders(attack)
        const hasStrongSecurity = this.hasStrongSecurityHeaders(attack)
        const hasStrongCSP = this.hasStrongCSP(attack)
        const hasStrongCache = this.hasStrongCacheControl(attack)

        // Build the final message for reporting
        let detailedResult = result // Start with the original evidence
        // If strong security controls are present, clarify risk to avoid confusion
        if (hasStrongSecurity && hasStrongCSP && hasStrongCache) {
            detailedResult = `Potential Host Header Injection (Low Risk):\n` + detailedResult;
        }
        // Add the vulnerability to the result
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, detailedResult)
        this.getPluginScopedStore(attack).HHIVulnFound = true
    }

    reportSSRF(attack, result) {
        if (!attack || !result) return

        // Validate the attack object
        if (!this.validateResponse(attack)) return

        // Check for false positives
        const responseBody = this.getResponseBody(attack)
        const responseHeaders = this.getResponseHeaders(attack)

        // Additional validation for SSRF
        const hasInternalIndicators = Object.values(responseHeaders).some(headerValue =>
            headerValue && (
                GLOBAL_CONFIG.patterns.internal.test(headerValue) ||
                headerValue.includes('internal') ||
                headerValue.includes('private') ||
                headerValue.includes('localhost') ||
                headerValue.includes('127.0.0.1')
            )
        )

        // Only report if we have strong evidence
        if (!hasInternalIndicators && !responseBody) return

        // Build the final message for reporting
        let detailedResult = result // Start with the original evidence
        // (Add more context here if needed in the future)
        this.addVulnerabilitytoResult(attack, this.SSRFVulnID, detailedResult)
        this.getPluginScopedStore(attack).SSRFVulnFound = true
    }

    reportCachePoisoning(attack, result) {
        if (!attack || !result) return

        // Validate the attack object
        if (!this.validateResponse(attack)) return

        // Check for false positives
        const isVulnerable = this.checkCacheVulnerability(attack)
        if (!isVulnerable) return

        // Additional validation
        const responseHeaders = this.getResponseHeaders(attack)
        const hasSecureCookies = this.hasSecureCookies(attack)
        const hasStrongCache = this.hasStrongCacheControl(attack)

        // Build the final message for reporting
        let detailedResult = result // Start with the original evidence
        // If strong security controls are present, clarify risk
        if (hasSecureCookies && hasStrongCache) {
            detailedResult = `Potential Cache Poisoning (Low Risk):\n` + detailedResult;
        }
        this.addVulnerabilitytoResult(attack, this.WebCachePoisoning, detailedResult)
        this.getPluginScopedStore(attack).WebCachePoisoningVulnFound = true
    }

    reportProxyBypass(attack, result) {
        if (!attack || !result) return

        // Validate the attack object
        if (!this.validateResponse(attack)) return

        // Check for false positives
        const responseHeaders = this.getResponseHeaders(attack)
        const hasStrongSecurity = this.hasStrongSecurityHeaders(attack)
        const isHeaderExploitable = this.isHeaderExploitable(attack, '', '')
        const isProxyBypassExploitable = this.isProxyBypassExploitable(attack, '', '')

        // Only report if we have a real vulnerability
        if (!isHeaderExploitable && !isProxyBypassExploitable) return

        // Build the final message for reporting
        let detailedResult = result // Start with the original evidence
        // If strong security controls are present, clarify risk
        if (hasStrongSecurity) {
            detailedResult = `Potential Proxy Bypass (Low Risk):\n` + detailedResult;
        }
        this.addVulnerabilitytoResult(attack, this.ProxyBypassVulnID, detailedResult)
        this.getPluginScopedStore(attack).ProxyBypassVulnFound = true
    }

    reportCriticalHHI(attack, result) {
        if (!attack || !result) return

        // Validate the attack object
        if (!this.validateResponse(attack)) return

        // Add the vulnerability to the result
        this.addVulnerabilitytoResult(attack, this.HHICriticalVulnID, result)
        this.getPluginScopedStore(attack).HHICriticalVulnFound = true
    }

    onAutoPOC(attack, vulnID) {
        if (!attack || !vulnID || vulnID !== this.vulnerabilityID) return

        super.onAutoPOC(attack, vulnID)

        // Add POC annotations
        try {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', 'param', [attack.href])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', 'param', [attack.href])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', 'param', ['Origin', 'Host'])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', 'param', ['statusCode'])
        } catch (error) {
            // Log error but don't throw
            console.error('Error adding POC annotations:', error)
        }
    }
}

// Headers to test for injection
const injectionHeaders = [
    'Host',
    'X-Forwarded-Host',
    'X-Host',
    'Forwarded',
    'Referer',
    'Origin',
    'X-Forwarded-For',
    'X-Original-Host',
    'X-Rewrite-URL',
    'X-Forwarded-Server',
    'X-ProxyUser-Ip',
    'True-Client-IP',
    'Forwarded-For',
    'Client-IP',
    'X-Real-IP',
    'Base-URL',
    'Request-URL',
    'X-Http-Destinationurl',
    'Proxy-Authorization',
    'CF-Connecting-IP',
    'WL-Proxy-Client-IP',
    'X-ProxyUser-Ip',
    'X-HTTP-Host-Override'
]

// Response headers to check for injected host
const vulnerableHeaders = [
    'location',
    'content-location',
    'set-cookie',
    'referer',
    'x-redirect',
    'x-host',
    'x-original-host',
    'x-backend-server',
    'x-powered-by',
    'server',
    'via',
    'x-forwarded-server',
    'x-forwarded-host',
    'x-forwarded-for',
    'x-http-host-override',
    'forwarded-for',
    'client-ip',
    'x-real-ip'
]

// Malicious hosts for injection
const maliciousHosts = [
    'testfire.net',
    '127.0.0.1',  // SSRF testing
    'localhost',
    '0.0.0.0',
    '***************',  // AWS metadata service
    '[::1]',  // IPv6 localhost
    '***********',  // Common private network
    'webhook.site/haiku-custom-url',  // External callback check
    'haiku.internal',  // Simulating an internal hostname
    'host.example.com',  // Custom host to test misconfigurations
    'metadata.google.internal'
]

module.exports = HostHeaderAttack