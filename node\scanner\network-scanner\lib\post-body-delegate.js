const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = 'PostBody'

// Delegate that can iterate POSTs body and update content
class PostBodyDelegate extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    static isPostRequest(httpRequest) {
        return httpRequest.method == 'POST'
    }


    /**
     * @param {request} request the request whose body we are  tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     * @param {object} options config related to plugin
     */
    constructor(request, scanStore,options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType,options)
        this.postBody = request.httpRequest.body
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    * getIterator() {
        // starting iteration, keep track of original post body.
        this.originalPostbody = _.cloneDeep(this.postBody)

            yield {
                name: 'body',
                val: this.postBody,
                resetRequired: false
            }
    }

    modifyParam(param, value) {
        this.postBody = value
    }

    // get the modified request
    getHttpRequest(encoding) {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)
        req.body = this.postBody
        return req
    }

    // reset post body
    reset() {
        this.postBody = _.cloneDeep(this.originalPostbody)
    }
}

module.exports = PostBodyDelegate