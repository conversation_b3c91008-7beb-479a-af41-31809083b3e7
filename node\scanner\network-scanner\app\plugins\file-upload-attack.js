const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
const crypto = require('crypto')
const fs = require('fs')
const path = require('path')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const caseless = require('caseless')

class FileUploadAttack extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config);
        // Basic unrestricted file upload - common extensions
        this.UnrestrictFileUploadVuln = 'ID-unrestricted-file-upload-vuln';
        // Bypass blacklist using uncommon extensions
        this.BlacklistBypassVuln = 'ID-blacklist-bypass-file-upload-vuln';
        // Double extension to bypass validation
        this.DoubleExtensionFileUploadVuln = 'ID-double-extension-file-upload-vuln';
        // Special character bypass (null byte, spaces, dots)
        this.SpecialCharBypassVuln = 'ID-special-char-bypass-file-upload-vuln';
        // Server configuration file upload
        this.ServerConfigFileUploadVuln = 'ID-server-config-file-upload-vuln';
        // ZIP symlink attack vulnerability
        this.ZipSymlinkVuln = 'ID-zip-symlink-file-upload-vuln';
    }

    getAttackVectors() {
        return uploadVectors;
    }

    getAttackableEvents() {
        return ['file-upload']
    }

    processAttackResponse(attack) {
        if (!attack || !attack.result || !attack.result.resp || !attack.result.resp.httpResponse) {
            return;
        }

        const { method, headers } = attack.result.req.options;

        // Check if the request is a file upload (POST with multipart/form-data)
        if (method !== 'POST') {
            return;
        }

        // Use caseless to handle case-insensitive header lookup
        const headerCase = caseless(headers);
        const contentType = headerCase.get('Content-Type');

        if (!contentType || !contentType.toLowerCase().includes('multipart/form-data')) {
            return;
        }

        const payloadFile = attack.vector;
        if (payloadFile) {
            const body = attack.result.resp.httpResponse.body;
            const statusCode = attack.result.resp.httpResponse.statusCode;

            if (body && this.checkBodyForVuln(body, statusCode)) {
                attack.isVulnerable = true;

                // Check for basic unrestricted file upload (common extensions)
                if (this.checkUnrestrictedFileUpload(payloadFile)) {
                    this.addVulnerabilitytoResult(attack, this.UnrestrictFileUploadVuln, payloadFile);
                }
                // Check for blacklist bypass using uncommon extensions
                else if (this.checkBlacklistBypass(payloadFile)) {
                    this.addVulnerabilitytoResult(attack, this.BlacklistBypassVuln, payloadFile);
                }
                // Check for double extension bypass
                else if (this.checkDoubleExtension(payloadFile)) {
                    this.addVulnerabilitytoResult(attack, this.DoubleExtensionFileUploadVuln, payloadFile);
                }
                // Check for special character bypasses
                else if (this.checkSpecialCharBypass(payloadFile)) {
                    this.addVulnerabilitytoResult(attack, this.SpecialCharBypassVuln, payloadFile);
                }
                // // Check for server configuration file upload
                // else if (this.checkServerConfigFileUpload(payloadFile)) {
                //     this.addVulnerabilitytoResult(attack, this.ServerConfigFileUploadVuln, payloadFile);
                // }
                // Check for ZIP symlink attack [Disabling in Phase 1]
                // else if (this.checkZipSymlinkAttack(payloadFile)) {
                //     this.addVulnerabilitytoResult(attack, this.ZipSymlinkVuln, payloadFile);
                // }
            }
        }
    }

    checkUnrestrictedFileUpload(filename) {
        const commonExtPattern = /\.(php|asp|aspx|jsp|jspx)$/i;
        return commonExtPattern.test(filename);
    }

    checkBlacklistBypass(filename) {
        const uncommonExtPattern = /\.(php[3-5]?|pht|phps|phar|phpt|pgif|phtml|phtm)$/i;
        return uncommonExtPattern.test(filename);
    }

    checkDoubleExtension(filename) {
        const doubleExtPattern = /\.(php|asp|aspx|jsp|jspx|js|exe|dll|so|sh|bat|cmd|vbs|ps1)\.(png|jpg|jpeg|gif)$/i;
        return doubleExtPattern.test(filename);
    }

    checkSpecialCharBypass(filename) {
        const specialCharPattern = /\.php(?:%00|\x00|\s|\.|;|::).*$|\.php%20|\.php\./i;
        return specialCharPattern.test(filename);
    }

    // checkServerConfigFileUpload(filename) {
    //     const serverConfigPattern = /\.(htaccess|web\.config|httpd\.conf|nginx\.conf|php\.ini|apache2\.conf|httpd\.conf\.bak|nginx\.conf\.bak)$/i;
    //     return serverConfigPattern.test(filename);
    // }

    // checkZipSymlinkAttack(filename) {
    //     // Check if the file is a ZIP file
    //     const isZipFile = /\.zip$/i.test(filename);
    //     return isZipFile;
    // }

    checkBodyForVuln(respBody, respStatusCode) {
        if (!respBody || respStatusCode < 200 || respStatusCode >= 300) {
            return false;
        }

        const bodyString = respBody.toString();
        const successPatterns = [
            // File upload success patterns
            /The file has been uploaded/i,
            /(?:file|upload).*(?:successful|completed|saved|uploaded)/i,
            /successfully.*(?:uploaded|saved|stored)/i,
            /upload.*(?:complete|finished|done)/i,
            /The file .*\.[\w]+(?:\s+)?has been uploaded/i,
            /.*\.[\w]+(?:\s+)?(?:uploaded|saved)(?:\s+)?successfully/i,
            /(?:uploaded|saved)(?:\s+)?(?:file|path)?:?\s+.*\.[\w]+/i,
            /File unzipped/i,
        ];

        return successPatterns.some(pattern => new RE2(pattern).test(bodyString));
    }

}

const uploadVectors = [
    // Basic web shells
    'scanner/upload-attack-files/basic-payloads/haiku.php',
    'scanner/upload-attack-files/basic-payloads/haiku.asp',
    'scanner/upload-attack-files/basic-payloads/haiku.aspx',
    'scanner/upload-attack-files/basic-payloads/haiku.jsp',
    'scanner/upload-attack-files/basic-payloads/haiku.jspx',

    // Blacklist bypasses
    'scanner/upload-attack-files/basic-payloads/haiku.php5',
    'scanner/upload-attack-files/basic-payloads/haiku.phtml',
    'scanner/upload-attack-files/basic-payloads/haiku.phar',
    'scanner/upload-attack-files/basic-payloads/haiku.pht',
    'scanner/upload-attack-files/basic-payloads/haiku.php3',
    'scanner/upload-attack-files/basic-payloads/haiku.php7',

    // Double extensions
    'scanner/upload-attack-files/double-extension-payloads/haiku.php.jpeg',
    'scanner/upload-attack-files/double-extension-payloads/haiku.php.png',
    'scanner/upload-attack-files/double-extension-payloads/haiku.asp.jpeg',
    'scanner/upload-attack-files/double-extension-payloads/haiku.jsp.jpeg',

    // Special character bypasses
    'scanner/upload-attack-files/special-char-payloads/haiku.php%00.jpeg',
    'scanner/upload-attack-files/special-char-payloads/haiku.php%00.png',
    'scanner/upload-attack-files/special-char-payloads/haiku.php%0d%0a.jpeg',
    'scanner/upload-attack-files/special-char-payloads/haiku.php%0d%0a.png',
    'scanner/upload-attack-files/special-char-payloads/haiku.php%20',

    // Critical config files
    // 'scanner/upload-attack-files/server-config/haiku.htaccess',
    // 'scanner/upload-attack-files/server-config/haiku.web.config',
]

module.exports = FileUploadAttack;