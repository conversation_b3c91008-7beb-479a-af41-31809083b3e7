const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class MaliciousContentFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-malicious-content-found'
    }

    wantProcessAttackResponse(attack) {
        let ExcludeURL = /\/blog\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i
        if (ExcludeURL.test(attack.href)) {
            return false
        }
        
        let ResBody = _.get(attack, "result.resp.body")
        // let ResContentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')
        // if (attack.httpRequest.method == 'GET' && attack.attackArea == 'original-crawler-request' && ResContentType == 'text/html' && ResBody.length > 10) {
        if (attack.httpRequest.method == 'GET' && attack.attackArea == 'original-crawler-request' && ResBody.length > 10) {

            /*Example defaced site:
            http://mail.bmj.ac.in/opindia.html
            https://free.geeex.net/opspatuk.html 
            http://dpsi.iycworld.com/
            https://cs1.iycworld.com/
            https://sosatrey.edu.in/            
            https://nwcmc.gov.in/webalizer/   
            https://nwcmc.gov.in/webalizer/index.php         
            
            Currently only check homepage and endsWith .html
            if want to check other pages update below or use regex or remove this checking in if condition.*/
            // let proto = _.get(attack, 'result.req.parsedURL.protocol')
            // let homepage = proto + '//' + attack.hostname + '/'

            //Add response body keywords below
            let RegExp_Resbody = /\b(A2 Team|AnonGhost|ANONYMOUSINDONESIA_1D|Bangkulu Cyber Team|BEKASI CYBER TEAM_ID|BONDOWOSOBLACKHAT|C.P ANON|Chedoya Cyber T|Chedoya Cyber Team|CIREBON-GREYHAT-CRIME Bangkulu Cyber Team|CodeNewbie|CYBER MAFIA|DragonForceMalaysia|ELITE_6-27|Foursdeath Team|FROM LAMMER TO MASTAH|GANOSEC TEAM|GARUDA CYBER FORT|GREY FORCE SEC |H.A.T(HENTAI) |Hacked by|Hacked By AnimSec|Hacked By ikiroy|Hacked By Jambi Cyber Team|HACKTIVIST INDONESIA|Hacktivist Of Garuda|HarimauMalayaCyberArmy|Horizon Cyber Team|INDONESIA CYBER XPLOID|JAMBI BLACKHAT|JAMBI CYBER TEAM|JatengXploit|JAWA TENGAH XPLOI|Karawang Cyber Team|Ketapang Grey Hat Team|LocalhostMalaysia|Manado Cyber Team|Minions Cyber Crime|MyGovTeam|one hat cyber team|PhantomCrews|RileksCrew|SABUN BOLONG CYBER CLUB|SQUAD CYBER KALONG 2K23|T3DimensionMalaysia|TEGAL GREYHAT TEAM|UIBH|UnitedMuslimCyberArmy|WARRIOR GARUDA CRIME)\b/i
            // let RegExp_URI = /\/([\w\-\/]+(.\w{2,5})?)$/i
            // if (homepage == attack.href || RegExp_URI.test(attack.href) && RegExp_Resbody.test(ResBody)) {
            if (RegExp_Resbody.test(ResBody)) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.MaliciousContentFound) {
            return
        }

        let ResBody = _.get(attack, "result.resp.body")

        //RegExp based on current scenario. it ll be updated later if needs.
        let RegExp_style = /(position:absolute|top:0|left:0|border:0)/i
        let RegExp_title = /\b(A2 Team|AnonGhost|ANONYMOUSINDONESIA_1D|Bangkulu Cyber Team|BEKASI CYBER TEAM_ID|BONDOWOSOBLACKHAT|C.P ANON|Chedoya Cyber T|Chedoya Cyber Team|CIREBON-GREYHAT-CRIME Bangkulu Cyber Team|CodeNewbie|CYBER MAFIA|DragonForceMalaysia|ELITE_6-27|Foursdeath Team|FROM LAMMER TO MASTAH|GANOSEC TEAM|GARUDA CYBER FORT|GREY FORCE SEC |H.A.T(HENTAI) |Hacked by|Hacked By AnimSec|Hacked By ikiroy|Hacked By Jambi Cyber Team|HACKTIVIST INDONESIA|Hacktivist Of Garuda|HarimauMalayaCyberArmy|Horizon Cyber Team|INDONESIA CYBER XPLOID|JAMBI BLACKHAT|JAMBI CYBER TEAM|JatengXploit|JAWA TENGAH XPLOI|Karawang Cyber Team|Ketapang Grey Hat Team|LocalhostMalaysia|Manado Cyber Team|Minions Cyber Crime|MyGovTeam|one hat cyber team|PhantomCrews|RileksCrew|SABUN BOLONG CYBER CLUB|SQUAD CYBER KALONG 2K23|T3DimensionMalaysia|TEGAL GREYHAT TEAM|UIBH|UnitedMuslimCyberArmy|WARRIOR GARUDA CRIME)\b/i

        //Checking hidden iframe
        let iframetag = ResBody.match(/<iframe.+?>/gi)
        let details = []
        if (RegExp_style.test(iframetag)) {
            for (let iframeval of iframetag) {
                let styleattr = (iframeval.match(/\bstyle="[\w:;\s%]+?"/i))[0]
                if (styleattr.length > 20 && RegExp_style.test(styleattr)) {
                    let style = styleattr.split(';')
                    let count = 0
                    for (let i of style) {
                        if (RegExp_style.test(i)) { count++ }
                    }
                    if (count == 4) {
                        details.push({
                            dom_element: iframeval
                        })
                        if (details.length > 0) {
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                            pluginDataForRequest.MaliciousContentFound = true
                        }
                    }
                }
            }
        }
        //checking defaced site
        let titletag = ResBody.match(/<title.+?>/gi)[0]
        if (titletag) {
            if (titletag.length > 0 && RegExp_title.test(titletag)) {
                // let imgsrc = ResBody.match(/<img.+?>/gi)
                let bodytag = ResBody.match(/<body[\w\W]+<\/body>/i)
                /*currently check exact value of imagename, it ll be updated if needs.
                //ex img src:
                src="https://i.ibb.co/P53Cn6b/1623330735104-1.png" 
                src="https://i.imgur.com/l04DZJV.png"                
                Disable checking src file name bcoz possible to miss 
                /l04DZJV\.png|1623330735104-1.png/i.test(imgsrc)*/

                // if (bodytag && imgsrc && RegExp_title.test(bodytag)) {
                if (bodytag && RegExp_title.test(bodytag)) {
                    details.push({
                        dom_element: `Site defaced: ${titletag} URL: ${attack.href}`
                    })
                    if (details.length > 0) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        pluginDataForRequest.MaliciousContentFound = true
                    }
                }
            }
        }
    }
}
module.exports = MaliciousContentFound