const NetworkAttack = require('./network-attack')
const URL = require('url').URL
const _ = require('lodash')

class Version<PERSON><PERSON><PERSON> extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.iisVersionDisclosureVulnID = 'ID-Microsoft-Server-Version-Disclosure'
        this.ASPNETVersionDisclosureVulnID = 'ID-asp-net-version-headers'
        this.WebServerVersionDts = 'ID-Web-Server-Version-Disclosure'
        this.FrameworkFound = 'ID-info-disclosure-http-headers'
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.Appdtsfound && pluginDataForRequest.serverdtsfound) { return }

        /**
        * server:Microsoft-IIS\/8.5
        * x-aspnet-version:4.0.30319
        * x-powered-by:ASP.NET 
        * const infoDisclosureHeaders = ['x-aspnet-version', 'x-aspnetmvc-version', 'x-powered-by']
       const webServerVersionDisclosureHeaders = ['server']
       */
        let ExcludeRegExp = /deny|undefined|false|^\W+$|XXX|YYY|ZZZ|ABC|XYZ|none|CloudFront|WAF|AkamaiGHost/i  //Avoid FP 
        if (!pluginDataForRequest.serverdtsfound) {
            let serverdts = _.get(attack, 'result.resp.httpResponse.headers.server', '')
            //Microsoft IIS Version Disclosure
            if (serverdts.length > 0 && /iis/i.test(serverdts)) {
                let details = { "result": "Server: " + serverdts }
                this.addVulnerabilitytoResult(attack, this.iisVersionDisclosureVulnID, details)
                pluginDataForRequest.serverdtsfound = true
            }
            //Web Server Version Disclosure
            if (serverdts.length > 0 && !pluginDataForRequest.serverdtsfound && !ExcludeRegExp.test(serverdts)) {
                let details = { "result": "Server: " + serverdts }
                this.addVulnerabilitytoResult(attack, this.WebServerVersionDts, details)
                pluginDataForRequest.serverdtsfound = true

            }
        }

        if (!pluginDataForRequest.Appdtsfound) {
            let frameworkdts = _.get(attack, 'result.resp.httpResponse.headers.x-powered-by', '')
            let aspnetdts = _.get(attack, 'result.resp.httpResponse.headers.x-aspnet-version', '')
            let aspnetmvcdts = _.get(attack, 'result.resp.httpResponse.headers.x-aspnetmvc-version', '')

            if ((frameworkdts.length > 0 && !ExcludeRegExp.test(frameworkdts)) || (aspnetdts.length > 0 && !ExcludeRegExp.test(aspnetdts)) || (aspnetmvcdts.length > 0 && !ExcludeRegExp.test(aspnetmvcdts))) {
                let aspValues = [frameworkdts, aspnetdts, aspnetmvcdts]
                let details = []
                for (let i = 0; i < 3; i++) {
                    if (aspValues[i].length > 0) {
                        if (i == 0) {
                            details.push({ result: "x-powered-by: " + aspValues[i] })
                        }
                        if (i == 1) {
                            details.push({ result: "x-aspnet-version: " + aspValues[i] })
                        }
                        if (i == 2) {
                            details.push({ result: "x-aspnetmvc-version: " + aspValues[i] })
                        }
                    }
                }
                if (details.length > 0 && frameworkdts.length > 0) {
                    this.addVulnerabilitytoResult(attack, this.FrameworkFound, details)
                    pluginDataForRequest.Appdtsfound = true
                }
                if (details.length > 0 && (aspnetdts.length > 0 || aspnetmvcdts.length > 0)) {
                    this.addVulnerabilitytoResult(attack, this.ASPNETVersionDisclosureVulnID, details)
                    pluginDataForRequest.Appdtsfound = true
                }
            }
        }
    }
}
module.exports = VersionChecker