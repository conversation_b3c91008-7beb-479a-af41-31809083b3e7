// Browser actions related methods 
// Any interaction with elements takes XPath as parameter and the parameter is assumed to be 
// base64 encoded to ensure that it can be passed between renderer and main easily. 
// specifically, prameter us run through JSON.parse(atob(..))

const HaikuUtils = require("../../common/lib/haiku-utils")

MutationSummary = nodeRequire('mutation-summary')
Indusface_LoDash = nodeRequire('lodash')

class IndusfaceCore {
    constructor() {}

    name(short) {
        return short ? "IFC-Core" : "Indusface Browser Core Plugin"
    }

    init() {
        indusfaceRenderer.pluginData.core = {}

        // set up internal datastucture & methods
        indusfaceRenderer.pluginData.core.interestingElementTypes = new Set()
        indusfaceRenderer.pluginData.core.linkItems = new Map() // dumb links i.e. ones without events attached
        indusfaceRenderer.pluginData.core.clickableItems = new Map() // anything with click event : TODO change to more event types
        indusfaceRenderer.pluginData.core.inputItems = new Map() // inputs including selects, checkbox, text ...
        indusfaceRenderer.pluginData.core.formItems = new Map() // forms
        indusfaceRenderer.pluginData.core.iframeItems = new Map() // iframes
        indusfaceRenderer.pluginData.core.rawItems = new Map() // raw elements - all inputs, forms, clickables, links combined (Putting them in different buckets is not needed at this time)
        indusfaceRenderer.pluginData.core.documentSteadyCallback = this.documentSteady.bind(this)
        indusfaceRenderer.pluginData.core.docSteadyId = 0
        indusfaceRenderer.pluginData.core.scriptItems = new Map() // scripts src

        //inject methods
        indusfaceRenderer.invokePluginMethod = this.invokePluginMethod.bind(this)
        indusfaceRenderer.waitForDocSteady = this.waitForDocSteady.bind(this)
        indusfaceRenderer.resetSteadyTimer = this.resetSteadyTimer.bind(this)
        indusfaceRenderer.addInterestingElementType = this.addInterestingElementType.bind(this)
        indusfaceRenderer.addInput = this.addInput.bind(this)
        indusfaceRenderer.addForm = this.addForm.bind(this)
        indusfaceRenderer.addIframe = this.addIframe.bind(this)
        indusfaceRenderer.addEvent = this.addEvent.bind(this)
        indusfaceRenderer.addLink = this.addLink.bind(this)
        indusfaceRenderer.addScript = this.addScript.bind(this)
        indusfaceRenderer.getItemForAdd = this.getItemForAdd.bind(this)
        indusfaceRenderer.getInterestingItems = this.getInterestingItems.bind(this)
        indusfaceRenderer.debug = {}
        indusfaceRenderer.debug.sendEventToMain = this.sendEventToMain.bind(this)
        indusfaceRenderer.getElementDepth = this.getElementDepth.bind(this);

    }

    /**
     * Get element depth in the DOM tree, limited to 100 levels to avoid infinite loops
     * @param {Object} element - The element to get the depth of
     * @returns {Object} - An object containing the depth and whether the limit was reached
     */
    getElementDepth(element, maxDepth = 100) {
        if (!element) {
            console.warn("Invalid element provided to getElementDepth.");
            return -1; // Return -1 to indicate an invalid input
        }

        let depth = 0;
        while (element.parentNode && depth < maxDepth) {
            depth++;
            element = element.parentNode;
        }

        // Return depth and whether the limit was reached
        return {
            depth,
            limitReached: depth === maxDepth
        };
    }

    // Loop through all plugins calling the appropriate method
    // returns how many plugins implemeneted the method  
    invokePluginMethod(method) {
        let args = [...arguments].slice(1) // skip the method arg

        // Call method on all plugins  that implement it
        let methodImplemented = 0
        for (let plugin of indusfaceRenderer.plugins) {
            if (plugin[method]) {
                methodImplemented++
                plugin[method](...args)
            }
        }

        return methodImplemented
    }

    getItemForAdd(el) {
        let nodeName = el.nodeName ? el.nodeName.toLowerCase() : 'null'
        let text = (el.textContent || el.innerText || '').replace(/\s+/g, ' ').trim()
        if (!text || text.length < 1) {
            text = 'null'
        }

        let isHidden = el.hasAttribute('hidden');
        let isDisabled = el.hasAttribute('disabled');

        // will be recalculated at getInterestingItems(). Is here as well in case item is not available there.
        let rect = indusfaceRenderer.getBoundingRect(el)
        let allAttrs = Array.from(el.attributes).map(a => {
            return {
                name: a.name,
                value: a.value
            }
        })

        let depthInfo = indusfaceRenderer.getElementDepth(el);
        let fullPath = indusfaceRenderer.getFullXpath(el);

        let item = {
            name: 'string' == typeof (el.name) ? el.name.toLowerCase() : 'null',
            id: 'string' == typeof (el.id)? el.id : 'null',
            value: el.value ? el.value : 'null',
            nodeName: nodeName,
            type: 'string' == typeof (el.type) ? el.type.toLowerCase() : nodeName == 'input' ? 'text' : nodeName,
            text: text,
            pattern: el.pattern,
            labels: el.labels ? Array.from(el.labels).map(x=>x.textContent.trim()).join(',') : 'null',
            href: 'string' == typeof (el.href) ? el.href : 'null',
            formaction: 'string' == typeof (el.formaction) ? el.formaction : 'null',
            eventType: 'null',
            allAttrs,
            rect,
            hrefBtn: allAttrs.find(x => x.name == 'href') ? allAttrs.find(x => x.name == 'href').value : 'null',
            // for forms specifically
            action: 'string' == typeof (el.action) ? el.action : 'null',
            method: 'string' == typeof (el.method) ? el.method.toUpperCase() : 'null',
            //add scriptr for script tags
            src: 'string' == typeof (el.src) ? el.src : 'null',
            // input attrs
            minlength: el.minlength,
            maxlength: el.maxlength,
            min: el.min,
            max: el.max,
            depthInfo: depthInfo,
            fullPath: fullPath,
            isDisabled: isDisabled,
            isHidden: isHidden
        }

        return item
    }

    addInput(input) {
        let coreData = indusfaceRenderer.pluginData.core
        let inputItem = indusfaceRenderer.getItemForAdd(input)
        coreData.inputItems.set(indusfaceRenderer.getXpath(input), inputItem)
        coreData.rawItems.set(indusfaceRenderer.getXpath(input), input)
        indusfaceRenderer.invokePluginMethod('OnInputItemAdded', inputItem, input)
    }

    addScript(script) {
        let coreData = indusfaceRenderer.pluginData.core
        let scriptItem = indusfaceRenderer.getItemForAdd(script)
        coreData.scriptItems.set(indusfaceRenderer.getXpath(script), scriptItem)
        coreData.rawItems.set(indusfaceRenderer.getXpath(script), script)
        indusfaceRenderer.invokePluginMethod('OnScriptItemAdded', scriptItem, script)
    }

    addForm(form) {
        let coreData = indusfaceRenderer.pluginData.core
        let formItem = indusfaceRenderer.getItemForAdd(form)

        // add the xpaths of the forms elements
        formItem.formElements = []
        for (let formEl of form.elements) {
            formItem.formElements.push(indusfaceRenderer.getXpath(formEl))
        }

        // get the potential submit items so that the 'button' action can be added to the submit info
        let submitElems = form.querySelectorAll("[type='submit'], [type='image'], a")
        let isAspPage = !!document.getElementById('__VIEWSTATE')
        formItem.potentialSubmitItems = []
        for (let submitEl of submitElems) {
            if (isAspPage || submitEl.tagName.toLowerCase() != 'a') {
                formItem.potentialSubmitItems.push(indusfaceRenderer.getXpath(submitEl))
            }
        }

        // now add the form item
        coreData.formItems.set(indusfaceRenderer.getXpath(form), formItem)
        coreData.rawItems.set(indusfaceRenderer.getXpath(form), form)
        indusfaceRenderer.invokePluginMethod('OnFormItemAdded', formItem, form)
    }

    // for now only click
    addEvent(el, eventType) {
        let coreData = indusfaceRenderer.pluginData.core
        eventType = eventType.toLowerCase()
        var key = indusfaceRenderer.getXpath(el)

        // if there is a link that later got an event added, remove from link items
        coreData.linkItems.delete(key)

        let item = indusfaceRenderer.getItemForAdd(el)
        item.eventType = eventType
        if ( el.attributes.getNamedItem('onclick') ) {
            item.onclick = el.attributes.getNamedItem('onclick').value
        } else if ( el.onclick ) {
            item.onclick = el.onclick.toString()
        }        
        coreData.clickableItems.set(key, item)
        coreData.rawItems.set(key, el)

        indusfaceRenderer.invokePluginMethod('OnEventItemAdded', item, el)
    }

    addLink(link) {
        let isHref = link.hasAttribute('href') || link.href
        let isClick = link.hasAttribute('onclick') || link.onclick
        if (isClick || isHref) {
            let coreData = indusfaceRenderer.pluginData.core

            // if this has an event already attached, dont do anything
            let xpath = indusfaceRenderer.getXpath(link)
            if (coreData.clickableItems.has(xpath)) {
                return // don't add since its already an evented link
            }

            let href = link.href || link.getAttribute('href')
            let re_js_links = /^javascript:/i
            if (isClick || re_js_links.test(href)) {
                indusfaceRenderer.addEvent(link, 'click')
            } else if (href.length) {
                let item = indusfaceRenderer.getItemForAdd(link)
                coreData.linkItems.set(xpath, item)
                coreData.rawItems.set(xpath, link)

                indusfaceRenderer.invokePluginMethod('OnLinkItemAdded', item, link)
            }
        }
    }

    addIframe(ele) {
        let coreData = indusfaceRenderer.pluginData.core
        let xpath = indusfaceRenderer.getXpath(ele)
       
        let item = indusfaceRenderer.getItemForAdd(ele)
        coreData.iframeItems.set(xpath, item)
        coreData.rawItems.set(xpath, ele)

        indusfaceRenderer.invokePluginMethod('OnIframeItemAdded', item, ele)
    }

    OnAddEventListener(elem, type, ...args) {
        //console.log( 'OnAddEventListener: ', arguments)
        // // reset timer if it has been set.
        indusfaceRenderer.resetSteadyTimer(true);

        if (!elem || elem === document || elem === document.body || elem === global || elem === window) {
            // dont need such global handlers
            // console.log(`...skipping ${elem} -> ${type}`)
            return
        }

        type = type.toLowerCase()
        // TODO add more event types
        if (type.includes('click') || type.includes('mouse')) {
            indusfaceRenderer.invokePluginMethod('OnElementsAdded', elem, {
                elementType: 'event',
                args: [type]
            });
            //indusfaceRenderer.addEvent(this, type);
        }
    }

    OnElementsAdded(e, ifcInfo) {
        let tag = e.tagName ? e.tagName.toLowerCase() : null
        let type = 'string' == typeof (e.type) ? e.type.toLowerCase() : null
        // input text tags...
        let elemAdded = false

        if (ifcInfo && ifcInfo.elementType) { // ifcInfo
            elemAdded = true
            switch (ifcInfo.elementType) {
                case 'input':
                    indusfaceRenderer.addInput(e)
                    break;

                case 'link':
                    indusfaceRenderer.addLink(e)
                    break;

                case 'event':
                    indusfaceRenderer.addEvent(e, 'click')
                    break;

                default:
                    console.log(`unknown ifcInfo.elementType ${ifcInfo.elementType}`)
                    elemAdded = false
            }
        }

        if(!elemAdded && tag == 'iframe'){
            indusfaceRenderer.addIframe(e)
            elemAdded = true
        }

        // add form element
        if (!elemAdded && tag == 'form') {
            indusfaceRenderer.addForm(e)
            elemAdded = true
        }

        // for script tags
        if (!elemAdded && tag == 'script') {
            indusfaceRenderer.addScript(e)
            elemAdded = true
        }

        if (elemAdded) {
            return
        }

        // don't reset forms
        if (type == 'reset') {
            return
        }

        // input & button type elements
        if (tag == 'a' || tag == 'area') {
            indusfaceRenderer.addLink(e)
            elemAdded = true
        } else if ((tag == 'input' && (type != 'button' && type != 'submit' && type != 'image')) ||
            (tag == 'select' || tag == 'textarea')) {
            indusfaceRenderer.addInput(e)
            elemAdded = true
        } else if (tag != 'input' || type == 'button' || type == 'submit' || type == 'image') {
            indusfaceRenderer.addEvent(e, 'click') // assume all events are clicks
            elemAdded = true
        }

        if (!elemAdded) {
            console.log('....elem not added', e, ifcInfo)
        }
    }

    waitForDocSteady(quietTimeout, docSteadyId) {
        indusfaceRenderer.pluginData.core.quietTimeout = quietTimeout

        // Set up mutation observer - event reset timer already there as well.
        let coreData = indusfaceRenderer.pluginData.core
        if (!coreData.steadyMutationSummary) {
            let queryElems = []
            for (let el of coreData.interestingElementTypes) {
                queryElems.push(el)
            }
            coreData.steadyMutationSummary = new MutationSummary({
                callback: function (res) {
                    indusfaceRenderer.resetSteadyTimer()
                    for (let e in res.added) {
                        indusfaceRenderer.invokePluginMethod('OnElementsAdded', e)
                    }
                }, // required
                queries: [{
                    element: queryElems.join(',')
                }]
            })
        }

        coreData.docSteadyId = docSteadyId
        indusfaceRenderer.resetSteadyTimer()
    }

    documentSteady() {
        // see if there are outstanding ajax requests - if so, reset the timer
        if (indusfaceRenderer.pluginData.ajax && indusfaceRenderer.pluginData.ajax.outstandingRequests) {
            if (indusfaceRenderer.pluginData.ajax.outstandingRequests < 0) {
                console.log(`\n**** Something strange: outstanding requests is negative: ${indusfaceRenderer.pluginData.ajax.outstandingRequests}. Setting it back to 0 for crawl`)
                indusfaceRenderer.pluginData.ajax.outstandingRequests = 0
            }

            // Check if the only activity is from socket.io polling
            let hasOnlySocketIoActivity = Array.from(indusfaceRenderer.pluginData.ajax.requests || []).every(req => 
                req && req.responseURL && req.responseURL.includes('socket.io')
            );

            if (hasOnlySocketIoActivity) {
                console.log('[DocSteady] Only socket.io activity detected, considering page steady');
                // Don't reset timer for socket.io only activity
            } else {
                //console.log(`[DocSteady] Still have ${indusfaceRenderer.pluginData.ajax.outstandingRequests} non-socket.io requests in pipeline`);
                indusfaceRenderer.resetSteadyTimer(false);
                return;
            }
        }

        let coreData = indusfaceRenderer.pluginData.core
        //console.log(`[DocSteady] Document steady check passed for ID ${coreData.docSteadyId}`);
        
        if (coreData.steadyMutationSummary) {
            //console.log('[DocSteady] Disconnecting mutation observer');
            coreData.steadyMutationSummary.disconnect()
            coreData.steadyMutationSummary = null
        }

        if (coreData.steadyTimer) {
            //console.log('[DocSteady] Clearing steady timer');
            clearTimeout(coreData.steadyTimer)
            coreData.steadyTimer = null
        }

        indusfaceRenderer.sendEventToMain('document-steady-' + coreData.docSteadyId)
    }

    sendEventToMain(event) {
        indusfaceRenderer.sendEventToMain(event)
    }
    resetSteadyTimer(resetOnlyIfRunning = false) {
        let coreData = indusfaceRenderer.pluginData.core

        if (resetOnlyIfRunning && !coreData.steadyTimer)
            return

        if (coreData.steadyTimer)
            clearTimeout(coreData.steadyTimer);
        coreData.steadyTimer = setTimeout(coreData.documentSteadyCallback, coreData.quietTimeout)
    }
    addInterestingElementType(type) {
        indusfaceRenderer.pluginData.core.interestingElementTypes.add(type)
    }

    onAddInterestingElementTypes() {
        let interestingElements = ['input', 'email', 'a', 'area', 'button', 'textarea', 'submit', 'select', 'form', 'iframe', 'script']
        for (let type of interestingElements) {
            indusfaceRenderer.addInterestingElementType(type)
        }
    }

    gotInterestingItems(rawItems, coreData) {
        let {
            clickableItems,
            inputItems,
            formItems,
            linkItems,
            iframeItems,
            scriptItems,
        } = coreData
        for (var i = 0; i < rawItems.length; i++) {
            var rawItemArr = rawItems[i]
            var rawXpath = rawItemArr[0]
            var rawItem = rawItemArr[1]
            var itemsToAnnotate = [clickableItems.get(rawXpath), inputItems.get(rawXpath), formItems.get(rawXpath), linkItems.get(rawXpath), iframeItems.get(rawXpath),scriptItems.get(rawXpath)]
            let newRect = indusfaceRenderer.getBoundingRect(rawItem)
            if (newRect) {
                for (let itemToAnnotate of itemsToAnnotate) {
                    if (itemToAnnotate) {
                        itemToAnnotate.rect = newRect
                    }
                }
            }
        }
    }

    getInterestingItems(clearItems = false, scanType) {
        // Run through all the interesting element types and tell plugins that 
        // elements have been added
        let coreData = indusfaceRenderer.pluginData.core
        coreData.interestingElementTypes.forEach((tag) => {
            let elems = document.body.getElementsByTagName(tag)
            for (let elem of elems) {
                if(!elem) {
                    continue;
                }
                
                indusfaceRenderer.invokePluginMethod('OnElementsAdded', elem)
            }
        })

        // Add elements using xpaths
        // add all elements that have attributes: onclick, onchange, type=submit/image and all 'mouse' event handlers
        // Future:   '//@*[contains(., "click")]/..' will match elemnets with attribute values containing click
        //          can be used for sites like twitter which define events as data-action attributes
        let xpaths = ['//*[@onclick or @onchange or @type="submit" or @type="image"]', // having attributes: onclick, onchange, type=submit/image
            '//@*[contains(name(), "onmouse")]/..' // attr name contains onmouse => all 'mouse' event handlers
        ]

        for (let xpath of xpaths) {
            let elems = indusfaceRenderer.getElementsMatchingXpath(xpath)
            for (let elem of elems) {
                indusfaceRenderer.invokePluginMethod('OnElementsAdded', elem)
            }
        }

        // convert to form that server can consume.
        // @todo: See if Array.from() will work out  - it was not in earlier versions of electron/chromium.
        function convertToArray(iterable) {
            let arr = []
            for (let i of iterable) {
                arr.push(i)
            }
            return arr
        }

        let rawItems = convertToArray(coreData.rawItems)

        indusfaceRenderer.invokePluginMethod('gotInterestingItems', rawItems, coreData)

        let clickableItems = convertToArray(coreData.clickableItems)
        let inputItems = convertToArray(coreData.inputItems)
        let formItems = convertToArray(coreData.formItems)
        let linkItems = convertToArray(coreData.linkItems)
        let iframeItems = convertToArray(coreData.iframeItems)
        let scriptItems = convertToArray(coreData.scriptItems)

        //filter form items which have inViewport as false & also remove all its elements from clickableItems, inputItems, linkItems, iframeItems, scriptItems
        formItems = formItems.filter(formItem => {
            if(formItem[1].rect.inViewport) {
                return true;
            }

            for (let formItemElement of formItem[1].formElements) {
                clickableItems.splice(clickableItems.indexOf(formItemElement), 1);  
                inputItems.splice(inputItems.indexOf(formItemElement), 1);
                linkItems.splice(linkItems.indexOf(formItemElement), 1);
                iframeItems.splice(iframeItems.indexOf(formItemElement), 1);
                scriptItems.splice(scriptItems.indexOf(formItemElement), 1);
            }

            return false;
        });

        //filter form items where id similar, take only one form item
        //and also check form which duplicate, iterate their elements, get their path & remove it from 
        //clickableItems, inputItems, linkItems, iframeItems, scriptItems
        //after that remove those form items from formItems array

        let formItemsMap = new Map();
        let formItemToRemove = [];
        for (let formItem of formItems) {
            let formId = formItem[1].id + formItem[1].text;

            if(!formId) {
                continue;
            }

            if (formItemsMap.has(formId)) {
                let existingFormItem = formItemsMap.get(formId);
                formItemToRemove.push(formItem);
            } else {
                formItemsMap.set(formId, formItem);
            }
        }

        //only remove form items from formItems array, more than one, keep only one
        for (let formItem of formItemToRemove) {
            formItems.splice(formItems.indexOf(formItem), 1);
        }

        inputItems = indusfaceRenderer.getAgGridTableRow(inputItems);
        clickableItems = indusfaceRenderer.getAgGridTableRow(clickableItems);
        linkItems = indusfaceRenderer.getAgGridTableRow(linkItems);

        //filter clickable items which has type as button & have same text, take only one 
        // and filter similar other clickable items for same text.
        let count = 0;
        let btnTextName = {};
        clickableItems = clickableItems.filter(item => {
            if(item[1].text == '' || item[1].text == 'null' || item[1].text == null) {
                return true;
            }

            if (item[1].type == 'button' || item[1].type == 'submit') {
                if (btnTextName[item[1].text]) {
                    //If item is disabled or hidden, return true.
                    //cause it will removed later in filter-items.js
                    //via isDisabledOrHidden function.
                    if(item[1].isDisabled || item[1].isHidden) {
                        return true;
                    }
                    //If item is not in viewport, return true.
                    //cause it will removed later in filter-items.js
                    //via visibleOnly function.
                    if(!item[1].rect.inViewport) {
                        return true;
                    }

                    count++;
                    return false;
                } else {
                    btnTextName[item[1].text] = item[1].text;
                    return true;
                }
            }

            return true;
        })

        console.log(`Count of similar button items: ${count}\n ${JSON.stringify(Object.keys(btnTextName))}`);

        if (clearItems) {
            coreData.clickableItems.clear()
            coreData.inputItems.clear()
            coreData.formItems.clear()
            coreData.linkItems.clear()
            coreData.iframeItems.clear()
            coreData.scriptItems.clear()
        }

        // get all frames as well
        // for now just get their URIs as simple HREFs (not annotated item)
        let frameUris = []
        if (window.frames.length) {
            for (let i = 0; i < window.frames.length; i++) {
                try {
                    // cross origin frames will throw an exception
                    frameUris.push(window.frames[i].location.href)
                } catch (err) {
                    // silently eat error
                }
            }
        }

        let maxDepth = 0;

        try {
            function element_list(el, depth) {
                if (depth > maxDepth) {
                    maxDepth = depth;
                }

                for (let i = 0; i < el.children.length; i++) {
                    element_list(el.children[i], depth + 1);
                }
            }

            element_list(document, 0);
        } catch (error) {
            console.error(`Unable to get DOM max depth, reason: ${error.message}`);
        }

        let simpleHrefs = [...frameUris]
        let domInfo = {
            "depth": maxDepth,
            "document":document.documentElement.innerHTML
        }

        // Function to check if an element and the clickable element share a common parent container
        function isElementPartOfSection(element, clickableElement) {
            let rawElement = coreData.rawItems.get(element[0]);
            let rawClickableElement = coreData.rawItems.get(clickableElement[0]);

            let depthLevel = 0;

            if(!rawElement || !rawClickableElement) {
                return false;
            }

            let currentElement = rawElement;

            while (currentElement.parentNode) {
                if (currentElement.parentNode.contains(rawClickableElement)) {
                    return {
                        xpath: clickableElement[0],
                        parentElement: currentElement,
                        depthLevel: depthLevel,
                        clickableItem: clickableElement
                    };
                }

                currentElement = currentElement.parentNode;
                depthLevel++;
            }
            
            return null;
        }

        let releventItems = new Map();
        let inputItemDethInfo = {};

        for (let i = 0; i < inputItems.length; i++) {
            let inputItem = inputItems[i];
            
            //If item is table item, skip it.
            if(inputItem[1].isTableItem) {
                continue;
            }

            inputItemDethInfo[inputItem[0]] = [];

            for (let j = 0; j < clickableItems.length; j++) {
                //If item is table item, skip it.
                if(clickableItems[j][1].isTableItem) {
                    continue;
                }

                let commonParent = isElementPartOfSection(inputItem, clickableItems[j]);

                if (commonParent) {
                    inputItemDethInfo[inputItem[0]].push(commonParent);
                }
            }

            if (inputItemDethInfo[inputItem[0]].length > 0) {
                let closestRelevantItems = Indusface_LoDash.chain(inputItemDethInfo[inputItem[0]]).sortBy(['depthLevel']).take(10).value();

                for (let k = 0; k < closestRelevantItems.length; k++) {
                    let closestRelevantItem = closestRelevantItems[k];

                    if (!releventItems.has(closestRelevantItem.xpath)) {
                        releventItems.set(closestRelevantItem.xpath, closestRelevantItem);
                    }
                }
            }
        }

        let relevantClickItems = {};

        Indusface_LoDash.chain(Array.from(releventItems)).map(item => {
            relevantClickItems[item[0]] = item[0];
            return;
        }).value();

        //If table crawl optimization is disabled, do not perform the optimization to improve crawl performance.
        //Only use for the site where input & click items are in table rows but its non standard table. Like login page form items which are in table rows.
        //Found during idbi site scans
        if (!indusfaceRenderer.config.ignoreTableCrawlOptimizatiion) {
            let duplicateTableClickableItems = [];
            let duplicateTableInputItems = [];

            //If DOM have tables, remove duplicates clicks, links items which are children to these tables.
            //Take only unique 2 referece rows which have these clicks, links items.
            Indusface_LoDash.map(document.getElementsByTagName("table"), table => {
                try {
                    let rowElements = table.querySelector('tbody').children;
                    let evaluator = new XPathEvaluator();

                    // Define the XPath expression
                    let xpathExpression = ".//*";  // Selects all descendant elements of the <tr> element

                    // Evaluate the XPath expression
                    for (let i = 0; i < rowElements.length; i++) {
                        let rowElement = rowElements[i];
                        let result = evaluator.evaluate(xpathExpression, rowElement, null, XPathResult.ORDERED_NODE_SNAPSHOT_TYPE, null);

                        // Loop through the result to get the elements
                        for (let j = 0; j < result.snapshotLength; j++) {
                            let childElementXPath = indusfaceRenderer.getXpath(result.snapshotItem(j));

                            for (let k = 0; k < clickableItems.length; k++) {
                                if (childElementXPath == clickableItems[k][0]) {
                                    //Allow only first two rows clickable items.
                                    if (i >= 2) {
                                        duplicateTableClickableItems.push(clickableItems[k][0]);
                                    }
                                }
                            }

                            for (let k = 0; k < inputItems.length; k++) {
                                if (childElementXPath == inputItems[k][0]) {
                                    //Allow only first two rows clickable items.
                                    if (i >= 2) {
                                        duplicateTableInputItems.push(inputItems[k][0]);
                                    }
                                }
                            }
                        }
                    }

                } catch (error) {
                    console.error(`Error occured while iterating tables data in getInterestingItems method, ${error.toString()}`);
                }

                return table;
            });

            if (duplicateTableClickableItems.length > 0) {
                clickableItems = Indusface_LoDash.filter(clickableItems, clickableItem => {
                    let found = true;
                    for (let i = 0; i < duplicateTableClickableItems.length; i++) {
                        let duplicateTableClickableItem = duplicateTableClickableItems[i];

                        if (clickableItem[0] == duplicateTableClickableItem) {
                            found = false;
                            break;
                        }
                    }

                    return found;
                });
            }

            if (duplicateTableInputItems.length > 0) {
                inputItems = Indusface_LoDash.filter(inputItems, inputItem => {
                    let found = true;
                    for (let i = 0; i < duplicateTableInputItems.length; i++) {
                        let duplicateTableInputItem = duplicateTableInputItems[i];

                        if (inputItem[0] == duplicateTableInputItem) {
                            found = false;
                            break;
                        }
                    }

                    return found;
                });
            }
        }

        let interestingItems = {
            clickableItems,
            inputItems,
            formItems,
            linkItems,
            simpleHrefs,
            location: document.location.href,
            iframeItems,
            scriptItems,
            relevantClickItems: relevantClickItems
        }

        //When debugging, uncomment this to see the items that are being filtered out.
        // function visibleOnly(item) {
        //     if (item[1].rect.inViewport) {
        //         return true
        //     }
        //     // remove from relevantItems
        //     delete interestingItems.relevantClickItems[item[0]];
        //     return false
        // }

        // // Remove disabled or hidden items and also from relevantItems
        // function isDisabledOrHidden(item) {
        //     if (item[1].isDisabled || item[1].isHidden) {
        //         // remove from relevantItems
        //         delete interestingItems.relevantClickItems[item[0]];
        //         return true
        //     }
        //     return false;
        // }

        // interestingItems.clickableItems = interestingItems.clickableItems.filter(visibleOnly);
        // interestingItems.clickableItems = interestingItems.clickableItems.filter(item => !isDisabledOrHidden(item));

        if (scanType == 'defacement')
            interestingItems = { ...interestingItems, ...domInfo }

        return interestingItems;
    }

    // defacement info
    getDomInfo(clearItems = false) {
        let maxDepth = 0;

        try {
            function element_list(el, depth) {
                if (depth > maxDepth) {
                    maxDepth = depth;
                }
    
                for (let i = 0; i < el.children.length; i++) {
                    element_list(el.children[i], depth + 1);
                }
            }
            
            element_list(document, 0);
        } catch (error) {
            console.error(`Unable to get DOM max depth, reason: ${error.message}`);
        }

        let domInfo = {
            "depth": maxDepth,
            "document":document.documentElement.innerHTML
        }

        return {
            ...domInfo
        }
    }

}

module.exports = IndusfaceCore