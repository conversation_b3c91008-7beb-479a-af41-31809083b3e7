/**
 * Generate a site config by applying a recipe to the base config (typically network-scanner-config.js)
 * This can run a recipe of a list of steps consisting of {scope, match, edit} actions and generates the 
 * appropriate site specific config.
 *  - scope: Sets the scope for match & edit
 *           plugin - treated specially since it's keys are iterated
 *           vulnerability - treated specially since it's keys are iterated
 *           any top level object of config like HTTPRequestMonster or DefaultPluginSettings
 *  - match: body for function that returns boolean, edit will be called only if this returns true. Missing
 *           match body matches everything. The signatures for various scopes are:
 *              plugin - body of function (pluginName, plugin){ }, Called for each plugin
 *              vulnerability - body of function (vuln, vulnName, pluginName, plugin){ }, Called for each vuln Id
 *              others - body of function (settings){ } 
 *  - edit: body for function that returns boolean, edit will be called only if this returns true. Missing
 *           match body matches everything. The signatures for various scopes are:
 *              plugin - body of function (pluginName, plugin){ }
 *              vulnerability - body of function (vuln, vulnName, pluginName, plugin){ }
 *              others - body of function (settings) { } 
 * 
 * The way site specific config is implemented using proxies, when any nested property is changed, the
 * closest proxied parent property is duplicated and modified. This class helps do that automatically.
 * See documentaion in ConfigProxy for details.
 */

const fs = require('fs')
const _ = require('lodash')
const HaikuUtils = require('../../common/lib/haiku-utils')
const defaultConfig = require('../../network-scanner/config/network-scanner-config.js')

// from: https://gist.github.com/Yimiprod/7ee176597fef230d1451
/**
 * Deep diff between two object, using lodash
 * @param  {Object} object Object compared
 * @param  {Object} base   Object to compare with
 * @return {Object}        Return a new object who represent the diff
 */
function difference(object, base) {
    return _.transform(object, (result, value, key) => {
        if (!_.isEqual(value, base[key])) {
            //result[key] = !_.isArray(value) && _.isObject(value) && _.isObject(base[key]) ? difference(value, base[key]) : value;
            result[key] = _.isPlainObject(value) && _.isPlainObject(base[key]) ? difference(value, base[key]) : value;
        }
    });
}

class ModifyConfig {
    constructor(recipe, config = defaultConfig) {
        this.recipe = recipe
        this.originalConfig = config
        this.modifiedConfig = {}

        this.propertiesToDeepProxy = _.cloneDeep(config.propertiesToDeepProxy).filter(x => x != 'Plugins')
        this.processingLog = []
    }

    getModifiedConfig() {
        return this.modifiedConfig
    }

    getDiff() {
        return difference(this.getModifiedConfig(), this.originalConfig)
    }

    process() {
        for (let step of this.recipe) {
            this.performStep(step)
        }
    }

    performStep(step) {
        this.processingLog.push('processing step: ' + step.name)
        this.parseStep(step)

        // apply the operations
        this.applyStep(step)
    }

    parseStep(step) {
        step.match = this.getScopedfn(step.scope, step.match || 'return true') // no match fn => match all
        step.edit = this.getScopedfn(step.scope, step.edit)
    }

    getScopedfn(scope, body) {
        let fn
        switch (scope) {
            case 'plugin':
                fn = '(pluginName, plugin) => { ' + body + '}'
                break

            case 'vulnerability':
                fn = '(vulnName, vuln, pluginName, plugin) => { ' + body + '}'
                break

            default:
                fn = '(settings) => { ' + body + '}'
                break;

        }

        // convert to functions
        return eval(fn)
    }

    cloneObject(propertyName) {
        // if we already have this (maybe from a previous step), don't reset modified settings 
        let ob = _.get(this.modifiedConfig, propertyName)
        if (!ob) {
            _.set(this.modifiedConfig, propertyName, _.cloneDeep(_.get(this.originalConfig, propertyName)))
        }
    }

    clonePlugin(pluginName) {
        // if we already have this (maybe from a previous step), don't reset modified settings 
        return this.cloneObject(`Plugins['${pluginName}']`)
    }

    applyStep(step) {
        if (!_.isFunction(step.edit)) {
            throw new Error(`step ${step.name}, no editing operation specified`)
        }

        let originalConfig = this.originalConfig

        // apply step to matched objects
        switch (step.scope) {
            case 'plugin':
                for (let pluginName of Object.keys(originalConfig.Plugins)) {
                    // check at plugin level
                    let plugin = _.get(this.modifiedConfig, `Plugins['${pluginName}']`, originalConfig.Plugins[pluginName])
                    if (step.match && step.match(pluginName, plugin)) {
                        this.clonePlugin(pluginName)
                        step.edit(pluginName, this.modifiedConfig.Plugins[pluginName])
                    }
                }
                break

            case 'vulnerability':
                for (let pluginName of Object.keys(originalConfig.Plugins)) {
                    let plugin = _.get(this.modifiedConfig, `Plugins['${pluginName}']`, originalConfig.Plugins[pluginName])
                    let vulnerabilities = plugin.vulnerabilities || []
                    for (let vulnName of Object.keys(vulnerabilities)) {
                        if (step.match && step.match(vulnName, vulnerabilities[vulnName], pluginName, plugin)) {
                            this.clonePlugin(pluginName)
                            let vuln = this.modifiedConfig.Plugins[pluginName].vulnerabilities[vulnName]
                            step.edit(vulnName, vuln, pluginName, this.modifiedConfig.Plugins[pluginName])
                        }
                    }
                }
                break

            default:
                // the generic form is to modify settings i.e. properties of the key speified as scope
                // eg. request monster or plugin defaults
                if (!this.propertiesToDeepProxy.includes(step.scope)) {
                    throw new Error(`scope  ${step.scope} is not a deep proxied property (one of ${this.propertiesToDeepProxy})`)
                }
                let settings = this.modifiedConfig[step.scope] || originalConfig[step.scope]
                if (step.match && step.match(settings)) {
                    this.cloneObject(step.scope)
                    step.edit(this.modifiedConfig[step.scope])
                }
                break;


        }
    }
}

// main code
function main() {
    console.log('Generating site specific config using recipe: ', process.argv[2])
    let recipe = require(process.argv[2])
    let modifyCfg = new ModifyConfig(recipe)
    modifyCfg.process()
    console.log('\n Done - modified properties (not 100% accurate):')
    console.log(JSON.stringify(modifyCfg.getDiff(),null,2))
    fs.writeFileSync('./site-specific.json', JSON.stringify(modifyCfg.getModifiedConfig(),HaikuUtils.configReplacer))
}

module.exports=ModifyConfig