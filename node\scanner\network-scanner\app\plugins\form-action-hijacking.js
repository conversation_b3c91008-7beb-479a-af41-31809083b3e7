const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
var crypto = require('crypto')
const requestPromise = require('request-promise-native')
const cheerio = require('cheerio')
const { forEach, includes } = require('lodash')

class FormActionHijacking extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-form-action-hijacking'
    }

    getAttackVectors() {
        return __AtVectors
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['Referer', 'Host', 'Origin', 'X-Forwarded-Host', 'X-Forwarded-For', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded']
        })
    }

    getAttackableEvents() {
        return ['http-headers', 'form-encoded-post', 'uri-query-params']
    }

    /* async performNetworkAttack(attack) {
        let ResBody = _.get(attack, "result.resp.body")
        if (ResBody.length > 10 && /<form.+action=./i.test(ResBody)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    } */

    wantProcessAttackResponse(attack) {
        if (attack.pluginName == this.getName()) {
            let ResBody = _.get(attack, "result.resp.body", '')
            if (ResBody.length > 15 && /<form.+action=./i.test(ResBody)) {
                if (/action\s?=.+www.haikuformtest.com/i.test(ResBody)) {
                    return true
                }
                /* else {
                    let actionattr = ResBody.match(/action\s?=\s?('|").+?\1/g)
                    if (!/action\s?=\s*(?:'|")https?:/i.test(actionattr) && !/action\s?=\s?('|")\s*?\1/i.test(actionattr)) {
                        let page = cheerio.load(ResBody, {
                            baseURI: attack.href
                        });
                        let checklink = ResBody.match(/<(img|link).+?>/gi)
                        for (let element of checklink) {
                            if (element.includes('href')) {
                                let abspath = page(element).prop('href')
                                if (abspath.includes('www.haikuformtest.com')) {
                                    page = null
                                    return true
                                }
                            }
                            else if (element.includes('src')) {
                                let abspath = page(element).prop('src')
                                if (abspath.includes('www.haikuformtest.com')) {
                                    page = null
                                    return true
                                }
                            }
                        }
                    }
                    page = null
                } */
            }
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.formActFound) {
            return
        }

        /* if (attack.pluginName != this.getName()) {
            return false
        } */

        let ResBody = _.get(attack, "result.resp.body")
        try {
            const formTag = ResBody.match(/<form.+action=[\w\W]+?<\/form>/gi)
            const RegExp_action = /action=\s?('|").*?\1/i

            let details = []
            if (formTag.length > 0 && RegExp_action.test(formTag)) {
                let page = cheerio.load(ResBody, {
                    baseURI: attack.href
                });
                for (let formval of formTag) {
                    let RHS = page(formval).prop('action')
                    let actionattr = 'action="' + RHS
                    // let RHS = actionattr.split('action=')[1].replace(/('|")/g, '')
                    // let regexp_AttVec = new RegExp(_.escapeRegExp(attack.vector))
                    // let uri = new URL(attack.href)
                    if (actionattr.length > 1 && RHS.includes('www.haikuformtest.com')) {
                        let vuln = false
                        if (/^(?:https?:\/\/)?www.haikuformtest.com/i.test(RHS)) {
                            vuln = true
                        }
                        else if (RHS.includes('?') && /(?:url|Returnurl|route|redirect|path|next)/i.test(RHS)) {
                            let params = RHS.split('?')[1].split('&')
                            for (let i of params) {
                                if (i.includes('www.haikuformtest.com')) {
                                    let val = i.split('=')
                                    if (/(?:url|Returnurl|route|redirect|path|next)/i.test(val[0]) && val[1].includes('www.haikuformtest.com')) {
                                        vuln = true
                                        break
                                    }
                                }
                            }
                        }
                        if (vuln == true) {
                            details.push({
                                dom_element: `${actionattr}`
                                // dom_element: `${actionattr} and action absolute path: ${uri.href + actionattr}`
                            })
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                            pluginDataForRequest.formActFound = true
                            return
                        }
                    }
                    /* else if (actionattr.length > 1 && !RHS.startsWith(uri.origin)) {
                        /* let actionval = page(formval).prop('action')
                        if (regexp_AttVec.test(actionval)) {
                            details.push({
                                dom_element: `${actionattr} and action absolute path: ${actionval}`
                            })
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                            pluginDataForRequest.formActFound = true
                            return
                        }
                        else { 
                        let checklink = ResBody.match(/<(img|link).+?>/gi)
                        for (let element of checklink) {
                            let fullurl, path = element.match(/(href|src)\s?=\s?.+?('|")/i)[0].split('=')[1].replace(/('|")/g, '')
                            if (element.includes('href')) {
                                fullurl = page(element).prop('href')
                                let actionURL = new URL(fullurl)
                                let vuln = false
                                if (actionURL.hostname.includes(www.haikuformtest.com)) {
                                    vuln = true
                                }
                                else if (www.haikuformtest.com.test(actionURL.search)) {
                                    let params = actionURL.search.split('&')
                                    for (let i of params) {
                                        if (i.includes('www.haikuformtest.com')) {
                                            let val = i.split('=')
                                            if (/(?:url|Returnurl|route|redirect|path|next)/i.test(val[0]) && val[1].includes('www.haikuformtest.com')) {
                                                vuln = true
                                                break
                                            }
                                        }
                                    }
                                }
                                if (vuln == true) {
                                    details.push({
                                        dom_element: `${actionattr}, action absolute path: ${fullurl.replace(path, '') + actionattr.split('=')[1].replace(/('|")/g, '')}`
                                    })
                                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                                    pluginDataForRequest.formActFound = true
                                    return
                                }
                            }
                            else if (element.includes('src')) {
                                fullurl = page(element).prop('src')
                                if (regexp_AttVec.test(fullurl)) {
                                    details.push({
                                        dom_element: `${actionattr}, action absolute path: ${fullurl.replace(path, '') + actionattr.split('=')[1].replace(/('|")/g, '')}`
                                    })
                                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                                    pluginDataForRequest.formActFound = true
                                    return
                                }
                            }
                        }
                    } */
                }
                page = null
            }
        }
        catch (e) { return }
    }
}

const __AtVectors = [
    `www.haikuformtest.com`,
    `https://www.haikuformtest.com/`
]

module.exports = FormActionHijacking