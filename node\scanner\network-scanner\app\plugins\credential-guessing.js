const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const _ = require('lodash')
const FingerPrint = require('../../../common/lib/fingerprint')
const cheerio = require("cheerio")
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Credential Guessing strategy:
 * Here for all the login pages will be capturing first the original request successfull login response checksum
 * , then will be 
 * attacking with different user & pwd combination and capturing there response checksum and if any of them matches
 * with orignal response checksum, will mark it as vulnerable
 */
class CredentialGuessing extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-username-password-guess'
    }

    /**
     * get array of LFI attack vectors
     * @override
     */
    getAttackVectors() {
        return LoginDelegate.createVectorsIterator(BruteForceUNVectors, BruteForcePwdVectors)
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['login-request']
    }

    /**
     * @typedef {Object} fingerprintData
     * @property {fingerprint} structure  fingerprint of all the HTML/XML tags
     * @property {fingerprint} content fingerprint of all the text i.e. content of text nodes
     * @property {Number} statusCode status code from this response
     * @property {string} redirects entire redirect chain joined with ' + '
     */

    /**
     * Get structure and content fingerprints from the response.
     * @returns {fingerprintData} fingerprintData
     * @param {response} response The HTTP response
     */
    getFingerprints(response) {
        let fingerprints = { statusCode: response.statusCode }
        Object.assign(fingerprints, HaikuUtils.getFingerprints(response))
        return fingerprints
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {

        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //if vuln already found then return
        if (pluginStorageScanScope.bruteForceFound) {
            return
        }

        //Below scope only for all original crawler request in current scan scope
        let pluginStorage = this.getPluginScopedStore(attack, 'original-crawler-request')

        //Below code is to fetch the body of original request made
        if (attack.pluginName != this.getName()) {

            // only store fingerprints if request is login request
            let httpRequest = attack.httpRequest
            //
            if (LoginDelegate.isLoginRequest(httpRequest) && attack.attackArea == "original-crawler-request") {
                //attack.result.resp.httpResponse.cheerio = cheerio.load(attack.result.resp.body)
                let fingerprints = this.getFingerprints(attack.result.resp.httpResponse)
                _.set(pluginStorage, 'originalResponse.fingerprint', fingerprints)

            }
            return
        }

        //if original response not present or is null
        if (!pluginStorage.originalResponse ||
            pluginStorage.originalResponse.fingerprint.structure == null ||
            pluginStorage.originalResponse.fingerprint.content == null) {
            return
        }

        //Store the fingerprints of attacked requests in pluginstorage
        let attackedfingerprints = this.getFingerprints(attack.result.resp.httpResponse)
        _.set(pluginStorage, 'attackedResponse.fingerprint', attackedfingerprints)


        // Calculate variances based on difference of original and attack responses
        let structureVariances = 1 - pluginStorage.originalResponse.fingerprint.structure.similarity(attackedfingerprints.structure)
        let contentVariances = 1 - pluginStorage.originalResponse.fingerprint.content.similarity(attackedfingerprints.content)


        //if variation in original login response and attacked login response is less/equal to 0.1 
        // and structure of both the documents is less/equal to 0.1
        // and status code is 200 ok for both then only report the vulnerability
        if (pluginStorage.originalResponse.fingerprint.statusCode == 200 &&
            pluginStorage.attackedResponse.fingerprint.statusCode == 200 &&
            contentVariances <= this.getMetadata(attack).maxContentVariancesAllowed &&
            structureVariances <= this.getMetadata(attack).maxstructureVariancesAllowed
        ) {
            let bodycheck = attack.result.resp.body
            if (/(Invalid|Incorrect)\sUsername\sor\sPassword/i.test(bodycheck) ||
                /Login\sfailed/i.test(bodycheck)) {
                return
            }


            let vuln = {
                details: {
                    username: attack.vector.unVector,
                    password: attack.vector.pwdVector
                }
            }
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginStorageScanScope.bruteForceFound = true
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;
        }
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.bruteForceFound == true) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
        }
    }

}

//most common usernames from : 
//https://github.com/danielmiessler/SecLists/blob/master/Usernames/top-usernames-shortlist.txt
// List of top 120 usernames
// References: https://www.projecthoneypot.org/dictionary_attacker_usernames.php
// https://github.com/danielmiessler/SecLists/blob/master/Usernames/top-usernames-shortlist.txt
// https://lifehacker.com/the-top-10-usernames-and-passwords-hackers-try-to-get-i-1762638243

const BruteForceUNVectors = [
    // all the username vectors, can use IdentityVector as well
    `admin`,
    `123456`,
    `12345678`,
    `1234`,
    `Password`,
    `123`,
    `12345`,
    `admin123`,
    `123456789`,
    `adminisp`,
    `demo`,
    `root`,
    `123123`,
    `admin@123`,
    `123456aA@`,
    `1031974`,
    `Admin@123`,
    `111111`,
    `admin1234`,
    `admin1`,
    'iamjustsendingthisleter',
    'info',
    'sales',
    'buh',
    'bux',
    'direktor',
    'buhgalteria',
    'buhg',
    'finance',
    'dir',
    'buhgalter',
    'hr',
    'thisisjusttestletter',
    'sekretar',
    'contact',
    'director',
    'support',
    'petgord34truew',
    'contactus',
    'mail',
    'manager',
    'adm',
    'business',
    'job',
    'billing',
    'home',
    'account',
    'anna',
    'bank',
    'todysfunctionalanawalt',
    'avto',
    'ad',
    'office',
    'email',
    'alekse',
    'ao',
    'arhipov',
    'alla',
    'aozt',
    'aleksandrov',
    'aleksander',
    'design',
    'advert',
    'anya',
    'avdeev',
    'andreev',
    'abramov',
    'help',
    'belousov',
    'company',
    'biryukov',
    'afanasev',
    'marketing',
    'andre',
    'blinov',
    'agafonov',
    'andrey',
    'adver',
    'alexeev',
    'borisov',
    'bobrov',
    'alexe',
    'baranov',
    'anisimov',
    'boss',
    'borya',
    'postmaster',
    'blohin',
    'art',
    'bragin',
    'anthon',
    'bykov',
    'by',
    'anatol',
    'burov',
    'expert',
    'aksenov',
    'belov',
    'fabrika',
    'serviciosradio',
    'constantine',
    'alenk',
    'fin',
    'avone.w.corre',
    'consul',
    'holding',
    'designer',
    'alenka',
    'bobylev',
    'designe',
    'billin',
    'admi',
    'consult',
    'byx',
    'danil',
    'dmitriev',
    'daemon',
    'elena',
    'all',
    'username',
    'administrator',
    'user1',
    'alex',
    'pos',
    'db2admin',
    'sql',
    `root`,
    `test`,
    `guest`,
    `adm`,
    `mysql`,
    `user`,
    `oracle`,
    `ftp`,
    `pi`,
    `ec2-user`,
    `vagrant`,
    `azureuser`,
]

//most common passwords from : 
// https://github.com/danielmiessler/SecLists/blob/master/Passwords/Common-Credentials/best15.txt
// List of most common 1000 passwords
// Reference:  https://github.com/DavidWittman/wpxmlrpcbrute/blob/master/wordlists/1000-most-common-passwords.txt

const BruteForcePwdVectors = [
    // all the password vectors, can use IdentityVector as well
    `admin`,
    `123456`,
    `12345678`,
    `1234`,
    `Password`,
    `123`,
    `12345`,
    `admin123`,
    `123456789`,
    `adminisp`,
    `demo`,
    `root`,
    `123123`,
    `admin@123`,
    `123456aA@`,
    `1031974`,
    `Admin@123`,
    `111111`,
    `admin1234`,
    `admin1`,
    'qwerty',
    '1234567',
    'dragon',
    'baseball',
    'abc123',
    'football',
    'monkey',
    'letmein',
    '696969',
    'shadow',
    'master',
    '666666',
    'qwertyuiop',
    '123321',
    'mustang',
    '1234567890',
    'michael',
    '654321',
    'pussy',
    'superman',
    '1qaz2wsx',
    '7777777',
    'fuckyou',
    '121212',
    '000000',
    'qazwsx',
    '123qwe',
    'killer',
    'trustno1',
    'jordan',
    'jennifer',
    'zxcvbnm',
    'asdfgh',
    'hunter',
    'buster',
    'soccer',
    'harley',
    'batman',
    'andrew',
    'tigger',
    'sunshine',
    'iloveyou',
    'fuckme',
    '2000',
    'charlie',
    'robert',
    'thomas',
    'hockey',
    'ranger',
    'daniel',
    'starwars',
    'klaster',
    '112233',
    'george',
    'asshole',
    'computer',
    'michelle',
    'jessica',
    'pepper',
    '1111',
    'zxcvbn',
    '555555',
    '11111111',
    '131313',
    'freedom',
    '777777',
    'pass',
    'fuck',
    'maggie',
    '159753',
    'aaaaaa',
    'ginger',
    'princess',
    'joshua',
    'cheese',
    'amanda',
    'summer',
    'love',
    'ashley',
    '6969',
    'nicole',
    'chelsea',
    'biteme',
    'matthew',
    'access',
    'yankees',
    '987654321',
    'dallas',
    'austin',
    'thunder',
    'taylor',
    'matrix',
    'william',
    'corvette',
    'hello',
    'martin',
    'heather',
    'secret',
    'fucker',
    'merlin',
    'diamond',
    '1234qwer',
    'gfhjkm',
    'hammer',
    'silver',
    '222222',
    '88888888',
    'anthony',
    'justin',
    'test',
    'bailey',
    'q1w2e3r4t5',
    'patrick',
    'internet',
    'scooter',
    'orange',
    '11111',
    'golfer',
    'cookie',
    'richard',
    'samantha',
    'bigdog',
    'guitar',
    'jackson',
    'whatever',
    'mickey',
    'chicken',
    'sparky',
    'snoopy',
    'maverick',
    'phoenix',
    'camaro',
    'sexy',
    'peanut',
    'morgan',
    'welcome',
    'falcon',
    'cowboy',
    'ferrari',
    'samsung',
    'andrea',
    'smokey',
    'steelers',
    'joseph',
    'mercedes',
    'dakota',
    'arsenal',
    'eagles',
    'melissa',
    'boomer',
    'booboo',
    'spider',
    'nascar',
    'monster',
    'tigers',
    'yellow',
    'xxxxxx',
    '123123123',
    'gateway',
    'marina',
    'diablo',
    'bulldog',
    'qwer1234',
    'compaq',
    'purple',
    'hardcore',
    'banana',
    'junior',
    'hannah',
    '123654',
    'porsche',
    'lakers',
    'iceman',
    'money',
    'cowboys',
    '987654',
    'london',
    'tennis',
    '999999',
    'ncc1701',
    'coffee',
    'scooby',
    '0000',
    'miller',
    'boston',
    'q1w2e3r4',
    'fuckoff',
    'brandon',
    'yamaha',
    'chester',
    'mother',
    'forever',
    'johnny',
    'edward',
    '333333',
    'oliver',
    'redsox',
    'player',
    'nikita',
    'knight',
    'fender',
    'barney',
    'midnight',
    'please',
    'brandy',
    'chicago',
    'badboy',
    'iwantu',
    'slayer',
    'rangers',
    'charles',
    'angel',
    'flower',
    'bigdaddy',
    'rabbit',
    'wizard',
    'bigdick',
    'jasper',
    'enter',
    'rachel',
    'chris',
    'steven',
    'winner',
    'adidas',
    'victoria',
    'natasha',
    '1q2w3e4r',
    'jasmine',
    'winter',
    'prince',
    'panties',
    'marine',
    'ghbdtn',
    'fishing',
    'cocacola',
    'casper',
    'james',
    '232323',
    'raiders',
    '888888',
    'marlboro',
    'gandalf',
    'asdfasdf',
    'crystal',
    '87654321',
    '12344321',
    'sexsex',
    'golden',
    'blowme',
    'bigtits',
    '8675309',
    'panther',
    'lauren',
    'angela',
    'bitch',
    'spanky',
    'thx1138',
    'angels',
    'madison',
    'winston',
    'shannon',
    'mike',
    'toyota',
    'blowjob',
    'jordan23',
    'canada',
    'sophie',
    'apples',
    'dick',
    'tiger',
    'razz',
    '123abc',
    'pokemon',
    'qazxsw',
    '55555',
    'qwaszx',
    'muffin',
    'johnson',
    'murphy',
    'cooper',
    'jonathan',
    'liverpoo',
    'david',
    'danielle',
    '159357',
    'jackie',
    '1990',
    '123456a',
    '789456',
    'turtle',
    'horny',
    'abcd1234',
    'scorpion',
    'qazwsxedc',
    '101010',
    'butter',
    'carlos',
    'password1',
    'dennis',
    'slipknot',
    'qwerty123',
    'booger',
    'asdf',
    '1991',
    'black',
    'startrek',
    '12341234',
    'cameron',
    'newyork',
    'rainbow',
    'nathan',
    'john',
    '1992',
    'rocket',
    'viking',
    'redskins',
    'butthead',
    'asdfghjkl',
    '1212',
    'sierra',
    'peaches',
    'gemini',
    'doctor',
    'wilson',
    'sandra',
    'helpme',
    'qwertyui',
    'victor',
    'florida',
    'dolphin',
    'pookie',
    'captain',
    'tucker',
    'blue',
    'liverpool',
    'theman',
    'bandit',
    'dolphins',
    'maddog',
    'packers',
    'jaguar',
    'lovers',
    'nicholas',
    'united',
    'tiffany',
    'maxwell',
    'zzzzzz',
    'nirvana',
    'jeremy',
    'suckit',
    'stupid',
    'porn',
    'monica',
    'elephant',
    'giants',
    'jackass',
    'hotdog',
    'rosebud',
    'success',
    'debbie',
    'mountain',
    '444444',
    'xxxxxxxx',
    'warrior',
    '1q2w3e4r5t',
    'q1w2e3',
    '123456q',
    'albert',
    'metallic',
    'lucky',
    'azerty',
    '7777',
    'shithead',
    'alex',
    'bond007',
    'alexis',
    '1111111',
    'samson',
    '5150',
    'willie',
    'scorpio',
    'bonnie',
    'gators',
    'benjamin',
    'voodoo',
    'driver',
    'dexter',
    '2112',
    'jason',
    'calvin',
    'freddy',
    '212121',
    'creative',
    '12345a',
    'sydney',
    'rush2112',
    '1989',
    'asdfghjk',
    'red123',
    'bubba',
    '4815162342',
    'passw0rd',
    'trouble',
    'gunner',
    'happy',
    'fucking',
    'gordon',
    'legend',
    'jessie',
    'stella',
    'qwert',
    'eminem',
    'arthur',
    'apple',
    'nissan',
    'bullshit',
    'bear',
    'america',
    '1qazxsw2',
    'nothing',
    'parker',
    '4444',
    'rebecca',
    'qweqwe',
    'garfield',
    '01012011',
    'beavis',
    '69696969',
    'jack',
    'asdasd',
    'december',
    '2222',
    '102030',
    '252525',
    '11223344',
    'magic',
    'apollo',
    'skippy',
    '315475',
    'girls',
    'kitten',
    'golf',
    'copper',
    'braves',
    'shelby',
    'godzilla',
    'beaver',
    'fred',
    'tomcat',
    'august',
    'buddy',
    'airborne',
    '1993',
    '1988',
    'lifehack',
    'qqqqqq',
    'brooklyn',
    'animal',
    'platinum',
    'phantom',
    'online',
    'xavier',
    'darkness',
    'blink182',
    'power',
    'fish',
    'green',
    '789456123',
    'voyager',
    'police',
    'travis',
    '12qwaszx',
    'heaven',
    'snowball',
    'lover',
    'abcdef',
    '00000',
    'pakistan',
    '007007',
    'walter',
    'playboy',
    'blazer',
    'cricket',
    'sniper',
    'hooters',
    'donkey',
    'willow',
    'loveme',
    'saturn',
    'therock',
    'redwings',
    'bigboy',
    'pumpkin',
    'trinity',
    'williams',
    'tits',
    'nintendo',
    'digital',
    'destiny',
    'topgun',
    'runner',
    'marvin',
    'guinness',
    'chance',
    'bubbles',
    'testing',
    'fire',
    'november',
    'minecraft',
    'asdf1234',
    'lasvegas',
    'sergey',
    'broncos',
    'cartman',
    'private',
    'celtic',
    'birdie',
    'little',
    'cassie',
    'babygirl',
    'donald',
    'beatles',
    '1313',
    'dickhead',
    'family',
    '12121212',
    'school',
    'louise',
    'gabriel',
    'eclipse',
    'fluffy',
    '147258369',
    'lol123',
    'explorer',
    'beer',
    'nelson',
    'flyers',
    'spencer',
    'scott',
    'lovely',
    'gibson',
    'doggie',
    'cherry',
    'andrey',
    'snickers',
    'buffalo',
    'pantera',
    'metallica',
    'member',
    'carter',
    'qwertyu',
    'peter',
    'alexande',
    'steve',
    'bronco',
    'paradise',
    'goober',
    '5555',
    'samuel',
    'montana',
    'mexico',
    'dreams',
    'michigan',
    'cock',
    'carolina',
    'yankee',
    'friends',
    'magnum',
    'surfer',
    'poopoo',
    'maximus',
    'genius',
    'cool',
    'vampire',
    'lacrosse',
    'asd123',
    'aaaa',
    'christin',
    'kimberly',
    'speedy',
    'sharon',
    'carmen',
    '111222',
    'kristina',
    'sammy',
    'racing',
    'ou812',
    'sabrina',
    'horses',
    '0987654321',
    'qwerty1',
    'pimpin',
    'baby',
    'stalker',
    'enigma',
    '147147',
    'star',
    'poohbear',
    'boobies',
    '147258',
    'simple',
    'bollocks',
    '12345q',
    'marcus',
    'brian',
    '1987',
    'qweasdzxc',
    'drowssap',
    'hahaha',
    'caroline',
    'barbara',
    'dave',
    'viper',
    'drummer',
    'action',
    'einstein',
    'bitches',
    'genesis',
    'hello1',
    'scotty',
    'friend',
    'forest',
    '010203',
    'hotrod',
    'google',
    'vanessa',
    'spitfire',
    'badger',
    'maryjane',
    'friday',
    'alaska',
    '1232323q',
    'tester',
    'jester',
    'jake',
    'champion',
    'billy',
    '147852',
    'rock',
    'hawaii',
    'badass',
    'chevy',
    '420420',
    'walker',
    'stephen',
    'eagle1',
    'bill',
    '1986',
    'october',
    'gregory',
    'svetlana',
    'pamela',
    '1984',
    'music',
    'shorty',
    'westside',
    'stanley',
    'diesel',
    'courtney',
    '242424',
    'kevin',
    'porno',
    'hitman',
    'boobs',
    'mark',
    '12345qwert',
    'reddog',
    'frank',
    'qwe123',
    'popcorn',
    'patricia',
    'aaaaaaaa',
    '1969',
    'teresa',
    'mozart',
    'buddha',
    'anderson',
    'paul',
    'melanie',
    'abcdefg',
    'security',
    'lucky1',
    'lizard',
    'denise',
    '3333',
    'a12345',
    '123789',
    'ruslan',
    'stargate',
    'simpsons',
    'scarface',
    'eagle',
    '123456789a',
    'thumper',
    'olivia',
    'naruto',
    '1234554321',
    'general',
    'cherokee',
    'a123456',
    'vincent',
    'Usuckballz1',
    'spooky',
    'qweasd',
    'cumshot',
    'free',
    'frankie',
    'douglas',
    'death',
    '1980',
    'loveyou',
    'kitty',
    'kelly',
    'veronica',
    'suzuki',
    'semperfi',
    'penguin',
    'mercury',
    'liberty',
    'spirit',
    'scotland',
    'natalie',
    'marley',
    'vikings',
    'system',
    'sucker',
    'king',
    'allison',
    'marshall',
    '1979',
    '098765',
    'qwerty12',
    'hummer',
    'adrian',
    '1985',
    'vfhbyf',
    'sandman',
    'rocky',
    'leslie',
    'antonio',
    '98765432',
    '4321',
    'softball',
    'passion',
    'mnbvcxz',
    'bastard',
    'passport',
    'horney',
    'rascal',
    'howard',
    'franklin',
    'bigred',
    'assman',
    'alexander',
    'homer',
    'redrum',
    'jupiter',
    'claudia',
    '55555555',
    '141414',
    'zaq12wsx',
    'shit',
    'patches',
    'nigger',
    'cunt',
    'raider',
    'infinity',
    'andre',
    '54321',
    'galore',
    'college',
    'russia',
    'kawasaki',
    'bishop',
    '77777777',
    'vladimir',
    'money1',
    'freeuser',
    'wildcats',
    'francis',
    'disney',
    'budlight',
    'brittany',
    '1994',
    '00000000',
    'sweet',
    'oksana',
    'honda',
    'domino',
    'bulldogs',
    'brutus',
    'swordfis',
    'norman',
    'monday',
    'jimmy',
    'ironman',
    'ford',
    'fantasy',
    '9999',
    '7654321',
    'PASSWORD',
    'hentai',
    'duncan',
    'cougar',
    '1977',
    'jeffrey',
    'house',
    'dancer',
    'brooke',
    'timothy',
    'super',
    'marines',
    'justice',
    'digger',
    'connor',
    'patriots',
    'karina',
    '202020',
    'molly',
    'everton',
    'tinker',
    'alicia',
    'rasdzv3',
    'poop',
    'pearljam',
    'stinky',
    'naughty',
    'colorado',
    '123123a',
    'water',
    'test123',
    'ncc1701d',
    'motorola',
    'ireland',
    'asdfg',
    'slut',
    'matt',
    'houston',
    'boogie',
    'zombie',
    'accord',
    'vision',
    'bradley',
    'reggie',
    'kermit',
    'froggy',
    'ducati',
    'avalon',
    '6666',
    '9379992',
    'sarah',
    'saints',
    'logitech',
    'chopper',
    '852456',
    'simpson',
    'madonna',
    'juventus',
    'claire',
    '159951',
    'zachary',
    'yfnfif',
    'wolverin',
    'warcraft',
    'hello123',
    'extreme',
    'penis',
    'peekaboo',
    'fireman',
    'eugene',
    'brenda',
    '123654789',
    'russell',
    'panthers',
    'georgia',
    'smith',
    'skyline',
    'jesus',
    'elizabet',
    'spiderma',
    'smooth',
    'pirate',
    'empire',
    'bullet',
    '8888',
    'virginia',
    'valentin',
    'psycho',
    'predator',
    'arizona',
    '134679',
    'mitchell',
    'alyssa',
    'vegeta',
    'titanic',
    'christ',
    'goblue',
    'fylhtq',
    'wolf',
    'mmmmmm',
    'kirill',
    'indian',
    'hiphop',
    'baxter',
    'awesome',
    'people',
    'danger',
    'roland',
    'mookie',
    '741852963',
    '1111111111',
    'dreamer',
    'bambam',
    'arnold',
    '1981',
    'skipper',
    'serega',
    'rolltide',
    'elvis',
    'changeme',
    'simon',
    '1q2w3e',
    'lovelove',
    'fktrcfylh',
    'denver',
    'tommy',
    'mine',
    'loverboy',
    'hobbes',
    'happy1',
    'alison',
    'nemesis',
    'chevelle',
    'cardinal',
    'burton',
    'wanker',
    'picard',
    '151515',
    'tweety',
    'michael1',
    '147852369',
    '12312',
    'xxxx',
    'windows',
    'turkey',
    '456789',
    '1974',
    'vfrcbv',
    'sublime',
    '1975',
    'galina',
    'bobby',
    'newport',
    'manutd',
    'daddy',
    'american',
    'alexandr',
    '1966',
    'victory',
    'rooster',
    'qqq111',
    'madmax',
    'electric',
    'bigcock',
    'a1b2c3',
    'wolfpack',
    'spring',
    'phpbb',
    'lalala',
    'suckme',
    'spiderman',
    'eric',
    'darkside',
    'classic',
    'raptor',
    '123456789q',
    'hendrix',
    '1982',
    'wombat',
    'avatar',
    'alpha',
    'zxc123',
    'crazy',
    'hard',
    'england',
    'brazil',
    '1978',
    '01011980',
    'wildcat',
    'polina',
    'freepass',

]

module.exports = CredentialGuessing