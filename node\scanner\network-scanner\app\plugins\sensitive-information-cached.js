const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

class SensitiveInformationCached extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.sensitiveInformationCacheVulnID = 'ID-sensitive-information-cached'
        this.cacheExpirationVulnID = 'ID-cache-expired'

        const sensitiveInformationCachedHeader = ['cache-control', 'pragma', 'x-cache', 'x-cache-lookup']

        this.headersToIterate = [{
            headers: sensitiveInformationCachedHeader,
            vulnId: 'ID-sensitive-information-cached'
        }]
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    /* wantProcessAttackResponse(attack) {

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (attack.attackArea == "original-crawler-request") {
            return true
        }
        return false
    } */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        if (attack.attackArea == 'original-crawler-request' && attack.pluginName == 'Original Crawler Request') {
            let ResBody = _.get(attack, "result.resp.body", '')
            if (!ResBody.trim()) return;

            /** Avoid FP.
             * <meta http-equiv="Pragma" content="no-cache"/>: This is primarily for HTTP/1.0 clients. It instructs the browser not to cache the page.
             * <meta http-equiv="Expires" content="-1"/>: This sets the expiration date of the page to a past date, ensuring that the browser treats the page as expired and does not cache it.
             * <meta http-equiv="CacheControl" content="no-cache"/>: This is for HTTP/1.1 clients. It tells the browser to always revalidate the page with the server before displaying it.
              */
            if (/<meta http-equiv="Pragma" content="no-cache"\/>|<meta http-equiv="Expires" content="-1"\/>|<meta http-equiv="Cache-Control" content="no-cache"\/>/i.test(ResBody)) {
                return
            }
            //condition to skip for the custom error pages
            const responseBody = ResBody.toLowerCase();

            // Check each category of error messages
            const errorCategories = [
                RegExpVari.ErrorMessages.HttpErrors,
                RegExpVari.ErrorMessages.SecurityErrors,
                RegExpVari.ErrorMessages.SessionErrors,
                RegExpVari.ErrorMessages.SystemErrors,
                RegExpVari.ErrorMessages.WafErrors,
                RegExpVari.ErrorMessages.GeneralErrors
            ];

            // Early return if any error message is found
            for (const category of errorCategories) {
                if (category.some(error => responseBody.includes(error))) {
                    return;
                }
            }

            // Check for WAF servers in headers
            let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
            ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

            if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
                return;
            }
            // cheching for request specific plugin data in specific functions
            this.checkSensitiveInformationCached(attack) // sensitive information is stored in cache
            this.InsecureCacheControlHeaderDetected(attack)
        }
    }

    checkSensitiveInformationCached(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'original-crawler-request')
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {})

        let vuln = [];
        let s1 = "no-cache";
        let s2 = "no-store";
        let s3 = "max-age=0";
        let s4 = "hit";

        //adding condition to skip unwanted error codes
        let statusCodeArray = [-1, 301, 302, 400, 401, 403, 405, 409, 415, 491, 500, 501, 502, 530, 503]
        if (statusCodeArray.includes(_.get(attack, 'result.resp.httpResponse.statusCode', -1))) { return }
        let cacheControlHeaderPresent = _.get(attack, 'result.resp.httpResponse.headers[cache-control]')
        let pragmaHeaderPresent = _.get(attack, 'result.resp.httpResponse.headers[pragma]')
        let xCachePresent = _.get(attack, 'result.resp.httpResponse.headers[x-cache]', '')
        let xCacheLookupPresent = _.get(attack, 'result.resp.httpResponse.headers[x-cache-lookup]', '')

        if (cacheControlHeaderPresent || pragmaHeaderPresent || xCachePresent || xCacheLookupPresent) {
            for (let h in headers) {
                if ("cache-control" == h || "pragma" == h || "x-cache" == h || "x-cache-lookup" == h) {
                    let val = headers[h];

                    let f1 = val.toLowerCase().includes(s1.toLowerCase());
                    let f2 = val.toLowerCase().includes(s2.toLowerCase());
                    let f3 = val.toLowerCase().includes(s3.toLowerCase());
                    let f4 = val.toLowerCase().includes(s4.toLowerCase());//true if HIT in x-cache, else false
                    let f5 = val.toLowerCase().includes(s4.toLowerCase());//true if HIT in x-cache-lookup, else false


                    if (f1 || f2 || f3) {
                        return;
                    }

                    //Adding x-cache and x-cache-lookup, if either has HIT , do not report
                    if (f4 || /error/i.test(f4) || f5 || /error/i.test(f5)) {
                        return;
                    }
                    //if cache control header does not contain desired values means caching is enabled
                    // hence report the vulnerability as below
                    else {
                        //added vulns info which was not set correctly earlier
                        // Report vulns of Browser Cache enabled as below
                        vuln = {
                            details: {
                                header: h,
                                value: valṇbn
                            }
                        }
                        this.addVulnerabilitytoResult(attack, this.sensitiveInformationCacheVulnID, vuln)
                        pluginDataForRequest.sensitiveInfoCacheVulnFound = true
                    }
                }
            }
        }
        else {

            //if cache-control or pragma headers are not at all present then report the
            // vulnerability as browsers default caching comes in place here
            vuln = {
                details: {
                    msg: "No cache-control/pragma headers are present which means there is some default caching still present"
                }
            }
            this.addVulnerabilitytoResult(attack, this.sensitiveInformationCacheVulnID, vuln)
            pluginDataForRequest.sensitiveInfoCacheVulnFound = true
        }
    }

    InsecureCacheControlHeaderDetected(attack) {
        //Now this is to report if max-age is present and is set less then one day
        // or no-cache/no-store is present which means server is going to receive request 
        // that many times reducing applications performance
        let cacheControlHeaderPresent = _.get(attack, 'result.resp.httpResponse.headers[cache-control]', false)
        if (cacheControlHeaderPresent && cacheControlHeaderPresent.includes("max-age=")) {
            let parseHeaders = HaikuUtils.parseHeaderVal(cacheControlHeaderPresent)
            // Default: maxAgeLimitToVeriy: 86400
            if ((parseHeaders['max-age'] < this.getMetadata(attack).vulnerabilities['ID-cache-expired'].maxAgeLimitToVeriy)
                || cacheControlHeaderPresent.includes('no-cache') || cacheControlHeaderPresent.includes('no-store')) {
                vuln = {
                    details: {
                        header: "cache-control",
                        value: cacheControlHeaderPresent
                    }
                }
                this.addVulnerabilitytoResult(attack, this.cacheExpirationVulnID, vuln)
                pluginDataForRequest.cacheExpirationVulnFound = true
            }
        }
    }
}
module.exports = SensitiveInformationCached