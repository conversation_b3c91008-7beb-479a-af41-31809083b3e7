const debug = require('debug')('CrossOriginResourceSharing')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

class CrossOriginResourceSharing extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-cross-origin-resource-sharing'
    }

    /**
     * get array of CORS attack vectors
     * @override
     */
    getAttackVectors() {
        return CORSVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['http-headers']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['Host', 'Origin', 'Referer', 'X-Custom-Header', 'X-haiku-test']
        })
    }

    processAttackResponse(attack) {
        //if vuln detected for a req then return
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CORSVuln) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        if (statusCode >= 500) { return }

        if (attack.pluginName == this.getName() || attack.pluginName == 'Original Crawler Request') {
            let res = _.get(attack, 'result.resp.httpResponse.headers["access-control-allow-origin"]', '')
            if (/(https?:\/\/)?demo.testfire.net|\*/i.test(res)) {
                let details = {
                    name: `Possible to CORS, access-control-allow-origin: ${res}`
                }
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                pluginDataForRequest.CORSVuln = true
            }
        }
    }
}

// vectors & matches ...
const CORSVectors = [
    `demo.testfire.net`,
    `http://demo.testfire.net`,
    `https://demo.testfire.net`
]
module.exports = CrossOriginResourceSharing