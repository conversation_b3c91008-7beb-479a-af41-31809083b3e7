class IndusfaceElectron {
    constructor() {
    }

    name(short) {
        return short ? "IFC-Electron" : "Indusface Browser Electron Plugin"
    }

    init() {
        // inject methods to main context/class
        indusfaceRenderer.logToMain = this.logToMain.bind(this)       
        indusfaceRenderer.sendEventToMain = this.sendEventToMain.bind(this)

        // wire off browser blocking methods. These could be routed to main 
        // and processed there but nogt needed for now
        indusfaceRenderer.window_alert = window.alert
        indusfaceRenderer.window_prompt = window.prompt
        indusfaceRenderer.window_confirm = window.confirm
        indusfaceRenderer.window_close = window.close
        window.alert = (msg) => {
            console.log(`alert called with '${msg}'`)
            indusfaceRenderer.sendEventToMain('window.alert', msg)
        }
        window.prompt = (msg, def) => {
            console.log(`prompt called with '${msg}, ${def}'. returning ${def}`);
            indusfaceRenderer.sendEventToMain('window.prompt', msg, def)
            return def
        }
        window.confirm = (msg) => {
            console.log(`confirm called with '${msg}'. returning true`);
            indusfaceRenderer.sendEventToMain('window.confirm', msg)
            return true
        }
        window.print = () => {
            console.log(`print() called, ignoring`);
            indusfaceRenderer.sendEventToMain('window.print')
        }
        window.close = () => {
            console.log(`close() called, ignoring`);
            indusfaceRenderer.sendEventToMain('window.close')
        }

        //debug helper IPC events
        indusfaceRenderer.ipcRenderer.on('log-it-b64', function (event, args) {
            indusfaceRenderer.log('log-it(b64) from main: ', args, ' => ',atob(args))
        })
        indusfaceRenderer.ipcRenderer.on('log-it', function (event, args) {
            indusfaceRenderer.log('log-it from main: ', args)
        })
    }

    sendEventToMain(event) {
        let args = [...arguments].slice(1)      // skip event
        indusfaceRenderer.ipcRenderer.send(event, indusfaceRenderer.scope.windowId, args)
    }

    logToMain() {
        indusfaceRenderer.sendEventToMain('renderer-log', arguments)
    }
}

module.exports = IndusfaceElectron