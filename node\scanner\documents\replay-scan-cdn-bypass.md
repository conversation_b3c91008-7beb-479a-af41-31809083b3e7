# PRD: CDN Bypass for Replay Scans

## 1. Feature Name
CDN Bypass for Replay Scans

## 2. Introduction / Problem Statement (Why are we doing it?)
Currently, our replay scan process interacts with sites as any regular user would, meaning traffic for CDN-enabled sites passes through the CDN. While our WAF protection rules effectively block attack payloads at the origin/WAF, the CDN can cache legitimate (but pre-patch or pre-block) responses.

When a replay scan is conducted shortly after an attack or a rule update, the vulnerability scanner may receive a cached, vulnerable response from the CDN. This occurs even if the WAF has already blocked the malicious activity or if the vulnerability has been patched at the origin. This leads to the scanner incorrectly reporting an unpatched vulnerability until the CDN cache for that specific resource expires or is purged.

This discrepancy results in false positive vulnerability reports for CDN-enabled sites, undermining confidence in the scan results and misrepresenting the true, immediate security posture provided by our WAF.

## 3. Proposed Solution (What are we doing?)
We are enhancing the replay scan process to intelligently bypass the Content Delivery Network (CDN) for designated sites. This will allow the scanner to directly interact with the Web Application Firewall (WAF) block IP address assigned to the site.

By directing scan traffic to the WAF's IP, we ensure that the scanner assesses the application's response as filtered and protected by the most current WAF rules, eliminating the influence of potentially stale CDN caches.

## 4. Goals / Success Criteria (What will success look like?)
Success for this feature will be characterized by:

*   **Accurate Scan Results:** Vulnerability scan results for sites utilizing a CDN will accurately reflect the immediate protection provided by our WAF rules, even when replay scans are conducted shortly after an attack or rule deployment.
*   **Reduced False Positives:** A noticeable reduction in false positive vulnerability reports specifically for CDN-enabled sites where the WAF is already blocking attack payloads.
*   **Improved Confidence:** Increased confidence in the vulnerability scan results for CDN-protected sites, providing a true representation of their security posture in relation to WAF protections.
*   **Elimination of Discrepancy:** The elimination of the issue where the WAF is actively blocking attacks, but the scanner still reports a vulnerability due to CDN caching for CDN-protected sites.

## 5. Technical Implementation Details

The core of this feature involves modifying how the scanner resolves the target URL's hostname for specific replay scans.

### 5.1. New Configuration Parameters
During a replay scan, two new properties will be sent for CDN-enabled sites that require this bypass:

*   `is` (boolean): When `true`, indicates that the site is CDN-enabled and the bypass logic should be considered.
*   `wafBlockName` (string): The specific hostname designated for the WAF block (e.g., `SaaS-MUM-BLK-112.apptrana.com`). This is the address that resolves to the WAF's direct IP(s).

These parameters will be part of the `replayScanInfo` object in the site configuration, for example:
```json
"replayScanInfo": {
    "isReplayScan": true,
    // ... other parameters ...
    "wafBlockName": "SaaS-MUM-BLK-112.apptrana.com",
    "isCdnEnabled": true
    // ...
}
```

### 5.2. Logic Flow
1.  **Check Parameters:** At the initiation of a replay scan, the scanner will check if `isCdnEnabled` is `true` and `wafBlockName` is provided and non-empty.
2.  **NSLookup `wafBlockName`:** If both conditions are met, the scanner will perform an `nslookup` (or equivalent DNS resolution) on the provided `wafBlockName`.
    *   Example: `nslookup SaaS-MUM-BLK-112.apptrana.com`
    *   This lookup is expected to return one or more IP addresses directly associated with the WAF. (Refer to the user-provided image for an example of `nslookup` output).
3.  **Select WAF IP:** If multiple IP addresses are returned, the scanner will use the first valid IP address resolved.
4.  **Identify Target Hostname:** The scanner will parse the main scan URL (e.g., `https://demo.testfire.net/login.jsp`) to extract the base hostname (e.g., `demo.testfire.net`).
5.  **Host File Modification:** The scanner will temporarily modify its host resolution mechanism (e.g., by updating the system's hosts file or using an in-memory DNS override) to map the target hostname (and its `www.` prefixed variant) to the selected WAF IP address.
    *   **Previous Behavior:** "In replay scan scanner do not update host entry & remove if any existing entry present."
    *   **New Behavior (for this feature only):** If `isCdnEnabled: true` and `wafBlockName` is set, host entries WILL be added for the duration of the scan.
    *   Example: If `wafBlockName` resolves to `************` and the scan URL's hostname is `finpartner.adityabirlacapital.com`:
        ```
        ************ finpartner.adityabirlacapital.com
        ************ www.finpartner.adityabirlacapital.com
        ```
        If the scan URL's hostname is `demo.testfire.net`:
        ```
        ************ demo.testfire.net
        ************ www.demo.testfire.net
        ```
6.  **Execute Scan:** The replay scan proceeds with these temporary host resolution overrides, ensuring traffic to the target domain is routed directly to the WAF IP.
7.  **Revert Host File Changes:** Upon completion or termination of the scan, the scanner MUST revert any changes made to the host resolution mechanism, restoring it to its original state.

### 5.3. Considerations
*   **Permissions:** The scanner process will require sufficient permissions to modify host resolution (e.g., write access to the hosts file).
*   **Atomicity & Cleanup:** Host file modifications must be managed carefully to ensure they are always reverted, even if the scan terminates unexpectedly. This is crucial to avoid unintended network routing issues post-scan.
*   **Error Handling:**
    *   If `wafBlockName` is missing or `isCdnEnabled` is false, the scan proceeds without CDN bypass (i.e., default replay scan behavior).
    *   If `nslookup` of `wafBlockName` fails to resolve an IP address, the scan should log this issue specifically for tracking and proceed with the default replay scan behavior (i.e., without updating any host entries and not treating it as a scan setup error).
*   **Scope of Host File Change:** The modification should only affect the current scan process if possible, or be system-wide but strictly temporary.
*   **Multiple WAF IPs:** The current approach is to use the first IP returned from `nslookup`. Further enhancements could involve strategies for selecting among multiple IPs (e.g., round-robin, health checks), but this is out of scope for the initial implementation.
