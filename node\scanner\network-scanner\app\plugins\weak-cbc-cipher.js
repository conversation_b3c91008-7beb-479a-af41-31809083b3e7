const NetworkAttack = require('./network-attack')
const tls = require('tls');
const _ = require('lodash')

class WeakCBCCipherVuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.CBCCiphersVulneranility = 'ID-CBC-Ciphers-found'
    }

    async processAttackResponse(attack) {
        //Verify below only once per scan
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (attack.attackArea != "original-crawler-request") {
            return
        }

        let parsedUrl = new URL(attack.httpRequest.uri)
        if (parsedUrl.protocol != 'https:') {
            return
        }

        //if vuln detected for a req then return
        if (pluginDataForRequest.CBCCiphers) {
            return
        }
        // List of all ciphers supported by OpenSSL
        // const cipher = tls.getCiphers();
        let protocolList = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2']
        //Find protocol
        let protocol = []
        for (let val of protocolList) {
            const options = {
                host: attack.hostname, // Replace with the target host
                port: 443, // Standard HTTPS port
                method: 'GET',
                rejectUnauthorized: false, // Allow self-signed certificates
                // You can specify the TLS version here
                minVersion: val,
                maxVersion: val,
                servername: attack.hostname, // This is where you specify the SNI
                // ciphers: ele
            };
            let sslInfo = await this.alldts(options)
            if (sslInfo && sslInfo != 'Not Found' && sslInfo.Version === val) {
                protocol.push(val)
            }
        }
        // let protocol = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3']
        let certInfo = []
        if (protocol.length > 0) {
            for (let val of protocol) {
                if (val === 'SSLv3') {
                    let count = 0
                    let cipher = [
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_DES_CBC_SHA',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'SSL_RSA_WITH_ARIA_128_CBC_SHA',
                        'SSL_RSA_WITH_ARIA_256_CBC_SHA',
                    ]
                    let reason = [
                        'Certain cipher suites are vulnerable due to various reasons: 3DES is prone to meet-in-the-middle attacks and has a small key size; DHE key exchange is susceptible to the Raccoon Attack; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'DES is weak due to its small key size and susceptibility to brute-force attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites are vulnerable because they use CBC mode, which can be exploited by the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites are vulnerable because they use CBC mode, which can be exploited by the GOLDENDOODLE attack to decrypt sensitive information',
                        'IDEA is considered outdated and less secure compared to modern algorithms',
                        'SSL 3.0 is vulnerable to the POODLE attack, which exploits CBC mode padding.',
                        'SSL 3.0 is vulnerable to the POODLE attack, which exploits CBC mode padding.',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1') {
                    let count = 0
                    let cipher = [
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_DES_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384',
                        'TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256',
                        'TLS_RSA_WITH_ARIA_128_CBC_SHA',
                        'TLS_RSA_WITH_ARIA_256_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_ARIA_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_ARIA_256_CBC_SHA',
                    ]
                    let reason = [
                        'Certain cipher suites are vulnerable due to various reasons: 3DES is prone to meet-in-the-middle attacks and has a small key size; DHE key exchange is susceptible to the Raccoon Attack; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Logjam and Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Logjam and Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'DES is weak due to its small key size and susceptibility to brute-force attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST3 and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Similar to AES_128_CBC_SHA, it is vulnerable to padding oracle attacks and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to CAMELLIA_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        'SEED is not widely supported and has known vulnerabilities in its implementation.',
                        'which is susceptible to attacks like the Lucky Thirteen and POODLE. These attacks exploit weaknesses in the way CBC mode handles padding, leading to potential security breaches',
                        'which is susceptible to attacks like the Lucky Thirteen and POODLE. These attacks exploit weaknesses in the way CBC mode handles padding, leading to potential security breaches',
                        'SHA-1 Vulnerability, SHA-1 is vulnerable to collision attacks, making it insecure for message authentication.',
                        'SHA-1 Vulnerability, SHA-1 is weaknesses make it unsuitable for secure communications.',
                        'SHA-1 Vulnerability, ECDHE with SHA-1 is insecure due to the weaknesses of SHA-1.',
                        'SHA-1 Vulnerability, The use of SHA-1 in this suite makes it vulnerable to attacks.',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1.1') {
                    let count = 0
                    let cipher = [
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384',
                        'TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256',
                        'TLS_RSA_WITH_ARIA_128_CBC_SHA',
                        'TLS_RSA_WITH_ARIA_256_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_ARIA_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_ARIA_256_CBC_SHA',
                    ]
                    let reason = [
                        'Certain cipher suites are vulnerable due to various reasons: 3DES is prone to meet-in-the-middle attacks and has a small key size; DHE key exchange is susceptible to the Raccoon Attack; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Logjam and Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Logjam and Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST3 and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Similar to AES_128_CBC_SHA, it is vulnerable to padding oracle attacks and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to CAMELLIA_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        'SEED is not widely supported and has known vulnerabilities in its implementation.',
                        'which is susceptible to attacks like the Lucky Thirteen and POODLE. These attacks exploit weaknesses in the way CBC mode handles padding, leading to potential security breaches',
                        'which is susceptible to attacks like the Lucky Thirteen and POODLE. These attacks exploit weaknesses in the way CBC mode handles padding, leading to potential security breaches',
                        'SHA-1 Vulnerability, SHA-1 is vulnerable to collision attacks, making it insecure for message authentication.',
                        'SHA-1 Vulnerability, SHA-1 is weaknesses make it unsuitable for secure communications.',
                        'SHA-1 Vulnerability, ECDHE with SHA-1 is insecure due to the weaknesses of SHA-1.',
                        'SHA-1 Vulnerability, The use of SHA-1 in this suite makes it vulnerable to attacks.',]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1.2') {
                    let count = 0
                    let cipher = [
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_128_CBC_SHA256',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_CAMELLIA_256_CBC_SHA256',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA384',
                        'TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA256',
                    ]

                    let reason = [
                        'Certain cipher suites are vulnerable due to various reasons: 3DES is prone to meet-in-the-middle attacks and has a small key size; DHE key exchange is susceptible to the Raccoon Attack; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Logjam and Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'Certain cipher suites are vulnerable due to various reasons: DHE key exchange is susceptible to the Logjam and Raccoon attacks, and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Similar to AES_128_CBC_SHA256, it is vulnerable to padding oracle attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size; and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST3 and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'Similar to AES_128_CBC_SHA, it is vulnerable to padding oracle attacks and CBC mode can be exploited by both the GOLDENDOODLE and POODLE attacks.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to CAMELLIA_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        'SEED is not widely supported and has known vulnerabilities in its implementation.',
                        'which is susceptible to attacks like the Lucky Thirteen and POODLE. These attacks exploit weaknesses in the way CBC mode handles padding, leading to potential security breaches',
                        'which is susceptible to attacks like the Lucky Thirteen and POODLE. These attacks exploit weaknesses in the way CBC mode handles padding, leading to potential security breaches',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            ciphers: ele,
                            servername: attack.hostname, // This is where you specify the SNI
                            // ciphers: 'ALL', // Specify all ciphers
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found') {
                            if (sslInfo.Cipher.standardName === options.ciphers) {
                                certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                            }
                        }
                        count++;
                    }
                }
            }
        }
        if (certInfo.length > 0) {
            this.addVulnerabilitytoResult(attack, this.CBCCiphersVulneranility, certInfo)
            pluginDataForRequest.CBCCiphers = true
        }
        else {
            // checked - avoid repeating the check
            pluginDataForRequest.CBCCiphers = true
        }
    }

    alldts(options) {
        return new Promise((resolve) => {
            try {
                // Create a TLS connection
                const socket = tls.connect(options, () => {
                    const tlsInfo = {
                        // Certificate: socket.getPeerCertificate(true),
                        // Version: socket.getProtocol(),
                        Cipher: socket.getCipher(),
                        IPaddress: socket.remoteAddress,
                        // ServerName: socket.servername,
                        // RemoteFamily: socket.remoteFamily
                    }
                    socket.end();
                    resolve(tlsInfo)
                });

                // Handle errors
                socket.on('error', (err) => {
                    resolve('Not Found');
                });
            }
            catch (e) {
                resolve('Not Found')
            }
        });
    }
}

module.exports = WeakCBCCipherVuln