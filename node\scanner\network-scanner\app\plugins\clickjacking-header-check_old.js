const debug = require('debug')('ClickjackingHeaderCheck')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

/** 
 * Passive plugin that checks if clickjacking protection using the X-Frame-Options/Content-Security-Policy 
 * header is enabled on a page
 */
class ClickjackingHeaderCheck extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the ClickjackingHeaderCheck plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-clickjacking-header-check'
        this.XPermittedCDP = 'ID-X-Permitted-CDP'
    }

    /* wantProcessAttackResponse(attack) {
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')

        let ResBody = _.get(attack, 'result.resp.body', '')
        
        //Adding condition to skip for the custom error pages
        let CustomErrMsg1 = RegExpVari.RegExp.CustomErrMsg1
        let CustomErrMsg2 = RegExpVari.RegExp.CustomErrMsg2
        let CustomErrMsg3 = RegExpVari.RegExp.CustomErrMsg3
        let CustomErrMsg4 = RegExpVari.RegExp.CustomErrMsg4
        let CustomErrMsg5 = RegExpVari.RegExp.CustomErrMsg5
        if (CustomErrMsg1.test(ResBody) || CustomErrMsg2.test(ResBody) || CustomErrMsg3.test(ResBody) || CustomErrMsg4.test(ResBody) || CustomErrMsg5.test(ResBody)) { return false }

        let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        let CType = /(application\/json|text\/(?:plain|xml))/i

        // Avoid FP
        if (statusCode == 404 && /URL(?: that)? No Longer Exists/i.test(ResBody)) {
            return false
            // A.A. Unique ID: 4237468 - https://was.indusface.com/admin/alerts/adminGeneratePochtml/173030628/61666c73c5A04a5b1198993290a787be8
        }

        if (ResBody.length < 30 && !/[<>]/.test(ResBody) && !/=\s?('|")(\/\w|http)/.test(ResBody)) {
            return false
        }

        if ((/302|400|401|403|418/.test(statusCode) || statusCode >= 500 || redirect != 0) && !CType.test(RescontentType)) {
            return false
        }

        if (attack.attackArea != 'original-crawler-request' && statusCode == 404 && /text\/html/i.test(RescontentType)) {
            try {
                let regexp = new RegExp(_.escapeRegExp(attack.vector))
                if (!regexp.test(ResBody) || !ResBody.includes('<script>haikumsg(326)</script>')) {
                    return false
                }
            } catch (e) { return false }

        }

        //API - As per CS team comment dont report status code is 404(said FP)
        // if (CType.test(RescontentType) && (/302|400|401|403|404|418/.test(statusCode) || statusCode >= 500 || redirect != 0)) {
        if (CType.test(RescontentType)) {
            return false
        }
        return true
    } */

    /**
     * Will check that X-Frame-Options header and/or Content-Security-Policy header is set
     * X-Frame-Options header has to be set to one of the allowed values: DENY, SAMEORIGIN, ALLOW-FROM
     * See: {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Frame-Options}
     * 
     * Content-Security-Policy Header should have frame-ancestors set
     * See: {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/frame-ancestors}
     * 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //Plugin request scan scope - this scan, means to report one only in entire scan
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginDataForRequest.clickjackingVulnFound && pluginDataForRequest.XpcdpHdrVulnFound) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')

        let ResBody = _.get(attack, 'result.resp.body', '')
        if (!ResBody.trim()) { return }

        //Adding condition to skip for the custom error pages
        let CustomErrMsg1 = RegExpVari.RegExp.CustomErrMsg1
        let CustomErrMsg2 = RegExpVari.RegExp.CustomErrMsg2
        let CustomErrMsg3 = RegExpVari.RegExp.CustomErrMsg3
        let CustomErrMsg4 = RegExpVari.RegExp.CustomErrMsg4
        let CustomErrMsg5 = RegExpVari.RegExp.CustomErrMsg5
        if (CustomErrMsg1.test(ResBody) || CustomErrMsg2.test(ResBody) || CustomErrMsg3.test(ResBody) || CustomErrMsg4.test(ResBody) || CustomErrMsg5.test(ResBody)) { return }

        let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        if (/^(application\/json|text\/plain|application\/octet-stream|multipart\/form-data|application\/x-www-form-urlencoded|audio\/.*|video\/.*|image\/(?!svg\+xml).*|font\/.*)$/i.test(RescontentType)) {
            return
        }

        // Avoid FP
        if (statusCode == 404 && /URL(?: that)? No Longer Exists/i.test(ResBody)) {
            return
            // A.A. Unique ID: 4237468 - https://was.indusface.com/admin/alerts/adminGeneratePochtml/173030628/61666c73c5A04a5b1198993290a787be8
        }

        if (ResBody.length < 30 && !/[<>]/.test(ResBody) && !/=\s?('|")(\/\w|http)/.test(ResBody)) {
            return
        }

        if (/302|400|401|403|418/.test(statusCode) || statusCode >= 500 || redirect != 0) {
            return
        }

        if (attack.attackArea != 'original-crawler-request' && statusCode == 404 && /text\/html/i.test(RescontentType)) {
            try {
                if (attack.vector && attack.vector.length > 0) {
                    let regexp = new RegExp(_.escapeRegExp(attack.vector))
                    if (!regexp.test(ResBody) || !ResBody.includes('<script>haikumsg(326)</script>')) {
                        return
                    }
                }
            } catch (e) { return }

        }

        //API - As per CS team comment dont report status code is 404(said FP)
        // if (CType.test(RescontentType) && (/302|400|401|403|404|418/.test(statusCode) || statusCode >= 500 || redirect != 0)) {
        

        /**
        * check pathname for avoid duplicate alert
        * https://corporate.hdfcbank.com/wp-content/plugins/jsmol2wp/php/jsmol.php?isform=true&call=getRawDataFromDatabase&query=php://filter/resource=../../../../wp-config.php
        * https://los.hdfcbank.com/owa/auth/x.js
        */

        let uri = new URL(attack.href)
        //RegExp: pathname created by haiku for checking vuln
        let pathname_RegExp = /\/jsmol2wp\/php\/jsmol\.php|\/owa\/auth\/x\.js|\/DSWAPutTest\.txt/i
        if (pathname_RegExp.test(uri.pathname) && pluginStorageScanScope.duplicatepathname && pluginStorageScanScope.XpcdpHdrVulnFound) {
            return
        }

        // Adding the condition to make sure that Host values are same in the original and attack requests
        let att_host = _.get(attack, 'httpRequest.headers.Host')
        let orig_host = _.get(attack, 'originalRequest.httpRequest.headers.Host')
        // clickjacking to be only tested for either get/post method and status code not 400
        if (att_host == orig_host && statusCode != 400 && statusCode < 500 && redirect == 0 && (/get/i.test(attack.httpRequest.method) || /post/i.test(attack.httpRequest.method))) {
            let headers = _.get(attack, 'result.resp.httpResponse.headers', {})

            if (!pluginDataForRequest.clickjackingVulnFound && !pluginStorageScanScope.duplicatepathname) {
                //Dont report more then one 404 - AA Alert
                if (pluginStorageScanScope.duplicatealert404) { return }

                // see if X-Frame-Options or Content-Security-Policy header is set and one of the allowed values
                let xfoHdrVal = headers['x-frame-options']
                let cspHdrVal = headers['content-security-policy']
                let vuln = {
                    // X-Frame-Options
                    xfoHdrValue: xfoHdrVal,
                    xfoHdrValid: /DENY|SAMEORIGIN|ALLOW-FROM/i.test(xfoHdrVal),
                    xfoHdrPresent: !!(xfoHdrVal && xfoHdrVal.length > 0),

                    //Content-Security-Policy
                    cspHdrValue: cspHdrVal,
                    cspHdrValid: /frame-ancestors|frame-src/i.test(cspHdrVal),
                    cspHdrPresent: !!(cspHdrVal && cspHdrVal.length > 0)
                }

                // see if we found a clickjacking vuln.
                let vulnFound = (!vuln.xfoHdrPresent && !vuln.cspHdrPresent) || // both headers absent
                    ((vuln.xfoHdrPresent && vuln.cspHdrPresent) && (!vuln.xfoHdrValid && !vuln.cspHdrValid)) || //XFO and CSP headers present but both are invalid
                    (vuln.xfoHdrPresent && !vuln.xfoHdrValid && !vuln.cspHdrPresent) || // XFO header present but not valid and CSP header absent 
                    (vuln.cspHdrPresent && !vuln.cspHdrValid && !vuln.xfoHdrPresent)// CSP header present but not valid and XFO header absent

                /**
                 * The below checking was disabled on 15th feb - waiting for CSP implementation 
                 * https://portswigger.net/web-security/clickjacking                     
                 * Two mechanisms for server-side clickjacking protection are X-Frame-Options and Content Security Policy. 
                // Our plugin is updated according to the manualPT report
                if (!vulnFound && (vuln.xfoHdrPresent == false || vuln.cspHdrPresent == false)) {
                    vulnFound = true
                }*/

                // At some point, can decide if we should go as far as to check if both are present and inconsistent
                // probably that would be taking it too far though.
                if (vulnFound) {
                    pluginDataForRequest.clickjackingVulnFound = true

                    // add this vulnerability to list of vulnerabilities.
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)

                    //For checking duplicate alert
                    if (pathname_RegExp.test(uri.pathname)) {
                        pluginStorageScanScope.duplicatepathname = true
                    }
                    if (statusCode == 404) {
                        pluginStorageScanScope.duplicatealert404 = true
                    }
                }
            }

            //X-Permitted-Cross-Domain-Policies: none
            if (!pluginDataForRequest.XpcdpHdrVulnFound && !pluginStorageScanScope.XpcdpHdrVulnFound) {
                //x-permitted-cross-domain-policies:none
                let XpcdpHdrVal = _.get(attack, 'result.resp.httpResponse.headers.x-permitted-cross-domain-policies', '')
                if (!XpcdpHdrVal || /all/i.test(XpcdpHdrVal)) {
                    let details = { result: "Susceptible to resource abuse" }
                    this.addVulnerabilitytoResult(attack, this.XPermittedCDP, details)
                    pluginDataForRequest.XpcdpHdrVulnFound = true
                    //For checking duplicate alert
                    if (pathname_RegExp.test(uri.pathname)) {
                        pluginStorageScanScope.XpcdpHdrVulnFound = true
                    }
                }
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "Host"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["x-frame-options", "content-security-policy"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

module.exports = ClickjackingHeaderCheck