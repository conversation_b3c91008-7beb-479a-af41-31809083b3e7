const fs = require('fs')
const csvStringify = require('csv-stringify/lib/sync')

function simplifyLogs(x) {
    let res = {
        timestamp: new Date(x.timestamp),
        scanId: x.scanId,
        message: "skip",
        tokens: '-'
    }

    if (x.message.startsWith('sending request:')) {
        res.message = 'msgSent'
    }
    if (x.message.startsWith('Request finished:')) {
        res.message = 'msgComplete'
    }
    if (x.message.startsWith('scan tokens')) {
        res.message = 'addTokens'
        res.tokens = x.message.split('= ')[1]
    }

    return res
}

console.log(`processing file: ${process.argv[2]}`)
let haikuLog = JSON.parse(fs.readFileSync(process.argv[2])).map(simplifyLogs).filter(x => x.message != 'skip')
let tokens = 0
let inFlightReqs = 0
let basetime = haikuLog[0].timestamp
for (let req of haikuLog) {
    switch (req.message) {
        case 'addTokens':
            tokens = req.tokens
            break;
        case 'msgSent':
            tokens--
            inFlightReqs++
            break;
        case 'msgComplete':
            inFlightReqs--
            break;
    }
    req.tokens = tokens
    req.inFlightReqs = inFlightReqs
}


// Excel uses very defined formats while parsing csv. 
function toExcelParsableDate(d) {
    return d.toISOString().replace(/T/, ' ').replace(/\..+/, '')
}
const csvOptions = {
    header: true,
    cast: {
        date: toExcelParsableDate
    }
}
fs.writeFileSync(`./reqsPerMin.csv`, csvStringify(haikuLog, csvOptions))