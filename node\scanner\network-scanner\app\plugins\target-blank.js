// const cheerio = require('cheerio');
const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class TargetBlank extends NetworkAttack {
  constructor(networkScanner, config) {
    super(networkScanner, config)
    this.vulnerabilityID = 'ID-target-blank'
    this.Unsafethirdpartylink = 'ID-target-blank-low'
  }

  wantProcessAttackResponse(attack) {
    if (attack.attackArea == "original-crawler-request") {
      let ResBody = _.get(attack, "result.resp.body", '')
      if (/<\s*a[^<>]+target\s*=\s*['"]_blank/i.test(ResBody)) {
        return true
      }
    }
    return false
  }

  processAttackResponse(attack) {
    let pluginDataForRequest = this.getPluginScopedStore(attack)
    if (pluginDataForRequest.TargetBlankvulnFound) {
      return
    }
    let details = []
    let dts_low = []
    let ResBody = _.get(attack, "result.resp.body", '')
    let atag = ResBody.match(/<a[^<>]+target\s*=\s*['"]_blank[^<>]+>/gi)
    if (atag && atag.length > 0 && /href="https?:/i.test(atag)) {
      /** In web development, subdomains are treated as separate entities, so linking to a different subdomain is considered linking to a third-party URL. */
      for (let relAttr of atag) {
        if (relAttr.includes('http')) {
          let ahref = new URL(relAttr.match(/https?:\/\/[^"]+"/gi)[0].replace('"', ''))
          if (ahref != null && !ahref.hostname.includes(attack.hostname)) {
            if (!relAttr.includes('rel=')) {
              details.push({ result: `rel attribute is missing: ${relAttr}` })
            }
            else if (!relAttr.includes('noopener') && !relAttr.includes('noreferrer') && !relAttr.includes('linkRelValue')) {
              details.push({ result: `noopener and noreferrer is missing: ${relAttr}` })
            }
            else if (!relAttr.includes('noopener') && !relAttr.includes('linkRelValue')) {
              // When rendered, if linkRelValue is set to "noopener"
              dts_low.push({ result: `noopener is missing: ${relAttr}` })
            }
            else if (!relAttr.includes('noreferrer')) {
              dts_low.push({ result: `noreferrer is missing: ${relAttr}` })
            }
          }
        }
      }
      if (details.length > 0) {
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
        pluginDataForRequest.TargetBlankvulnFound = true
      }
      if (dts_low.length > 0) {
        this.addVulnerabilitytoResult(attack, this.Unsafethirdpartylink, dts_low)
        pluginDataForRequest.TargetBlankvulnFound = true
      }
    }
  }
}
module.exports = TargetBlank

/* const detectUnsafeLinks = (htmlContent) => {
  const $ = cheerio.load(htmlContent);
  const unsafeLinks = [];

  $('a[target="_blank"]').each(function () {
    const relAttr = $(this).attr('rel');
    if (!relAttr || !relAttr.includes('noopener') || !relAttr.includes('noreferrer')) {
      unsafeLinks.push($(this).attr('href'));
    }
  });

  return unsafeLinks;
};

// Usage:
const htmlContent = '...'; // Your HTML content
const unsafeLinks = detectUnsafeLinks(htmlContent);
console.log('Unsafe links:', unsafeLinks);
 */