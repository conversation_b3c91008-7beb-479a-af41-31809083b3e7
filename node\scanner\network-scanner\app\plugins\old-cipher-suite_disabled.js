const NetworkAttack = require('./network-attack')
const tls = require('tls');
const _ = require('lodash')
const https = require('https');
const { exec } = require('child_process');

class oldCipherVuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-ssl-old-version'
        this.oldCipherVulnerabilityID = 'ID-old-ciphers-found'
        this.CBCCiphersVulneranility = 'ID-CBC-Ciphers-found'
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    async processAttackResponse(attack) {
        //Verify below only once per scan
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (attack.attackArea != "original-crawler-request") {
            return
        }

        let parsedUrl = new URL(attack.httpRequest.uri)
        if (parsedUrl.protocol != 'https:') {
            return
        }

        //if vuln detected for a req then return
        if (pluginDataForRequest.sslFound && pluginDataForRequest.oldCiphers && pluginDataForRequest.CBCCiphers) {
            return
        }
        // List of all ciphers supported by OpenSSL
        // const cipher = tls.getCiphers();
        let protocolList = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3']
        //Find protocol
        let protocol = []
        for (let val of protocolList) {
            const options = {
                host: attack.hostname, // Replace with the target host
                port: 443, // Standard HTTPS port
                method: 'GET',
                rejectUnauthorized: false, // Allow self-signed certificates
                // You can specify the TLS version here
                minVersion: val,
                maxVersion: val,
                // ciphers: ele
            };
            let sslInfo = await this.alldts(options)
            if (sslInfo && sslInfo != 'Not Found') {
                protocol.push(val)
            }
        }
        // let protocol = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3']
        let certInfo = []
        if (protocol.length > 0) {
            for (let val of protocol) {
                if (val === 'SSLv3') {
                    let count = 0
                    let cipher = [
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_EXPORT_WITH_RC4_40_MD5',
                        'TLS_RSA_EXPORT_WITH_DES40_CBC_SHA',
                        'TLS_DHE_RSA_WITH_DES_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA']
                    let reason = [
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Provides no encryption, making it completely insecure.',
                        'Provides no encryption and uses MD5, which is cryptographically broken.',
                        'Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        'Export-grade ciphers are intentionally weakened and provide only 40 bits of security.',
                        'Uses DES with a 40-bit key, which is easily breakable.',
                        'DES is weak due to its small key size and susceptibility to brute-force attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.']
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found') {
                            certInfo.push({ result: `Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1') {
                    let count = 0
                    let cipher = [
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_EXPORT_WITH_RC4_40_MD5',
                        'TLS_RSA_EXPORT_WITH_DES40_CBC_SHA',
                        'TLS_DHE_RSA_WITH_DES_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                    ]
                    let reason = [
                        'RC4 is considered insecure due to biases in its output, making it susceptible to various attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to AES_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to Logjam attack due to weak Diffie-Hellman parameters.',
                        'Vulnerable to Logjam attack due to weak Diffie-Hellman parameters.',
                        'Provides no encryption, making it completely insecure.',
                        'Provides no encryption and uses MD5, which is cryptographically broken.',
                        'Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'SEED is not widely supported and has known vulnerabilities in its implementation.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to CAMELLIA_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        'Export-grade ciphers are intentionally weakened and provide only 40 bits of security.',
                        'Uses DES with a 40-bit key, which is easily breakable.',
                        'DES is weak due to its small key size and susceptibility to brute-force attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Uses RC4, which is weak and susceptible to biases.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Uses RC4, which is weak and susceptible to biases.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found') {
                            certInfo.push({ result: `Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1.1') {
                    let count = 0
                    let cipher = [
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                    ]
                    let reason = [
                        'RC4 is considered insecure due to biases in its output, making it susceptible to various attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to AES_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to Logjam attack due to weak Diffie-Hellman parameters.',
                        'Vulnerable to Logjam attack due to weak Diffie-Hellman parameters.',
                        'Provides no encryption, making it completely insecure.',
                        'Provides no encryption and uses MD5, which is cryptographically broken.',
                        'Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'SEED is not widely supported and has known vulnerabilities in its implementation.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to CAMELLIA_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Uses RC4, which is weak and susceptible to biases.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Uses RC4, which is weak and susceptible to biases.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found') {
                            certInfo.push({ result: `Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1.2') {
                    let count = 0
                    let cipher = [
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256',
                        'TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384',
                        'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                    ]
                    let reason = [
                        'RC4 is considered insecure due to biases in its output, making it susceptible to various attacks.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to AES_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to BEAST and Lucky 13 attacks.',
                        'Vulnerable to Logjam attack due to weak Diffie-Hellman parameters.',
                        'Vulnerable to Logjam attack due to weak Diffie-Hellman parameters.',
                        'Provides no encryption, making it completely insecure.',
                        'Provides no encryption and uses MD5, which is cryptographically broken.',
                        'Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'SEED is not widely supported and has known vulnerabilities in its implementation.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to CAMELLIA_128_CBC_SHA, it is vulnerable to padding oracle attacks.',
                        'IDEA is considered outdated and less secure compared to modern algorithms.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'Uses RC4, which is weak and susceptible to biases.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                        'CBC mode is vulnerable to padding oracle attacks like BEAST.',
                        'Similar to AES_128_CBC_SHA256, it is vulnerable to padding oracle attacks.',
                        'Uses RC4, which is weak and susceptible to biases.',
                        '3DES is vulnerable to meet-in-the-middle attacks and has a relatively small key size.',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found') {
                            certInfo.push({ result: `Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
            }
        }
        if (certInfo.length > 0) {
            this.addVulnerabilitytoResult(attack, this.oldCipherVulnerabilityID, certInfo)
            pluginDataForRequest.sslFound = true
        }
    }

    alldts(options) {
        return new Promise((resolve) => {
            try {
                // Create a TLS connection
                const socket = tls.connect(options, () => {
                    // console.log('Connected to server');
                    const tlsInfo = {
                        Certificate: socket.getPeerCertificate(),
                        Version: socket.getProtocol(),
                        Cipher: socket.getCipher()
                    }
                    // console.log('SSL/TLSVersion:', socket.getProtocol());
                    // console.log('Certificate Info:', tlsInfo);
                    socket.end();
                    resolve(tlsInfo)
                });

                // Handle errors
                socket.on('error', (err) => {
                    resolve('Not Found');
                });
            }
            catch (e) {
                resolve('Not Found')
            }
        });
    }
}

strongCipherList = [
    'TLS-AES-128-GCM-SHA256',
    'TLS-AES-256-GCM-SHA384',
    'TLS-CHACHA20-POLY1305-SHA256',
    'TLS-AES-128-CCM-SHA256',
    'TLS-AES-128-CCM-8-SHA256',
    'ECDHE-ECDSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-ECDSA-CHACHA20-POLY1305',
    'ECDHE-ECDSA-CHACHA20-POLY1305-SHA256',
    'ECDHE-RSA-CHACHA20-POLY1305',
    'ECDHE-ECDSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-ECDSA-AES256-SHA384',
    'ECDHE-RSA-AES256-SHA384',
    'ECDHE-ECDSA-AES128-SHA256',
    'ECDHE-RSA-AES128-SHA256']

module.exports = oldCipherVuln