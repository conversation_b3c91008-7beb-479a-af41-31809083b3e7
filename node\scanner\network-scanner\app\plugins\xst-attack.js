const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

// Checks for TRACE, TRACK method enabled
class XSTAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-xst-attack'
    }

    getAttackVectors() {
        return AttackVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'uri-path-iterator', 'http-headers']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', '<PERSON><PERSON>', 'X-Wing', 'X-Forwarded-Host', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded']
            })
        }
    }

    /**
     * @param {method} attack
     * Overriding the performNetworkAttack method to add TraceEnable header in the request
     */
    async performNetworkAttack(attack) {
        attack.httpRequest.headers['TraceEnable'] = "yes"
        attack.httpRequest.method = "TRACE"

        return await super.performNetworkAttack(attack)
    }

    wantProcessAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return false
        }

        let ResContentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        let rawHtml = _.get(attack, 'result.resp.body', '')

        if (/message\/http/i.test(ResContentType) && /haikumsg/i.test(rawHtml)) {
            return true
        }
        return false
    }

    /**
     * check result for vulnerability
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.XSTvulnFound) {
            return
        }

        /**
         * If the response has status code 200 and Content-type: message/http 
         *  then it will add it to the vulnerability list
         */
        // let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        try {
            let Resbody = _.get(attack, 'result.resp.body', '')
            let regexp = new RegExp(".+" + _.escapeRegExp(attack.vector) + ".+", 'ig')
            if (regexp.test(Resbody)) {
                let xst_dts = `XST vulnerability was exist: ${Resbody.match(regexp)}`
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, xst_dts)
                pluginDataForRequest.XSTvulnFound = true
                return
            }
        }
        catch (e) { return }
    }
}

const AttackVectors = [
    `<sCrIpT/x>haikumsg(123)</sCrIpT/x>`,
    `<svg onload=haikumsg(456)>`,
]

module.exports = XSTAttack