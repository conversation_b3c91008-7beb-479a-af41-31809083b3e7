const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class SourceCodeDisc extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-source-code-disclosure'
        this.weakCryptographyVulnerabilityID = 'ID-weak-cryptography-detected'

        this.matchRegexp = new RegExp(SourceCodeDiscMatch.map((v) => {
            return v.source
        }).join('|'), "is")
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        this.checkSourceCodeDisclosure(originalRequest)
        this.checkWeakCryptography(originalRequest)
    }

    checkSourceCodeDisclosure(originalRequest) {
        if (originalRequest.result.resp.httpResponse.err) {
            return
        }
        let bodycheck = _.get(originalRequest, "result.resp.body")
        
        //Below condition added to remove the FPs 
        if (originalRequest.pluginName == 'Code Injection OOB' && /<\?php/i.test(originalRequest.vector) && /<\?php.+?haikuscan\.indusfacefinder\.in/i.test(bodycheck)) {
            return
        }
        //Below condition added to remove the FPs 
        if (originalRequest.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }
        //Below condition added to remove the FPs 
        if (originalRequest.href.includes("/examples/servlets/")
            || originalRequest.href.includes("/examples/jsp/")
            || originalRequest.href.includes("/examples/websocket/")) {
            return
        }
        //Below condition added to remove the FPs 
        if (/haiku_/i.test(bodycheck)) {
            return
        }
        this.checkBodyForVuln(originalRequest, this.matchRegexp, this.vulnerabilityID)
    }

    checkWeakCryptography(originalRequest) {
        //Set the plugin scope to entire scan per site
        // let pluginStorageScanScope = this.getPluginScopedStore(originalRequest, 'original-crawler-request')

        // //if vuln already found then return
        // if (pluginStorageScanScope.weakCryptoFound) {
        //     return
        // }
        let bodycheck = _.get(originalRequest, "result.resp.body")

        //Below condition added to remove the FPs 
        if (originalRequest.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }

        //Below condition added to remove the FPs 
        if (originalRequest.href.includes("/examples/servlets/")
            || originalRequest.href.includes("/examples/jsp/")
            || originalRequest.href.includes("/examples/websocket/")) {
            return
        }

        //Below method called just to evaluate the expression and return with context in the body
        // where match is found
        let vulnFound = this.checkBodyForVuln(originalRequest, /['"](?:MD4|MD5|RC4|RC2|SHA1|DES)['"]/i, this.weakCryptographyVulnerabilityID, { addVulnerabilitytoResult: false })
        if (vulnFound) {
            if (!/class="des">|DefaultSortDir ?== ?"des"|alt="des"/i.test(vulnFound.context) && !(/['"](?:jan|feb|march|spc|adx|riskClass)['"]/i.test(vulnFound.context) && /['"](?:des|rc[24]|md[45]|sha1)['"]/i.test(vulnFound.context))) {
                let vuln = {
                    details: {
                        context: vulnFound.context,
                        matchedCrypto: _.join(vulnFound.details, ","),
                    }
                }
                this.addVulnerabilitytoResult(originalRequest, this.weakCryptographyVulnerabilityID, vuln.details)
                // pluginStorageScanScope.weakCryptoFound = true
                return
            }
        }
    }
}

const SourceCodeDiscMatch = [
    /<%[\x20-\x80\x0d\x0a\x09]*?%>/,
    /<\?php[\x20-\x80\x0d\x0a\x09]*?\?>/,
    /<\?php/,
    /<\?=/,
    /^#\!\/.*\/perl/,
    /import java\.util\./,
    /import java\.applet\./,
    /import java\.io\./,
    /import java\.awt/,
    /import java\.net\./,
    /import java\.awt\./,
    /java\.applet\.Applet/,
    /import java\./,
    /using System;/,
    /using System\.Web;/,
    /using System\.Web\./,
    /using System\./,
    /using System\.Data/,
    /Imports System\.Data/,
    /Imports System\.Data\.SqlClient/,
    /Imports System\./,
    /Imports System\.Web/,
    /Imports System\.Web\./,
    /import java\.awt.*/,
    /import java\./,
    /@Url\.Content\(/,
    /Url\.Content\(/,
    /@RenderBody\(/,
    /@Html\.Partial\(/,
    /<asp\:/,
    /<\/asp\:/,
    /<%@ Page/,
    /\[WebMethod\]/,
    /CryptoJS\.enc\.Utf8\.parse\(['"]\d+['"]\)/,
    /REACT_APP_(?:JWT_SECRET|APIKEY|EN(?:C|v)_SECRET_KEY)(?:{|:)/,
    /lmsencrptkey\s*=[ '"#]+lmsCodeExpert@2014Phase_II##/,
    /lmsbaseurl\s*=\s*["']http/,
    /"config":.*"template":\s*"Magento_Msrp/,
]
module.exports = SourceCodeDisc