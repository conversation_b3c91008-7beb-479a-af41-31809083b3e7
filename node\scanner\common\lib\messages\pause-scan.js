const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:PauseScan')

/** 
 * external like ScanAPI -> scanner indicating scan should be paused
 * @extends QueueMessage
*/
class PauseScan extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} PauseScanMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {string} source source that sent this request (haiku)
     */
    /**
     * @param {PauseScanMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner'
        this.routingKey = 'request.pause-scan'
        this.msgType = PauseScan.msgType
    }
}

module.exports = PauseScan