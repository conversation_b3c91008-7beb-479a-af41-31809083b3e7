// Override require for 're2' to use our memory-efficient replacement
require('../../app/require-override');

// very simple main code for now. Just create an instance of the network scanner
// which will wait for messages from the Queue
const fs = require('fs')
let config = require('../config/network-scanner-config.js')

// start the network scanner
const NetworkScanner = require('./network-scanner')
let networkScnr = new NetworkScanner(config)
networkScnr.init().then( () => {
    console.log( 'Network scanner started!')
}).catch((e) => {
    console.log( 'Error thrown ', e)
})
