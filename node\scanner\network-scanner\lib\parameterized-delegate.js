const debug = require('debug')('ParameterizedDelegate')
const querystring = require('querystring')
const _ = require('lodash')

// base of delegates that can iterate parts of requests as parameters, update the params
// and get the corresponding http request. eg. form encoded POSTs
class ParameterizedDelegate {
    /**
     * defines the identity vector i.e. vector that denotes that the parameter should be set to what it was
     * originally before any tampering.
     */
    static get  identityVector() {
        return '__Identity__';
    }

    /**
     * @param {request} request the request whose parts we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iteraate same URI path more than once.
     * @param {string} parameterType parameter type like http-header
     */
    constructor(request, scanStore, parameterType, options) {
        // expect caller to have verified everything already since this is a helper class
        this.originalRequest = request
        this.parameterType = parameterType
        let revalidationInfo = _.get(this.getOriginalRequest(), 'revalidationInfo')
        if (revalidationInfo) {
            // when revalidating, don't store things like already attacked path info since we do want to 
            // attack again during further revalidate and post-revalidate, crawler based scan
            this.scanStore = {}     // create a dummy store to avoid changing all subclasses. Investigate if moving this logic to subclass make sense
        } else {
            this.scanStore = scanStore
        }

        this.extraParam = 'haiku630498'
        this.attackType = 'value' // attack param value (subclasses can add other types eg. name)
        this.options = _.cloneDeep(options);
    }

    /**
     * @typedef {Object} parameterizedDelegateOptions
     * @property {boolean} addExtraParam add an additional parameter to the attack / only iterate parms in request
     * @property {boolean} attackParamName only tamper values / additionally tamper parameter name at the end
     */

    /**
     * Set options for this parameterized request.
     * @param {parameterizedDelegateOptions} options 
     */
    setOptions(options) {
        Object.assign(this.options, options)
    }

    /**
     * set additional content type from config
     * @param {}  
     * 
     */
    setAdditionalHttpHeaders(request) {
        if (this.options.headers) {
            Object.assign(request.headers, this.options.headers)
        }
    }

    // abstract functions

    /**
     * get encodings suported by this type of delegate
     * @abstract
     */
    getEncodings(param, value) {
        throw new TypeError('must be implemented by subclass')
    }

    /**
     * generator for iterating parameters
     * @abstract
     */
    * getIterator() {
        throw new TypeError('must be implemented by subclass')
    }

    /**
     * modify a parameter for specific encoding
     * @abstract
     */
    modifyParam(param, value, encoding) {
        throw new TypeError('must be implemented by subclass')
    }

    /**
     * get the modified HTTP request
     * @abstract
     */
    getHttpRequest(encoding) {
        throw new TypeError('must be implemented by subclass')
    }

    /**
     * reset the request back to the original request to prepare for next set of modifications
     * @abstract
     */
    reset() {
        throw new TypeError('must be implemented by subclass')
    }

    getAttackType() {
        return this.attackType
    }

    getParameterType() {
        return this.parameterType
    }

    getOptions() {
        return this.options
    }

    getOriginalRequest() {
        return this.originalRequest
    }
}

module.exports = ParameterizedDelegate