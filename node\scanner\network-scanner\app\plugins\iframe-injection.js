const VectorResponseAttack = require('./vector-response-attack')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')
const cheerio = require('cheerio')

/**
 * Iframe Plugin Strategy:
 * Detects iframe injection vulnerabilities and clickjacking issues by testing various attack vectors
 * and analyzing response headers and content for security misconfigurations.
 */

class IframeAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        this.randomNumber = HaikuUtils.getRandomInt(1000, 9999)
        this.vulnerabilityID = VULNERABILITY_IDS.IFRAME_INJECTION
        this.ClickjackingVulnerability = VULNERABILITY_IDS.CLICKJACKING

        // Cache attack vectors during initialization
        this.iframeInjVectors = _iframeInjVectors.map(vector =>
            vector.replace(/###/g, this.randomNumber)
        )
    }

    getAttackVectors() {
        return this.iframeInjVectors
    }

    getAttackableEvents() {
        return [
            // URL-based injection points
            'uri-query-params',
            'uri-path-iterator',

            // Header-based injection points
            'http-headers',
            'cookie-params',

            // Body-based injection points
            'form-encoded-post',
            'json-body',
            'xml-post',
            'post-body'
        ]
    }

    initParameterizedDelegate(parameterizedDelegate) {
        if (parameterizedDelegate.getParameterType() === 'HTTPHeaders') {
            parameterizedDelegate.setOptions({ headersToIterate: HEADERS_TO_ITERATE })
        }
    }

    async performNetworkAttack(attack) {
        const pluginData = this.getPluginScopedStore(attack)

        // Skip if both vulnerabilities are already found for this endpoint
        if (pluginData.IframeAttackFound && pluginData.ClickJackingAttackFound) {
            return false
        }

        return await super.performNetworkAttack(attack)
    }

    async processAttackResponse(attack) {
        if (attack.pluginName !== this.getName()) return

        // Extract and validate response data upfront
        const response = _.get(attack, 'result.resp', {})
        const httpResponse = response.httpResponse || {}
        const contentType = _.get(httpResponse, 'headers["content-type"]', '')

        // Fast-fail content type check
        if (!CONTENT_TYPES.HTML_MIME_TYPES.test(contentType) ||
            CONTENT_TYPES.EXCLUDED.test(contentType)) {
            return
        }

        const statusCode = httpResponse.statusCode
        const rawHtml = response.body || ''

        // Fast-fail other basic checks
        if (!REGEX_PATTERNS.VALID_STATUS.test(statusCode) ||
            !REGEX_PATTERNS.HAIKUMSG.test(rawHtml) ||
            !REGEX_PATTERNS.IFRAME_WITH_CLASS.test(rawHtml)) {
            return
        }

        const securityHeaders = this.getSecurityHeaders(attack)
        if (!this.areSecurityHeadersVulnerable(securityHeaders)) return

        try {
            const $ = cheerio.load(rawHtml.trim())
            if (!this.isValidHtmlStructure($)) return

            let injectedIframes = this.findInjectedIframes($)
            if (injectedIframes.length > 0) {
                injectedIframes = this.isLikelyTruePositive(injectedIframes)
                if (injectedIframes.index > 0) {
                    this.reportVulnerabilities(attack, injectedIframes, rawHtml, securityHeaders)
                }
            }
        } catch (error) {
            // console.error('HTML parsing error:', error.message)
            return
        }
    }

    getSecurityHeaders(attack) {
        const headers = _.get(attack, 'result.resp.httpResponse.headers', {})
        return {
            xFrameOptions: (headers[SECURITY_HEADERS.X_FRAME_OPTIONS] || '').toLowerCase(),
            csp: (headers[SECURITY_HEADERS.CSP] || '').toLowerCase()
        }
    }

    areSecurityHeadersVulnerable({ xFrameOptions, csp }) {
        // Check X-Frame-Options
        const hasStrongXFO = SECURITY_PATTERNS.XFO_STRONG.test(xFrameOptions)
        const hasWeakXFO = SECURITY_PATTERNS.XFO_WEAK.test(xFrameOptions)
        const xfoVulnerable = !hasStrongXFO || hasWeakXFO || !xFrameOptions.includes(attack.hostname)

        // Check Content-Security-Policy
        const hasStrongCSP = SECURITY_PATTERNS.CSP_STRONG.test(csp)
        const hasWeakCSP = SECURITY_PATTERNS.CSP_WEAK.test(csp)
        const cspVulnerable = !hasStrongCSP || hasWeakCSP

        return xfoVulnerable && cspVulnerable
    }

    isValidHtmlStructure($) {
        // Ensure basic HTML structure exists
        if (!$('html').length || !$('body').length) {
            return false
        }

        // Check if the document appears to be a complete HTML page
        const hasHead = $('head').length > 0
        const hasTitle = $('title').length > 0
        const hasContent = $('body').text().trim().length > 0

        return hasHead || hasTitle || hasContent
    }

    findInjectedIframes($) {
        const frames = []
        $('iframe.haikumsg').each((i, el) => {
            const $el = $(el)
            const src = $el.attr('src') || ''
            if (REGEX_PATTERNS.VALID_SOURCE.test(src)) {
                frames.push(this.createIframeObject($, $el, i))
            }
        })
        return frames
    }

    createIframeObject($, $el, index) {
        return {
            index: index + 1,
            html: $.html($el),
            position: $el.prev().length > 0 ? 'Inside existing content' : 'At top of the <body>',
            src: $el.attr('src') || '',
            parentElement: $el.parent().prop('tagName') || '',
            precedingElement: $el.prev().prop('tagName') || '',
            followingElement: $el.next().prop('tagName') || ''
        }
    }

    isLikelyTruePositive(injectedIframes) {
        for (const iframe of injectedIframes) {
            // Check for suspicious parent elements
            const hasValidParent = ![
                'SCRIPT',
                'STYLE'
            ].includes(iframe.parentElement)

            // Check for sanitization artifacts
            const isNotSanitized = !REGEX_PATTERNS.SANITIZED_IFRAME.test(iframe.html)

            if (hasValidParent && isNotSanitized) {
                return iframe
            }
        }
        return []
    }

    reportVulnerabilities(attack, injectedIframes, responseBody, securityHeaders) {
        // Update vulnerability status
        this.markIframeInjection(attack, injectedIframes, this.getPluginScopedStore(attack), securityHeaders)
        this.checkClickJacking(attack, injectedIframes, responseBody, this.getPluginScopedStore(attack), securityHeaders)
    }

    markIframeInjection(attack, injectedIframes, pluginData, securityHeaders) {
        const iframe = injectedIframes

        // Format header values, showing if they're absent
        const xfoValue = securityHeaders.xFrameOptions || 'not set'
        const cspValue = securityHeaders.csp || 'not set'

        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, {
            type: 'Iframe Injection Detected',
            description: `The injected iframe appears ${iframe.position}: ${iframe.html}. Security Headers: X-Frame-Options: ${xfoValue}, CSP: ${cspValue}. The absence of these headers increases the risk of iframe-based attacks.`
        })
        pluginData.IframeAttackFound = true
    }

    checkClickJacking(attack, injectedIframes, responseBody, pluginData, securityHeaders) {
        // Check for multiple framebusting patterns
        const hasFramebusting = SECURITY_PATTERNS.FRAMEBUSTING.some(pattern =>
            pattern.test(responseBody)
        )

        if (!hasFramebusting) {
            const iframe = injectedIframes

            // Format header values, showing if they're absent
            const xfoValue = securityHeaders.xFrameOptions || 'not set'
            const cspValue = securityHeaders.csp || 'not set'

            this.addVulnerabilitytoResult(
                attack,
                this.ClickjackingVulnerability,
                `An injected <iframe> was found ${iframe.position}: ${iframe.html}. The page successfully loads within an iframe, lacks framebusting JavaScript, and is vulnerable to transparent overlay click hijacking. Security Headers: X-Frame-Options: ${xfoValue}, CSP: ${cspValue}. The absence of these protections makes the page susceptible to clickjacking attacks.`)
            pluginData.ClickJackingAttackFound = true
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID)

        if (vulnID !== this.vulnerabilityID) {
            return
        }

        const annotations = [
            ['original', 'httpRequest.uri', 'param', [attack.href]],
            ['attack', 'httpRequest.uri', 'param', [attack.href]],
            ['attack', 'httpResponse.headers', 'param', [SECURITY_HEADERS.X_FRAME_OPTIONS, SECURITY_HEADERS.CSP]],
            ['attack', 'httpResponse', 'param', ['statusCode']],
            ['attack', 'httpResponse.body', 'text', ['was.indusface.com', 'haikumsg']],
            ['attack', 'httpRequest', 'param', [attack.param]],
            ['attack', 'httpRequest.body', 'param', [attack.param]]
        ]

        annotations.forEach(([type, path, paramType, values]) => {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, type, path, paramType, values)
        })
    }
}

// Attack vectors for iframe injection testing
const _iframeInjVectors = [
    // Basic Context Breaking
    `"><iframe src="https://was.indusface.com/###" class="haikumsg"></iframe><!--`,

    // Attribute Context Breaking
    `" onload="this.src='https://was.indusface.com/###'" class="haikumsg"><iframe class="haikumsg"></iframe><!--`,

    // Script Context Breaking
    `";document.body.appendChild(document.createElement('iframe')).src='https://was.indusface.com/###';var c="haikumsg`,
    `'+document.body.appendChild(document.createElement('iframe')).src='https://was.indusface.com/###'+'haikumsg`,

    // Template Context Breaking
    `\${document.body.appendChild(document.createElement('iframe')).src='https://was.indusface.com/###'}haikumsg`,

    // Event Handler Breaking
    `"><img src=x onerror="(this.outerHTML='<iframe src=https://was.indusface.com/### class=haikumsg></iframe>')">`,

    // DOM Manipulation
    `"><div id="haiku###"></div><script>document.getElementById('haiku###').innerHTML='<iframe src=https://was.indusface.com/### class=haikumsg></iframe>'</script>`,

    // Comment Breaking
    `--><iframe src="https://was.indusface.com/###" class="haikumsg"></iframe><!--`,
    `*/iframe src="https://was.indusface.com/###" class="haikumsg"></iframe>/*`,

    // Mixed Encoding
    `%3Ciframe%20src%3D%22https%3A%2F%2Fwas.indusface.com%2F###%22%20class%3Dhaikumsg%3E%3C%2Fiframe%3E`,
    `&#x3C;iframe src=&#x22;https://was.indusface.com/###&#x22; class=&#x22;haikumsg&#x22;&#x3E;&#x3C;/iframe&#x3E;`,

    // Nested Context Breaking
    `"><iframe srcdoc="<iframe src='https://was.indusface.com/###' class='haikumsg'></iframe>" class="haikumsg"></iframe><!--`,

    // Data URI
    `"><iframe src="data:text/html;base64,PHNjcmlwdD5kb2N1bWVudC53cml0ZSgnPGlmcmFtZSBzcmM9Imh0dHBzOi8vd2FzLmluZHVzZmFjZS5jb20vIyMjIiBjbGFzcz0iaGFpa3Vtc2ciPjwvaWZyYW1lPicpOzwvc2NyaXB0Pg==" class="haikumsg"></iframe><!--`
]

// Constants for vulnerability IDs and headers
const VULNERABILITY_IDS = {
    IFRAME_INJECTION: 'ID-iframe-injection',
    CLICKJACKING: 'ID-clickjacking-vulnerability'
}

const SECURITY_HEADERS = {
    X_FRAME_OPTIONS: 'x-frame-options',
    CSP: 'content-security-policy'
}

const CONTENT_TYPES = {
    EXCLUDED: /application\/json|text\/plain|application\/octet-stream|multipart\/form-data|application\/x-www-form-urlencoded|audio\/.*|video\/.*|image\/(?!svg\+xml).*|font\/.*/i,
    HTML_INDICATORS: [/<!DOCTYPE\s+html/i, /<html/i, /<\/html>/i],
    HTML_MIME_TYPES: /text\/html|application\/xhtml\+xml/i
}

const HEADERS_TO_ITERATE = ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']

// Regex patterns for security checks
const SECURITY_PATTERNS = {
    FRAMEBUSTING: [
        /if\s*\(\s*self\s*!==\s*top\s*\)\s*top\.location\s*=\s*self\.location\s*;?/i,
        /if\s*\(\s*window\.top\s*!==\s*window\.self\s*\)\s*window\.top\.location\s*=\s*window\.self\.location\s*;?/i,
        /if\s*\(\s*parent\.frames\.length\s*>\s*0\s*\)\s*top\.location\s*=\s*self\.location\s*;?/i
    ],
    XFO_STRONG: /^(deny|sameorigin)$/i,
    XFO_WEAK: /allow-from|allowall/i,
    CSP_STRONG: /frame-ancestors\s+('none'|'self'|https?:\/\/[^*;]+)(?:\s*;\s*|$)/i,
    CSP_WEAK: /frame-ancestors\s+(\*|https?:\/\/\*\.)/i
}

// Pre-compile regex patterns for better performance
const REGEX_PATTERNS = {
    HAIKUMSG: /haikumsg/i,
    IFRAME_WITH_CLASS: /<iframe[^>]*class=(['"])?haikumsg\1[^>]*>/i,
    SANITIZED_IFRAME: /(&lt;iframe|\\u003ciframe)/,
    VALID_STATUS: /^(200|201|202|203|206|404)$/,
    VALID_SOURCE: /https:\/\/was\.indusface\.com/
}

module.exports = IframeAttack