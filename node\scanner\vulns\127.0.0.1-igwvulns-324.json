{"session_id":324,"scanlog_id":324,"scanner":"haiku","vulnerabilities":[{"scanner":"haiku","productionReady":false,"vulnerabilityid":"405111","result":"Potential CSS Injection Detected. Payload: <style>body { color: red; } /*haikutest*/</style>","founddate":"2025-06-11T10:56:18.862Z","key":"http://127.0.0.1:5000/test?input=||input","url":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","para":"input","vector":"<style>body { color: red; } /*haikutest*/</style>","requestheader":"GET /test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E HTTP/1.1\naccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\naccept-language: en-US\nreferer: http://127.0.0.1:5000/\nsec-fetch-dest: document\nsec-fetch-mode: navigate\nsec-fetch-site: same-origin\nsec-fetch-user: ?1\nupgrade-insecure-requests: 1\nuser-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36\nhost: 127.0.0.1:5000\naccept-encoding: gzip, deflate","method":"GET","injectedurl":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","responseheader":"200 OK\ncontent-type: text/html; charset=utf-8\ncontent-length: 1131\nserver: Werkzeug/2.0.3 Python/3.6.0\ndate: Wed, 11 Jun 2025 10:55:46 GMT","haikuVulnId":"ID-css-injection"}]}{"session_id":324,"scanlog_id":324,"scanner":"haiku","vulnerabilities":[{"scanner":"haiku","productionReady":false,"vulnerabilityid":"405111","result":"Potential CSS Injection Detected.\nPayload: <style>body { color: red; } /*haikutest*/</style>\nDetection Method: Style Attribute","founddate":"2025-06-12T13:01:22.938Z","key":"http://127.0.0.1:5000/test?input=||input","url":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","para":"input","vector":"<style>body { color: red; } /*haikutest*/</style>","requestheader":"GET /test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E HTTP/1.1\naccept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7\naccept-language: en-US\nreferer: http://127.0.0.1:5000/\nsec-fetch-dest: document\nsec-fetch-mode: navigate\nsec-fetch-site: same-origin\nsec-fetch-user: ?1\nupgrade-insecure-requests: 1\nuser-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36\nhost: 127.0.0.1:5000\naccept-encoding: gzip, deflate","method":"GET","injectedurl":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","responseheader":"200 OK\ncontent-type: text/html; charset=utf-8\ncontent-length: 1131\nserver: Werkzeug/2.0.3 Python/3.6.0\ndate: Thu, 12 Jun 2025 13:01:03 GMT","haikuVulnId":"ID-css-injection"}]}