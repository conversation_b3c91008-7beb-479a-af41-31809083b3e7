const debug = require('debug')('BasePlugin')
const fs = require('fs')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const logger = require('../../../common/lib/haiku-logger')
const HaikuScanScriptHelper = require('../../../common/lib/haiku-scan-script-helper')
const _ = require('lodash')

/**
 * base class for all plugins. 
 */
class BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        this.networkScanner = networkScanner;
    }

    /**
     * Sets up event handlers and provides good default implementation for them
     * Sub classes should override the event handlers if  they need custom behaviour. Most 
     * of these will call a method (conventinally named process<eventname> for on<eventname>
     * handler)
     */
    setupEventHandlers() {
        this.networkScanner.on('new-request', this.onNewRequest.bind(this))
    }

    /**
     * Default new Request handler. Will call processNewRequest() if request can proceed
     * @param {request} originalRequest new request received from crawler
     */
    onNewRequest(originalRequest) {
        // see if this plugin is prohibited from sending attacks by config
        if (!this.canAttack(originalRequest)) {
            logger.log('info', `Ignoring new Request for plugin '${this.getName()}' -> disallowed by config`, HaikuUtils.getMetadataForLog(originalRequest))
            return
        }

        this.processNewRequest(originalRequest)
    }

    // -- default event handlers
    /**
     * Default, do nothing processNewRequest. subclasses should override if they need to handle this 
     * event
     * @param {request} originalRequest new request received from crawler
     */
    processNewRequest(originalRequest) {
    }

    /**
     * Initialize plugin, set up things we can't in constructor eg. getting things from plugin metadata
     * Subclasses can override to do any init that requires object to be already created
     */

    init() {
        this.setupEventHandlers()
    }

    /**
     * get the plugin name.
     */
    getName() {
        return this.pluginName
    }

    /**
     * get the config information specific to this site
     * @param {*} obOrScanId scanId or Object containing scanId of site (being scanned) to get config
     */
    getConfig(obOrScanId) {
        let scanId = _.isObject(obOrScanId) ? HaikuUtils.getMetadataForLog(obOrScanId).scanId : obOrScanId
        return this.networkScanner.getConfig(scanId)
    }

    /**
     * get the config information for this plugin specific to this site
     * @param {*} obOrScanId scanId or Object containing scanId to get metadata for
     */
    getMetadata(obOrScanId) {
        return this.getConfig(obOrScanId).Plugins[this.getName()]
    }

    /**
     * Get boolean property from this plugins' site specific metadata or default config's plugin metadata
     * or (global) DefaultPluginSettings in that order
     * @param {*} obOrScanId scanId or Object containing scanId to get metadata for
     * @param {*} property The boolean property to get eg: 'canAttack'
     */
    _getBooleanProperty(obOrScanId, property) {
        let siteSpecificProp = this.getMetadata(obOrScanId)[property]
        return _.isBoolean(siteSpecificProp) ?
            siteSpecificProp :
            this.getConfig(obOrScanId).DefaultPluginSettings[property]
    }

    isApiScanMode(obOrScanId) {
        return this.getConfig(obOrScanId).ScannerSettings.isApiScan ? true : false;
    }

    /**
     * 
     * @param {Object} obOrScanId
     * @returns {Boolean} true if the replay scan is in debug mode, false otherwise
     */
    isReplayScanDebugMode(obOrScanId) {
        return this.getConfig(obOrScanId).ScannerSettings.replayScanInfo.isReplayScanDebugMode ? true : false;
    }

    /**
     * Get the url analysis data for the scanId
     * @param {Object|String|Number} obOrScanId scanId or Object containing scanId to get metadata for
     * @returns {Object} urlAnalysisData object for the scanId
     */
    getUrlAnalysis(obOrScanId) {
        try {
            let scanId = _.isObject(obOrScanId) ? HaikuUtils.getMetadataForLog(obOrScanId).scanId : obOrScanId;

            if(scanId) {
                return this.networkScanner.getScanInfo(scanId).urlAnalysisData;
            }
        } catch (error) {
            
        }
    }

    /**
     * Can this plugin send attacks - site specific config can override the default config setting
     * @param {*} obOrScanId scanId or Object containing scanId to get metadata for
     */
    canAttack(obOrScanId) {
        if(this.isApiScanMode(obOrScanId)) {
            if(!this._getBooleanProperty(obOrScanId, 'canAttackInApiScan')) {
                return false;
            }

            //Check if allowed request has specific attack area
            let attackAreas = _.get(this.getPluginScopedStore(obOrScanId), 'attackAreas')

            if (_.get(obOrScanId, 'attackArea') && attackAreas) {
                let attackArea = _.chain(attackAreas)
                    .find(attackArea => {
                        return obOrScanId.attackArea.toLowerCase() == attackArea.toLowerCase();
                    })
                    .value();

                if (!attackArea) {
                    return false;
                }
            }
            
            return true;
        }
        
        return this._getBooleanProperty(obOrScanId, 'canAttack')
    }

    /**
     * Can this plugin check for vulns - site specific config can override the default config setting
     * @param {*} obOrScanId scanId or Object containing scanId to get metadata for
     */
    canProcessAttackResponse(obOrScanId) {
        if(this.isApiScanMode(obOrScanId)) {
            return this._getBooleanProperty(obOrScanId, 'processAttackResponseInApiScan')
        }
        
        return this._getBooleanProperty(obOrScanId, 'processAttackResponse')
    }

    /**
     * Return a modifiable object instance specific to this plugin and whose lifetime is as per scope
     * i.e. not necc. tied to the specific attack - the original request could spawn off multiple attacks
     *  due to attack vectors and encodings.
     * @param {attack|request} attackOrOrigReq Instance of the attack or original request. 
     * @param {string} scope scope/lifetime of the storage - this request, original (crawler) request, scan
     * @returns {object} Object instance specific to this plugin & tied to the original request
     */
    getPluginScopedStore(attackOrOrigReq, scope = 'original-crawler-request') {
        let pluginName = this.getName()
        let objectKey = `pluginData.${pluginName}`

        let container
        switch (scope) {
            case 'current-request':
                container = attackOrOrigReq
                break;

            case 'original-crawler-request':
                container = attackOrOrigReq.originalRequest || attackOrOrigReq
                break;

            case 'this-scan':
                let scanId = attackOrOrigReq.originalRequest ? attackOrOrigReq.originalRequest.scanId : attackOrOrigReq.scanId
                container = this.networkScanner.getScanInfo(scanId)
                break;

            default:
                throw new TypeError(`Unknown storage scope : ${scope}`)
        }

        // return the specific scoped storage object - create object if necc.  
        let store = _.get(container, objectKey)
        if (!store) {
            _.set(container, objectKey, {})
            store = _.get(container, objectKey)
        }

        return store
    }

    /**
     * Get HaikuScanScriptHelper instance
     * @param {*} scanId scanId of site (being scanned) to get HaikuScanScriptHelper instance
     * @returns {HaikuScanScriptHelper} HaikuScanScriptHelper instance specific to this plugin
     */
     getHaikuScanScriptHelper(scanId) {
         return _.get(this.networkScanner.runningScans[scanId], "haikuScanScriptHelper");
    }

    /**
     * get the vulnerability object using vulnKey.
     */
     getVulnObject(scanId, vulnKey) {
        try {
            return _.get(this.getConfig(scanId), `Plugins.${this.getName()}.vulnerabilities.${vulnKey}`, null);
        } catch (err) {
            logger.log('error', `Unable to get ${vulnKey} vulnId for plugin '${this.getName()}', Reason: ${err.toString()}`)
        }

        return null;
    }
}

module.exports = BasePlugin