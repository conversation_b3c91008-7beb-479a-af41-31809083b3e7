// XPath related methods 
// TODO - see if it makes sense to use something like: https://www.npmjs.com/package/potent-tools
class IndusfaceXpath {
    constructor() {}

    name(short) {
        return short ? "IFC-XPath" : "Indusface Browser XPath Plugin"
    }

    init() {
        indusfaceRenderer.getXpath = this.getXpath.bind(this)
        indusfaceRenderer.getElementFromXPath = this.getElementFromXPath.bind(this)
        indusfaceRenderer.getElementsMatchingXpath = this.getElementsMatchingXpath.bind(this)
        indusfaceRenderer.xpathClick = this.xpathClick.bind(this)
        indusfaceRenderer.getFullXpath = this.getFullXpath.bind(this)
    }

    // take from firebug source. See comment on top of t his file and check if it makes sense to
    // use an npm module instead
    getXpath(el) {
        // if full xpath is enabled, use the full xpath
        if(indusfaceRenderer.config.useFullXPath) {
            return indusfaceRenderer.getFullXpath(el);
        }
        
        // firebug
        /**
         * Gets an XPath for an element which describes its hierarchical location.
         */
        function getElementXPathFirebug(element) {
            if (element && element.id)
                return '//*[@id="' + element.id + '"]';
            else
                return getElementTreeXPathFirebug(element);
        };

        function getElementTreeXPathFirebug(element) {
            var paths = [];

            // Use nodeName (instead of localName) so namespace prefix is included (if any).
            for (; element && element.nodeType == Node.ELEMENT_NODE && !element.id; element = element.parentNode) {
                var index = 0;
                var hasFollowingSiblings = false;
                for (var sibling = element.previousSibling; sibling; sibling = sibling.previousSibling) {
                    // Ignore document type declaration.
                    if (sibling.nodeType == Node.DOCUMENT_TYPE_NODE)
                        continue;

                    if (sibling.nodeName == element.nodeName)
                        ++index;
                }

                for (var sibling = element.nextSibling; sibling && !hasFollowingSiblings; sibling = sibling.nextSibling) {
                    if (sibling.nodeName == element.nodeName)
                        hasFollowingSiblings = true;
                }

                var tagName = (element.prefix ? element.prefix + ":" : "") + element.localName;
                var pathIndex = (index || hasFollowingSiblings ? "[" + (index + 1) + "]" : "");
                paths.splice(0, 0, tagName + pathIndex);
            }

            let xpath = paths.length ? "/" + paths.join("/") : null
            if (xpath && element && element.id) {
                xpath = '//*[@id="' + element.id + '"]' + xpath
            }
            return xpath
        };
        return getElementXPathFirebug(el);
    }

    /**
     * Get the full XPath for an element
     * @param {HTMLElement} element 
     * @returns {string} full XPath
     */
    getFullXpath(element) {
        if (!element) {
            return null;
        }

        // If the element is the root (document or html), return its tag name
        if (element.nodeType === Node.DOCUMENT_NODE) {
            return '/';
        }
        if (element.nodeType === Node.ELEMENT_NODE && element.tagName.toLowerCase() === 'html') {
            return '/html';
        }

        // Traverse up the DOM tree to construct the full XPath
        let path = '';
        while (element && element.nodeType === Node.ELEMENT_NODE) {
            let tagName = element.tagName.toLowerCase();

            // Get the index of the element among its siblings with the same tag name
            let index = 1;
            let sibling = element.previousElementSibling;
            while (sibling) {
                if (sibling.tagName.toLowerCase() === tagName) {
                    index++;
                }
                sibling = sibling.previousElementSibling;
            }

            // Append the tag name and index to the path
            path = `/${tagName}[${index}]` + path;

            // Move to the parent node
            element = element.parentElement;
        }

        return path;
    }


    // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Introduction_to_using_XPath_in_JavaScript
    // Gets 1st node matching xpath. Use whe expecting only one node.
    getElementFromXPath(xpath) {
        let res = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null)
        return res ? res.singleNodeValue : res
    }

    // given an xpath that results in multiple nodes, return array of elemnts that
    // match the xpath. Uses Snapshots
    getElementsMatchingXpath(xpath) {
        let ret = []
        try {
            let res = document.evaluate(xpath, document, null, XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE, null)
            for (let i = 0; i < res.snapshotLength; i++) {
                ret.push(res.snapshotItem(i))
            }
        } catch (err) {
            console.log(err)
        }

        return ret
    }

    xpathClick(xpath) {
        try {
            let element = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            // check if the element is found
            if (element) {
                // simulate a click on the element
                element.click();
            } else {
                console.error(`Element not found with XPath: ${xpath}`);
            }
        }
        catch (e) {
            console.error(`xpathClick error for XPath: ${xpath}, reason: ${e.message}`);
        }
    }
}

module.exports = IndusfaceXpath