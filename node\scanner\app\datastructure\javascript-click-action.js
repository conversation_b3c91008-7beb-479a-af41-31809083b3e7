const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// javascript click action - use JS to send various mouse actions
class JSClickAction {
    constructor(xpath, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.annotation = annotation
    }

    get actionType() {
        return 'js-click'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        // move item into view   
        let encXPath = utils.encode(this.xpath)
        let jscode = `indusfaceRenderer.scrollIntoView('${encXPath}')`
        await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true), '', 200)
        await executer.waitForDocSteady(200, 500)

        function f(xpath, event) {
            return new Promise(async (resolve, reject) => {
                //utils.log('triggering ', event, ' on ', xpath)
                let jscode = 'indusfaceRenderer.triggerMouseEvent(\'' + xpath + '\',\'' + event + '\')';
                await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true), '', 200)
                await executer.waitForDocSteady(200, 500)
                resolve()
            })
        }

        // call click with sequence of events
        await f(encXPath, 'mouseenter')
        await f(encXPath, 'mouseover')
        await f(encXPath, 'mousedown')
        await f(encXPath, 'mouseup')
        await f(encXPath, 'click')

        return true
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = JSClickAction