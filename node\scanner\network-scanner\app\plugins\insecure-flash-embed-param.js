const debug = require('debug')('InsecureFlashParamVulnerability')
const NetworkAttack = require('./network-attack')

class InsecureFlashParamVulnerability extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-insecure-flash-embed-param'
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.insecureFlashParamFound) {
            return
        }

        let matchPattern = /(AllowScriptAccess='always')|(allowscriptaccess = 'always')|(embed\[@AllowScriptAccess='always'\])/i
        this.checkBodyForVuln(attack,matchPattern,this.vulnerabilityID)
    }
}

module.exports = InsecureFlashParamVulnerability