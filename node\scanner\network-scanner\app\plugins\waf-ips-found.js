const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const SetCookie = require('set-cookie-parser');
const RE2 = require('re2')


class WAFIPSFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-waf-ips-found'

        //Below variables to be checked along with response body message
        this.wafIPSHeaders = [
            'x-cnection', //BigIP 
            'X-WA-Info', //BigIP
            'cf-ray', //cloudflare
            'FORTIWAFSID', //Fortinet FortiWeb
            'barracuda_',
        ]

        this.wafIPSServerHeaders = [
            'BigIP',
            'F5',
            'WebKnight',
            'cloudflare-nginx',
            'mod_security',
            'IF_WAF',
            'Mod_Security',
            'NYOB', //ModSec Older Version
            'Sucuri', //Sucuri waf
            'Cloudproxy', //Sucuri waf
            'AkamaiGhost',
        ]

        this.wafIPSCookies = [
            'BIGipServer',
            '__cfduid', //cloudflare
            'FORTIWAFSID=', //Fortinet FortiWeb
            'incap_ses', //incapsula by imperva - not needed as it's with normal request as well
            'visid_incap', //incapsula by imperva - not needed as it's with normal request as well
            'barra_counter_session', //barracuda cookies
        ]

        this.matchRegexp = new RE2(blockedPageResponse.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        //Plugin scope to entire scan
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        //If vuln already found then return
        if (pluginDataForRequest.wafIPSDetected) {
            return
        }

        /**
         * As we need to know if any blockage is there after sending few attack request and crawler-seed 
         * in picture hence we will start observing requests after first 200 request have passed
         * and will observe next 15 request to determine if waf/ids/ips is blocking any request
         */
        if (pluginDataForRequest.AttackRequest == null) {
            pluginDataForRequest.AttackRequest = 0
            pluginDataForRequest.AnalyzedRequest = 0
        }
        pluginDataForRequest.AttackRequest += 1

        //fetch server header name if any
        let serverHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["server"]');

        //fetch all headers
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {})

        //fetch all result for cookies
        let res = _.get(attack, 'result.resp.httpResponse', {});

        let val = null
        let headerName = null
        let cookieName = null
        let checkResponse = null
        let vuln = null

        //start verification only when number of attack request is above 200
        // and verify till number of analyzed request is 15
        if (pluginDataForRequest.AttackRequest >= this.getMetadata(attack).startVerificationAfterThisManyAttackRequest &&
            pluginDataForRequest.AnalyzedRequest <= this.getMetadata(attack).maxRequestToVerify) {

            checkResponse = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID, { addVulnerabilitytoResult: false })
            //only start to report vuln when a blocking page is found - below condition is true
            if (checkResponse) {

                //increment analyzed request whenever above condition is true
                pluginDataForRequest.AnalyzedRequest += 1


                //check for presence of serverHeader in response
                // if found assign it to vuln val
                if (this.wafIPSServerHeaders.indexOf(serverHeaderVal) > -1) {
                    val = serverHeaderVal
                }

                //check for presence of headers
                for (let i = 0; i < this.wafIPSHeaders.length; i++) {
                    //get the list of all keys in response header and 
                    // see if any of them is present with our array list
                    if (Object.keys(headers).indexOf(this.wafIPSHeaders[i]) > -1) {
                        headerName = this.wafIPSHeaders[i]
                    }
                }

                //fetch all cookies from result
                let cookies = SetCookie.parse(res, {
                    decodeValues: true
                });

                //check for presence of cookies
                for (let cookie of cookies) {
                    for (let i = 0; i < this.wafIPSCookies.length; i++) {
                        //Verify if any waf cookies are present in response, if so then 
                        // assign to Cookiename
                        if (cookie.name.includes(this.wafIPSCookies[i])) {
                            cookieName = cookie.name
                        }
                    }
                }

                //Create vuln with required details
                vuln = {
                    details: {
                        serverHeader: val, //assign waf server header detected
                        header: headerName, // assign waf header detected
                        cookie: cookieName, // assign waf cookie detected
                        context: checkResponse.details // assign the body which is matched for regexp
                    }
                }
            }
        }

        //if maximum request are verified and detected that page is blocked
        // then report the vulnerability
        if (vuln && pluginDataForRequest.AnalyzedRequest >= this.getMetadata(attack).maxRequestToVerify) {
            //&& (vuln.details.serverHeader != undefined || vuln.details.header != undefined || vuln.details.cookie != undefined)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginDataForRequest.wafIPSDetected = true

        }
    }
}

const blockedPageResponse = [
    /This\serror\swas\sgenerated\sby\sMod_Security/,
    /One\sor\smore\sthings\sin\syour\srequest\swere\ssuspicious/, //mod\ssec
    /rules\sof\sthe\smod_security\smodule/,
    /mod_security\srules\striggered/,
    /The\srequested\sURL\swas\srejected.\sPlease\sconsult\swith\syour\sadministrator\./, //F5 ASM
    /Cloudflare\sRay\sID:/,
    /DDoS\sprotection\sby\sCloudflare/,
    /WebKnight\sApplication\sFirewall\sAlert/,
    /AQTRONIX\sWebKnight/,
    /Sucuri\sWebsite\sFirewall/,
    /<h1\>invalid\surl<\/h1\>/, //akamaighost
    /<title\>access\sdenied<\/title\>/, //akamaighost
    /_Incapsula_Resource/, //incasula by imperva
    /Incapsula\sincident\sID:/, //incapsula by imperva
    /Contact\ssupport\sfor\sadditional\sinformation.<br\/>The\sincident\sID\sis:/, //securesphere by imperva
    /You\shave\sbeen\sblocked/, //barracuda
    /You\sare\sunable\sto\saccess\sthis\swebsite/, //barracuda
    /This\swebsite\sis\ssecured\sagainst\sonline\sattacks.\sYour\srequest\swas\sblocked\sdue\sto\ssuspicious\sbehavior/, //apptrana waf
    /Incident\sID/, //generic
]

module.exports = WAFIPSFound