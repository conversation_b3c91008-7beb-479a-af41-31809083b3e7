const Combinatorics = require('js-combinatorics');
const ActionList = require('../../datastructure/action-list.js')
const LoadAction = require('../../datastructure/load-action.js')
const ClickAction = require('../../datastructure/click-action.js')
const SelectAction = require('../../datastructure/select-action.js')
const TypeAction = require('../../datastructure/type-action.js')
const CheckboxAction = require('../../datastructure/checkbox-action.js')
const FormSubmitAction = require('../../datastructure/form-submit-action.js')
const URL = require('url').URL
const utils = require('../../ifc-utils.js')
const logger = require('../../../common/lib/haiku-logger')
const _ = require('lodash');
const HaikuUtils = require('../../../common/lib/haiku-utils.js');
const UploadAction = require('../../datastructure/upload-action.js')

const pluginName = 'coreHeuristics'

class CoreHeuristics {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config
        this.simpleLoginInfo = _.get(this.config, 'siteConfig.pluginData.simpleLogin')
        this.excludeItems = _.get(this.config, 'siteConfig.pluginData.excludeItems', null)
        this.formatExcludeItems();

        this.seenNonLinkFingerprints = []
        this.seenClickableItems = {};
        this.duplicatePatternUrls = {};
        this.config.uploadFilePath = this.config.uploadFilePath || 'crawler/uploads/indusface-haiku-file.json';
        this.config.uploadFileType = this.config.uploadFileType || 'text/plain';

        this.config.uploadFilePaths = [
            { url: 'crawler/uploads/indusface-haiku-file.json', fileType: 'application/json' },
            { url: 'crawler/uploads/indusface-haiku-file.csv', fileType: 'text/csv' },
            { url: 'crawler/uploads/indusface-haiku-file.png', fileType: 'image/png' },
            { url: 'crawler/uploads/indusface-haiku-file.xml', fileType: 'application/xml' },
            { url: 'crawler/uploads/indusface-haiku-file.jpg', fileType: 'image/jpeg' },
            { url: 'crawler/uploads/indusface-haiku-file.jpeg', fileType: 'image/jpeg' },
            { url: 'crawler/uploads/indusface-haiku-file.gif', fileType: 'image/gif' },
            { url: 'crawler/uploads/indusface-haiku-file.svg', fileType: 'image/svg+xml' },
            { url: 'crawler/uploads/indusface-haiku-file.pdf', fileType: 'application/pdf' },
            { url: 'crawler/uploads/indusface-haiku-file.doc', fileType: 'application/msword' },
            { url: 'crawler/uploads/indusface-haiku-file.docx', fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
            { url: 'crawler/uploads/indusface-haiku-file.txt', fileType: 'text/plain' },
            { url: 'crawler/uploads/indusface-haiku-file.xlsx', fileType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
            { url: 'crawler/uploads/indusface-haiku-file.xls', fileType: 'application/vnd.ms-excel' },
            { url: 'crawler/uploads/indusface-haiku-file.ppt', fileType: 'application/vnd.ms-powerpoint' },
            { url: 'crawler/uploads/indusface-haiku-file.pptx', fileType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' },
        ];

        // set up event listeners
        scanner.on('got-interesting-items', this.getActions.bind(this))
        scanner.on('serialize-state', this.serializeState.bind(this))
        scanner.on('deserialize-state', this.deserializeState.bind(this))
    }

    formatExcludeItems() {
        if (!this.excludeItems) {
            return;
        }

        try {
            for (let index = 0; index < this.excludeItems.checkForElements.length; index++) {
                let element = this.excludeItems.checkForElements[index];
                this.excludeItems.checkForElements[index] = element.replace(new RegExp(`'`, 'g'), `"`)
            }

            for (let index = 0; index < this.excludeItems.filterElements.length; index++) {
                let element = this.excludeItems.filterElements[index];
                this.excludeItems.filterElements[index] = element.replace(new RegExp(`'`, 'g'), `"`)
            }
        } catch (error) {
            // TODO : remove this try catch statment once build is stabalize after release
           try {
                utils.log(`Error : Invalid xpath provided : excludeItems ${error.toString()}`);
           } catch (e) {

           }
        }
    }

    /**
     * return object that can be JSON.stringified and stored
     * @param {Object} pluginData Object where we can add our serialized state under our own key
     */
    serializeState(pluginData) {
        pluginData[pluginName] = {
            seenNonLinkFingerprints: this.seenNonLinkFingerprints
        }
    }

    /**
     * Restore plugin data that from serialized object
     * @param {Object} pluginData Object with our serialized state under our own key
     */
    deserializeState(pluginData) {
        if (!pluginData[pluginName]) {
            return
        }

        // add to sets
        this.seenNonLinkFingerprints = pluginData[pluginName].seenNonLinkFingerprints || []
    }

    createAction(ifcAction, xpath, ifcValue, name) {
        let action
        switch (ifcAction) {
            case 'click':
            case 'login-click':
                action = new ClickAction(xpath, name)
                break;

            case 'type':
                action = new TypeAction(xpath, ifcValue, name)
                break;

            case 'select':
                action = new SelectAction(xpath, ifcValue, name)
                break;

            case 'checkbox':
                action = new CheckboxAction(xpath, ifcValue, name)
                break;

            case 'upload':
                action = new UploadAction(xpath, this.config.uploadFilePath, name, this.config.uploadFileType)
                break;

            default:
                utils.log(`skipping unknown item action: ${ifcAction}`)
        }

        return action
    }

    // heuristic plugin - returns an array of array of actions 
    // (Should return only combination of actions like type1, type2 and click and not dumb link clicks.)
    // But for now the same one heuristic plugin is going to return all actions
    async getActions(ret, interestingItems) {
        // previous plugin already decided to skip state or specifically asked to stop processing more plugins => nothing to do
        if (ret.skipState) {
            return
        }

        // Only process non link items if we have not already performed the same set of actions before.
        // We only do this at a gross level i.e. all actions in a page/state not granular because
        // determining if a single click is a duplicate is risky. We check if all actions are done before
        // i.e all inputs, all clikcs, all forms have been seen in another page.
        let clickActions = [];
        let formSubmitActions = [];
        let tableInputActions = [];
        let nonLinkFingerprint = _.get(interestingItems, 'serverData.nonLinkFingerprint')
        let fileUploadItems = [];
        if (nonLinkFingerprint && !this.seenNonLinkFingerprints.includes(nonLinkFingerprint.fingerPrintData)) {
            this.seenNonLinkFingerprints.push(nonLinkFingerprint.fingerPrintData)
            let inputActions = [];
            
            for (let inputItem of interestingItems.inputItems) {
                for (let ifcValue of inputItem[1].ifcValues) {
                    // for username, password and email, use login creds if it was given to us
                    let valToUse = ifcValue
                    if (this.simpleLoginInfo) {
                        if (inputItem[1].ifcType == 'password') {
                            valToUse = this.simpleLoginInfo.password
                        }
                        if (inputItem[1].ifcType == 'Normal Text' ||
                            (inputItem[1].ifcType == 'email' && /@.+\..+/.test(this.username))) {
                            valToUse = this.simpleLoginInfo.username
                        }
                    }

                    if(inputItem[1].ifcAction == 'upload') {
                        fileUploadItems.push(inputItem);
                        continue;
                    }

                    let action = this.createAction(inputItem[1].ifcAction, inputItem[0], valToUse, inputItem[1].name)
                    if (action) {
                        //if table item then each input should be one table action in single ActionList                  
                        if(inputItem[1].isTableItem) {
                            let tableAction = new ActionList('core-heuristic generated table action', [action]);
                            tableInputActions.push(tableAction);
                        }
                        else {
                            inputActions.push(action);
                        }
                    }
                    break // do only first value for now
                }
            }

            let duplicateClickableItems = [];

            for (let clickableItem of interestingItems.clickableItems) {
                //utils.log(`JSON.stringify clickableItem ${JSON.stringify(clickableItem[1])}`)
                if (clickableItem[1].ifcAction != 'non-click') {
                    if(this.config.ignoreRowAndColumns && clickableItem[1].allAttrs) {
                        //check if the element is column header, just column or header present any of the attribute
                        let isColumnOrRowElement = false;
                        for (let attr of clickableItem[1].allAttrs) {
                            if (attr && attr.value) {
                                //use regex here so later can be added for other attributes values for difference sites
                                if (/column|header|row|cell|gridcell/.test(attr.value)) {
                                    isColumnOrRowElement = true;
                                    break;
                                }
                            }
                        }

                        if(isColumnOrRowElement) {
                            //Do not click on column & row elements
                            //to avoid infinite crawl on table elements
                            continue;
                        }
                    }

                    let actions = []
                    let xpathKeys = clickableItem[0];
                    
                    if (inputActions.length && interestingItems.relevantClickItems[clickableItem[0]]) {
                        xpathKeys = `${_.chain(inputActions).map(item => item.xpath).join('|').value()}|${xpathKeys}`;

                        if(this.seenClickableItems[xpathKeys]) {
                            duplicateClickableItems.push(xpathKeys);
                            continue;
                        }

                        actions.push(...inputActions);
                        this.seenClickableItems[xpathKeys] = true;
                    }
                    else {
                        if(this.seenClickableItems[xpathKeys]) {
                            duplicateClickableItems.push(xpathKeys);
                            continue;
                        }
    
                        this.seenClickableItems[xpathKeys] = true;
                    }

                    // if clickable action is an 'a href', do load if click fails
                    let clickAction = new ClickAction(clickableItem[0], clickableItem[1].text)

                    //if table clickable item then each clickable item should be one single click action in single ActionList
                    if(clickableItem[1].isTableItem) {
                        clickActions.push(new ActionList('core-heuristic generated table click action', [clickAction]));
                        continue;
                    }

                    if (clickableItem[1].canLoadUrl) {
                        let clickOrLoad = new ActionList('clickOrLoad', [], 'or')
                        clickOrLoad.addAction(clickAction)
                        try {
                            let linkFull = new URL(clickableItem[1].href, this.config.parsedUrl.href)
                            if (/^http/.test(linkFull) && this.scanner.allowedToCrawlUrl(linkFull.toString())) {
                                let loadAction = new LoadAction(clickableItem[0], linkFull.toString(), clickableItem[1].text)
                                clickOrLoad.addAction(loadAction)
                            }
                        } catch (err) {
                            // silently eat up invalid href type erros. the try-catch is scoped to keep
                            // the click action anyway.
                        }
                        
                        actions.push(clickOrLoad)
                    } else {
                        actions.push(clickAction)
                    }

                    let filteredActions = [];
                    if (this.excludeItems && !(_.isEmpty(this.excludeItems.checkForElements) || (_.isEmpty(this.excludeItems.filterElements)))) {
                        try {
                            filteredActions = this.ignoreItem(actions);    // exclude particular action
                        } catch (error) {
                            utils.log(`Error : ignoreItems ${error.toString()}`)
                        }
                    }
                    else {
                        filteredActions = actions;                     // normal scan
                    }

                    if(fileUploadItems.length > 0) {
                        for(let fileUploadItem of fileUploadItems) {
                            for(let fileUploadPath in this.config.uploadFilePaths) {
                                fileUploadPath = this.config.uploadFilePaths[fileUploadPath];
                                let filteredActionsCopy = _.cloneDeep(filteredActions);
                                let action = new UploadAction(fileUploadItem[0], fileUploadPath.url, fileUploadItem[1].name, fileUploadPath.fileType);
                                // Insert the action just before the last item in filteredActions
                                filteredActionsCopy.splice(filteredActionsCopy.length - 1, 0, action);
                                clickActions.push(new ActionList('core-heuristic generated', filteredActionsCopy))
                            }
                        }
                    }
                    else {
                        clickActions.push(new ActionList('core-heuristic generated', filteredActions))
                    }
                }
            }

            //Give priority to single clicks
            clickActions = _.chain(clickActions).sortBy(clickAction => {
                return clickAction.actions.length;
            }).value();

            duplicateClickableItems;

            // Form submit actions
            for (let formItem of interestingItems.formItems) {
                let submitActions = formItem[1].potentialSubmitItems
                if (0 == submitActions.length) {
                    // no potential submit items, submit form anyway
                    submitActions = ['']
                }

                for (let submitAction of submitActions) {
                    let actions = []
                    if (inputActions.length) {
                        actions.push(...inputActions)
                    }
                    actions.push(new FormSubmitAction(formItem[0], submitAction, formItem[1].action))

                    formSubmitActions.push(new ActionList('core-heuristic generated', actions))
                }
            }
        }

        // log as well if we are not processing non link items
        if (nonLinkFingerprint && this.seenNonLinkFingerprints.includes(nonLinkFingerprint.fingerPrintData)) {
            logger.log('info', `${pluginName}: Not generating non-link actions on ${interestingItems.location} since all non-link items have been seen on another state`)
        }

        // We can always process links since we have very good dedup logic around that.

        // Links
        let linkActions = []
        for (let linkItem of interestingItems.linkItems) {
            //utils.log(`JSON.stringify linkItem ${JSON.stringify(linkItem[1])}`)
            if (linkItem[1].ifcAction != 'non-click') {
                let linkFull = new URL(linkItem[1].href, this.config.parsedUrl.href)
                linkActions.push(new LoadAction(linkItem[0], linkFull.toString()))
            }
        }

        // Simple Href
        let simpleHrefActions = []
        for (let simpleHref of interestingItems.simpleHrefs) {
            //utils.log(`JSON.stringify linkItem ${JSON.stringify(simpleHref)}`)
            simpleHrefActions.push(new LoadAction('[simple href]', simpleHref.toString()))
        }

        //utils.log(`returnActions ${JSON.stringify(returnActions)}`)
        ret.actions = ret.actions || []
        if (ret.actions) {
            let filteredLinkActions = _.filter(linkActions, (linkAction) => {
                let url = HaikuUtils.removeHashFromURL(linkAction.url);

                let regex = HaikuUtils.getUrlRegex(url);

                if (!this.duplicatePatternUrls[regex]) {
                    // If the regular expression doesn't already exist in the set of duplicate pattern URLs:
                    // Add the regular expression and its corresponding URL to the set.
                    this.duplicatePatternUrls[regex] =  url;
                    return true;
                }

                return false;
            });

            linkActions = filteredLinkActions;

            if(this.config.loginurl == interestingItems.location) {
                ret.actions.push(...clickActions, ...formSubmitActions, ...linkActions, ...simpleHrefActions, ...tableInputActions)
            }
            else {
                ret.actions.push(...linkActions, ...clickActions, ...formSubmitActions, ...simpleHrefActions, ...tableInputActions)
            }
        }
    }

    ignoreItem(actions) {
        if (!this.excludeItems.filterElements[0])
            return actions;    // no filterElement found

        let actionItems = _.map(actions, 'xpath');
        let filteredActions;

        if (actionItems.includes(this.excludeItems.filterElements[0])) {
            if ((_.intersection(this.excludeItems.checkForElements, actionItems)).length === this.excludeItems.checkForElements.length) {
                filteredActions = _.filter(actions,
                    (action) => {
                        return action.xpath !== this.excludeItems.filterElements[0];
                    });

                utils.log(`crawl  ACTION : **Ignoring Action Item**, XPATH : ${this.excludeItems.filterElements[0]}`)
            }
            else {
                filteredActions = actions;
            }
        }
        else {
            filteredActions = actions;
        }

        return filteredActions;
    }
}

module.exports = CoreHeuristics