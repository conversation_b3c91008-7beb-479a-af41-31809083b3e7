
function getBoundingRect(elOrEncXpath) {
    let el = elOrEncXpath
    if ('string' == typeof (el)) {
        let xpath = JSON.parse(atob(elOrEncXpath))
        el = indusfaceRenderer.getElementFromXPath(xpath)
    }

    let boundingRect
    if (el) {
        function isInViewport(rect) {
            // this is from puppeteer code. 
            const style = window.getComputedStyle(el);
            let isVisible = style && style.display !== 'none' && style.visibility !== 'hidden'
            if (!isVisible) {
                return false
            }

            let windowInnerHeight = window.innerHeight || document.documentElement.clientHeight /*or $(window).height() */
            let windowInnerWidth = window.innerWidth || document.documentElement.clientWidth /*or $(window).width() */

            // do we need to do part of rect in viewport check?
            let inViewport = rect.width > 0 && rect.height > 0 &&
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= windowInnerHeight &&
                rect.right <= windowInnerWidth
            return inViewport
        }

        // get the bounding rect & visibiity state
        let rect = el.getBoundingClientRect()
        let inViewport = isInViewport(rect)

        // not in viewport? see if we can bring it into viewport
        if (!inViewport) {
            el.scrollIntoView({ behavior: "instant", block: 'center' })
            rect = el.getBoundingClientRect()
            inViewport = isInViewport(rect)
        }

        boundingRect = {
            left: rect.left,
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom,
            width: rect.width,
            height: rect.height,
            inViewport: inViewport
        }
        //console.log( 'bound rect:', el, window.innerHeight, window.innerWidth, boundingRect)
    }

    return boundingRect
}


function getLoginInfo() {
    // if we see a password type field 
    // and someting that looks like an username field
    // and something like a login button
    // give it a shot.
    //debugger
    let loginInfo
debugger
    // Is there a password type field
    let passwords = Array.from(document.querySelectorAll('input[type="password"]'))
    passwords = passwords.filter((pwd) => {
        // skip invisible password fields
        let rect = indusfaceRenderer.getBoundingRect(pwd)
        return rect.inViewport
    })

    // need at least one password field 
    if (passwords.length) {
        // now look for login or submit type buttons
        let loginActionTypeSelector = ['button', 'submit'].map((a) => {
            return `input[type=${a}]`
        }).join(',')
        let loginActions = Array.from(document.querySelectorAll(loginActionTypeSelector))
        loginActions.push(...Array.from(document.getElementsByTagName('button')))
        loginActions = loginActions.filter((action) => {
            // skip invisible buttons
            let rect = indusfaceRenderer.getBoundingRect(action)
            if (!rect.inViewport) {
                return false
            }
            let loginActionPattern = /Einloggen|login|signin|authenticate|authorize|enter|next|submit/i
            let ssoPattern = /close|facebook|fblogin|google|googlogin|amazon|amzlogin|twitter/i
            let potentialLoginAction = false
            let props = ['name', 'id', 'value', 'innerText', 'provider']
            for (let prop of props) {
                if (!action[prop]) {
                    continue
                }
                let sanitizedVal = action[prop].toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                if (loginActionPattern.test(sanitizedVal) && !ssoPattern.test(sanitizedVal)) {
                    potentialLoginAction = true
                    break
                }
            }
            return potentialLoginAction
        })

        if (loginActions.length == 1) { // cannot handle multiple 'login' type buttons
            // now look for all input fields that have id/name/placeholder that look like
            // user names. Here we dont mind 
            let usernameTypeSelector = ['text', 'email','name'].map((a) => {
                return `input[type=${a}]`
            }).join(',')
            usernameTypeSelector = 'input:not([type]),' + usernameTypeSelector  // no type= defaults to type=text                
            let usernameInputs = Array.from(document.querySelectorAll(usernameTypeSelector)).filter((input) => {
                // skip invisible input items
                let rect = indusfaceRenderer.getBoundingRect(input)
                if (!rect.inViewport) {
                    return false
                }
                let usernamePattern = /user|name|email|mobile|phone|uid|login/i
                let potentialUsername = false
                let props = ['name', 'placeholder', 'id', 'value']
                for (let prop of props) {
                    if (!input[prop]) {
                        continue
                    }
                    let sanitizedVal = input[prop].toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                    if (usernamePattern.test(sanitizedVal)) {
                        potentialUsername = true
                        break
                    }
                }
                return potentialUsername
            })

//            if (usernameInputs.length >= 1) {
                // reached here, we got a potential login, convert to xpaths
                loginInfo = {
                    usernames: usernameInputs.map((u) => { return u }),
                    passwords: passwords.map((p) => { return p }),
                    action: loginActions[0]
                }
            }
//        }
    }

    return loginInfo
}

indusfaceRenderer = { getBoundingRect }
getLoginInfo()