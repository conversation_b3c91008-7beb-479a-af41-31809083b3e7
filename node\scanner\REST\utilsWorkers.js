const debug = require('debug')('REST:utilsWorkers')
const mkdirp = require('mkdirp')
const RabbitMQ = require('../common/lib/rabbitmq')
const logger = require('../common/lib/haiku-logger')
const util = require('util')
const {
    spawn
} = require('child_process')
const HaikuUtils = require('../common/lib/haiku-utils')
const ssAPI = require('../common/lib/sooper-scheduler-api')

const QueueMessage = require('../common/lib/queue-message')
const AnalyzeDupUrls = require('../common/lib/messages/analyze-dup-urls')
const CreateAnimatedCrawlViz = require('../common/lib/messages/create-animated-crawl-viz')


const productName = 'scanapi'

/**
 * Haiku Workers - most of them will spawn shell scripts to do teh actual work
 * This class will listen on the MQ and ensure a limit of number of scripts spawned at a time.
 * Rabbit MQ persistent queues & prefetch is used to safely limit the number of messages being processed
 * at any given point of time.
 */
class HaikuWorkers {
    /**
     * @constructor
     */
    constructor() {
        this.msgQ = new RabbitMQ('scanapi')
        this.logDir = path.join(__dirname, `../logs/worker-logs/`)
        mkdirp.sync(this.logDir)
    }

    /**
     * initialize the msg queue
     */
    async init() {
        // init the message Queue
        await this.msgQ.init()

        // set up message handler
        this.msgQ.consume('lb-utils-request', this.mqRequestHandler.bind(this))
    }

    /**
     * 
     * @param {rabbitMQMessage} mqMsg Rabbit MQ Message object withworker message
     */
    async mqRequestHandler(mqMsg) {
        let msgBeingProcessed = 'unknown'
        try {
            // crack open the msg and perform whatever action we need to 
            let requestMsg = QueueMessage.createMessage(mqMsg)

            // always process the load balanced (RPC) ones. Other than that, only process requests where
            // this scanner is the one performing the scan
            let request = requestMsg ? requestMsg.getContent() : null
            msgBeingProcessed = requestMsg.msgType
            logger.log('info', `received msg type=${msgBeingProcessed}: ${JSON.stringify(request)}`, HaikuUtils.getMetadataForLog(request))
            let worker = null
            switch (msgBeingProcessed) {
                case AnalyzeDupUrls.msgType:
                    worker = this.analyzeDupUrlsWorker.bind(this)
                    break;

                case CreateAnimatedCrawlViz.msgType:
                    worker = this.createAnimatedCrawlVizWorker.bind(this)
                    break;

                default:
                    logger.log('info', `ignoring UNKNOWN MESSAGE ${msgBeingProcessed}`, HaikuUtils.getMetadataForLog(request))
            }

            // wait for script to finish so that we only ack after script done
            // this way (due to prefetch) we limit number of running scripts and let rabbit hold
            // on to the unprocessed requests
            if (worker) {
                await worker(request)
            }
        } catch (err) {
            logger.log('error', `error processing message ${msgBeingProcessed} - ${err}`)
        }

        // ack the message only after it is completely processed
        this.msgQ.ack(mqMsg)
    }

    runCommand(command, args, redirectFile) {
        return new Promise((res, rej) => {
            try {
                let cwd = path.dirname(command)
                logger.log('info', `will run: ${command} ${args} in directory ${cwd}`)
                const out = fs.openSync(redirectFile, "a");
                let child = spawn(command, args, {
                    //detached: true,
                    cwd,
                    stdio: ['ignore', out, out]
                })
                // clean up running list
                child.on('exit', (code) => {
                    res(code)
                })
            } catch (err) {
                logger.log('error', `error: ${err.toString()} command: ${command} ${args} >> ${redirectFile}`)
                rej(err)
            }
        })
    }

    /**
     * Spawn script to generate regex for dup URLS from the paused scan data.
     * @param {AnalyzeDupUrls} request AnalyzeDupUrls request with details for worker
     */
    analyzeDupUrlsWorker(request) {
        logger.log('info', `analyzing dup URLS`, HaikuUtils.getMetadataForLog(request))
        let script = path.join(__dirname, './workerScripts/analyzeDupUrlsWorker.sh')
        return this.runCommand(script, [request.scanId], `${this.logDir}/analyzeDupUrls-${request.scanId}.log`)
    }

    /**
     * Spawn script to create animated gif from captured crawler screenshots
     * @param {createAnimatedCrawlViz} request createAnimatedCrawlViz request with details for worker
     */
    async createAnimatedCrawlVizWorker(request) {
        // May need a better way to do below. We have to let WAS portal know we are done and pass teh filename
        // but file is created in the worker so copying the filename format.
        const gifFilename = `crawl-session-${request.scanlogId}.gif` // hardcoded since utils worker creates the file

        logger.log('info', `Creating animated gif crawl visualization for ${request.scanlogId}`, HaikuUtils.getMetadataForLog(request))
        let script = path.join(__dirname, './workerScripts/createAnimatedCrawlViz.sh')
        await this.runCommand(script, [request.scanlogId], `${this.logDir}/createAnimatedCrawlViz-${request.scanlogId}.log`)
        await ssAPI.crawlVisualizationCreated( request.scanlogId, gifFilename)
        logger.log('info', `Told WAS portal that crawl Visualization (GIF) is ready for ${request.scanlogId}`, HaikuUtils.getMetadataForLog(request))
    }
}

module.exports = HaikuWorkers