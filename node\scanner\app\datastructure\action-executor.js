let utils = require('../ifc-utils.js')
const electron = require('electron')
const EventEmitter = require('events')
const {
    session
} = require('electron'); // ask karthik for what this means
const path = require('path')
const HaikuUtils = require('../../common/lib/haiku-utils')
const _ = require('lodash')
const ParseDomain = require('parse-domain')
const { SCAN_TYPES } = require('../../common/config/app-constants')

class ActionExecutor extends EventEmitter {
    constructor(scanner) {
        super()

        this.scanner = scanner
        this.config = scanner.config
        this.browser = scanner.browser

        this.replayRequired = false // replay needed before executing next action?
        this.nextDocSteadyId = 0
        this.loadPlugins();
        this.config.actionMetrics = {};

        this.didNavigate = true         // initial load should be considered a navigate

        // defaults - TODO : should come from config
        // in millis
        this.maxWaitAfterStartLoading = 3000
        this.maxWaitAfterDidNavigate = 10000
        this.steadyWaitForDocSteady = 500
        this.maxWaitForDocSteady = 5000  // Reduced from 20000 to 5000
        this.maxTimeToWaitForAllLoadAndNavigate = (15 * 1000) // Reduced from 30s to 15s

        // event handlers
        this.browser.webContents.on('did-start-loading', () => {
            this.waitForLoadFinished = true
        })
        this.browser.webContents.on('did-navigate', () => {
            this.didNavigate = true
        })

        // If stop loading fires before the code that waits for it in triggerAction()
        this.browser.webContents.on('did-stop-loading', () => {
            this.didNavigate = false
            this.waitForLoadFinished = false
        })

        // keep track of inflight network requests
        this.trackInFlightRequests();
        if (this.config.scanType == SCAN_TYPES.DEFACEMENT) {
            try {
                scanner.statusInfo = {};

                let host = this.config.parsedUrl.hostname.replace(/^www\./, '')
                let parsedMainDomain = ParseDomain(this.config.parsedUrl.href);
                let isIpAddress = HaikuUtils.isIpAddress(this.config.parsedUrl.hostname);

                if (!this.config.allowSubDomain) {
                    this.filter = {
                        urls: [
                            `http://${host}/*`, `http://www.${host}/*`,
                            `https://${host}/*`, `https://www.${host}/*`,
                        ]
                    }
                }
                else {
                    this.filter = {
                        urls: [
                            `http://*.${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`,
                            `https://*.${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`,
                            `http://${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`,
                            `https://${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`
                        ]
                    }
                }

                if (isIpAddress) {
                    //replace www by empty string from each filter url item
                    this.filter.urls = this.filter.urls.map(url => url.replace('www.', ''));
                }


                //if first scan what to do with following            
                scanner.browser.webContents.session.webRequest.onCompleted(this.filter, (details) => {
                    // Access request headers via details.requestHeaders
                    // Access response headers via details.responseHeaders
                    scanner.statusInfo[details.url] = {url: details.url, statusCode: details.statusCode};
                });

            } catch (error) {
                utils.log(`Error : Unable To register onCompleted Event,${error.toString()}`);
            }
        }
    }

    setReplayIsNeededBeforeNextAction(isReplayRequired= true) {
        this.replayRequired = isReplayRequired
    }

    // keep track of inflight network requests
    trackInFlightRequests() {
        let host = this.config.parsedUrl.host.replace(/^www\./, '')
        let isIpAddress = HaikuUtils.isIpAddress(this.config.parsedUrl.hostname);
        
        let filter = {
            urls: [
            `http://${host}/*`, `http://www.${host}/*`,
            `https://${host}/*`, `https://www.${host}/*`,
            ]
        }

        if (isIpAddress) {
            //replace www by empty string from each filter url item
            filter.urls = filter.urls.map(url => url.replace('www.', ''));
        }
        
        this.inFlightReqs = new Set();
        session.fromPartition(this.scanner.browserPartition).webRequest.onBeforeSendHeaders(filter, ((details, callback) => {
            // Don't track socket.io polling requests
            if (details.url.includes('socket.io') && details.url.includes('transport=polling')) {
                utils.log(`[InFlight] Ignoring socket.io polling request ${details.id} for ${details.url}`);
                callback({
                    cancel: false,
                    requestHeaders: details.requestHeaders
                });
                return;
            }

            this.inFlightReqs.add(details.id);

            if(details.url) {
                this.scanner.emit('found-resource', details.url);
            }

            //Add request headers to indentify malware crawl request in malware antivirus engines logs. i.e. SquidClamAV
            if (this.config.scanType == SCAN_TYPES.MALWARE) {
                this.config.addToRequest = this.config.addToRequest || {};
                this.config.addToRequest.headers = this.config.addToRequest.headers || {};
 
                this.config.addToRequest.headers['Scan-URL'] = details.url;
                this.config.addToRequest.headers['Scan-URL-Method'] = details.method;
                this.config.addToRequest.headers['Crawl-URL'] = details.url;
                this.config.addToRequest.headers['Crawl-URL-Method'] = details.method;
                this.config.addToRequest.headers['Scan-ID'] = this.config.scanId.toString();
                this.config.addToRequest.headers['ScanLog-ID'] = this.config.scanLogId.toString();
                this.config.addToRequest.headers['Date'] = new Date(details.timestamp).toISOString();
            }
           
            // form clamav   - should be conditional if scantype is malware
            // add any additional headers
            HaikuUtils.addAdditionalPerRequestHeaders(details.requestHeaders, this.config.addToRequest)
            
            //utils.log('on before send - in flight reqs:', this.inFlightReqs, details)
            callback({
                cancel: false,
                requestHeaders: details.requestHeaders
            });
        }).bind(this));

        // request completion cases: redirect, success, error
        session.fromPartition(this.scanner.browserPartition).webRequest.onBeforeRedirect(filter, ((details) => {
            if (!details.url.includes('socket.io')) {
                this.inFlightReqs.delete(details.id);
                //utils.log(`[InFlight] Request ${details.id} redirected for ${details.url} - Remaining: ${this.inFlightReqs.size}`);
            }
        }).bind(this));
        
        session.fromPartition(this.scanner.browserPartition).webRequest.onCompleted(filter, ((details) => {
            if (!details.url.includes('socket.io')) {
                this.inFlightReqs.delete(details.id);
                //utils.log(`[InFlight] Request ${details.id} completed for ${details.url} - Remaining: ${this.inFlightReqs.size}`);
            }
        }).bind(this));
        
        session.fromPartition(this.scanner.browserPartition).webRequest.onErrorOccurred(filter, ((details) => {
            if (!details.url.includes('socket.io')) {
                this.inFlightReqs.delete(details.id);
                //utils.log(`[InFlight] Request ${details.id} failed for ${details.url} - Remaining: ${this.inFlightReqs.size} - Error: ${details.error}`);
            }
        }).bind(this));
    }

    loadPlugins() {
        this.plugins = [];
        for (let plugin of this.config.plugins.actionExecutor) {
            let Plugin = require(path.join(__dirname, '../plugins/' + plugin));
            let plug = new Plugin(this);
            this.plugins.push(plug)
            utils.log('action-executor plugin loaded : ' + plug.constructor.name);
        }
    }

    getDefaultExecutionContext() {
        return {
            scanner: this.scanner,
            browser: this.browser,
            executer: this,
            ignoreFailures: true, // in general, for ActionList style of composite rules, ignore errors
            runPlugins: true,
            mode: 'crawl'
        }
    }

    getReplayExecutionContext(context) {
        if (!context) {
            context = this.getDefaultExecutionContext()
        }
        let ec = _.clone(context)
        ec.ignoreFailures = true // for replay, we have to ignore errors so that crawl can proceed
        ec.mode = 'replay'
        return ec
    }

    getNoPluginContext(context) {
        if (!context) {
            context = this.getDefaultExecutionContext()
        }
        let ec = _.clone(context)
        ec.mode = 'no-plugin'
        ec.runPlugins = false
        return ec
    }

    async waitForBrowserEvent(event, maxWait) {
        let executer = this
        await (() => {
            return new Promise(async (resolve, reject) => {
                executer.browser.webContents.once(event, () => {
                    //utils.log(`wait for event received ${event}`);
                    clearTimeout(timerId);
                    resolve()
                })
                let timerId = setTimeout(() => {
                    utils.log(`wait for event ${event} timed out`);
                    resolve()
                }, maxWait)

            })
        })()
    }

    async waitForDocSteady(docSteadyWait, maxWait) {
        let executer = this
        let docSteadyId = this.nextDocSteadyId++; // ; to get next line to format correctly
        await (() => {
            return new Promise(async (resolve, reject) => {
                let timerId = setTimeout(() => {
                    utils.log(`doc steady ${docSteadyId} timed out`);
                    resolve()
                }, maxWait)

                await executer.browser.webContents.executeJavaScript(`indusfaceRenderer.waitForDocSteady(${docSteadyWait},${docSteadyId});`)
                electron.ipcMain.once('document-steady-' + docSteadyId, () => {
                    clearTimeout(timerId);
                    resolve()
                })
            })
        })()
        //utils.log(`doc is steady : ${docSteadyId}`)
    }

    async processActionSuccess(action, executionContext) {
        // wait for the load finished event
        if (executionContext.browser.webContents.isLoading()) {
            this.waitForLoadFinished = true
        }

        // wait for in flight requests to complete. However, wait only so many times for this
        // as some sites will keep firing requests.
        let waitForInFlightRequests = 3 // 3 times.
        if (this.inFlightReqs.size > 0) {
            this.waitForLoadFinished = true
            waitForInFlightRequests--
        }

        // Emergency cut off switch - dont wait more than 2 minutes max
        let startWaitingTimestamp = Date.now()
        while (this.waitForLoadFinished || this.didNavigate) { // while so that another load/navigate event may happen while waiting here
            let maxWaitForLoadFinish = this.maxWaitAfterStartLoading
            if (this.didNavigate) {
                maxWaitForLoadFinish = this.maxWaitAfterDidNavigate
            }
            this.waitForLoadFinished = false
            this.didNavigate = false
            //utils.log( 'waiting for did-stop-loading event for : ', maxWaitForLoadFinish)
            await this.waitForBrowserEvent('did-stop-loading', maxWaitForLoadFinish)
            if (executionContext.browser.webContents.isLoading()) {
                this.waitForLoadFinished = true
            }

            if (waitForInFlightRequests && this.inFlightReqs.size > 0) {
                utils.log('waiting for in flight requests to complete')
                this.waitForLoadFinished = true
                waitForInFlightRequests--
            }

            // dont wait for more than 2 minutes max...
            if ((Date.now() - startWaitingTimestamp) > this.maxTimeToWaitForAllLoadAndNavigate) {
                utils.log('Waiting for all load finished timed out > ', this.maxTimeToWaitForAllLoadAndNavigate / 1000, ' seconds')
                break
            }
        }

        // Successfully triggered action - wait for doc steady again
        await this.waitForDocSteady(this.steadyWaitForDocSteady, this.maxWaitForDocSteady)
    }

    async triggerAction(action, executionContext) {
        executionContext = executionContext || this.getDefaultExecutionContext()

        let url = this.browser.webContents.getURL();
        if(!this.config.actionMetrics[url]) {
            this.config.actionMetrics[url] =  {
                timeSpent: 0,
                totalActions: 0
            }; 
        }
        
        let actionMetric = this.config.actionMetrics[url];
        actionMetric.totalActions = actionMetric.totalActions + 1;


        let startTimeBeforeTrigerAction = new Date().getTime();

        let actionSucceeded = true
        try {
            // run the action
            utils.log(executionContext.mode, ' ', action.toString())

            // pre trigger plugin actions. TODO - move to event based
            let url = executionContext.browser.webContents.getURL()
            if (url && executionContext.runPlugins) {
                executionContext.pluginTookAction = false

                for (let plugin of this.plugins) {
                    if (plugin.preActionExecute) {
                        await plugin.preActionExecute(action, executionContext)
                    }
                }

                if (executionContext.pluginTookAction) {
                    await this.processActionSuccess(action, executionContext)
                }
            }

            // take the action
            // if replay is needed, do the replay now
            if (action.actionType == 'load') {
                this.replayRequired = false
            }
            if (this.replayRequired && action.actionType != 'action-list') {
                this.replayRequired = false
                let replayActions = this.scanner.crawlState.getReplayActions()
                if (replayActions) {
                    await this.triggerAction(replayActions, this.getReplayExecutionContext(executionContext))
                }
            }
            actionSucceeded = await action.execute(executionContext)

            // wait for browser to settle
            if (actionSucceeded) {
                await this.processActionSuccess(action, executionContext)
                url = executionContext.browser.webContents.getURL()
                if (url && executionContext.runPlugins) {
                    executionContext.pluginTookAction = false

                    for (let plugin of this.plugins) {
                        if (plugin.postActionExecute) {
                            await plugin.postActionExecute(action, executionContext)
                        }
                    }

                    if (executionContext.pluginTookAction) {
                        await this.processActionSuccess(action, executionContext)
                    }
                }
            } else {
                utils.log(`Action ${action} failed`)
            }
        } catch (e) {
            utils.log(`Action ${action} failed ${e}`)
            actionSucceeded = false
        }

        actionMetric.timeSpent = parseFloat((actionMetric.timeSpent + ((new Date().getTime() - startTimeBeforeTrigerAction) / 1000)).toFixed(2));

        return actionSucceeded
    }
}

module.exports = ActionExecutor