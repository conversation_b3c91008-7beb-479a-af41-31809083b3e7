const electron = require('electron')
const path = require('path')
const URL = require('url').URL
const {
    ipcMain,
    session
} = require('electron'); // ask karthik for what this means
const EventEmitter = require('events');
const fs = require('fs')
const glob = require('glob')
const mkdirp = require('mkdirp')
const urlPkg = require('url')
const utils = require('../ifc-utils.js')
const HaikuUtils = require('../../common/lib/haiku-utils')
const s3Utils = require('../../common/lib/s3-utils')
const ElectronCertificateError = require('../../common/lib/messages/electron-certificate-error')
const LoadAction = require('../datastructure/load-action')
const logger = require('../../common/lib/haiku-logger')
const QueueMessage = require('../../common/lib/queue-message')
const RefreshCrawlerRequestRpc = require('../../common/lib/messages/refresh-crawler-request-rpc')
const ScanFinished = require('../../common/lib/messages/scan-finished')
const DeferredPromise = require('../../common/lib/deferred-promise')
const createActionList = require('../datastructure/create-action-list')
const SCAN_TYPES = require('../../common/config/app-constants').SCAN_TYPES
const _ = require('lodash')

const ActionExecutor = require('../datastructure/action-executor.js')
const ERROR_CODES = require('../../common/config/app-constants').ERROR_CODES

class IfcScannerDefault extends EventEmitter {
    constructor(config) {
        super()
        this.config = config

        // the message queue
        this.msgQ = config.msgQ

        // defining variables for readability, will be initialized in initForCrawl()
        this.crawlState = null
        this.mainUrl = null
        this.plugins = []
        this.browser = null
        this.screenshotId = this.getScreenshotIdGen()
        this.actionExecutor = null
        this.wasBlocked = false
        this.navigationBlockedUrls = []
        this.browserPartition = null
        this.seenLoadActions = new Set()
        this.actionsToAddToCurrentState = [] // any actions that need to be added to current state while processing eg. new-window
        this.processedSeedUrls = false
        this.pendingOperations = []
        this.defacementHistory = null
        this.didNavigate = false;
        this.config.isLoginSteps = false;

        // pause resume from S3
        this.pauseResumePrefix = 'crawler/paused/'
        this.crawlerPauseNewPrefix = 'crawler/paused-new/'
        this.crawlerNetworkRequestFingerprint = 'crawler/networkRequestFingerprints/'
        this.screenshotsS3Prefix = 'crawler/screenshots/'
        this.crawlerNetworkStatsPrefix = 'crawler/networkStats/'
    }

    onNavigated(event, url, httpResponseCode, httpStatusText) {
        if(this.config.isLoginSteps && this.config.ssoProvider && HaikuUtils.isSameDomain(url, this.config.mainUrl)) {
            this.config.isLoginSteps = false;
        }

        this.didNavigate = true
    }

    anythingBlocked() {
        return this.wasBlocked
    }

    onDefacementHistoryLoaded(defacementHistory){
        this.defacementHistory = defacementHistory;
    }

    resetAnythingBlocked() {
        this.wasBlocked = false
        this.navigationBlockedUrls = []
    }
    actionWasBlocked(url) {
        this.wasBlocked = true
        if (url) {
            this.navigationBlockedUrls.push(url)
        }
    }

    /**
     * add a promise that should be resolved before the next crawl loop iteration 
     * @param {promise} op Promise to wait for before starting the next crawl iteration
     */
    addPendingOperation(op) {
        this.pendingOperations.push(op)
    }

    /**
     * clear all pending operations 
     */
    clearPendingOperations() {
        this.pendingOperations = []
    }

    /**
     * Wait for any pending (non crawl) operation like taking screenshots to complete
     * @param {Number} timeout milliseconds to wait for promise to resolve
     */
    async waitForPendingOperations(timeout = 30 * 1000) {
        if (this.pendingOperations && this.pendingOperations.length > 0) {
            try {
                logger.log('info', `awaiting ${this.pendingOperations.length} pending operations`);
                await utils.timedPromise(Promise.all(this.pendingOperations, 'timed out', timeout))
            } catch (err) {
                logger.log('error', `awaiting pending operations ${err.toString()}`)
            }
        }
    }

    addActionToCurrentState(action) {
        this.actionsToAddToCurrentState.push(action)
    }

    /**
     * generator to get incrementing numbers for screenshot IDs
     * @param {integer} startVal starting value for screenshot IDs
     */
    * getScreenshotIdGen(startVal = 1) {
        let nextScreenshotId = startVal;
        for (;;) {
            yield nextScreenshotId++
        }
    }

    onStartScan() {
        try {
            this.mainUrl = new URL(this.config.mainUrl)
        } catch (err) {
            utils.log(err)
        }

        return this.startScan()
    }

    onStopAutomatedCrawl(reason) {
        this.stopAutomatedCrawl = true
        this.stopReason = reason
    }

    async onLoginStatus(loginStatus, loginInfo, plugin) {
        utils.log(`Login status: ${loginStatus} via plugin ${plugin} for ${this.mainUrl}`)

        // failed login -> take screenshot and email concerned people
        if (!loginStatus) {
            // have we already sent login failed email?
            if (this.reportedLoginFailure) {
                return
            }

            // Screenshot is sent to portal in job completed API via S3 key so that it can be displayed
            // create the screenshot and save to S3
            this.loginFailedS3Image = await this.screenshotToS3('login-may-have-failed')

            this.reportedLoginFailure = true
        }
        else {
            this.config.isLoginSteps = false;
        }
    }

    /**
     * See electron docs for param info
     * The handler keeps track of failures in the requests.
     */
    failLoadHandler(event, errorCode, errorDescription, validatedURL, isMainFrame, frameProcessId, frameRoutingId) {
        if (errorCode == 0 || errorCode == -3) {
            /**  This means success: {"errorCode":0,"errorDescription":"OK"} OR initial page redirected so navigation aborted with -3.
            If main url redirects, sometimes we get an Abort followed by a redirect (eg. to a login page) do not treat this as error.
            Reference: https://github.com/electron/electron/issues/4396
            */
            utils.log(`scan feasibility - ignoring success error code: ${errorCode}, desc: ${errorDescription}`)
            return
        }

        if (!this.failedUrls[validatedURL]) {
            this.failedUrls[validatedURL] = []
        }

        this.failedUrls[validatedURL].push({
            errorCode: errorCode,
            errorDescription: errorDescription
        })
    }

    async checkScanFeasibility(timeToWait) {
        // load the initial page and wait for a bit in case there is any initial  browser check scripts 
        // checkif mainurl load failed and decide scan feasibility based on
        utils.log(`checking scan feasibility for ${this.config.mainUrl} with ${timeToWait / 1000} seconds wait`)
        this.failedUrls = {}
        let eventHandler = this.failLoadHandler.bind(this)

        // load the main scan url and see if we get major errors
        this.resetAnythingBlocked()
        this.browser.webContents.on('did-fail-load', eventHandler)
        this.browser.webContents.loadURL(this.config.mainUrl)
        await utils.sleep(timeToWait) // wait for 'timeToWait' seconds for page load to complete. 
        this.browser.webContents.removeListener('did-fail-load', eventHandler)

        // see if main URL was blocked
        this.scanFeasible = true
        this.scanFeasibilityMsg = 'Scan Feasible'
        if (this.failedUrls[this.config.mainUrl]) {
            this.scanFeasible = false
            this.scanFeasibilityMsg = `got failure in loading scan URL: ${JSON.stringify(this.failedUrls)}`
        } else {
            // see if the page is not a chrome error page
            let docLocation = await utils.timedPromise(this.browser.webContents.executeJavaScript('document.location.href'), 'haiku://timed.out')

            // check for allowed URLs
            let siteLocations = [docLocation, ...this.navigationBlockedUrls]
            for (let loc of siteLocations) {
                if (!this.allowedToCrawlUrl(loc)) {
                    this.scanFeasible = false
                    this.scanFeasibilityMsg = `scan URL redirects to not allowed crawl location: ${loc}`
                    break
                }

                if (this.config.scanType == SCAN_TYPES.MALWARE && this.scanFeasible) {
                    try {
                        let domHTML = await utils.timedPromise(this.browser.webContents.executeJavaScript("document.documentElement.outerHTML", true), null);
                        if (domHTML && domHTML.includes('squid')) {
                            this.scanFeasible = false
                            this.scanFeasibilityMsg = `site not reachable via malware clamav scan`
                            break;
                        }
                    } catch (error) {
                        utils.log(`Error while checking for squid in dom, Reason: ${error.toString()}`);
                    }
                }
            }
        }

        if (this.scanFeasible && Object.keys(this.failedUrls).length) {
            this.scanFeasibilityMsg = `scan URL loaded OK, but got other failures while loading. Scan will continue: ${JSON.stringify(this.failedUrls)}`
        }

        if (!_.isEmpty(this.config.formatError)) {
            switch (this.config.formatError['code']) {
                case ERROR_CODES.ERR_INVALID_URL:
                    this.scanFeasible = false;
                    this.scanFeasibilityMsg = this.config.formatError['msg'];
                    break;
            }
        }

        utils.log(this.scanFeasibilityMsg)
    }


    async startScan() {
        try {
            // Read networkRequestFingerprints from s3/efs 
            let networkRequestFingerprints = await s3Utils.getFile(this.crawlerNetworkRequestFingerprint + '/' + this.config.serviceId, 'networkRequestFingerprints.json');

            if (networkRequestFingerprints && networkRequestFingerprints.Body) {
                this.config.networkRequestFingerprints = JSON.parse(networkRequestFingerprints.Body.toString());
            }
        } catch (error) {
            logger.log('error', `Could not get networkRequestFingerprints : ${error.toString()}`);
        }

        this.initForCrawl();

        // resume if we are asked to 
        if (this.config.resumeScan) {
            logger.log('info', `Asked to resume crawl for session ID ${this.config.scanId}`)

            this.resumeSuccessful = false
            try {
                let currentCrawlerState
                if (!process.env['HAIKU_PAUSE_STORE'] || process.env['HAIKU_PAUSE_STORE'] == 'S3') {
                    let resp = await s3Utils.getFile(this.pauseResumePrefix, this.config.scanId + '.json')
                    if (resp && resp.Body) {
                        currentCrawlerState = resp.Body
                    }
                } else if (process.env['HAIKU_PAUSE_STORE']) {
                    currentCrawlerState = fs.readFileSync(process.env['HAIKU_PAUSE_STORE'] + this.config.scanId + '.json');
                }

                if (currentCrawlerState) {
                    this.deserializeState(JSON.parse(currentCrawlerState))
                    this.resumeSuccessful = true
                } else {
                    logger.log('error', `Could not get paused crawler info from S3, no data in S3 object`)
                }
            } catch (err) {
                logger.log('error', `Could not get paused crawler info from S3 : ${err.message}`)
            }

            // if we could not resume, complain but continue to do a new crawl
            if (!this.resumeSuccessful) {
                logger.log('warn', `Could not get paused crawler info - starting a spanking new crawl`)
            }
        }

        this.crawlStarted = true
        this.crawlCompleted = false

        // Promise will resolve only when scan/crawl is done.
        let scanner = this
        return new Promise(async function (resolve, reject) {
            try {
                scanner.emit('scan-start', scanner.config)

                if (scanner.config.skipCrawl) {
                    utils.log(`Will not crawl since skipCrawl setting is set to ${scanner.config.skipCrawl}`)
                    scanner.stopReason = 'asked to skip crawl'
                } else {
                    utils.log(`maxDepth, maxActions= ${scanner.config.maxDepth} ${scanner.config.maxActions}`)
                }

                // check scan feasibility
                let scanFeasibilityTimetoWait = 20 * 1000
                for (let i = 1; i <= 5; i++) {
                    await scanner.checkScanFeasibility(scanFeasibilityTimetoWait * i)
                    if (scanner.scanFeasible) {
                        break
                    }
                }
                utils.log(`scan feasible = ${scanner.scanFeasible}, ${scanner.scanFeasibilityMsg}`)

                // Screenshot is sent to portal in job completed API via S3 key so that it can be displayed
                if (!scanner.scanFeasible) {
                    scanner.stopReason = 'scan not feasible';
                    if (scanner.config && scanner.config.formatError && scanner.config.formatError['code']) {
                        scanner.stopReSason = scanner.config.formatError['msg'];
                    }

                    try {
                        // create the screenshot and send to S3
                        //TO DO: If an error, crawler getting caught in loop error and missing existing metrics, isPartial & crawlTreeInfo data 
                        //When completing crawl with error. Resolve error later related to screenshot upload.
                        utils.log('Creating screenshot for feasibility failed');
                        scanner.scanFeasibilityS3Image = await scanner.screenshotToS3('screenshot-feasibility-failed');
                        utils.log('Screenshot created for feasibility failed');
                    } catch (error) {
                        utils.log(`Error while uploading screenshot-feasibility-failed image, Reason: ${error.toString()}`);
                    }
                }

                // Upload initial log immediately so that we get the feasibility status even if spot instance stops
                // before the first periodic save.
                scanner.nextSnapshotTime = Date.now() + scanner.config.periodicSaveStateMins * 60 * 1000

                // Do the crawl if possible
                if (scanner.scanFeasible && !scanner.config.skipCrawl) {
                    await scanner.iterateCrawlTree()

                    if (!scanner.stopAutomatedCrawl) {
                        // All crawl tree iteration is done. See if we need to add the seed URLS
                        if (!scanner.processedSeedUrls) {
                            // add seed URL load actions to the root and process them
                            scanner.processSeedUrls()
                        }

                        // do the crawl tree iteration again to process the seed url actions
                        await scanner.iterateCrawlTree(scanner)
                    }
                }

                if (scanner.stopAutomatedCrawl) {
                    utils.log(`asked to exit crawl loop: ${scanner.stopReason}`)
                }

                let stopReason = scanner.stopReason ? scanner.stopReason : 'no more actions'
                let ret = {}
                utils.log(`emmiting scan-loop-done with reason ${stopReason}`);
                scanner.emit('scan-loop-done', stopReason, ret, utils)
                scanner.crawlCompleted = true
                utils.log('Crawler done : ', stopReason)

                scanner.crawlTreeInfo = scanner.crawlState.getCrawlInfo()
                scanner.isPartial = scanner.crawlTreeInfo.unprocessedStates > 0

                // any pending operations to finish?
                await scanner.waitForPendingOperations()

                // save the crawler paused info, screenshots & logs
                await scanner.saveCrawlerState()
                await scanner.saveScreenshotsToS3();
                await scanner.saveLogToS3()

                // write metrics
                scanner.metrics = ret.metrics || {}
                let metricsFilename = path.join(scanner.config.logPath, 'crawl-metrics.json')
                fs.writeFileSync(metricsFilename, JSON.stringify(scanner.metrics))
                //await s3Utils.uploadFile(scanner.config.s3OutPrefix, 'crawl-metrics.json', metricsFilename)
                utils.log('----- Metrics -----', JSON.stringify(scanner.metrics))
                // Pull out individual metrics to make it easier to query in elastic
                for (let metric of Object.keys(scanner.metrics)) {
                    utils.log(`CRAWLERMETRIC ${metric} : `, JSON.stringify(scanner.metrics[metric]))
                }

                // haiku crawler is done now.
                scanner.emit('crawl-end-processing-done', stopReason)

                resolve('done')
            } catch (err) {
                utils.log('caught error in main crawler (promise) loop', err)
                reject(err)
            }
        })
    }

    /**
     * Do complete iteration of crawl tree, taking actions and discovering new states
     */
    async iterateCrawlTree() {
        while (this.moreActionsToProcess()) {
            this.emit('next-iterate-crawl-tree')

            await this.doOneCrawlTreeIteration()
            if (this.stopAutomatedCrawl) {
                break
            }

            // move to root to restart iteration of crawl tree to process
            // actions which were skipped due to limit of actions per state
            this.crawlState.moveToRoot()
        }
    }

    /**
     * Go through the crawl tree from current state, processing actions and creating new states as needed
     * The crawl tree may be only paryially covered in one iteration since we have a limit on number of
     * actions that can be processed per state.
     */
    async doOneCrawlTreeIteration() {
        let actionExecutor = this.actionExecutor
        let crawlState = this.crawlState
        let crawlActions = this.crawlState.getIterator()
        let next = crawlActions.next(false) // goto 1st action.

        let curStateId = -1
        let actionsToProcessForThisState
        while (!next.done) {
            // wait for any pending (non-crawl) actions 
            await this.waitForPendingOperations()
            this.clearPendingOperations()

            // check for scan stop event
            if (this.stopAutomatedCrawl) {
                break
            }

            let actions = next.value.action
            try {
                if (curStateId != this.crawlState.currentContext.getId()) {
                    // changed states 
                    curStateId = this.crawlState.currentContext.getId()
                    actionsToProcessForThisState = this.config.maxActionsPerStatePerIteration - 1 // -1 because we execute action then check
                }
                // take snapshot of crawl (save state) if needed
                if (Date.now() > this.nextSnapshotTime) {
                    try {
                        this.saveCrawlerState()
                        await this.saveScreenshotsToS3();
                    } catch (err) {
                        logger.log('error', `Ignoring error in crawl loop, periodic save state code: ${err.tostring()}`)
                    }
                    this.nextSnapshotTime = Date.now() + this.config.periodicSaveStateMins * 60 * 1000
                }

                // see if we need to get a refreshed request i.e. up to date (in terms of session, params) for a request captured earlier
                await this.processRefresheCrawlerRequest()

                utils.log(`curNode - depth, doneActions/totalActions = ${crawlState.currentContext.depth} ${crawlState.currentContext.getActionDoneCount()}/${crawlState.currentContext.getActionCount()}`)
                let maxDepthExceeded = (crawlState.currentContext.depth > this.config.maxDepth)
                if (maxDepthExceeded) {
                    next = crawlActions.next(true)
                    continue
                }

                // we are going to perform next action
                this.emit('next-action', actions, crawlState)

                // See if action leads to a dup state base don offline analysis of previous scans
                let preTriggerResult = {}
                this.emit('pre-trigger', preTriggerResult, actions)

                // are we sure its a dup?
                if (preTriggerResult.skipAction) {
                    this.emit('skip-before-trigger', actions)
                    utils.log('\tskipping action before triggering ', actions)
                    next = crawlActions.next(false)
                    continue
                }

                // take action
                let lookforInterestingItems = false
                if (next.value.replayReqd) {
                    actionExecutor.setReplayIsNeededBeforeNextAction()
                }

                this.resetAnythingBlocked()

                if (actions && actions.actions) {
                    utils.log(`\t** going to trigger ${actions.actions.length} actions`)
                } else if (actions) {
                    utils.log(`\t** going to trigger ${actions.length} actions`)
                }
                this.actionsToAddToCurrentState = [] // clear the actionsToAdd.. bit
                lookforInterestingItems = await actionExecutor.triggerAction(actions)
                this.processActionsToAddToCurrentState()
                if (!actions.mustRunAtInit) {
                    // need to process all the 'must run at init' actions, cannot optimize them away
                    actionsToProcessForThisState--
                }

                // check for scan stop event
                if (this.stopAutomatedCrawl) {
                    continue // let code at loop start do the logging etc.
                }

                // post triggering action event
                this.emit('post-trigger')

                if (!lookforInterestingItems) {
                    utils.log('action set failed, go to next action')
                    actionExecutor.setReplayIsNeededBeforeNextAction()
                    next = crawlActions.next(actionsToProcessForThisState <= 0)
                    continue
                }
                utils.log('action done (doc steady)')

                // gather info after action
                // kk -> this should also go to a plugin ...
                let interestingItems;
                if (this.config.scanType == 'defacement')
                    interestingItems = await utils.timedPromise(this.browser.webContents.executeJavaScript("indusfaceRenderer.getInterestingItems(true,'defacement');", true), null)
                else
                    interestingItems = await utils.timedPromise(this.browser.webContents.executeJavaScript('indusfaceRenderer.getInterestingItems(true);', true), null)   // normal scan including malware


                if (!interestingItems) {
                    utils.log('Could not get interesting Items for this action (maybe error/timedout), continuing crawl')
                    // we need replay
                    actionExecutor.setReplayIsNeededBeforeNextAction()
                    next = crawlActions.next(actionsToProcessForThisState <= 0)
                    continue
                }

                interestingItems.serverData = {}
                interestingItems.serverData.preTriggerResult = preTriggerResult
                interestingItems.serverData.actionMayBeBlocked = this.anythingBlocked()
                // save screenshot ID in context 
                // TODO - CHANGE TO CONTEXT ID
                interestingItems.serverData.screenshotID = this.screenshotId.next().value; // ; to get next line formatted correctly :-)
                
                utils.log(`**LOCATION**:${interestingItems.location}`)

                let pluginRet = {}
                this.emit('got-interesting-items', pluginRet, interestingItems, this.crawlState)
                
                // check for scan stop event
                if (this.stopAutomatedCrawl) {
                    continue // let code at loop start do the logging etc.
                }

                let retryCurrentAction = false
                if (pluginRet && pluginRet.skipState) {
                    utils.log(`Skipping duplicate. Dup state check said ${pluginRet.skipState}, pre trigger said ${preTriggerResult.checkAlreadyVisited.dupAction}, guess? ${preTriggerResult.checkAlreadyVisited.isGuess}`)
                    this.emit('skip-state', actions)
                    next = crawlActions.next(actionsToProcessForThisState <= 0)

                    // we need replay
                    actionExecutor.setReplayIsNeededBeforeNextAction()
                    continue
                }

                // if we reach here, found an unique state

                // create state even if we don't have any actions to do for that state. Needed for fingerprinting to detect dup states
                let newState = this.crawlState.createState()
                newState.addInterestingItems(interestingItems)
                newState.addActions(pluginRet.actions)
                newState.addActionFingerprint(preTriggerResult.fingerprint.actionFingerprint)

                this.emit('unique-state', actions, interestingItems, this.crawlState, newState)
            } catch (err) {
                utils.log('error in main event loop, going to next action', err.toString())
            }
            // ensure browser state is where we expect
            actionExecutor.setReplayIsNeededBeforeNextAction()

            // rinse and repeat
            next = crawlActions.next(actionsToProcessForThisState <= 0)
        }
    }

    /**
     * Save all screenshots to S3 and delete the local png files.
     * @param {boolean} deleteLocalFiles do you really need an explanation?
     */
    async saveScreenshotsToS3(deleteLocalFiles = true) {
        // zip all png, json (metadata) files into a tar.gz
        let filespec = '{*.png,*.json}'
        let screenshotFiles = glob.sync(filespec, {
            cwd: this.config.scrPath
        })
        if (screenshotFiles.length < 1) {
            // no files, nothing to do
            return
        }

        let secsSinceStart = Math.floor((Date.now() - this.config.scanStartTime) / 1000)
        let zipFilename = `screenshots-${secsSinceStart}.tar.gz`
        let zipPath = path.join(this.config.scrPath, zipFilename)
        // this call wil create archive with all screenshots under root
        await HaikuUtils.zipFiles(filespec, zipPath, this.config.scrPath)

        // upload the archive
        await s3Utils.uploadFile(this.screenshotsS3Prefix + `${this.config.scanLogId}/`, zipFilename, zipPath)

        // delete all the png files and archive if asked to do so.
        if (deleteLocalFiles) {
            screenshotFiles.map(file => {
                fs.unlinkSync(path.join(this.config.scrPath, file))
            })
            fs.unlinkSync(zipPath)
        }
    }

    /**
     * checks if there is any action to process in the crawl tree.
     */
    moreActionsToProcess() {
        let contextIterator = this.crawlState.getVisitor()

        let nextContext = contextIterator.next(false)
        while (!nextContext.done) {
            let ctx = nextContext.value
            if (ctx.moreActions()) {
                return true
            }
            nextContext = contextIterator.next(false)
        }
        return false
    }

    /**
     * Add seed URLs to the root state.
     */
    processSeedUrls() {
        this.crawlState.moveToRoot()
        for (let url of this.config.seedUrls) {
            this.loadUrlInCurrentState(url);
        }
        this.processActionsToAddToCurrentState();
        this.processedSeedUrls = true;
    }

    /**
     * See if there are any actions to add to the current state - typically 'new-window' events rerouted to load action.
     */
    processActionsToAddToCurrentState() {
        if (this.actionsToAddToCurrentState.length) {
            this.crawlState.addActionsToCurrentState(this.actionsToAddToCurrentState);
            this.actionsToAddToCurrentState = [];
        }
    }

    /**
     * Save screenshot to S3 with the name given
     * @param {string} name base name of object in S3, prefix is same for entire scan and created using scanlogid, date etc.
     */
    async screenshotToS3(name) {
        let scanner = this
        let img = await scanner.browser.webContents.capturePage();
        let s3name = name + this.screenshotId.next().value + '.png';
        fs.writeFileSync(this.screenshotsS3Prefix + s3name, img.toPNG());
        s3Utils.uploadFile(this.config.s3OutPrefix, s3name, this.screenshotsS3Prefix + s3name);
        return JSON.stringify({
            prefix: this.config.s3OutPrefix,
            name: s3name
        });
    }

    /**
     * Save the log file to S3
     */
    async saveLogToS3() {
        let ret = false

        if (!this.config.saveCrawlerLogs)
            return;
            
        if (this.config.outFile) {
            try {
                ret = await s3Utils.uploadFile(this.config.s3OutPrefix, `haiku-crawler-${this.config.scanId}-${this.config.scanStartTime.toISOString()}.log`, this.config.outFile)
            } catch (err) {
                utils.log(`Could not save log '${this.config.outFile}' to S3 : ${err.toString()}`)
            }
        }
        return ret
    }

    /**
     * Save the current crawl state to persistent storage
     */
    async saveCrawlerState() {
        logger.log('info', 'Saving crawler state to S3');

        try {
            await s3Utils.upload(this.crawlerNetworkRequestFingerprint + '/' + this.config.serviceId, 'networkRequestFingerprints.json', JSON.stringify(this.config.networkRequestFingerprints));
        } catch (err) {
            logger.log('error', `Could not save networkRequestFingerprints : ${err.toString()}`);
        }

        let serializeData = this.serializeState();
        let currentCrawlerState = serializeData.currentCrawlerState;
        let crawlerStateForScanner = serializeData.crawlerStateForScanner;

        if (!process.env['HAIKU_PAUSE_STORE'] || process.env['HAIKU_PAUSE_STORE'] == 'S3') {
            await s3Utils.upload(this.pauseResumePrefix, this.config.scanId + '.json', JSON.stringify(currentCrawlerState));
            await s3Utils.upload(this.crawlerPauseNewPrefix, this.config.scanId + '.json', JSON.stringify(crawlerStateForScanner));
        } else if (process.env['HAIKU_PAUSE_STORE']) {
            try {
                await s3Utils.upload(this.crawlerPauseNewPrefix, this.config.scanId + '.json', JSON.stringify(crawlerStateForScanner));
            } catch (error) {
                logger.log('error', `Could not save crawlerStateForScanner : ${error.toString()}`);
            }
            
            mkdirp.sync(process.env['HAIKU_PAUSE_STORE']);
            fs.writeFileSync(process.env['HAIKU_PAUSE_STORE'] + this.config.scanId + '.json', JSON.stringify(currentCrawlerState));
        }
    }

    /**
     * Save the crawler network stats to storage for failed scans reporting
     */
    async saveCrawlerNetworkStat() {
        try {
            if (this.config) {
                utils.log(`Network stats generation started for scanlogId: ${this.config.scanLogId}`);
                let crawlerNetworkStats = await HaikuUtils.generateNetworkStats(new URL(this.mainUrl).host);
                let scanlogId = this.config.scanLogId;
                let serviceId = this.config.serviceId;
                await s3Utils.upload(this.crawlerNetworkStatsPrefix + serviceId + '/', `networkStats-${scanlogId}.json`, JSON.stringify(crawlerNetworkStats));
                utils.log(`Network stats saved for scanlogId: ${scanlogId}`);
            }
        } catch (error) {
            utils.log(`Error while generating network stats, Reason: ${error.toString()}`);
        }
    }

    /**
     * get the refreshed request i.e. up to date (in terms of session, params) for a request captured earlier
     */
    async processRefresheCrawlerRequest() {
        if (!this.refreshCrawlerRequest) {
            return
        }

        // have something to process
        let refreshInfo = this.refreshCrawlerRequest.request
        try {
            utils.log(`refreshing crawler actions ${JSON.stringify(refreshInfo)}`)

            // let the network actions capture plugin know that we are refreshing and get the refreshed request
            this.emit('refresh-request-start', refreshInfo);

            // replay that crawler state + action
            let refreshActions
            if (refreshInfo.crawlerBookmark) {
                refreshActions = this.crawlState.getActionsForBookmark(refreshInfo.crawlerBookmark)
            } else if (refreshInfo.wizardGuide) {
                refreshActions = createActionList(refreshInfo.wizardGuide)
            }
            if (refreshActions && refreshActions.getNumActions() > 0) {
                this.actionExecutor.setReplayIsNeededBeforeNextAction(false);
                this.browser.setProgressBar(0.5) // for debugging - visual indicator of bookmark action
                await this.actionExecutor.triggerAction(refreshActions)
            }
            let response = {
                scanId: refreshInfo.scanId,
                scanlogId: refreshInfo.scanlog_id,
                scanner: 'haiku'
            }
            this.emit('refresh-request-finish', response);

            // reply with refreshed request if any
            this.refreshCrawlerRequest.requestMsg.rpcReply(this.msgQ, response);
        } catch (err) {
            utils.log(`Error refreshing crawler actions ${err.toString()}`)
        }

        // done
        this.refreshCrawlerRequest = null;
        this.browser.setProgressBar(-1) // for debugging - visual indicator of bookmark action
        this.actionExecutor.setReplayIsNeededBeforeNextAction();
        utils.log(`Done refreshing crawler actions ${JSON.stringify(refreshInfo)}`)
    }

    /**
     * Get an object capturing current scanner state that can be JSONified and stored.
     */
    serializeState() {
        // get current crawl tree
        let serializedCrawlContext = this.crawlState.serializeState()

        // Plugin state
        let pluginData = {}
        this.emit('serialize-state', pluginData);
        let ret = {};
        this.emit('generate-crawler-metric', this.stopReason ? this.stopReason : 'interval metric update', ret);

        // scanner state
        let crawlerState = {
            nextScreenshotId: this.screenshotId.next().value,
            seenLoadActions: Array.from(this.seenLoadActions),
            processedSeedUrls: this.processedSeedUrls,
            pluginData
        }

        let currentCrawlerState = {
            frozenOn: new Date(),
            version: '4.0',
            serviceId: this.config.serviceId,
            crawler: crawlerState,
            crawlContext: serializedCrawlContext,
        }

        let crawlTreeInfo = this.crawlState.getCrawlInfo()
        let isPartial = crawlTreeInfo.unprocessedStates > 0


        //Crawler statistics which will be used by scanner when it finished (i.e. jobsession compelete) the scan & updating to WAS portal for scan stats. 
        //We can't use currentCrawlerState which is incompatible with scanner stats. Hence this object exist.
        let crawlerStateForScanner = {
            scanId: this.config.scanId,
            scanlog_id: this.config.scanLogId,
            serviceId: this.config.serviceId,
            scanner: 'haiku',
            mainUrl: this.config.mainUrl,
            resumeRequested: this.config.resumeScan,
            resumeSuccessful: this.resumeSuccessful,
            scanFeasible: this.scanFeasible,
            scanFeasibilityMsg: this.scanFeasibilityMsg,
            scanFeasibilityS3Image: this.scanFeasibilityS3Image,
            loginFailedS3Image: this.loginFailedS3Image,
            isPartial: isPartial,
            crawlTreeInfo: crawlTreeInfo,
            metrics: ret.metrics
        }

        return {
            currentCrawlerState,
            crawlerStateForScanner
        }
    }

    /**
     * Restore scanner state from serilized object. 
     * --> can only be done BEFORE starting scan <--
     * @param {Object} currentCrawlerState Serialized object (object not file/JSON string)
     */
    deserializeState(serializedCrawlerState) {
        // When we increment version info to the serialized data, add checks based on version
        utils.log(`Restoring crawler state stored on ${serializedCrawlerState.frozenOn}, version ${serializedCrawlerState.version}`)

        // --- version 3 info
        if (serializedCrawlerState.version > 2) {
            // preserve screenshot numbers across pause & resume.
            if (serializedCrawlerState.crawler.nextScreenshotId) {
                this.screenshotId = this.getScreenshotIdGen(serializedCrawlerState.crawler.nextScreenshotId)
            }
        }

        // --- version 2 info
        // processed seed URLs?
        this.processedSeedUrls = true // before version 2, seed urls were processed in the beginning so always true
        if (serializedCrawlerState.version > 1) {
            this.processedSeedUrls = serializedCrawlerState.crawler.processedSeedUrls
        }

        // -- common
        // restore crawl tree
        this.crawlState.deserializeState(serializedCrawlerState.crawlContext)

        // haiku crawler
        if (serializedCrawlerState.crawler.seenLoadActions) {
            for (let el of serializedCrawlerState.crawler.seenLoadActions) {
                this.seenLoadActions.add(el)
            }
        }

        // Plugin state
        this.emit('deserialize-state', serializedCrawlerState.crawler.pluginData)
    }

    /**
     * resolve the deferrred can exit promise allowing crawler to exit
     * @param {string} why reason why we can exit - timeout, scanner said done etc.
     */
    setCanExit(why) {
        utils.log(`crawler can now exit: ${why}`)
        if (this.canExitPromise) {
            this.canExitPromise.__resolve(why)
        }
    }

    /**
     * even after automated crawl done, have to stick around to process scanner requests
     * can exit either when we have timed out OR when scanner says it is done OR when we are stopped forcefully
     */
    canExit() {
        return this.canExitPromise ? this.canExitPromise.p : true
    }

    // ----------------------
    // internal methods
    initForCrawl() {
        // This controls if crawler exits after the automated scan is done
        this.canExitPromise = new DeferredPromise()

        // when time is up, we can exit
        setTimeout(this.setCanExit.bind(this, 'timeout'), this.config.maxScanTime * 60 * 1000)

        // set up the Rabbit MQ msg handlers.
        this.msgQ.consume('crawler-request', this.mqRequestHandler.bind(this))

        // create the crawl state data structure.
        this.crawlState = require('../datastructure/ifc-crawlstate-factory.js').createCrawlstateDS(this.config)

        // ** Technical Debt ** 
        // This is where initial crawl actions should be added not in crawlstate-context since we now have addActionsToCurrentState
        // ** Technical Debt ** 

        this.createWindow()
        this.hookElectronEvents()

        // register event listeners
        this.on('stop-automated-crawl', this.onStopAutomatedCrawl)
        this.on('login-status', this.onLoginStatus)

        // load plugins
        this.config.scanStartTime = new Date()
        
        for (let plugin of this.config.plugins.crawler) {
            let Plugin = require(path.join(__dirname, '../plugins/' + plugin))
            let plug = new Plugin(this) // plugin will merge its part of config
            this.plugins.push(plug)
            utils.log('Plugin loaded : ' + plug.constructor.name)
        }

        for (let plugin of this.config.plugins.network) {
            let Plugin = require(path.join(__dirname, '../plugins/' + plugin))
            let plug = new Plugin(this)
            this.plugins.push(plug)
            utils.log('Network plugin loaded : ' + plug.constructor.name)
        }

        // create the Executor
        this.actionExecutor = new ActionExecutor(this)
    }

    mqRequestHandler(mqMsg) {
        let msgBeingProcessed = 'unknown'
        try {
            // crack open the msg and perform whatever action we need to 
            let requestMsg = QueueMessage.createMessage(mqMsg)

            // ack message to rabbitMQ
            this.msgQ.ack(mqMsg)

            // Only process requests for this scan.
            let request = requestMsg ? requestMsg.getContent() : null
            let shouldProcessMessage = request && (request.scanId == this.config.scanId)
            if (shouldProcessMessage) {
                msgBeingProcessed = requestMsg.msgType
                let tolog = {
                    msgType: msgBeingProcessed,
                    timestamp: (new Date()).toUTCString(),
                    request
                }
                fs.appendFile(`${this.config.logPath}/received-msg.json`, `${JSON.stringify(tolog)}`, (err) => {
                    if (err) {
                        logger.log('error', `could not append request to file: ${err}`);
                    }
                })
                switch (msgBeingProcessed) {
                    case RefreshCrawlerRequestRpc.msgType:
                        this.onRefreshRequest(request, requestMsg)
                        break;

                    case ScanFinished.msgType:
                        this.setCanExit('scan-finsihed')
                        break;

                    default:
                        logger.log('info', `UNKNOWN MESSAGE ${msgBeingProcessed}`)
                }
            }
        } catch (err) {
            logger.log('error', `error processing message ${msgBeingProcessed} - ${err}`)
        }
    }

    /**
     * refresh means rerun the crawler request and if we get the network action indicated in the request,
     * reply with that new (refreshed) network request.
     * @param {RefreshCrawlerRequestRpcMsgContent} request data about crawler request to refresh
     * @param {QueueMessage} requestMsg The actual Msg (needed to send RPC response)
     */
    onRefreshRequest(request, requestMsg) {
        this.refreshCrawlerRequest = {
            request,
            requestMsg
        }

        if (this.crawlCompleted) {
            // if crawl is running, it will process the refresh at the appropriate time. If it is completed
            // we can directly process here
            this.processRefresheCrawlerRequest()
        }
    }

    async createWindow() {
        // --- Add IPC Listener for Preload Config ---
        // This is used to pass config from main.js to preload.js during bootstrap process
        // Do not pass whole config object since it will be huge and not all properties are needed in preload        
        let preloadConfig = {
            useFullXPath: this.config.useFullXPath, // Define the config value(s) here
            ignoreTableCrawlOptimizatiion: this.config.ignoreTableCrawlOptimizatiion
        };
        // Listen for the config request from the preload script
        
        ipcMain.on('get-initial-config', (event) => {
          console.log('Main Process: Handling synchronous config request from preload.');
          // Send the config back synchronously
          event.returnValue = preloadConfig;
        });
        
        // create browser window.
        this.browserPartition = this.config.scanId + '-' + this.config.scanLogId + '-' + Date.now()
        this.browser = new electron.BrowserWindow({
            width: this.config.browser.width,
            height: this.config.browser.height,
            show: this.config.browser.show,
            webPreferences: {
                webSecurity: this.config.webSecurity,
                allowRunningInsecureContent: true,
                nodeIntegration: false,
                images: true, // may need to move this into network load callback for flexibility (eg. screenshot)
                preload: path.join(__dirname, '../browser/indusface-bootstrap.js'),
                partition: this.browserPartition,
                contextIsolation: true,
                sandbox: false   // imp for electron after upgrade 32.0.0
            }
        })

        if (!_.isEmpty(this.config.addToRequest)) {
            if (this.config.addToRequest.headers && this.config.addToRequest.headers['User-Agent']) {
                //get value of User-Agent from config file and set it to browser.webcontents
                this.browser.webContents.setUserAgent(this.config.addToRequest.headers['User-Agent']);
            }
        }

        this.browser.webContents.on('did-navigate', this.onNavigated.bind(this))

        // this.browser.webContents.session.clearCache();
        // this.browser.webContents.session.clearStorageData();

        this.browser.webContents.on('certificate-error', async function (event, url, error, certificate, callback) {
            // ignore certifiate errors
            utils.log(`Ignoring certificate error: ${error} in url ${url}`)
            event.preventDefault()
            callback(true)

            // send this error to Haiku scanner
            if (!this.sentCertErrorToScanner && this.hostInAllowedDomains(url)) {
                this.sentCertErrorToScanner = true
                // send certifcate error to scanner to log as an issue
                let partialCert = _.clone(certificate)
                delete partialCert.data
                delete partialCert.issuerCert
                let certErrContent = {
                    scanId: this.config.scanId,
                    scanlog_id: this.config.scanLogId,
                    uri: url,
                    scanner: 'haiku',
                    chromiumNetErr: error,
                    partialCert: partialCert
                }
                let msg = new ElectronCertificateError(certErrContent)
                let ret = await msg.publish(this.config.msgQ)
                utils.log(`Sending certificate error to scanner ${JSON.stringify(certErrContent)}\nret is ${ret}`)
            }
        }.bind(this));

        /*      function f(name) {
                    return () => {
                        utils.log('global event handler: ', name)
                    }
                }
                this.browser.webContents.on('did-start-loading', f('did-start-loading'));
                this.browser.webContents.on('did-stop-loading', f('did-stop-loading'));
                this.browser.webContents.on('did-finish-load', f('did-finish-load'));
                this.browser.webContents.on('did-fail-load', f('did-fail-load'));
                this.browser.webContents.on('did-frame-finish-load', f('did-frame-finish-load'));
                this.browser.webContents.on('dom-ready', f('dom-ready'));
                this.browser.webContents.on('did-navigate', f('did-navigate'));
                this.browser.webContents.on('did-navigate-in-page', f('did-navigate-in-page'));
                this.browser.webContents.on('login', f('login'));
        */
    }

    /**
     * Check if domain of URL is same as that of scan URL
     *  We will only accept exact domain that has been registered. This means that the below will not be crawled
     *  Registered xyz.com and link to blogs.xyz.com (no child domains)
     *  Regsitered blogs.xyz.com and link to xyz.com (no parent domains)
     *  Registered blogs.xyz.com and link to products.xyz.com (no sibling domains)
     *  One exception is that we will allow both www.<domain> and <domain> when either
     *  <domain> or <www.domain...> is registered
     * 
     * *** Technical debt ***
     * Very similar code in crawler & scanner (as canAttackUrl), should refactor to common lib
     * *** Technical debt ***
     * 
     * @param {string} url url to check 
     */
    hostInAllowedDomains(url) {
        // We will only accept exact domain that has been registered. This means that the below will not be crawled
        // Registered xyz.com and link to blogs.xyz.com (no child domains)
        // Regsitered blogs.xyz.com and link to xyz.com (no parent domains)
        // Registered blogs.xyz.com and link to products.xyz.com (no sibling domains)
        // One exception is that we will allow both www.<domain> and <domain> when either
        // <domain> or <www.domain...> is registered
        let okToCrawl = false
        try {
            let parsedUrl = urlPkg.parse(url)
            if (parsedUrl.host.replace(/^www\./, '') == this.config.parsedUrl.host.replace(/^www\./, '')) {
                okToCrawl = true
            }

            // JIRA TAS-2219 - Allow sub domain. scan url = abc.indusface.com, allow http://abc.indusface.com , http://indusface.com , http://site1.abc.indusface.com , blogs.indusface.com etc.
            if(this.config.allowSubDomain) {
                okToCrawl = HaikuUtils.isSameDomain(url, this.config.parsedUrl.href);
            }
        } catch (err) {
            utils.log(`hostInAllowedDomains, returning false due to exception ${err}`)
            okToCrawl = false
        }
        return okToCrawl
    }


    /**
     * See if we are allowed to crawl URL
     * 
     * *** Technical debt ***
     * Very similar code in crawler & scanner (as canAttackUrl), should refactor to common lib
     * *** Technical debt ***
     * 
     * @param {string} url url to check 
     */
    allowedToCrawlUrl(url, parentUrl) {
        // First check if we are specifically allowing a particular page eg. SSO page outside the scan subdomain
        // The check is very tight
        for (let forceAllow of this.config.unsafeForceAllowCrawl) {
            if (url.startsWith(forceAllow)) {
                utils.log(`allowedToCrawlUrl, allowing since forceAllow ${forceAllow} matches URL ${url}`)
                return true
            }
        }

        // Check allowed domain and then site specifc crawl restrictions
        let okToCrawl = false

        try {
            okToCrawl = this.hostInAllowedDomains(url)

            // check if we are restricting crawl to a sub path hierarchy
            if (okToCrawl && this.config.restrictCrawlToPath) {
                okToCrawl = url.includes(this.config.restrictCrawlToPath)
            }

            // also check if the URL is in excludeist
            if (okToCrawl && this.config.excludeUrls) {
                for (let exludedPath of this.config.excludeUrls) {
                    if (url.includes(exludedPath)) {
                        okToCrawl = false
                        break
                    }
                }
            }

        } catch (err) {
            utils.log(`allowedToCrawlUrl, returning false due to exception ${err}`)
            okToCrawl = false
        }

        if(!okToCrawl && parentUrl) {
            this.emit('external-resource', {
                url: url,
                parentUrl: parentUrl
            })
        }

        return okToCrawl
    }

    isLoginUrl(url) {
        return this.config.loginurl && this.config.loginurl == url;
    }

    hookElectronEvents() {
        let scanner = this
        scanner.browser.webContents.on('will-navigate', function (event, url) {
            
            if(this.isLoginUrl(scanner.browser.webContents.getURL())) {
                this.config.ssoProvider = HaikuUtils.getSSOProviderFromRedirectURL(url);

                if(this.config.ssoProvider) {
                    this.config.isLoginSteps = true;
                }
            }

            if(this.config.isLoginSteps) {
                try {
                    let unsafeUrl = new URL(url);
                    
                    if(!this.config.unsafeForceAllowCrawl) {
                        this.config.unsafeForceAllowCrawl = [];
                    }

                    if(!_.includes(this.config.unsafeForceAllowCrawl, unsafeUrl.origin)) {
                        utils.log(`Adding ${this.config.ssoProvider} SSO login url ${url} into unsafeForceAllowCrawl list.`);
                        this.config.unsafeForceAllowCrawl.push(unsafeUrl.origin)
                    }

                    if(!this.config.excludeFromAttack) {
                        this.config.excludeFromAttack = [];
                    }
            
                    if(!_.includes(this.config.excludeFromAttack, unsafeUrl.origin + unsafeUrl.pathname)) {
                        utils.log(`Adding ${this.config.ssoProvider} SSO login url ${unsafeUrl.origin + unsafeUrl.pathname} into excludeFromAttack list.`);
                        this.config.excludeFromAttack.push(unsafeUrl.origin + unsafeUrl.pathname);
                    }
                } catch (error) {
                    utils.log(`Unable to add unsafeForceAllowCrawl for login, reason: ${error.toString()}`);
                }
            }

            if (!this.allowedToCrawlUrl(url)) {
                event.preventDefault();
                utils.log(`event will-navigate : prevented ${url} (not allowed domain)`);
                scanner.actionWasBlocked(url);
                scanner.emit('found-resource', url);
            }
        }.bind(this))

        scanner.browser.webContents.on('new-window', function (event, url) {
            // Always block the new window
            event.preventDefault()
            scanner.loadUrlInCurrentState(url)
            scanner.actionWasBlocked()
        }.bind(this))

        scanner.browser.webContents.session.on('will-download', (event, item, webContents) => {
            utils.log(`event will-download : preventing file download of ${item.getFilename()}`)
            utils.log(`event will-download : ${item.getSavePath()} ${item.getFilename()} ${item.getURL()}`)
            item.once('done', (event, state) => {
                utils.log(`event will-download : ${item.getFilename()} state ${state}`)
            })
            item.once('updated', (event, state) => {
                utils.log(`event will-download : ${item.getFilename()} state ${state}`)
            })
            item.cancel()
            utils.log(`event will-download : prevented file download of ${item.getFilename()}`)
            scanner.actionWasBlocked()
        })

        scanner.browser.on('close', function (event) {
            if (!scanner.crawlCompleted) {
                // crawl is not yet done, cannot allow window to be closed.
                event.preventDefault()
                utils.log(`event close : crawl still in progress, prevented closing window`)
                scanner.actionWasBlocked()
            }
        })


    }

    /**
     * Add load action for this URL to the current crawler state if the URL is permitted and not seen yet
     * @param {URL} url URL to be added as load action in current state
     * @param {string} annotation optional annotation
     */
    loadUrlInCurrentState(url, annotation = '[from new-window]') {
        let href = utils.canonicalizeUrl(url, this.config.parsedUrl.href);
        if (this.allowedToCrawlUrl(href) && !this.seenLoadActions.has(href)) {
            // Add a load action with teh new-window URL to the end of the current states action list
            // load original URL not canonacalized URL to avoid issues with site sthat behave differently eg. with/without trailing slashes
            let loadAction = new LoadAction(annotation, url);
            this.addActionToCurrentState(loadAction);
            this.seenLoadActions.add(href);
        } else {
            utils.log(`${annotation}: skipping ${href} (not allowed domain OR already seen this URL)`);
        }
    }
}

module.exports = IfcScannerDefault