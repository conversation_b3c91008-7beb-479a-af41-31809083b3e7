fs = require('fs')

file = process.argv[2] || './search_results.json'
console.log('processing file: ', file)

aa = fs.readFileSync(file).toString().split('\n')
aa.pop()
bb = aa.map(JSON.parse).map(a => {
    return {
        timestamp: a.timestamp,
        tm: new Date(a.timestamp).valueOf(),
        msg: a.message
    }
})
out = {}
skipped = 0
for (b of bb) {
    d = new Date(b.timestamp);
    d.setMilliseconds(0);
    d.setSeconds(0);
    // minutes = Math.floor(d.getMinutes()/5)*5
    // d.setMinutes(minutes);
    s = d.toISOString();
    out[s] = out[s] || {
        start: 0,
        finish: 0,
        err: 0
    };

    if (/mod_pagespeed_beacon/i.test(b.msg)) {
        continue
    }

    if (/request finished:/i.test(b.msg)) {
        out[s].finish++
    } else if (/sending request:/i.test(b.msg)) {
        out[s].start++
    } else if (/request failed:/i.test(b.msg)) {
        out[s].start--
        out[s].err++
    } else {
        skipped++
    }
}
fs.writeFileSync('./out.json', JSON.stringify(out))
keys = Object.keys(out).sort((x, y) => {
    return out[y].start - out[x].start
})
sortedOut = keys.map(k => {
    return Object.assign({}, out[k], {
        time: k
    })
})
// for (k of keys) {
//     sortedOut.push(out[k])
// }
fs.writeFileSync('./sorted-out.json', JSON.stringify(sortedOut))

// spit out the analysis
maxIter = 15000
if (maxIter > sortedOut.length) {
    maxIter = sortedOut.length
}
console.log(`top ${maxIter} requests started at time (min or sec) (${bb[0].msg})\n`)
console.log("start\tfinish\terr\ttime")
for (i = 0; i < maxIter; i++) {
    console.log(`${sortedOut[i].start}\t${sortedOut[i].finish}\t${sortedOut[i].err}\t${sortedOut[i].time}`)
}