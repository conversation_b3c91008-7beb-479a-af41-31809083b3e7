const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RegExpVari = require('./generic-regexp');
const HaikuUtils = require('../../../common/lib/haiku-utils')
// const LoginDelegate = require('../../lib/login-delegate')

class InsecureHTTPMethodTransition extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.InsecureTransitionPOST = 'ID-insecure-transition-from-post-get'
        // this.InsecureTransitionGET = 'ID-insecure-transition-from-get-post'
    }
    getAttackVectors() {
        return httpMethodInjVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    async performNetworkAttack(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.skip == true) {
            pluginDataForRequest.skip = false
            return false
        }
        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
        // let Clength = _.get(attack, 'originalRequest.httpResponse.headers["content-length"]', '') && Clength.length > 0

        // if (statusCode == '200' && /get|post/i.test(attack.originalRequest.httpRequest.method) && attack.originalRequest.httpRequest.method != attack.vector && attack.vector == 'GET') {
        if (statusCode == '200' && /post/i.test(attack.originalRequest.httpRequest.method)) {
            // Insecure Transition From HTTP Requests With "POST" To "GET" && attack.vector == 'GET'
            if (attack.vector == 'GET') {
                if (attack.originalRequest.httpRequest.body.length > 5 && !attack.originalRequest.httpRequest.body.includes('/{\s*".+?":".*?"/gi')) {
                    let Reqbody = attack.originalRequest.httpRequest.body.split('&')
                    let check = false
                    for (let param of Reqbody) {
                        if (/=./.test(param) && !/submit/i.test(param)) {
                            check = true
                            break
                        }
                    }
                    if (check) {
                        if (!attack.httpRequest.uri.includes('?')) {
                            attack.httpRequest.uri = attack.httpRequest.uri + '?' + attack.originalRequest.httpRequest.body
                            // attack.httpRequest.headers["Haiku-Test"] = 'ansa'
                            delete attack.httpRequest.body
                            delete attack.httpRequest.headers["Content-Type"]
                            return await super.performNetworkAttack(attack)
                        }
                        else {
                            attack.httpRequest.uri = attack.httpRequest.uri + '&' + attack.originalRequest.httpRequest.body
                            delete attack.httpRequest.body
                            delete attack.httpRequest.headers["Content-Type"]
                            // attack.httpRequest.headers["Haiku-Test"] = 'ansa'
                            return await super.performNetworkAttack(attack)
                        }
                    }
                }
            }

            /**
             * Insecure Transition From HTTP Requests With "GET" To "POST" - If necessary, run the following checks.
             **/

            /*  if (attack.vector == 'POST') {
                 let uriRaw = new URL(attack.originalRequest.httpRequest.uri)
                 if (uriRaw.search.length > 1) {
                     attack.httpRequest.uri = uriRaw.href.replace(uriRaw.search, '')
                     attack.httpRequest.body = uriRaw.search.replace('?', '') //need scanner feature
                     attack.httpRequest.headers["Content-Length"] = attack.httpRequest.body.length
                     attack.httpRequest.headers["Content-Type"] = 'application/x-www-form-urlencoded'
                     // attack.originalRequest.httpRequest.headers["Haiku-Test"] = 'ansa'
                     return await super.performNetworkAttack(attack)
                 }
             } */
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        let ResBody = _.get(attack, "result.resp.body", '')
        if (!ResBody.trim()) return false;

        // let Clength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', '')
        if (ResBody.length < 2 || !/\w+/.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        if (/(?:login|sign(?:-|_| )?(?:in|up)).*?<\/(?:title|h\d)|(?:Invalid|Incorrect) (?:Username|Password|Username or Password)|Login failed|Authentication Failed|The username or password you have entered is incorrect|type ?= ?"password"/i.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        if (/account.*(?:locked|disabled)|user.*(?:locked|disabled)|(?:locked|disabled).*account|(?:locked|disabled).*user|You are being rate limited/i.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        //condition to skip for the custom error pages
        const responseBody = ResBody.toLowerCase();

        // Check each category of error messages
        const errorCategories = [
            RegExpVari.ErrorMessages.HttpErrors,
            RegExpVari.ErrorMessages.SecurityErrors,
            RegExpVari.ErrorMessages.SessionErrors,
            RegExpVari.ErrorMessages.SystemErrors,
            RegExpVari.ErrorMessages.WafErrors,
            RegExpVari.ErrorMessages.GeneralErrors
        ];

        // Early return if any error message is found
        for (const category of errorCategories) {
            if (category.some(error => responseBody.includes(error))) {
                if (attack.attackArea == "original-crawler-request") {
                    pluginDataForRequest.skip = true
                }
                return false;
            }
        }

        // Check for WAF servers in headers
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

        if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false;
        }

        if (attack.attackArea == "original-crawler-request" && attack.pluginName == 'Original Crawler Request'
        ) {
            // let body = _.get(attack, "result.resp.body", '')
            let OristatusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            if (OristatusCode == 200 && ResBody.length < 4) {
                pluginDataForRequest.skip = true
                return false
            }
            if (/(id|name)="\w*captcha\w*"|location.href ?= ?".*(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound/i.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }
            let Reqbody = _.get(attack, 'httpRequest.body', '')
            if (/(?:(?:txt)?Password|passwd|pwd|passw)(?:" ?: ?"|=)Test(?:%40|@)1234/i.test(Reqbody)) {
                pluginDataForRequest.skip = true
                return false
            }
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (redirects > 0) {
                // let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                let uriRaw = new URL(redirectedUri)
                if (/(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/i.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/i.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/i.test(uriRaw.hash)) {
                    pluginDataForRequest.skip = true
                    return false
                }
            }

            let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
            let possibleCType = /(application\/json|text\/(?:plain|xml))/i
            if (possibleCType.test(contentTypeHeaderVal) && /"(?:status|Result)" ?: ?"?false|ErrorCode\\?":\\?"?(?!0)\w|"IsRequestSuccessfull":false/i.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }
            else if (/{"errors?" ?:|"errors?" ?: ?false|{"found":false}|>Error(?: !!!)?<\/(h|title)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/i.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }

            if (attack.httpRequest.loginInfo && (/defaultText|Test(?:%40|@)1234/i.test(attack.httpRequest.loginInfo.username) || /defaultText|Test(?:%40|@)1234/i.test(attack.httpRequest.loginInfo.password))) {
                pluginDataForRequest.skip = true
                return false
            }

            let OriRedirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (OristatusCode == 200 && OriRedirects == 0) {
                pluginDataForRequest.InsecureHTTPMethod = { OriResBodylength: ResBody.length }
            }
        }
        if (attack.pluginName == this.getName()) {
            if (/(id|name)="\w*captcha\w*"|location.href ?= ?".*(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/i.test(ResBody)) {
                return false
            }

            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (redirects > 0) {
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                let uriRaw = new URL(redirectedUri)
                if (/(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/i.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/i.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/i.test(uriRaw.hash)) {
                    // pluginDataForRequest.skip = true
                    return false
                }
            }
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')        
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //if vuln detected for a req then return
        if (pluginDataForRequest.HttpPOSTVulnFound) {
            return
        }
        // let redirectsFollowed = _.get(attack, 'result.resp.httpResponse.redirectsFollowed', '')
        let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
        let OriRedirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '')
        let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '')

        if (redirects > 0 && OriRedirects > 0) {
            let OriReLocation = _.get(attack, 'originalRequest.httpResponse.redirects[0].headers.location', '')
            let OriredirectedUri = _.get(attack, 'originalRequest.httpResponse.redirects[0].redirectedUri', '')
            let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
            let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
            if (ReLocation == OriReLocation || redirectedUri == OriredirectedUri) {
                if (attack.vector == "GET" && attack.paramVal == "POST") {
                    this.addVulnerabilitytoResult(attack, this.InsecureTransitionPOST, { href: `and Status Code: ${OristatusCode} Redirected Location: ${OriReLocation}` })
                    pluginDataForRequest.HttpPOSTVulnFound = true
                }
                /* if (attack.vector == "POST" && attack.paramVal == "GET") {
                    this.addVulnerabilitytoResult(attack, this.InsecureTransitionGET, { href: attack.href, method: attack.vector })
                    pluginDataForRequest.HttpGETVulnFound = true
                } */
            }
        }
        else if (redirects == 0 && OriRedirects > 0 || redirects > 0 && OriRedirects == 0) {
            return
        }
        else if (redirects == 0 && OriRedirects == 0) {
            let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
            if (OristatusCode == 200 && AtkstatusCode == 200) {
                let OriClength = _.get(attack, 'originalRequest.httpResponse.headers["content-length"]', '0')
                let AtkClength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', '0')
                let AtkResbody = _.get(attack, "result.resp.body", '0')
                let OriResBodylength = pluginDataForRequest.InsecureHTTPMethod.OriResBodylength
                if ((OriClength > 4 && OriClength == AtkClength) || (AtkResbody.length > 4 && OriResBodylength == AtkResbody)) {
                    if (attack.vector == "GET" && attack.paramVal == "POST") {
                        this.addVulnerabilitytoResult(attack, this.InsecureTransitionPOST, { href: `and response body size: ${OriClength} : ${AtkClength} and ${OriResBodylength} : ${AtkResbody.length}` })
                        pluginDataForRequest.HttpPOSTVulnFound = true
                    }
                    /* if (attack.vector == "POST" && attack.paramVal == "GET") {
                        this.addVulnerabilitytoResult(attack, this.InsecureTransitionGET, { href: attack.href, method: attack.vector })
                        pluginDataForRequest.HttpGETVulnFound = true
                    } */
                }
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.InsecureTransitionhttp) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.method', `param`, [attack.httpRequest.method]);
    }
}
const httpMethodInjVectors = [
    `GET`,
    // `POST`,
]

module.exports = InsecureHTTPMethodTransition