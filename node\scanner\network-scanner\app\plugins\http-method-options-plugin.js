const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

class HTTPOptionsPlugin extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.optionsVulnerabilityID = 'ID-OPTIONS-enabled'
    }

    getAttackVectors() {
        return httpMethodVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * @param {method} attack
     * Overriding the performNetworkAttack method to change the redirect to false
     
    async performNetworkAttack(attack) {
        attack.httpRequest.redirect = false;
        return await super.performNetworkAttack(attack)
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        // redirects
        let Reallow = _.get(attack, 'result.resp.httpResponse.redirects[0].headers["allow"]', false)
        let ReAccessallow = _.get(attack, 'result.resp.httpResponse.redirects[0].headers["access-control-allow-methods"]', false) //API - Access-Control-Allow-Methods

        //Normal
        let allowHeader = _.get(attack, 'result.resp.httpResponse.headers["allow"]', false)
        let Accessallow = _.get(attack, 'result.resp.httpResponse.headers["access-control-allow-methods"]', false) //API - Access-Control-Allow-Methods

        /**
         * If the response has ALLOW header then it will add it to the vulnerability list
         */
        if (Reallow || ReAccessallow || allowHeader || Accessallow) {
            if (Reallow) {
                this.addVulnerabilitytoResult(attack, this.optionsVulnerabilityID, Reallow)
            } else if (ReAccessallow) {
                this.addVulnerabilitytoResult(attack, this.optionsVulnerabilityID, ReAccessallow)
            } else if (allowHeader) {
                this.addVulnerabilitytoResult(attack, this.optionsVulnerabilityID, allowHeader)
            } else if (Accessallow) {
                this.addVulnerabilitytoResult(attack, this.optionsVulnerabilityID, Accessallow)
            }
        }
    }
}

const httpMethodVectors = [
    `OPTIONS`
]

module.exports = HTTPOptionsPlugin