const debug = require('debug')('HttpMethodIterator')
const querystring = require('querystring')
const BasePlugin = require('../base-plugin')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const HttpMethodsIterator = require('../../../lib/http-methods-iterator')

class HttpMethod extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }

    /**
     * New request received from crawler - Kick off paramter iteration if request is viable
     * @param {request} originalRequest untampered request
     * @override
     */
    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http methods
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area')
            if (originalRequest.revalidationInfo && HttpMethodsIterator.ParameterType != attackArea) {
                logger.log('info', `http method splitter - not an interesting request since request being revalidated is attacking '${attackArea}'`, HaikuUtils.getMetadataForLog(originalRequest))
                return
            }
        }

        // only process successful requests
        if (originalRequest.httpResponse.err) {
            logger.log('info', `http method splitter - not an interesting request ${originalRequest.httpRequest.method} ${originalRequest.httpRequest.uri}  err=${originalRequest.httpResponse.err}`,HaikuUtils.getMetadataForLog(originalRequest))
            return
        }

        // Create object that can iterate and manipulate params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan')
        let options = this.getMetadata(originalRequest).options;
        let createParameterizedMethod = function () {
            return new HttpMethodsIterator(originalRequest, scanStore, options)
        }
        this.networkScanner.emit('http-methods', createParameterizedMethod)
    }
}

module.exports = HttpMethod