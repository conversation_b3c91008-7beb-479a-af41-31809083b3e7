/**
 * Get one fingerprint token per interesting item using tokens sent by the browser
 */
const utils = require('../../ifc-utils.js')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const URL = require('url').URL
const _ = require('lodash')

/**
 * Get one fingerprint token per interesting item using tokens sent by the browser and adding info like 
 * canonicalized href. Generated token is a string.
 */
class GetFingerprintTokensPerItem {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        scanner.on('got-interesting-items', this.getActions.bind(this))
    }

    /**
     * annoate item with fingerprint tokens from the info sent about item by browser
     * @param {object} interestingItems The interstingItems collected by the crawler that 
     * has data about the items and will be annotated with the fingerprint tokens.
     */
    addFingerprintTokens(interestingItems) {
        let {
            clickableItems = [],
                inputItems = [],
                formItems = [],
                linkItems = []
        } = interestingItems

        // for the non clickable link items (will be processed with load), fingerprint token is just the 
        // canoncalized URI
        for (let item of linkItems) {
            item[1].fingerprintTokens = utils.canonicalizeUrl(item[1].href, this.config.parsedUrl.href)
        }

        // For the others, fingerprinting token is a combination of attributes
        let itemsToIterate = [...clickableItems, ...inputItems, ...formItems]
        for (let item of itemsToIterate) {
            item[1].fingerprintTokens = this.getFingerprintToken(item[1])
        }

        // For forms and clickables, add the xpath as well
        itemsToIterate = [...clickableItems, ...formItems]
        for (let item of itemsToIterate) {
            item[1].fingerprintTokens += `,xpath=${item[0]}`
        }
    }

    /**
     * Generate fingerprint token as combination of fingerprint tokens and oter data sent by the crawler
     * @param {Object} item An interesting item as sent by the browser. Has info about the item.
     */
    getFingerprintToken(item) {
        // get list of info we want to pick up about this element like tags,events and attributes 
        // href will be handled separately in the crawler code since it needs to be normalized.
        let tokens = []

        function isValidValue(val) {
            return !HaikuUtils.isNullOrUndefined(val) && val != 'null' && val != 'undefined'
        }

        // local function to add token
        function addItemToken(propName) {
            let value = item[propName]
            if (isValidValue(value)) {
                tokens.push(propName + ':' + value);
            }
        }

        // common items
        addItemToken('name')
        addItemToken('id')
        addItemToken('nodeName')
        addItemToken('type')
        addItemToken('eventType')
                
        // additional info for input boxes
        addItemToken('minlength')
        addItemToken('maxlength')
        addItemToken('min')
        addItemToken('max')
        addItemToken('labels')
        addItemToken('ifcAction')
        addItemToken('ifcType')
        addItemToken('ifcValues')

        // --form specific--
        addItemToken('method')
        if (Array.isArray(item['formElements'])) {
            tokens.push('formElements' + ':' + item['formElements'].length)
        }
        // --form specific--

        // canonacalize anything that is an href

        // visibility status
        tokens.push('visibility:' + (!(item.rect && item.rect.inViewport) ? 'not-' : '') + 'visible')

        // send token as a string since one element should have one fingerprinting token
        return tokens.join(',')
    }

    /**
     * Called when interesting items have been found by browser after an action taken by the crawler.
     * @param {object} ret control crawl state, this plugin does not modify ret
     * @param {object} interestingItems The interstingItems collected by the crawler, items will be updated by this plugin.
     */
    getActions(ret, interestingItems) {
        // previous plugin already decided to skip state or specifically asked to stop processing more plugins => nothing to do
        if (ret.skipState) {
            return
        }

        this.addFingerprintTokens(interestingItems)
    }
}

module.exports = GetFingerprintTokensPerItem