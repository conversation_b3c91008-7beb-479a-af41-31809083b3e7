// Ajax hooking
class IndusfaceAjax {
    constructor() {}

    name(short) {
        return short ? "IFC-ajax" : "Indusface Ajax hooking Plugin"
    }

    init() {
        this.requests = 0
        indusfaceRenderer.pluginData.ajax = {}
        indusfaceRenderer.pluginData.ajax.outstandingRequests = 0

        this.hookAjax()
    }

    hookAjax() {
        // Hook XMLHttpRequest.
        let ifcAjax = this
        let origAjaxMethods = {
            'open': XMLHttpRequest.prototype.open,
            'send': XMLHttpRequest.prototype.send
        }
        XMLHttpRequest.prototype.open = function () {
            return origAjaxMethods.open.call(this, ...arguments);
        }
        XMLHttpRequest.prototype.send = function () {
            // hook the onreadystatechange handler
            let reqNum = ifcAjax.requests++;
            //console.log(`------: Send for request ${reqNum}`)
            indusfaceRenderer.pluginData.ajax.outstandingRequests++;
            this.addEventListener('loadend', () => {
                indusfaceRenderer.pluginData.ajax.outstandingRequests--;
                //console.log(`${reqNum} completed. Outstading ajax requests: ${indusfaceRenderer.pluginData.ajax.outstandingRequests}`)
            })
            return origAjaxMethods.send.call(this, ...arguments);
        }
    }


}

module.exports = IndusfaceAjax