const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
var crypto = require('crypto')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

/**
 * Cookie Misconfiguration Plugin Strategy:
 * If for any url having some kind of session id in them, then tampering with that sesion id should
 * not allow user to use functionality as it is, hence everytime it's tampered it should generate new 
 * session id. If this does not happen then it's vulnerable to cookie misconfiguration
 */

class cookieMisconfiguration extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-cookie-manipulation'
    }

    checksum(str, algorithm, encoding) {
        return crypto
            .createHash(algorithm || 'md5')
            .update(str, 'utf8')
            .digest(encoding || 'hex')
    }

    //as only need to modify the cookie values so will only append the vector to value for only raw encoding
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            replaceValue: false,
            appendVector: true,
            encodings: ['raw']
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return cookieVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['cookie-params']
    }

    async performNetworkAttack(attack) {
        if (/^TS[\da-f]+$/i.test(attack.param)) return false; //Skip to check because The TS* cookie pattern is used across multiple F5 customers.
        return await super.performNetworkAttack(attack)
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName == this.getName() && /^TS[\da-f]+$/i.test(attack.param)) return; //Skip to check because The TS* cookie pattern is used across multiple F5 customers.

        let pluginStorage = this.getPluginScopedStore(attack)
        //Below code is to fetch the body of original request made
        if (attack.pluginName != this.getName()) {
            if (attack.attackArea == "original-crawler-request") {
                let orginalChecksum = this.checksum(attack.result.resp.body)
                if (orginalChecksum != null) { pluginStorage.originalChecksum = orginalChecksum }
            }
            return
        }

        // get plugin storage
        //let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorage.cookieMisconfiguration) {
            return
        }
        let vuln = {
            href: attack.href,
            attackedCookieName: attack.param,
            wholeCookie: attack.httpRequest.headers.Cookie
        }

        // if the response status code of original request matches with attack request then 
        // will report the vulnerability
        // Updating for FPs - if the server sets a cookie again after sending attack then will not report that
        let isSetCookiePresent = _.get(attack, 'result.resp.httpResponse.headers[set-cookie]', '')
        let responseSize = _.get(attack, 'result.resp.httpResponse.headers[content-length]', '')
        // Return if response body is null - don't report anything
        if (responseSize <= 5) { return }

        // Return is set cookie is present in response
        if (isSetCookiePresent) { return }

        let ResBody = _.get(attack, 'result.resp.body', '')

        //If content-length is not present in response header but even response body is present ex:204 no content and 200 ok. Content length is missing then only calculate response body.
        if (!responseSize) {
            if (ResBody.length <= 5) { return }
        }

        //condition to skip for the custom error pages
        const responseBody = ResBody.toLowerCase();

        // Check each category of error messages
        const errorCategories = [
            RegExpVari.ErrorMessages.HttpErrors,
            RegExpVari.ErrorMessages.SecurityErrors,
            RegExpVari.ErrorMessages.SessionErrors,
            RegExpVari.ErrorMessages.SystemErrors,
            RegExpVari.ErrorMessages.WafErrors,
            RegExpVari.ErrorMessages.GeneralErrors
        ];

        // Early return if any error message is found
        for (const category of errorCategories) {
            if (category.some(error => responseBody.includes(error))) {
                return;
            }
        }

        // Check for WAF servers in headers
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

        if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
            return;
        }

        //status code is 200 but Redirection is present in response body - Dont report anything
        if (/(?:<meta [\w\s\-;"=']*?(?:url=(?:'|")[\:.\w\/\-]*?log(?:in|out))|<title>[\w\s\-:\&]*?(?:log(?:in|out)|redirect)|onload=("|')window\.document\.location=('|")[\:.\w\/\-]*?log(in|out)|window\.location\.href = ("|')[\:.\w\/\-]*?log(in|out))/i.test(attack.result.resp.body)) {
            return
        }

        if (attack.originalRequest.httpResponse.statusCode < 400) {
            //Return if response status code is any of redirection codes
            let redirectionStatusCode = [301, 302, 303, 304, 305, 307, 308]
            if (redirectionStatusCode.includes(attack.originalRequest.httpResponse.statusCode)) {
                return
            }
            //rather than comparing the whole text body we are comparing entire body checksum
            let attackChecksum = this.checksum(attack.result.resp.httpResponse.body)
            if (attack.result.resp.httpResponse.statusCode == attack.originalRequest.httpResponse.statusCode &&
                attackChecksum == pluginStorage.originalChecksum) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
                pluginStorage.cookieMisconfiguration = true
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers.Cookie', `text`, ['abcd1234']);
    }
}

const cookieVectors = [
    'abcd1234'
]

module.exports = cookieMisconfiguration