// <PERSON><PERSON><PERSON> simplified login heuristics plugin 
class SimpleLoginHeuristics {
    constructor() { }

    name(short) {
        return short ? "IFC-Simple-Login-Heuristics" : "Indusface Simple Login Heuristics Plugin"
    }

    init() {
        indusfaceRenderer.getLoginInfo = this.getLoginInfo.bind(this);
        indusfaceRenderer.getMultiStepElementAfterLogin = this.getMultiStepElementAfterLogin.bind(this);
    }

    getMultiStepElementAfterLogin() {
        // get all the input elements with type="submit"
        let submitInputs = document.querySelectorAll('input[type="submit"]');

        // create an empty array to store the matching buttons
        let yesButtons = [];

        // loop through all the input elements and check if their value is "Yes"
        for (let i = 0; i < submitInputs.length; i++) {
            if (submitInputs[i].value.toLowerCase() === 'yes') {
                yesButtons.push(submitInputs[i]);
            }
        }

        // now the yesButtons array contains all the input elements with type="submit" and value="Yes"
        return {
            yesButton: yesButtons.length > 0 ? indusfaceRenderer.getXpath(yesButtons[0]) : null
        }
    }

    getLoginInfo() {
        function getAdditionalInfoForElem(e) {
            // metadata that will be useful for heuristics in crawler & scanner
            return {
                name: e.name,
                id: e.id,
            }
        }
        
        // if we see a password type field 
        // and someting that looks like an username field
        // and something like a login button
        // give it a shot.

        let loginInfo

        // Is there a password type field
        let passwords = Array.from(document.querySelectorAll('input[type="password"]'))
        passwords = passwords.filter((pwd) => {
            // skip invisible password fields
            let rect = indusfaceRenderer.getBoundingRect(pwd)
            return rect.inViewport
        })

        // need at least one password field 
        if (passwords.length) {
            // now look for login or submit type buttons
            let loginActionTypeSelector = ['button', 'submit'].map((a) => {
                return `input[type=${a}]`
            }).join(',')
            let loginActions = Array.from(document.querySelectorAll(loginActionTypeSelector))
            loginActions.push(...Array.from(document.getElementsByTagName('button')))
            loginActions = loginActions.filter((action) => {
                // skip invisible buttons
                let rect = indusfaceRenderer.getBoundingRect(action)
                if (!rect.inViewport) {
                    return false
                }
                //let loginActionPattern = /Einloggen|login|signin|authenticate|authorize|enter|next|submit/i
                let loginActionPattern = /^(?!inloggenmetcode\b)(inloggen met wachtwoord|inloggen|Einloggen|login|signin|authenticate|authorize|enter|next|submit|signon|logon|entrar)/i;
                let ssoPattern = /facebook|fblogin|google|googlogin|amazon|amzlogin|twitter/i
                let potentialLoginAction = false
                let props = ['name', 'id', 'value', 'innerText', 'provider']
                for (let prop of props) {
                    if (!action[prop]) {
                        continue
                    }
                    let sanitizedVal = action[prop].toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                    if (loginActionPattern.test(sanitizedVal) && !ssoPattern.test(sanitizedVal)) {
                        potentialLoginAction = true
                        break
                    }
                }
                return potentialLoginAction
            })

            if (loginActions.length == 1) {
                // cannot handle multiple 'login' type buttons
                // now look for all input fields that have id/name/placeholder that look like
                // user names. Here we dont mind 
                let usernameTypeSelector = ['text', 'email', 'name'].map((a) => {
                    return `input[type=${a}]`
                }).join(',')
                usernameTypeSelector = 'input:not([type]),' + usernameTypeSelector  // no type= defaults to type=text                
                let usernameInputs = Array.from(document.querySelectorAll(usernameTypeSelector)).filter((input) => {
                    // skip invisible input items
                    let rect = indusfaceRenderer.getBoundingRect(input)
                    if (!rect.inViewport) {
                        return false
                    }
                    let usernamePattern = /user|name|email|mobile|phone|uid|login/i
                    let potentialUsername = false
                    let props = ['name', 'placeholder', 'id', 'value']
                    for (let prop of props) {
                        if (!input[prop]) {
                            continue
                        }
                        let sanitizedVal = input[prop].toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                        if (usernamePattern.test(sanitizedVal)) {
                            potentialUsername = true
                            break
                        }
                    }
                    return potentialUsername
                })

                //check at least one username element exists
                if (usernameInputs.length >= 1) {

                    // reached here, we got a potential login, convert to xpaths
                    loginInfo = {
                        usernames: usernameInputs.map((u) => { return indusfaceRenderer.getXpath(u) }),
                        passwords: passwords.map((p) => { return indusfaceRenderer.getXpath(p) }),
                        action: indusfaceRenderer.getXpath(loginActions[0]),
                        additionalInfo: {
                            usernames: usernameInputs.map((u) => { return getAdditionalInfoForElem(u) }),
                            passwords: passwords.map((p) => { return getAdditionalInfoForElem(p) }),
                            action: getAdditionalInfoForElem(loginActions[0])
                        }
                    }
                }
            }
        }
        else {
            //Check if at least one clickable login item exists
            let loginRegex = /^(?!inloggenmetcode\b)(inloggen met wachtwoord|inloggen|Einloggen|login|signin|authenticate|authorize|signon|logon)/i;
            let logoutRegex = /(logout|signout|exit|quit|forgot|wachtwoordvergeten|vergeten)/i;
            //let logoutRegex = /(log\s*out|sign\s*out|exit|quit)(?!\w*wachtwoord\s+vergeten)/i;

            let loginActions = Array.from(document.querySelectorAll('a')).filter(link => {
                return (
                    (loginRegex.test(link.href.toLowerCase().replace(/\s/g, '').replace(/-/g, '')) || loginRegex.test(link.textContent.toLowerCase().replace(/\s/g, '').replace(/-/g, ''))) &&
                    !logoutRegex.test(link.textContent.toLowerCase().replace(/\s/g, '').replace(/-/g, ''))
                );
            });

            if (loginActions.length == 0) {
                loginRegex = /^(?!inloggenmetcode\b)(inloggen met wachtwoord|inloggen|Einloggen|login|signin|authenticate|authorize|signon|logon)/i;
                logoutRegex = /(logout|signout|exit|quit|forgot|wachtwoordvergeten|vergeten)/i;
                loginActions = Array.from(document.querySelectorAll('button')).filter(button => {
                    return (
                        (loginRegex.test(button.textContent.toLowerCase().replace(/\s+/g, '').replace(/-/g, ''))) &&
                        !logoutRegex.test(button.textContent.toLowerCase().replace(/\s+/g, '').replace(/-/g, ''))
                    );
                });
            }

            if (loginActions.length > 0) {
                loginInfo = {
                    action: indusfaceRenderer.getXpath(loginActions[0]),
                    additionalInfo: {
                        action: getAdditionalInfoForElem(loginActions[0])
                    }
                }
            }
        }

        return loginInfo
    }
}

module.exports = SimpleLoginHeuristics