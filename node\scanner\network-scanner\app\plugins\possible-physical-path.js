const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class PossiblePhysicalPathDisc extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-possible-physical-path-disclosure'
        this.matchRegexp = new RE2(PossiblePhysicalPathDiscMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        if (originalRequest.result.resp.httpResponse.err) {
            return
        }
        let bodycheck = _.get(originalRequest, "result.resp.body")
        if (originalRequest.href.includes("/docs") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }
        if (/s:\\|s:\\u002|p:\\u002/i.test(bodycheck)) {
            return
        }
        if (!(/16-bit\sapp\s(.*)\[mci\sextensions\]|\/(?:var|usr|mnt|sbin|proc|opt|www)\//i.test(bodycheck)) && /\\n/i.test(bodycheck)) { //FP - Response comes with \n
            return
        }
        this.checkBodyForVuln(originalRequest, this.matchRegexp, this.vulnerabilityID, { minMatchLength: 7 })
    }
}

const PossiblePhysicalPathDiscMatch = [
    /\b[a-zA-Z]:\\[^\/:*?"<>|\r\n]+\\?/,
    /\/(?:var|usr|mnt|sbin|proc|opt)\/[\w\/\-\.]*\.\w/,
]
module.exports = PossiblePhysicalPathDisc