const _ = require('lodash');
const logger = require('../../common/lib/haiku-logger');
const HaikuUtils = require('../../common/lib/haiku-utils');
let networkScanner = null;

module.exports = class HaikuScanScriptHelper {
    constructor(networkScannerObject, scanId) {
        networkScanner = networkScannerObject;
        this.scanId = scanId;
    }

    /**
     * Set global variables for request script. i.e. url, token etc. 
     * @param {String} paramName parameter name
     * @param {any} value parameter value to be replaced with.
     * @returns {HaikuScanScriptHelper} HaikuScanScriptHelper object
     */
    setVar(paramName, value) {
        try {
            let scriptVariables = _.get(networkScanner.getScanInfo(this.scanId), `annotatedRequests.scriptVariables`);

            if (scriptVariables) {
                scriptVariables[paramName] = value;
            }
        }
        catch (err) {
            logger.log('error', `Could not set script variables: ${err.toString()}`, HaikuUtils.getMetadataForLog({
                scanId: this.scanId
            }));
        }

        return this;
    }

    /**
     * Set global variables for request script. i.e. url, token etc. 
     * @param {any} args script arguments
     * @param {String} vulnID Vulnerability id
     * @param {any} details Vulnerability details 
     * @returns {HaikuScanScriptHelper} HaikuScanScriptHelper object
     */
    addVulnerabilityToResult(args, vulnID, details) {
        try {
            args.networkAttack.addVulnerabilitytoResult(args.attack, vulnID, details);
        }
        catch (err) {
            logger.log('error', `Could not addVulnerabilitytoResult, reason: ${err.toString()}`, HaikuUtils.getMetadataForLog({
                scanId: this.scanId
            }));
        }

        return this;
    }

    getPlugin(pluginName) {
        let pluginInstance = networkScanner.getPluginByName(pluginName);

        if (!pluginInstance) {
            logger.log('error', `Request specific vectors can't be set, reason: no plugin with name ${pluginName} found.`, HaikuUtils.getMetadataForLog({
                scanId: this.scanId
            }));

            return this;
        }
    }

    /**
     * Set request specific vectors to attack. 
     * @param {any} args Script arguments
     * @param {String} pluginName Plugin name for which specific request vectors need to be attack
     * @param {String[]} vectors Vectors to be attack with
     * @returns {HaikuScanScriptHelper} HaikuScanScriptHelper object
     */
    setVectors(args, pluginName, vectors) {
        try {
            let pluginInstance = networkScanner.getPluginByName(pluginName);

            if (!pluginInstance) {
                logger.log('error', `Request specific vectors can't be set, reason: no plugin with name ${pluginName} found.`, HaikuUtils.getMetadataForLog({
                    scanId: this.scanId
                }));

                return this;
            }

            let pluginStorage = pluginInstance.getPluginScopedStore(args.attack);
            pluginStorage.vectors = vectors;

            if (_.isArray(vectors)) {
                pluginInstance.pluginSpecificVectors = vectors;
            }
            else {
                logger.log('error', `Request specific vectors is not in array of string format.`, HaikuUtils.getMetadataForLog({
                    scanId: this.scanId
                }));
            }
        }
        catch (err) {
            logger.log('error', `Could not set script variables: ${err.toString()}`, HaikuUtils.getMetadataForLog({
                scanId: this.scanId
            }));
        }

        return this;
    }

    /**
     * Set request specific vectors to attack. 
     * @param {any} args Script arguments
     * @param {String} pluginName Plugin name for which specific request vectors need to be attack
     * @param {String[]} attackAreas Vectors to be attack with
     * @returns {HaikuScanScriptHelper} HaikuScanScriptHelper object
     */
    setAttackArea(args, pluginName, attackAreas) {
        try {
            let pluginInstance = networkScanner.getPluginByName(pluginName);

            if (!pluginInstance) {
                logger.log('error', `Request specific attack area can't be set, reason: no plugin with name ${pluginName} found.`, HaikuUtils.getMetadataForLog({
                    scanId: this.scanId
                }));

                return this;
            }

            let pluginStorage = pluginInstance.getPluginScopedStore(args.attack);
            pluginStorage.attackAreas = attackAreas;

            if (_.isArray(attackAreas)) {
                pluginInstance.pluginSpecificAttackAreas = attackAreas;
            }
            else {
                logger.log('error', `Request specific attack areas is not in array of string format.`, HaikuUtils.getMetadataForLog({
                    scanId: this.scanId
                }));
            }
        }
        catch (err) {
            logger.log('error', `Could not set attack areas: ${err.toString()}`, HaikuUtils.getMetadataForLog({
                scanId: this.scanId
            }));
        }

        return this;
    }

    saveHeader(request, header) {
        if(!_.get(request, "savedHeaders")) {
            request.savedHeaders = {};
        }

        request.savedHeaders[header] = request.headers[header];
    }

    restoreHeader(request, header) {
        let originalHeader = _.get(request, `savedHeaders.${header}`);

        if(originalHeader) {
            request.headers[header] = originalHeader;
        }
    }

    /**
     * Get arguments for annoration script. 
     * @param {any} attack attack instance for which annotation script execution to be done.
     * @param {NetworkAttack} networkAttack network attack instance
     * @returns {any} argument object
     */
    static getArgs(attack, networkAttack) {
        return {
            resp: attack.result.resp.httpResponse,
            attack: attack,
            networkAttack: networkAttack,
            originalRequest: attack.originalRequest 
        }
    }
}