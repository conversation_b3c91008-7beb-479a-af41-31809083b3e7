// Javascript for gen-site-config.html
let singleStepping = false

function openTab(evt, tabName) {
    jQuery(".content-tab").hide()
    jQuery(".tab").removeClass('is-active')
    jQuery('#' + tabName).show()
    jQuery(evt.currentTarget).addClass("is-active")
}

jQuery(function () {
    jQuery('#create').click(generateRegex)
    jQuery('#testGeneratedRegex').click(testRegex)
})

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function resizeTextArea(id) {
    let el = document.getElementById(id)
    while (el.scrollHeight > el.getBoundingClientRect().height) {
        el.rows = el.rows + 1
    }
}

async function generateRegex() {
    let statusMessage = ''
    try {
        //debugger;
        let dupUrls = getUriList('#urls')
        let nonDupUrls = getUriList('#non-dup-urls')

        let showSteps = jQuery('#show-steps').prop('checked')
        let stepSecs = jQuery('#steps-secs').val()
        if (showSteps && stepSecs > 0 && dupUrls.length > 2) {
            singleStepping = true
            let originalDupUrlsVal = jQuery("#urls").val()
            for (let i = 2; i < dupUrls.length; i++) {
                jQuery("#urls").val(dupUrls.slice(i))
                statusMessage = `processed ${i} of ${dupUrls.length}: `
                await callGenRegexApi(dupUrls.slice(0, i).reverse(), nonDupUrls)
                await sleep(stepSecs*1000)
                if (i > 16) {
                    break
                }
            }
            jQuery("#urls").val(originalDupUrlsVal)
            singleStepping = false
            statusMessage = `processed all ${dupUrls.length} urls: `
            callGenRegexApi(dupUrls, nonDupUrls)
        } else {
            statusMessage = `processed all ${dupUrls.length} urls: `
            callGenRegexApi(dupUrls, nonDupUrls)
        }
    } catch (err) {
        processFailure(
            err.toString())
    }

    function callGenRegexApi(dupUrls, nonDupUrls) {
        return new Promise((res, rej) => {
            jQuery.post('/utils/regexFromUrls', {
                data: JSON.stringify({
                    dupUrls,
                    nonDupUrls
                })
            }).done((result) => {
                if (result && result.success) {
                    processSuccess(result)
                } else {
                    processFailure(result)
                }
                res(result)
            }).fail((jqxhr, textStatus, error) => {
                processFailure({
                    status: textStatus,
                    error
                })
                res(['error'])
            })
        })
    }

    // process success & failures
    function processSuccess(result) {
        // update the result UI
        UpdateUrlListContent(result.tpUrlsFound, "#matched-urls", singleStepping ? 1 : 0)
        UpdateUrlListContent(result.fnUrlsFound, "#fn-urls")
        UpdateUrlListContent(result.fpUrlsFound, "#fp-urls")

        //jQuery('#generated-regex').val(result.regex)
        jQuery('#generated-regex').text(result.regex)
        jQuery('.generated-container').show()
        jQuery('#status').text(statusMessage + `fβ (biased toward lower FP)=${parseFloat(result.fScore).toFixed(2)}. TP=${result.tpUrlsFound.length}, FP=${result.fpUrlsFound.length}, FN=${result.fnUrlsFound.length}`)
        jQuery('.generated-container').show()

        // also update the test regex tab   
        jQuery('#test-regex').val(result.regex)
        jQuery('#test-url').val('')
        jQuery('#can-url  ').val('')      
        jQuery("#matches").hide()
        jQuery("#does-not-match").hide()

    }

    function processFailure(result) {
        // output
        jQuery('#status').text((result.status ? result.status : '') +
            (result.output ? result.output : '') +
            (result.error ? result.error.toString() : ''))
        jQuery('.generated-container').show()
        // jQuery('#generated-regex').val('**ERROR**')
        jQuery('#generated-regex').text('**ERROR**')
    }
}

function testRegex() {
    try {
        //debugger;
        let url = jQuery('#test-url').val().trim()
        let regex = jQuery('#test-regex').val().trim()

        jQuery.post('/utils/doesUrlMatchRegex', {
            data: JSON.stringify({
                url,
                regex
            })
        }).done((result) => {
            if (result && result.success) {
                processSuccess(result)
            } else {
                processFailure(result)
            }
        }).fail((jqxhr, textStatus, error) => {
            processFailure({
                status: textStatus,
                error
            })
        })
    } catch (err) {
        processFailure(
            err.toString())
    }

    // proess success, failure
    function processSuccess(result) {
        // update the result UI
        jQuery('#can-url').val(result.canUrl)
        jQuery("#matches").hide()
        jQuery("#does-not-match").hide()
        if (result.matches) {
            jQuery("#matches").show()
        } else {
            jQuery("#does-not-match").show()
        }
    }

    function processFailure(result) {
        jQuery("#matches").hide()
    }


}

function getUriList(id) {
    let uris = jQuery(id).val().trim()
    let uriList
    try {
        uriList = JSON.parse(uris)
    } catch (err) {
        uriList = uris.split('\n')
    }
    return uriList
}

function arraytoHtmlList(urls, linesToHighlight = 0) {
    let matchedUrlHTML = `<ul style="list-style-type:none;">`
    let i = 0
    const styleForLI = 'style="border-bottom: 1px dotted"'
    for (let url of urls) {
        if (i++ < linesToHighlight) {
            let style = (i == linesToHighlight ? styleForLI : '')
            matchedUrlHTML += `<li class="has-text-info" ${style}>${url}</li>`
        } else {
            matchedUrlHTML += `<li ${styleForLI}>${url}</li>`
        }
    }
    matchedUrlHTML += `</ul>`
    return matchedUrlHTML
}

function UpdateUrlListContent(urls, idControl, linesToHighlight = 0) {
    let matchedUrlHTML = arraytoHtmlList(urls, linesToHighlight)
    let el = jQuery(idControl)
    el.empty()
    el.append(matchedUrlHTML)
}