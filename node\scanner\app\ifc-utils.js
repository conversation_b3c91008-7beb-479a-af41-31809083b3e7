// Unless we add more methods, dont even need this class, just the event handlers
const electron = require('electron')
const ipcMain = electron.ipcMain
const debug = require('debug')('Haiku:Crawler')
const HaikuUtils = require('../common/lib/haiku-utils')
const logger = require('../common/lib/haiku-logger')
const normalizeUrl = require('normalize-url')
const URL = require('url').URL
const path = require('path')
// Use the RE2 replacement from common/lib to avoid memory issues with the native module
const RE2 = require('../common/lib/re2-replacement')

// for tokenizing URLs
const TOK_BOUNDARIES = '/?&=.-'
const TOK_REGEX = new RegExp(`([${TOK_BOUNDARIES}])`)

let utils
class Utils {
    constructor() {
        this.docSteadyId = 1
    }

    /**
     * expose contant needed by analyze Dup Urls code.
     */
    getTokBoundaries() {
        return TOK_BOUNDARIES        
    }

    mergeConfig(config) {
        this.config = config
        if (!this.config.siteConfig || !this.config.siteConfig.utils) {
            return
        }
        this.config.utils = this.config.siteConfig.utils
    }

    onRendererLog(event, windowId, args) {
        var title = 'unknown';
        let window = electron.BrowserWindow.fromId(windowId);
        if (window)
            title = window.webContents.getTitle()

        utils.log('renderer-log: ', title.substr(0, 16), "... :", args);
    }

    log() {
        logger.log('info', ...arguments)
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Resolves with a specified value if the passed promise is pending after a timeout. 
     * @param {Promise} promise promise to wrap with timeout
     * @param {*} valOnTimeout value to resolve to on timeout
     * @param {Number} timeout milliseconds to wait for promise to resolve
     */
    timedPromise(promise, valOnTimeout = 'timed out', timeout = this.config.pageExecutionScriptTimeout * 1000) {
        if (this.config.pageExecutionScriptTimeout > 120) {
            this.config.pageExecutionScriptTimeout = 120;
            timeout = this.config.pageExecutionScriptTimeout * 1000;
        }

        let timeoutPromise = new Promise((resolve) => {
            let timer = setTimeout(() => {
                resolve(valOnTimeout)
            }, timeout)
        })

        return Promise.race([promise, timeoutPromise])
    }

    /**
     * returns module filename relative to this class suitable for use in {@link serializedDataToObject}
     * typical usage: const serliaziedName = utils.getRelativeModulePath(__filename)
     * @param {String} classFn absolute path of module in Haiku hierarchy (from __filename in module)
     */
    getRelativeModulePath(classFn) {
        return './' + path.relative(__dirname, classFn)
    }

    /**
     * @typedef {Object} SerialzedData
     * @property {String} SerialzedData.serliaziedName The 'action' module name that returns a class
     * @property {Array} SerialzedData.args arguments to constructor in sequence
     */

    /**
     * checks if an object is in our serialized form (format as above) 
     * @param {SerialzedData} serialzedData Object that may be serialized representation {@link SerialzedData}
     */
    isSerializedObject(serialzedData) {
        return serialzedData && serialzedData.serliaziedName && Array.isArray(serialzedData.args)
    }

    /**
     * returns an instance from serialized data of the form module name followed by arguments to constructor in sequence
     * Ifthe object is not a serialized object, returns the input param
     * @param {SerialzedData} serialzedData serialized object in form as above
     */
    serializedDataToObject(serialzedData) {
        // sanity
        if (!this.isSerializedObject(serialzedData)) {
            return serialzedData
        }
        return new(require(serialzedData.serliaziedName))(...serialzedData.args)
    }

    encode(obj) {
        return Buffer.from(JSON.stringify(obj)).toString('base64')
    }

    async doesElementExist(xpath, browser) {
        // check if element exists
        let encElems = utils.encode([xpath]) // array since JS function expevcts array
        let jscode = `indusfaceRenderer.allElementsExist('${encElems}')`
        let elementExists = await utils.timedPromise(browser.webContents.executeJavaScript(jscode), false)

        //Added this code to give some pause for dom to load elements. 
        if(!elementExists) {
            elementExists = await utils.timedPromise(browser.webContents.executeJavaScript(jscode), 1000);
        }

        return elementExists
    }

    /**
     * normalize href and remove session-ish info from pathname
     * @param {string} href the href to canonicalize
     * @param {string} base The base URL to resolve against if the input is not absolute.
     */
    canonicalizeUrl(href, base) {
        let retHref = href
        try {
            let normUrl = this.normalizeUrl(href, base, {
                stripWWW: true,
                removeTrailingSlash: true,
                stripHash: false
            })
            normUrl.pathname = HaikuUtils.canonacalizePathname(normUrl.pathname)
            retHref = normUrl.href
        } catch (err) {
            logger.log('error', `could not normalize ${href}, ${err.toString()}`)
        }
        return retHref
    }

    /**
     * normalize href - canonacalize, sort query params etc.
     * @param {string} href the href to canonicalize
     * @param {string} base The base URL to resolve against if the input is not absolute.
     * @param {object} normalizeAdditionalOpts Any additional options to normalizeUrl
     */
    normalizeUrl(href, base, normalizeAdditionalOpts = {}) {
        let ret = new URL(href, base)
        let fullHref = ret.href
        try {
            let normalizeOptions = {
                stripHash: true,
                removeTrailingSlash: false
            }
            Object.assign(normalizeOptions, normalizeAdditionalOpts)
            ret = new URL(normalizeUrl(fullHref, normalizeOptions))
        } catch (err) {}

        return ret
    }

    getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }

    //https://stackoverflow.com/questions/3561493/is-there-a-regexp-escape-function-in-javascript
    // regExStr is expecetd to be a string not a RegExp object
    regexEscape(regExStr) {
        return regExStr.replace(/[.?+*^$|\(\)\{\}\[\]\\]/g, '\\$&');
    }

    /**
     * Tokenize URL using TOK_REGEX 
     * @param {string} urlToTokenize Full url to tokenize (http://...)
     * @param {integer} maxTokenLength tokens larger than this will be trimmed and ~any~ added after them. <! => unlimited
     */
    tokenizeUrl(urlToTokenize, maxTokenLength = 18) {
        // website + tokensize the rest
        let href = utils.canonicalizeUrl(urlToTokenize)
        let url = new URL(href)
        let tokens = href.replace(url.origin, '').split(TOK_REGEX).filter(x => x.length)
        if (maxTokenLength > 1) {
            // use only the the first maxTokenLength chars
            tokens = tokens.map(k => k.length < maxTokenLength ? k : k.substr(0, maxTokenLength - 1) + '~any~')
        }
        return [url.origin, ...tokens]
    }

    /**
     * since the regex is generate dby analyzing a bunch of URIs, there are a lot of optional matches. This function
     * returns number of matches and number of tokens so they can be used to accurately check if uri matches regex
     * @param {regex} r regex string generated by MSA from dup urls
     * @param {string} u URL to check if dup
     */
    countMatchesAndTokens(r, u) {
        let match = r.exec(u);
        return [
            match ? Object.keys(match).filter(m => /\d+/.test(m)).map(x => match[x]).filter(x => x != undefined).length - 1 : 0,
            utils.tokenizeUrl(u).length
        ]
    }

    /**
     * checks is a URL is a duplicate given duplicate matching regex
     * @param {string} regexStr generated regex string
     * @param {string} url URL to check if dup 
     */
    isDupUrl(regexStr, url) {
        let re2 = new RE2(regexStr)
        let matchesAndTokens = utils.countMatchesAndTokens(re2, utils.canonicalizeUrl(url))
        return matchesAndTokens[0] == matchesAndTokens[1]
    }

    sendMail(mailOptions) {
        if (!this.config || !this.config.utils || !this.config.utils.mail) {
            utils.log(`Mail configuration not provided, cannot send mail :\n${mailOptions}`)
            return
        }

        const nodemailer = require('nodemailer')

        // set from email address correctly
        mailOptions.from = this.config.utils.mail.auth.user

        // create reusable transporter object using the default SMTP transport
        if (!this.transporter) {
            // lazy initiailization
            let mailTransportOptions = this.config.utils.mail
            this.transporter = nodemailer.createTransport({
                host: mailTransportOptions.host,
                port: mailTransportOptions.port,
                secure: mailTransportOptions.secure,
                auth: mailTransportOptions.auth
            })
        }

        // send mail with defined transport object
        this.transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
                return utils.log(error);
            }
            utils.log('\n**Message %s sent: %s\n**', info.messageId, info.response);
        });
    }

    /**
     * Sends a test mail using nodemailers ethereal account. Logs the preview link
     * to see how the mail would look. See https://ethereal.email/.
     * @param {mailOptions} mailOptions as per nodemailer
     */
    async sendTestMail(mailOptions) {
        if (!this.config || !this.config.utils || !this.config.utils.mail) {
            utils.log(`Mail configuration not provided, cannot send mail :\n${mailOptions}`)
            return
        }

        const nodemailer = require('nodemailer')

        try {
            // create test ethereal email address
            let testAccount = await nodemailer.createTestAccount()

            // set from email address correctly
            mailOptions.from = this.config.utils.mail.auth.user

            // create temp transporter object using the default SMTP transport
            let transporter = nodemailer.createTransport({
                host: 'smtp.ethereal.email',
                port: 587,
                secure: false, // true for 465, false for other ports
                auth: {
                    user: testAccount.user, // generated ethereal user
                    pass: testAccount.pass // generated ethereal password
                }
            })

            // send mail with defined transport object
            let info = await transporter.sendMail(mailOptions)
            utils.log('Preview URL: %s', nodemailer.getTestMessageUrl(info));
        } catch (error) {
            utils.log(error);
        }
    }

    // sorry Venky, Vivek!
    printBanner() {
        let banner = `
                                                            
 ▄  █ ██   ▄█ █  █▀  ▄          ▄▄▄▄▄   ▄█▄    ██      ▄      ▄   ▄███▄   █▄▄▄▄ 
█   █ █ █  ██ █▄█     █        █     ▀▄ █▀ ▀▄  █ █      █      █  █▀   ▀  █  ▄▀ 
██▀▀█ █▄▄█ ██ █▀▄  █   █     ▄  ▀▀▀▀▄   █   ▀  █▄▄█ ██   █ ██   █ ██▄▄    █▀▀▌  
█   █ █  █ ▐█ █  █ █   █      ▀▄▄▄▄▀    █▄  ▄▀ █  █ █ █  █ █ █  █ █▄   ▄▀ █  █  
   █     █  ▐   █  █▄ ▄█                ▀███▀     █ █  █ █ █  █ █ ▀███▀     █   
  ▀     █      ▀    ▀▀▀                          █  █   ██ █   ██          ▀    
       ▀                                        ▀                               

        HAIKU SCANNER VERSION : ${require('../package.json').version}

 █████╗ ██████╗ ██████╗ ████████╗██████╗  █████╗ ███╗   ██╗ █████╗ 
██╔══██╗██╔══██╗██╔══██╗╚══██╔══╝██╔══██╗██╔══██╗████╗  ██║██╔══██╗
███████║██████╔╝██████╔╝   ██║   ██████╔╝███████║██╔██╗ ██║███████║
██╔══██║██╔═══╝ ██╔═══╝    ██║   ██╔══██╗██╔══██║██║╚██╗██║██╔══██║
██║  ██║██║     ██║        ██║   ██║  ██║██║  ██║██║ ╚████║██║  ██║
╚═╝  ╚═╝╚═╝     ╚═╝        ╚═╝   ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝
`
        utils.log(banner)
    }
}

utils = new Utils()
module.exports = utils