// Override require for 're2' to use our memory-efficient replacement
require('../app/require-override');

const debug = require('debug')('REST:scan-api')
var express = require('express');
var app = express();
var fs = require("fs");
var serveIndex = require('serve-index')
const path = require("path")
const mkdirp = require('mkdirp')
const osUtils = require('os-utils')
const Request = require('request-promise-native')
const os = require('os')
var bodyParser = require('body-parser');
const urlPkg = require('url')
const HaikuMsgQ = require('./messageQ')
const HaikuUtilsAPI = require('./utilsApi')
const HaikuUtilsWorkers = require('./utilsWorkers')
const stream = require('stream');
const HaikuUtils = require('../common/lib/haiku-utils');
const logger = require('../common/lib/haiku-logger');
const ssAPI = require('../common/lib/sooper-scheduler-api');
const hostFileQueue = require('../common/lib/host-file-queue')
const _ = require('lodash')

logger.setMetadata({
    haikuProcess: 'scan-api'
})

// Function to wrap runningScans with monitoring
function wrapRunningScansOperations(runningScans) {
    try {
        // Ensure we have a valid object to proxy
        const targetObj = (runningScans && typeof runningScans === 'object') ? runningScans : {};
        
        return new Proxy(targetObj, {
            set: function(target, property, value, receiver) {
                try {
                    // Use string representation safely
                    const propStr = typeof property === 'symbol' ? property.toString() : property;
                    logger.log('info', `runningScans[${propStr}] being set`);
                    // Use Reflect.set to properly handle property descriptors and return correct boolean
                    return Reflect.set(target, property, value, receiver);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy set: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return false;
                }
            },
            deleteProperty: function(target, property) {
                try {
                    const propStr = typeof property === 'symbol' ? property.toString() : property;
                    logger.log('info', `runningScans[${propStr}] being deleted`);
                    return Reflect.deleteProperty(target, property);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy delete: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return false;
                }
            },
            get: function(target, property, receiver) {
                try {
                    // Handle JSON serialization
                    if (property === 'toJSON') {
                        return function() { 
                            try {
                                // Return a plain object copy without the proxy
                                return Object.keys(target).reduce((obj, key) => {
                                    try {
                                        obj[key] = target[key];
                                    } catch (err) {
                                        obj[key] = undefined;
                                    }
                                    return obj;
                                }, {});
                            } catch (err) {
                                try {
                                    logger.log('error', `Error in toJSON: ${err.toString()}`);
                                } catch (logErr) {
                                    console.error('Logger error:', logErr);
                                }
                                return {}; // Return empty object as fallback
                            }
                        };
                    }
                    
                    // Get the property value
                    const value = Reflect.get(target, property, receiver);
                    
                    // If the property value is an object (and not null), wrap it with a proxy too
                    // This allows us to handle deep property access safely
                    if (value !== null && typeof value === 'object') {
                        return new Proxy(value, {
                            get: function(obj, prop) {
                                try {
                                    return obj[prop];
                                } catch (err) {
                                    try {
                                        logger.log('error', `Error accessing nested property ${String(property)}.${String(prop)}: ${err.toString()}`);
                                    } catch (logErr) {}
                                    return undefined;
                                }
                            }
                        });
                    }
                    
                    return value;
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy get for property ${String(property)}: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return undefined;
                }
            },
            // Add handler for Object.keys and similar operations
            ownKeys: function(target) {
                try {
                    return Reflect.ownKeys(target);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy ownKeys: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return []; // Return empty array as fallback
                }
            },
            // Add handler for property descriptor inspection
            getOwnPropertyDescriptor: function(target, prop) {
                try {
                    return Reflect.getOwnPropertyDescriptor(target, prop);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy getOwnPropertyDescriptor: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return undefined;
                }
            },
            // Add handler for in operator
            has: function(target, prop) {
                try {
                    return Reflect.has(target, prop);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy has: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return false;
                }
            },
            // Add handler for Object.defineProperty
            defineProperty: function(target, prop, descriptor) {
                try {
                    return Reflect.defineProperty(target, prop, descriptor);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy defineProperty: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return false;
                }
            },
            // Add handler for Object.preventExtensions
            preventExtensions: function(target) {
                try {
                    return Reflect.preventExtensions(target);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy preventExtensions: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return false;
                }
            },
            // Add handler for Object.isExtensible
            isExtensible: function(target) {
                try {
                    return Reflect.isExtensible(target);
                } catch (err) {
                    try {
                        logger.log('error', `Error in proxy isExtensible: ${err.toString()}`);
                    } catch (logErr) {
                        console.error('Logger error:', logErr);
                    }
                    return false;
                }
            }
        });
    } catch (err) {
        console.error('Fatal error creating proxy:', err);
        // Return a plain object as fallback so the app won't crash
        return {};
    }
}

// global
let APIEndPoint = process.env['SCAN_API_ENDPOINT'] || 'http://***********' // connectria scan api IP 
if (process.env['SCAN_API_LISTEN_PORT']) {
    APIEndPoint += ":" + process.env['SCAN_API_LISTEN_PORT']
}
let runningScans = wrapRunningScansOperations({});

// middleware
app.use(bodyParser.json({
    limit: '5mb'
}));
app.use(bodyParser.urlencoded({
    extended: false,
    limit: '5mb'
}))

// log all errors
app.use(logErrors)

function logErrors(err, req, res, next) {
    logger.log('error', `catch all error handler: ${err.toString()}`)
    next(err)
}

// Simple logging of request
function logAndSendResponse(req, res, respJSON) {
    // send the response so other processing continues
    let result = _.get(respJSON, 'result');
    let isBuffer = _.isBuffer(result);

    if(isBuffer) {
        let stream = HaikuUtils.bufferToStream(result);
        stream.pipe(res);
    }
    else {
        res.json(respJSON);
    }

    // now log
    let reqJSON = {
        method: req.method,
        url: req.originalUrl,
        params: req.params,
        body: req.body
    }

    let logObject = {
        request: reqJSON,
        response: isBuffer ? null : respJSON
    }

    logger.log('info', `${JSON.stringify(logObject)}`)
}

async function getScanConfig(id, scanType) {
    // get the config from the REST API
    let config = null;

    if (scanType) {
        config = await ssAPI.getMalwareScanConfig(id)
    }
    else {
        config = await ssAPI.getScanConfig(id)
    }

    logger.log('info', `Site config: ${JSON.stringify(config)}`)
    return config
}

// *** Technical Debt ***
// Should refactor all the scan API functions insto class like HaikuUtilsAPI and HaikuMsgQ relay
// *** Technical Debt ***

// get config by ID
function getConfig(req, res) {
    let id = req.params.id
    if (id && siteScanProfiles[id]) {
        logAndSendResponse(req, res, {
            status: "OK",
            result: [siteScanProfiles[id]]
        })
    } else {
        logAndSendResponse(req, res, {
            status: "NOK",
            error: `id ${id} not found`
        })
    }
}


// register machine with SS
function getMachineInfo() {
    let ifaces = os.networkInterfaces()
    let address
    for (let ifName in ifaces) {
        for (let ifc of ifaces[ifName]) {
            // skip local & IPv6 interface
            if (ifc.internal || ifc.family != 'IPv4') {
                continue
            }
            address = ifc.address
            break
        }
        if (address) {
            break
        }
    }

    return {
        machine_name: os.hostname(),
        physical_add: os.hostname(), // till https://github.com/nodejs/node/issues/13581 is resolved
        machine_ip: address ? address : 'n/a',
        machine_type: 'LX',
        port_no: process.env['SCAN_API_LISTEN_PORT'] || 80
    }
}

async function registerMachineWithSS(machineInfo) {
    if (process.env['SCAN_MACHINE_ID']) {
        logger.log('info', 'Running locally, will not send stats to SOC portal')
        return process.env['SCAN_MACHINE_ID']
    }

    var options = {
        method: 'POST',
        uri: `${APIEndPoint}/scanner/v1/machine-insert`,
        headers: {
            'accessKey': 'cashoktl31lyepk2pYmQa3IB0RUyt2im',
            'secretKey': 'QsahdevxqinGwwkLqooUvswcZaelHcpg'
        },
        body: machineInfo,
        json: true // Automatically parses the JSON string in the response
    }

    let machine_id
    let ret = await Request(options)
    if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
        machine_id = ret.result.machine_id

        logger.log('info', `starting timer to send machine stats (id=${machine_id}) to SS every 90 seconds`)
        setInterval(sendMachineStatsToSS, 90 * 1000, machine_id)
    } else {
        logger.log('info', `Could not register machine: ${JSON.stringify(ret)}`)
    }

    return machine_id
}

async function loadProfiles() {
    return await new Promise((res, rej) => {
        fs.readFile(profilesFilename, 'utf8', function (err, data) {
            if (err) {
                logger.log('error', `could not read ${configFilename}`)
                rej(err)
            }
            siteScanProfiles = JSON.parse(data)
            res(true)
        })
    })
}

function getCpuUsage() {
    return new Promise((resolve) => {
        osUtils.cpuUsage(function (v) {
            resolve(v);
        });
    })
}

async function getMachineStats() {
    let cpuUsage = await getCpuUsage()
    
    // Get all scan IDs and count them
    const scanIds = runningScans ? Object.keys(runningScans) : [];
    const runningJobCount = scanIds.length;
    
    // Create a detailed list of running processes for logging
    const runningProcesses = scanIds.map(id => ({
        id,
        pid: runningScans[id]?.pid,
        startedAt: runningScans[id]?.startedAt
    }));
    
    // Log the current state with detailed information
    logger.log('info', `Getting machine stats - running jobs: ${runningJobCount}, scan IDs: ${JSON.stringify(scanIds)}, metadata: ${JSON.stringify(logger.getMetadata() || {})}`);
    
    // If we're showing 0 jobs but we suspect there are processes, check the system
    if (runningJobCount === 0) {
        try {
            // Use child_process.exec to get actual running processes (Linux compatible)
            const { exec } = require('child_process');
            exec('pgrep -f electron | wc -l', (error, stdout) => {
                if (!error) {
                    const actualProcessCount = parseInt(stdout.trim(), 10);
                    if (actualProcessCount > 0) {
                        logger.log('warn', `Potential tracking issue: runningScans shows ${runningJobCount} but found ${actualProcessCount} electron processes`);
                        
                        // Additional diagnostic - list the actual processes
                        exec('ps -ef | grep electron | grep -v grep', (err, psOutput) => {
                            if (!err && psOutput) {
                                logger.log('info', `Running electron processes:\n${psOutput}`);
                            }
                        });
                    }
                }
            });
        } catch (err) {
            logger.log('error', `Error checking actual processes: ${err.toString()}`);
        }
    }

    return {
        id: -100,
        runningjob: runningJobCount,
        scanIds: scanIds, // Add scan IDs to the stats
        icpuusage: cpuUsage * 100,
        dmemoryusage: osUtils.freememPercentage() * 100,
        irank: 1 // hardcoded - not important.
    }
}

async function sendMachineStatsToSS(machine_id) {
    try {
        // Get machine stats with scan IDs
        let machineStats = await getMachineStats();
        machineStats.id = machine_id;

        // Capture the current logger metadata for diagnostic purposes
        const currentMetadata = Object.assign({}, logger.getMetadata() || {});
        
        // Extract scan IDs for more readable logging
        const scanIds = machineStats.scanIds || [];
        
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/machine-alive`,
            headers: {
                'accessKey': 'cashoktl31lyepk2pYmQa3IB0RUyt2im',
                'secretKey': 'QsahdevxqinGwwkLqooUvswcZaelHcpg'
            },
            body: machineStats,
            json: true // Automatically parses the JSON string in the response
        }

        let ret = await Request(options);
        if (ret) {
            // Log scan IDs separately for better visibility
            logger.log('info', `Send Machine Stats: ${JSON.stringify(options.body)} -> ${JSON.stringify(ret)}, scan IDs: ${JSON.stringify(scanIds)}, metadata: ${JSON.stringify(currentMetadata)}`);
        } else {
            logger.log('error', `sending machine stats to SS failed: ${ret}`);
        }
    } catch (err) {
        logger.log('error', `sending machine stats to SS failed: ${err.toString()}`);
    }
}

// set up utils API
logger.log('info', `setting up Utils API`)
let haikuUtilsAPI = new HaikuUtilsAPI(logAndSendResponse)
haikuUtilsAPI.init(app)

// Set up Workers
if (process.env['SCAN_API_ENABLE_WORKERS']) {
    let haikuUtilsWorkers = new HaikuUtilsWorkers()
    haikuUtilsWorkers.init()
}

// SCAN API control endpoints called by WAS (disabled if in UTILS API only mode)
if (!process.env['SCAN_API_UTILS_ONLY']) {
    // set up the message queue relay
    logger.log('info', `connecting to rabbit msg queue`)
    let haikuMsgQ = new HaikuMsgQ()
    haikuMsgQ.init(app)


    // list all configs 
    app.get('/getConfig', function (req, res) {
        logAndSendResponse(req, res, {
            status: "OK",
            profile: siteScanProfiles
        })
    })

    app.get('/getConfig/:id', getConfig)
    app.get('/scanner/v1/aaservices-scannerconfig/:id', getConfig)
    app.get('/scanner/v2/mmservices-scannerconfig/:id', getConfig)
    app.post(['/scanner/v1/jobsession-jobstart', '/scanner/v2/mm-jobsession-jobstart'], function (req, res) {
        let startReq = req.body
        let resp = {
            status: "OK",
            result: {
                scanlog_id: startReq.scanlog_id || startReq.session_id,
                maxScanTimeMins: startReq.maxScanTimeMins
            }
        }
        logAndSendResponse(req, res, resp)
    })

    // Start scan for ID
    const {
        spawn
    } = require('child_process')

    app.get(['/startScan/:id', '/startScan/:id/:scanType', '/resumeScan/:id','/resumeScan/:id/:scanType'], async function (req, res) {
        let id = req.params.id;
        let scannerMachineUID = req.query ? req.query.scannerMachineUID : null;
        logger.log('info', `API=> ${req.url} called via IP ${req.ip} for scanId=${id}`, HaikuUtils.getMetadataForLog({ scanId: id }));

        let electronCommand = require(path.join(__dirname, '../node_modules/electron'))

        let scanner = path.join(__dirname, '../app/main.js')
        if (id) {
            // if we already have a crawler running for this id, kill existing one then start new one
            let previousRunningScanPid
            if (runningScans[id]) {
                logger.log('info', `Force kill previous crawler instance.`, HaikuUtils.getMetadataForLog({
                    scanId: id
                }));
                previousRunningScanPid = runningScans[id].pid
                process.kill(previousRunningScanPid) // not graceful kill using SIGINT, just wipe it out
            }

            // if(Object.keys(runningScans).length > 10) {
            //     logAndSendResponse(req, res, {
            //         status: "MAX_SCAN_LIMIT_REACHED",
            //         error: `Unable to start/resume scan for ${id}, because max scan 10 limit per machine has been reached.`,
            //         data: {
            //             runningScansIds: Object.keys(runningScans)
            //         }
            //     })
            // }

            // timestamp in format: 20180504_041724 i.e. YYYYMMDD_HHMMSS
            let logDir = path.join(__dirname, `../logs/${id}`);
            mkdirp.sync(logDir)

            // TODO: the scanLogId should actually be scanLogId that is to be returned by the SOC portal API
            let timestamp = new Date().toISOString().replace(/T/, '_').replace(/\..+/, '').replace(/:+/g, '-').replace(/-+/g, '')
            let scanLogId = timestamp
            let outFile = path.join(logDir, `./scanid-${id}-out-${scanLogId}.log`)
            const out = fs.openSync(outFile, "w");

            let crawlerArgs = ['--no-sandbox', scanner, '--id=' + id, '--outFile=' + outFile];

            if(req.params.scanType) {
                crawlerArgs.push('--scanType=' + req.params.scanType);
            }

            // if scannerMachineUID is passed, pass it to crawler
            if(scannerMachineUID) {
                crawlerArgs.push('--scannerMachineUID=' + scannerMachineUID);
                logger.log('info', `Scanner machine UID passed to crawler: ${scannerMachineUID}`, HaikuUtils.getMetadataForLog({ scanId: id }));
            }

            let config = {}; // Initialize config
            try {
                config = await getScanConfig(id, req.params.scanType);

                const replayScanInfo = _.get(config, 'haikuScanner.networkScanner.ScannerSettings.replayScanInfo');
                const isCdnEnabled = _.get(replayScanInfo, 'isCdnEnabled', false);
                let wafBlockName = _.get(replayScanInfo, 'wafBlockName', null);

                if(wafBlockName) {
                    wafBlockName = `${wafBlockName}.apptrana.com`;
                }


                let wafIpToUse = null;
                const targetHostname = new URL(config.url).hostname;
                const logContext = { scanId: id, uri: config.url, haikuKey: config.url };
                let proxyPassDetails = null;

                if (isCdnEnabled && wafBlockName) {
                    logger.log('info', `CDN Bypass: Enabled for ${targetHostname}, wafblockname: ${wafBlockName}. Attempting DNS lookup.`, logContext);
                    wafIpToUse = await HaikuUtils.doDNSLookup(wafBlockName, logger);
                    if (wafIpToUse) {
                        crawlerArgs.push(`--wafIpToUse=${wafIpToUse}`);
                        logger.log('info', `CDN Bypass: DNS lookup for ${wafBlockName} successful. IP: ${wafIpToUse}`, logContext);
                        proxyPassDetails = {
                            [targetHostname]: { ip: wafIpToUse, proxyhostname: wafBlockName },
                            // The www variant is handled by updateHostFileIfNeeded internally
                            ['www.' + targetHostname]: { ip: wafIpToUse, proxyhostname: wafBlockName }
                        };
                        // Single call, updateHostFileIfNeeded handles both hostname and www.hostname
                        await hostFileQueue.updateHostFileWithQueue(targetHostname, false, logContext, proxyPassDetails, logger);
                        logger.log('info', `CDN Bypass: Host entry modification initiated for ${targetHostname} (and www variant) to use WAF IP ${wafIpToUse}.`, logContext);
                    }
                }

                // Existing proxyPassDetails logic for origin server bypass
                config.parsedUrl = urlPkg.parse(config.url);
                let existingSkipUpdateHostFile = _.get(config, 'haikuScanner.skipUpdateHostFile', false);
                if (replayScanInfo && _.isBoolean(replayScanInfo.isReplayScan)) {
                    existingSkipUpdateHostFile = replayScanInfo.isReplayScan;
                }
                
                // Only apply general proxyPassDetails if CDN bypass didn't set an IP for the main target OR if targetHostname is different from config.parsedUrl.hostname
                // This prevents overwriting a specific CDN bypass IP with a general origin IP.
                if (!wafIpToUse || targetHostname.toLowerCase() !== config.parsedUrl.hostname.toLowerCase()) {
                    proxyPassDetails = await HaikuUtils.getProxyPass(config.parsedUrl.hostname, logContext, logger);
                    await hostFileQueue.updateHostFileWithQueue(config.parsedUrl.hostname, existingSkipUpdateHostFile, logContext, proxyPassDetails, logger);
                } else if (wafIpToUse && targetHostname.toLowerCase() === config.parsedUrl.hostname.toLowerCase()) {
                    logger.log('info', `CDN Bypass IP ${wafIpToUse} was set for ${targetHostname}. General proxyPass update for this hostname was handled by CDN bypass logic.`, logContext);
                }

                logger.log('info', `ProxyPassDetails: ${JSON.stringify(proxyPassDetails)}`, logContext);
                crawlerArgs.push(`--proxyPassDetails=${JSON.stringify(proxyPassDetails)}`);
            } catch (err) {
                logger.log('error', `Error during CDN bypass or proxyPass setup: ${err.toString()}`, { scanId: id, uri: config.url });
                // Fallback to ensure host entries are cleared in case of error during setup
                try {
                    const targetHostnameOnError = new URL(config.url).hostname; // Re-evaluate in case config.url was not set
                    const logContextOnError = { scanId: id, uri: config.url || 'unknown_url_on_error', haikuKey: config.url || 'unknown_url_on_error' };
                    logger.log('warn', `CDN Bypass/General Host Cleanup: Error occurred during setup. Attempting to clear host entries for ${targetHostnameOnError}.`, logContextOnError);
                    await hostFileQueue.updateHostFileWithQueue(targetHostnameOnError, true, logContextOnError, {}, logger);
                } catch (cleanupErr) {
                    logger.log('error', `Error during fallback host entry cleanup: ${cleanupErr.toString()}`, { scanId: id });
                }
            }
            
            // tell crawler if we are resuming
            if (/resumeScan/.test(req.path)) {
                crawlerArgs.push('--resumeScan=true')
            }

            logger.log('info', `Spawning: ${electronCommand} ${crawlerArgs}`, {
                scanId: id
            })
            let child = spawn(electronCommand, crawlerArgs, {
                //detached: true,
                stdio: ['ignore', out, out]
            })

            // clean up running list
            child.on('exit', (code) => {
                logger.log('info', `Crawler process exit event triggered. exit code ${code}`, HaikuUtils.getMetadataForLog({
                    scanId: id
                }));

                if (runningScans[id] && runningScans[id].pid == child.pid) {
                    delete runningScans[id]
                    logger.log('info', `Cleaned crawler instance gracefully.`, HaikuUtils.getMetadataForLog({
                        scanId: id
                    }));
                }
            })
            child.unref();

            if (child.pid) {
                runningScans[id] = {
                    startedAt: new Date().toLocaleString(),
                    pid: child.pid,
                    profile: siteScanProfiles[id] ? siteScanProfiles[id] : 'no profile on this server',
                    outFile: outFile,
                    previousRunningScanPid
                }
            }

            logAndSendResponse(req, res, {
                status: "OK",
                pid: child.pid,
                id: id,
                scanInfo: runningScans[id],
                profile: siteScanProfiles[id],
                previousRunningScanPid
            })
        } else {
            logAndSendResponse(req, res, {
                status: "NOK",
                error: `Required param id ${id} not present`
            })
        }
    })

    app.get(['/stopScan/:id', '/pauseScan/:id'], function (req, res) {
        let id = req.params.id;
        logger.log('info', `API=> ${req.url} called via IP ${req.ip} for scanId=${id}`, HaikuUtils.getMetadataForLog({scanId: id}));
        if (!id) {
            logAndSendResponse(req, res, {
                status: "NOK",
                id,
                error: `Invalid id ${JSON.stringify(req.params)}`
            });
            return;
        }
        // stop scanner
        haikuMsgQ.pauseScan(id);
        // stop crawler
        if (id && runningScans[id]) {
            // stop crawler
            let pidToKill = runningScans[id].pid;
            process.kill(pidToKill, 'SIGINT');
            logAndSendResponse(req, res, {
                status: "OK",
                pid: pidToKill,
                id: id,
                message: `sent pause (stop) signal to crawler & scanner`
            });
        } else {
            logAndSendResponse(req, res, {
                status: "OK",
                id,
                error: `No crawler running with id = ${id}`,
                message: `sent pause (stop) signal to scanner`
            });
        }
    })

    app.get(['/stopAllScans', '/pauseAllScans'], function (req, res) {
        logger.log('info', `API=> ${req.url} called via IP ${req.ip} for all running scans.`);
        let scansStopped = []
        for (let id in runningScans) {
            let scan = runningScans[id]
            let pidToKill = scan.pid
            process.kill(pidToKill, 'SIGINT')
            scansStopped.push({
                id: id,
                pid: pidToKill
            })

            // tell scanner to stop as well
            let scannerRet = pauseScan(id)
        }

        logAndSendResponse(req, res, {
            status: "OK",
            scansStopped: scansStopped,
            message: "Sent stop signal (SIGINT) to all running scans. Can take up to 5 minutes to stop"
        })

    })

    // get info on all scans
    app.get('/getScanInfo', function (req, res) {
        // check if all these scans are still running
        logAndSendResponse(req, res, {
            status: "OK",
            runningScans: runningScans
        })
    })

    // get info on specific scan 
    app.get('/getScanInfo/:id', function (req, res) {
        let id = req.params.id
        if (runningScans[id]) {
            let filename = runningScans[id].outFile
            fs.readFile(filename, 'utf8', function (err, data) {
                if (err) {
                    data = `could not read ${filename} : ${err}`
                }

                res.end(data)
            })
        } else {
            res.end(`No running scan with ID : ${id}`)
        }
    })

    // reload config
    app.get('/reload', async function (req, res) {
        let ret = await loadProfiles()
        logAndSendResponse(req, res, {
            status: "OK",
            reloaded: ret
        })
    })

    // reload config
    app.get('/getMachineStats', async function (req, res) {
        logger.log('info', `API=> ${req.url} called via IP ${req.ip}`);
        let ret = await getMachineStats()
        logAndSendResponse(req, res, {
            status: "OK",
            machineStats: ret
        })
    })

    // -----------
    app.use('/files', serveIndex(path.join(__dirname, '../logs'), {
        'icons': true
    }))

    app.use('/files', express.static(path.join(__dirname, '../logs')))
}

// catch all - always the last param...
app.all('*/', (req, res) => {

    logger.log('info', `got unknown request - echoing back:${JSON.stringify({
        method: req.method,
        url: req.originalUrl,
        params: req.params,
        body: req.body,
        headers: req.headers
    })}`)
    res.json({
        status: "OK",
        msg: 'echoing request - to aid in local debug...',
        method: req.method,
        url: req.originalUrl,
        params: req.params,
        body: req.body,
        headers: req.headers
    })
})


// main
let server // the express server
let siteScanProfiles // the site scan profile data
let profilesFilename = path.join(__dirname, '/sites-config.json')

let machineInfo = getMachineInfo()
if (process.env['SCAN_API_UTILS_ONLY']) {
    server = app.listen(machineInfo.port_no, function () {
        let host = server.address().address
        let port = server.address().port
        logger.addMetadata({
            host
        })
        logger.log('info', `Scan API running in Utils API only mode - listening at http://${host}:${port}`)
    })
} else {
    registerMachineWithSS(machineInfo).then((machine_id) => {
        if (machine_id) {
            logger.addMetadata({
                machineId: machine_id
            })
            logger.log('info', `starting scan REST API server`)
            // read config and create rest api service
            if (loadProfiles()) {
                server = app.listen(machineInfo.port_no, function () {
                    let host = server.address().address
                    let port = server.address().port
                    
                    // Make sure host metadata is always set consistently
                    logger.addMetadata({
                        host: host || "::" // Use a default if host is empty
                    })
                    
                    logger.log('info', `Scan API service listening at http://${host}:${port}, host metadata set to: ${host}`)
                })
            }
        } else {
            logger.log('error', 'error - could not start server')
        }
    }).catch((e) => logger.log('error occured while registering machine with SS: ', `${e.toString()}`))
}