const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')
const caseless = require('caseless')

class AuthDtsinURL extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-session-id-in-url'
        this.ImproperTokenHandling = 'ID-improper-token-handling'
        this.SensitiveInformationInURL = 'ID-sensitive-information-url'
    }

    /* wantProcessAttackResponse(originalRequest) {
        if (originalRequest.attackArea == 'original-crawler-request') {
            let uri = new URL(originalRequest.href)
            let ResBody = _.get(originalRequest, "result.resp.body", '')
            let args_regexp = /token|session|\w*[\-_]?(?:number|no)s?\b|mobile|contact|phone|blood|\bATM|\bPIN(?![\-_]code)|\bPAN|email|(credit|debit|ATM)[_\-]?(card|id)|(user|emp|employee|customer)[_\-]?(id|name|Input)|uname|uid|user|account|\bac[_\-]id|password|passwd|pwd|api[_\-]?key|key\b|customerId|productId|invoiceId|messageId|deviceId|accountId|memberId|articleId|commentId|postId|ticketId|requestId|referenceId|documentId|assetId|bankId|policyId|claimId|loanId|branchId|clientId|contractId|fundId|investmentId|portfolioId|paymentId|premiumId|quoteId|settlementId|profileId|subscriberid|\bOTP|cardPin|pseudo_id|appid|\bpid/ig
            //token|session|(\w*( |-|_)?(number|no)|\w*(\b|-|_)(mobile|contact|phone|blood_group|(user)?email_?(id|address)?|(credit|debit|pan)(_|-)?(card|id)|(user|emp|employee)(_|-)?(id|name|Input)|uname|uid|user|(account|ac)(_|-)?(id)?|(password|passwd|pwd)(_|-)?(new|old|conf)?|api(_|-)?key)) ?= ?(?! \+ |\+)[^'"<>{}\?&]+/i
            if (args_regexp.test(uri.search) || (args_regexp.test(ResBody))) {
                return true
            }
        }
        return false
    } */

    async processAttackResponse(originalRequest) {
        if (originalRequest.attackArea == 'original-crawler-request' && originalRequest.pluginName == 'Original Crawler Request') {
            let pluginDataForRequest = this.getPluginScopedStore(originalRequest)
            //if vuln detected for a req then return
            if (pluginDataForRequest.SessionIDFound && pluginDataForRequest.tokenFound && pluginDataForRequest.SensitiveDataFound) {
                return
            }

            let args_regexp = /\w*[\-_]?(?:number|no)s?\b|mobile|contact|phone|blood|\bATM|\bPIN(?![\-_]code)|\bPAN|email|(?:credit|debit|ATM)[_\-]?(?:card|id)|(?:user|emp|employee|customer)[_\-]?(?:id|name|Input)|uname|uid|user|account|\bac[_\-]id|password|passwd|pwd|api[_\-]?key|key\b|customerId|productId|invoiceId|messageId|deviceId|accountId|memberId|articleId|commentId|postId|ticketId|requestId|referenceId|documentId|assetId|bankId|policyId|claimId|loanId|branchId|clientId|contractId|fundId|investmentId|portfolioId|paymentId|premiumId|quoteId|settlementId|profileId|subscriberid|\bOTP|cardPin|pseudo_id|appid|\bpid/i

            let userdetails = []
            let tokendetails = []
            let sessiondetails = []
            // let uri = new URL(originalRequest.href)
            let ResBody = _.get(originalRequest, "result.resp.body", '')

            /*  if (args_regexp.test(uri.search) || /token|session/i.test(uri.search)) {
                 // below regex for exclude the argnames comes in value side - RHS
                 let argvalregexp = /( |\b|_)(?:pan|otp|credit|debit|(set)?(user|employee)(_|-| )?(id|name|Input)|user|(account|ac)_?(id)?|(set|enter)(-|_)?password|api(_|-)key|mobile|contact|phone|nil|(in)?valid|number|defaultText|No|none|Choose|select|yes|**********|fax|cell|indusface.com|true)\b| = ?"|N\/A|special characters|required|null|haiku(msg)?|undefined|false/i
                 let params = []
                 if (uri.search.length > 5) {
                     params = uri.search.split('?')[1].split('&');
                     if (params.length > 0) {
                         for (let checkparam of params) {
                             let args = checkparam.split('=')
                             if (args[0].length > 1 && args[1].length > 1 && !argvalregexp.test(args[1])) {
                                 if (/token/i.test(args[0])) {
                                     tokendetails.push({ result: checkparam })
                                 }
                                 if (/session/i.test(args[0])) {
                                     sessiondetails.push({ result: checkparam })
                                 }
                                 //Sensitive data disclosed in URL            
                                 if (args_regexp.test(args[0])) {
                                     userdetails.push({ result: checkparam })
                                 }
                             }
                             else { continue }
                         }
                         if (tokendetails.length > 0) {
                             this.addVulnerabilitytoResult(originalRequest, this.ImproperTokenHandling, tokendetails)
                             pluginDataForRequest.tokenFound = true
                         }
                         if (sessiondetails.length > 0) {
                             this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, sessiondetails)
                             pluginDataForRequest.SessionIDFound = true
                         }
                         if (userdetails.length > 0) {
                             this.addVulnerabilitytoResult(originalRequest, this.SensitiveInformationInURL, userdetails)
                             pluginDataForRequest.SensitiveDataFound = true
                         }
                     }
                 }
             } */ //Prone to false positives - Required feature to understand Ori req method. Request raised.30 dec 2024.
            //AA scan - Check in ResBody        
            if (/< ?form.+method ?= ?(?:'|")GET/i.test(ResBody) && args_regexp.test(ResBody)) {
                let regexp = /< ?form.+method ?= ?('|")GET\1[^>]+>.+?< ?\/form ?>/gis
                let formtag = []
                try {
                    formtag = ResBody.match(regexp)
                    if (formtag != null && formtag.length > 0 && args_regexp.test(formtag)) {
                        for (let tag of formtag) {
                            if (tag != null && tag.length > 20 && args_regexp.test(tag)) {
                                let inputtag = tag.match(/< ?input[^>]+?>/gi)
                                for (let stag of inputtag) {
                                    let argname = stag.match(/name ?= ?('|")[^'"]+?\1/gi)
                                    if (argname != null && argname.length > 0) {
                                        try {
                                            let val = stag.match(/value ?= ?('|")[^'"]+?\1/gi)
                                            if (val != null) {
                                                val = val[0].split('=')[1].replace(/\"/g, '')
                                                if (val.length > 0 && !/\*/.test(val)) {
                                                    if (args_regexp.test(argname)) {
                                                        userdetails.push({ result: stag })
                                                    }
                                                    if (/session/i.test(argname)) {
                                                        sessiondetails.push({ result: stag })
                                                    }
                                                    if (/token/i.test(argname)) {
                                                        tokendetails.push({ result: stag })
                                                    }
                                                }
                                            }
                                        } catch (e) { return }
                                    }
                                }
                            }
                        }
                        if (tokendetails.length > 0) {
                            this.addVulnerabilitytoResult(originalRequest, this.ImproperTokenHandling, tokendetails)
                            pluginDataForRequest.tokenFound = true
                        }
                        if (sessiondetails.length > 0) {
                            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, sessiondetails)
                            pluginDataForRequest.SessionIDFound = true
                        }
                        if (userdetails.length > 0) {
                            this.addVulnerabilitytoResult(originalRequest, this.SensitiveInformationInURL, userdetails)
                            pluginDataForRequest.SensitiveDataFound = true
                        }
                    }
                }
                catch (e) {
                    return
                }
            }

            //Check in URL itself
            if (this.isApiScanMode(originalRequest.originalRequest)) {
                let uri = new URL(originalRequest.href)
                // below regex for exclude the argnames comes in value side - RHS
                let argvalregexp = /( |\b|_)(?:pan|otp|credit|debit|(set)?(user|employee)(_|-| )?(id|name|Input)|user|(account|ac)_?(id)?|(set|enter)(-|_)?password|api(_|-)key|mobile|contact|phone|nil|(in)?valid|number|defaultText|No|none|Choose|select|yes|**********|fax|cell|indusface.com|true)\b| = ?"|N\/A|special characters|required|null|haiku(msg)?|undefined|false/i
                let params
                if (uri.search.length > 5) {
                    params = uri.search.split('?')[1].split('&');
                    if (params.length > 0 && /token|session/i.test(params) || args_regexp.test(params)) {
                        let userdetails = []
                        let tokendetails = []
                        let sessiondetails = []
                        for (let checkparam of params) {
                            let args = checkparam.split('=')
                            if (args[0].length > 1 && args[1].length > 1 && !argvalregexp.test(args[1])) {
                                if (/token|session/i.test(args[0])) {
                                    if (/token/i.test(args[0])) {
                                        tokendetails.push({ result: checkparam })
                                    }
                                    if (/session/i.test(args[0])) {
                                        sessiondetails.push({ result: checkparam })
                                    }
                                }
                                //Sensitive data disclosed in URL            
                                else if (args_regexp.test(args[0])) {
                                    userdetails.push({ result: checkparam })
                                }
                            }
                            else { continue }
                        }
                        if (tokendetails.length > 0) {
                            this.addVulnerabilitytoResult(originalRequest, this.ImproperTokenHandling, tokendetails)
                            pluginDataForRequest.tokenFound = true
                        }
                        if (sessiondetails.length > 0) {
                            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, sessiondetails)
                            pluginDataForRequest.SessionIDFound = true
                        }
                        if (userdetails.length > 0) {
                            this.addVulnerabilitytoResult(originalRequest, this.SensitiveInformationInURL, userdetails)
                            pluginDataForRequest.SensitiveDataFound = true
                        }
                    }
                }

                // ImproperTokenHandling - API
                if (this.isApiScanMode(originalRequest)) {
                    let statusCode = _.get(originalRequest, 'result.resp.httpResponse.statusCode')
                    let contentTypeHeaderVal = _.get(originalRequest, 'result.resp.httpResponse.headers["content-type"]', '')
                    if (contentTypeHeaderVal.length == 0) { return }
                    let OriHeaders = caseless(_.get(originalRequest, 'originalRequest.httpRequest.headers', ""));
                    let auth = OriHeaders.get('authorization');
                    if (contentTypeHeaderVal == 'application/json' && statusCode == 200 && args_regexp.test(ResBody) && !auth) {
                        let tokendetails = []
                        tokendetails.push({ result: 'Valid token not found' })
                        this.addVulnerabilitytoResult(originalRequest, this.ImproperTokenHandling, tokendetails)
                        pluginDataForRequest.tokenFound = true
                    }
                }
            }
        }
    }
    onAutoPOC(originalRequest, vulnID) {
        super.onAutoPOC(originalRequest, vulnID);
        if (vulnID == this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(originalRequest, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(originalRequest, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
        if (vulnID == this.ImproperTokenHandling) {
            HaikuUtils.addToAutoPOCAnnotation(originalRequest, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(originalRequest, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
        if (vulnID == this.SensitiveInformationInURL) {
            HaikuUtils.addToAutoPOCAnnotation(originalRequest, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(originalRequest, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
    }
}

module.exports = AuthDtsinURL