const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
var crypto = require('crypto')
const FingerPrint = require('../../../app/datastructure/fingerprint.js')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

/** CGI generic unseen common parmaters plugin Strategy:
 * VectorResponse style plugin that checks for CGI generic hidden common parmaters.
 * Like repeater we resend request with added parameter(one from vector list).. 
 * repeater will send repeated GET/POST request with all permutation and combination of poison vectors taking one per request.  
 * We check status code as 200 and compare hash of body from original and current response.
 * If response page has significantly different body above condition will be true, then we report vulnerability found with injected parameter.
 * 
 * 
 */

class CGIAttack extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the CGIattack plugin
     */
    constructor(networkScanner, config) {
        //config.HTTPRequestMonster.allowedContentTypes=`/text|application\/(xhtml\+xml|xml|csv|css|javascript|java-archive|x-httpd-php|x-sh|xhtml\+xml|bin|x-csh)/i`
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-cgi-generic-unseen-parameter'
    }
    //below code is used to compare response body
    checksimilarity(resp1, resp2) {
        var fingerprint1 = new FingerPrint(resp1)
        var fingerprint2 = new FingerPrint(resp2)

        if (resp1.length > resp2.length) {
            return fingerprint1.similarity(fingerprint2)
        }
        if (resp2.length > resp1.length) {
            return fingerprint2.similarity(fingerprint1)
        }
        return fingerprint1.similarity(fingerprint2)
    }
    //options below modify behaviour of getAttackableEvents, refer network config file to know available options for each iterator
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            skipRoot: false,
            maxPathComponents: 4,
            clearQueryParams: false,
            addSlashBeforeAttack: false,
            encodings: ['raw'],
            haveSlashAfterAttack: 'never'
        });
    }
    /**
     * get array of CGI attack vectors
     * @override
     */
    getAttackVectors() {
        return CGIvectors
    }
    //below code will decide injection point for vectors in order to attack
    getAttackableEvents() {
        return ['uri-path-iterator']
    }
    // Overriding the performNetworkAttack method to change method in attack and it's body specifically 
    //modifying behaviur of post-body iterator to not replace body params but append vectors with already present params. (example: )
    //else part will inject vector when no parameter already exist 
    /* async performNetworkAttack(attack) {
        return await super.performNetworkAttack(attack)
    }*/

    async processAttackResponse(attack) {
        // get plugin storage 
        let pluginStorage = this.getPluginScopedStore(attack)

        if (attack.pluginName != this.getName()) {
            return
        }

        //Below code is to calculate body of original request made
        if (attack.pluginName == this.getName()) {
            if (attack.vector == '') {
                let originbody = _.get(attack, 'result.resp.body', '')
                if (originbody && originbody.length > 50 && !originbody.includes('newArrivalViewed')) {
                    pluginStorage.attackArea = _.get(attack, 'attackArea')
                    pluginStorage.originalstatuscode = _.get(attack, 'result.resp.httpResponse.statusCode')
                    pluginStorage.originalbody = originbody
                    pluginStorage.originaluri = _.get(attack, 'href', '')
                }
                else {
                    pluginStorage.attackArea = ""
                    pluginStorage.originalstatuscode = ""
                    pluginStorage.originalbody = ""
                    pluginStorage.originaluri = ""
                }
                return
            }
        }

        if (!pluginStorage.originalbody) {
            return
        }

        if (pluginStorage.attackArea != _.get(attack, 'attackArea')) {
            return
        }

        let body1 = pluginStorage.originalbody
        let body2 = _.get(attack, 'result.resp.body')

       //Skip for the custom error pages
       const responseBody = body2.toLowerCase();
        
       // Check each category of error messages
       const errorCategories = [
           RegExpVari.ErrorMessages.HttpErrors,
           RegExpVari.ErrorMessages.SecurityErrors,
           RegExpVari.ErrorMessages.SessionErrors,
           RegExpVari.ErrorMessages.SystemErrors,
           RegExpVari.ErrorMessages.WafErrors,
           RegExpVari.ErrorMessages.GeneralErrors
       ];

       // Early return if any error message is found
       for (const category of errorCategories) {
           if (category.some(error => responseBody.includes(error))) {
               return;
           }
       }

       // Check for WAF servers in headers
       let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
       ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()
       
       if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
           return;
       }

        try {
            //proceed further checks only if attack area is one of below
            //Below code is to calculate body and status_code of attack request made

            let similar_percentage = await this.checksimilarity(body1, body2)
            let currentstatuscode = _.get(attack, 'result.resp.httpResponse.statusCode')
            let originalstatuscode = pluginStorage.originalstatuscode
            let initialurl = pluginStorage.originaluri
            let attackurl = _.get(attack, 'httpRequest.uri')
            let staCode = [200]
            let evaluatecondition = staCode.includes(currentstatuscode) && staCode.includes(originalstatuscode) && similar_percentage < 0.80
            // attack response status code must be 200
            // body of original and current request should be different
            if (evaluatecondition) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, {
                    "cgivulnerability": "exists",
                    "initial_uri": initialurl,
                    "attack_uri": attackurl,
                    "similarpercentage": similar_percentage
                });
                //pluginStorage.cgi =true
                return
            }
        }
        catch (e) { return }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["statusCode"]);
    }
}
//vectors
poison_arg = [
    "admin",
    "administrator",
    "debug",
    "developer",
    "hide",
    "source",
    "test"
    //"redirectto" disabling due to FP
];

poison_val = [
    1,
    "on",
    "true",
    "y",
    "yes"
];

//this block of code is an automation to create a list of formated vectors
CGIvectors = [];//Total number of attack vector = 35
for (key of poison_arg) {
    for (value of poison_val) {
        CGIvectors.push('?' + String(key) + '=' + String(value) + '&')
    }
}
CGIvectors.unshift('')
module.exports = CGIAttack