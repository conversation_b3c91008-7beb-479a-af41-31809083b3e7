const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')


class WebCachePoisoning extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.WebCachePoisoning = 'ID-web-cache-poisoning'

    }

    checInTags(attack) {
        let details = []
        let $ = _.get(attack, 'result.resp.httpResponse.cheerio')
        for (let tags of tagsToCheck) {
            let tagElements = $(tags.tag)
            tagElements.each(function (index, el) {
                let val = $(el).attr(tags.attr)
                if (val && (/haikumsg1234/.test(val))) {
                    details.push({
                        name: $(this).attr(tags.attr),
                        dom_element: $.html(this)
                    })
                }
            })
        }
        return details
    }

    getAttackVectors() {
        return _attackVectors
    }

    /** 
     * Only attack header: Referer, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override    
     */
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['X-Forwarded-Host', 'Referer', 'User-Agent', 'Origin', 'Cookie', 'Accept-Language']
        })
    }

    getAttackableEvents() {
        return ['http-headers']
    }

    async performNetworkAttack(attack) {
        let xforwardedhostPresent = _.get(attack, 'result.resp.httpResponse.headers[X-Forwarded-Host]')

        if (!xforwardedhostPresent) {
            attack.httpRequest.headers['X-Forwarded-Host'] = `<a href="http://was.indusface.com/" class='haikumsg1234'>clickme</a>`

            headers_to_add.forEach((obj) => {
                let key = Object.keys(obj)[0]
                let val = obj[key]
                attack.httpRequest.headers[key] = val
            })
        }
        return await super.performNetworkAttack(attack)
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    wantProcessAttackResponse(attack) {
        //checking reflected vector in response
        let rawHtml = _.get(attack, 'result.resp.body')
        if (/haikumsg1234/i.test(rawHtml)) {
            return true
        }

        return false
    }

    async processAttackResponse(attack) {
        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }
        //plugin scope variable 
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if already found dont check further
        if (pluginDataForRequest.WebCachePoisoning == true) {
            return
        }

        //extracting response header to be checked for web cache enabled condition
        let age = _.get(attack, 'result.resp.httpResponse.headers[Age]', '')
        if (age == '') {
            age = _.get(attack, 'result.resp.httpResponse.headers[age]', '')
        }
        try {
            let cacheControlHeaderPresent = _.get(attack, 'result.resp.httpResponse.headers[cache-control]', '')
            let pragmaHeaderPresent = _.get(attack, 'result.resp.httpResponse.headers[pragma]', '')
            let parseHeaders = HaikuUtils.parseHeaderVal(cacheControlHeaderPresent)
            let check_cache_conditions = [
                (!pragmaHeaderPresent) ? true : false,
                (cacheControlHeaderPresent.includes("max-age=")) ? true : false,
                (parseHeaders['max-age'] > this.getMetadata(attack).vulnerabilities['ID-web-cache-poisoning'].maxAgeLimitToVeriy) ? true : false,
                (!cacheControlHeaderPresent.includes('no-cache')) ? true : false,
                (!cacheControlHeaderPresent.includes('no-store')) ? true : false
            ]

            let checker = arr => arr.every(v => v === true);

            if (checker(check_cache_conditions) == false) {
                if (!age) {
                    return
                }
                if (age && age < this.getMetadata(attack).vulnerabilities['ID-web-cache-poisoning'].maxAgeLimitToVeriy) {
                    return
                }
            }

            //checking injection in all tags
            let details = await this.checInTags(attack)

            //if injection found report vulnerability
            if (details.length > 0) {
                this.addVulnerabilitytoResult(attack, this.WebCachePoisoning, details)
                pluginDataForRequest.WebCachePoisoning = true
            }
        } catch (e) { return }
        //cache control header to be used for web cache poisoning
    }
    //Request Header: x-host, X-Forwarded-For, X-Forwarded-Proto, X-Forwarded-Ssl, parameter and it's values. Response Header: Age, cache-control, Response body: haikumsg1234
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.WebCachePoisoning) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["X-Forwarded-Host", "x-host", "X-Forwarded-For", "X-Forwarded-Proto", "X-Forwarded-Ssl"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["statusCode", "Age", "age", "cache-control"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haikumsg1234']);
    }
}

const headers_to_add = [
    { "x-host": `<a href="http://was.indusface.com/" class='haikumsg1234'>clickme</a>` },
    { "X-Forwarded-For": `<a href="http://was.indusface.com/" class='haikumsg1234'>clickme</a>` },
    { "X-Forwarded-Proto": `<a href="http://was.indusface.com/" class='haikumsg1234'>clickme</a>` },
    { "X-Forwarded-Ssl": `<a href="http://was.indusface.com/" class='haikumsg1234'>clickme</a>` }
]
//random_header = headers_to_add[Math.floor(Math.random() * headers_to_add.length)];
/**
 * .... These are the tags and it's attributes where attributes value will be changed after sending the attack. 
 * More can be added in the list as necessary.
 */


const _attackVectors = [
    `<a href="http://was.indusface.com/" class='haikumsg1234'>clickme</a>`,
    `"><a href="http://was.indusface.com/###" class='haikumsg1234'>clickme</a><!--`,
    `"<a href="http://was.indusface.com/###" class='haikumsg1234'>clickme</a><!--`,
    `"></a><a href="http://was.indusface.com/###" class='haikumsg1234'>clickme</a><!--`,
    `<a href="http://was.indusface.com/###" class='haikumsg1234'>clickme</a><!--`,
    `"></a><a href="http://was.indusface.com/###" class='haikumsg1234'>clickme</a><!--`

]

const tagsToCheck = [
    { tag: 'iframe', attr: 'src' },
    { tag: 'iframe', attr: 'class' },
    { tag: 'a', attr: 'class' },
    { tag: 'a', attr: 'href' },
    { tag: 'link', attr: 'href' },
    { tag: 'link', attr: 'class' },
    { tag: 'input', attr: 'value' },
    { tag: 'input', attr: 'class' },
    { tag: 'input', attr: 'id' },
    { tag: 'img', attr: 'src' },
    { tag: 'img', attr: 'class' },
    { tag: 'form', attr: 'action' },
    { tag: 'form', attr: 'class' },
    { tag: 'base', attr: 'href' },
    { tag: 'base', attr: 'class' },
    { tag: 'script', attr: 'src' },
    { tag: 'meta', attr: 'class' },
    { tag: 'meta', attr: 'content' },
    { tag: 'meta', attr: 'id' }
]


module.exports = WebCachePoisoning