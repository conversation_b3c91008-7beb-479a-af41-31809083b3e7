# node
node_modules/
logs
typings/
network\-scan\-test/
crawler*/
struts*/
node/test/
node/tmp/
node/Wappalyzer
node/thedailywtf.js
screenshots/
vulns/
requests/
htmlfiles/
paused/
http-response/
httpResponse/
REST/pause-crawler/
revalidate/
prev_logs/
haiku-store/
debugging/
utils/**/*.json
utils/**/*.png
utils/**/*.gz
utils/tmp/
dedup-analysis/
paused-json/

\.vscode/
\.eslint*

# Go
# Binaries for programs and plugins
*.exe
*.dll
*.so
*.dylib

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# specifc to indusface
Go/src/gryffin/
Go/src/indusface.com/stripe_test
*.csv

# >>>>> for now ignore all of Go for simplicity <<<<<<
Go/
