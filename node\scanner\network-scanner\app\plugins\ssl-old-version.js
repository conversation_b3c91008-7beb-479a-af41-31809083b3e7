const NetworkAttack = require('./network-attack')
const tls = require('tls');
const _ = require('lodash')
class SSLWeakVersion extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-ssl-old-version'
    }

    async processAttackResponse(attack) {
        //Verify below only once per scan
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (attack.attackArea != "original-crawler-request") {
            return
        }

        let parsedUrl = new URL(attack.httpRequest.uri)
        if (parsedUrl.protocol != 'https:') {
            return
        }

        //if vuln detected for a req then return
        if (pluginDataForRequest.sslFound) {// && pluginDataForRequest.oldCiphers && pluginDataForRequest.CBCCiphers) {
            return
        }

        let protocol = ['SSLv2', 'SSLv3', 'TLSv1', 'TLSv1.1']
        // let protocol = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3']
        let certInfo = []
        for (let val of protocol) {
            // Options for the TLS connection
            const options = {
                host: attack.hostname, // Replace with the target host
                // host: 'proxypass.oneiruat.adityabirla.com.indusguard.com', // Replace with the target host
                port: 443, // Standard HTTPS port
                method: 'GET',
                rejectUnauthorized: false, // Allow self-signed certificates
                // You can specify the TLS version here
                minVersion: val,
                servername: attack.hostname,
                // servername: 'proxypass.oneiruat.adityabirla.com.indusguard.com',
                maxVersion: val
            };
            let sslInfo = await this.alldts(options)
            if (sslInfo && sslInfo != 'Not Found') {
                certInfo.push({ result: `Protocol ${sslInfo.Version} is supported. Cipher: ${JSON.stringify(sslInfo.Cipher.standardName)}. Remote Address: ${sslInfo.RemoteAddress} ` })
            }
        }
        if (certInfo.length > 0) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, certInfo)
            pluginDataForRequest.sslFound = true
        }
        else {
            // checked - avoid repeating the check
            pluginDataForRequest.sslFound = true
        }
    }
    alldts(options) {
        return new Promise((resolve) => {
            let tlsInfo = []
            try {
                // Create a TLS connection
                const socket = tls.connect(options);

                socket.on('secureConnect', () => {
                    tlsInfo = {
                        // Certificate: socket.getPeerCertificate(),
                        Version: socket.getProtocol(),
                        Cipher: socket.getCipher(),
                        RemoteAddress: socket.remoteAddress
                    };
                    socket.end(); // Close the connection after gathering information
                    resolve(tlsInfo);
                });

                // Handle errors
                socket.on('error', (err) => {
                    resolve('Not Found');
                });
            }
            catch (e) {
                resolve('Not Found');
            }
        });
    }
}

module.exports = SSLWeakVersion

// let sslInfo = await this.alldts(options)
// let sslInfo = await this.versiondts(options)


/* try {
    //call python script and find vulnerability
    if (parsedUrl.hostname) {
        let mergedCipherList = _.join(strongCipherList, ':')
        let jsonData = {
            "hostName": parsedUrl.hostname,
            "strongCiphers": mergedCipherList
        }
        if (!pluginDataForRequest.sslFound || !pluginDataForRequest.oldCiphers) {
            let output = await this.networkScanner.pythonExtension.executeCommand('sslVersionDetection.py', jsonData)

            //parse output to get vulnerability
            if (!output.err && output.json.length > 0) {
                for (let vulnObj of output.json) {
                    if (!pluginDataForRequest.sslFound && (vulnObj.sslV3Found || vulnObj.tlsV1Found || vulnObj.tlsV11Found)) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnObj)
                        pluginDataForRequest.sslFound = true
                    }
                    if (!pluginDataForRequest.oldCiphers && vulnObj.oldCiphersFound) {
                        this.addVulnerabilitytoResult(attack, this.oldCipherVulnerabilityID, vulnObj)
                        pluginDataForRequest.oldCiphers = true
                    }
                }
            }
        }

        if (!pluginDataForRequest.CBCCiphers) {
            let CBCoutput = await this.networkScanner.pythonExtension.executeCommand('TLSCBCCiphersFound.py', jsonData)

            //Zombie POODLE, GOLDENDOODLE, 0-Length OpenSSL and Sleeping POODLE were published for websites that use CBC
            if (!CBCoutput.err && CBCoutput.json[0].length > 0) {
                for (let vulnObj of CBCoutput.json) {
                    if (vulnObj.CBCCiphersFound) {
                        this.addVulnerabilitytoResult(attack, this.CBCCiphersVulneranility, vulnObj)
                        pluginDataForRequest.CBCCiphers = true
                    }
                }
            }
        }
    }
} catch (e) { return }
}

versiondts(options) {
return new Promise((resolve) => {
    // const supportedProtocols = ['TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3'];
    const supportedProtocols = ['TLSv1.2', 'TLSv1.3'];
    let result = []

    supportedProtocols.forEach(protocol => {
        const req = https.request({ options, secureProtocol: protocol }, (res) => {
            result.push({
                Protocol: `Protocol ${protocol} is supported.`
            })
        });

        req.on('error', (e) => {
            result.push({ Protocol: `Protocol ${protocol} is not supported` });
        });

        req.end();
    });
    if (result.length > 0) {
        resolve(result)
    }
})
} */

/* strongCipherList = [
    'TLS-AES-128-GCM-SHA256',
    'TLS-AES-256-GCM-SHA384',
    'TLS-CHACHA20-POLY1305-SHA256',
    'TLS-AES-128-CCM-SHA256',
    'TLS-AES-128-CCM-8-SHA256',
    'ECDHE-ECDSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-ECDSA-CHACHA20-POLY1305',
    'ECDHE-ECDSA-CHACHA20-POLY1305-SHA256',
    'ECDHE-RSA-CHACHA20-POLY1305',
    'ECDHE-ECDSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-ECDSA-AES256-SHA384',
    'ECDHE-RSA-AES256-SHA384',
    'ECDHE-ECDSA-AES128-SHA256',
    'ECDHE-RSA-AES128-SHA256'] */
