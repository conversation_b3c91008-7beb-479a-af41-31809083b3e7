
<!DOCTYPE html>
<html>
<head>
    <title>CSS Injection Tester</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f2f2f2;
            padding: 20px;
        }
        .box {
            padding: 15px;
            margin-top: 20px;
            border: 2px solid #ccc;
        }
    </style>
    <style>body { color: red; } /*haikutest*/</style>
</head>
<body>
    <h1>CSS Injection Test</h1>
    <form method="GET" action="/test">
        <label for="input">Enter your CSS injection payload:</label><br><br>
        <input type="text" name="input" id="input" style="width: 80%;" value="<style>body { color: red; } /*haikutest*/</style>"><br><br>
        <button type="submit">Test Payload</button>
    </form>

    <div class="box" id="output" style="">
        Target div with inline style
    </div>

    <p class="box" style="">
        Paragraph with inline style
    </p>

    <div class="box">
        <strong>Injected Payload (for debugging):</strong><br>
        <code>&lt;style&gt;body { color: red; } /*haikutest*/&lt;/style&gt;</code>
    </div>
</body>
</html>
