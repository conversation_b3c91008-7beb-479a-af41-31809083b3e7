/**
 * Expand every link item to inlcude all subpaths. Filter plugin will ensure duplicates are removed.
 */
const utils = require('../../ifc-utils.js')
const URL = require('url').URL
const _ = require('lodash')

/**
 * Expand every link item to inlcude all subpaths i.e from http://www.xyz.com/a/b/c?a=b, generate
 * http://www.xyz.com/a/b?a=b
 * http://www.xyz.com/a?a=b
 */
class ExpandLinkToAllSubpaths {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        scanner.on('got-interesting-items', this.getActions.bind(this))
    }

    /**
     * Expand every link item to inlcude all subpaths i.e from http://www.xyz.com/a/b/c?a=b, generate
     * http://www.xyz.com/a/b?a=b
     * http://www.xyz.com/a?a=b
     * @param {object} interestingItems The interstingItems collected by the crawler
     */
    expandSubpathsForLinks(interestingItems) {
        // -- Link items - these are simple, non evented links that are processed using LoadAction --
        let newLinkItems = []
        for( let linkItem of interestingItems.linkItems) {
            let linkFull = new URL(linkItem[1].href, this.config.parsedUrl.href)
            let pathComponents = linkFull.pathname.split('/')
            for( let i = 1; i < pathComponents.length -1; i++) {
                linkFull.pathname = pathComponents.slice(1,i+1).join('/')
                let newLinkItem = _.cloneDeep(linkItem)
                newLinkItem[1].href = linkFull.href
                newLinkItems.push(newLinkItem)
            }
        }

        if (this.config.subpathConfig.enabled && this.config.subpathConfig.subPathLevel > 0) {
            //from clickableItems repeat the same process
            for (let clickableItem of interestingItems.clickableItems) {
                try {
                    if (clickableItem[1].hrefBtn == 'null') {
                        continue;
                    }
                    let linkFull = new URL(clickableItem[1].hrefBtn, this.config.parsedUrl.href)
                    if (linkFull.pathname) {
                        //write code exclude filename from linkFull.pathname
                        let pathComponents = linkFull.pathname.split('/').slice(0, -1);
                        for (let i = 1; i <= this.config.subpathConfig.subPathLevel; i++) {
                            if (pathComponents.length <= i) {
                                break
                            };
                            linkFull.pathname = pathComponents.slice(1, i + 1).join('/')
                            let newLinkItem = _.cloneDeep(clickableItem)
                            newLinkItem[1].href = linkFull.href
                            //for href is already added to any interestingItems.linkItems item dont add it again
                            let isPresent = interestingItems.linkItems.filter((item) => {
                                return item[1].href === newLinkItem[1].href
                            });
                            if (isPresent.length === 0) {
                                newLinkItems.push(newLinkItem)
                            }
                        }
                    }
                } catch (error) {
                    console.log("subpath inclusion error : clickableItem hrefBtn")
                }
            }

            //repeat above just like clickableItems for scriptItems
            for (let scriptItem of interestingItems.scriptItems) {
                try {
                    if (scriptItem[1].src == 'null') {
                        continue;
                    }
                    let linkFull = new URL(scriptItem[1].src, this.config.parsedUrl.href)
                    if (linkFull.pathname) {
                        let pathComponents = linkFull.pathname.split('/').slice(0, -1);
                        for (let i = 1; i <= this.config.subpathConfig.subPathLevel; i++) {
                            if (pathComponents.length <= i) {
                                break
                            };
                            linkFull.pathname = pathComponents.slice(1, i + 1).join('/')
                            let newLinkItem = _.cloneDeep(scriptItem)
                            newLinkItem[1].href = linkFull.href
                            //for href is already added to any interestingItems.linkItems item dont add it again
                            let isPresent = interestingItems.linkItems.filter((item) => {
                                return item[1].href === newLinkItem[1].href
                            });
                            if (isPresent.length === 0) {
                                newLinkItems.push(newLinkItem)
                            }
                        }
                    }
                } catch (error) {
                    console.log("subpath inclusion error:scriptItems")
                }
            }

        }

        interestingItems.linkItems = interestingItems.linkItems.concat(newLinkItems)
    }

    /**
     * Called when interesting items have been found by browser after an action taken by the crawler.
     * @param {object} ret control crawl state, this plugin does not modify ret
     * @param {object} interestingItems The interstingItems collected by the crawler
     */
    getActions(ret, interestingItems) {
        // previous plugin already decided to skip state or specifically asked to stop processing more plugins => nothing to do
        if (ret.skipState) {
            return
        }

        this.expandSubpathsForLinks(interestingItems)
    }
}

module.exports = ExpandLinkToAllSubpaths