const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// load url action
class LoadAction {
    constructor(xpath, url, annotation = '') {
        this.xpath = xpath
        this.url = url
        this.annotation = annotation
    }

    get actionType() {
        return 'load'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.url, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        try {
            await browser.webContents.loadURL(this.url)
        }
        catch(e) {
            console.error(e.message);
        }
        return true
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} ${this.url} from ${this.xpath}, ${this.annotation}`
    }
}

module.exports = LoadAction