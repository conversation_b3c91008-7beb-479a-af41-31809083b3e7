const RpcQueueMessage = require('../rpc-queue-message')
const path = require('path')
const debug = require('debug')('Messages:CrawlStartedRpc')

/** 
 * crawler -> scanner indicating crawl has started
 * @extends QueueMessage
*/
class CrawlStartedRpc extends RpcQueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} crawlStartedRpcMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request
     * @property {string} mainUrl Full URI that is the root of the scan/crawl
     * @property {Boolean} resumeScan true if t's resuming a scan, false if its a completely new scan
     * @property {Number} maxCrawlTimeMins The sender will run for a maximum of this time (in minutes)
     * @property {string} restrictCrawlToPath only URIs matching path will be scanned
     * @property {Array} excludeUrls Array of URI patterns that will not be attacked 
     * @property {string} scannerMachineUID Unique ID of the machine that sent this request
     */
    /**
     * @param {crawlStartedRpcMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner';
        this.routingKey = 'lb.request.rpc.crawl-started';
        this.msgType = CrawlStartedRpc.msgType;

        // if we have a machineUID, we can send a direct message to that machine. i.e. crawler > scanner
        if(content && content.scannerMachineUID) {
            this.routingKey = `direct.message.${content.scannerMachineUID}.rpc.crawl-started`
        }
    }
}

module.exports = CrawlStartedRpc