const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Server Side Template Injection Plugin Strategy:
 * Here we will inject various template engine payloads in all request for uri query params and 
 * form encodedpost, and if received expected response in return then will mark it as vulnerable
 */

class serverSideTemplateInjection extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-serverside-template-injection'

        /* //Code to repeat a particular value and check it in response later
        this.repeatVal = "99".repeat(15) */
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return templatePayloads
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        //Return of attack not made by this plugin
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')

        //if one attack already made then don't attack further
        if (pluginStorage.ssTI) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, "result.resp.body")

        // report only when response code is 200 ok and we receive certain strings in response
        // then only report the vuln

        if (statusCode === "200") {
            if (/concathaiku/.test(body)) {
                return;
            }
            // Report only when response code is 200 OK and we receive certain strings in the response
            if (/haiku(?:_?1,?485|(?:99){15})/i.test(body)) { // TO-9002
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href);
                pluginStorage.ssTI = true;
                return;
            }
        }
    }

    //Parameter and it's values(attack vector) and  haiku_1485 or haiku_1,485 or 99(15times)in response body.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haiku_1485', 'haiku_1,485', this.repeatVal]);
    }
}

const templatePayloads = [
    `haiku{{99*15}}`, //CS Team
    `{{'haiku_' ~ 99*15}}`, //returns haiku_1485 with twig/java/pebble (without $ interpreter)
    `\${'haiku_'+99*15}`, //returns haiku_1485/haiku_1,485 with java/smarty/freemarker
    `\${{'haiku_' ~ 99*15}}`, //returns haiku_1485 with twig (including $ at start)
    `{{'haiku_'+99*15}}`, //for pebble it will return haiku_1485/
    `haiku{{99*'15'}}`, // will print 99 - 15 times in jinja 2
    `#set($a ='haiku_')#set($b=99*15) $a$b`, //for velocity, should return haiku_1485
    `#{'haiku_'+99*15}`, //returns haiku_1485 with smarty
    `<%= haiku_ + 99 * 15 %>`,// for ruby, returns haiku_1485
    `<% x=99*15%> haiku_\${x}`, //for mako - should return haiku_1485 in response
    `'haiku_'+$eval('99*15')`, //for angular js - also the first one would work here, return 1485
    // `99*15`, //for jade/codepen - should return 1485,
    `<style><j:jelly xmlns:j="jelly" xmlns:g='glide'><g:evaluate>s="";s=s.concat("haiku_","1485");gs.addErrorMessage(s);</g:evaluate></j:jelly></style>`,
]

module.exports = serverSideTemplateInjection