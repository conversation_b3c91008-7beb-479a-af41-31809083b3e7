const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Plugin to detect exposure of sensitive filenames in responses
 * Functions are organized in the following groups:
 * 1. Setup & Initialization - Constructor and pattern initialization
 * 2. Response Processing - Handle response and extract filenames
 * 3. Filename Detection - Validate and clean filenames
 * 4. Result Handling - Categorize and report findings
 */
class ExposureofSensFiles extends NetworkAttack {
    // ==========================================
    // 1. Setup & Initialization
    // ==========================================

    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-exposure-of-sensitive-filenames'
        this.initializePatterns()
        this._validationCache = new Map()
        this._validationCacheSize = 1000
    }

    initializePatterns() {
        // Pre-compile patterns for better performance
        this.filenamePattern = /\b[\w\-\.]+\.[\w\-\.]+(?![\w\. ]*?[=\(\)\{\}\[\]]|[^<]*>)/g

        // Initialize false positive patterns
        this.initializeFPPatterns()
    }

    initializeFPPatterns() {
        this.commonFPPatterns = [
            /\.(?:css|js|png|jpg|jpeg|gif|ico|svg|woff2?|ttf|eot)$/i,
            /(?:readme|changelog|license|contributing|authors)\.(?:md|txt|html?)$/i,
            /(?:package|composer|gemfile|cargo|requirements)\.(?:json|lock|txt|toml)$/i,
            /\.example$/i,
            /(?:test|spec|mock|fixture)\.(?:js|py|php|rb|java|cs|ts)$/i,
            /\.(?:gitignore|gitattributes|gitmodules)$/i,
            /(?:webpack|babel|tsconfig|eslint|prettier)\.(?:config|rc)\.?(?:js|json|yml)?$/i,
            /\.(?:php|aspx?|jsp|do|action)$/i,
            /\.[a-f0-9]{8,}\.(?:js|css|map)$/i,
            /\.(?:bundle|chunk|vendor|main|app|runtime)\.[a-z0-9]+\.(?:js|css)$/i
        ].map(pattern => ({
            pattern,
            test: (str) => pattern.test(str)
        }))
    }

    // ==========================================
    // 2. Response Processing - Handle response and extract filenames
    // ==========================================

    processAttackResponse(attack) {
        // Early exit conditions to reduce processing load
        if (this.getPluginScopedStore(attack).sensitiveFileFound) return;

        const responseBody = _.get(attack, 'result.resp.body', '').toString().trim()
        if (!responseBody || responseBody.length < 3) return;

        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        const contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '').toLowerCase()
        const contentLength = parseInt(_.get(attack, 'result.resp.httpResponse.headers["content-length"]', responseBody.length))

        // Quick validation before heavy processing
        if (this.shouldSkipProcessing(attack.href, statusCode, contentType, contentLength)) {
            return;
        }

        // Check for "not found" messages in 200 responses
        if (statusCode === 200) {
            const lowerBody = responseBody.toLowerCase();
            // Use cached patterns for better performance
            if (ExposureofSensFiles.notFoundPatterns.some(({ test }) => test(lowerBody))) {
                return;
            }
        }

        // Process in chunks for large responses
        const href = attack.href
        const matches = this.processResponseInChunks(responseBody, href)
        if (matches.length > 0) {
            this.processMatches(matches, attack)
        }
    }

    shouldSkipProcessing(url, statusCode, contentType, contentLength) {
        // Quick checks first
        if (!this.isValidResponse(statusCode, contentType, contentLength)) {
            return true;
        }

        // Skip if URL contains /docs
        if (url.toLowerCase().includes('/docs')) {
            return true;
        }

        // URL validation - important for reducing false positives/negatives
        const urlParts = url.split('/');
        const lastPart = urlParts[urlParts.length - 1];

        // If URL contains sensitive patterns, don't skip
        if (SENSITIVE_FILE_PATTERNS.HIGH_RISK.some(pattern => pattern.test(lastPart)) ||
            SENSITIVE_FILE_PATTERNS.MEDIUM_RISK.some(pattern => pattern.test(lastPart))) {
            return false;
        }

        // Skip if URL matches exclude pattern and doesn't contain sensitive directories
        if (excludeURLRegex.test(url) && !url.match(/\/(?:config|admin|secure|private)\//i)) {
            return true;
        }

        return false;
    }

    isValidResponse(statusCode, contentType, contentLength) {
        // Basic response validation
        return !(
            [404, 301, 302, 303, 307, 308].includes(statusCode) ||
            this.isIrrelevantContentType(contentType) ||
            contentLength > 5000000 ||
            contentLength < 10
        );
    }

    processResponseInChunks(responseBody, href) {
        try {
            const CHUNK_SIZE = 50000 // Process 50KB at a time
            const matches = new Set()
            const seenFilenames = new Set()

            // Process large responses in chunks to avoid memory spikes
            for (let i = 0; i < responseBody.length; i += CHUNK_SIZE) {
                const chunk = responseBody.slice(i, i + CHUNK_SIZE)
                const chunkMatches = chunk.match(this.filenamePattern) || []

                for (const match of chunkMatches) {
                    const cleanedFilename = this.cleanFilename(match)
                    if (!cleanedFilename || seenFilenames.has(cleanedFilename.toLowerCase()) || href.includes(cleanedFilename.toLowerCase())) continue

                    if (this.isValidFilename(cleanedFilename)) {
                        seenFilenames.add(cleanedFilename.toLowerCase())
                        matches.add(cleanedFilename)
                    }

                    // Early exit if we have too many matches
                    if (matches.size > 50) {
                        break
                    }
                }
            }

            return Array.from(matches).map(filename => ({ filename }))
        } catch (error) {
            console.error('Error in processResponseInChunks:', error)
            return [] // Fail safe - return empty array on error
        }
    }

    isValidFilename(filename) {
        try {
            // Cache common validation results
            const cacheKey = `validation_${filename}`

            // Check cache
            if (this._validationCache && this._validationCache.has(cacheKey)) {
                return this._validationCache.get(cacheKey)
            }

            const result = this._validateFilename(filename)

            // Update cache
            if (this._validationCache.size >= this._validationCacheSize) {
                const firstKey = this._validationCache.keys().next().value
                this._validationCache.delete(firstKey)
            }
            this._validationCache.set(cacheKey, result)

            return result
        } catch (error) {
            console.error('Error in isValidFilename:', error)
            return false // Fail safe - reject on error
        }
    }

    _validateFilename(filename) {
        if (!filename || filename.length < 3 || filename.length > 255) return false;

        const lowerFilename = filename.toLowerCase();

        // Quick rejections first - most common cases
        if (NUMBER_ONLY_FILENAME.test(filename) ||
            SUSPICIOUS_CHARS.test(filename) ||
            DYNAMIC_EXTENSIONS.test(filename) ||
            BASE64_PATTERN.test(filename) ||
            VERSION_PATTERN.test(filename) ||
            MINIFIED_FILE.test(filename) ||
            !VALID_EXTENSION.test(filename)) {
            return false;
        }

        // Check for common false positives in one pass
        if (/^(?:metadata|data|field|property|attribute|XMP|EXIF|IPTC|DublinCore|schema|en_US|en_GB|locale|language|label|value|type|format|description)\./i.test(filename) ||
            /\.(?:metadata|data|field|property|attribute|XMP|EXIF|IPTC|DublinCore|schema|en_US|en_GB|locale|language|label|value|type|format|description)$/i.test(filename) ||
            /^(?:evt|event)\.(?:key|keyCode|charCode|which|target|currentTarget|type)$/i.test(filename) ||
            /^(?:document|window|navigator|location|history|screen)\./i.test(filename) ||
            /^(?:on(?:load|click|submit|change|input|keydown|keyup|keypress|mouseover|mouseout|focus|blur))$/i.test(filename) ||
            /^(?:webpack|babel|eslint|prettier|jest|karma|mocha|chai|sinon)\./i.test(filename) ||
            /^(?:package|composer|gemfile|cargo|requirements)\./i.test(filename) ||
            /^(?:readme|changelog|license|contributing|authors)\./i.test(filename) ||
            /\.(?:css|js|jpg|jpeg|png|gif|ico|svg|woff2?|ttf|eot|map)$/i.test(filename)) {
            return false;
        }

        // Check high-risk patterns first for quick acceptance
        if (SENSITIVE_FILE_PATTERNS.HIGH_RISK.some(pattern => pattern.test(filename))) {
            return true;
        }

        // Check medium-risk patterns next
        if (SENSITIVE_FILE_PATTERNS.MEDIUM_RISK.some(pattern => pattern.test(filename))) {
            return true;
        }

        // Check against false positive patterns
        if (FALSE_POSITIVE_PATTERNS.some(pattern => pattern.test(filename))) {
            return false;
        }

        // Check if filename contains any false positive terms
        if (Array.from(FALSE_POSITIVE_TERMS).some(term => lowerFilename.includes(term))) {
            // Exception: if it's in a sensitive path or has sensitive terms, don't exclude it
            if (lowerFilename.includes('config/') ||
                lowerFilename.includes('admin/') ||
                lowerFilename.includes('secure/') ||
                lowerFilename.includes('private/')) {
                // Continue checking other conditions
            } else {
                return false;
            }
        }

        // Check false positives from commonFPPatterns
        if (this.commonFPPatterns.some(({ test }) => test(filename))) {
            return false;
        }

        // Use Set.has() for better performance than Array.includes()
        return Array.from(SENSITIVE_TERMS).some(term => lowerFilename.includes(term));
    }

    processMatches(matches, attack) {
        if (matches.length > 0) {
            const findings = this.categorizeFindings(matches)
            if (Object.entries(findings).length === 0) return;

            // Format findings with limited entries per category
            const formattedFindings = Object.entries(findings).map(([category, files]) => {
                const totalFiles = files.length;
                const limitedFiles = files.slice(0, 4);
                const fileList = limitedFiles.map(file => file.filename).join(", ");
                return `${category}: ${fileList}${totalFiles > 4 ? ` (and ${totalFiles - 4} more)` : ''}`;
            }).join("; ");

            if (formattedFindings.length === 0) return;

            this.addVulnerabilitytoResult(
                attack,
                this.vulnerabilityID,
                `Details: Sensitive files exposed in response: ${formattedFindings}\n riskLevel: ${this.determineRiskLevel(matches)}`
            );
            this.getPluginScopedStore(attack).sensitiveFileFound = true;
        }
    }

    determineRiskLevel(matches) {
        // If any file matches HIGH_RISK patterns, it's HIGH risk
        if (matches.some(m => SENSITIVE_FILE_PATTERNS.HIGH_RISK.some(pattern => pattern.test(m.filename)))) {
            return 'HIGH'
        }
        // If any file matches MEDIUM_RISK patterns, it's MEDIUM risk
        if (matches.some(m => SENSITIVE_FILE_PATTERNS.MEDIUM_RISK.some(pattern => pattern.test(m.filename)))) {
            return 'MEDIUM'
        }
        // Otherwise it's LOW risk (matched by SENSITIVE_TERMS)
        return 'LOW'
    }

    // ==========================================
    // 3. Filename Detection - Validate and clean filenames
    // ==========================================
    isCommonWebFile(filename) {
        return COMMON_WEB_EXTENSIONS.test(filename) ||
            COMMON_ASSET_NAMES.test(filename) ||
            COMMON_QUERY_PARAMS.test(filename)
    }

    cleanFilename(filename) {
        if (!filename) return null

        let cleaned = filename.trim()
            .replace(/^[./\\]+/, '')     // Remove leading slashes/dots
            .replace(/["'`;]+/g, '')     // Remove quotes and other unwanted chars

        if (cleaned.includes('/') || cleaned.includes('\\')) {
            const parts = cleaned.split(/[/\\]/)
            cleaned = parts[parts.length - 1]
        }

        return cleaned || null
    }

    // ==========================================
    // 4. Result Handling - Categorize and report findings
    // ==========================================

    categorizeFindings(matches) {
        // Use Map for better performance with large datasets
        const findings = new Map();
        const categories = this._getCategories();

        for (const match of matches) {
            for (const [category, pattern] of categories) {
                if (pattern.test(match.filename)) {
                    if (!findings.has(category)) {
                        findings.set(category, []);
                    }
                    findings.get(category).push(match);
                    break;
                }
            }
        }

        // Convert Map back to object for compatibility
        return Object.fromEntries(findings);
    }

    _getCategories() {
        // Cache categories for reuse
        if (!this._categoriesCache) {
            this._categoriesCache = new Map([
                ['Environment & Config', /\.(?:env|config|cfg|ini|properties|settings|conf|ya?ml|toml|json)$/i],
                ['Backup & Temp', /\.(?:bak|backup|old|orig|save|tmp|temp|swp|swpx?|gz|tar|zip|7z|rar)$/i],
                ['Logs', /\.logs?(?:\b|_)/i],
                ['Database', /\.(?:sql|sqlite[3]?|db|mdb|accdb|oracle|mysql|pgsql|mongodb|redis|dump)$/i],
                ['Certificates & Keys', /\.(?:crt|pem|cer|der|p12|pfx|key|keystore|jks|truststore|csr|pub)$/i],
                ['Web Server Config', /(?:htaccess|htpasswd|web\.config|nginx\.conf|apache2?\.conf)$/i],
                ['Application Config', /(?:config(?:\.inc)?\.php|settings\.php|database\.yml|credentials\.json|secrets\.yml|config.*\.php)$/i],
                ['Development', /\.(?:vscode|project|classpath|metadata|iml|sln|suo|git)$/i],
                ['Cloud & Infrastructure', /(?:terraform|\.aws|gcloud|azure|\.kube|\.ssh|\.pem|\.key)$/i],
                ['Source Code', /(?:install|setup|shell|script|phpinfo)\.(?:php|py|js|java|cs|cpp|h|rb|go|rs|ts|jsx|tsx)$/i]
            ]);
        }
        return this._categoriesCache;
    }

    isIrrelevantContentType(contentType) {
        const skipTypes = [
            'image/', 'video/', 'audio/',
            'application/octet-stream',
            'application/pdf',
            'application/zip',
            'application/x-compressed',
            'application/x-gzip',
            'application/x-tar',
            'font/',
            'application/x-font',
            'application/x-woff',
            'application/x-ttf',
            'application/vnd.ms-fontobject',
            'binary/',
            'application/x-binary'
        ]
        return skipTypes.some(type => contentType.includes(type))
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID)
        if (vulnID === this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', 'param', [attack.href])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', 'param', [attack.href])
        }
    }

    // Cache the not found patterns for better performance
    static get notFoundPatterns() {
        if (!this._notFoundPatterns) {
            this._notFoundPatterns = [
                /file not found/i,
                /does not exist/i,
                /no such file/i,
                /404 not found/i,
                /cannot find/i,
                /could not find/i,
                /not available/i,
                /no longer exists/i,
                /has been removed/i,
                /page not found/i,
                /resource not found/i
            ].map(pattern => ({
                pattern,
                test: (str) => pattern.test(str)
            }));
        }
        return this._notFoundPatterns;
    }
}

// Pre-compile all regex patterns at module level for better performance
const COMMON_WEB_EXTENSIONS = /\.(?:html?|php|aspx?|jsp|jspx|do|action|cfm)$/i
const COMMON_ASSET_NAMES = /(?:index|default|home|main|app|bundle)\.[a-z]+$/i
const COMMON_QUERY_PARAMS = /\?.*(?:v|version|t|timestamp|_)=[0-9]+/i
const VALID_EXTENSION = /\.[a-zA-Z0-9]{2,6}$/i
const NUMBER_ONLY_FILENAME = /^\d+\.[a-zA-Z0-9]+$/
const SUSPICIOUS_CHARS = /[<>'"`;]/
const DYNAMIC_EXTENSIONS = /\.(?:php|asp|aspx|jsp)\?/i
const BASE64_PATTERN = /^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/
const VERSION_PATTERN = /v?\d+\.\d+\.\d+/
const MINIFIED_FILE = /\.min\.[a-zA-Z0-9]+$/
const excludeURLRegex = /\.(?:js|css|jpg|png|gif|ico|svg|woff|ttf|eot|map)$|\/(?:blog\/|learning\/|node_modules\/|vendor\/(?!config)|assets\/(?!config)|static\/(?!config))/i

// Cache common terms
const SENSITIVE_TERMS = new Set([
    'config', 'secret', 'key', 'password', 'credential', 'token', 'auth', 'private', 'sensitive', 'secure',
    'database', 'admin', 'root', 'user', 'login', 'oauth', 'cert', 'ssh', 'ftp', 'ldap', 'db', 'sql',
    'backup', 'dump', 'prod', 'production', 'staging', 'dev', 'development', 'test'
])

const FALSE_POSITIVE_TERMS = new Set([
    'example', 'sample', 'template', 'demo', 'test', 'mock', 'dummy', 'fake', 'copy', 'backup',
    'public', 'docs', 'static', 'assets', 'dist', 'build', 'vendor', 'node_modules'
])

// Sensitive file patterns - grouped by risk level
const SENSITIVE_FILE_PATTERNS = {
    HIGH_RISK: [
        // Core PHP Configuration Files
        /^php(?:\.ini|\d*\.ini|-?fpm\.conf)$/i,
        /^php-fpm\.d\/.*\.conf$/i,
        /^(?:php|fpm)\.conf$/i,
        /^php\.ini-(?:development|production)$/i,
        // Exact match for config.inc.php
        /^config\.inc\.php$/i,
        // PHP Config and Include files
        /^config\.(?:php|inc\.php|ini|json|xml|ya?ml|cfg|properties|config.*\.php)$/i,
        /^(?:config|conf|settings|setup)\.(?:php|inc\.php)$/i,
        /^\.(?:env|env\.[a-zA-Z0-9]+|htaccess|htpasswd)$/i,
        // PHP Include files with sensitive names
        /^(?:config|auth|db|connect|database|settings|functions|global)\.(?:php|inc\.php)$/i,
        // PHP Framework Config Files
        /^(?:wp-config|local|database|db-config|parameters|credentials|secrets)\.php$/i,
        /^(?:configuration|bootstrap|autoload|init)\.(?:php|inc\.php)$/i,
        // Database connection files
        /^(?:db|database|mysql|mysqli|pdo|oracle|pgsql)(?:config|connect|settings)?\.(?:php|inc\.php)$/i,
        /^my\.cnf$/i,
        /^mysqld?\.conf$/i,
        /^pg_hba\.conf$/i,
        /^postgresql\.conf$/i,
        /^mongod?\.conf$/i,
        /^redis\.conf$/i,
        // Authentication/Authorization
        /^(?:auth|login|admin|users?|acl|rbac|permissions|roles|session)\.(?:php|inc\.php)$/i,
        // Security related
        /^(?:security|encryption|crypto|salt|token|oauth|saml|ldap)\.(?:php|inc\.php)$/i,
        // API and Service configs
        /^(?:api|service|endpoint|gateway)(?:config|settings)?\.(?:php|inc\.php)$/i,
        // Critical certificate and key files - more specific patterns
        /^(?:private|secret|internal|root|ca|server|client)\.(?:key|pem|crt|csr|p12|pfx|jks|keystore|truststore|cer|der|priv|pub)$/i,
        /^(?:ssl|tls|https|cert|certificate|key)\.(?:key|pem|crt|csr|p12|pfx|jks|keystore|truststore|cer|der|priv|pub)$/i,
        /^id_(?:rsa|dsa|ed25519|ecdsa)(?:\.pub)?$/i,
        // Sensitive data
        /^(?:password|secret|credential|token)s?\.(?:php|txt|json|xml|ya?ml)$/i,
        // Backup files
        /^.*\.(?:bak|backup|old|orig|save|swp|copy|tmp|temp)$/i,
        // PHP error and debug files
        /^(?:error|debug|dev|development)(?:config|settings)?\.(?:php|inc\.php)$/i,
        // Framework specific
        /^(?:symfony|laravel|codeigniter|yii|zend|cake|slim)(?:config|settings)?\.(?:php|inc\.php)$/i,
        // Web Server Configuration
        /^(?:apache2?|httpd|nginx)\.conf$/i,
        /^sites?-(?:available|enabled)\/.*\.conf$/i,
        /^(?:apache2?|nginx|httpd)\/conf\.d\/.*\.conf$/i,
        // System Configuration
        /^passwd$/i,
        /^shadow$/i,
        /^group$/i,
        /^hosts$/i,
        /^resolv\.conf$/i,
        /^authorized_keys$/i,
        /^known_hosts$/i,
        /^sudoers$/i,
        /^crontab$/i,
        // Docker/Container files
        /^docker-compose\.ya?ml$/i,
        /^Dockerfile$/i,
        /^\.dockerignore$/i,
        /^docker-compose\.override\.ya?ml$/i
    ],
    MEDIUM_RISK: [
        // Development files
        /^(?:phpinfo|test|install|setup)\.php$/i,
        // Log files
        /^.*\.(?:log|logs)$/i,
        // Debug files
        /^(?:error|debug|trace)\.(?:log|txt|php)$/i,
        // PHP Test/Dev files
        /^(?:test|dev|stage|staging).*\.(?:php|inc\.php)$/i,
        // Additional PHP includes
        /^(?:header|footer|sidebar|template|theme)\.(?:php|inc\.php)$/i,
        // Development Configuration
        /^\.(?:env|env\..+)\.(?:dev|development|local|test|example)$/i,
        /^(?:dev|development|test|staging)\.(?:config|settings|properties)$/i,
        // Less critical certificate and key files
        /^(?:public|test|demo|example|sample)\.(?:key|pem|crt|csr|p12|pfx|jks|keystore|truststore|cer|der|priv|pub)$/i,
        /^(?:dev|development|staging|test)\.(?:key|pem|crt|csr|p12|pfx|jks|keystore|truststore|cer|der|priv|pub)$/i
    ]
};

// Common false positive patterns - more comprehensive
const FALSE_POSITIVE_PATTERNS = [
    // Common web assets
    /\.(?:css|js|jpg|jpeg|png|gif|ico|svg|woff2?|ttf|eot|map)$/i,
    // Public documentation
    /(?:readme|changelog|license|contributing|authors)\.(?:md|txt|html?)$/i,
    // Package management
    /(?:package|composer|gemfile|cargo|requirements)\.(?:json|lock|txt|toml)$/i,
    // Example files
    /(?:example|sample|demo|test)\..*$/i,
    // Build files
    /(?:webpack|babel|tsconfig|eslint|prettier)\.(?:config|rc)\.?(?:js|json|yml)?$/i,
    // Generated files
    /\.[a-f0-9]{8,}\.(?:js|css|map)$/i,
    /\.(?:bundle|chunk|vendor|main|app|runtime)\.[a-z0-9]+\.(?:js|css)$/i,
    /(?:console.log)/i,
    // JavaScript event handling patterns
    /^(?:evt|event)\.(?:key|keyCode|charCode|which|target|currentTarget|type)$/i,
    /^(?:document|window|navigator|location|history|screen)\./i,
    /^(?:addEventListener|removeEventListener|dispatchEvent)$/i,
    /^(?:on(?:load|click|submit|change|input|keydown|keyup|keypress|mouseover|mouseout|focus|blur))$/i,
    // Metadata and JSON field patterns
    /^(?:metadata|data|field|property|attribute)\./i,
    /\.(?:metadata|data|field|property|attribute)$/i,
    /^(?:XMP|EXIF|IPTC|DublinCore|schema)\./i,
    /\.(?:XMP|EXIF|IPTC|DublinCore|schema)$/i,
    /^(?:en_US|en_GB|locale|language)\./i,
    /\.(?:en_US|en_GB|locale|language)$/i,
    /^(?:label|value|type|format|description)\./i,
    /\.(?:label|value|type|format|description)$/i
];

module.exports = ExposureofSensFiles 