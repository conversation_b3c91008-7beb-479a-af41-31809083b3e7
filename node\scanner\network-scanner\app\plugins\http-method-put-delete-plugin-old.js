const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

// checks for PUT method first, then checks for DELETE method, if found reports both vulnerabilities separately
class PutDeleteMethodPlugin extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        this.postData = "DSWA" + HaikuUtils.getRandomInt(10000, 99999)
        this.matchRegex = new RE2(this.postData)
    }

    getAttackVectors() {
        return httpAttackVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * forms attack request and performs network attack
     * @param {attack} new attack received from crawler
     * @override
     */
    async performNetworkAttack(attack) {
        this.url = attack.httpRequest.uri + "/DSWAPutTest.txt";
        attack.httpRequest.uri = this.url;

        if(attack.httpRequest.method == "PUT" || attack.httpRequest.method == "DELETE"){
            attack.httpRequest.body = this.postData
        }
        return await super.performNetworkAttack(attack)
    }

    /**
     * checks for next condition in PUT request attack response
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        //here result for vulnerability in each case
        
        let body = _.get(attack, 'result.resp.httpResponse.body');

        if (attack.result.resp.httpResponse.statusCode == 200) {
            this.checkBodyForVuln(attack, this.matchRegex, 'ID-put-method-enabled')
        }

        if(!body.includes(this.postData)) {
            let vuln = {
                details: "File deleted from web server :" + this.url,
            }
            this.addVulnerabilitytoResult(attack, 'ID-delete-method-enabled', vuln)
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != 'ID-delete-method-enabled') { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['DSWA']); 
    }
}

const httpAttackVectors = [
    `PUT`,
    `GET`,
    `DELETE`
]

module.exports = PutDeleteMethodPlugin