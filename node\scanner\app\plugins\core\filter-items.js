let utils = require('../../ifc-utils.js')
const URL = require('url').URL
const logger = require('../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')

const pluginName = 'filterItems'

class FilterItems {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        this.seenLinkItems = new Set()
        this.seenFormItems = new Set()

        scanner.on('scan-start', this.onScanStart.bind(this))
        scanner.on('got-interesting-items', this.getActions.bind(this))
        scanner.on('serialize-state', this.serializeState.bind(this))
        scanner.on('deserialize-state', this.deserializeState.bind(this))
    }

    /**
     * return object that can be JSON.stringified and stored
     * @param {Object} pluginData Object where we can add our serialized state under our own key
     */
    serializeState(pluginData) {
        pluginData[pluginName] = {
            seenLinkItems: Array.from(this.seenLinkItems),
            seenFormItems: Array.from(this.seenFormItems)
        }
    }

    /**
     * Restore plugin data that from serialized object
     * @param {Object} pluginData Object with our serialized state under our own key
     */
    deserializeState(pluginData) {
        if (!pluginData[pluginName]) {
            return
        }

        // add to sets
        if (pluginData[pluginName].seenLinkItems) {
            for (let el of pluginData[pluginName].seenLinkItems) {
                this.seenLinkItems.add(el)
            }
        }

        if (pluginData[pluginName].seenFormItems) {
            for (let el of pluginData[pluginName].seenFormItems) {
                this.seenFormItems.add(el)
            }
        }
    }

    /**
     * Main thing this does is to add the initial crawl action URLs to set of
     * seen URIs so that we dont process them again during crawl
     * @param {config} config Scanner config & runtime data store
     */
    onScanStart(config) {
        // find all load actions
        for (let initialGuide of config.initialWizardGuides) {
            // find all the 'load' actions
            let loadActions = initialGuide.actions.filter(x => x.action == 'load')
            for (let loadAction of loadActions) {
                if (loadAction.uri) {
                    this.seenLinkItems.add(utils.canonicalizeUrl(loadAction.uri, this.config.parsedUrl.href))
                }
            }
        }
    }

    /**
     * See if we should ignore this link/href eg. downloads, unsupported protocols etc.
     * @param {string} href href to check
     */
    shouldSkipLink(href) {
        // links that could download files
        let extsToSkip = /\.(?:avi|bmp|css|(?:doc|dot|xls|xlt|ppt|pot)(?:[xmb])?|flv|gif|ico|jpg|js|mp3|mpeg|pdf|png|pps|swf|txt|wmv|xml|zip|ttf|otf|svg|eot|woff|woff2)(?:$|\?)/
        // unsupported protocols like tel:
        let protocolsToSkipInHref = /^\s*(?:ftp|mailto|file|tel|callto|chrome):/i
        // links that we dont want to attack eg. captcha
        let nonAttackableLinks = /captcha/i

        this.scanner.emit('found-resource', href);
        return protocolsToSkipInHref.test(href) || extsToSkip.test(href) || nonAttackableLinks.test(href)
    }

    isAllowedAction(href) {
        if (!href || href == 'null' || href == 'undefined') {
            return true
        }
        
        this.scanner.emit('found-resource', href);
        return /^\s*javascript/i.test(href) || this.scanner.allowedToCrawlUrl(href)
    }

    //Add function to filter out items which has same type of pattern as xpath
    //Example: //*[@id="prefix-1"], //*[@id="prefix-2"], allow only one of them
    //But accept all unique ones like //[@id="saved_query"] & //[@id="location"]
    filterOutSimilarXpath(item, interestingItems) {        
        let xpath = item[0];
        if (xpath) {
            // Match patterns like //*[@id="<any_prefix>-<number>"] or //*[@id="<any_prefix><number>"]
            // Optional symbols can be included in the prefix
            let xpathPattern = /\*\[@id="([a-zA-Z0-9_]*[^\d]*)(\d+)(.*)"\]/g; // Updated regex to capture any prefix with optional symbols followed by digits
            let matches = xpath.match(xpathPattern);

            // If matches are found, process them
            if (matches && matches.length > 0) {
                // Initialize a tracker for processed patterns if not already present
                if (!interestingItems.processedXPaths) {
                    interestingItems.processedXPaths = {};
                }

                for (let match of matches) {
                    // Create a pattern representation based on the structure
                    let pattern = match.replace(/(\d+)/, '<number>'); // Replace the numeric part with a placeholder

                    // If the pattern has already been processed, skip this item
                    if (interestingItems.processedXPaths[pattern]) {
                        return false;
                    } else {
                        // Add the pattern to the processed set
                        interestingItems.processedXPaths[pattern] = true;
                    }
                }
            }
        }
        return true;
    }

    /***
     *  Todo - KK : This method is **CRYING** to be refactored !!!
     */

    filterItems(interestingItems) {
        // Keep only the visible items. Simple links (load action) dont need visibility checks.
        function visibleOnly(item) {
            if (item[1].rect.inViewport) {
                return true
            }
            // remove from relevantItems
            delete interestingItems.relevantClickItems[item[0]];
            return false
        }

        // Remove disabled or hidden items and also from relevantItems
        function isDisabledOrHidden(item) {
            if (item[1].isDisabled || item[1].isHidden) {
                // remove from relevantItems
                delete interestingItems.relevantClickItems[item[0]];
                return true
            }
            return false;
        }

        // from interestingItems.linkItems using href key remove duplicates
        interestingItems.linkItems = _.uniqBy(interestingItems.linkItems, '[1].href')

        // Can only click visible items so add all invisible, valid, crawlable hrefs clickable items to linkitems 
        interestingItems.linkItems.push(...interestingItems.clickableItems.filter(item => !visibleOnly(item) && item[1].href != 'null' && item[1].href.length > 0))
        interestingItems.clickableItems = interestingItems.clickableItems.filter(visibleOnly)

        // Filter out disabled or hidden items
        interestingItems.clickableItems = interestingItems.clickableItems.filter(item => !isDisabledOrHidden(item));

        // Similar thing for any 'fully qualified' href that is also clickable
        function mustClick(clickableItem) {
            return true
            let hrefAttr = clickableItem[1].allAttrs.find(x => x.name == 'href')
            let href = hrefAttr ? hrefAttr.value : null

            // empty, starts with javascript, starts with # : must be clicked.
            if (!href || href == 'null' || href == 'undefined') {
                return true
            }
            return /^\s*(?:#|javascript)/i.test(href)
        }
        interestingItems.linkItems.push(...interestingItems.clickableItems.filter(item => !mustClick(item)))
        interestingItems.clickableItems = interestingItems.clickableItems.filter(mustClick)

        // Ignore form if configured
        if (this.config.ignoreForm) {
            interestingItems.inputItems = [];
        }

        function visibleOrHasNoValue(item) {
            // if it does not have value, keep it and fill so that forms that submit 
            // input items defined inside/outside the form get filled values.
            if (HaikuUtils.isNullOrUndefined(item[1].value)) {
                return true
            }

            return visibleOnly(item)
        }

        interestingItems.inputItems = interestingItems.inputItems.filter(visibleOrHasNoValue);
        interestingItems.inputItems = interestingItems.inputItems.filter(item => this.filterOutSimilarXpath(item, interestingItems));
        // Filter out disabled or hidden items
        interestingItems.inputItems = interestingItems.inputItems.filter(item => !isDisabledOrHidden(item));

        // don't fill the following parameters
        // .net viewstate, session IDs, csrf token
        let inviolateParamsRegex = /__EVENTARGUMENT|__VIEWSTATE|__LASTFOCUS|__EVENTVALIDATION|__EVENTTARGET|(?:(?:sess|session)[_\-]?(?:id|key|name))|csrf|xsrf/i
        interestingItems.inputItems = interestingItems.inputItems.filter(item => {
            return !inviolateParamsRegex.test(item[1].name) && !inviolateParamsRegex.test(item[1].id)
        })

        // sort by stackIndex
        function byStackIndex(a, b) {
            return b[1].stackIndex - a[1].stackIndex
        }

        if (_.get(this.config, 'pluginData.filterItems.orderByStackIndex')) {
            interestingItems.inputItems.sort(byStackIndex)
            interestingItems.formItems.sort(byStackIndex)
            interestingItems.clickableItems.sort(byStackIndex)
        }

        // sort clickable items so that login actions bubble to the top
        function byLogin(a, b) {
            if (a[1].ifcType == 'login' && b[1].ifcType != 'login') {
                return -1
            } else if (a[1].ifcType != 'login' && b[1].ifcType == 'login') {
                return 1
            }
            return 0
        }
        interestingItems.linkItems.sort(byLogin)
        interestingItems.clickableItems.sort(byLogin)

        // filter by stack index, dealing with the top most (highest z-index) first
        if (_.get(this.config, 'pluginData.filterItems.filterByStackIndex')) {
            var maxStkIdx = Number.MIN_SAFE_INTEGER

            function filterByStackIndex(a) {
                return a[1].stackIndex == maxStkIdx
            }

            if (interestingItems.inputItems.length > 0) {
                interestingItems.inputItems.forEach((ele) => {
                    // only visible items contribute to the max stack index
                    if (ele[1].rect && ele[1].rect.inViewport && ele[1].stackIndex > maxStkIdx) {
                        maxStkIdx = ele[1].stackIndex
                    }
                })
                interestingItems.inputItems = interestingItems.inputItems.filter(filterByStackIndex)
            }

            if (interestingItems.clickableItems.length > 0) {
                maxStkIdx = Number.MIN_SAFE_INTEGER
                interestingItems.clickableItems.forEach((ele) => {
                    if (ele[1].stackIndex > maxStkIdx) {
                        maxStkIdx = ele[1].stackIndex
                    }
                })
                interestingItems.clickableItems = interestingItems.clickableItems.filter(filterByStackIndex)
            }
        }

        // don't process clickable items that have href set to things like downloadable files/images/...
        interestingItems.clickableItems = interestingItems.clickableItems.filter((item) => !this.shouldSkipLink(item[1].href))

        // Also ensure click will not be to an external domain
        interestingItems.clickableItems = interestingItems.clickableItems.filter((item) => this.isAllowedAction(item[1].href))
        interestingItems.clickableItems = interestingItems.clickableItems.filter((item) => this.isAllowedAction(item[1].formaction))

        // Remove all actionable items that are part of forms that we have already seen
        if (Array.isArray(interestingItems.formItems) && interestingItems.formItems.length > 0) {
            for (let formItem of interestingItems.formItems) {
                if (!this.seenFormItems.has(formItem[1].fingerprintTokens)) {
                    continue
                }

                if (Array.isArray(formItem.formElements) && formItem.formElements.length > 0) {
                    // This form has been seen before, remove all form elements from actionable items
                    interestingItems.clickableItems = interestingItems.clickableItems.filter((item) => !formItem.formElements.includes(item[0]))
                    interestingItems.inputItems = interestingItems.inputItems.filter((item) => !formItem.formElements.includes(item[0]))
                    interestingItems.linkItems = interestingItems.linkItems.filter((item) => !formItem.formElements.includes(item[0]))
                }
            }
        }

        // -- Link items - these are simple, non evented links that are processed using LoadAction --
        // remove dups links
        interestingItems.linkItems = _.uniqBy(interestingItems.linkItems, '[1].href')

        // Only keep links we are allowed to crawl since loadUrl will not trigger the will-navigate event
        function allowedToCrawlLink(href) {
            try {
                let linkFull = new URL(href, this.config.parsedUrl.href);
                this.scanner.emit('found-resource', href);
                return /^http/.test(linkFull.href) && !this.shouldSkipLink(linkFull.href) && this.scanner.allowedToCrawlUrl(linkFull.toString(), interestingItems.location)
            } catch (err) {
                return false
            }
        }

        function allowedToCrawlLinkItem(linkItem) {
            return allowedToCrawlLink.bind(this)(linkItem[1].href)
        }
        interestingItems.linkItems = interestingItems.linkItems.filter(allowedToCrawlLinkItem.bind(this))

        // Only keep links we have not loaded before - can't do this for click since dont have a good way 
        // of uniquely identifying the click, can't do this for input since that needs to be filled everytime
        function uniqueHref(linkItem) {
            return !this.seenLinkItems.has(utils.canonicalizeUrl(linkItem[1].href, this.config.parsedUrl.href))
        }
        interestingItems.linkItems = interestingItems.linkItems.filter(uniqueHref.bind(this))

        // ** keep the adding 'seen' items at the end otherwise may add links/forms to the set that were not really sent to crawler 

        // Add the unique link items to our set so we can skip it in the future 
        for (let linkItem of interestingItems.linkItems) {
            this.seenLinkItems.add(utils.canonicalizeUrl(linkItem[1].href, this.config.parsedUrl.href))
        }

        // Simple hrefs are very similar to links and processed the same except that they are simple hrefs
        // not an annotated 'item'
        // remove dups links
        interestingItems.simpleHrefs = _.uniq(interestingItems.simpleHrefs)
        interestingItems.simpleHrefs = interestingItems.simpleHrefs.filter(allowedToCrawlLink.bind(this))
        interestingItems.simpleHrefs = interestingItems.simpleHrefs.filter((u) => {
            return !this.seenLinkItems.has(utils.canonicalizeUrl(u, this.config.parsedUrl.href))
        })

        // ** keep the adding 'seen' items at the end otherwise may add links/forms to the set that were not really sent to crawler 
        for (let href of interestingItems.simpleHrefs) {
            this.seenLinkItems.add(utils.canonicalizeUrl(href, this.config.parsedUrl.href))
        }

        // tag clickable items that have duplicate URLs
        for (let clickableItem of interestingItems.clickableItems) {
            clickableItem[1].canLoadUrl = false
            if (clickableItem[1].href && clickableItem[1].href != 'null') {
                clickableItem[1].canLoadUrl = !this.seenLinkItems.has(utils.canonicalizeUrl(clickableItem[1].href, this.config.parsedUrl.href))
            }
        }

        // skip previously seen forms
        // keep only unique forms
        interestingItems.formItems = interestingItems.formItems.filter((formItem) => {
            return !this.seenFormItems.has(formItem[1].fingerprintTokens)
        })

        // skip the forms that contain elements we dont want to attack eg. captcha
        interestingItems.formItems = interestingItems.formItems.filter((formItem) => {
            return !/captcha/i.test(formItem[1].fingerprintTokens)
        })
        // Also ensure form submit will not be to an external domain
        interestingItems.formItems = interestingItems.formItems.filter((item) => this.isAllowedAction(item[1].action))

        // we have seen these forms now...
        for (let formItem of interestingItems.formItems) {
            this.seenFormItems.add(formItem[1].fingerprintTokens)
        }
    }

    //filters the interestingItems passed to it - re-assigns the passed variable, does not return any 'actions'
    getActions(ret, interestingItems) {
        // previous plugin already decided to skip state or specifically asked to stop processing more plugins => nothing to do
        if (ret.skipState) {
            return
        }

        this.filterItems(interestingItems)
    }
}

module.exports = FilterItems