const utils = require('../ifc-utils.js')
const electron = require('electron')

// This class really will not be serialized but there for consistency
// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// debug wait action
class DebugWaitAction {
    constructor() {}

    get actionType() {
        return 'debug-wait'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: []
        }
    }

    async execute(executionContext) {
        console.log('Crawler waiting... type indusfaceRenderer.debug.sendEventToMain("debug-go") in the browser console to proceed')
        await new Promise((resolve, rej) => {
            electron.ipcMain.once('debug-go', resolve)
        })
        return true
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType}`
    }
}

module.exports = DebugWaitAction