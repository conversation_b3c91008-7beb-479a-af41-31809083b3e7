const debug = require('debug')('PredictableResourceLocation')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class PredictableResourceLocation extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-predictable-resource-location'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            skipRoot: true,
            maxPathComponents: 4,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'always'
        });
    }
    getAttackVectors() {
        return PRLVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
      
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let stacode = attack.result.resp.httpResponse.statusCode
        if (stacode == 403) {
            let url = attack.httpRequest.uri
            if (url.charAt(url.length - 1) == '/') {
                if (url.substring(url.length - 6).includes('.')) {
                    return
                }
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.httpRequest.uri)
            }
        }
    }
}

// vectors & matches ...
// Adding a few more vectors based on the customer cases reported
const PRLVectors = [
    ``,
    `/resources`,
    `/resource`,
    `/images`,
    `/image`
]

module.exports = PredictableResourceLocation