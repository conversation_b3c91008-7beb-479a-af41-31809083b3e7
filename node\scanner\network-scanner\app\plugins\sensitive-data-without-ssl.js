const debug = require('debug')('PasswordWithoutSSLPlugin')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

// Checks for any sensitive data (type password) being submitted without https
class PasswordWithoutSSLPlugin extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-sensitive-data-submitted-without-ssl'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        if (attack.attackArea == 'original-crawler-request') {
            let body = _.get(attack, "result.resp.body")
            let args_regexp = /\w*( |-|_)?(number|no)\b|\w*(\b|-|_)(mobile|contact|phone|blood_group|(user)?email_?(id|address)?|(credit|debit|pan)(_|-)?(card|id)|(user|emp|employee)(_|-)?(id|name|Input)|uname|uid|user|(account|ac)(_|-)?(id)?|(password|passwd|pwd)(_|-)?(new|old|conf)?|api(_|-)?key)|\btype="?password|\bAuthorization\b|xmlns:\w+= ?('|")http:/i //Sensitive data disclosed
            if (args_regexp.test(body)) {
                return true
            }
        }
        return false
    }

    /**
     * checks for protocol and form action and any password field present in the form
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        // var classIns = this
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //if vuln detected for a req then return
        if (pluginDataForRequest.pwdSubmittedOverHttpVulnFound) {
            return
        }

        let args_regexp = /\w*[\-_]?(?:number|no)s?\b|mobile|contact|phone|blood|\bATM|\bPIN(?![\-_]code)|\bPAN|email|(?:credit|debit|ATM)[_\-]?(?:card|id)|(?:user|emp|employee|customer)[_\-]?(?:id|name|Input)|uname|uid|user|account|\bac[_\-]id|password|passwd|pwd|api[_\-]?key|key\b|customerId|productId|invoiceId|messageId|deviceId|accountId|memberId|articleId|commentId|postId|ticketId|requestId|referenceId|documentId|assetId|bankId|policyId|claimId|loanId|branchId|clientId|contractId|fundId|investmentId|portfolioId|paymentId|premiumId|quoteId|settlementId|profileId|subscriberid|\bOTP|cardPin|pseudo_id|appid|\bpid|\bAuthorization\b|session|token|aadhaar|passport|\bcvv|captcha/i

        //For API
        let ReqBody = _.get(attack, 'httpRequest.body')
        let proto = _.get(attack, 'result.req.parsedURL.protocol')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')
        let Resbody = _.get(attack, "result.resp.body")

        if (proto == 'http:' && args_regexp.test(ReqBody) && contentTypeHeaderVal == 'application/json' && statusCode == "200") {
            let details = ReqBody
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
            pluginDataForRequest.pwdSubmittedOverHttpVulnFound = true
            return
        }
        else if (/xmlns:\w+= ?('|")http:/i.test(Resbody) && /application\/(json|xml)/i.test(contentTypeHeaderVal) && statusCode == "200") {
            let details = Resbody.match(/xmlns:\w+= ?('|")http:.+?"/ig).join('\n')
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
            pluginDataForRequest.pwdSubmittedOverHttpVulnFound = true
            return
        }

        // For AA
        let details = []

        if (/< ?form.+method ?=/i.test(Resbody)) {
            let regexp = /< ?form[\w\W]+?< ?\/form ?>/gi
            let formtag = []
            try {
                formtag = Resbody.match(regexp)
                if (formtag.length > 0 && args_regexp.test(formtag)) {
                    for (let tag of formtag) {
                        if (details.length > 0) { break }
                        if (tag.length > 20 && args_regexp.test(tag)) {
                            let actionval = tag.match(/action ?= ?('|").+?\1/gi)[0]
                            if ((proto == 'http:' && !actionval.includes('https:')) || (proto == 'https:' && actionval.includes('http:'))) {
                                let inputtag = tag.match(/< ?input[\w\W]+?>/gi)
                                for (let stag of inputtag) {
                                    let argname = stag.match(/name ?= ?('|").+?\1/gi)
                                    if (argname.length > 0 && args_regexp.test(argname)) {
                                        details.push({ result: argname + '\n' + actionval })
                                        break
                                    }
                                }
                            }
                        }
                    }
                    if (details.length > 0) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        pluginDataForRequest.pwdSubmittedOverHttpVulnFound = true
                    }
                }
            }
            catch (e) {
                return
            }
        }
        /* 
        let $ = _.get(attack, 'result.resp.httpResponse.cheerio');
        let pwdFound = $('input[type=password]')

        if (pwdFound.length > 0) {
            //get all forms and switch on protocol
            let forms = $("form")
            let prot = _.get(attack, 'result.req.parsedURL.protocol')
            let details = []
            switch (prot) {
                case "http:":
                    //for each form check action if fullfilled then call createVulnerabilityDetails ;
                    {
                        let redirects = _.get(attack, 'result.resp.httpResponse.redirects')
                        if (redirects.length > 0) {
                            let redirectURI = redirects[redirects.length - 1].redirectUri
                            let parsedUrl = new URL(redirectURI)
                            if (parsedUrl.protocol == 'https:') {
                                return
                            }
                        }
                        if (forms.length > 0) {
                            let vuln = ''
                            forms.each(function (index) {
                                let form_action = $(this).attr('action')
                                if (!form_action || (form_action.indexOf("https") == -1)) {
                                    let frm = $(this).html()
                                    let pwdField = $('input[type=password]', frm)
                                    //if getting password fields then create vul object and add in result
                                    if (pwdField.length > 0) {
                                        pwdField.each(function (index) {
                                            classIns.createVulnerabilityDetails($, details, form_action, this)
                                        })

                                    }

                                }
                            })
                            if (details.length > 0) {
                                this.addVulnToAttack(attack, details, pluginDataForRequest)
                            }
                        }

                    }
                    break
                case "https:":
                    {
                        if (forms.length == 0) {
                            return; //action not set,then no vulnerability
                        }
                        let details = []
                        forms.each(function () {
                            let form_action = $(this).attr('action')
                            if (form_action && (form_action.indexOf("http:") == 0)) {
                                // check for password and report vulnerability
                                let frm = $(this).html()
                                let pwdField = $('input[type=password]', frm)
                                //if getting password fields then create vul object and add in result
                                if (pwdField.length > 0) {
                                    pwdField.each(function (index) {
                                        classIns.createVulnerabilityDetails($, details, form_action, this)
                                    })
                                }
                            }
                        })
                        if (details.length > 0) {
                            this.addVulnToAttack(attack, details, pluginDataForRequest)
                        }

                    }
            }
        }
    }
    /**
     * @param  {$} $ cheerio html parser
     * @param  {details} details The details object giving details of the vulnerable element
     * @param  {form_action} form_action action of the form
     * @param  {pwdFld} pwdField DOM element of the password field
     * function creates an object containing description of vulnerable element and pushes it in the details array.
     
    createVulnerabilityDetails($, details, form_action, pwdFld) {
        details.push(
            $(pwdFld).attr('name')
        )
    }

    /**
     * @param  {attack} object to be updated
     * @param  {details} Array-details of vulnerable elements 
     * @param  {pluginDataForRequest} request specific data store
     * Function craetes vulnerability object and adds it to the attack result.
     
    addVulnToAttack(attack, details, pluginDataForRequest) {

        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
        pluginDataForRequest.pwdSubmittedOverHttpVulnFound = true
    } */
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, ['password', 'pwd', 'paswwd']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['password']);
    }
}

module.exports = PasswordWithoutSSLPlugin