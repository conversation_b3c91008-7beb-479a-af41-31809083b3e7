const RpcQueueMessage = require('../rpc-queue-message')
const path = require('path')
const debug = require('debug')('Messages:RefreshCrawlerRequestRpc')

/** 
 * scanner <-> crawler asking for refresh of a particular request to get the latest session, params etc.
 * @extends QueueMessage
*/
class RefreshCrawlerRequestRpc extends RpcQueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} RefreshCrawlerRequestRpcMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request
     * @property {*} crawlerBookmark memento returned by crawler to enable it to get back to state where thie request was captured
     * @property {string} appTranaKey Key identifying request that was captured.
     */
    /**
     * @param {RefreshCrawlerRequestRpcMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'crawler'
        this.routingKey = 'request.rpc.refresh-request'
        this.msgType = RefreshCrawlerRequestRpc.msgType
    }

    /*** tmp for testing - will refactor later */
    deserialize(content, contentType) {
        let ret = super.deserialize(content, contentType)

        // clean up the http request
        if (ret && ret.httpRequest) {
            // Always delete 'key' parameter - totally messes up HTTPS sites
            // since 'key' means client side SSL key for https Agent !!
            // Rename the key
            ret.httpRequest.crawlerKey = ret.httpRequest.appTranaKey || ret.httpRequest.key
            delete ret.httpRequest.key

            // Network scanner and Request object use 'uri' and crawler uses 'url'. Remove url to avoid confusion
            ret.httpRequest.uri = ret.httpRequest.url
            delete ret.httpRequest.url

            // Make method uppercase for consistency
            if (ret.httpRequest.method) {
                ret.httpRequest.method = ret.httpRequest.method.toUpperCase()
            }
        }

        return ret
    }

}

module.exports = RefreshCrawlerRequestRpc