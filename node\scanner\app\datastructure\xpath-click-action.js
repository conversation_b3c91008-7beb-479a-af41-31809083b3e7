const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// real click action - send input actions for mouseup, mousedown
class XPathClickAction {
    constructor(xpath, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.annotation = annotation
    }

    get actionType() {
        return 'xpath-click'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let actionSucceeded = true // optimistic approach
        let jscode = `indusfaceRenderer.xpathClick('${this.xpath}')`
        await utils.timedPromise(browser.webContents.executeJavaScript(jscode), null)

        return actionSucceeded
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = XPathClickAction