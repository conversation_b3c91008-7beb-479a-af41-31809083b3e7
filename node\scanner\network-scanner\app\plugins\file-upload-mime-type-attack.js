const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
const crypto = require('crypto')
const fs = require('fs')
const path = require('path')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const caseless = require('caseless')

class MimeTypeValidationBypassAttack extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config);
        // MIME type validation bypass vulnerability
        this.MimeTypeValidationBypassVuln = 'ID-mime-type-validation-bypass-vuln';
    }

    async performNetworkAttack(attack) {
        // Only process POST requests
        if (attack.httpRequest.method !== "POST") {
            return null;
        }

        const body = attack.httpRequest.body || '';

        // Extract multipart form-data values
        try {
            // Extract boundary from the body
            const boundaryMatch = body.match(/^-{2}([\w-]+)/m);
            if (!boundaryMatch) {
                console.error("No boundary found in multipart form-data");
                return null;
            }
            const boundary = boundaryMatch[1];

            // Split body into parts using boundary
            const parts = body.split(`--${boundary}`);

            // Find the file upload part
            const filePart = parts.find(part => part.includes('Content-Disposition: form-data') && part.includes('filename='));
            if (!filePart) {
                console.error("No file upload part found in body");
                return null;
            }

            // Extract filename
            const filenameMatch = filePart.match(/filename="([^"]+)"/);
            const filename = filenameMatch ? filenameMatch[1] : null;

            // Extract Content-Type
            const contentTypeMatch = filePart.match(/Content-Type:\s*([^\r\n]+)/);
            const originalContentType = contentTypeMatch ? contentTypeMatch[1].trim() : null;

            // Extract file content from the request body - improved pattern
            const headerEndIndex = filePart.indexOf('\r\n\r\n');
            let originalContent = '';
            if (headerEndIndex !== -1) {
                originalContent = filePart.substring(headerEndIndex + 4).trim();
                // Remove the last \r\n if exists
                if (originalContent.endsWith('\r\n')) {
                    originalContent = originalContent.slice(0, -2);
                }
            }

            // Change content type to something malicious (example: image/jpeg)
            const maliciousContentType = 'image/jpeg';

            // Reconstruct the file part with new content type
            let newFilePart = filePart;

            // Replace content type if it exists
            if (originalContentType) {
                newFilePart = newFilePart.replace(
                    /Content-Type:\s*[^\r\n]+/,
                    `Content-Type: ${maliciousContentType}`
                );
            }

            // Reconstruct the full body
            const newParts = parts.map(part =>
                part === filePart ? newFilePart : part
            );
            const newBody = newParts.join(`--${boundary}`);

            // Update the request with new body
            attack.httpRequest.body = newBody;

            console.log('Modified Request:', {
                filename,
                originalContentType,
                newContentType: maliciousContentType,
                contentPreview: originalContent,
                boundary
            });

            // Perform the attack with modified request
            const result = await super.performNetworkAttack(attack);
            return result;

        } catch (error) {
            console.error(`Error modifying multipart form-data: ${error.message}`);
            return null;
        }
    }

    getAttackVectors() {
        return uploadVectors;
    }

    getAttackableEvents() {
        return ['file-upload']
    }

    processAttackResponse(attack) {
        if (!attack || !attack.result || !attack.result.resp || !attack.result.resp.httpResponse) {
            console.error("Invalid attack object", attack);
            return;
        }

        const payloadFile = attack.vector;
        if (payloadFile) {
            const body = attack.result.resp.httpResponse.body;
            const statusCode = attack.result.resp.httpResponse.statusCode;

            if (body && this.checkBodyForVuln(body, statusCode)) {
                attack.isVulnerable = true;
                this.addVulnerabilitytoResult(attack, this.MimeTypeValidationBypassVuln, payloadFile);
            }
        }
    }

    checkBodyForVuln(respBody, respStatusCode) {
        if (!respBody || respStatusCode < 200 || respStatusCode >= 300) {
            return false;
        }

        const bodyString = respBody.toString();
        const successPatterns = [
            // File upload success patterns
            /The file has been uploaded/i,
            /(?:file|upload).*(?:successful|completed|saved|uploaded)/i,
            /successfully.*(?:uploaded|saved|stored)/i,
            /upload.*(?:complete|finished|done)/i,
            /The file .*\.[\w]+(?:\s+)?has been uploaded/i,
            /.*\.[\w]+(?:\s+)?(?:uploaded|saved)(?:\s+)?successfully/i,
            /(?:uploaded|saved)(?:\s+)?(?:file|path)?:?\s+.*\.[\w]+/i,
            /File unzipped/i,
        ];

        return successPatterns.some(pattern => new RE2(pattern).test(bodyString));
    }
}

const uploadVectors = [
    // Basic payloads for MIME type validation bypass
    'scanner/upload-attack-files/basic-payloads/haiku.php',
    'scanner/upload-attack-files/basic-payloads/haiku.asp',
    'scanner/upload-attack-files/basic-payloads/haiku.jsp'
]

module.exports = MimeTypeValidationBypassAttack;