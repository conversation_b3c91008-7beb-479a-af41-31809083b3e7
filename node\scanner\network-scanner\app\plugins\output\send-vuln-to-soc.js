const debug = require('debug')('SendVulnToSoc')
const fs = require('fs')
const mkdirp = require('mkdirp')
const URL = require('url').URL
const ssAPI = require('../../../../common/lib/sooper-scheduler-api')
const BasePlugin = require('../base-plugin')
const logger = require('../../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const _ = require('lodash')

/**
 * Plugin that will append vulnerability info to a file 
 */
class SendVulnToSoc extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the netork scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // temp till output plugin ready
        this.vulnsOutPath = './vulns'
        mkdirp.sync(this.vulnsOutPath)

        // event handler
        this.networkScanner.on('vulnerability-found', this.appendVulnToFile.bind(this))
    }

    /**
     * Convert heaader object to a string array with one header per entry
     * @return {array} string array like ["host: xyz.com", "content-length: 300",...]
     * @param {object} headers HTTP Headers hash map like { host : 'xyz.com', ... } 
     */
    httpHeadersToStringArray(headers) {
        let headerStrArray = []
        try {
            headerStrArray = Object.entries(headers).map((header) => {
                return header[0] + ': ' + header[1]
            })
        } catch (e) { }

        return headerStrArray
    }

    /** 
     * @param {vuln} vuln Information on vulnerablities found in one network request
     */
    appendVulnToFile(vuln) {
        let IGWStyleVulns = []

        for (let haikuVulnId of Object.keys(vuln.vulns)) {
            let thisVuln = vuln.vulns[haikuVulnId]
            const defaultVulnInfo = {
                productionReady: thisVuln.productionReady,
                igwId: -1
            }
            let vulnInfo = _.get(this.getConfig(vuln).Plugins[thisVuln.foundBy], `vulnerabilities[${haikuVulnId}]`, defaultVulnInfo)

            // generate igw 'result' from vuln. details
            let igwResult = HaikuUtils.getIGWResult(vuln, haikuVulnId)

            let IGWVuln = {
                // common & vulnerability info
                scanner: 'haiku',
                productionReady: vulnInfo.productionReady,
                vulnerabilityid: vulnInfo.igwId,
                result: igwResult,
                founddate: (new Date).toISOString(), // scan start date-time -> If cant do this, use current date-time for now.

                // not sending
                // "responseurl": null,
                // "referenceurl": null,
            }

            // construct the IGW key 
            IGWVuln.key = HaikuUtils.getIGWKey(vuln)

            // attack specific info
            if (vuln.attack) {
                Object.assign(IGWVuln, {
                    url: vuln.attack.href,
                    para: vuln.attack.param,
                    vector: vuln.attack.vector,
                    requestheader: this.httpHeadersToStringArray(_.get(vuln, 'attack.headers', [])).join('\n'),
                    method: vuln.attack.method,
                    injectedurl: vuln.attack.href,
                })
            }

            // response specific info
            if (vuln.response) {
                Object.assign(IGWVuln, {
                    responseheader: this.httpHeadersToStringArray(_.get(vuln, 'response.headers', [])).join('\n'),
                })
            }

            /* vvv Till Soc fixes API ... vvv
             * no place to send status code and soc does not sue the 'HTTP method' property that we send
             * for now simulate IGW and add to request & response headers
             */
            if (IGWVuln.requestheader && vuln.attack) {
                let url = new URL(vuln.attack.href)
                IGWVuln.requestheader = `${vuln.attack.method} ${url.pathname}${url.search} HTTP/1.1\n` + IGWVuln.requestheader
                if (vuln.attack.body) {
                    IGWVuln.requestheader += "\n\n" + vuln.attack.body
                }
            }
            if (IGWVuln.responseheader && vuln.response) {
                IGWVuln.responseheader = `${vuln.response.statusCode} ${vuln.response.statusMessage}\n` + IGWVuln.responseheader
            }
            /* ^^^ Till Soc fixes API ... ^^^ */

            // also add the haiku id as that's more human readable
            IGWVuln.haikuVulnId = haikuVulnId

            IGWStyleVulns.push(IGWVuln)
        }

        let socAPIVulnerabilityInfo = {
            session_id: vuln.scanId,
            scanlog_id: vuln.scanlogId,
            scanner: 'haiku',
            vulnerabilities: IGWStyleVulns,
        }

        // Write to the file as well.
        let hostname = _.get(vuln, 'attack.hostname') || 'GLOBAL-ERROR'
        fs.appendFile(`${this.vulnsOutPath}/${hostname}-igwvulns-${vuln.scanlogId}.json`, JSON.stringify(socAPIVulnerabilityInfo), (err) => {
            if (err) {
                logger.log('error', `could not append vulnerability to file: ${err}`, HaikuUtils.getMetadataForLog(vuln))
            }
        })

    }
}

module.exports = SendVulnToSoc