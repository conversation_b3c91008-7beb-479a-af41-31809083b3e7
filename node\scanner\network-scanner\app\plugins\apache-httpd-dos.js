const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * Apache httpd DOS Plugin Strategy:
 * Here for any url on sending a request with set of params in header and if response obtained is 
 * 206 then it's vulnerable to ranger dos attack
 */

class apacheHttpdDOS extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-httpd-dos'
    }

    /* initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true, // this false with max path 0 will give only one request with core url
            skipRoot: false,
            maxPathComponents: 2,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never',
            encodings: ['uri']
        });
    } */

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return AttackVector
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        let getMethod = _.get(attack, 'originalRequest.httpRequest.method')
        let currserver = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (getMethod == "GET") {
            if (currserver == '' || /Apache/i.test(currserver) || !/\b(?:nginx|Microsoft-IIS|LiteSpeed|Caddy|Jetty|Node\.js|express|openresty|gunicorn|Cherokee|GWS|Tengine|Oracle-HTTP-Server|IBM_HTTP_Server|WEBrick|Resin|GlassFish|\.NET|Kestrel)\b/i.test(currserver)) {
                // attack.httpRequest.method = "HEAD"
                let uriRaw = new URL(attack.httpRequest.uri)
                attack.httpRequest.uri = `${uriRaw.origin}/`
                attack.httpRequest.headers['Range'] = "bytes=5-0,1-1,2-2,3-3,4-4,5-5,6-6,7-7,8-8,9-9,10-10"
                attack.httpRequest.headers['Request-Range'] = "bytes=5-0,1-1,2-2,3-3,4-4,5-5,6-6,7-7,8-8,9-9,10-10"
                return await super.performNetworkAttack(attack)
            }
        }
        return false
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginDataForRequest.apachehttpdDOS) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        if (statusCode == "206") {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `A request was sent with the HEAD method and the Range & Range-Request headers. A 206 response status code was received. Therefore, it was marked as vulnerable to a Apache Range Denial Of Service attack.`)
            pluginDataForRequest.apachehttpdDOS = true
            return
        }
    }
}

/* const nothingToAttack = [
    VectorResponseAttack.identityVector
] */
const AttackVector = [
    'HEAD',
]

module.exports = apacheHttpdDOS