const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class SlowLorisDDOSAttack extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-slowloris-ddos-attack'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (attack.attackArea == "original-crawler-request") {
            //Added below condition to reduce number of FP's, will only target servers
            // vulnerable to this attack
            let server = _.get(attack, 'result.resp.httpResponse.headers["server"]');
            // let serverHeader2 = _.get(attack, 'originalRequest.httpResponse.headers.server')
            if (/apache|iis|httpd/i.test(server)) {
                return true
            }
        }
        return false
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */

    async processAttackResponse(attack) {
        // let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.slowlorisVulnFound) {
            return
        }

        try {
            let parsedUrl = new URL(attack.httpRequest.uri)
            if (parsedUrl.hostname) {
                // Input json data for python script
                let jsonData = {
                    "hostName": parsedUrl.hostname,
                    "port": (parsedUrl.protocol == 'https:') ? 443 : 80,
                    "detectionTimeout": this.getMetadata(attack).vulnerabilities['ID-slowloris-ddos-attack'].detectionTimeout
                }

                // let cmd = 'slowlorisDetection.py ' + this.getMetadata(attack).vulnerabilities['ID-slowloris-ddos-attack'].detectionTimeout
                let output = await this.networkScanner.pythonExtension.executeCommand("slowlorisDetection.py", jsonData)
                let vulnObj = output.json;
                vulnObj.detectionTimeoutVal = this.getMetadata(attack).vulnerabilities['ID-slowloris-ddos-attack'].detectionTimeout

                if (_.isObject(vulnObj)) {
                    // checking if the target webserver is vulnerable to slowloris
                    if (vulnObj.isSlowlorisVulsFound) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnObj)
                        pluginStorageScanScope.slowlorisVulnFound = true
                    }
                }
            }
        } catch (e) { return }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["server", "statusCode"]);
    }
}

module.exports = SlowLorisDDOSAttack