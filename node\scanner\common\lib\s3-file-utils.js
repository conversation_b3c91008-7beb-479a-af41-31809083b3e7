const fs = require('fs')
const mkdirp = require('mkdirp')
const haikuBucket = process.env['HAIKU_BUCKET'] || 'haiku-store'
const _ = require('lodash');

class HaikuS3FileUtils {

    /**
     * get a 'file' from local file sysytem
     * @param {string} prefix  is the path
     * @param {string} name is the name of the file 
     */
    getFile(prefix, name) {
        let localStorage = this.getStoragePath(prefix)
        return {
            Body: fs.readFileSync(localStorage + name)
        }
    }

    /**
     * Write to local file system
     * @param {string} prefix is the path in local file directory
     * @param {string} name of the file
     * @param {any} Body the data to upload 
     */
    upload(prefix, name, Body) {
        let localStorage = this.getStoragePath(prefix)
        this.createDependencyDirs(localStorage)
        let filename = localStorage + name
        if (typeof Body === 'object' && Body.readable) {
            // looks like a stream?
            return new Promise((resolve, reject) => {
                //Uncomment this when running this program in windows machine in order to start/stop scan over api channel
                //filename = _.replace(filename, new RegExp(":", "g"), '')

                Body.pipe(fs.createWriteStream(filename))
                    .on('finish', resolve)
                    .on('error', reject)
            })
        } else {
            // everything else can be directly passed to writeFileSync
            return fs.writeFileSync(filename, Body)
        }
    }

    /**
     * check and create  local directory
     * @param {string} storage is the path to be created
     */
    createDependencyDirs(storage) {
        if (!fs.existsSync(storage)) {
            mkdirp.sync(storage)
            //fs.mkdirSync(storage,{ recursive: true })
        }
    }

    /**
     * get the full correct path of the file.
     * @param {string} storage is the path of the file which will be inside haiku-store.
     */
    getStoragePath(storage) {
        storage = './' + haikuBucket + '/' + storage
        if (!storage.endsWith('/')) {
            storage = storage + '/'
        }
        return storage
    }
}
const haikuS3FileUtils = new HaikuS3FileUtils()
module.exports = haikuS3FileUtils