# RE2 Replacement with Native RegExp

## Overview
This change replaces the native C++ RE2 module with a JavaScript polyfill that uses the built-in RegExp object. The RE2 native module was identified as a source of memory issues, particularly memory leaks or excessive memory usage over time.

## Implementation Details

1. **Used existing replacement**: `common/lib/re2-replacement.js` - A pure JavaScript implementation that wraps the native RegExp object and provides the same API as the RE2 module.

2. **Added require override**: Created `app/require-override.js` that intercepts all `require('re2')` calls and redirects them to our replacement module. This ensures all files using RE2 will use our replacement without needing to modify each file.

3. **Updated all application entry points**: 
   - Modified `app/main.js` (crawler entry point) to include the require override
   - Modified `network-scanner/app/main.js` (scanner entry point) to include the require override
   - Modified `REST/scan-api.js` (REST API entry point) to include the require override
   
   All entry points load the override at the very beginning, before any other modules are loaded.

4. **No node_modules changes**: This approach avoids modifying the node_modules directory, making it compatible with deployment workflows that use copied node_modules folders.

5. **Dependency management**: The RE2 dependency remains in package.json but is not actually used since we're intercepting all imports.

## Benefits

- **Reduced memory usage**: Native C++ modules like RE2 can contribute to memory fragmentation and high RSS usage.
- **Simplified deployment**: No need to build/compile the native module during installation.
- **Improved stability**: Less chance of memory-related crashes.
- **No file modifications needed**: Files like `apache-struts.js` can continue to use `require('re2')` without any changes.

## Performance Impact

While the native RE2 module was designed for better performance with large regexes, in practice:

1. Most regex operations in this codebase are short-lived and not computationally intensive.
2. The memory benefits outweigh any performance differences for our use case.
3. The stability improvement is more critical than raw regex performance.

## How It Works

The replacement provides the same interface as the RE2 module, and our require override ensures all imports of 're2' use our replacement:

```javascript
// Original code in plugin files (unchanged)
const RE2 = require('re2');
const regex = new RE2('pattern', 'flags');
regex.test(string);

// This is automatically redirected to our replacement by require-override.js
```

Internally, all RE2 operations now use the native JavaScript RegExp object, which avoids the memory overhead of the native module.

## Testing

### Basic Functionality Test

Run this command to verify the replacement works correctly with basic patterns:

```bash
node -e "const RE2 = require('./common/lib/re2-replacement'); const regex = new RE2('test.*pattern', 'i'); console.log(regex.test('Test example pattern')); console.log(regex.exec('Test example pattern'));"
```

Expected output:
```
true
[ 'Test example pattern', index: 0, input: 'Test example pattern', groups: undefined ]
```

### Testing the Override

To verify the require override is working:

```bash
# Start the application with a simple test
node -e "require('./app/require-override'); const RE2 = require('re2'); console.log('Using RE2 replacement:', RE2 === require('./common/lib/re2-replacement'));"
```

Expected output:
```
Using RE2 replacement: true
```

### Memory Monitoring

Use the existing `logmemudage` function in network-scanner.js to monitor memory usage before and after the RE2 replacement:

1. Run a standard scanning workflow with the original RE2 module and record memory metrics
2. Deploy the RE2 replacement with the require override
3. Run the same workflow and compare memory usage patterns
4. Look for reduced RSS usage and more stable memory allocation

### Integration Testing

1. Run the scanner on a test site and verify that all patterns still work correctly
2. Monitor memory usage during a long-running scan using the existing memory tracking
3. Compare memory usage patterns with the original RE2 implementation

### Verifying Array Pattern Support

Many plugins combine multiple patterns - verify this works correctly:

```bash
node -e "require('./app/require-override'); const RE2 = require('re2'); const patterns = [/one/, /two/, /three/]; const regex = new RE2(patterns.map(p => p.source).join('|'), 'i'); console.log(regex.test('two')); console.log(regex.test('none'));"
```

Expected output:
```
true
false
```

## Implementation Completeness

This replacement implementation covers:

- Constructor with pattern + flags parameters
- Core matching methods (test, exec)
- String manipulation methods (replace, match)
- Property accessors (source, flags, etc.)

If any additional methods or properties are needed, they can be added to the replacement. 