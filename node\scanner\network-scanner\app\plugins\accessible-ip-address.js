const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const dns = require('dns')
const { exec } = require('child_process');
const { Braket } = require('aws-sdk');

class AccessibleByIPAddress extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-accessible-by-ip-address'
    }

    async processAttackResponse(originalRequest) {
        let pluginStorageScanScope = this.getPluginScopedStore(originalRequest, 'this-scan')

        if (pluginStorageScanScope.ipaddressChecked || originalRequest.attackArea != 'original-crawler-request') {
            return
        }
        let OristatusCode = _.get(originalRequest, 'result.resp.httpResponse.statusCode', '')
        if (OristatusCode >= 400) { return }

        let Oriuri = new URL(originalRequest.href)
        let host = Oriuri.hostname

        if (/\d+(?:\.\d+){3}/.test(host) && OristatusCode == 200 && !/[a-z]/.test(host)) {
            let details = { "result": `Accessible by IP address (${host}).\n Request URL: ${originalRequest.href}` }
            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, details)
            pluginStorageScanScope.ipaddressChecked = true
        }

        let gethost = await this.dnsresolve(host)
        if (!gethost) {
            pluginStorageScanScope.ipaddressChecked = true
            return
        }

        let ipaddress = await this.getaddress(gethost)
        //{address: '**************', family: 4}    
        if (ipaddress != 'Error' && ipaddress.length > 0) {
            for (let currval of ipaddress) {
                if (currval.address.length > 1 && currval.family == 4) {
                    let updatedhref = Oriuri.href.replace(Oriuri.hostname, currval.address)
                    let response = await this.getresponse(updatedhref)
                    if (response != 'Error' && /HTTP\/[\d.]{1,3} \d{3} [a-z ]+/i.test(response)) {
                        let statusCode = response.match(/HTTP\/[\d.]{1,3} \d{3} [a-z ]+/ig)[0].split(' ')[1]
                        if (statusCode >= 300 && statusCode < 400 && /Location: https?:\/\/[\w.\/]+/i.test(response)) {
                            let location = response.match(/Location: https?:\/\/[\w.]+/gi)[0]
                            if (location.includes(host) || !location.includes(currval.address)) {
                                pluginStorageScanScope.ipaddressChecked = true
                                continue
                            }
                        }
                        else if (!(statusCode >= 400)) {
                            let details = { "result": `Accessible by IP address (${currval.address}).\n Request URL: ${updatedhref}\n Response: ${response}` }
                            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, details)
                            pluginStorageScanScope.ipaddressChecked = true
                            break
                        }
                    }
                    else {
                        pluginStorageScanScope.ipaddressChecked = true
                        return
                    }
                }
            }
        }
        else {
            pluginStorageScanScope.ipaddressChecked = true
            return
        }
    }

    getresponse(updatedhref) {
        let cmd = `echo "quit" | curl -kIs ${updatedhref}`
        return new Promise((resolve) => {
            exec(cmd, (err, stdout, stderr) => {
                if (stdout) {
                    resolve(stdout)
                }
                else {
                    resolve('Error');
                }
            });
        });
    }


    getaddress(gethost) {
        return new Promise((resolve, reject) => {
            const options = {
                all: true,
                family: 4
                //verbatim: false //Request URL: https://was.indusface.com/, Remote Address: **************:443
            };
            dns.lookup(gethost, options, (err, address, family) => {
                if (address) {
                    resolve(address)
                }
                else {
                    resolve('Error')
                }
            });
        });
    }

    dnsresolve(host) {
        return new Promise((resolve) => {
            dns.resolveCname(host, (err, addr) => {
                if (addr && /\.induscdn\.com/i.test(addr)) {
                    let hostname = `proxypass.${host}.indusguard.com`
                    resolve(hostname)
                }
                else {
                    resolve(host)
                }
            });
        });
    }
}

module.exports = AccessibleByIPAddress