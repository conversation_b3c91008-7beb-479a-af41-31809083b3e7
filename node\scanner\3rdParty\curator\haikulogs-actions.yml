---
# Haikulogs actions to be run everyday
# Currently we run the following actions
# 1.	Rollover index haikulogs-write when older than 1 day
# 2.	Delete ‘haikulogs-‘ indices older than 7 days.
#
# Remember, leave a key empty if there is no value.  None will be a string,
# not a Python "NoneType"
#
# Also remember that all examples have 'disable_action' set to True.  If you
# want to use this action as a template, be sure to set this to <PERSON>alse after
# copying it.
actions:
  1:
    action: rollover
    description: >-
      Rollover the haikulogs index
    options:
      disable_action: False
      name: haikulogs-write
      conditions:
        max_age: 1d

  2:
    action: delete_indices
    description: >-
      Delete indices older than 7 days (based on creation date), for haikulogs-
      prefixed indices. Ignore the error if the filter does not result in an
      actionable list of indices (ignore_empty_list) and exit cleanly.
    options:
      ignore_empty_list: True
      disable_action: False
    filters:
    - filtertype: pattern
      kind: prefix
      value: haikulogs-
    - filtertype: age
      source: creation_date
      direction: older
      unit: days
      unit_count: 7