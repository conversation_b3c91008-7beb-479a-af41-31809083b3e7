const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

// Checks for TRACE, TRACK method enabled
class TraceTrackMethodPlugin extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.tracevulnerabilityID = 'ID-TRACE-method-enabled'
        this.trackvulnerabilityID = 'ID-TRACK-method-enabled'
    }

    getAttackVectors() {
        return httpAttackVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * @param {method} attack
     * Overriding the performNetworkAttack method to add TraceEnable header in the request
     */
    async performNetworkAttack(attack) {
        attack.httpRequest.headers['TraceEnable'] = "yes"
        return await super.performNetworkAttack(attack)
    }

    /**
     * check result for vulnerability
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let CTHeader = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')

        /**
         * If the response has status code 200 and Content-type: message/http 
         *  then it will add it to the vulnerability list
         */
        if (attack.result.resp.httpResponse.statusCode == 200 && CTHeader == "message/http") {
            let trace = (attack.vector == 'TRACE') ? true : false;
            let details = {
                name: (trace) ? "Trace Method enabled" : "Track Method enabled",
                params: attack.httpRequest,
                status: attack.result.resp.httpResponse.statusCode
            }

            this.addVulnerabilitytoResult(attack, (trace) ? this.tracevulnerabilityID : this.trackvulnerabilityID, details)
        }
    }
}

const httpAttackVectors = [
    `TRACE`,
    `TRACK`
]

module.exports = TraceTrackMethodPlugin