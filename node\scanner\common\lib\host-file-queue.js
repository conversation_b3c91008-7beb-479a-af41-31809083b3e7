const HaikuUtils = require("./haiku-utils");

class HostFileQueue {
    constructor() {
      this.tasks = [];
      this.isProcessing = false;
    }
  
    async enqueue(task) {
      return new Promise((resolve, reject) => {
        this.tasks.push({
          task: task,
          resolve: resolve,
          reject: reject
        });
        this.processQueue();
      });
    }
  
    async processQueue() {
      if (!this.isProcessing && this.tasks.length > 0) {
        this.isProcessing = true;
        const { task, resolve, reject } = this.tasks.shift();
        try {
          const result = await task();
          resolve(result);
        } catch (err) {
          reject(err);
        } finally {
          this.isProcessing = false;
          this.processQueue();
        }
      }
    }
  
    async updateHostFileWithQueue(hostname, scanViaWAF, request, proxyPassDetails, logger = console) {
      return new Promise((resolve, reject) => {
        this.enqueue(async () => {
          try {
            let result = await HaikuUtils.updateHostFileIfNeeded(hostname, scanViaWAF, request, proxyPassDetails, logger);
            resolve(result);
          } catch (err) {
            reject(err);
          }
        });
      });
    }
  }
  
  module.exports = new HostFileQueue();
  