<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Generate Haiku Site Config</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.8.0/css/bulma.min.css">
    <link rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@creativebulma/bulma-collapsible@1.0.4/dist/css/bulma-collapsible.min.css">
    <script defer src="https://use.fontawesome.com/releases/v5.3.1/js/all.js"></script>
    <script defer src="https://use.fontawesome.com/releases/v5.3.1/js/all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@creativebulma/bulma-collapsible@1.0.4/dist/js/bulma-collapsible.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.20.0/min/vs/loader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"
        integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script>
    <script src="/utils/generate-haiku-site-config/gen-site-config.js"></script>
    <style>
        .vsContainer {
            height: 70vh;
            border: 1px solid grey
        }
    </style>
</head>

<body>
    <!-- bulma stuff -->
    <title>Generate Haiku Site Specific Config from recipe</title>

    <!-- Tabs -->
    <div class="tabs is-centered is-toggle is-toggle-rounded" style="margin-top: .5rem;margin-bottom: .5rem;">
        <ul>
            <li class="tab is-active" onclick="openTab(event, 'generate-site-config-tab')"><a>Site Config</a></li>
            <li class="tab" onclick="openTab(event, 'default-config-tab')"><a>Default Config</a></li>
        </ul>
    </div>


    <!-- generate site config tab -->
    <div id="generate-site-config-tab" style="margin-top: .5rem;margin-bottom: .5rem;padding-top:0"
        class="box content-tab">
        <div class="field is-grouped is-horizontal">
            <a class="navbar-item">
                Generate site specific config
            </a>
            <p class="control">
                <a id="generate" class="button is-link is-info" value="generate">Generate</a>
            </p>
        </div>

        <!-- columns -->
        <div class="columns gapless">
            <!--recipe column-->
            <div class="column is-6">
                <article class="message is-dark">
                    <div class="message-header">
                        <p>
                            <span class="icon">
                                <i class="fas fa-exchange-alt" aria-hidden="true"></i>
                            </span>
                            Recipe
                        </p>
                    </div>
                    <!-- Edit container  -->
                    <div class="message-body is-collapsible is-active">
                        <div id="edit-container" class="container vsContainer"></div>
                    </div>
                </article>
            </div>
            <div class="column is-6">
                <article id='site-config-accordion' class="message is-dark">
                    <div class="message-header">
                        <p>Site Specific Config</p>
                        <a href="#site-config-body" data-action="collapse" aria-label="expand/collapse">
                            <span class="icon">
                                <i class="fas fa-chevron-circle-down" aria-hidden="true"></i>
                            </span>
                        </a>
                    </div>
                    <div id="site-config-body" class="message-body is-collapsible is-active"
                        data-parent="site-config-accordion" data-allow-multiple="true">
                        <!-- generated site specific config -->
                        <div id="config-container" style="height: 50vh;" class="container vsContainer"></div>
                    </div>
                </article>
                <article id="site-diff-accordion" class="message is-dark">
                    <div class="message-header">
                        <p>Diff</p>
                        <a href="#site-diff-body" data-action="collapse" aria-label="expand/collapse">
                            <span class="icon">
                                <i class="fas fa-angle-down" aria-hidden="true"></i>
                            </span>
                        </a>
                    </div>
                    <div id="site-diff-body" class="message-body is-collapsible" data-parent="site-diff-accordion"
                        data-allow-multiple="true">
                        <!-- generated site specific config -->
                        <div id="config-changes-container" style="height: 50vh;" class="container vsContainer"></div>
                    </div>
                </article>
            </div>
        </div>
    </div>

    <div id="default-config-tab" class="message is-info content-tab"
        style="margin-top: .5rem;margin-bottom: .5rem;padding-top:0;display:none">
        <div class="message-header">
            <p>
                <span class="icon">
                    <i class="fa fa-cogs" aria-hidden="true"></i>
                </span>
                Default Network Scanner Config
            </p>
        </div>
        <!-- default config container  -->
        <div id="default-config-container" class="container" style="height:80vh"></div>
    </div>

    <!-- accordian stuff -->
    <script>
        bulmaCollapsible.attach('.is-collapsible')
    </script>

    <!-- bulma stuff -->

</body>

</html>