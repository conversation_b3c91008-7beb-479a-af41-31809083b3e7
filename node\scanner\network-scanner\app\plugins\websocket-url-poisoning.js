const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class WebSocketURLpoisoning extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-websocket-url-poisoning'
    }

    getAttackVectors() {
        return InjVectors
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['Referer', 'Host', 'Origin', 'X-Forwarded-Host', 'X-Forwarded-For', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded']
        })
    }

    getAttackableEvents() {
        return ['http-headers', 'form-encoded-post', 'uri-query-params']
    }

    /*
    async performNetworkAttack(attack) {
        let ResBody = _.get(attack, "result.resp.body")
        if (/wss?:\/\//i.test(ResBody)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }*/

    wantProcessAttackResponse(attack) {
        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }

        let ResBody = _.get(attack, "result.resp.body")
        if (/haikuWStest/i.test(ResBody) && /WebSocket/i.test(ResBody) && /wss?:\/\//i.test(ResBody)) {
            return true
        }
        return false
    }

    // event handler, annotates attack parameter, no return value
    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     * <script>
      function getImage(e) {
        var exampleSocket = new WebSocket("wss://localhost:8000", "dummyProtocol");
        exampleSocket.onopen = function (event) {
        exampleSocket.send("WebSocket is really cool");
        };
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.WSPoisonFound) {
            return
        }

        let ResBody = _.get(attack, "result.resp.body")
        let wsURL = ResBody.match(/\bwss?:\/\/.*haikuWStest/gi)
        // let wsURL = ResBody.match(/(?:'|")wss?:\/\/.+?;/gi)
        let dts = []
        if (/haikuWStest/i.test(wsURL)) {
            // if (/\bwss?:\/\//i.test(wsURL)) {
            for (let currVal of wsURL) {
                if (/haikuWStest/i.test(currVal)) {
                    // if (/wss?:\/\//i.test(currVal)) {
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `WebSocket details: ${currVal}`)
                    pluginDataForRequest.WSPoisonFound = true
                    return
                }
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["Origin", "Host"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}
// vectors & matches ...
const InjVectors = [
    `haikuWStest`,
    // `haikuWStest.apptrana.com`
]

module.exports = WebSocketURLpoisoning