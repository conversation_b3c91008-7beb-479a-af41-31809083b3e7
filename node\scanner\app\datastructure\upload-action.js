const utils = require('../ifc-utils.js')
const RealClickAction = require('./real-click-action');
const serializedName = utils.getRelativeModulePath(__filename)
const path = require('path')
const s3Utils = require('../../common/lib/s3-utils')

class UploadAction {
    constructor(xpath, filePath, annotation = '', fileType = 'text/plain') {
        this.xpath = xpath;
        this.filePath = filePath;
        this.annotation = annotation;
        this.fileType = fileType;
    }

    get actionType() {
        return 'upload';
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serializedName,
            args: [this.xpath, this.filePath, this.annotation]
        };
    }

    getXPath() {
        return this.xpath;
    }

    async execute(executionContext) {
        let browser = executionContext.browser;
        let executer = executionContext.executer;

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist");
            return false;
        }

        let encXPath = utils.encode(this.xpath);

        // Trigger a click action on the element before uploading the file
        //let actionSucceeded = await executer.triggerAction(new RealClickAction(this.getXPath()), executionContext);
        let actionSucceeded = true;
        if (actionSucceeded) {
            // let filePath = 'D:\\RD\\feature\\malware-file-upload\\node\\scanner\\your-file.txt'; // Replace 'your-file.txt' with the actual file name and extension
            //convert file lto blob format and pass to the below function
            //const fileContent = "This is a sample file content"; // Content of the file
            //const file = new File([fileContent], 'sample-file.txt', { type: 'text/plain' }); // Create a File object


            try {
                let resp = await s3Utils.getFile(path.dirname(this.filePath), path.basename(this.filePath));
                let fileContent = '';
                
                if (resp && resp.Body) {
                    fileContent = resp.Body.toString(); 

                    if(fileContent.length > 0) {
                        // let jscode = `indusfaceRenderer.uploadFile('${encXPath}', '${fileContent}','${path.basename(this.filePath)}', '${this.fileType}');`;
                        // await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true));

                            let jscode = `
                            indusfaceRenderer.uploadFile(
                                '${encXPath}', 
                                 ${JSON.stringify(fileContent)}, 
                                '${path.basename(this.filePath)}', 
                                '${this.fileType}'
                            );
                        `;
                        await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true));

                    }
                }
            } catch (err) {
                console.error(`Could not find upload file at ${this.filePath} - ${err.message}`);
            }
        }

        return actionSucceeded;
    }

    /**
      * flatten all actions 
      */
    flatten() {
        return this;
    }

    toString() {
        return `ACTION: ${this.actionType} '${this.filePath}' into ${this.annotation} xpath=<${this.xpath}>`;
    }
}

module.exports = UploadAction;