const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

class WebDavExtensions extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.WebDavExtensionsEnabled = 'ID-webdav-OPTIONS-enabled'
    }

    getAttackVectors() {
        return httpMethodVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * @param {method} attack
     * Overriding the performNetworkAttack method to change the redirect to false
     */
    async performNetworkAttack(attack) {
        if (attack.vector == 'PROPFIND') {
            attack.httpRequest.headers["Depth"] = 1
            return await super.performNetworkAttack(attack)

            /* attack.httpRequest.headers["Content-Type"] = `application/xml`
            attack.httpRequest.headers["Content-Length"] = 298
            attack.httpRequest.body = `<?xml version="1.0" encoding="utf-8"?>
            <propfind xmlns="DAV:">
            <prop>
                <getcontentlength xmlns="DAV:"/>
                <getlastmodified xmlns="DAV:"/>
                <executable xmlns="http://apache.org/dav/props/"/>
                <resourcetype xmlns="DAV:"/>
                <checked-in xmlns="DAV:"/>
                <checked-out xmlns="DAV:"/>
            </prop>
            </propfind>` */
        }
        else if (attack.vector == 'OPTIONS') {
            return await super.performNetworkAttack(attack)
        }
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode');
        let ResBody = _.get(attack, "result.resp.body")


        if (attack.vector == 'PROPFIND' && statusCode == 207 && /"DAV:"/i.test(ResBody)) { // HTTP/1.1 207 Multi-Status
            this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. StatusCode: 207 Multi-Status`)
        }
        else if (attack.vector == 'OPTIONS') {
            /**
             * HTTP/1.1 200 OK
            Allow: OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, COPY, MOVE, MKCOL, PROPFIND, PROPPATCH, LOCK, UNLOCK
            DAV: 1,2
             */

            //Normal
            let DAV = _.get(attack, 'result.resp.httpResponse.headers["DAV"]', false)
            let DAVRedirect = _.get(attack, 'result.resp.httpResponse.redirects[0].headers["DAV"]', false)
            // redirects
            let Reallow = _.get(attack, 'result.resp.httpResponse.redirects[0].headers["allow"]', false)
            let ReAccessallow = _.get(attack, 'result.resp.httpResponse.redirects[0].headers["access-control-allow-methods"]', false) //API - Access-Control-Allow-Methods

            //Normal
            let allowHeader = _.get(attack, 'result.resp.httpResponse.headers["allow"]', false)
            let Accessallow = _.get(attack, 'result.resp.httpResponse.headers["access-control-allow-methods"]', false) //API - Access-Control-Allow-Methods

            /**
             * If the response has ALLOW header then it will add it to the vulnerability list
             */
            if (Reallow || ReAccessallow || allowHeader || Accessallow) {
                if (Reallow && /COPY|MOVE|MKCOL|PROPFIND|PROPPATCH|LOCK|UNLOCK/i.test(Reallow)) {
                    this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. allow: ${Reallow}`)
                } else if (ReAccessallow && /COPY|MOVE|MKCOL|PROPFIND|PROPPATCH|LOCK|UNLOCK/i.test(ReAccessallow)) {
                    this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. access-control-allow-methods: ${ReAccessallow}`)
                } else if (allowHeader && /COPY|MOVE|MKCOL|PROPFIND|PROPPATCH|LOCK|UNLOCK/i.test(allowHeader)) {
                    this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. allow: ${allowHeader}`)
                } else if (Accessallow && /COPY|MOVE|MKCOL|PROPFIND|PROPPATCH|LOCK|UNLOCK/i.test(Accessallow)) {
                    this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. access-control-allow-methods: ${Accessallow}`)
                }
            }
            else if (DAV || DAVRedirect) {
                if (DAV) {
                    this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. DAV: ${DAV}`)
                }
                else if (DAVRedirect) {
                    this.addVulnerabilitytoResult(attack, this.WebDavExtensionsEnabled, `WebDav Extensions are Enabled. DAV: ${DAVRedirect}`)
                }
            }
        }
    }
}

const httpMethodVectors = [
    `OPTIONS`,
    `PROPFIND`,
]

module.exports = WebDavExtensions