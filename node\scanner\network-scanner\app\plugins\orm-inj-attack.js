const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * ORM Injection Attack Plugin
 * 
 * Description:
 * ORM Injection occurs when user-controlled input is improperly sanitized and directly used in ORM (Object-Relational Mapping) queries. 
 * This can lead to unauthorized data access, privilege escalation, and even remote code execution in certain cases.
 * 
 * Severity: High
 * CWE ID: CWE-707 (Improper Neutralization)
 * OWASP Categories: A03:2021 (Injection)
 * CVSS Score: 8.8
 * CVSS Vector: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:L
 */
class ORMInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-orm-injection'
        // Pre-compile regex patterns
        this.excludeURLRegex = /\/(?:phpinfo|info)\.php|\/(?:blog|docs?|learning|readme|changelog|license|contributing|api-docs?|documentation|help|support|guide|tutorial|faq)\/|\.(?:txt|md|rst|pdf)$/i

        // ORM-specific error patterns with unique keywordsv to check HTTP response and confirm vulnerabilities.
        this.frameworkErrors = [
            // SQLAlchemy - More specific context
            /(?:SQLAlchemy|ORM)\s*.*?sqlalchemy\.exc\.[A-Za-z]+(?:Error|Exception)/i,

            // TypeORM-specific errors
            /(?:TypeORM)\s*.*?typeorm\.error\.[A-Za-z]+(?:Error|Exception)/i,

            // Knex.js-specific errors
            /(?:Knex)\s*.*?knex\.[A-Za-z]+(?:Error|Exception)/i,

            // Hibernate - More specific context
            /Hibernate\s*.*?org\.hibernate\.[A-Za-z]+(?:Error|Exception)/i,

            // Java SQL - More specific context and error types
            /(?:JPA|Hibernate|ORM)\s*.*?java\.sql\.SQLSyntaxErrorException/i,
            /(?:JPA|Hibernate|ORM)\s*.*?javax\.persistence\.PersistenceException/i,

            // .NET Entity Framework - More specific context
            /(?:EntityFramework|EF Core)\s*.*?System\.Data\.SqlClient\.SqlException/i,
            /(?:EntityFramework|EF Core)\s*.*?System\.Data\.Entity\.Core\.EntityCommandExecutionException/i,
            /(?:EntityFramework|EF Core)\s*.*?Microsoft\.EntityFrameworkCore\.DbUpdateException/i,
            /(?:EntityFramework|EF6|EFCore)\s*.*?System\.Data\.Entity\.[A-Za-z]+(?:Error|Exception)/i,

            // Database structure errors - More specific context
            /(?:Database|ORM|Entity)\s*.*?(?<!no )valid (?:column|table|query)/i,

            // Django - More specific context and error types
            /(?:Django|ORM)\s*.*?django\.db\.utils\.OperationalError\b/i,
            /(?:Django|ORM)\s*.*?django\.core\.exceptions\.FieldError\b/i,
            /(?:Django|ORM)\s*.*?django\.db\.utils\.IntegrityError\b/i,
            /(?:Django|ORM)\s*.*?django\.db\.utils\.DataError\b/i,
            /(?:Django|ORM)\s*.*?UniqueConstraintError\b/i,
            /(?:Django|ORM)\s*.*?ValidationError\b/i,
            /(?:Django|ORM)\s*.*?QueryException\b/i,
            /(?:Django|ORM)\s*.*?ORMException\b/i,

            // Sequelize - More specific context
            /Sequelize[A-Za-z]+(?:Error|Exception)/i,

            // Prisma - More specific context
            /PrismaClient[A-Za-z]+(?:Error|Exception)/i
        ];

        this.dbStructurePatterns = [
            // General ORM errors
            /Error: ORM operation failed/i,
            /Exception: ORM encountered an error/i,
            /Failed to initialize ORM session/i,
            /Invalid entity state in ORM/i,
            /Error: Entity repository not found/i,

            // Query Builder & Execution errors
            /QueryFailedException: Malformed query in QueryBuilder/i,
            /QueryBuilder Error: Unexpected token/i,
            /Query execution failed in ORM/i,
            /Error: Invalid query parameters in ORM/i,
            /Exception: Query binding error in ORM/i,

            // Lazy & Eager Loading errors
            /LazyLoadingException: Entity is detached/i,
            /EagerLoadingException: Failed to load related entities/i,
            /Error: Hydration failed in ORM/i,
            /Exception: Detached entity passed to persist/i,

            // Transaction & Persistence errors
            /TransactionRequiredException: Commit failed/i,
            /TransactionFailedException: Rollback failed/i,
            /Error: ORM transaction could not be completed/i,
            /PersistenceException: Failed to save entity/i,
            /Exception: ORM failed to persist entity/i,

            // ORM framework-specific errors
            /SequelizeError: Unable to execute query/i,
            /TypeORM Error: Invalid column reference/i,
            /DoctrineException: QueryBuilder error/i,
            /Eloquent Error: Failed to load model/i,
            /HibernateException: Unable to initialize session/i,

            // ORM Lifecycle & State Management Issues
            /UnitOfWorkException: ORM failed to track entity/i,
            /DetachedEntityException: Entity is not managed/i,
            /Error: Hydration failed in ORM/i,

            // More Query Processing Errors
            /QueryCompilationException: Unable to compile ORM query/i,
            /BindingError: Failed to bind parameters in QueryBuilder/i,
            /UnexpectedTokenException: Query parsing failed/i,

            //Persistence Layer & Transaction Failures
            /RollbackException: ORM transaction rolled back unexpectedly/i,
            /TransactionRequiredException: Commit failed in ORM/i,
            /PersistenceException: ORM failed to save entity/i,

            //CriteriaBuilder (Java-based ORMs like Hibernate)
            /CriteriaBuilderException: Invalid criteria query/i,
            /Error: CriteriaBuilder could not create query/i,

            //ActiveRecord (Rails ORM)
            /ActiveRecord::StatementInvalid: Invalid query/i,
            /ActiveRecord Error: Failed to save entity/i,

            //SQLAlchemy (Python ORM)
            /SQLAlchemyError: Invalid query or parameter/i,
            /SQLAlchemy Exception: Unable to execute query/i,

            //Invalid Parameter Handling
            /InvalidParameterException: Invalid query parameter/i,
            /Error: Invalid parameter passed to ORM/i,
            /InvalidParameterError: Failed to bind parameter/i,
        ]


        // Replace separate confidence level arrays with a single Set
        this.ConfidenceHeaders = new Set([
            'x-sequelize-error',
            'x-typeorm-error',
            'x-prisma-error',
            'x-hibernate-error',
            'x-sqlalchemy-error',
            'x-django-orm-error',
            'x-entityframework-error',
            'x-active-record-error',
            'x-orm-error',
            'x-db-query-error',
            'x-db-validation-error',
            'x-sql-query-error',
            'x-transaction-error',
            'x-repository-error',
            'x-db-connection',
            'x-db-pool',
            'x-db-transaction',
            'x-db-lock',
            'x-db-migration',
            'x-db-retry',
            'x-db-timeout',
            'x-db-error',
            'x-orm-error',
            'x-orm-exception',
        ]);
    }

    getAttackVectors() {
        return InjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'form-encoded-post', 'json-body', 'uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        if (this.excludeURLRegex.test(attack.httpRequest.uri)) {
            return false
        }
        return await super.performNetworkAttack(attack)
    }

    analyzeResponseStatus(attack) {
        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 0)
        const originalStatus = _.get(attack, 'originalRequest.httpResponse.statusCode', '')

        return statusCode !== originalStatus && (statusCode === 500 || statusCode === 503 || statusCode === 504)
    }

    processAttackResponse(attack) {
        if (attack.pluginName !== this.getName()) return;
        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.ORMtInjFound) return;

        let responseBody = _.get(attack, "result.resp.body", "").trim();
        if (responseBody.length < 10) return;

        // Add maximum response size limit
        const MAX_RESPONSE_SIZE = 100000;
        if (responseBody.length > MAX_RESPONSE_SIZE) {
            responseBody = responseBody.substring(0, MAX_RESPONSE_SIZE);
        }

        // Use isLikelyORMError for initial validation
        const headers = _.get(attack, 'result.resp.httpResponse.headers', {});
        if (!this.isLikelyORMError(responseBody, headers)) return;

        let matchedErrorSection = null;
        let matchedDbPattern = null;
        let matchedHeaders = null;

        // Check for ORM errors
        for (const pattern of this.frameworkErrors) {
            const match = responseBody.match(pattern);
            if (match) {
                matchedErrorSection = match[0];
                break;
            }
        }

        if (!matchedErrorSection) {
            // Optimize header check
            const headers = _.get(attack, 'result.resp.httpResponse.headers', {});
            // More efficient header matching using Set
            const matchedHeadersList = Object.keys(headers)
                .filter(header => this.ConfidenceHeaders.has(header))
                .map(header => `${header}: ${headers[header]}`);

            matchedHeaders = matchedHeadersList.length > 0 ? matchedHeadersList.join(', ') : null;

            // Check for database structure patterns only if headers match
            if (matchedHeaders || this.analyzeResponseStatus(attack)) {
                for (const pattern of this.dbStructurePatterns) {
                    const match = responseBody.match(pattern);
                    if (match) {
                        matchedDbPattern = match[0];
                        break;
                    }
                }
            }
        }

        // 3. Optimize evidence collection
        if (matchedErrorSection || matchedDbPattern) {
            const evidence = [];
            const isHighConfidence = !!matchedErrorSection;

            if (matchedErrorSection) {
                evidence.push(`ORM Error: ${matchedErrorSection}`);
            } else {
                evidence.push(`Database Structure: ${matchedDbPattern}`);
                if (matchedHeaders) evidence.push(`Suspicious Headers: ${matchedHeaders}`);
            }

            const vulnerabilityDetails = {
                details: `${isHighConfidence ? 'ORM' : 'Potential ORM'} Injection Vulnerability Detected. Evidence: Suspicious details: ${evidence.join(', ')}. Confidence: ${isHighConfidence ? 'High' : 'Medium'}`
            };

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnerabilityDetails);
            pluginDataForRequest.ORMtInjFound = true;
        }
    }

    // Add isLikelyORMError as a class method
    isLikelyORMError(responseBody, headers) {
        // Check for ORM-specific headers
        const hasErrorHeader = Object.keys(headers)
            .filter(header => this.ConfidenceHeaders.has(header))
            .map(header => `${header}: ${headers[header]}`);

        // Check for error stack traces and details
        const hasErrorStack = responseBody.includes('stack trace') ||
            responseBody.includes('error details') ||
            responseBody.includes('exception') ||
            responseBody.includes('error occurred');

        // Check for ORM context with more specific terms
        const hasORMContext = responseBody.includes('ORM') ||
            responseBody.includes('Entity') ||
            responseBody.includes('Model') ||
            responseBody.includes('Repository') ||
            responseBody.includes('Query') ||
            responseBody.includes('Database') ||
            responseBody.includes('SQLAlchemy') ||
            responseBody.includes('Hibernate') ||
            responseBody.includes('Django') ||
            responseBody.includes('EntityFramework');

        // Check for specific error patterns
        const hasSpecificError = responseBody.includes('sqlalchemy.exc.') ||
            responseBody.includes('org.hibernate.') ||
            responseBody.includes('django.db.') ||
            responseBody.includes('System.Data.') ||
            responseBody.includes('javax.persistence.') ||
            responseBody.includes('Sequelize') ||
            responseBody.includes('PrismaClient') ||
            responseBody.includes('SQLSyntaxErrorException') ||
            responseBody.includes('django.core');
        // Return true if we have either:
        // 1. Error header with ORM context
        // 2. Error stack with ORM context
        // 3. Specific error pattern with any context
        return (hasErrorHeader && hasORMContext) ||
            (hasErrorStack && hasORMContext) ||
            (hasSpecificError && (hasORMContext || hasErrorStack));
    }
}

// ORM Injection Payloads covering modern ORM frameworks
const InjVectors = [
    // Core SQL Injection Tests - Most common patterns
    "' OR '1'='1",                    // Basic boolean-based
    "' UNION SELECT null, username, password FROM users --",  // Union-based
    "' AND 1=(SELECT COUNT(*) FROM users)",  // Subquery-based

    // Modern ORM Framework Payloads - Unique patterns per framework
    // Sequelize/TypeORM/Prisma (Node.js) - JSON-based injection
    `{"where": {"username": {"$eq": "admin"}}}`,  // Covers basic where clause
    `{"$in": ["admin", "root"]}`,                // Covers array operations
    `{"$regex": ".*"}`,                          // Covers regex operations
    `{"include": {"*": true}}`,                  // Covers eager loading

    // SQLAlchemy (Python) - JSON-based injection
    `{"$where": "this.password.length > 1"}`,   // Covers where clause with function
    `{"$ne": None}`,                            // Covers null checks
    `{"$in": [1, 2, 3]}`,                       // Covers numeric arrays

    // Hibernate (Java) - SQL-based injection
    "' OR 1=1 --",                              // Covers basic boolean
    "' AND EXISTS (SELECT 1 FROM users)",       // Covers exists clause
    "' AND 1=(SELECT COUNT(*) FROM users WHERE username='admin')",  // Covers subquery with condition

    // NoSQL Injection - MongoDB-style operators
    `{"$gt": {"$ne": null}}`,                   // Covers comparison operators
    `{"$exists": true}`,                        // Covers existence checks
    `{"$regex": ".*"}`,                         // Covers regex operations

    // Complex ORM Query Payloads - Advanced patterns
    `{"where": {"$or": [{"username": "admin"}, {"role": "admin"}]}}`,  // Covers OR conditions
    `{"select": ["*"], "where": {"active": true}, "order": {"createdAt": "DESC"}}`  // Covers complex queries
]

module.exports = ORMInjAttack