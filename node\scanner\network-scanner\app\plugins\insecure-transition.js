const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class InsecureTransition extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        // Insecure transition from HTTPS to HTTP in form post Severity - LOW
        this.InsecureTransitionhttps = 'ID-insecure-transition-from-https'

        // Insecure transition from HTTP to HTTPS in form post Severity - MEDIUM
        this.InsecureTransitionhttp = 'ID-insecure-transition-from-http'
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea == "original-crawler-request") {
            let redirect = attack.result.resp.httpResponse.redirects.length
            if (redirect > 0) {//TO-10129 - Fix based on custom rule.
                let proto = _.get(attack, 'result.req.parsedURL.protocol');
                let redirectedURI = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri')
                if (!redirectedURI.includes(proto)) { return false }
            }
            let body = _.get(attack, "result.resp.body")
            let regexpAction = /< ?form.+action ?= ?('|")https?:/i
            let regexpMethod = /< ?form.+method ?= ?('|")post/i
            if (regexpAction.test(body) && regexpMethod.test(body)) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //if vuln detected for a req then return
        if (pluginDataForRequest.HttpVulnFound && pluginDataForRequest.HttpsVulnFound) {
            return
        }

        // For AA
        let details = []
        let proto = _.get(attack, 'result.req.parsedURL.protocol');
        let regexp = /< ?form[\w\W]+?< ?\/form ?>/gi
        let Resbody = _.get(attack, "result.resp.body")
        let formtag = []
        try {
            formtag = Resbody.match(regexp)
            if (formtag.length > 0 && /action ?= ?('|")https?:/i.test(formtag)) {
                for (let tag of formtag) {
                    if (/action ?= ?('|")https?:/i.test(tag) && /method ?= ?('|")post/i.test(tag)) {
                        let actionval = tag.match(/action ?= ?('|").+?\1/gi)[0]
                        if (proto == 'http:' && actionval.includes('https')) {
                            details.push({ result: actionval })
                        }
                        else if (proto == 'https:' && actionval.includes('http:')) {
                            details.push({ result: actionval })
                        }
                    }
                }
                if (details.length > 0 && proto == 'http:') {
                    this.addVulnerabilitytoResult(attack, this.InsecureTransitionhttp, details)
                    pluginDataForRequest.HttpVulnFound = true
                }
                else if (details.length > 0 && proto == 'https:') {
                    this.addVulnerabilitytoResult(attack, this.InsecureTransitionhttps, details)
                    pluginDataForRequest.HttpsVulnFound = true
                }
            }
        }
        catch (e) {
            return
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.InsecureTransitionhttp) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['action="https://']);
    }
}

module.exports = InsecureTransition