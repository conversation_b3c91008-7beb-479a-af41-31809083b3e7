const NetworkAttack = require('./network-attack');
const _ = require('lodash');

const trojanShell = [
    /\bcurl\s+-fsSL\s+http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\w+\.sh\b|\bnc\s+-e\s+\/bin\/bash\s+\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/,
    /\b(netcat|ncat|nc|nc\.exe)\s+\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\s+\d{1,5}\s+-e\s+sh\b/,
    /\b(bash\s+-i\s+>&\s+\/dev\/tcp\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\d{1,5}|sh\s+-i\s+>&\s+\/dev\/tcp\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\d{1,5})\b/,
    /\b(telnet\s+\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\s+\d{1,5}|nc\s+\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\s+\d{1,5})\b/,
    /\b(php\s+-r\s+'\\$sock\s=\s(fsockopen|stream_socket_client)\("tcp:\/\/\\$ip\\$port"\);\\$proc\s=\sproc_open\("\/bin\/sh\s+-i",\sarray\(0=>\\$sock,1=>\\$sock,2=>\\$sock\),\\$pipes\);foreach\(\\$pipes\sas\s\\$pipe\)\sfwrite\(\\$pipe,\s"\\$cmd\\n"\);fclose\(\\$sock\);')\b/,
    /\b(perl\s+-e\s+'use\sSocket;\$i="\\$ip";\$p=\\$port;socket\(S,PF_INET,SOCK_STREAM,getprotobyname\("tcp"\)\);if\(connect\(S,sockaddr_in\(\$p,inet_aton\(\$i\)\)\)\{open\(STDIN,">&S"\);open\(STDOUT,">&S"\);open\(STDERR,">&S"\);exec\("\/bin\/sh\s+-i"\);\};')\b/,
    /\b(ruby\s+-rsocket\s+-e\s+'f=TCPSocket.open\("[0-9]{1,3}(\.[0-9]{1,3}){3}",\d{1,5}\).to_i;exec sprintf\("\/bin\/sh -i <&%d >&%d 2>&%d",f,f,f\)\')\b/,
    /\b(java\s+-jar\s+commons-collections-\d+\.\d+\.\d+\.jar\s+"http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}\/")\b/,
    /\b(java\s+-jar\s+\S+\s+\"?http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}:\d{1,5}\/\"?)\b/,
    /\b(wget\s+http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\w+\.sh\s+-O\s-|curl\s+-fsSL\s+http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\w+\.sh\s+\|\s+bash)\b/,
    /\b(powershell\s+-NoP\s+-NonI\s+-W Hidden\s+-Exec Bypass\s+-Command\s+"IEX\(New-Object Net.WebClient\).DownloadString\('http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\w+\.ps1'\)")\b/,
    /\b(curl\s+-s\s+http:\/\/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\/\w+\.sh\s+\|\s+bash)\b/,
]

class TrojanShellScript extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner);
        this.vulnerabilityID = 'ID-trojan-shell-script-detected';

        // Using PascalCase for regexPattern
        this.regexPattern = new RegExp(trojanShell.map(v => v.source).join('|'), 'ig');
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        this.detectTrojanShellScript(originalRequest);
    }

    detectTrojanShellScript(originalRequest) {
        let bodyCheck = _.get(originalRequest, 'result.resp.body');

        // Ensure the condition makes sense based on your application logic
        if (originalRequest.result.resp.httpResponse.err) {
            return;
        }

        // Detect trojan shell scripts using the regex pattern
        let matches = [];
        let match;
        while ((match = this.regexPattern.exec(bodyCheck)) !== null) {
            matches.push(`${match[0]}`);
        }

        if (matches.length > 0) {
            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, matches.join('\n'));
        }
    }
}

module.exports = TrojanShellScript;