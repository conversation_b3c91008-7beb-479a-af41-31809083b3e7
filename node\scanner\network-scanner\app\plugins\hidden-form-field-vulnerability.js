const debug = require('debug')('HiddenFormFieldVulnerability')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class HiddenFormFieldVulnerability extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-hidden-form-input-field'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {

        // detect if input type type hidden is present in body, if found then call processAttackResponse
        let body = _.get(attack, "result.resp.body")
        if (/<input\stype=hidden\s|<input\stype="hidden"\s/i.test(body)) {
            return true
        }
        return false
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.hiddenFormInputFieldVulnFound) {
            return
        }
        let vuln
        let $ = _.get(attack, 'result.resp.httpResponse.cheerio');
        let details = []
        let forms = $("form")
        //let details
        if (forms.length > 0) {
            let hiddenInputField = $('input[type=hidden]', forms)

            if (hiddenInputField.length > 0) {
                hiddenInputField.each(function (index) {
                    if (/price/i.test($(this).attr('name'))) {
                        details.push({
                            name: $(this).attr('name'),
                            inputType: "hidden",
                            dom_element: $.html(this)
                        })
                    }
                })
                vuln = {
                    details: details
                }
            }
        }
        // vuln is an array so have to update condition from only vuln to vuln.length > 0
        if (vuln && vuln.details.length > 0) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginDataForRequest.hiddenFormInputFieldVulnFound = true
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['<input', 'type="hidden"', 'price']);
    }
}
module.exports = HiddenFormFieldVulnerability