// Version: v0.51
// Override require for 're2' to use our memory-efficient replacement
require('./require-override');

const electron = require('electron')
const { dialog } = electron; // Assuming 'app' might be needed later, ensure it's from 'electron'
const path = require('path')
const fs = require('fs')
const URL = require('url').URL
const urlPkg = require('url')
const mkdirp = require('mkdirp')
const ssAPI = require('../common/lib/sooper-scheduler-api')
const ParseDomain = require('parse-domain')
const RabbitMQ = require('../common/lib/rabbitmq')
const GetSitemapUrlsRpc = require('../common/lib/messages/get-sitemap-urls-rpc')
const CrawlStartedRpc = require('../common/lib/messages/crawl-started-rpc')
const CrawlFinished = require('../common/lib/messages/crawl-finished')
const CrawlStartedPublish = require('../common/lib/messages/crawl-started-publish')
const s3Utils = require('../common/lib/s3-utils')
const utils = require('./ifc-utils.js')
const HaikuUtils = require('../common/lib/haiku-utils')
const { SCAN_TYPES, MACHINE_TYPES, SSO_URLS } = require('../common/config/app-constants')
const _ = require('lodash')
const os = require('os')
let argvGlobal = require('minimist')(process.argv.slice(2))

const logger = require('../common/lib/haiku-logger.js')
logger.setMetadata({
    haikuProcess: MACHINE_TYPES.CRAWLER
})

// Module to control application.
const app = electron.app
const ipcMain = electron.ipcMain;

// set needed chromium command line switches
app.commandLine.appendSwitch('disable-xss-auditor') // disable browser check for XSS. 
//app.commandLine.appendSwitch('disable-popup-blocking') // disable popup blocking 
//app.commandLine.appendSwitch('proxy-server', '127.0.0.1:8080')

if(argvGlobal.hostMapEntries) {
    app.commandLine.appendSwitch('host-rules', argvGlobal.hostMapEntries);
    logger.info(`Host rules pushed - ${argvGlobal.hostMapEntries}`, {
        scanId: argvGlobal.id
    });
}

argvGlobal.disableDialogPopup = argvGlobal.disableDialogPopup === 'true' ? true : false;

/**  Dialog code */
if (argvGlobal.disableDialogPopup) {
    try {
        // --- Intercept dialog.showMessageBox ---
        let originalShowMessageBox = dialog.showMessageBox;
        dialog.showMessageBox = function (browserWindow, options, callback) {
            // Handle cases where browserWindow is omitted and options is the first argument
            let actualOptions = options;
            let actualCallback = callback;
            let actualBrowserWindow = browserWindow;

            if (typeof browserWindow === 'object' && browserWindow !== null &&
                (typeof browserWindow.message === 'string' || typeof browserWindow.type === 'string')) {
                // Looks like browserWindow is actually the options object
                actualOptions = browserWindow;
                actualCallback = options; // options would be the callback here
                actualBrowserWindow = null; // No browserWindow was passed
            } else if (browserWindow && browserWindow.constructor.name !== 'BrowserWindow' && browserWindow.constructor.name !== 'Object') {
                // If browserWindow is not a BrowserWindow instance and not an options object,
                // it might be an older Electron version or an unexpected call.
                // Let's assume it's options if it has a message property.
                if (typeof browserWindow.message === 'string' || typeof browserWindow.type === 'string') {
                    actualOptions = browserWindow;
                    actualCallback = options;
                    actualBrowserWindow = null;
                } else {
                    // Fallback or error if arguments are unexpected
                    //console.error('showMessageBox called with unexpected arguments', browserWindow, options);
                    // Proceed with original to avoid breaking unrelated dialogs completely
                    if (actualCallback && typeof actualCallback === 'function') {
                        return originalShowMessageBox.call(dialog, actualBrowserWindow, actualOptions, actualCallback);
                    } else {
                        return originalShowMessageBox.call(dialog, actualBrowserWindow, actualOptions);
                    }
                }
            }

            //console.log('Intercepted dialog.showMessageBox call. Message:', actualOptions ? actualOptions.message : 'N/A');

            if (actualOptions && actualOptions.message) {
                //console.log('DSCHandler dialog intercepted and suppressed in main.js (async)!');
                if (actualCallback && typeof actualCallback === 'function') {
                    actualCallback(0); // For older Electron versions
                    return Promise.resolve({ response: 0, checkboxChecked: false }); // Modern return
                } else {
                    // Sync behavior if no callback, or for versions that don't use callback for async
                    return Promise.resolve({ response: 0, checkboxChecked: false }); // Modern return for async
                }
            }

            // If it's not the dialog we want to suppress, call the original function
            if (actualCallback && typeof actualCallback === 'function') {
                return originalShowMessageBox.call(dialog, actualBrowserWindow, actualOptions, actualCallback);
            } else {
                // This handles the case where showMessageBox is called without a callback (returns a Promise)
                // or the synchronous version if arguments align (though that's showMessageBoxSync)
                return originalShowMessageBox.call(dialog, actualBrowserWindow, actualOptions);
            }
        };

        // --- Intercept dialog.showMessageBoxSync ---
        let originalShowMessageBoxSync = dialog.showMessageBoxSync;
        dialog.showMessageBoxSync = function (browserWindow, options) {
            let actualOptions = options;
            let actualBrowserWindow = browserWindow;

            // Check if the first argument is options (browserWindow omitted)
            if (typeof browserWindow === 'object' && browserWindow !== null &&
                (typeof browserWindow.message === 'string' || typeof browserWindow.type === 'string') &&
                (browserWindow.constructor.name !== 'BrowserWindow')) {
                actualOptions = browserWindow;
                actualBrowserWindow = null; // No browserWindow was passed or it was the options object
            }


            //console.log('Intercepted dialog.showMessageBoxSync call. Message:', actualOptions ? actualOptions.message : 'N/A');
            if (actualOptions && actualOptions.message && actualOptions.message.includes('DSCHandler')) {
                //console.log('DSCHandler dialog (sync) intercepted and suppressed in main.js!');
                return 0; // Default button index (typically "OK")
            }

            // If it's not the dialog, call the original.
            // The originalSync might be called with (options) or (browserWindow, options)
            if (actualBrowserWindow && actualBrowserWindow.constructor.name === 'BrowserWindow') {
                return originalShowMessageBoxSync.call(dialog, actualBrowserWindow, actualOptions);
            } else {
                // First argument was options, or no browser window
                return originalShowMessageBoxSync.call(dialog, actualOptions);
            }
        };
        // --- End of Dialog Interception Code ---
    } catch (error) {
        console.error('Error in dialog interception code:', error);
    }
}
/**  End - Dialog code */

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
const MainUI = require('./ui/mainUI.js')
let mainUI

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', function () {
    appMain()
})

// Quit when all windows are closed.
app.on('window-all-closed', function () {
    // On OS X it is common for applications and their menu bar
    // to stay active until the user quits explicitly with Cmd + Q
    // since this is a scanner window, quit when scanis done
    //if (process.platform !== 'darwin') {
    app.quit()
    //}
    if (mainUI) {
        mainUI.rendererWindow = null
    }
})

app.on('activate', function () {
    // On OS X it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (mainUI && mainUI.rendererWindow === null) {
        mainUI.createUIWindow()
    }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.

//-----------------------------------
// utils
// hook logging events. May make sense to move this to a logger class later
ipcMain.on('renderer-log', utils.onRendererLog);

// args :
// --interactive for interactive mode
// --id=<id> for running scan for given id, all config params will be retreived from portal using API.
// --resume={truthy} to resume a previously paused scan 
function appMain() {
    let argv = require('minimist')(process.argv.slice(2))
    if (argv.interactive) {
        mainUI = new MainUI(electron.BrowserWindow)
        mainUI.createUIWindow()
    } else if (argv.id) {
        // go straight to starting the scan
        logger.addMetadata({
            scanId: argv.id
        })
        logger.log('info', `Starting scan for id: ${argv.id}`)
        startScan(argv).catch((err) => {
            logger.log('error', 'Whoa Whoa Whoa !!!. Exiting!! Error caught in appMain: ', err)
            app.exit(2)
        })
    } else {
        logger.log('error', '--interactive OR --id=<id> must be specified')
        app.exit(1)
    }
}

//-----------------------------------
// scanner
ipcMain.on('start-crawl', (ev, args) => {
    startScan({
        id: args.siteId
    }) // eventually get here, for now use the url, max depth etc from UI
});

async function setupForScan(config, {
    id,
    scanLogId,
    resumeScan,
    outFile
}, resp) {
    // set up common params and log directories
    config.scanId = id
    config.scanLogId = scanLogId
    config.resumeScan = resumeScan
    config.outFile = outFile
    config.logPath = path.join(__dirname, `../logs/${config.scanId}/${config.scanLogId} ${config.parsedUrl.hostname}/`)
    config.scrPath = `${config.logPath}/screenshots/`;

    // S3 out prefix
    let now = new Date()
    let timestamp = [now.getUTCFullYear(), now.getUTCMonth() + 1, now.getUTCDate()].map(c => c.toString().padStart(2, '0')).join('-')
    config.s3OutPrefix = (`crawler/logs/${timestamp}/${config.scanLogId}-${config.parsedUrl.hostname}/`)
    await mkdirp.sync(config.scrPath);

    config.msgQ = this.msgQ // really need to find a better way to share data than 'config'

    // if scanner has reflected parans like addToRequest, ensure we are using it
    if (resp.addToRequest && Object.keys(config.addToRequest).length < 1) { // if this is set for crawler, do not overwrite it.
        config.addToRequest = resp.addToRequest
    }

    // skip crawl means no crawl tree iteration, just stay around to refresh request
    if (!config.isApiScan && resp.skipCrawl) {
        config.skipCrawl = resp.skipCrawl
    }

    // Add the additional initial crawl actions & seed Urls
    await addInitialCrawlActions(config)
    addSeedUrls(config)
}

async function addInitialCrawlActions(config) {
    // add all the init URLs to the initial crawl actions
    // add the load action to the initial crawl actions as the first action
    let parsedUrl = new URL(config.mainUrl)
    let initUrls = [config.mainUrl, ...config.initialCrawlUrls]
    // make sure we always process the init URLs specified by customer first and dont 
    // skip them in the 'max actions per state' type processing
    let specifiedInitUrlsPos = initUrls.length

    // Waf access log analysis URLs and sitemap URLs
    for (let urls of await Promise.all([getUrlsFromWafAccessLogAnalysis(config), getSitemapUrls(config)])) {
        // remove already added elements from the urls and respect max
        urls = urls.filter(u => !initUrls.includes(u)).slice(0, config.maxInitialUrlsPerExternalSource)

        initUrls.push(...urls)
    }

    // get all subpaths as well
    let initUrlsSubPaths = []
    for (let link of initUrls) {
        let linkFull = new URL(link, config.parsedUrl.href)
        let pathComponents = linkFull.pathname.split('/')
        if (linkFull.pathname.endsWith('/')) {
            pathComponents.pop()    // get rid of last empty path component when url ends with /
        }
        for (let i = 1; i < pathComponents.length - 1; i++) {
            linkFull.pathname = pathComponents.slice(1, i + 1).join('/')
            initUrlsSubPaths.push(linkFull.href)
        }
    }

    // normalize the URLs and make a unique array
    initUrls = _.uniq([...initUrls, ...initUrlsSubPaths].map(u => utils.normalizeUrl(u, config.parsedUrl.href, {
        stripWWW: config.stripWWW,
        stripHash: false
    }).href));

    // create wizard guides for all initial URLs
    logger.log('info', `Creating wizard guides for ${initUrls.length} initial urls (including sitemap & WAF)`)
    initUrls.forEach((url, idx) => {
        let mustRunAtInit = idx < specifiedInitUrlsPos
        config.initialWizardGuides.push({
            name: 'initial actions',
            mustRunAtInit,
            actions: [{
                action: 'load',
                uri: url,
                xpath: "[initial]",
                annotation: `initial, mustRunAtInit: ${mustRunAtInit}`
            }]
        })
    })


}

function addSeedUrls(config) {
    if(config.scanType == SCAN_TYPES.DEFACEMENT) { 
        logger.log('info', `Excluding post seed urls from the ${SCAN_TYPES.DEFACEMENT} scan.`);
        config.seedUrls = [];
        return;
    }

    if(config.excludePostSeedUrls || config.apiDiscovery) {
        logger.log('info', `Excluding post seed urls from the scan.`);
        config.seedUrls = [];
        return;
    }
      
    // seed url paths
    let seedURLS = require('./post-crawl-seed-urls')
    seedURLS = seedURLS.slice(0, config.maxSeedUrls)

    // normalize the URLs and make a unique array
    seedURLS = _.uniq(seedURLS.map(u => utils.normalizeUrl(u, config.parsedUrl.href, {
        stripWWW: config.stripWWW
    }).href));

    // make a unique array
    logger.log('info', `Seed URLs to use at end of crawl: ${seedURLS.length} URLs`)

    config.seedUrls = seedURLS
}

async function getSitemapUrls(config) {
    // if defacement retun empty array
    if(config.scanType == SCAN_TYPES.DEFACEMENT)
        return [];

    // Get sitemap URLs - wait 5 minutes tops.
    let sitemapUrls = []
    try {
        // Ask network scanner to get list of sitemap URLs from sitemap.xml file(s)
        let getSitemapUrls = {
            scanId: config.scanId,
            scanlog_id: config.scanLogId,
            scanner: 'haiku',
            mainUrl: config.mainUrl,
            maxSitemapLinks: config.maxInitialUrlsPerExternalSource,
            scannerMachineUID: config.scannerMachineUID
        }

        let getSitemapUrlsReq = new GetSitemapUrlsRpc(getSitemapUrls)
        let response = await getSitemapUrlsReq.rpcRequest(this.msgQ, 5 * 60 * 1000)
        if (response && response.sitemapUrls) {
            sitemapUrls = response.sitemapUrls
        }
    } catch (err) {
        logger.log('error', `Could not get sitemap URls : ${err.toString()}`)
        sitemapUrls = []
    }

    return sitemapUrls
}

async function getUrlsFromWafAccessLogAnalysis(config) {
    let initUrlsFromWaf = []
    let s3InPrefix = 'crawler/scan-data/' + config.parsedUrl.hostname + '/'
    try {
        let wafToScanDiff = await s3Utils.getFile(s3InPrefix, 'urls-from-waf.json')
        initUrlsFromWaf = JSON.parse(wafToScanDiff.Body).missed_urls
        let parsedUrl = new URL(config.mainUrl)
        initUrlsFromWaf = initUrlsFromWaf.map((u) => {
            return new URL(u, parsedUrl).href
        })
    } catch (err) {
        logger.log('error', `Could not get S3 object ${s3InPrefix}/urls-from-waf.json: ${err.toString()}`)
        initUrlsFromWaf = []
    }

    return initUrlsFromWaf
}

// Start the scan
async function startScan(args) {
    let {
        id,
        scanLogId,
        resumeScan,
        outFile,
        scanType,
        proxyPassDetails,
        scannerMachineUID,
        wafIpToUse,
    } = args
    utils.printBanner()
    logger.log('info', `\n\n----- Starting scan with ID: ${id} -----\n\n`)

    let startCrawl = Date.now()
    let s3Prefix = (`crawler/early-logs/${id}/`)
    let s3startTime = new Date()
    //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)
    
    try {
        let config = await getScanConfig(id,scanType)

        //set scanType for config
        if (!config) {
            logger.log('error', `Could not find any site with ID = ${id}`)
            //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)
            app.exit(2)
        }

        // Generate scan url analysis data. Gives us a lot of info about the site framework, tech stack etc.
        // which can be used in scanner for better scanning.
        try {
            if (config.generateUrlAnalysis) {
                let urlAnalysisResult = await HaikuUtils.analyzeUrls(config.wappalyzerSourcePath, [{
                    url: config.mainUrl,
                    serviceId: config.serviceId,
                    saveInStorage: true
                }]);

                if (urlAnalysisResult && urlAnalysisResult.length > 0 && urlAnalysisResult[0].result && config.serviceId) {
                    try {
                        let urlAnalysisPrefix = 'scanner/urlAnalysis/'
                        await s3Utils.upload(urlAnalysisPrefix + config.serviceId, `analysis.json`, JSON.stringify(urlAnalysisResult[0].result));
                    } catch (error) {
                        logger.log('error', `analyzeUrls upload error for serviceId: ${serviceId} url: ${urlInfo.url} error: ${error.toString()}`);
                    }
                }
                else {
                    logger.log('error', `analyzeUrls failed for serviceId: ${serviceId} info: ${JSON.stringify(urlAnalysisResult)}`);
                }
            }
        } catch (error) {
            logger.log('error', `Error in generating URL analysis report: ${error.toString()}`)
        }
        
        config.proxyPassDetails = proxyPassDetails ? JSON.parse(proxyPassDetails) : proxyPassDetails;
        config.scannerMachineUID = scannerMachineUID;
        
        //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)
        logger.addMetadata({
            mainUrl: config.mainUrl
        })

        //Setup malware scan proxy with malware engines. i.e. SquidClamAV etc.
        if(config.scanType == SCAN_TYPES.MALWARE && config.malwareConfig) {
            let malwareConfig = config.malwareConfig;
            logger.log('info', `Malware site config = ${JSON.stringify(malwareConfig)}`)
            
            if(malwareConfig.engine && malwareConfig.proxy) {
                logger.log('info', `Got ${malwareConfig.engine} malware proxy at ${malwareConfig.proxy}`);

                try {
                    app.commandLine.appendSwitch('proxy-server', `${malwareConfig.proxy}`)
                    logger.log('info', `${malwareConfig.engine} malware proxy set successfully at ${malwareConfig.proxy}`);
                } catch (err) {
                    logger.log('error', 'Unable apply proxy: ', err.toString())
                }
            }
            else {
                logger.log('error', `Malware site not configured properly.`)
            }
        }

        // now set up RabbitMQ for the crawler.
        if(config.scannerMachineUID) {
            //Direct message queue
            this.msgQ = new RabbitMQ(MACHINE_TYPES.CRAWLER, {
                scannerMachineUID: config.scannerMachineUID
            });
        }
        else {
            //Default round robin message queue
            this.msgQ = new RabbitMQ(MACHINE_TYPES.CRAWLER)
        }

        await this.msgQ.init()

        // Send message to haiku scanner that scan has started. It will call the SSAPI and
        // get the scan log ID
        let excludeUrls = config.excludeUrls || []
        excludeUrls.push(...config.excludeFromAttack)
        let msgContent = {
            scanId: id,
            scanlog_id: scanLogId,
            scanner: 'haiku',
            serviceId: config.serviceId,
            mainUrl: config.mainUrl,
            maxCrawlTimeMins: config.maxScanTime,
            skipUpdateHostFile: config.skipUpdateHostFile,
            wafIpToUse: wafIpToUse,
            resumeScan,
            restrictCrawlToPath: config.restrictCrawlToPath,
            excludeUrls,
            perDomainMaxParallelRequests: config.perDomainMaxParallelRequests,
            networkScanner: config.networkScanner,
            allowSubDomain: config.allowSubDomain,
            isApiScan: config.isApiScan,
            skipCrawl: config.skipCrawl,
            annotatedRequestsFromFile: config.annotatedRequestsFromFile,
            scanType: config.scanType,
            proxyPassDetails: config.proxyPassDetails,
            periodicSaveStateMins: config.periodicSaveStateMins,
            storeAttackInfoInReplayScan: config.storeAttackInfoInReplayScan,
            crawlerIP: HaikuUtils.getPrivateIP(),
            scannerMachineUID: config.scannerMachineUID,
            generateUrlAnalysis: config.generateUrlAnalysis,
            wappalyzerSourcePath: config.wappalyzerSourcePath,
            apiDiscovery: config.apiDiscovery,
        }
        
        let resp = null;

        if (config.scanType) {
            scanLogId = await ssAPI.scanStarted(msgContent);   //scanType flag to distinguish
            resp = {
                scanLogId: scanLogId,
                addToRequest: {},
                skipCrawl: config.skipCrawl,
                pid: process.pid,
                hostname: os.hostname()
            }
            // scanLogId = await ssAPI.startScanMalware(msgContent);  //if new API required
        }        
        else {
            if(resumeScan) {
                try {
                    let crawlStartedPublishMessage = new CrawlStartedPublish({
                        scanId: msgContent.scanId,
                        scanlog_id: scanLogId,
                        scanner: 'crawler',
                        mainUrl: msgContent.mainUrl,
                        crawlerIP: msgContent.crawlerIP
                    });
                    crawlStartedPublishMessage.publish(this.msgQ)   
                } catch (error) {
                    logger.log('error', `Unable to send CrawlStartedPublish message during resume scan, ${error.toString()}`);
                }
            }

            let msg = new CrawlStartedRpc(msgContent)
            resp = await msg.rpcRequest(this.msgQ, 6 * 60 * 1000) // wait up to 6 minutes
        }

        logger.log('info', `CrawlStartedRpc got response: ${JSON.stringify(resp)}`)
        //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)

        scanLogId = resp ? resp.scanLogId : null
        logger.addMetadata({
            scanlogId: scanLogId
        })

        if (scanLogId) {
            // Almost set to scan, do any other setup just before starting
            args.scanLogId = scanLogId
            await setupForScan(config, args, resp)
            logger.log('info', `setupForScan completed`)
            //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)

            // create the scanner
            // adjust the max crawl time with the time alredy used up.
            let minutesUsedUp = Math.ceil((Date.now() - startCrawl) / (60 * 1000))
            config.maxScanTime -= minutesUsedUp
            let scanner = require('./scanner/ifc-scanner-factory.js').createScanner(config)
            
            process.on('SIGINT', () => {
                logger.log('info', 'Received SIGINT. Sending stop signal. Will wait max 5 minutes for clean exit');
                if (scanner) {
                    scanner.emit('stop-automated-crawl', 'SIGINT')
                    scanner.setCanExit('force-stop')
                }
                setTimeout(() => {
                    if (scanner && scanner.browser) {
                        scanner.browser.destroy()
                    }
                    app.exit(2)
                }, 5 * 60 * 1000)
            });

            if(config.scanType == SCAN_TYPES.DEFACEMENT)
            {
                let defacementHistory = await getDefacementFile(msgContent)
                await scanner.onDefacementHistoryLoaded(defacementHistory)
            }

            // kick off the scan
            try {
                logger.log('info', `calling startScan()`)
                await scanner.onStartScan()
                logger.log('info', `startScan() completed`)
            } catch (err) {
                logger.log('error', `Scanner had error, quitting: ${err.toString()}`)
            }

            //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)

            // Send message to haiku scanner that scan has finished. It will inform SOC
            // once all pending requests are done
            msgContent = {
                scanId: config.scanId,
                scanlog_id: scanLogId,
                serviceId: config.serviceId,
                scanner: 'haiku',
                mainUrl: config.mainUrl,
                resumeRequested: resumeScan,
                resumeSuccessful: scanner.resumeSuccessful,
                scanFeasible: scanner.scanFeasible,
                scanFeasibilityMsg: scanner.scanFeasibilityMsg,
                scanFeasibilityS3Image: scanner.scanFeasibilityS3Image,
                loginFailedS3Image: scanner.loginFailedS3Image,
                isPartial: scanner.isPartial,
                crawlTreeInfo: scanner.crawlTreeInfo,
                metrics: scanner.metrics
            }

           
            if (config.scanType) {
                utils.log(`initiating scan stop ${config.scanType}:`)

                let scannerInfo = {
                    scanner: 'haiku',
                    scanId: msgContent.scanId,
                    scanLogId: msgContent.scanlog_id,
                    mainUrl: msgContent.mainUrl,
                    resumeRequested: msgContent.resumeRequested,
                    resumeSuccessful: msgContent.resumeSuccessful,
                    isPartial: msgContent.isPartial,
                    metrics: msgContent.metrics
                }

                let haikuInfo = {
                    scanFeasible: msgContent.scanFeasible,
                    scanFeasibleMsg: msgContent.scanFeasibilityMsg,
                    scanner: scannerInfo,
                    crawler: msgContent,
                    scanType: config.scanType
                }

                if (config.scanType == SCAN_TYPES.DEFACEMENT) {
                    if (_.has(scanner, "defacementHistory.urlInfos")) {
                        haikuInfo.urlInfos = Object.values(scanner.defacementHistory.urlInfos);
                    }

                    if (_.has(scanner, "defacementHistory.defacementDetails")) {
                        haikuInfo.defacementDetails = scanner.defacementHistory.defacementDetails;
                    }
                }

                if (config.scanType == SCAN_TYPES.MALWARE) {
                    haikuInfo.externalResources = scanner.externalResources;
                }

                if (msgContent.scanFeasible) {
                    utils.log(`calling scanStopped in scanFeasible Mode True: jobsession-completed`)

                    await ssAPI.scanStopped(msgContent.scanId, haikuInfo)
                } else {
                    utils.log(`Calling scanStopped in scanFeasible Mode False: jobsession-completed`)

                    // isBlocked = 2 => Scan failed due to issue with initial scan, typically site down
                    // blocked reason = 2 => blocked on connectria and AWS
                    await ssAPI.scanStopped(msgContent.scanId, haikuInfo, /*isBlocked =*/ 2, /*blockedReason =*/ 2);
                }
            }
            else {
                utils.log(`scanStop process with normal scan`);
                //regular crawl
                msg = new CrawlFinished(msgContent)
                msg.publish(this.msgQ)
            }

            // process any refresh requests that happened in the time between exiting crawl loop & now
            await scanner.processRefresheCrawlerRequest()

            // Even is automated crawl is done, wait and process scanner requests till max time/scanner done
            if (config.scanType) {
                scanner.setCanExit(`Finished ${config.scanType} scan`)
            }
            else {
                await scanner.canExit()
            }

            // upload the final log (with all the refresh)
            await scanner.saveLogToS3()

            //TO DO save scanner.defacementInfo to save by dateTime and scanId. 
            ///i.e. /scanner/crawler/serviceId/defacementInfo-scanlogId-date.json 
            //scanner.defacementInfo
            if (config.scanType == SCAN_TYPES.DEFACEMENT)
                await saveDefacementInfo(scanner.defacementHistory, msgContent)

            // if there is a browser window, kill it
            if (scanner.browser) {
                scanner.browser.destroy()
            }

        } else {
            logger.log('error', `could not get scan log ID from Sooper Scheduler for ${id}`)
        }
    } catch (err) {
        logger.log('error', `error in startScan : ${err.toString()}`)
    }

    //await s3Utils.uploadFile(s3Prefix, `haiku-crawler-${id}-${s3startTime.toISOString()}.log`, outFile)
    app.exit()
}

//-----------------------------------
// default config
async function getScanConfig(id, scanType) {
    // get the config from the REST API
    let siteConfig = null;

    if (scanType) {
        siteConfig = await ssAPI.getMalwareScanConfig(id)
    }
    else {
        siteConfig = await ssAPI.getScanConfig(id)
    }

    let config = {}
    if (siteConfig) {
        // merge the config

        if (scanType) {
            config = mergeConfig(require(`./${scanType}-scan-config`), siteConfig)
            config.scanType = scanType;
        }
        else {
            config = mergeConfig(require('./default-scan-config'), siteConfig)
            config.scanType = scanType;
        }
    }

    logger.log('info', `Site config: ${JSON.stringify(siteConfig)}`)
    return config
}

function mergeConfig(defaultConfig, siteSpecificConfig) {
    let config = defaultConfig
    config.siteConfig = siteSpecificConfig.haikuScanner
    if (!config.siteConfig) {
        config.siteConfig = {}
    }

    if( _.isBoolean(config.siteConfig.useFullXPath) && config.siteConfig.useFullXPath) {
        config.useFullXPath = config.siteConfig.useFullXPath;
    }

    if( _.isBoolean(config.siteConfig.ignoreTableCrawlOptimizatiion) && config.siteConfig.ignoreTableCrawlOptimizatiion) {
        config.ignoreTableCrawlOptimizatiion = config.siteConfig.ignoreTableCrawlOptimizatiion;
    }

    if(config.siteConfig.apiDiscovery) {
        config.apiDiscovery = config.siteConfig.apiDiscovery;
    }

    if(_.isBoolean(siteSpecificConfig.excludePostSeedUrls) && siteSpecificConfig.excludePostSeedUrls == true) {
        config.excludePostSeedUrls = siteSpecificConfig.excludePostSeedUrls;
    }

    if(_.isBoolean(config.siteConfig.optimizeScan) && config.siteConfig.optimizeScan === true) {
        config.optimizeScan = config.siteConfig.optimizeScan;
    }

    // merge in the global part of site config 
    config.serviceId = siteSpecificConfig.id
    // tried using Object.assign but does not do the merge nested properties like we want
    // for now, merge only known properties
    if (siteSpecificConfig.url) {
        config.mainUrl = siteSpecificConfig.url
        config.parsedDomain = ParseDomain(config.mainUrl)
        config.parsedUrl = urlPkg.parse(config.mainUrl)
    }

    if (siteSpecificConfig.maxScanTime) {
        config.maxScanTime = Number.parseInt(siteSpecificConfig.maxScanTime)
    }

    // Use to set generate url analysis report using wappalyzer for the site
    if (_.isBoolean(siteSpecificConfig.generateUrlAnalysis) && siteSpecificConfig.generateUrlAnalysis) {
        config.generateUrlAnalysis = siteSpecificConfig.generateUrlAnalysis;
    }

    if(siteSpecificConfig.wappalyzerSourcePath) {
        config.wappalyzerSourcePath = siteSpecificConfig.wappalyzerSourcePath;
    }

    // Skip host file update - some sites want to use DNS and not skip WAF
    if (_.isBoolean(config.siteConfig.skipUpdateHostFile)) {
        config.skipUpdateHostFile = config.siteConfig.skipUpdateHostFile;
    }

    // Strip www from url when sending request
    if (_.isBoolean(config.siteConfig.stripWWW)) {
        config.stripWWW = config.siteConfig.stripWWW;
    }

    // Allow sub domain
    // Refer Jira - TAS-2219
    // Allowed scan url = abc.indusface.com, allow http://abc.indusface.com , http://indusface.com , http://site1.abc.indusface.com , blogs.indusface.com etc.
    if (_.isBoolean(config.siteConfig.allowSubDomain)) {
        config.allowSubDomain = config.siteConfig.allowSubDomain;
    }

    // websecurity check for some sites expecting true like 'https://admin.officeatwork.com'
    if (_.isBoolean(config.siteConfig.webSecurity)) {
        config.webSecurity = config.siteConfig.webSecurity;
    }

    // sitemapurl: add to initital URls to crawl at the beginning, During api discovery, we don't need to add initial crawl urls
    if(!config.apiDiscovery) {
        config.initialCrawlUrls = require('./initial-crawl-seeds');
    }
    else {
        config.initialCrawlUrls = [];
    }
    
    if (siteSpecificConfig.sitemapurl) {
        config.initialCrawlUrls.push(siteSpecificConfig.sitemapurl)
    }

    if (config.scanType == SCAN_TYPES.DEFACEMENT)
    config.initialCrawlUrls = [];

    if(siteSpecificConfig.Auth_loginurl) {
        if(siteSpecificConfig.Auth_loginurl != config.mainUrl) {
            config.initialCrawlUrls.push(siteSpecificConfig.Auth_loginurl);
        }

        config.loginurl = siteSpecificConfig.Auth_loginurl;
    }

    // includeurl: add to initital URls to crawl at the beginning
    if (siteSpecificConfig.includeurl) {
        config.initialCrawlUrls.push(...siteSpecificConfig.includeurl.split('|'))
    }

    // make the initial crawl actions into FQDNs
    config.initialCrawlUrls = config.initialCrawlUrls.filter(input => input.trim().length > 0) // remove blanks
    config.initialCrawlUrls = config.initialCrawlUrls.map(path => new URL(path.trim(), config.parsedUrl.href).href)

    // excludeurl: specific URL patterns to exclude separated by '|'. 
    if (siteSpecificConfig.excludeurl) {
        let excludeUrls = siteSpecificConfig.excludeurl.split('|').filter(input => input.trim().length > 0) // remove blanks
        if (excludeUrls.length) {
            config.excludeUrls = excludeUrls
        }
    }

    // sitemapdir: restrict scan to specific path in the domain.
    if (siteSpecificConfig.sitemapdir && siteSpecificConfig.sitemapdir.trim().length > 0) {
        config.restrictCrawlToPath = new URL(siteSpecificConfig.sitemapdir.trim(), config.parsedUrl.href).pathname
    }

    // START ----- HACK HACK HACK -----
    // This should be changed to have WAS generate a recipe step instead of this 'global' setting
    // This ugly choice was made because recipes are available only to the CS team initially. When
    // we allow recipes for everyone, this should be fixed to be clean and below code deleted

    // Haiku scanner perDomainMaxParallelRequests
    if (siteSpecificConfig.perDomainMaxParallelRequests) {
        config.perDomainMaxParallelRequests = siteSpecificConfig.perDomainMaxParallelRequests
    }

    // END ----- HACK HACK HACK -----

    // haiku scanner specific stuff 
    if (config.siteConfig.maxDepth) {
        config.maxDepth = Number.parseInt(config.siteConfig.maxDepth)
    }
    if (config.siteConfig.maxActions) {
        config.maxActions = Number.parseInt(config.siteConfig.maxActions)
    }
    if (config.siteConfig.maxScanTime) {
        utils.log(`Site ${config.mainUrl} has a haiku specifc max time set to ${config.siteConfig.maxScanTime}, Ignoring!!`)
    }
    if (config.siteConfig.maxInitialUrlsPerExternalSource) {
        config.maxInitialUrlsPerExternalSource = Number.parseInt(config.siteConfig.maxInitialUrlsPerExternalSource)
    }
    if (config.siteConfig.maxActionsPerStatePerIteration) {
        config.maxActionsPerStatePerIteration = Number.parseInt(config.siteConfig.maxActionsPerStatePerIteration)
    }

    // merge 'addToRequest' crawler config. This can also be sent by scanner via recipe
    if (config.siteConfig.addToRequest) {
        config.addToRequest = config.siteConfig.addToRequest
    }

    if (config.siteConfig.initialWizardGuides) {
        config.initialWizardGuides = config.siteConfig.initialWizardGuides

        // make sure we always process the init URLs specified by customer first and don't 
        // skip them in the 'max actions per state' type processing
        for (let guide of config.initialWizardGuides) {
            guide.mustRunAtInit = true
        }
    }

    config.unsafeForceAllowCrawl = config.siteConfig.unsafeForceAllowCrawl || [];
    // excludeFromSitemap: specific URL patterns to exclude from sitemap separated by '|'. 

    config.excludeFromSitemap = config.siteConfig.excludeFromSitemap || []

    // scanner config - this is just a pass through from crawler for now. 
    // TODO: need to rework to scanner specific & crawler specific config.
    config.excludeFromAttack = config.siteConfig.excludeFromAttack || []

    //Add default SSO urls as unsafeForceAllowCrawl and excludeFromAttack
    //i.e. Microsoft, Google, Facebook, Twitter etc.
    _.forEach(Object.keys(SSO_URLS), (provideName) => {
        config.unsafeForceAllowCrawl.push(...SSO_URLS[provideName]);
        config.excludeFromAttack.push(...SSO_URLS[provideName]);
    });


    if(siteSpecificConfig.Auth_loginurl) {
        if(siteSpecificConfig.Auth_loginurl != config.mainUrl && !HaikuUtils.isSameDomain(siteSpecificConfig.Auth_loginurl, config.mainUrl)) {
            try {
                let unsafeUrl = new URL(siteSpecificConfig.Auth_loginurl);
                config.unsafeForceAllowCrawl.push(unsafeUrl.origin);
                config.excludeFromAttack.push(unsafeUrl.origin + unsafeUrl.pathname);
            } catch (error) {
                // wrt this will fail in scanfeasebility check
                config.formatError = { "msg": error.message, "url": siteSpecificConfig.Auth_loginurl, 'code': error.code };
            }
        }
    }

    config.networkScanner = config.siteConfig.networkScanner

    if (config.siteConfig.maxSeedUrls) {
        config.maxSeedUrls = config.siteConfig.maxSeedUrls
    }

    if (config.siteConfig.createAnimatedGif) {
        config.createAnimatedGif = config.siteConfig.createAnimatedGif
    }
    if (config.siteConfig.createUniqueStateImage) {
        config.createUniqueStateImage = config.siteConfig.createUniqueStateImage
    }
    if (config.siteConfig.createAllStatesImage) {
        config.createAllStatesImage = config.siteConfig.createAllStatesImage
    }

    // utils
    utils.mergeConfig(config)

    // scanner props
    if (config.siteConfig.scanner) {
        config.scanner = config.siteConfig.scanner
    }
    if (config.siteConfig.crawlState) {
        config.crawlState = config.siteConfig.crawlState
    }
    if (config.siteConfig.crawlTraversal) {
        config.crawlTraversal = config.siteConfig.crawlTraversal
    }

    // emails.
    if (config.siteConfig.email) {
        config.email = config.siteConfig.email
    }

    //Whether is current scan is api mode & set skip crawl to false if its true. 
    if(_.isBoolean(_.get(config, "networkScanner.isApiScan"))) {
        config.isApiScan = config.networkScanner.isApiScan;
        config.skipCrawl = config.isApiScan;

        if(config.networkScanner.scriptVariables) {
            config.scriptVariables = config.networkScanner.scriptVariables;
        }

        config.annotatedRequestsFromFile = config.networkScanner.annotatedRequestsFromFile;
        
        if(_.isArray(config.networkScanner.apiIds)) {
            config.apiIds = config.networkScanner.apiIds;
        }
    }
    
    if (_.isBoolean(config.siteConfig.saveCrawlerLogs)) {
        config.saveCrawlerLogs = config.siteConfig.saveCrawlerLogs
    }

	// check for defacement settings
    if (config.scanType === SCAN_TYPES.DEFACEMENT) {
        config.customerThreshold = siteSpecificConfig.customerThreshold || defaultConfig.customerThreshold;
        try {
            config.customerThreshold.low = parseFloat(config.customerThreshold.low)
            config.customerThreshold.medium = parseFloat(config.customerThreshold.medium)
            config.customerThreshold.high = parseFloat(config.customerThreshold.high)
        }
        catch (error) {
            utils.log(`Site ${config.mainUrl} has invalid customer threshold values ${JSON.stringify(config.customerThreshold)}`);
        }

        config.maxPageFailedCount = parseFloat(siteSpecificConfig.maxPageFailedCount) || defaultConfig.maxPageFailedCount;

    }

	// Update malware specific configs
    if(config.scanType === SCAN_TYPES.MALWARE && siteSpecificConfig.malwareConfig) {
        config.malwareConfig = siteSpecificConfig.malwareConfig;
    }
    
    if(_.has(siteSpecificConfig,'haikuScanner.puppeteerStepsFilePath')) {
        config.puppeteerStepsFilePath = siteSpecificConfig.haikuScanner.puppeteerStepsFilePath;
    }

    if (_.isBoolean(config.siteConfig.hostRules)) {
        config.hostRules = config.siteConfig.hostRules
    }
    
    if (_.isBoolean(config.siteConfig.storeAttackInfoInReplayScan)) {
        config.storeAttackInfoInReplayScan = config.siteConfig.storeAttackInfoInReplayScan
    }

    if (_.isBoolean(config.siteConfig.triggerValidation)) {
        config.triggerValidation = config.siteConfig.triggerValidation
    }

    if (_.isBoolean(config.siteConfig.setInputValue)) {
        config.setInputValue = config.siteConfig.setInputValue
    }

    if (_.isBoolean(config.siteConfig.ignoreForm)) {
        config.ignoreForm = config.siteConfig.ignoreForm;
    }

    if (_.isBoolean(config.siteConfig.ignoreRowAndColumns)) {
        config.ignoreRowAndColumns = config.siteConfig.ignoreRowAndColumns;
    }

    // pageExecutionScriptTimeout - default 10 seconds if not set in siteConfig or defaultConfig
    // used to control the time to wait for page execution script to complete when loading a slow page
    if (config.siteConfig.pageExecutionScriptTimeout) {
        if (_.isNumber(config.siteConfig.pageExecutionScriptTimeout)) {
            config.pageExecutionScriptTimeout = config.siteConfig.pageExecutionScriptTimeout;
        } else {
            config.pageExecutionScriptTimeout = defaultConfig.pageExecutionScriptTimeout;
        }
    }
    else {
        config.pageExecutionScriptTimeout = defaultConfig.pageExecutionScriptTimeout;
    }

    if (siteSpecificConfig.subpathEnabled && !isNaN(siteSpecificConfig.subpathEnabled)) {
        config.subpathConfig.enabled = parseInt(siteSpecificConfig.subpathEnabled);       
    }

    if (config.siteConfig.subpathConfig) {
        config.subpathConfig = config.siteConfig.subpathConfig;       
    }

    return config
}

async function getDefacementFile(config) {
    // call from main js scan start and then attach to scanner config and read 
    // from deffacementcheck scan start method or constructor
    let defacementHistory = null

    let s3InPrefix = (`crawler/defacement/${config.serviceId}`)
    try {
         let resp = await s3Utils.getFile(s3InPrefix, 'defacementInfo.json');
         if (resp && resp.Body) {
            defacementHistory = resp.Body.toString();
        }
        if (defacementHistory) {
            defacementHistory = JSON.parse(defacementHistory);
        }
    } catch (err) {
        logger.log('error', `Could not get S3 object ${s3InPrefix}/'defacementInfo.json: ${err.toString()}`)
    }

    return defacementHistory
}

// write all defacement matrix
async function saveDefacementInfo(defacementInfo, msgContent) {
    let s3Prefix =(`crawler/defacement/${msgContent.serviceId}/`)
    try {
        await s3Utils.upload(s3Prefix, `defacementInfo.json`, JSON.stringify(defacementInfo))
    } catch (error) {
        console.log(error);
    } 
}