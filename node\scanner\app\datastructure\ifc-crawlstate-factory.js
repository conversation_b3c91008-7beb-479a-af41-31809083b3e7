let utils = require('../ifc-utils.js')
const IfcCrawlStateContext = require('./ifc-crawlstate-context.js')

module.exports = {
    createCrawlstateDS: function (config, actions) {
        whichDS = config.crawlState
        switch (whichDS) {
            case 'ifc-crawlstate-context':
                return new IfcCrawlStateContext(config, actions)
                break;

            default:
                utils.log('Unknown crawl data store: ', whichDS)
        }
    }
}