const VectorResponseAttack = require('./vector-response-attack');
const _ = require('lodash');

class JSONInjection extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        this.vulnerabilityID = 'ID-json-injection';
    }

    initParameterizedDelegate(parameterizedDelegate) {
        parameterizedDelegate.setOptions({
            addExtraParam: false,
            attackParamName: false,
            encodings: ['uri'],
        });
    }

    getAttackVectors() {
        return JSON_ATTACK_VECTORS;
    }

    getAttackableEvents() {
        return ['json-body', 'uri-query-params'];
    }

    async processAttackResponse(attack) {
        if (attack.pluginName !== this.getName()) {
            return;
        }

        let headers = _.get(attack, 'headers', {});
        let contentType = _.get(headers, 'content-type', '').toLowerCase();
        let accept = _.get(headers, 'accept', '').toLowerCase();
        
        if (!contentType.includes('application/json') && !accept.includes('application/json')) {
            return;
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.JSONInjection) {
            return;
        }

        let responseBody = _.get(attack, 'result.resp.body', '');
        responseBody = this.normalizeResponse(responseBody);
        let detectedIndicator = this.containsIndicators(responseBody);
        if (detectedIndicator) {
            let vulnerabilityDetails = `Possible JSON Injection found: ${detectedIndicator}`;
            this.addVulnerabilitytoResult(
                attack,
                this.vulnerabilityID,
                vulnerabilityDetails
            );
            pluginDataForRequest.JSONInjection = true;
        }
    }

    normalizeResponse(responseBody) {
        if (!responseBody) return '';
        return responseBody.replace(/\s+/g, ' ').trim();
    }

    containsIndicators(responseBody) {
        for (let pattern of JSON_INDICATORS) {
            let match = responseBody.match(pattern);
            if (match) {
                return match[0];
            }
        }
        return null;
    }
}

const JSON_ATTACK_VECTORS = [
    // Template injection + JS error trigger
    '{ "input": "{{7*7}}" }',
  
    // Basic NoSQL injection
    '{ "username": { "$ne": null }, "password": { "$ne": null } }',
  
    // Expression Language (EL/SpEL) injection pattern
    '{ "field": "${{7*7}}" }'
  ];

  const JSON_INDICATORS = [
    /SyntaxError: Unexpected token/i,
    /MongoError:.*?\$ne/i,
    /Uncaught TypeError/i,
    /ReferenceError:.*?not defined/i,
    /Unexpected end of JSON input/i,
    /javax\.el\.ELException/i,
    /org\.springframework\.expression\.ExpressionException/i
  ];

module.exports = JSONInjection;
