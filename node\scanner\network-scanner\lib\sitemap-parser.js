const Request = require('request')
const Xml2JS = require('xml-to-json-promise');
const zlib = require('zlib')
const debug = require('debug')('SitemapParser')
const URL = require('url').URL
const logger = require('../../common/lib/haiku-logger')
const HaikuUtils = require('../../common/lib/haiku-utils')

/**
 * Parses sitemaps and get list of links up to the max set in the constructor.
 * This can handle sitemapindex files as well so will fetch and parse multiple sitemap.xml files
 * until it gets the max links. It first checks robots.txt for sitemap file and then defaults 
 * to sitemap.xml. Also handles xml.gz files.
 * Currently does not use {@link HTTPRequestMonster} to get the robots.txt file.
 */
class SitemapParser {
    /**
     * @param {string} rootUrl The URL for which sitemap URLs have to be feteched
     * @param {int} maxLinks Max links to fetch 
     */
    constructor(rootUrl, maxLinks = 1000) {
        this.maxLinks = maxLinks
        this.rootUrl = rootUrl
        this.parsedUrl = new URL(rootUrl)
        this.rootDomain = HaikuUtils.canonacalizeHost(this.parsedUrl)
        this.sitemapsProcessed = []
    }

    /** 
     * Kicks off the collection of sitemap URls up to the supplied maxLinks.
     */
    async getSitemapUrls() {
        // get sitemaps list from robots.txt/default
        let sitemaps = await this.getSitemaps()
        let urlsFound = []

        while (sitemaps.length && urlsFound.length < this.maxLinks) {
            let sitemapUrl = sitemaps.shift();
            this.sitemapsProcessed.push(sitemapUrl)

            // get & parse sitemap
            let data = null
            try {
                let httpRespBody = await this._httpGet(sitemapUrl)
                if (!httpRespBody || httpRespBody.length < 1) {
                    continue
                }

                // see if we need to unzip
                if (sitemapUrl.endsWith('.gz')) {
                    httpRespBody = zlib.unzipSync(httpRespBody)
                }

                // convert XML to JSON
                data = await Xml2JS.xmlDataToJSON(httpRespBody, { normalizeTags: true} )
            } catch (e) {
                logger.log('error',`could not get sitemap from ${sitemapUrl}: ${e.toString()}`)
                data = null
            }

            if (!data) {
                continue
            }

            // see if sitemap has URLs or sitemapIndex
            if (data.sitemapindex && data.sitemapindex.sitemap) {
                // list of sitemaps, add to queue to process
                for (let sitemap of data.sitemapindex.sitemap) {
                    if (sitemap && sitemap.loc) {
                        sitemaps.push(sitemap.loc[0])
                    }
                }
            } else if (data.urlset && data.urlset.url) {
                // list of urls
                for (let siteUrl of data.urlset.url) {
                    if (siteUrl && siteUrl.loc) {
                        if (this._isAllowedDomain(siteUrl.loc[0])) { // only add links that we are allowed to crawl.
                            urlsFound.push(siteUrl.loc[0])
                            if (urlsFound.length >= this.maxLinks) {
                                break
                            }
                        }
                    }
                }
            }
        }

        return urlsFound
    }

    /**
     * Get the sitemap filenames. 
     * First try to get it from robots.txt. If that is not found, default to domain/sitemap.xml 
     */
    async getSitemaps() {
        // find sitemaps
        // see if robots.txt has a sitemap
        let robots_txt = await this._httpGet(this.parsedUrl.origin + '/robots.txt')
        let robots_sitemap_re = /^Sitemap:\s?([^\s]+)$/gim

        let match, sitemaps = []
        while ((match = robots_sitemap_re.exec(robots_txt)) !== null) {
            sitemaps.push(match[1])
        }

        // if there is no sitemaps found, try for default one
        if (sitemaps.length < 1) {
            sitemaps.push(this.parsedUrl.origin + '/sitemap.xml')
        }
        return sitemaps
    }

    /**
     * @private
     * @param {string} siteUrl The url found from sitemap file
     * @returns are we allowed to crawl this URL?
     */
    _isAllowedDomain(siteUrl) {
        try {
            let allowDomain = (this.rootDomain == HaikuUtils.canonacalizeHost(siteUrl))
            return allowDomain
        } catch (e) {
            logger.log('error',`_isAllowedDomain(${siteUrl}): ${e.toString()}`)
            return false
        }
    }

    /**
     * @private
     * @param {string} url 
     * Performs a HTTP GET to fetch contents of URI. Silently eats errors -> returns empty body on error/ non 200 resposne
     */
    _httpGet(url) {
        return new Promise((resolve) => {
            let options = {
                gzip: true, // always support gzip/deflate
                encoding: null, // may have binary data
                uri: url,
                method: "GET",
                headers: {
                    "Connection": "keep-alive",
                    // fake the user agent, sites like jeevansaathi give access denied with missing/unsupported UA
                    "User-Agent": "Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36",
                    //"User-Agent" : "Pingdom.com_bot_version_1.4_(http://www.pingdom.com/)",
                    "Accept-Encoding": "gzip, deflate",
                }
            }
            Request(options, (err, response, body) => {
                if (err || !response || response.statusCode != '200') {
                    resolve('') // silently eat errors
                } else {
                    resolve(body)
                }
            })
        })
    }

}

module.exports = SitemapParser