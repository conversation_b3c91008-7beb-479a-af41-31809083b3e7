const utils = require('../ifc-utils.js')
const RealClickAction = require('./real-click-action.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

class CheckboxAction {
    constructor(xpath, val, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.val = val
        this.annotation = annotation
    }

    get actionType() {
        return 'checkbox'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.val, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let encXPath = utils.encode(this.xpath)
        let jscode = `indusfaceRenderer.getCheckboxValue('${encXPath}')`
        let curVal = await utils.timedPromise(browser.webContents.executeJavaScript(jscode), null)

        if (curVal == this.val) {
            return true
        }

        let actionSucceeded = await executer.triggerAction(new RealClickAction(this.getXPath()), executionContext)
        if (!actionSucceeded) {
            let encXPath = utils.encode(this.xpath)
            let jscode = `indusfaceRenderer.setCheckboxValue('${encXPath}', ${this.val})`
            await utils.timedPromise(browser.webContents.executeJavaScript(jscode))
        }

        // check/uncheck failing is not a deal-breaker
        actionSucceeded = true
        return actionSucceeded
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType}  '${this.val}' into ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = CheckboxAction