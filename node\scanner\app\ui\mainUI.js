const path = require('path')
const url = require('url')

class ScannerUI {
    constructor(BrowserWindow) {
        this.electronBrowserWindow = BrowserWindow
        this.rendererWindow = null
    }

    createUIWindow() {
        console.log( path.join(__dirname, 'uiVisualization.js') )
        this.rendererWindow = new this.electronBrowserWindow({
            width: 800,
            height: 600,
            webPreferences: {
                webSecurity: false,
                nodeIntegration: true,
                preload: path.join(__dirname, 'uiVisualization.js')
            }
        })

        this.rendererWindow.loadURL(url.format({
            pathname: path.join(__dirname, 'MainUI.html'),
            protocol: 'file:',
            slashes: true
        }))
    }
}

module.exports = ScannerUI