const NetworkAttack = require('./network-attack')
const { spawn } = require('child_process');
const dns = require('dns');

class dnssecvuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Vulnerability ID for DNSSEC not signed
        this.dnssecnotsignedID = 'ID-dnssec-not-signed'
        this.commandTimeout = 60000; // 60 seconds timeout
    }

    // Main entry point for DNSSEC vulnerability checks
    async processAttackResponse(attack) {
        if (!attack || typeof attack !== 'object') {
            return;
        }

        if (attack.attackArea !== 'original-crawler-request' || attack.pluginName !== 'Original Crawler Request') {
            return;
        }

        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        if (!pluginStorageScanScope || pluginStorageScanScope.dnssecchecked) {
            return;
        }

        if (!attack.hostname || !attack.href) {
            return;
        }        

        try {
            let host = attack.hostname;
            let gethost = await this.dnsresolve(host);
            if (!gethost) {
                return;
            }

            // Check for DNSSEC
            await this.checkDNSSEC(attack, gethost, host);
            pluginStorageScanScope.dnssecchecked = true;
        } catch (error) {
            pluginStorageScanScope.dnssecchecked = true;
        }
    }

    // DNS resolution with CDN check
    async dnsresolve(host) {
        try {
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    resolve(host);
                }, this.commandTimeout);

                dns.resolveCname(host, (err, addresses) => {
                    clearTimeout(timeout);
                    if (err) {
                        resolve(host);
                    }

                    if (addresses && addresses.some(addr => /\.induscdn\.com/i.test(addr))) {
                        resolve(`proxypass.${host}.indusguard.com`);
                    } else {
                        resolve(host);
                    }
                });
            });
        }
        catch (error) {
            return null;
        }
    }

    // Check for DNSSEC vulnerabilities
    async checkDNSSEC(attack, host, originalHost) {
        try {
            const dnssec = await this.dnssecrrsig(host);
            if (!dnssec || dnssec.includes('output: Not found')) {
                return;
            }

            const lines = dnssec.split('\n');
            let hasRRSIG = false;
            let targetIPs = new Set(); // Use Set to avoid duplicate IPs
            let hasUnsignedAnswer = false;
            let hasValidStatus = false;

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;

                if (trimmedLine.includes('status:')) {
                    hasValidStatus = trimmedLine.split('status:')[1].trim() === 'success';
                    continue;
                }

                if (trimmedLine.includes('unsigned_answer:')) {
                    hasUnsignedAnswer = true;
                    continue;
                }

                if (trimmedLine.includes('RRSIG')) {
                    hasRRSIG = true;
                }

                if (trimmedLine.includes('IN A')) {
                    const ipMatch = trimmedLine.match(/\b(?:\d{1,3}\.){3}\d{1,3}\b/);
                    if (ipMatch && this.isValidIPv4(ipMatch[0])) {
                        targetIPs.add(ipMatch[0]);
                    }
                }
            }

            // Only report if we have valid status and no RRSIG
            if (hasValidStatus && !hasRRSIG && targetIPs.size > 0) {
                const details = [
                    `Domain: ${originalHost}`,
                    'Status: DNSSEC Disabled',
                    `IPs: ${Array.from(targetIPs).join(', ')}`,
                    hasUnsignedAnswer ? 'Note: Domain uses unsigned DNS records' : null
                ].filter(Boolean).join('\n');

                if (details.trim()) {
                    this.addVulnerabilitytoResult(attack, this.dnssecnotsignedID, details);
                }
            }

        } catch (error) {
            return null;
        }
    }

    // Helper method to validate IPv4 addresses
    isValidIPv4(ip) {
        const parts = ip.split('.');
        return parts.length === 4 && parts.every(part => {
            const num = parseInt(part, 10);
            return num >= 0 && num <= 255 && part === num.toString();
        });
    }

    // Execute DNS query with RRSIG check
    dnssecrrsig(host) {
        return new Promise((resolve) => {
            const args = ['+yaml', host, '@*******'];
            const delv = spawn('delv', args);

            let output = '';
            const timeout = setTimeout(() => {
                delv.kill('SIGTERM');
                resolve('output: Not found');
            }, this.commandTimeout);

            delv.stdout.on('data', (data) => {
                output += data.toString();
            });

            delv.on('close', () => {
                clearTimeout(timeout);
                resolve(output || 'output: Not found');
            });

            delv.on('error', () => {
                clearTimeout(timeout);
                resolve('output: Not found');
            });
        });
    }
}

module.exports = dnssecvuln 