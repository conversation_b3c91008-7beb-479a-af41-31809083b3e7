const utils = require('../ifc-utils.js')
const _ = require('lodash')

// all the actions
const ActionList = require('./action-list.js')
const LoadAction = require('./load-action')
const ClickAction = require('./click-action.js')
const JSClickAction = require('./javascript-click-action')
const SelectAction = require('./select-action.js')
const TypeAction = require('./type-action.js')
const ContentMatchAction = require('./content-match-action.js')
const CheckboxAction = require('./checkbox-action.js')
const ScreenshotAndEmailAction = require('./screenshot-and-email-action')
const UploadAction = require('./upload-action')

/**
 * Helper function to create action list from object containing array of action information. 
 * @param {Object} guidedScan Object with array of actions info (not action classes, just data like JSON) 
 */
function createActionList(guidedScan) {
    try {
        let actionList = new ActionList(guidedScan.name || 'unspecified');
        if (_.isBoolean(guidedScan.mustRunAtInit)) {
            actionList.setMustRunAtInit(guidedScan.mustRunAtInit)
        }

        for (let action of guidedScan.actions) {
            switch (action.action) {
                case 'load':
                    actionList.addAction(new LoadAction(action.xpath, action.uri, action.annotation));
                    break;
                case 'type':
                    actionList.addAction(new TypeAction(action.xpath, action.text, action.annotation));
                    break;
                case 'click':
                    actionList.addAction(new ClickAction(action.xpath, action.annotation));
                    break;
                case 'jsclick':
                    actionList.addAction(new JSClickAction(action.xpath, action.annotation));
                    break;
                case 'contentMatch':
                    actionList.addAction(new ContentMatchAction(action.xpath, action.matchString, action.annotation));
                    actionList.setCombinator('and')
                    break;
                case 'checkbox':
                    actionList.addAction(new CheckboxAction(action.xpath, action.val, action.annotation));
                    break;
                case 'select':
                    actionList.addAction(new SelectAction(action.xpath, action.selectVal, action.annotation));
                    break;
                case 'screenshotAndEmail':
                    actionList.addAction(new ScreenshotAndEmailAction(action.emails, action.subject, action.maxEmails, action.annotation))
                    break;
                case 'upload':
                    actionList.addAction(new UploadAction(action.xpath, action.filePath, action.annotation, action.fileType));
                    break;
                default:
                    utils.log(`Guided Scan - skipping guided scan ${guidedScan.name} due to unknown action: ${action.action}`);
                    throw new Error('unknown action')
            }
        }
        return actionList
    } catch (err) {
        return null
    }
}


module.exports = createActionList