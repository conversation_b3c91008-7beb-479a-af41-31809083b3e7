const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
var crypto = require('crypto')
const requestPromise = require('request-promise-native')
const cheerio = require('cheerio')

class PRSSI extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-path-relative-stylesheet-import'
    }

    getAttackVectors() {
        return __AtVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    wantProcessAttackResponse(attack) {
        let ResBody = _.get(attack, "result.resp.body")
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
        if (attack.attackArea == 'BaseURI' && attack.httpRequest.uri.includes('/haikupathtest') && redirect == 0 && statusCode == 200 && ResBody.length > 10) {
            if (/<link.+?rel=/i.test(ResBody) && /href=.+?\.css/i.test(ResBody)) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.PRSSIFound) {
            return
        }

        if (attack.pluginName != this.getName()) {
            return false
        }

        let ResBody = _.get(attack, "result.resp.body")

        /* Solutions:
        Set the server header X-Frame-Options to "deny" on all pages
        Set the server header X-Content-Type-Options to "nosniff" on all pages
        Set a modern doctype on all pages            
        */

        let xframeoptions = _.get(attack, 'result.resp.httpResponse.headers["x-frame-options"]')
        let xcontenttypeoptions = _.get(attack, 'result.resp.httpResponse.headers["x-content-type-options"]')
        if (/<!DOCTYPE html/i.test(ResBody) && /DENY|SAMEORIGIN|ALLOW-FROM/i.test(xframeoptions) && /nosniff/i.test(xcontenttypeoptions)) { return }

        //Absolute URL: page, including the protocol and domain
        //Relative Path CSS Links found:
        //Checking <link rel="stylesheet" href="../../css/reset.css?v=1" />
        try {
            let linktag = ResBody.match(/<link.+?rel=.+?>/i)
            let RegExp_href = /href=.+?\.css/i
            let details = []
            if (linktag.length > 0 && RegExp_href.test(linktag)) {
                let page = cheerio.load(ResBody, {
                    baseURI: attack.href
                });
                for (let linkval of linktag) {
                    if (linkval.includes('.css')) {
                        let hrefattr = linkval.match(RegExp_href)[0]
                        if (hrefattr.length > 5 && !hrefattr.includes('http') && hrefattr.includes('.css')) {
                            let fullurl = page(linkval).prop('href')
                            if (fullurl.includes('haikupathtest')) {
                                details.push({
                                    dom_element: linkval,
                                    href: fullurl
                                })
                            }
                        }
                    }
                }
                page = null
                if (details.length > 0) {
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                    pluginDataForRequest.PRSSIFound = true
                }
            }
        }
        catch (e) { return }
    }
}

const __AtVectors = [
    `haikupathtest/`
]

module.exports = PRSSI