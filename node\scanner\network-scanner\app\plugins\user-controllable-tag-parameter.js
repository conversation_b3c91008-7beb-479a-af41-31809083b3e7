const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const cheerio = require('cheerio')

class UserControllableTagsParameters extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-user-controllable-tag-parameter'
        this.partialusercont = 'ID-partial-user-controllable-script'
        this.crosssiteflashing = 'ID-cross-site-flashing'
    }

    getAttackVectors() {
        return _attackVectors
    }

    /** 
     * Only attack header: Referer, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'X-Forwarded-Host', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded', 'Cookie',  'Accept-Language', 'X-Forwarded-For']
        })
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers']
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }

        let ExcludeURL = /\/blog\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i
        if (ExcludeURL.test(attack.href)) {
            return false
        }

        // check if any of the below tags present in response, then only call processAttackResponse
        let body = _.get(attack, "result.resp.body")
        // if (/(?:https:\/\/www\.|\()indusface(?:9868\)|\.com)/i.test(body)) {
        if (/< ?(?:iframe|a|img|form|input|link|base) |< ?script.{1,10}src=|location\.|< ?(param|embed)[\w\W]+?FlashVars/i.test(body)) {
            return true
        }
        // }
        return false
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        //if vuln detected for a req then return
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.userControllableTagVuln && pluginDataForRequest.partialUserContScript && pluginDataForRequest.userContDOMLocVuln) {
            return
        }

        let details = []
        let dts_src = []
        let dts_xsf = []
        let domloc_dts = []

        let matched_regexp = /(?:https:\/\/www\.|\()indusface(?:9868\)|\.com)/i
        let ResBody = _.get(attack, "result.resp.body")
        let tagsDts = ResBody.match(/< ?(?:iframe|a|img|form|input|link|base) .+?>|< ?script>[\w\W]+?<\/script>|< ?param.+?FlashVars.+?>|< ?embed[\w\s\/"=.#%&(:)]+?FlashVars[\w\W]+?\/?>/ig)
        if (tagsDts.length > 0) {// && /(?:https:\/\/www\.|\()indusface(?:9868\)|\.com)/.test(tagsDts)) {
            let page = cheerio.load(ResBody, {
                baseURI: attack.href
            });
            let currval = false
            tagsDts.forEach(ele => {
                if (/FlashVars/i.test(ele)) {
                    /**
                     * <embed src="https://example.com/malicious.swf" type="application/x-shockwave-flash" AllowScriptAccess="always" allowNetworking="all" width="1" height="1" FlashVars="param1=<iframe src='https://malicious-site.com/'></iframe>"/>
                     * <param name=FlashVars value="myVariable=Hello%20World&mySecondVariable=Goodbye" />
                     */
                    if (matched_regexp.test(ele)) {
                        dts_xsf.push({
                            dom_element: `${ele}`
                        })
                    }
                    else {
                        try {
                            if (/< ?embed/i.test(ele)) {
                                currval = page(ele).prop(FlashVars)
                                if (matched_regexp.test(currval)) {
                                    dts_xsf.push({
                                        dom_element: `${ele}, values=${currval}`
                                    })
                                }
                            }
                            else if (/< ?param/i.test(ele)) {
                                currval = page(ele).prop(value)
                                if (matched_regexp.test(currval)) {
                                    dts_xsf.push({
                                        dom_element: `${ele}, values=${currval}`
                                    })
                                }
                            }
                        } catch (e) { return }
                    }
                }
                // if (ele.includes('indusface')) {
                else if (ele.includes('src') && (!pluginDataForRequest.partialUserContScript || !pluginDataForRequest.userControllableTagVuln)) {
                    currval = page(ele).prop('src')
                    if (matched_regexp.test(currval)) {
                        if (ele.includes('script') && !pluginDataForRequest.partialUserContScript) {
                            dts_src.push({
                                dom_element: `${ele}, values=${currval}`
                            })
                        }
                        else {
                            details.push({
                                dom_element: `${ele}, values=${currval}`
                            })
                        }
                    }
                }
                else if (ele.includes('href') && !pluginDataForRequest.userControllableTagVuln) {
                    currval = page(ele).prop('href')
                    if (matched_regexp.test(currval)) {
                        details.push({
                            dom_element: `${ele}, values=${currval}`
                        })
                    }
                }
                else if (ele.includes('input') && ele.includes('value') && !pluginDataForRequest.userControllableTagVuln) {
                    currval = page(ele).prop('value')
                    if (matched_regexp.test(currval)) {
                        details.push({
                            dom_element: `${ele}, values=${currval}`
                        })
                    }
                }
                else if (ele.includes('action') && !pluginDataForRequest.userControllableTagVuln) {
                    currval = page(ele).prop('action')
                    if (matched_regexp.test(currval)) {
                        details.push({
                            dom_element: `${ele}, values=${currval}`
                        })
                    }
                }
                else if (ele.includes('script') && /(window|location)\./i.test(ele) && !pluginDataForRequest.userContDOMLocVuln) {
                    ele = ele.replace(/\n/g, '')
                    let arg = ele.match(/\w+\s=\s(?:window\.)?(?:location|document)\.(?:hash|host|hostname|search|pathname|origin|href|url|replace|reload)/igm)
                    if (arg.length > 0) {
                        let argname = arg[0].split('=')[1]
                        argname = argname.split('.')
                        argname = argname[argname.length - 1]
                        let uriRaw = new URL(attack.href)
                        if (/origin/i.test(argname)) { currval = uriRaw.origin }
                        else if (/host/i.test(argname)) { currval = uriRaw.host }
                        else if (/url|href|serach|pathname/i.test(argname)) { currval = uriRaw.href }
                        // currval = page(ele).prop(argname)
                        if (matched_regexp.test(currval)) {
                            domloc_dts.push({
                                dom_element: `${ele}, values=${currval}`
                            })
                        }
                    }
                }
            });
            page = null
        }        

        if (details.length > 0 && !pluginDataForRequest.userControllableTagVuln) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
            pluginDataForRequest.userControllableTagVuln = true
        }
        if (dts_src.length > 0 && !pluginDataForRequest.partialUserContScript) {
            this.addVulnerabilitytoResult(attack, this.partialusercont, dts_src)
            pluginDataForRequest.partialUserContScript = true
        }
        if (domloc_dts.length > 0 && !pluginDataForRequest.userContDOMLocVuln) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, domloc_dts)
            pluginDataForRequest.userContDOMLocVuln = true
        }
        if (dts_xsf.length > 0 && !pluginDataForRequest.XSFVulnFound) {
            this.addVulnerabilitytoResult(attack, this.crosssiteflashing, dts_xsf)
            pluginDataForRequest.XSFVulnFound = true
        }

        if (!pluginDataForRequest.userContDOMLocVuln) {
            //DOM Location Property/method, Below only covered LHS.
            //let body = _.get(attack, "result.resp.body", "")
            let StaCode = _.get(attack, 'result.resp.httpResponse.statusCode', "")
            if (/www.indusface/i.test(ResBody) && StaCode != 302) {
                if (/location\./i.test(ResBody)) {
                    let domloc = /location\.(href|host(name)?|origin|assign|replace)\s?(=|\()\s?"(https:\/\/)?www\.indusface\.com/ig
                    let currval = ResBody.match(domloc)
                    if (currval) {
                        domloc_dts.push({
                            dom_element: currval
                        })
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, domloc_dts)
                        pluginDataForRequest.userContDOMLocVuln = true
                    }
                }
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == this.vulnerabilityID || vulnID == this.partialusercont) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['indusface', 'https://www.indusface.com']);
        }
    }
}

const _attackVectors = [
    `(indusface9868)`,
    `https://www.indusface.com`
]
module.exports = UserControllableTagsParameters

/**
 * .... These are the tags and it's attributes where attributes value will be changed after sending the attack. 
 * More can be added in the list as necessary.
 */
/* const tagsToCheck = [
    { tag: 'iframe', attr: 'src' },
    { tag: 'a', attr: 'href' },
    { tag: 'link', attr: 'href' },
    { tag: 'input', attr: 'value' },
    { tag: 'img', attr: 'src' },
    { tag: 'form', attr: 'action' },
    { tag: 'base', attr: 'href' },
    { tag: 'script', attr: 'src' }] */

/* let $ = _.get(attack, 'result.resp.httpResponse.cheerio')
        for (let tags of tagsToCheck) {
            let tagElements = $(tags.tag)
            tagElements.each(function (index, el) {
                let val = $(el).attr(tags.attr)
                if (val && val == attack.vector && tags.tag != 'script' && !pluginDataForRequest.userControllableTagVuln) {
                    details.push({
                        name: $(this).attr(tags.attr),
                        dom_element: $.html(this)
                    })
                }
                if (val && tags.tag == 'script' && val.includes('indusface') && !pluginDataForRequest.partialUserContScript) {
                    dts_src.push({
                        name: $(this).attr(tags.attr),
                        dom_element: $.html(this),
                        target_tag: tags.tag
                    })
                }
            })
        } */