const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = 'Cookies'
const cookieParse = require('cookie-parse');

class CookieIterator extends ParameterizedDelegate {

    static isCookieExistInRequest(httpRequest) {
        if(httpRequest.headers.Cookie) {
            return true
        } else if(httpRequest.headers.cookie) {
            return true
        } else {
            return false
        }
    }


    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    /**
     * @param {request} request the request whose cookies we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     * @param {options} options Options to control URI path iterator
     */
    constructor(request, scanStore, options) {
        super(request, scanStore, _ParameterType)
        this.cookieObj = this.getCookieObj(_.cloneDeep(request))
        this.options = options
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

   /**
    * get parsed cookie from request object header
    * @param {reqCookie} reqCookie request header
    */
    getCookieObj(request) {
        let reqCookie
        if(request.httpRequest.headers.Cookie) {
            this.isCookieSmallCase = false
            reqCookie = request.httpRequest.headers.Cookie  
        } else if (request.httpRequest.headers.cookie) {
            this.isCookieSmallCase = true
            reqCookie = request.httpRequest.headers.cookie    
        }
        let parsedCookies = cookieParse.parse(reqCookie)
        return parsedCookies
    }

    /**
     * iterate through the cookies
     */

    * getIterator() {
        // iterate through all params
        if (this.options.replaceValue) {
            for (let param in this.cookieObj) {
                yield ({
                    name: param,
                    val: this.cookieObj[param],
                    resetRequired: true
                })
            }
        }
        if (this.options.appendVector) {
            this.reset()
            for (let param in this.cookieObj) {
                this.appendVector = true
                yield ({
                    name: param,
                    val: this.cookieObj[param],
                    resetRequired: true
                })
            }
        }
    }

    /**
     * replace/append the cookie value for a given param
     * @param {*} param  whose value to be replace/append
     * @param {*} value  value to be replace/append
     * @param {*} encoding enocding
     */
    modifyParam(param, value, encoding) {
        if (this.appendVector) {
            this.reset()
            this.cookieObj[param] = this.cookieObj[param] + value
        } else {
            this.cookieObj[param] = value
        }
    }

    /**
     * get the request object
     */
    getHttpRequest() {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        if(this.isCookieSmallCase) {
            req.headers.cookie = this.getCookieToSet()
        } else {
            req.headers.Cookie = this.getCookieToSet()
        } 

        return req
    }

    /**
     * reset the cookie object 
     * */ 
    reset() {
        this.cookieObj = this.getCookieObj(_.cloneDeep(this.originalRequest))
    }

    /**
     * get the cookies header in a format as required by the request
     */
    getCookieToSet() {
        let strCookie = ''
        let strd = ";"
        for (let cookieKey in this.cookieObj ) {
            if (strCookie != "") {
                strCookie = strCookie + strd
            }
            strCookie = strCookie + cookieKey + "=" + this.cookieObj[cookieKey];
        }
        return strCookie
    }  
}      
module.exports = CookieIterator