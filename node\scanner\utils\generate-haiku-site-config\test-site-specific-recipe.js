recipe1 = [{
        "name": "dont attack cookie and origin headers",
        "scope": "plugin",
        "match": "return /Protocol: HTTP Headers/.test(pluginName)", // body of function (vuln, vulnName, pluginName, plugin){ }, return bool, called for each vuln Id
        "edit": "plugin.options.headersToIterate = plugin.options.headersToIterate.filter( x => !['Cookie', 'Origin'].includes(x))", // body of function (plugin, pluginName){ }, do any edit necc.
    },
    {
        "name": "max parallel requests is 7",
        "scope": "HTTPRequestMonster",
        "edit": "settings.perDomainMaxParallelRequests = 7", // body of function (settings){ }, do any edit necc. 
    }
]

recipe2 = [{
    "name": "only allow text/html",
    "scope": "HTTPRequestMonster",
    "edit": "settings.allowedContentTypes=/text\\/html/", // body of function (settings){ }, do any edit necc. 
}]


recipe3 = [{
        "name": "xss plugin - don't report vulns",
        "scope": "vulnerability",
        "match": "return vulnName=='ID-xss-injection'", // body of function (vuln, vulnName, pluginName, plugin){ }, return bool, called for each vuln Id
        "edit": "vuln.reportVulnerability = false", // body of function (vuln, vulnName, pluginName, plugin){ }, return bool, called for each vuln Id
    },
    {
        "name": "Don't attack any headers",
        "scope": "plugin",
        "match": "return /Protocol: HTTP Headers/i.test(pluginName)", // body of function (pluginName, plugin){ }, return bool. called for each plugin
        "edit": "plugin.canAttack = false", // body of function (plugin, pluginName){ }, do any edit necc. Called only when above search is true
    },
    {
        "name": "Don't process response for Cookie Manipulation",
        "scope": "plugin",
        "match": "return /Cookie Manipulation/i.test(pluginName)", // body of function (pluginName, plugin){ }, return bool. called for each plugin
        "edit": "plugin.processAttackResponse = false", // body of function (plugin, pluginName){ }, do any edit necc. Called only when above search is true
    }
]

recipe5 = [{
        "name": "Turn off all plugins",
        "scope": "DefaultPluginSettings",
        "edit": "settings.canAttack=false", // body of function (settings){ }, do any edit necc. 
    },
    {
        "name": "Enable xss,sourcecode,sql plugin",
        "scope": "plugin",
        "match": "return /Protocol|Cross Site|SQL Injection|Source Code|Original/i.test(pluginName)", // body of function (pluginName, plugin){ }, return bool. called for each plugin
        "edit": "plugin.canAttack = true", // body of function (plugin, pluginName){ }, do any edit necc. Called only when above search is true
    },
    {
        "name": "Don't attack URI Path",
        "scope": "plugin",
        "match": "return pluginName == 'Protocol: URI Path Splitter'", // body of function (pluginName, plugin){ }, return bool. called for each plugin
        "edit": "plugin.canAttack = false", // body of function (plugin, pluginName){ }, do any edit necc. Called only when above search is true
    }
]

// ------- ******* ------- ******* ------- ******* -------
//          CS TEAM RECIPE TEMPLATES
// ------- ******* ------- ******* ------- ******* -------
// Only XSS, SQL recipe
CS_Template_XSS_SQL_ONLY = [
    {
        "__comment1__": "Remember canAttack, processAttackResponse, reportVulnerability are 3 difefrent settings",
        "__comment2__": "eg. even if SQL Injection attacks are turned off, you can find vuln. in response to XSS attack",
        "__comment3__": "3 settings above give fine grained control over attack, detection and reporting",

        "name": "Turn off all plugin attacks",
        "scope": "DefaultPluginSettings",
        "edit": "settings.canAttack=false"
    },
    {
        "name": "Enable Protocol and Original Crawler Request plugins",
        "scope": "plugin",
        "match": "return /^Protocol:|^Original/.test(pluginName)",
        "edit": "plugin.canAttack = true"
    },
    {
        // -- This is the only step you should change. --
        // Change step as needed and delete lines with '//' -> not valid JSON
        "name": "Enable Protocol and Original Crawler Request plugins",
        "scope": "plugin",
        "match": "return /^Cross Site Scripting|^SQL Injection/.test(pluginName)",
        "edit": "plugin.canAttack = true"
    }
]

// Additional vector for xss
CS_Additional_XSS_VECTOR = 
[
    {
        // -- This is the only step you should change. --
        // Change step as needed and delete lines with '//' -> not valid JSON
        "__comment1__": "XSS plugin has 2 vulnerabilities, attack response will be checked for both XSS and HTML Injection.",
        "__comment2__": "** For XSS specifically, use haikumsg instead of alert in your attack vectors **",
        "__comment3__": "Vectors have to be escaped appropriately. Ensure you check the generated config to",
        "__comment4__": "make sure the vector is what you expect it to be. Generated JSON should also be escaped.",
        "__comment5__": "set skipDefaultVectors to true if you want to run only vectors specified in this config and not the built in ones",

        "name": "Additional (site specific) vectors for XSS plugin",
        "scope": "plugin",
        "match": "return /Cross Site Scripting/i.test(pluginName)",
        "edit": "plugin.skipDefaultVectors=false;plugin.siteSpecificVectors = [`<svg><animate onbegin=haikumsg(1) attributeName=x dur=1s>`]"
    }
]

// Change number of vulnerabilities to report
CS_Report_20_Unvalidated_Redirection_Vulns = 
[
    {
        // -- This is the only step you should change. --
        // Change step as needed and delete lines with '//' -> not valid JSON
        "__comment1__": "set maxInstancesOfEachVulnToReport to -1 for unlimited (report all that are found)",

        "name": "report max 20 vulns for ID-unvalidated-redirection",
        "scope": "vulnerability",
        "match": "return vulnName=='ID-unvalidated-redirection'",
        "edit": "vuln.maxInstancesOfEachVulnToReport = 20",
    }
]


// Don't attack cookie and origin headers
CS_Dont_Attack_Cookie_Origin_Headers = 
[
    {
        // -- This is the only step you should change. --
        // Change step as needed and delete lines with '//' -> not valid JSON

        "name": "Don't attack cookie and origin headers",
        "scope": "plugin",
        "match": "return /Protocol: HTTP Headers/.test(pluginName)",
        "edit": "plugin.options.headersToIterate = plugin.options.headersToIterate.filter( x => !['Cookie', 'Origin'].includes(x))"
    }
]

// Send up to 30 requests at a time
CS_Send_30_Requests_In_Parallel = 
[
    {
        // -- This is the only step you should change. --
        // Change step as needed and delete lines with '//' -> not valid JSON

        "name": "set max parallel requests to 30",
        "scope": "HTTPRequestMonster",
        "edit": "settings.perDomainMaxParallelRequests = 30", // body of function (settings){ }, do any edit necc. 
    }
]


// Prevent all plugins from attack
// Use CS_Template_XSS_SQL_ONLY and delete last step

// Enable plugins
// When we have site specific plugins and/or plugins that are shipped in disabled state
// they can be enabled using site specific recipe.

module.exports = recipe5