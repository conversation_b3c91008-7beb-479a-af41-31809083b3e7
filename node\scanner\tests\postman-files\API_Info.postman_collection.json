{"info": {"_postman_id": "e72c0175-3725-4bcd-ad34-fd6b67eddce7", "name": "API_Info", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "https://devtas.indusface.com/wafportal/rest/loginService/login", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"<EMAIL>\",\n  \"password\": \"Test@12345\",\n  \"code\": \"\",\n  \"tasPortal\": true\n}"}, "url": {"raw": "https://devtas.indusface.com/wafportal/rest/loginService/login", "protocol": "https", "host": ["devtas", "indusface", "com"], "path": ["wafportal", "rest", "loginService", "login"]}, "description": "<PERSON><PERSON>"}, "response": []}, {"name": "https://devtas.indusface.com/wafportal/rest/loginService/getallusers/1597", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "siteId", "value": "5888", "type": "text"}], "url": {"raw": "https://devtas.indusface.com/wafportal/rest/loginService/getallusers/1597", "protocol": "https", "host": ["devtas", "indusface", "com"], "path": ["wafportal", "rest", "loginService", "getallusers", "1597"]}, "description": "Get All Users"}, "response": []}, {"name": "https://devtas.indusface.com/wafportal/rest/loginService/createuser", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "siteId", "value": "5888", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"customerId\": \"1597\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"userName\": \"<EMAIL>\",\r\n  \"fullName\": \"Demo Test Jan 07\",\r\n  \"twofa\": false,\r\n  \"role\":4\r\n}"}, "url": {"raw": "https://devtas.indusface.com/wafportal/rest/loginService/createuser", "protocol": "https", "host": ["devtas", "indusface", "com"], "path": ["wafportal", "rest", "loginService", "createuser"]}, "description": "Create User"}, "response": []}]}