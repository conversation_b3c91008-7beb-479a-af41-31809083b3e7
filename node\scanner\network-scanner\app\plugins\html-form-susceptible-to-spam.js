const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * HTML form susceptible to spam plugin strategy:
 * Here for every request made, the response source code will be checked for presence of any form having 
 * input type as hidden and specifically it's value as email.
 * 
 * If above condition is found then vulnerability will be reported
 */
class HiddenFieldSusceptibleToSpam extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-hidden-field-susceptible-to-spam'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {
        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {
            // check if input type hidden present in response body, then only call processAttackResponse
            let body = _.get(originalRequest, "result.resp.body")
            //if  one of our email id is present, then skip
            if (/<EMAIL>|test@indusface\.com/i.test(body) || /<EMAIL>/i.test(body)) {
                return false
            }
            if (/<input\stype=hidden\s|<input\stype="hidden"\s/i.test(body)) {
                return true
            }
        }
        return false
    }

    /**
     * Check response for html form susceptible to spam vuln
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        let vuln
        let $ = _.get(originalRequest, 'result.resp.httpResponse.cheerio');
        let forms = $("form")

        //adding check to report only for get or post methods as earlier it was reporting to Options method
        // which is not valid case
        if (forms.length > 0 && (/get/i.test(originalRequest.httpRequest.method) || /post/i.test(originalRequest.httpRequest.method))) {
            let hiddenInputField = $('input[type=hidden]', forms)
            let details = []
            if (hiddenInputField.length > 0) {
                // check each hidden input value attribute if any of them as email id present
                hiddenInputField.each(function (index) {
                    if ($(this).attr('value') != undefined) {
                        let res = $(this).attr('value').match(/[A-Z0-9._-]+@[A-Z0-9][A-Z0-9.-]{0,61}[A-Z0-9]\.[A-Z.]{2,6}/ig)
                        if (res) {
                            details.push({
                                name: $(this).attr('name'),
                                value: $(this).attr('value')
                            })
                        }
                    }
                })
                vuln = {
                    name: 'ID-hidden-field-susceptible-to-spam',
                    details: details
                }
            }
        }
        // vuln is an array so have to update condition from only vuln to vuln.details.length > 0
        if (vuln && vuln.details.length > 0) {
            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, vuln)
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['<input', 'type=\"hidden\"']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method"]);
    }
}

module.exports = HiddenFieldSusceptibleToSpam