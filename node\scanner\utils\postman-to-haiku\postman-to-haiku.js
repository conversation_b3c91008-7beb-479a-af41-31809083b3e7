const fs = require('fs')
const PostmanCollection = require('postman-collection')
const caseless = require('caseless')
const querystring = require('querystring')
const Minimist = require('minimist')
const _ = require('lodash')
const path = require('path')
const s3Utils = require('../../common/lib/s3-utils')
const logger = require('../../common/lib/haiku-logger')
const { AUTH_TYPES } = require('../../common/config/app-constants')
const HaikuUtils = require('../../common/lib/haiku-utils')

class AuthInfo {
    constructor(type, details) {
        this.type = type;
        this.details = details;
    }
}

/**
 * Convert postman file to haiku consumable format. Can handle 
 * ItemGroups
 * Requests: types = raw/urlencoded/json
 * variables
 * authentication: <define types>
 */
class PostmanToHaiku {
    postmanCollection = null

    // common properties
    name // collection Name
    description // collection description
    scriptVariables = {} // collection variables.
    haikuRequests = []

    /**
     * Parse postman collection from file
     * @param {string} postManFile postman filename to parse
     * @param {string} annotatedFile annotated file name to be saved
     * @param {string} fileMode file mode for storage to be use
     */
    constructor(postManFile, annotatedFile) {
        this.postManFile = postManFile;
        this.annotatedFile = annotatedFile;
        this.s3Utils = s3Utils;
    }

    getProperties(propList) {
        //return propList.all().filter(x => !x.disabled).map(this.getProperty)
        let ob = {}
        let props = propList.all()
        for (let prop of props) {
            if (prop.disabled) {
                continue
            }
            ob[prop.key] = prop.value
        }

        return ob
    }

    getProperty(prop) {
        let ob = {};
        ob[prop.key] = prop.value
        return ob
    }

    parseAuthentication(auth, haikuRequest) {
        let haikuAuth
        switch (auth.type) {
            case 'noauth':
            case 'oauth2':
                // nothing to do.
                break;

            case "apikey": {
                let props = this.getProperties(auth[auth.type])
                if (props.in == 'header') {
                    haikuAuth = {
                        headers: this.getProperty(props)
                    }
                }
            }
            break;
            
            case 'basic': {
                let props = this.getProperties(auth[auth.type]);

                if(!props['username'] || !props['password']) {
                    logger.log('error', `Provided basic auth details doesn't have username/password fields defined for the request ${JSON.stringify(haikuRequest)}`)
                }

                haikuRequest.authInfo = new AuthInfo(AUTH_TYPES.BASIC, {
                    username: props['username'],
                    password: props['password']
                });
            }
            break;

            default:
                logger.log('info', `ignoring unsupported auth type ${auth.type}`)
                haikuAuth = 'skipRequest'
        }

        return haikuAuth
    }

    /**
     * Add request to haiku request list
     * @param {Object} postmanItem postman item to be converted to haiku request
     * @returns {void}
     */
    addRequest(postmanItem) {
        // convert the request to postman request
        let postmanReq = postmanItem.request;

        // skip if no url defined
        if(_.isEmpty(postmanReq.url.host)) {
            logger.log('info', `skipping request ${postmanItem.name}, as it doesn't have url defined`);
            return;
        }

        // basic stuff
        let haikuRequest = {
            name: postmanItem.name,
            description: postmanItem.description,
            uri: postmanReq.url.toString(),
            method: postmanReq.method,
            headers: this.getProperties(postmanReq.headers),
        }

        let processRequest = true

        // path vars
        if (postmanReq.url.variables) {
            haikuRequest.pathVars = this.getProperties(postmanReq.url.variables)
        }

        // body
        processRequest = this.parseRequestBody(postmanReq, haikuRequest)
        // parse haiku anotations. This is given as a response (example) with name starting with haiku-annotate
        let haikuAnnotate = postmanItem.responses.filter(resp => /^haiku-annotate/.test(resp.name))
        if (haikuAnnotate.length) {
            try {
                haikuRequest.annotations = JSON.parse(haikuAnnotate[0].body)
            } catch (err) {
                logger.log('error', `${err.toString()} parsing annotation for ${haikuRequest.name}, skipping request`)
                processRequest = false
            }
        }

        // auth
        // if request has its own auth, use it otherwise use most specific from parent hierarchy
        let auth = postmanItem.getAuth() || postmanReq.findInParents('auth')
        if (auth) {
            let haikuAuth = this.parseAuthentication(auth, haikuRequest)
            if (haikuAuth == 'skipRequest') {
                processRequest = false
            } else if (haikuAuth) {
                haikuRequest.addToRequest = haikuAuth
            }
        }

        // event
        let events = postmanItem.findInParents('event') || postmanItem.getEvents()

        if (events.length) {
            let event = _.find(events, { listen: 'test' });

            if (event) {
                haikuRequest.annotations = HaikuUtils.parseHaikuScript(event.script.exec);
                logger.log('info', `haikuRequest annotations: ${haikuRequest.annotations}`);
            }
        }

        if (processRequest) {
            this.haikuRequests.push(haikuRequest)
        }
    }

    /**
     * Parse request body based on mode
     * @param {Object} postmanReq postman request object
     * @param {Object} haikuRequest haiku request object
     * @returns {boolean} true if parsed fine, false otherwise
     */
    parseRequestBody(postmanReq, haikuRequest) {
        let parsedFine = true
        if (postmanReq.body) {
            haikuRequest.mode = postmanReq.body.mode
            let rawBody = postmanReq.body[postmanReq.body.mode];
            try {
                let headers = caseless(haikuRequest.headers);
                let contentType = headers.get('content-type');
                let bodyOptions = postmanReq.body.options; 

                switch (haikuRequest.mode) {
                    case 'raw':
                        {
                            // from https://github.com/postmanlabs/postman-code-generators/blob/master/codegens/nodejs-request/lib/parseRequest.js
                            // Match any application type whose underlying structure is json
                            // For example application/vnd.api+json
                            // All of them have +json as suffix
                            haikuRequest.body = rawBody;
                            if (contentType && (contentType === 'application/json' || contentType.match(/\+json$/))) {
                                haikuRequest.body = _.isObject(rawBody) ? JSON.stringify(rawBody) : rawBody //JSON.parse(rawBody)
                            } else if (contentType && contentType === 'application/xml') {
                                haikuRequest.body = rawBody; // Assuming rawBody is already a string
                            } else if (contentType && contentType === 'text/plain') {
                                haikuRequest.body = rawBody; // Assuming rawBody is already a string
                            } else if (contentType && contentType === 'application/x-www-form-urlencoded') {
                                haikuRequest.body = querystring.stringify(this.getProperties(rawBody));
                            } else if (contentType && contentType === 'application/javascript') {
                                haikuRequest.body = rawBody; // Assuming rawBody is already a string
                            }

                            // If content type is not set, but raw body is set, try to guess the content type using raw body language
                            if (!contentType && bodyOptions && bodyOptions.raw && bodyOptions.raw.language) {
                                switch (bodyOptions.raw.language.toLowerCase()) {
                                    case 'javascript':
                                        haikuRequest.body = rawBody;
                                        haikuRequest.headers['Content-Type'] = 'application/javascript';
                                        break;
                                    case 'json':
                                        haikuRequest.body = _.isObject(rawBody) ? JSON.stringify(rawBody) : rawBody;
                                        haikuRequest.headers['Content-Type'] = 'application/json';
                                        break;
                                    case 'xml':
                                        haikuRequest.body = rawBody;
                                        haikuRequest.headers['Content-Type'] = 'application/xml';
                                        break;
                                    case 'text':
                                        haikuRequest.body = rawBody;
                                        haikuRequest.headers['Content-Type'] = 'text/plain';
                                        break;
                                    case 'html':
                                        haikuRequest.body = rawBody;
                                        haikuRequest.headers['Content-Type'] = 'text/html';
                                        break;
                                    default:
                                        logger.log('info', `Unsupported raw body language: ${bodyOptions.raw.language}`);
                                        parsedFine = false;
                                        break;
                                }
                            }
                        }
                        break

                    case 'urlencoded': {
                        haikuRequest.body = querystring.stringify(this.getProperties(rawBody))
                    }
                    break

                    default:
                        logger.log('info', `skipping request ${haikuRequest.name}, unsupported http request body type ${haikuRequest.mode}`);
                        parsedFine = false
                }
            } catch (err) {
                logger.log('error', `${err.toString()} parsing request body for${haikuRequest.name}, skipping request`);
                parsedFine = false
            }
        }
        return parsedFine
    }

    async readContents() {
        let postManContent = await this.s3Utils.getFile(path.dirname(this.postManFile), path.basename(this.postManFile));
        this.postmanCollection = new PostmanCollection.Collection(JSON.parse(postManContent.Body))
        this.name = this.postmanCollection.name || '<none>'
        this.description = this.postmanCollection.description ? this.postmanCollection.description.toString() : '<none>'
        this.scriptVariables = this.getProperties(this.postmanCollection.variables)

        let globalEvent = _.find(this.postmanCollection.events.members, { listen: 'test' });

        if(globalEvent) {
            this.annotations = HaikuUtils.parseHaikuScript(globalEvent.script.exec);
        }
    }

    parseAndFlatten(itemGroup) {
        itemGroup.items.each((item) => {
            if (PostmanCollection.ItemGroup.isItemGroup(item)) {
                this.parseAndFlatten(item)
            } else if (PostmanCollection.Item.isItem(item)) {
                this.addRequest(item)
            } else {
                logger.log('info', 'unknown type of item', item.name);
            }
        })
    }

    async save(substituteVars = false) {
        let haikuJson = {
            name: this.name,
            description: this.description,
            //auth : this.auth,
            scriptVariables: this.scriptVariables,
            annotations: this.annotations,
            haikuRequests: this.haikuRequests
        }

        // substitute vars globally if needed
        let haikuAnnotatedJSON = JSON.stringify(haikuJson)
        
        if (substituteVars) {
            for (let collVar of Object.keys(haikuJson.scriptVariables)) {
                let name = collVar
                let val = haikuJson.scriptVariables[name]
                let replacer = new RegExp(`{{${name}}}`, 'g')
                haikuAnnotatedJSON = haikuAnnotatedJSON.replace(replacer, val)
            }
        }

        await this.s3Utils.upload(path.dirname(this.annotatedFile), path.basename(this.annotatedFile), haikuAnnotatedJSON);
    }

    /**
     * getter for internal postman collection
     */
    get collection() {
        return this.postmanCollection
    }

    getAllRequests(itemGroup = this.postmanCollection) {
        let allReqs = []
        itemGroup.items.each((item) => {
            if (PostmanCollection.ItemGroup.isItemGroup(item)) {
                allReqs.push(...this.getAllRequests(item))
            } else if (PostmanCollection.Item.isItem(item)) {
                allReqs.push(item)
            } else {
                logger.log('info', 'unknown type of item', item.name)
            }
        })
        return allReqs
    }

    printAllRequests() {
        let allReqs = this.getAllRequests(this.postmanCollection)
        allReqs.forEach((item) => {
            if (PostmanCollection.Item.isItem(item)) {
                item.variables && logger.log('info', `\t vars = ${item.variables}`)
            } else {
                logger.log('info', 'unknown type of item', item.name);
            }
        })
    }

    async generateAnnotatedFile() {
        try {
            await this.readContents();
            this.parseAndFlatten(this.postmanCollection)
            await this.save();
        }
        catch(err) {
            logger.log('error', `generateAnnotatedFile error: ${err.toString()}`);
            return `generateAnnotatedFile error: ${err.toString()}`;
        }
    }
}

// --- Main code
let argv = Minimist(process.argv.slice(2), {
    boolean: true,
    alias: {
        o: "out-file",
        dryrun: "dry-run",
        f: "postman-file"
    }
})


async function main() {
    // set up vars
    let postmanFile = argv['postman-file']
    let outFile = argv['out-file'] || 'haiku-annotated-request'
    if (!outFile.endsWith('.json')) {
        outFile += '.json'
    }
    
    const isDryRun = !!argv['dry-run']
    const start = Date.now()
    logger.log('info', `running with args: --postman-file=${postmanFile} --out-file=${outFile} ${isDryRun ? '--dry-run' : ''}`)

    // parse postman file
    let postmanToHaiku = new PostmanToHaiku(postmanFile, outFile);
    await postmanToHaiku.generateAnnotatedFile();
}

// main / module exports based on command line args
if (argv['postman-file']) {
    main()
} else {
    module.exports = PostmanToHaiku
}



// PostmanToHaiku = require( '/Users/<USER>/work/indusface/haiku/node/scanner/utils/postman-to-haiku/postman-to-haiku.js')
// postmanToHaiku = new PostmanToHaiku('../../tests/postman-files/Tiredful.postman_collection.json')
// postmanToHaiku = new PostmanToHaiku('../../tests/postman-files/Swagger\ Petstore.postman_collection.json')
// postmanToHaiku = new PostmanToHaiku('../../tests/postman-files/kk-tmp.postman_collection.json')