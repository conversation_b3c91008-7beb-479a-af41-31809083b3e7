const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RegExpVari = require('./generic-regexp');
const LoginDelegate = require('../../lib/login-delegate')

class HttpVerbTamperAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-http-verb-tampering'
    }

    getAttackVectors() {
        return httpMethodInjVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /** 
     * Performs a network attack and emits the attack-response event with response. 
     * Vulnerability chekcing plugins should check and update {@link attack.vulns}
     * @param {attack} attack The attack to perform
     */
    //Overriding the performNetworkAttack method to change method in attack and it's body specifically 
    async performNetworkAttack(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        // always perform the initial verification before the attack
        let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
        if (statusCode == '200' && attack.httpRequest.method == "HAIKU" && pluginStorageScanScope.loginsucceed) {
            return await super.performNetworkAttack(attack)
        }
        //check status code returned is 401 then only process for attacking
        else if (statusCode == '401') {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        //any arbitrary method
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (!pluginStorageScanScope.loginsucceed && attack.pluginName == 'Original Crawler Request' && attack.attackArea == "original-crawler-request") {
            let httpRequest = attack.httpRequest
            pluginStorageScanScope.loginsucceed = LoginDelegate.isLoginRequest(httpRequest)
        }
        if (attack.pluginName == this.getName()) {
            if (attack.vector != attack.paramVal) {
                let ResBody = _.get(attack, 'result.resp.body', '')
                let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
                let staCode = [204, 400, 401, 403, 404, 405]

                //Adding condition to skip for the custom error pages
                let CustomErrMsg1 = RegExpVari.RegExp.CustomErrMsg1
                let CustomErrMsg2 = RegExpVari.RegExp.CustomErrMsg2
                let CustomErrMsg3 = RegExpVari.RegExp.CustomErrMsg3
                let CustomErrMsg4 = RegExpVari.RegExp.CustomErrMsg4
                let CustomErrMsg5 = RegExpVari.RegExp.CustomErrMsg5
                if (CustomErrMsg1.test(ResBody) || CustomErrMsg2.test(ResBody) || CustomErrMsg3.test(ResBody) || CustomErrMsg4.test(ResBody) || CustomErrMsg5.test(ResBody)) {
                    return false
                }
                
                let redirect = _.get(attack, 'originalRequest.httpResponse.redirects', '')
                let AttcontentLength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', "")
                let OriContentLength = _.get(attack, 'originalRequest.httpResponse.headers["content-length"]', '')
                if (!(statusCode >= 500 || staCode.includes(statusCode)) && redirect.length == 0 && AttcontentLength == OriContentLength) {
                    return true
                }
            }
            return false
        } return false
    }

    // event handler, annotates attack parameter, no return value
    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.HttpVerbTamperFound) { return }
        let ResBody = _.get(attack, "result.resp.body", '')
        //status code is !staCode but Redirection/error is present in response body - Dont report anything
        if (/(?:<meta [\w\s\-;"=']*?(url=('|")[\:.\w\/\-]*?log(in|out))|<title>[\w\s\-:\&]*?(log(in|out)|redirect)|onload=("|')window\.document\.location=('|")[\:.\w\/\-]*?log(in|out)|window\.location\.href = ("|')[\:.\w\/\-]*?log(in|out)|class="error"|Page Not Found|<h1[\w\s\/='"]*?>[\w\s\/='"]*?login[\w\s\/='"]*?<\/h1>)/i.test(ResBody)) {
            return
        }
        else {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, { href: attack.href, method: attack.vector })
            pluginDataForRequest.HttpVerbTamperFound = true
            return
        }

    }
}

// vectors & matches ...
const httpMethodInjVectors = [
    `HAIKU`, //any arbitrary method
    `OPTIONS`,
    `GET`,
    `HEAD`,
    `POST`,
    `PUT`,
    `DELETE`,
    `TRACE`,
    `CONNECT`,
    `PROPFIND`,
    `PROPPATCH`,
    `MKCOL`,
    `COPY`,
    `MOVE`,
    `LOCK`,
    `UNLOCK`,
]

module.exports = HttpVerbTamperAttack