{"scanId":324,"scanlogId":324,"attack":{"attackCount":1,"method":"GET","name":"CSS Injection","hostname":"127.0.0.1","originalRequest":{"scanId":324,"scanlog_id":324,"scanner":"haiku","httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36"},"appTranaKey":"GET-127.0.0.1:5000/test?input","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"resourceType":"mainFrame","ip":"127.0.0.1","SiteType":"website","DataCenter":"","responseHeaders":{"Content-Type":["text/html; charset=utf-8"]},"statusCode":200,"crawlerKey":"GET-127.0.0.1:5000/test?input","uri":"http://127.0.0.1:5000/test?input=defaultText","haikuKey":"GET-127.0.0.1:5000/test?input","haikuResourceType":"core","scanlog_id":324,"scanId":324,"attackRequestId":2},"requestId":12,"haikuPriority":{"priority":100,"requestParams":["input"],"nonEmptyValueCount":1,"pathComponentsCount":1},"httpResponse":{"err":null,"body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"original-2.body\"}","headers":{"content-type":"text/html; charset=utf-8","content-length":"1016","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Wed, 11 Jun 2025 10:55:46 GMT"},"statusCode":200,"statusMessage":"OK","redirectsFollowed":0,"redirects":[]},"pluginData":{"CSS Injection":{"CssInjFound":true}}},"href":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","area":"UriQueryParameters","type":"value","param":"input","vector":"<style>body { color: red; } /*haikutest*/</style>","encoding":"uri","headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","accept-language":"en-US","referer":"http://127.0.0.1:5000/","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36","host":"127.0.0.1:5000","accept-encoding":"gzip, deflate"},"httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36"},"appTranaKey":"GET-127.0.0.1:5000/test?input","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"resourceType":"mainFrame","ip":"127.0.0.1","SiteType":"website","DataCenter":"","responseHeaders":{"Content-Type":["text/html; charset=utf-8"]},"statusCode":200,"crawlerKey":"GET-127.0.0.1:5000/test?input","uri":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","haikuKey":"GET-127.0.0.1:5000/test?input","haikuResourceType":"core","scanlog_id":324,"scanId":324,"attackRequestId":3},"httpResponse":{"headers":{"content-type":"text/html; charset=utf-8","content-length":"1131","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Wed, 11 Jun 2025 10:55:46 GMT"},"statusCode":200,"statusMessage":"OK","httpVersion":"1.0","body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"attack-3.body\"}"}},"response":{"headers":{"content-type":"text/html; charset=utf-8","content-length":"1131","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Wed, 11 Jun 2025 10:55:46 GMT"},"statusCode":200,"statusMessage":"OK","httpVersion":"1.0","body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"attack-3.body\"}"},"vulns":{"ID-css-injection":{"foundBy":"CSS Injection","productionReady":false,"canAttackInReplayScan":true,"details":"Potential CSS Injection Detected. Payload: <style>body { color: red; } /*haikutest*/</style>","autoPOC":[{"type":"original","path":"httpRequest.uri","highlightType":"text","details":["input=defaultText"],"description":"orignal request query input param value will be attack"},{"type":"attack","path":"httpRequest.uri","highlightType":"text","details":["input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"],"description":"attack request query input param value tampered with vector %3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"}]}}}{"scanId":324,"scanlogId":324,"attack":{"attackCount":1,"method":"GET","name":"CSS Injection","hostname":"127.0.0.1","originalRequest":{"scanId":324,"scanlog_id":324,"scanner":"haiku","httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36"},"appTranaKey":"GET-127.0.0.1:5000/test?input","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"resourceType":"mainFrame","ip":"127.0.0.1","SiteType":"website","DataCenter":"","responseHeaders":{"Content-Type":["text/html; charset=utf-8"]},"statusCode":200,"crawlerKey":"GET-127.0.0.1:5000/test?input","uri":"http://127.0.0.1:5000/test?input=defaultText","haikuKey":"GET-127.0.0.1:5000/test?input","haikuResourceType":"core","scanlog_id":324,"scanId":324,"attackRequestId":2},"requestId":13,"haikuPriority":{"priority":100,"requestParams":["input"],"nonEmptyValueCount":1,"pathComponentsCount":1},"httpResponse":{"err":null,"body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"original-2.body\"}","headers":{"content-type":"text/html; charset=utf-8","content-length":"1016","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Thu, 12 Jun 2025 13:01:03 GMT"},"statusCode":200,"statusMessage":"OK","redirectsFollowed":0,"redirects":[]},"pluginData":{"CSS Injection":{"CssInjFound":true}}},"href":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","area":"UriQueryParameters","type":"value","param":"input","vector":"<style>body { color: red; } /*haikutest*/</style>","encoding":"uri","headers":{"accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","accept-language":"en-US","referer":"http://127.0.0.1:5000/","sec-fetch-dest":"document","sec-fetch-mode":"navigate","sec-fetch-site":"same-origin","sec-fetch-user":"?1","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36","host":"127.0.0.1:5000","accept-encoding":"gzip, deflate"},"httpRequest":{"method":"GET","headers":{"Accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","Accept-Encoding":"gzip, deflate, br, zstd","Accept-Language":"en-US","Referer":"http://127.0.0.1:5000/","Sec-Fetch-Dest":"document","Sec-Fetch-Mode":"navigate","Sec-Fetch-Site":"same-origin","Sec-Fetch-User":"?1","Upgrade-Insecure-Requests":"1","User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.6613.186 Electron/32.3.3 Safari/537.36"},"appTranaKey":"GET-127.0.0.1:5000/test?input","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"resourceType":"mainFrame","ip":"127.0.0.1","SiteType":"website","DataCenter":"","responseHeaders":{"Content-Type":["text/html; charset=utf-8"]},"statusCode":200,"crawlerKey":"GET-127.0.0.1:5000/test?input","uri":"http://127.0.0.1:5000/test?input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E","haikuKey":"GET-127.0.0.1:5000/test?input","haikuResourceType":"core","scanlog_id":324,"scanId":324,"attackRequestId":3},"httpResponse":{"headers":{"content-type":"text/html; charset=utf-8","content-length":"1131","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Thu, 12 Jun 2025 13:01:03 GMT"},"statusCode":200,"statusMessage":"OK","httpVersion":"1.0","body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"attack-3.body\"}"}},"response":{"headers":{"content-type":"text/html; charset=utf-8","content-length":"1131","server":"Werkzeug/2.0.3 Python/3.6.0","date":"Thu, 12 Jun 2025 13:01:03 GMT"},"statusCode":200,"statusMessage":"OK","httpVersion":"1.0","body":"{\"prefix\":\"scanner/httpResponse/324\",\"name\":\"attack-3.body\"}"},"vulns":{"ID-css-injection":{"foundBy":"CSS Injection","productionReady":false,"canAttackInReplayScan":true,"details":"Potential CSS Injection Detected.\nPayload: <style>body { color: red; } /*haikutest*/</style>\nDetection Method: Style Attribute","autoPOC":[{"type":"original","path":"httpRequest.uri","highlightType":"text","details":["input=defaultText"],"description":"orignal request query input param value will be attack"},{"type":"attack","path":"httpRequest.uri","highlightType":"text","details":["input=%3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"],"description":"attack request query input param value tampered with vector %3Cstyle%3Ebody%20%7B%20color%3A%20red%3B%20%7D%20%2F*haikutest*%2F%3C%2Fstyle%3E"}]}}}