const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// select action
class FormSubmitAction {
    constructor(xpath, submitAction, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.annotation = annotation
        this.submitAction = submitAction
    }

    get actionType() {
        return 'form-submit'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.submitAction, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let encXPath = utils.encode(this.xpath)
        let encSubmitAction = utils.encode(this.submitAction)
        let jscode = `indusfaceRenderer.submitForm('${encXPath}', '${encSubmitAction}')`
        return await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true))
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} for ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = FormSubmitAction