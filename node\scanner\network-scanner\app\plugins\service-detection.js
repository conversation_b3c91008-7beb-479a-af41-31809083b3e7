const NetworkAttack = require('./network-attack');
const { URL } = require('url');
const _ = require('lodash');

class ServiceChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-Services-Detection';
    }

    /**
     * @param  {object} attack the attack that was performed including http request+response
     * @override
     */

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.servicesdetection) {
            return;
        }
    
        let servicesDetail = this.getUrlAnalysis(attack);
    
        // Check for "PONG" in the response body and if the URI includes '/webtools/control/ping'
        if (servicesDetail) {
            // Extracting only "name" and "version" from technologies
            const technologiesDetails = servicesDetail.technologies.map(tech => ({
                name: tech.name,
                version: tech.version || "N/A" // If version is not available, use "N/A"
            }));

            // Constructing the vulnerability description
            const vulnDetails = technologiesDetails.map(tech => `Name: ${tech.name}, Version: ${tech.version}`).join("\n");

            const vulnerabilityDescription = `Services Details Technolgy:\n${vulnDetails}`;
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnerabilityDescription);
            pluginDataForRequest.servicesdetection = true;
        }
    }
}

module.exports = ServiceChecker;
