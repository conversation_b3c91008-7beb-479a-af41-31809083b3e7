const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require('cheerio')

class CSSINJ extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-css-injection'
    }

    getAttackVectors() {
        return __CssInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'form-encoded-post', 'json-body']  // 
    }

    wantProcessAttackResponse(attack) {
        return attack.pluginName === this.getName()
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.CssInjFound) {
            return
        }

        let contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        let responseBody = _.get(attack, 'result.resp.body', '')

        const possibleCType = /text\/html|application\/xhtml\+xml/i
        if (!possibleCType.test(contentType)) {
            return
        }

        const vector = attack.vector
        let foundInStyleTag = false
        let foundInStyleAttr = false

        // Use cheerio to parse the DOM and check for CSS context
        try {
            const $ = cheerio.load(responseBody)
            // Check for <style> tag injection
            $('style').each((i, el) => {
                if ($(el).html() && $(el).html().includes(vector)) {
                    foundInStyleTag = true
                }
            })
            // Check for inline style attribute injection
            $('[style]').each((i, el) => {
                if ($(el).attr('style') && $(el).attr('style').includes('haikutest')) {
                    foundInStyleAttr = true
                }
            })
        } catch (e) {
            // need fallback to reflection check if DOM parsing fails
        }

        // Fallback: simple reflection detection
        const escapedVector = _.escapeRegExp(vector)
        const reflectionRegex = new RegExp(escapedVector, 'i')
        let foundByReflection = reflectionRegex.test(responseBody)

        if (foundInStyleTag || foundInStyleAttr || foundByReflection) {
            const result =
                `Potential CSS Injection Detected. \nPayload: ${vector}`
            
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
            pluginDataForRequest.CssInjFound = true
        }
    }
}

const __CssInjVectors = [
    `<style>body { color: red; } /*haikutest*/</style>`,
    `"><style>body{color:blue;}</style>`,
    `" style="color:red;/*haikutest*/`,
    `</style><style>body{background:blue;}</style>`,
    `</title><style>body{color:green;}</style>`,
    '<style>#output { border: 3px dashed orange; } /*haikutest*/</style>',
    '</style><style>p { font-style: italic; } /*haikutest*/</style>'

];

module.exports = CSSINJ
