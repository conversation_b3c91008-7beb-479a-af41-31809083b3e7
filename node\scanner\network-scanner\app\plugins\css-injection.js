const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require('cheerio')

class CSSINJ extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-css-injection'
    }

    getAttackVectors() {
        return __CssInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'form-encoded-post', 'json-body']
    }

    wantProcessAttackResponse(attack) {
        return attack.pluginName === this.getName()
    }

    isValidCSSSyntax(css) {
        try {
            const cssRules = css.split(';').filter(rule => rule.trim())
            for (const rule of cssRules) {
                if (!rule.includes(':')) return false
                const [property, value] = rule.split(':').map(s => s.trim())
                if (!property || !value) return false
                if (/expression\s*\(|javascript:/i.test(value)) return false
            }
            return true
        } catch {
            return false
        }
    }

    isInCSSContext(html, vector) {
        const lowerHtml = html.toLowerCase()
        const lowerVector = vector.toLowerCase()
        return (
            lowerHtml.includes('<style') && lowerHtml.includes(lowerVector) ||
            lowerHtml.includes('style="') && lowerHtml.includes(lowerVector)
        )
    }

    /**
     * Ultra-strict processing with multiple validation layers - ZERO false positives
     */
    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CssInjFound) return

        const responseBody = _.get(attack, 'result.resp.body', '')
        const vector = attack.vector
        const originalBody = _.get(attack, 'originalRequest.result.resp.body', '')

        // STRICT REQUIREMENT: Must contain our unique marker
        if (!vector.includes(this.uniqueMarker)) {
            return
        }

        // STRICT REQUIREMENT: Vector must not exist in original response
        if (originalBody && originalBody.includes(vector)) {
            return
        }

        // STRICT REQUIREMENT: Must be valid CSS syntax
        if (!this.isValidCSSSyntax(vector)) {
            return
        }

        // STRICT REQUIREMENT: Must be in valid CSS context
        if (!this.isInValidCSSContext(responseBody, vector)) {
            return
        }

        // STRICT REQUIREMENT: Response must contain exact vector match
        if (!responseBody.includes(vector)) {
            return
        }

        // STRICT REQUIREMENT: Vector must appear in proper CSS structure
        let validCSSInjection = false
        let detectionMethod = ''

        try {
            const $ = cheerio.load(responseBody)

            // Check style tags with ultra-strict validation
            $('style').each((_, el) => {
                const styleContent = $(el).html() || ''
                if (styleContent.includes(vector) &&
                    styleContent.includes(this.uniqueMarker) &&
                    this.isValidCSSStructure(styleContent, vector)) {
                    validCSSInjection = true
                    detectionMethod = 'Style Tag Injection'
                    return false // break
                }
            })

            // Check inline style attributes with ultra-strict validation
            if (!validCSSInjection) {
                $('[style]').each((_, el) => {
                    const styleAttr = $(el).attr('style') || ''
                    if (styleAttr.includes(vector) &&
                        styleAttr.includes(this.uniqueMarker) &&
                        this.isValidCSSStructure(styleAttr, vector)) {
                        validCSSInjection = true
                        detectionMethod = 'Inline Style Injection'
                        return false // break
                    }
                })
            }

        } catch (error) {
            // No fallback - if cheerio fails, we don't report
            return
        }

        // FINAL VALIDATION: Only report if ALL conditions are met
        if (validCSSInjection && detectionMethod) {
            // Additional verification: check response size difference
            const sizeDifference = Math.abs(responseBody.length - (originalBody?.length || 0))
            if (sizeDifference < vector.length) {
                return // Response didn't grow as expected
            }

            const result = {
                message: `CSS Injection Confirmed`,
                payload: vector,
                method: detectionMethod,
                confidence: 'VERIFIED',
                marker: this.uniqueMarker
            }

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
            pluginDataForRequest.CssInjFound = true
        }
    }

    /**
     * Validate that CSS is properly structured within the context
     */
    isValidCSSStructure(cssContent, vector) {
        try {
            // Must contain our marker
            if (!cssContent.includes(this.uniqueMarker)) {
                return false
            }

            // Must contain the complete vector
            if (!cssContent.includes(vector)) {
                return false
            }

            // Check for proper CSS rule structure around our vector
            const vectorIndex = cssContent.indexOf(vector)
            const beforeVector = cssContent.substring(0, vectorIndex)
            const afterVector = cssContent.substring(vectorIndex + vector.length)

            // Must be part of a valid CSS rule or declaration
            const hasValidStructure =
                /[{;]\s*$/.test(beforeVector) || // After opening brace or semicolon
                /^\s*[;}]/.test(afterVector) ||  // Before closing brace or semicolon
                /^\s*$/.test(beforeVector) ||    // At the beginning
                /^\s*$/.test(afterVector)        // At the end

            return hasValidStructure
        } catch (error) {
            return false
        }
    }
}

// Ultra-strict CSS injection vectors - designed for ZERO false positives
const __CssInjVectors = [
    // Style tag injections with unique marker
    `<style>body{color:red;haikucsstest123456789}</style>`,
    `<style>.test{background-color:blue;haikucsstest123456789}</style>`,

    // Inline style injections with unique marker
    `" style="color:green;haikucsstest123456789"`,
    `' style='font-size:16px;haikucsstest123456789'`,

    // Style tag breaking with unique marker
    `</style><style>div{margin:10px;haikucsstest123456789}</style>`,
    `</style><style>p{padding:5px;haikucsstest123456789}</style>`,

    // Simple property injections with unique marker
    `<style>h1{border:1px;haikucsstest123456789}</style>`,
    `<style>span{width:100px;haikucsstest123456789}</style>`
]

module.exports = CSSINJ
