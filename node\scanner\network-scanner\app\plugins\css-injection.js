const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require('cheerio')

class CSSINJ extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-css-injection'
    }

    getAttackVectors() {
        return __CssInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'form-encoded-post', 'json-body']
    }

    wantProcessAttackResponse(attack) {
        return attack.pluginName === this.getName()
    }

    isValidCSSSyntax(css) {
        try {
            const cssRules = css.split(';').filter(rule => rule.trim())
            for (const rule of cssRules) {
                if (!rule.includes(':')) return false
                const [property, value] = rule.split(':').map(s => s.trim())
                if (!property || !value) return false
                if (/expression\s*\(|javascript:/i.test(value)) return false
            }
            return true
        } catch {
            return false
        }
    }

    isInCSSContext(html, vector) {
        const lowerHtml = html.toLowerCase()
        const lowerVector = vector.toLowerCase()
        return (
            lowerHtml.includes('<style') && lowerHtml.includes(lowerVector) ||
            lowerHtml.includes('style="') && lowerHtml.includes(lowerVector)
        )
    }

    processAttackResponse(attack) {
        const pluginData = this.getPluginScopedStore(attack)
        if (pluginData.CssInjFound) return

        const contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '').toLowerCase()
        if (!/text\/html|application\/xhtml\+xml/.test(contentType)) return

        const body = _.get(attack, 'result.resp.body', '')
        const vector = attack.vector

        // Only detect if our marker is present
        if (!vector.includes('/*haikutest*/')) return

        let found = false
        try {
            const $ = cheerio.load(body)

            $('style').each((_, el) => {
                const styleText = $(el).html()
                if (styleText && styleText.includes(vector) && this.isValidCSSSyntax(vector)) {
                    found = true
                }
            })

            $('[style]').each((_, el) => {
                const inlineStyle = $(el).attr('style')
                if (inlineStyle && inlineStyle.includes(vector) && this.isValidCSSSyntax(vector)) {
                    found = true
                }
            })
        } catch (e) {
            // fallback to reflection check
            const reflected = body.includes(vector)
            if (reflected && this.isInCSSContext(body, vector) && this.isValidCSSSyntax(vector)) {
                found = true
            }
        }

        if (found) {
            const result = `CSS Injection Detected.\nPayload: ${vector}`
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
            pluginData.CssInjFound = true
        }
    }
}

// ✅ Updated Payloads with precise markers
const __CssInjVectors = [
    `<style>body{color:red;}/*haikutest*/</style>`,
    `" style="background:yellow;/*haikutest*/`,
    `</style><style>h1{color:blue;}/*haikutest*/</style>`,
    `<style>#output{border:2px dashed green;}/*haikutest*/</style>`,
    `" style="font-size:22px;/*haikutest*/`,
    `<style>.box{padding:50px;}/*haikutest*/</style>`
]

module.exports = CSSINJ
