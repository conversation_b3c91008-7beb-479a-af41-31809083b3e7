const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require('cheerio')

class CSSINJ extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-css-injection'
    }

    getAttackVectors() {
        return __CssInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'form-encoded-post', 'json-body']
    }

    wantProcessAttackResponse(attack) {
        return attack.pluginName === this.getName()
    }

    isValidCSSSyntax(css) {
        try {
            const cssRules = css.split(';').filter(rule => rule.trim())
            for (const rule of cssRules) {
                if (!rule.includes(':')) return false
                const [property, value] = rule.split(':').map(s => s.trim())
                if (!property || !value) return false
                if (/expression\s*\(|javascript:/i.test(value)) return false
            }
            return true
        } catch {
            return false
        }
    }

    isInCSSContext(text, vector) {
        const cssContexts = [
            /<style[^>]*>[\s\S]*?<\/style>/i,
            /style\s*=\s*["'][^"']*["']/i
        ]
        return cssContexts.some(context => {
            const match = text.match(context)
            return match && match[0].includes(vector)
        })
    }

    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CssInjFound) return

        const contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '').toLowerCase()
        if (!/text\/html|application\/xhtml\+xml/i.test(contentType)) return

        const responseBody = _.get(attack, 'result.resp.body', '')
        const vector = attack.vector

        let foundInStyleTag = false
        let foundInStyleAttr = false
        let foundByGeneralizedReflection = false

        try {
            const $ = cheerio.load(responseBody)

            $('style').each((_, el) => {
                const styleContent = $(el).html()
                if (styleContent && styleContent.includes('/*haikutest*/') && this.isValidCSSSyntax(styleContent)) {
                    foundInStyleTag = true
                }
            })

            $('[style]').each((_, el) => {
                const styleAttr = $(el).attr('style')
                if (styleAttr && styleAttr.includes('/*haikutest*/') && this.isValidCSSSyntax(styleAttr)) {
                    foundInStyleAttr = true
                }
            })
        } catch (e) {
            // fallback handled below
        }

        // Backup fallback: reflection context detection
        const escapedVector = _.escapeRegExp(vector)
        const urlEncodedVector = encodeURIComponent(vector)
        const isVectorReflected = new RegExp(escapedVector, 'i').test(responseBody) || new RegExp(_.escapeRegExp(urlEncodedVector), 'i').test(responseBody)

        const cssPatternInReflection = /(body|html|div|p|span|a|h[1-6])\s*\{[^}]*\}|color\s*:\s*[^;]+;|background(?:-color)?\s*:\s*[^;]+;|font-size\s*:\s*[^;]+;|margin\s*:\s*[^;]+;|padding\s*:\s*[^;]+;/i

        if (isVectorReflected && this.isInCSSContext(responseBody, vector)) {
            const hasCssPattern = cssPatternInReflection.test(vector)
            if (hasCssPattern && this.isValidCSSSyntax(vector)) {
                foundByGeneralizedReflection = true
            }
        }

        if ((foundInStyleTag || foundInStyleAttr || foundByGeneralizedReflection)) {
            // Optional marker check to ensure it's your payload
            if (!vector.includes('/*haikutest*/')) return

            const method =
                foundInStyleTag ? 'Style Tag' :
                foundInStyleAttr ? 'Style Attribute' :
                'Reflected in CSS Context'

            const result = `Potential CSS Injection Detected.\nPayload: ${vector}\nDetection Method: ${method}`
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
            pluginDataForRequest.CssInjFound = true
        }
    }
}

const __CssInjVectors = [
    `<style>body { color: red; } /*haikutest*/</style>`,
    `"><style>body{color:blue;}/*haikutest*/</style>`,
    `" style="color:red;/*haikutest*/`,
    `</style><style>body{background:blue;}/*haikutest*/</style>`,
    `</title><style>body{color:green;}/*haikutest*/</style>`,
    `<style>#output { border: 3px dashed orange; } /*haikutest*/</style>`,
    `</style><style>p { font-style: italic; } /*haikutest*/</style>`,
    `</style><style>h1{color:blue;}/*haikutest*/</style>`
]

module.exports = CSSINJ
