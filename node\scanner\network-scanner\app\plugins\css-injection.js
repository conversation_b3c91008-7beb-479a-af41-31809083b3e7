const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require('cheerio')

class CSSINJ extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-css-injection'

        // Ultra-strict patterns to avoid any false positives
        this.uniqueMarker = 'haikucsstest123456789'
        this.strictCSSProperties = /^(color|background-color|font-size|margin|padding|border|width|height|display)$/i
        this.dangerousPatterns = /expression\s*\(|javascript:|@import|url\s*\(.*javascript:|behavior\s*:|moz-binding|binding\s*:|eval\s*\(|alert\s*\(/i
    }

    getAttackVectors() {
        return __CssInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'form-encoded-post', 'json-body']
    }

    /**
     * Ultra-strict pre-filtering to eliminate all possible false positives
     */
    wantProcessAttackResponse(attack) {
        if (attack.pluginName !== this.getName()) {
            return false
        }

        // Must be exactly HTTP 200 response
        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 0)
        if (statusCode !== 200) {
            return false
        }

        // Must be HTML content type only
        const contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '').toLowerCase()
        if (!contentType.includes('text/html')) {
            return false
        }

        // Skip if response has any error indicators
        if (_.get(attack, 'result.resp.httpResponse.err')) {
            return false
        }

        // Response must be substantial (not error pages)
        const body = _.get(attack, 'result.resp.body', '')
        if (body.length < 500) {  // Very strict minimum
            return false
        }

        // Must contain our unique marker
        const vector = attack.vector || ''
        if (!vector.includes(this.uniqueMarker)) {
            return false
        }

        // Skip if body contains any error indicators
        const errorIndicators = /error|exception|warning|fail|invalid|denied|forbidden|unauthorized|timeout|unavailable|maintenance|down|offline|404|500|502|503|not found|server error|bad gateway|service unavailable|access denied|permission denied|internal error|database error|connection error|network error|system error|fatal error|critical error|parse error|syntax error|runtime error|compilation error/i
        if (errorIndicators.test(body)) {
            return false
        }

        // Skip CDN and service responses
        const serviceIndicators = /cloudflare|akamai|fastly|amazon|google|microsoft|facebook|twitter|linkedin|github|nginx|apache|iis|tomcat|jetty|websphere|weblogic|jboss|glassfish/i
        if (serviceIndicators.test(body)) {
            return false
        }

        return true
    }

    /**
     * Ultra-strict CSS syntax validation - only allows very specific CSS patterns
     */
    isValidCSSSyntax(css) {
        try {
            // Remove our marker for validation
            const cleanCss = css.replace(new RegExp(this.uniqueMarker, 'g'), '').trim()

            // Must not contain any dangerous patterns
            if (this.dangerousPatterns.test(cleanCss)) {
                return false
            }

            // Must be simple CSS rules only
            const cssRules = cleanCss.split(';').filter(rule => rule.trim())
            if (cssRules.length === 0) return false

            for (const rule of cssRules) {
                const trimmedRule = rule.trim()
                if (!trimmedRule) continue

                // Must contain exactly one colon
                const colonCount = (trimmedRule.match(/:/g) || []).length
                if (colonCount !== 1) return false

                const [property, value] = trimmedRule.split(':').map(s => s.trim())

                // Property and value must exist and be reasonable length
                if (!property || !value || property.length > 50 || value.length > 100) {
                    return false
                }

                // Only allow very specific CSS properties
                if (!this.strictCSSProperties.test(property)) {
                    return false
                }

                // Value must be simple (no functions, no complex expressions)
                if (/\(|\)|url|calc|var|attr|counter|expression/i.test(value)) {
                    return false
                }
            }

            return true
        } catch (error) {
            return false
        }
    }

    /**
     * Ultra-strict context validation - must be in exact CSS context
     */
    isInValidCSSContext(html, vector) {
        try {
            // Must contain our unique marker
            if (!vector.includes(this.uniqueMarker)) {
                return false
            }

            // Check for exact style tag context
            const styleTagRegex = /<style[^>]*>([\s\S]*?)<\/style>/gi
            let match
            while ((match = styleTagRegex.exec(html)) !== null) {
                const styleContent = match[1]
                if (styleContent.includes(vector) && styleContent.includes(this.uniqueMarker)) {
                    return true
                }
            }

            // Check for exact inline style context
            const inlineStyleRegex = /style\s*=\s*["']([^"']*?)["']/gi
            while ((match = inlineStyleRegex.exec(html)) !== null) {
                const styleContent = match[1]
                if (styleContent.includes(vector) && styleContent.includes(this.uniqueMarker)) {
                    return true
                }
            }

            return false
        } catch (error) {
            return false
        }
    }

    /**
     * Ultra-strict processing with multiple validation layers - ZERO false positives
     */
    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CssInjFound) return

        const responseBody = _.get(attack, 'result.resp.body', '')
        const vector = attack.vector
        const originalBody = _.get(attack, 'originalRequest.result.resp.body', '')

        // STRICT REQUIREMENT: Must contain our unique marker
        if (!vector.includes(this.uniqueMarker)) {
            return
        }

        // STRICT REQUIREMENT: Vector must not exist in original response
        if (originalBody && originalBody.includes(vector)) {
            return
        }

        // STRICT REQUIREMENT: Must be valid CSS syntax
        if (!this.isValidCSSSyntax(vector)) {
            return
        }

        // STRICT REQUIREMENT: Must be in valid CSS context
        if (!this.isInValidCSSContext(responseBody, vector)) {
            return
        }

        // STRICT REQUIREMENT: Response must contain exact vector match
        if (!responseBody.includes(vector)) {
            return
        }

        // STRICT REQUIREMENT: Vector must appear in proper CSS structure
        let validCSSInjection = false
        let detectionMethod = ''

        try {
            const $ = cheerio.load(responseBody)

            // Check style tags with ultra-strict validation
            $('style').each((_, el) => {
                const styleContent = $(el).html() || ''
                if (styleContent.includes(vector) &&
                    styleContent.includes(this.uniqueMarker) &&
                    this.isValidCSSStructure(styleContent, vector)) {
                    validCSSInjection = true
                    detectionMethod = 'Style Tag Injection'
                    return false // break
                }
            })

            // Check inline style attributes with ultra-strict validation
            if (!validCSSInjection) {
                $('[style]').each((_, el) => {
                    const styleAttr = $(el).attr('style') || ''
                    if (styleAttr.includes(vector) &&
                        styleAttr.includes(this.uniqueMarker) &&
                        this.isValidCSSStructure(styleAttr, vector)) {
                        validCSSInjection = true
                        detectionMethod = 'Inline Style Injection'
                        return false // break
                    }
                })
            }

        } catch (error) {
            // No fallback - if cheerio fails, we don't report
            return
        }

        // FINAL VALIDATION: Only report if ALL conditions are met
        if (validCSSInjection && detectionMethod) {
            // Additional verification: check response size difference
            const sizeDifference = Math.abs(responseBody.length - (originalBody?.length || 0))
            if (sizeDifference < vector.length) {
                return // Response didn't grow as expected
            }

            const result = {
                message: `CSS Injection Confirmed`,
                payload: vector,
                method: detectionMethod,
                confidence: 'VERIFIED',
                marker: this.uniqueMarker
            }

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
            pluginDataForRequest.CssInjFound = true
        }
    }

    /**
     * Validate that CSS is properly structured within the context
     */
    isValidCSSStructure(cssContent, vector) {
        try {
            // Must contain our marker
            if (!cssContent.includes(this.uniqueMarker)) {
                return false
            }

            // Must contain the complete vector
            if (!cssContent.includes(vector)) {
                return false
            }

            // Check for proper CSS rule structure around our vector
            const vectorIndex = cssContent.indexOf(vector)
            const beforeVector = cssContent.substring(0, vectorIndex)
            const afterVector = cssContent.substring(vectorIndex + vector.length)

            // Must be part of a valid CSS rule or declaration
            const hasValidStructure =
                /[{;]\s*$/.test(beforeVector) || // After opening brace or semicolon
                /^\s*[;}]/.test(afterVector) ||  // Before closing brace or semicolon
                /^\s*$/.test(beforeVector) ||    // At the beginning
                /^\s*$/.test(afterVector)        // At the end

            return hasValidStructure
        } catch (error) {
            return false
        }
    }
}

// Ultra-strict CSS injection vectors - designed for ZERO false positives
const __CssInjVectors = [
    // Style tag injections with unique marker
    `<style>body{color:red;haikucsstest123456789}</style>`,
    `<style>.test{background-color:blue;haikucsstest123456789}</style>`,

    // Inline style injections with unique marker
    `" style="color:green;haikucsstest123456789"`,
    `' style='font-size:16px;haikucsstest123456789'`,

    // Style tag breaking with unique marker
    `</style><style>div{margin:10px;haikucsstest123456789}</style>`,
    `</style><style>p{padding:5px;haikucsstest123456789}</style>`,

    // Simple property injections with unique marker
    `<style>h1{border:1px;haikucsstest123456789}</style>`,
    `<style>span{width:100px;haikucsstest123456789}</style>`
]

module.exports = CSSINJ
