const NetworkAttack = require('./network-attack');
const _ = require('lodash');

// RFC 3986-compliant WebSocket URL validation
const validWebSocketPattern = /^ws:\/\/(?!localhost|127\.0\.0\.1)([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(?::\d+)?(?:\/[a-zA-Z0-9\-._~:/?#\[\]@!$&'()*+,;=%]*)?$/;

class InsecureWebSocketDetector extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        this.vulnerabilityID = 'ID-insecure-websocket';
    }

    processAttackResponse(attack) {
        // Skip if HTTP response has errors
        if (_.get(attack, 'result.resp.httpResponse.err')) return;

        // Skip known non-sensitive or irrelevant
        const href = _.get(attack, 'href', '');
        if (/\/blog\/|\/docs\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i.test(href)) return;

        const responseBody = _.get(attack, 'result.resp.body', '') || '';

        // Match potential insecure WebSocket URLs
        const matches = responseBody.match(/\bws:\/\/[a-zA-Z0-9\-._~:/?#\[\]@!$&'()*+,;=%]+/gi) || [];

        if (matches.length === 0) return;

        // Filter valid and relevant URLs
        const validMatches = matches.filter(url => {
            try {
                return (
                    !url.includes('.haikuscan.indusfacefinder.in') &&
                    url.length >= 10 &&
                    validWebSocketPattern.test(url)
                );
            } catch {
                return false;
            }
        });

        if (validMatches.length > 0) {
            const messages = validMatches.map(url => `Insecure WebSocket URL detected: ${url}`);
            const result = messages.join('\n');

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result);
        }
    }
}

module.exports = InsecureWebSocketDetector;
