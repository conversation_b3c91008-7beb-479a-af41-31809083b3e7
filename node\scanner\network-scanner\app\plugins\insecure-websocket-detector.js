const NetworkAttack = require('./network-attack');
const _ = require('lodash');

// Regex to detect insecure WebSocket URLs (ws://)
const insecureWebSocketPattern = /\bws:\/\/[^\s"']+/gi;

class InsecureWebSocketDetector extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner);
        this.vulnerabilityID = 'ID-insecure-websocket';
    }

    processAttackResponse(attack) {
        // Skip processing on HTTP errors
        if (_.get(attack, 'result.resp.httpResponse.err')) return;

        // Avoid FPs in common non-sensitive paths
        const href = _.get(attack, 'href', '');
        if (/\/blog\/|\/docs\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i.test(href)) return;

        const responseBody = _.get(attack, 'result.resp.body', '') || '';
        const matches = responseBody.match(insecureWebSocketPattern) || [];

        if (matches.length > 0) {
            // Filter out internal URLs
            const filteredMatches = matches.filter(url => !url.includes('.haikuscan.indusfacefinder.in'));
            
            if (filteredMatches.length > 0) {
                let result = '';
                const thisVulnDetails = filteredMatches
                    .map(url => `Insecure WebSocket URL detected: ${url}\nUse wss:// for secure communication.`)
                    .join('\n\n');
                
                result += thisVulnDetails;

                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result);
            }
        }
    }
}

module.exports = InsecureWebSocketDetector;
