/**
 * RE2Replacement.js
 * 
 * A drop-in replacement for the RE2 module that uses native RegExp
 * Created to avoid memory issues caused by the RE2 native module
 */

class RE2Replacement {
  constructor(pattern, flags) {
    // Convert flags object to string if needed
    let flagsStr = '';
    if (typeof flags === 'object') {
      if (flags.ignoreCase) flagsStr += 'i';
      if (flags.multiline) flagsStr += 'm';
      if (flags.global) flagsStr += 'g';
      if (flags.sticky) flagsStr += 'y';
      if (flags.unicode) flagsStr += 'u';
    } else if (typeof flags === 'string') {
      flagsStr = flags;
    }
    
    // Create the native RegExp object
    this.regexp = new RegExp(pattern, flagsStr);
    this.source = this.regexp.source;
    this.flags = this.regexp.flags;
    this.global = this.regexp.global;
    this.ignoreCase = this.regexp.ignoreCase;
    this.multiline = this.regexp.multiline;
    this.unicode = this.regexp.unicode;
    this.sticky = this.regexp.sticky;
  }

  // Main matching methods
  exec(str) {
    return this.regexp.exec(str);
  }

  test(str) {
    return this.regexp.test(str);
  }

  match(str) {
    return str.match(this.regexp);
  }

  replace(str, newSubStr) {
    return str.replace(this.regexp, newSubStr);
  }

  search(str) {
    return str.search(this.regexp);
  }

  split(str, limit) {
    return str.split(this.regexp, limit);
  }
  
  // Additional methods that might be used
  toString() {
    return this.regexp.toString();
  }
}

module.exports = RE2Replacement; 