import sys
import socket
import ssl
import pprint
import json
import random
import time

global detectionTimeout

startTime = 0
vulns_output = {}

# Read input JSON from haiku plugin
def read_json_stdin():
    infile = sys.stdin
    jsonstr = json.load(infile)

    if jsonstr != None:
        infile.close()
        return jsonstr
    else:
        return None

# Write the vulnerability output to JSON structure
def write_json_std_out(outdata):
    json.dump(outdata, sys.stdout, sort_keys=True, indent=4)
    print("\n")
    sys.stdout.flush()

# Creating socket connection 
def socketConnection(hostName, port):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    
    # checking for ssl socket connection
    if port == 443:
        context = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)
        context.verify_mode = ssl.CERT_NONE  # Disable certificate verification
        sock_connection = context.wrap_socket(s)
    else:
        sock_connection = s

    try:
        sock_connection.connect((hostName, port)) # socket connection with the target webserver
        sock_connection.send("GET / HTTP/1.1\n".encode())
    except socket.error:
        print('Unable to connect to the target webserver : {}'.format(socket.error))
        exit()
    return sock_connection

# it sends a new header every 5 seconds line by line to keep the connection alive.
def sendPackets(sock_connection, startTime, jsonInputData):
    try:
        while True:
            # sending random user-agent to keep the connection alive
            randomUserAgent = "User-Agent: Mozilla/5.0 (Windows NT 10; rv:36.0) Gecko/20100101 Firefox/{}\n".format(random.randint(1,5000)).encode("utf-8")
            sock_connection.send(randomUserAgent)
            
            # If the elapsed time is greater than detection time then it checks for vulnerablity
            elapsedTime = getElapsedTime(startTime)
            if elapsedTime > detectionTimeout:
                exportVulnsResult(True, sock_connection) # exporting the vulns result to haiku
                break
            
            # Cheking if sleepTime obj is present or not else default value=5
            if "sleepTime" in jsonInputData:
                sleepTime = jsonInputData["sleepTime"]
            else:
                sleepTime = 5
            time.sleep(sleepTime) # 5 seconds timer interval
    except socket.error:
        # If target webserver disconnects the socket then it checks for vulnerablity 
        detectSlowlorisVulns(startTime, sock_connection)

# Calculating elapsed time with the startTime
def getElapsedTime(startTime):
    currentTime = int(time.time())
    timePassed = round(currentTime - startTime)
    elapsedTime = timePassed % (detectionTimeout * 2)
    return elapsedTime

# function to detect slowloris vulns based on the elapsed timeout
def detectSlowlorisVulns(startTime, sock_connection):
    elapsedTime = getElapsedTime(startTime)

    # If the elapsed time within the detection timeout range then it marked as vulns found
    if elapsedTime > detectionTimeout:
        isVulnsFound = True
    else:
        isVulnsFound = False
    exportVulnsResult(isVulnsFound, sock_connection)

# exporting the vulns output to the haiku plugin
def exportVulnsResult(isVulnsFound, sock_connection):
    time.sleep(0)
    sock_connection.close() # closing socket once detection has completed
    vulns_output['isSlowlorisVulsFound'] = isVulnsFound
    write_json_std_out(vulns_output)

if __name__ == "__main__":
    jsonInputData = read_json_stdin()

    #detectionTimeout = jsonInputData["detectionTimeout"]
    #print("Detection Time:"+str(detectionTimeout))

    # if both host and port json keys presents then it will proceed further
    if("hostName" in jsonInputData and "port" in jsonInputData):
        hostName = jsonInputData["hostName"]
        port = jsonInputData["port"]
        socket_connection = socketConnection(hostName, port)
        
        # If the plugin sent the detection timeout value then it will be used as detection OR the default value=120
        if "detectionTimeout" in jsonInputData:
            detectionTimeout = jsonInputData["detectionTimeout"]

        # It initializes the start time and starts sending packets to the target webserver
        startTime = int(time.time())
        sendPackets(socket_connection, startTime, jsonInputData)