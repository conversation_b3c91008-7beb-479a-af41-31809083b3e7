const BasePlugin = require('../base-plugin')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const JSONBodyDelegate = require('../../../lib/json-body-iterator')

class JSONBody extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }

    /**
     * New request received from crawler - Kick off paramter iteration if request is viable
     * @param {request} originalRequest untampered request
     * @override
     */
    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http headers
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area')
            if (originalRequest.revalidationInfo && JSONBodyDelegate.ParameterType != attackArea) {
                logger.log('info', `json post - not an interesting request since request being revalidated is attacking '${attackArea}'`, HaikuUtils.getMetadataForLog(originalRequest))
                return
            }
        }

        // only process successful requests
        let httpRequest = originalRequest.httpRequest
        if (originalRequest.httpResponse.err || !JSONBodyDelegate.isJSONPostRequest(httpRequest)) {
            logger.log('info', `json encoded post - not an interesting request ${originalRequest.httpRequest.method} ${originalRequest.httpRequest.uri}  err=${originalRequest.httpResponse.err}`, HaikuUtils.getMetadataForLog(originalRequest))
            return
        }

        // Create object that can iterate and manipulate params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan')

        /** >>>>>  
            @TODO need to get options from annotation 
            <<<<< */
        let options = _.clone(this.getMetadata(originalRequest).options)
        options.propsToIterate = _.get(originalRequest, 'httpRequest.annotations.propsToIterate')
        let createJSONBodyDelegate = function () {
            return new JSONBodyDelegate(originalRequest, scanStore, options)
        }
        this.networkScanner.emit('json-body', createJSONBodyDelegate)
    }

}

module.exports = JSONBody