// https://github.com/creationix/http-parser-js
// we do this to avid http header errors when servers return invalid headers
// ... addresses issues such as corrupt HTTP headers, which would otherwise cause <PERSON><PERSON>'s parser 
//      to throw a fatal error (HPE_INVALID_HEADER_TOKEN)
// Monkey patch before you require http for the first time.
// process.binding('http_parser').HTTPParser = require('http-parser-js').HTTPParser;

const fs = require('fs')
const Request = require('request')
const debug = require('debug')('HTTPRequestMonster')
const URL = require('url').URL
const _ = require('lodash')
const osUtils = require('os-utils')
const DeferredPromise = require('../../common/lib/deferred-promise')
const logger = require('../../common/lib/haiku-logger')
const HaikuUtils = require('../../common/lib/haiku-utils')
const { AUTH_TYPES } = require('../../common/config/app-constants')

// key name to keep track of global requests    
const globalRequests = 'global requests'

class HTTPRequestMonster {
    constructor(config) {
        // set up global limits here. Per domain limits will have to be set dynamically
        this.config = config.HTTPRequestMonster

        // set up the request Queue 
        // request Queue is hashtable/dictionary of host and elements are objects containing metadata & array of requests
        this.requestQ = {}
        this.nextScanToProcess = 0
        this.savedNextScanid = -1
        this.inFlightRequests = {}
        this.inFlightRequests[globalRequests] = 0
        this.maxInFlightRequests = 0
        this.requestSent = {}
        this.requestCompleted = {}

        // set up timer 
        this.intvlId = setInterval(this.maintenanceProc.bind(this), this.config.maintenanceIntervalSecs * 1000)

        // ** keeping commented out code as its useful while debugging
        //this.requestsBeingProcessed = []

        // the default request oject
        this.baseRequest = Request.defaults({
            gzip: true, // always support gzip/deflate
            strictSSL: false, // Don't bother with SSL validation
            rejectUnauthorized: false, // -"- really mean it - dont validate!
            maxRedirects: 5, // default is 10 and ths causes issue since lib adds pipe event listeners in each rdirect causing issues
            removeRefererHeader: true, // if true, referer header set in the initial request is preserved during redirect chain

            // don't use DH since for some sites (at least one=https://pshrhelpdesk.corp.mphasis.com/), the 
            // DH key size they are using is too less which causes the OpenSSL lib that node is using 
            // to throw an error: 
            //      SSL routines:tls_process_ske_dhe:dh key too small:../deps/openssl/openssl/ssl/statem/statem_clnt.c:1457
            // curl also gives same issue and openssl gives warning but then connects.
            // Setting minDHSize does not work as it can only be used to raise the minimum not to reduce it.
            // This could be made into either
            //      global setting 
            //      site specific config OR
            //      automatically check for SSL error and fallback to less ecure Agent (more complicated)
            // As a a scanner, turning off PFC using EDH is not a big deal so going with the 'global' option. 
            ciphers: 'DEFAULT:!DH',

            //proxy: 'http://127.0.0.1:8080', // ENABLE THIS IF YOU WANT TO RUN via Burp.
            time: true, // get timings for each request
            //timeout: 90000,
            jar: true   // Persist cookie
        })

    }

    // allow redirect only to allowed domains
    /**
     * checks if the redirect is allowed.
     * @param {Object} res Response object
     * @param {*} options options object with reference to the request
     * @returns {boolean} true if redirect is allowed, false otherwise
     */
    allowRedirect(res, options) {
        try {
            let redirUrl = new URL(res.headers.location, res.request.parsedURL)
            let allowedDomain = res.request.allowedDomain
            let canonacalizedDomain = HaikuUtils.canonacalizeHost(redirUrl)
            let okToRedirect = ((allowedDomain == canonacalizedDomain) || /indusface\.com|indusguard\.com/.test(canonacalizedDomain))

            try {
                // Update the redirect chain if the redirect is allowed and the status code is in the range of 300-399
                if(res && res.statusCode && res.statusCode >= 300 && res.statusCode < 400) {
                    options.redirects = options.redirects || [];
                    options.redirects.push({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        redirectedUri: redirUrl.href
                    });
                }
            } catch (error) {
                console.error('Error in redirecting', HaikuUtils.getMetadataForLog(res.request))
            }
            
            //debug( `${allowedDomain} == ${redirUrl.host} : ${okToRedirect}`)
            return okToRedirect
        } catch (err) {
            logger.log('error', `Blocking redirect: could not resolve URL ${res.headers.location} -> base = ${res.request.parsedURL.toString()}`, HaikuUtils.getMetadataForLog(_.get(res, 'request')))
            return false
        }
    }

    /**
     * checks is the content type from response matches the pattern provided
     * @param {response} res HTTP response
     * @param {RegExp} allowedContentTypes regexp of allowed content types like /texti/(html|xml)/i
     */
    static contentTypeMatchesPattern(res, allowedContentTypes) {
        // assume that missing content-type is text/html. We can add autodetection later
        let contentType = _.get(res, 'headers[content-type]', 'text/html')
        if (allowedContentTypes.test(contentType)) {
            return true
        }

        // dont allow this request
        logger.log('info', `Blocking ${res.request.uri} since content-type (=${contentType}) is not text/html`, HaikuUtils.getMetadataForLog(_.get(res, 'request')))
        return false
    }

    /**
     * default allow content type handler - allows content types matching pattern in config.httpRequestMonster.allowedContentTypes
     * @param {response} res HTTP response
     * @param {RegExp} allowedContentTypes regexp of allowed content types like /text/(html|xml)/i
     */
    _allowContentType(res) {
        return HTTPRequestMonster.contentTypeMatchesPattern(res, this.config.allowedContentTypes)
    }

    /**
     * queue a request for processing. If scan is done, refuse to accept new requests
     * @param {Object} request the request to queue
     * @returns {Promise} a promise that will be resolved when the request is complete
     */
    queueRequest(request) {
        // if scan is done, do not accept new requests
        if (!this.requestQ[request.scanId] || this.requestQ[request.scanId].scanDone) {
            logger.log('info', `scan is done, refusing to accept request ${request.uri}`, HaikuUtils.getMetadataForLog(request))
            return false
        }

        let requestClone = _.cloneDeep(request)
        requestClone.parsedURL = requestClone.rawURl || new URL(requestClone.uri)
        requestClone.uri = requestClone.rawURl || requestClone.uri

        // set up the callback to restrict redirects to allowed domains
        let options = requestClone

        // set up defaults for missing params
        // options.defaultMethods = {
        //     allowContentType : this._allowContentType.bind(this),
        //     allowRedirect : this.allowRedirect.bind(this)
        // }

        options.followAllRedirects = !(options.followAllRedirects === false)
        options.followRedirect = options.followRedirect || ((res) => this.allowRedirect(res, options))
        options.allowContentType = options.allowContentType || this._allowContentType.bind(this)
        options.allowedDomain = requestClone.parsedURL.host.replace(/^www\./, '')
        options.maxResponseSize = options.maxResponseSize || this.requestQ[request.scanId].options.defaultMaxResponseSize

        let theRequest = {}
        theRequest.options = options
        theRequest.parsedURL = requestClone.parsedURL
        theRequest.scanId = requestClone.scanId
        theRequest.attackRequestId = requestClone.attackRequestId;

        // enqueue the request
        theRequest.defPromise = new DeferredPromise()
        this.requestQ[theRequest.scanId].requests.push(theRequest)

        // process queue
        this.processScanId(theRequest.scanId)

        return theRequest.defPromise.p
    }

    /**
     * Apply authentication for request. i.e. Basic, Bearer, OAuth1.0, OAuth2.0
     * @param {Object} request object for which authetication need need to be apply  
     */
    applyAuthentication(request)  {
        let authInfo = _.get(request, 'authInfo');
        
        if(!authInfo) { 
            return;
        }

        switch (authInfo.type) {
            case AUTH_TYPES.BASIC:
                {
                    request.auth = {
                        user: authInfo.details.username,
                        pass: authInfo.details.password
                    };
                }
                break;
        
            default:
                break;
        }
    }

    updateNetworkStats(request) {
        try {
            if(!isNaN(request.startTime)) {
                request.roundTripTimeSec = (new Date().getTime() - request.startTime) / 1000;
            }
        } catch (error) {
            
        }
    }

    getExtraLogs(deferredRequest) {
        let extraLogs = "";

        try {
            let scanId = deferredRequest.scanId;
            extraLogs = ` | Id: ${deferredRequest.scanId}_${deferredRequest.attackRequestId}`;

            if (this.requestQ[scanId].replayScanInfo.isReplayScan) {
                let alertMd5 = _.get(deferredRequest, 'options.headers.X-IFC-REPLAY', null);

                if (alertMd5) {
                    extraLogs = extraLogs + ' | X-IFC-REPLAY: ' + alertMd5;
                }
            }
        }
        catch (error) {
            logger.log('error', `Unable to generate extra logs, error ${error.toString()}`, HaikuUtils.getMetadataForLog(deferredRequest))
        }

        return extraLogs;
    }

    // perform the request
    async sendRequest(deferredRequest) {
        let errorInSending = null;
        let extraLogs = "";
        try {
            deferredRequest.startTime = new Date().getTime();
            extraLogs = this.getExtraLogs(deferredRequest);
            logger.log('info', `sending request: - ${deferredRequest.parsedURL.href}${extraLogs}`, HaikuUtils.getMetadataForLog(deferredRequest))

            // sanitize (delete/modify/add) headers eg. delete any we don't need and could cause us problems. 
            // The library will set them correctly if needed.
            this.sanitizeRequestHeaders(deferredRequest);
            let ContentLengthExceedsMaxRangeError = class extends RangeError {}
            let NotAllowedContentTypeError = class extends TypeError {}
            let callback = function (err, response, body) {
                let responseCode = 0;

                if(response && response.statusCode) {
                    responseCode = response.statusCode;
                }
                
                if(response && response.timings && response.timings.response) {
                    // convert to seconds & round off to 3 decimal places
                    deferredRequest.customerRoundTripTimeSec = !_.isNaN(response.timings.response) ? parseFloat((response.timings.response / 1000).toFixed(3)) : 0;
                }

                logger.log('info', `Request finished: ${deferredRequest.parsedURL.href}${responseCode ? ' | statusCode:' + responseCode : ''}${deferredRequest.customerRoundTripTimeSec ? ' TotalTime: ' + deferredRequest.customerRoundTripTimeSec: ''}${extraLogs}`, HaikuUtils.getMetadataForLog(deferredRequest))
                if (deferredRequest.ALREADYPROCESSED) {
                    logger.log('info', `Already processed... ${deferredRequest.parsedURL.href}${extraLogs}`, HaikuUtils.getMetadataForLog(deferredRequest))
                    return
                }
                deferredRequest.ALREADYPROCESSED = true

                // If body is not a string, convert to string (since for now we only handle text/html)
                if (body && typeof body != 'string') {
                    body = body.toString()
                }

                let resp = {
                    err: err,
                    fullResponse: response,
                    body: body
                }

                // Handle request exceeds max allowed size error.
                if (err) {
                    if (err instanceof ContentLengthExceedsMaxRangeError) {
                        resp.err = null
                        resp.isPartialResponse = true
                    } else if (err instanceof NotAllowedContentTypeError) {
                        resp.err = null
                        resp.isDisallowedContentType = true
                    }
                }

                this.updateNetworkStats(deferredRequest);
                // process request complete.
                this.requestComplete(deferredRequest, resp)
            }.bind(this)

            // response handling - stop at not allowed content-type & whne request exceeds max size.
            let partialBody = ''
            let bytesReadSoFar = 0
            let aborted = false
            this.applyAuthentication(deferredRequest.options);
            let req = this.baseRequest(deferredRequest.options, callback)
            req.on('response', (response) => {
                if (!req.allowContentType(response)) {
                    req.abort()
                    aborted = true
                    logger.log('info', `Request ${req.uri.href} : not allowed content-type ${response.headers ? response.headers['content-type'] :'unknown'}${extraLogs}`, HaikuUtils.getMetadataForLog(deferredRequest))
                    callback(new NotAllowedContentTypeError(`not allowed content-type: ${_.get(response, 'headers[content-type]')}`), response, partialBody)
                }
            })
            req.on('data', (chunk) => {
                // keep track of aborted ourselves. after req.abort(), the stream will keep discarding
                // for the response but we would keep collecting the data anyway.
                if (aborted) {
                    return
                }

                // uncompressed data
                if (bytesReadSoFar == 0) {
                    partialBody = chunk
                } else {
                    partialBody += chunk
                }

                bytesReadSoFar += chunk.length;
                if (bytesReadSoFar >= req.maxResponseSize) {
                    req.abort()
                    aborted = true
                    logger.log('info', `Request ${req.uri.href} exceeds max size: ${req.maxResponseSize}${extraLogs}`, HaikuUtils.getMetadataForLog(deferredRequest))
                    callback(new ContentLengthExceedsMaxRangeError('Request exceeds max size'), req.response, partialBody)
                }
            })
        } catch (err) {
            errorInSending = err
        }

        // If we could not even send the request due to an error, complete it with error right away
        if (errorInSending) {
            logger.log('error', `sending request failed, error ${errorInSending.toString()}${extraLogs}`, HaikuUtils.getMetadataForLog(deferredRequest))
            deferredRequest.ALREADYPROCESSED = true // force this in case error was after request sent so callback gets called as well.

            // do response processing with error
            let resp = {
                err: errorInSending,
            }

            this.updateNetworkStats(deferredRequest);
            this.requestComplete(deferredRequest, resp)
        }
    }

    /**
     * delete headers that we don't need and could cause us problems. The library will set them correctly
     * if they are not present
     * @param {object} deferredRequest - the request to sanitize headers for
     */
    sanitizeRequestHeaders(deferredRequest) {
        // accept-encoding - eg. until library supports, we cant handle 'bz' encoding.        
        delete deferredRequest.options.headers['Accept-Encoding'];
        delete deferredRequest.options.headers['accept-encoding'];

        // Attack may have changed content length, let framework recalculate correct length
        delete deferredRequest.options.headers['Content-Length'];
        delete deferredRequest.options.headers['content-length'];
    }

    requestComplete(deferredRequest, resp) {
        // complete this request
        // TODO: special csse for connection timeout, try again after a bit.
        // if (connection timed out && retry count < 3) { retry count ++; enqueue again}

        // see if we got a 429 - Too Many Requests status code
        let responseCode = _.get(resp, 'fullResponse.statusCode', -1)
        if (responseCode == 429) {
            // We could parse the 'Retry-After' header and pause for that much time.
            // However, since it's difficult to track to keep track of which particular APIs
            // or endpoints have different restrictions, just do a blanket pause scan for a minute.
            this.pauseScan(deferredRequest.scanId, 60)      // pause scan for a minute

            // @todo: We should retry this request after a minute i.e. add this request back  
            // to the head of the queue. 
        }

        // resolve the request
        this.resolveRequest(deferredRequest, resp)

        // adjust counts
        let scanId = deferredRequest.scanId
        this.inFlightRequests[globalRequests]--;
        this.inFlightRequests[scanId]--;
        // ** keeping commented out code as its useful while debugging
        //this.requestsBeingProcessed = this.requestsBeingProcessed.filter(e => e != deferredRequest)

        // increment request completed counter
        this.requestCompleted[scanId]++;

        // process next item in the queue
        this.processScanId(scanId)
    }

    /**
     * Resolve the request. This is called when the request is complete and we need to resolve the promise
     * @param {Object} deferredRequest  the request to resolve
     * @param {Object} resp the response object
     */
    resolveRequest(deferredRequest, resp) {
        // commonFields
        resp.httpResponse = {
            err: resp.err,
            body: resp.body,
            headers: resp.fullResponse ? resp.fullResponse.headers : undefined,
            statusCode: resp.fullResponse ? resp.fullResponse.statusCode : undefined,
            statusMessage: resp.fullResponse ? resp.fullResponse.statusMessage : undefined,
            isPartialResponse: resp.isPartialResponse,
            // redirects followed if any. This is taken from the internal representation
            // of the Request library and so may be fragile
            // @todo technical debt - keep track of this ourselves.
            redirectsFollowed: _.get(resp, 'fullResponse.request._redirect.redirectsFollowed', 0),
            // Redirect chain happened during the request, with each headers, status code respect to each redirectUri
            redirects: _.get(deferredRequest, 'options.redirects', [])
            /*
            other potentially useful bits from response
             timings, timingPhases, httpVersion, trailers, rawHeaders, rawTrailers
            */
        };
        deferredRequest.defPromise.__resolve({
            req: deferredRequest,
            resp: resp
        });
    }


    /**
     * Perform periodic tasks most importantly adding tokens to scan buckets & handling paused scans. 
     * Delibrately kept simple. Don't keep track of interval accuracy eg.
     */
    maintenanceProc() {
        let scanIds = Object.keys(this.requestQ)
        for (let scanId of scanIds) {
            // see if scan is paused
            if (this.isPaused(scanId)) {
                this.requestQ[scanId].pauseForIntervals--
                continue
            }

            // add scan token to bucket
            this.requestQ[scanId].scanTokens += this.requestQ[scanId].tokenRefillPerInterval
            if (this.requestQ[scanId].scanTokens > this.requestQ[scanId].maxTokens) {
                this.requestQ[scanId].scanTokens = this.requestQ[scanId].maxTokens
            }
        }

        // process queue for all scans
        this.processQueue()
    }

    // process queue - triggered when a request is queued and when a request completes
    processQueue() {
        // the strategy is to go across scans i.e. if we have 3 scans with 2 pending requests each
        // we will process as S1-r1, S2-r1, S3-r1, S1-r2, S2-r2, S3-r2
        let requestsSent = 0
        let numScans = Object.keys(this.requestQ).length
        do {
            requestsSent = 0
            for (let i = 0; i < numScans; i++) {
                if (this.inFlightRequests[globalRequests] >= this.config.maxParallelRequests) {
                    logger.log('info', `maxParallelRequests breached : current inFlightRequests- = ${this.inFlightRequests[globalRequests]}, maxParallelRequests = ${this.config.maxParallelRequests}, scanCount: ${numScans}`);
                    break
                }

                // process this scan
                let scanId = this.getScanToProcess()
                this.gotoNextScan()

                // get next item to process from queue
                if (this.processScanId(scanId)) {
                    requestsSent++
                }
            }
        } while (requestsSent > 0)
    }

    processScanId(scanId) {
        if (!this.canProcessThisScanQ(scanId)) {
            return false
        }

        // found one we can use - remove from queue
        let request = this.requestQ[scanId].requests.shift();
        // ** keeping commented out code as its useful while debugging
        //this.requestsBeingProcessed.push(request)
        // send the request
        this.requestQ[scanId].scanTokens--; // used up one scan token
        this.sendRequest(request);

        // adjust counts
        this.inFlightRequests[scanId]++;
        this.inFlightRequests[globalRequests]++;
        if (this.inFlightRequests[globalRequests] > this.maxInFlightRequests) {
            this.maxInFlightRequests = this.inFlightRequests[globalRequests];
        }

        // increment request sent counter
        this.requestSent[scanId]++;

        return true;
    }

    /**
     * Check if requests can be sent on for particular scan.
     * check if we have requests, not exceeded max concurrent requests, queue is not paused,
     * we have enough tokens to send request (are within requests per minute quota)
     * @param {Number} scanId the scanid to check 
     */
    canProcessThisScanQ(scanId) {
        return this.requestQ[scanId] && // scanId exists
            this.requestQ[scanId].requests.length > 0 && // have requests to send
            this.requestQ[scanId].scanTokens >= 1 && // have enough tokens to send request
            !this.isPaused(scanId) && // not paused
            this.inFlightRequests[scanId] < this.getMaxParallelRequests(scanId) // below max parallel requests count
    }

    /**
     * pause scan fro specified number of seconds. In this time, no requests will be sent for
     * the scan and tokens will not accumulate 
     * @param {Number} scanId scan ID to pause
     * @param {Number} pauseForSecs Seconds to pause scan
     */
    pauseScan(scanId, pauseForSecs = 61 /* default to just over a minute */) {
        if (!this.requestQ[scanId]) {
            return false
        }

        // Convert pause seconds to 'intervals to pause for'
        this.requestQ[scanId]
        this.requestQ[scanId].pauseForIntervals = Math.ceil(pauseForSecs / this.config.maintenanceIntervalSecs)
        return true
    }

    /**
     * checks if this scan is paused.
     * @param {Number} scanId scanId to check
     */
    isPaused(scanId) {
        return this.requestQ[scanId].pauseForIntervals > 0
    }

    /**
     * Returns the (site specific) max parallel requests allowed
     * @param {Number} scanId the scan for which info is requested
     */
    getMaxParallelRequests(scanId) {
        return this.requestQ[scanId].options.perDomainMaxParallelRequests;
    }

    /**
     * Return total number of scans
     */
    getNumScan() {
        return Object.values(this.requestQ).length;
    }

    /**
     * Print stats on the current Queues etc. 
     * @todo should make this a getstats and add more per site + global stats
     */
    printStats() {
        let runningScans = this.getNumScan();
        let timeStamp = new Date().toISOString();
        let totalRequestsSent = 0;
        let totalRequestsCompleted = 0;
        let totalAvgRequestsSent = 0;
        let totalAvgRequestsCompleted = 0;

        for (let scanId in this.requestQ) {
            // generates requests tracker data in every maintenance proc interval
            let requestsSent = this.requestSent[scanId];
            let requestsCompleted = this.requestCompleted[scanId];

            let requestTrackerObj = {
                scanId,
                timeStamp,
                requestsSent,
                requestsCompleted,
                runningScans
            }
            totalRequestsSent = totalRequestsSent + this.requestSent[scanId];
            totalRequestsCompleted = totalRequestsCompleted + this.requestCompleted[scanId];

            logger.log('info', `requests tracker : ${JSON.stringify(requestTrackerObj)}`)

            // reseting the requests sent/completed counters
            this.requestSent[scanId] = 0;
            this.requestCompleted[scanId] = 0;

            logger.log('info', `requests queued = ${this.requestQ[scanId].requests.length}, inflight: ${this.inFlightRequests[scanId]}`, {
                scanId
            })
        }

        let scanId = -1;
        totalAvgRequestsSent = totalRequestsSent / runningScans;
        totalAvgRequestsCompleted = totalRequestsCompleted / runningScans;

        if (runningScans > 0) {
            let totalScansObj = {
                scanId,
                timeStamp,
                totalRequestsSent,
                totalRequestsCompleted,
                totalAvgRequestsSent,
                totalAvgRequestsCompleted
            }
            logger.log('info', `requests tracker : ${JSON.stringify(totalScansObj)}`)
        }
        logger.log('info', `Concurrent inflight requests: cur = ${this.inFlightRequests[globalRequests]}, max = ${this.maxInFlightRequests}`)
    }

    /** 
     * set up metadata since we are going to start scan
     * @param {Number} scanId the scan that is being started
     */
    scanStarted(request, siteOptions) {
        let scanId = request.scanId;

        // sanity check
        if (this.inFlightRequests[scanId] || this.requestQ[scanId]) {
            logger.log('error', `asked to start a scan but scan is already running. Ignoring request.`, {
                scanId
            })
            return
        }

        // set up metadata
        let options = _.clone(this.config)
        Object.assign(options, siteOptions)

        // request Q - preserve the next scan to process, create metadata, restore next scan to process
        this.saveNextScanToProcess();
        this.requestQ[scanId] = {
            requests: [],
            options
        };

        // set up request limits
        let requestPerSec = this.requestQ[scanId].options.maxRequestsPerMin / 60
        this.requestQ[scanId].tokenRefillPerInterval = requestPerSec * this.config.maintenanceIntervalSecs
        this.requestQ[scanId].maxTokens = this.requestQ[scanId].options.maxBurstRate * this.requestQ[scanId].tokenRefillPerInterval
        this.requestQ[scanId].scanTokens = this.requestQ[scanId].maxTokens  // start with full bucket
        this.requestQ[scanId].pauseForIntervals = 0
        this.restoreNextScanToProcess();

        // inflight reqs
        this.inFlightRequests[scanId] = 0

        this.requestSent[scanId] = 0;
        this.requestCompleted[scanId] = 0;
        this.requestQ[scanId].proxyPassDetails = request.proxyPassDetails;
        this.requestQ[scanId].replayScanInfo = request.replayScanInfo || {};

        if(this.requestQ[scanId].replayScanInfo.isReplayScan) {
            this.requestQ[scanId].replayScanInfo.storeAttackInfoInReplayScan = request.storeAttackInfoInReplayScan;
        }
    }

    /**
     * Scan done - Do not queue any more requests - let in flight requests complete
     * @param {Number} scanId the scan that is done
     */
    scanDone(scanId) {
        if (this.requestQ[scanId]) {
            // don't accept any more requests
            this.requestQ[scanId].scanDone = true

            try {
                // complete all queued requests.
                let resp = {
                    err: new Error('Scan completed - cancelling request')
                }
                for (let defReq of this.requestQ[scanId].requests) {
                    this.resolveRequest(defReq, resp)
                }

                this.requestQ[scanId].requests = []
            } catch (err) {
                logger.log('error', `Unable to scan done. Reason. ${err.toString()}`, {
                    scanId
                })
            }
        }
    }

    forcePauseScan(scanId) {
        if (this.requestQ[scanId]) {
            this.requestQ[scanId].pauseScan = true;
        }
    }

    /**
     * Scan complete - cleanup
     * @param {Number} scanId the scan to delete fron request Queue
     */
    scanComplete(scanId) {
        // sanity checks
        if (!this.requestQ[scanId]) {
            return
        }

        if (0 != this.getPendingRequests(scanId) && !this.requestQ[scanId].pauseScan) {
            logger.log('error', `asked to clean up scan data for but there are pending requests. Ignoring request.`, {
                scanId
            })
            return
        }

        if(this.requestQ[scanId].pauseScan) {
            logger.log('info', `Cleaning all requests queue from httpRequestMonster.`, {
                scanId
            })
        }
        
        // preseve the next scan to process and clean up this scan entry
        this.saveNextScanToProcess();
        delete this.inFlightRequests[scanId];
        delete this.requestQ[scanId];
        this.restoreNextScanToProcess();
    }

    /**
     * Get all pending requests including in flight & queued ones. 
     * @param {Number} scanId return pending requests for this scan 
     */
    getPendingRequests(scanId) {
        if (!this.requestQ[scanId]) {
            return 0
        }
        return this.requestQ[scanId].requests.length + this.inFlightRequests[scanId]
    }

    /**
     * Ensure we preserve the scan to be processed next when inserting/deleting into requestQ. Meant to be used in pairs 
     * like: 
     *      this.saveNextScanToProcess()
     *      <code that changes requestQ>
     *      this.restoreNextScanToProcess()
     */
    saveNextScanToProcess() {
        this.savedNextScanid = this.getScanToProcess()
    }

    /**
     * Ensure we preserve the scan to be processed next when inserting/deleting into requestQ. Meant to be used in pairs 
     * like: 
     *      this.saveNextScanToProcess()
     *      <code that changes requestQ>
     *      this.restoreNextScanToProcess()
     * If we can't restore (eg. the next scan to process has been deleted), set it to a valid value
     */
    restoreNextScanToProcess() {
        let scanIds = Object.keys(this.requestQ)
        if (!scanIds.length || -1 == this.savedNextScanid) {
            this.nextScanToProcess = 0
        } else {
            // find the scanID in RequestQ
            let nextScanIdx = scanIds.indexOf(this.savedNextScanid)
            if (-1 != nextScanIdx) {
                // found => set it as next scan to process
                this.nextScanToProcess = nextScanIdx
            } else {
                // looks like this one has been deleted, just ensure thenext scan to process is in range
                this.nextScanToProcess %= scanIds.length
            }
        }

        this.savedNextScanid = -1
    }

    getScanToProcess() {
        return Object.keys(this.requestQ)[this.nextScanToProcess];
    }

    gotoNextScan() {
        this.nextScanToProcess++; // ';' to get indentation correct belw
        this.nextScanToProcess %= Object.keys(this.requestQ).length;
    }
}

module.exports = HTTPRequestMonster