// This file is preloaded using webPreferences and will
// be executed in the renderer process for that window.
// All of the Node.js APIs are available in this process.

const { contextBridge, ipcRenderer: preloadIpcRenderer } = require('electron');

// --- Backward compatibility for plugins expecting Node.js globals on window --- 
// These operate on the preload script's isolated 'window' object.
window.nodeRequire = require;
window.nodeExports = exports;
window.nodeModule = module;
// Attempt to delete standard ones to mimic old environment, though with contextIsolation,
// the main world renderer won't see these direct window modifications anyway.
if (typeof window.require === 'function') delete window.require;
if (typeof window.exports !== 'undefined') delete window.exports;
if (typeof window.module !== 'undefined') delete window.module;
// --- End backward compatibility section ---

// This file exists only to set up the global context and then load 
// other browser js files kinda like plugins. 

// The one global namespace - everything we do must be inside this.
class IndusfaceRenderer {
    constructor() {
        // TODO figure out how to get config from external sources.
        let corePlugins = ['indusface-core.js', 'indusface-electron.js', 'indusface-events.js', 'indusface-ajax.js',
            'indusface-xpath.js', 'indusface-actions.js', 'indusface-heuristics.js',
            'indusface-stacking', 'indusface-login-simple-heuristics.js', 'indusface-table-heuristics.js',
        ]

        this.config = {
            corePlugins: corePlugins,
            plugins: [...corePlugins]
        }

        // Use the ipcRenderer imported specifically for the preload script
        this.ipcRenderer = preloadIpcRenderer
        
        console.log('[Preload] Requesting initial configuration synchronously...');
        let initialSyncConfig = this.ipcRenderer.sendSync('get-initial-config');
        console.log('[Preload] Received initial configuration:', initialSyncConfig);
        
        //Interate over the initialSyncConfig and add the properties to the config object
        if(initialSyncConfig) { 
            for (let key in initialSyncConfig) {
                this.config[key] = initialSyncConfig[key];
            }
        }

        this.scope = {
            windowId: 1,
            webContents: null // This might be an issue with remote module disabled by default in newer Electron
        }

        this.pluginData = {}
        this.plugins = [] // Initialize plugins array

        // defaults
        this.log = console.log
    }

    bootstrap() {
        // Note: electron.remote is deprecated. If you need main process features, use IPC.
        // For now, assuming it's enabled and works in your Electron 32 version.
        // if (this.electron && this.electron.remote) { // 'this.electron' was not initialized in constructor
        //    this.scope.webContents = this.electron.remote.getCurrentWebContents();
        //    this.scope.windowId = this.electron.remote.getCurrentWindow() ? this.electron.remote.getCurrentWindow().id : 1;
        // }
        this.loadBrowserPlugins()
        this.invokePluginMethod('onAddInterestingElementTypes')
    }

    // load all browser plugins. Browser plugins are a little special as compared to the server/scanner
    // ones since they can and will directly interact with browser and indusfaceRenderer object.
    // there will be methods that core will call but plugins will also add properties and methods to 
    // the scanner instance directly  
    loadBrowserPlugins() {
        for (let pluginName of this.config.plugins) {
            try {
                // Use the 'nodeRequire' available in this preload script's global scope
                let Plugin = nodeRequire('./' + pluginName); 
                let plugin = new Plugin();
                this.plugins.push(plugin);
                // plugin.init() is expected to add methods to the global `window.indusfaceRenderer`
                // or to `this` (the IndusfaceRenderer instance).
                // If methods in plugins use `this` to refer to the plugin instance,
                // they should be bound, e.g., indusfaceRenderer.getXpath = this.getXpath.bind(this);
                plugin.init()
            } catch (error) {
                console.error(`Error loading plugin ${pluginName}: ${error.toString()}`);
            }
        }
    }

    // Method for core to call plugin methods
    invokePluginMethod(method, ...args) {
        let methodImplemented = 0;
        for (let plugin of this.plugins) {
            if (typeof plugin[method] === 'function') {
                methodImplemented++;
                plugin[method](...args); // Call method on the plugin instance
            }
        }
        return methodImplemented;
    }
}

// Create the instance of IndusfaceRenderer.
// This instance will be modified by plugins if they add methods to `window.indusfaceRenderer`.
const indusfaceRendererInstance = new IndusfaceRenderer()

// Make the instance globally available *within this preload script's isolated context*.
// Plugins (like IndusfaceXpath) that do `indusfaceRenderer.getXpath = ...`
// will be referring to this global object.
window.indusfaceRenderer = indusfaceRendererInstance

// Bootstrap the instance: loads plugins, which in turn add their methods to indusfaceRendererInstance
indusfaceRendererInstance.bootstrap()

// ---- Dynamically expose methods from indusfaceRendererInstance ----
const exposedApi = {}
if (indusfaceRendererInstance) {
    for (const key in indusfaceRendererInstance) {
        // Check if the property is a function and an "own" property of the instance
        // (i.e., not from the prototype chain, typically methods added by plugins).
        if (typeof indusfaceRendererInstance[key] === 'function' &&
            Object.prototype.hasOwnProperty.call(indusfaceRendererInstance, key)) {
            
            exposedApi[key] = (...args) => {
                // When a method is called via the exposed API (e.g., window.indusfaceRenderer.getXpath()),
                // ensure it's called on the original indusfaceRendererInstance.
                // The `this` context inside the original method depends on how it was attached by the plugin.
                // If plugins used `.bind(this)` when attaching, their internal `this` is preserved.
                // Otherwise, `this` inside the method will be `indusfaceRendererInstance`.
                return indusfaceRendererInstance[key].apply(indusfaceRendererInstance, args);
            }
        }
    }

    // Expose the dynamically built API to the main world (renderer process)
    contextBridge.exposeInMainWorld('indusfaceRenderer', exposedApi)
    console.log('[Preload] Exposed API to main world, methods:', Object.keys(exposedApi).join(', '))

} else {
    console.error('[Preload] indusfaceRendererInstance was not created, cannot expose API.')
}

// Clean up the global reference in the preload script's context if no longer needed,
// though it's generally fine as it's isolated.
// delete window.indusfaceRenderer;