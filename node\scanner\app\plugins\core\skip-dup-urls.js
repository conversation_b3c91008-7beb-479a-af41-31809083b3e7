let utils = require('../../ifc-utils.js')
const URL = require('url').URL
const logger = require('../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const s3Utils = require('../../../common/lib/s3-utils')
const AnalyzeDupUrls = require('../../../common/lib/messages/analyze-dup-urls')
const _ = require('lodash')

const pluginName = 'skipDupUrls'
const analysisPrefix = 'analysis/'

class SkipDupUrls {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        // merge the plugin specific config
        this.mergeConfig()
        let pluginData = this.config.pluginData.skipDupUrls
        this.enabled = pluginData.enabled

        // Only proceed if plugin is enabled
        if (this.enabled) {
            this.matchesToActivateDupRegex = pluginData.matchesToActivateDupRegex
            this.minUsablePrecision = pluginData.minUsablePrecision

            // dup analysis
            this.dupAnalysis = []

            // event handlers
            scanner.on('scan-start', this.onScanStart.bind(this))
            scanner.on('pre-trigger', this.preTriggerActions.bind(this))
            scanner.on('crawl-end-processing-done', this.onCrawlEndProcessingDone.bind(this))

            scanner.on('serialize-state', this.serializeState.bind(this))
            scanner.on('deserialize-state', this.deserializeState.bind(this))
        }
    }

    /**
     * Get analyzed dup url info from S3
     * @param {config} config Scanner config & runtime data store
     */
    async onScanStart(config) {
        // get the analysis from S3
        try {
            let resp = await s3Utils.getFile(analysisPrefix, `crawler-dup-analysis-${this.config.serviceId}.json`)
            if (resp && resp.Body) {
                let dupAnalysis = JSON.parse(resp.Body)
                for (let stateAnalysis of dupAnalysis) {
                    // if we are resuming the scan, merge in the saved runtime data. Since this is a resume, we can be sure 
                    // that the crawl tree => the stateIds are the same. This also means that the analysis is done on the
                    // entrire crawl tree so just need to merge in run time info.
                    let prevStateAnalysis = this.dupAnalysis.find(e => (e.stateId == stateAnalysis.stateId))
                    stateAnalysis.urlsMatched = prevStateAnalysis ? prevStateAnalysis.urlsMatched : 0
                    stateAnalysis.seenParamNames = prevStateAnalysis ? prevStateAnalysis.seenParamNames : []
                    stateAnalysis.activated = prevStateAnalysis ? prevStateAnalysis.activated : false // gets activated after 'n' matches

                    // calculate fscore. Only consider FP, we don't care about FNs here since even if there is high FN
                    // cannot be worse than doing no regex dup check.
                    stateAnalysis.precision = HaikuUtils.precision(stateAnalysis.TP, stateAnalysis.FP)
                }

                // Only keep those that are higher than the min precision specified
                this.dupAnalysis = dupAnalysis.filter(a => a.precision >= this.minUsablePrecision)
            }
        } catch (err) {
            logger.log('error', `${pluginName} - Could not get dup analysis for ${this.config.serviceId} : ${err.toString()}`)
        }

        logger.log('info', `skip urls will use ${this.dupAnalysis.length} analysed patterns`)
    }


    /**
     * return object that can be JSON.stringified and stored
     * @param {Object} pluginData Object where we can add our serialized state under our own key
     */
    serializeState(pluginData) {
        pluginData[pluginName] = {
            dupAnalysis: this.dupAnalysis
        }
    }

    /**
     * Restore plugin data that from serialized object
     * @param {Object} pluginData Object with our serialized state under our own key
     */
    deserializeState(pluginData) {
        if (!pluginData[pluginName]) {
            return
        }

        this.dupAnalysis = pluginData[pluginName].dupAnalysis || []
    }

    preTriggerActions(ret, actions) {
        // play well with other plugins
        if (ret.skipAction) {
            // another plugin already decided to skip, nothing to do
            return
        }

        // see if the action is a load action
        let lastAction = actions.flatten()
        if (Array.isArray(lastAction)) {
            lastAction = lastAction.pop()
        }

        let shouldCheckAction = (lastAction.actionType == 'load')
        if (!shouldCheckAction) {
            return
        }

        // See if any regex matches the URL
        let possibleDupUrl = lastAction.url
        let matchedAnalysis = this.dupAnalysis.filter(x => utils.isDupUrl(x.regex, possibleDupUrl))

        // if any one matched, update all matched analysis states.
        if (matchedAnalysis.length) {
            ret.regexMatched = []
        }

        let forceAllow = false
        for (let analysis of matchedAnalysis) {
            // see if this URL has any parameter not yet covered
            let paramNames = HaikuUtils.splitIntoParamsAndValues({
                uri: possibleDupUrl
            }).params
            let unseenParamNames = _.difference(paramNames, analysis.seenParamNames)
            if (unseenParamNames.length > 0) {
                // we need to allow this URL, have not yet attacked some parameters
                forceAllow = true

                // now we ahve seen these parameters
                analysis.seenParamNames.push(...unseenParamNames)
            }

            // see if we should skip this URL
            if (analysis.activated) {
                ret.regexMatched.push(analysis.regex)
            }

            // update analysis metadata
            analysis.urlsMatched++
            if (!analysis.activated && analysis.urlsMatched >= this.matchesToActivateDupRegex) {
                analysis.activated = true // gets activated after 'n' matches
                logger.log( 'info', `activated regex: ${analysis.regex}`)
            }

        }

        if (!forceAllow && ret.regexMatched && ret.regexMatched.length > 0) {
            ret.skipAction = true
            ret.possibleDupUrl = possibleDupUrl
        }

        if (ret.skipAction) {
            logger.log('info', `${pluginName} - skipping action because last action = load url ${possibleDupUrl} matches dup regex(es): ${ret.regexMatched}`)
        }
    }

    /**
     * Called when crawl processing i.e. crawl loop & end of crawl processing like uploading files
     * is done. We will send a message to kick off dup URL analysis worker
     */
    onCrawlEndProcessingDone() {
        if (!this.enabled) {
            return
        }

        // send message to kick off worker
        let msgContent = {
            scanId: this.config.scanId,
            scanlogId: this.config.scanlogId
        }
        
        let msg = new AnalyzeDupUrls(msgContent)
        msg.publish(this.config.msgQ)        
    }   

    mergeConfig() {
        if (!this.config.siteConfig || !this.config.siteConfig.pluginData || !this.config.siteConfig.pluginData.skipDupUrls) {
            return
        }

        // plugin data for "check for dup" plugin
        let pluginData = this.config.pluginData.skipDupUrls
        let sitePluginData = this.config.siteConfig.pluginData.skipDupUrls

        if (_.isBoolean(sitePluginData.enabled)) {
            pluginData.enabled = sitePluginData.enabled
        }

        if (sitePluginData.matchesToActivateDupRegex) {
            pluginData.matchesToActivateDupRegex = sitePluginData.matchesToActivateDupRegex
        }

        if (sitePluginData.minUsablePrecision) {
            pluginData.minUsablePrecision = sitePluginData.minUsablePrecision
        }
    }
}

module.exports = SkipDupUrls