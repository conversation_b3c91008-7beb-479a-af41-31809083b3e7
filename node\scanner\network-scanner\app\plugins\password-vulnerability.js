const debug = require('debug')('PasswordVulnerability')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class PasswordVulnerability extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-pwd-submitted-using-http-GET'
    }


    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     * 	<form action="#" method="GET">
            New password:<br />
            <input type="password" AUTOCOMPLETE="off" name="password_new"><br />
            Confirm new password:<br />
            <input type="password" AUTOCOMPLETE="off" name="password_conf"><br />
            <br />
            <input type="submit" value="Change" name="Change">

        </form>
     */
    wantProcessAttackResponse(attack) {
        //check if input type=password present, then only call processAttackResponse
        let body = _.get(attack, "result.resp.body", '')
        if (body.length > 20 && /method=(?:"|')?GET/i.test(body) && /type=(?:'|")?password/i.test(body)) {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //if vuln detected for a req then return
        if (pluginDataForRequest.pwdSubmittedUsingGetVulnFound) {
            return
        }
        let details = []
        let ResBody = _.get(attack, "result.resp.body", '')
        let regexp = /<form.+method=(?:'|")GET(?:"|')>[\w\W]+?<\/form>/gi
        let formtag = []
        try {
            formtag = ResBody.match(regexp)
            if (formtag.length > 0 && /type="password"/i.test(formtag)) {
                for (let tag of formtag) {
                    if (tag.length > 20 && /type="password"/i.test(tag)) {
                        let inputtag = tag.match(/<input.+?type=(?:"|')password(?:"|').+?>/gi)
                        for (let pwdvalue of inputtag) {
                            if (pwdvalue.length > 0 && /type=(?:"|')password/i.test(pwdvalue)) {
                                details.push({ result: pwdvalue })
                            }
                        }
                    }
                }
                if (details.length > 0) {
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                    pluginDataForRequest.pwdSubmittedUsingGetVulnFound = true
                }
            }
        }
        catch (e) {
            return
        }

        /*let $ = _.get(attack, 'result.resp.httpResponse.cheerio');
        let form = []
        form[1] = $("form[method=GET]")
        form[2] = $("form[method=get]")
        for (let i = 1; i < 3; i++) {
            if (form[i].length > 0) {
                let pwdField = $('input[type=password]', form[i])
                if (pwdField.length > 0) {
                    let details = []
                    pwdField.each(function (index) {
                        details.push(
                            {
                                name: $(this).attr('name'),
                                inputType: "password",
                                dom_element: $.html(this)
                            }
                        )
                    })
                    vuln = {
                        //name: 'pwd-submitted-using-http-GET',
                        details: details
                    }
                    break
                }
            }
        }
        if (vuln) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginDataForRequest.pwdSubmittedUsingGetVulnFound = true
        }*/
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['type=password', 'password', 'method=\'get\'']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method"]);
    }
}

module.exports = PasswordVulnerability