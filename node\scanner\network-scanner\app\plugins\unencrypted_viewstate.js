const debug = require('debug')('UnencryptedViewState')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')


class UnencryptedViewState extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }


    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (attack.attackArea == "original-crawler-request") {
            //check if view state present in response body, then only call processAttackResponse
            let body = _.get(attack, "result.resp.body")
            if (/viewstate/i.test(body)) {
                return true
            }
        }
        return false
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    async processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.viewStateVulnFound) {
            return
        }

        try {//get view state param value
            let $ = _.get(attack, 'result.resp.httpResponse.cheerio');
            let viewState = $("#__VIEWSTATE*").attr('value')

            //call python script and find vulnerability
            if (viewState) {
                let viewstateUnenc = {
                    "haikuVulnId": "ID-unencrypted-viewstate",
                    "parameters": {
                        "viewstate": viewState

                    }
                }
                let output = await this.networkScanner.pythonExtension.executeCommand('viewstate_vulns.py', viewstateUnenc)

                //parse output to get vulnerability
                if (!output.err && output.json.length > 0) {
                    for (let vulnObj of output.json) {
                        if (vulnObj.vulnerabilityFound) {
                            this.addVulnerabilitytoResult(attack, vulnObj.haikuVulnId, vulnObj.details)
                            pluginDataForRequest.viewStateVulnFound = true
                        }
                    }
                }
            }
        } catch (e) { return }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != 'ID-unencrypted-viewstate') { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['VIEWSTATE', 'viewstate']);
    }
}

module.exports = UnencryptedViewState