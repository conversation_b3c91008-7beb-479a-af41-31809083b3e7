class IndusfaceHeuristics {
    constructor() {}

    name(short) {
        return short ? "IFC-Heuristics" : "Indusface Heuristics Plugin"
    }

    init() {
        let dateToUse = new Date(Date.now() - (2 * 24 * 60 * 60 * 1000)) // 2 days back
        // these classes tested against: https://www.456bereastreet.com/lab/html5-input-types/  
        this.heuristicClasses = [new class __Email {
                matches(inputItem) {
                    let matched = inputItem.nodeName == 'input' && inputItem.type == 'email'
                    if (!matched && inputItem.nodeName == 'input' && inputItem.type == 'text') {
                        var arr = ['email', 'emailaddress']
                        var items = [inputItem.name, inputItem.id, inputItem.text].map((item) => {
                            return item.toLowerCase()
                        })
                        var matchedItems = items.filter((ele) => {
                            var fil = arr.filter((item) => {
                                return ele.includes(item)
                            });
                            return fil.length > 0
                        })
                        matched = matchedItems.length > 0
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'email'
                }
                getIfcValues() {
                    return ['<EMAIL>']
                }
            }(), new class __Color {
                matches(inputItem) {
                    return inputItem.nodeName == 'input' && inputItem.type == 'color'
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'Color'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || '#ff9300']
                }
            }(), new class __Phone {
                matches(inputItem) {
                    let re = /mobile|phone|number|tel|cell|fax/i;
                    return inputItem.nodeName == 'input' &&
                        (inputItem.type == 'tel' || re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id))
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'phone'
                }
                getIfcValues() {
                    return ['7428730894']
                }
            }(), new class __Zipcode {
                matches(inputItem) {
                    let re = /zip|postal/i;
                    let matched = inputItem.nodeName == 'input' && inputItem.type == 'number'
                    if (matched) {
                        matched = re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id)
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'zipcode'
                }
                getIfcValues() {
                    return ['90211']
                }
            }(), new class __DOBTextField {
                matches(inputItem) {
                    let matched = false
                    if (inputItem.nodeName == 'input' && (inputItem.type == 'text' || inputItem.type == 'date')) {
                        var arr = ['dob', 'dateofbirth']
                        var items = [inputItem.name, inputItem.id, inputItem.text].map((item) => {
                            return item.toLowerCase().trim()
                        })
                        var matchedItems = items.filter((ele) => {
                            var fil = arr.filter((item) => {
                                return ele.includes(item)
                            });
                            return fil.length > 0
                        })
                        matched = matchedItems.length > 0
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'Date Of Birth'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || '1985-11-12']
                }
            }(), new class __DateTime {
                matches(inputItem) {
                    return inputItem.nodeName == 'input' && /datetime/.test(inputItem.type)
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'DateTime'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || dateToUse.toISOString().split('.')[0]]
                }
            }(), new class __Date {
                matches(inputItem) {
                    let matched = inputItem.nodeName == 'input' && inputItem.type == 'date'
                    if (!matched && inputItem.nodeName == 'input' && inputItem.type == 'text') {
                        let re = /date|dt/i;
                        matched = re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id)
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'date'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || dateToUse.toISOString().split('T')[0]]
                }
            }(), new class __Week {
                matches(inputItem) {
                    let matched = inputItem.nodeName == 'input' && inputItem.type == 'week'
                    if (!matched && inputItem.nodeName == 'input' && inputItem.type == 'text') {
                        let re = /week/i;
                        matched = re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id)
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'week'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || dateToUse.getFullYear() + '-W10']
                }
            }(), new class __Month {
                matches(inputItem) {
                    let matched = inputItem.nodeName == 'input' && inputItem.type == 'month'
                    if (!matched && inputItem.nodeName == 'input' && inputItem.type == 'text') {
                        let re = /month/i;
                        matched = re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id)
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'month'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || dateToUse.getFullYear() + '-' + new String(dateToUse.getMonth()).padStart(2, '0')]
                }
            }(), new class __Time {
                matches(inputItem) {
                    let matched = inputItem.nodeName == 'input' && inputItem.type == 'time'
                    if (!matched && inputItem.nodeName == 'input' && inputItem.type == 'text') {
                        let re = /time/i;
                        matched = re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id)
                    }
                    return matched
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'time'
                }
                getIfcValues(inputItem) {
                    return [inputItem.min || inputItem.max || '14:30']
                }
            }(), new class __Url {
                matches(inputItem) {
                    return (inputItem.nodeName == 'input' && inputItem.type == 'url')
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'time'
                }
                getIfcValues() {
                    return ['https://www.indusface.com']
                }
            }(), new class Password {
                matches(inputItem) {
                    return inputItem.nodeName == 'input' && inputItem.type == 'password'
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'password'
                }
                getIfcValues() {
                    return ['Test@1234']
                }
            }(), new class Radio {
                matches(inputItem) {
                    return inputItem.nodeName == 'input' && inputItem.type == 'radio'
                }
                getIfcAction() {
                    return 'click'
                }
                getIfcType() {
                    return 'radio'
                }
                getIfcValues() {
                    return ['TestUser']
                }
            }(), new class SelectOne {
                matches(inputItem, input) {
                    this.input = input;
                    return inputItem.nodeName == 'select' && !inputItem.allAttrs.multiple
                }
                getIfcAction() {
                    return 'select'
                }
                getIfcType() {
                    return 'Normal Select'
                }
                getIfcValues() {
                    return [
                        [this.input.options.length - 1]
                    ]
                }
            }(), new class SelectMultiple {
                matches(inputItem, input) {
                    this.input = input;
                    return inputItem.nodeName == 'select' && inputItem.allAttrs.multiple
                }
                getIfcAction() {
                    return 'select'
                }
                getIfcType() {
                    return 'Normal Select'
                }
                getIfcValues() {
                    return [
                        [this.input.options.length - 1, this.input.options.length - 2]
                    ]
                }
            }(), new class NormalCheckbox {
                matches(inputItem) {
                    return inputItem.nodeName == 'input' && inputItem.type == 'checkbox'
                }
                getIfcAction() {
                    return 'checkbox'
                }
                getIfcType() {
                    return 'Normal Checkbox'
                }
                getIfcValues() {
                    return [true]
                } // true - check, false - uncheck
            }(), new class Number { // catch all number field
                matches(inputItem) {
                    return inputItem.nodeName == 'input' && (inputItem.type == 'number' || inputItem.type == 'range')
                }
                getIfcAction() {
                    return 'type'
                }
                getIfcType() {
                    return 'number'
                }
                getIfcValues(inputItem) {
                    let number = inputItem.min || inputItem.max || '560004'
                    return [number]
                }
            }(), new class __Pan {
                matches(inputItem) {
                    let re = /pan/i;
                    return inputItem.nodeName == 'input' &&
                        (re.test(inputItem.name) || re.test(inputItem.placeholder) || re.test(inputItem.id))
            }
            getIfcAction() {
                return 'type'
            }
            getIfcType() {
                return 'PAN'
            }
            getIfcValues() {
                return ['**********']
            }
        }(),new class __File {
            matches(inputItem) {
                return inputItem.nodeName == 'input' && inputItem.type == 'file';
            }
            getIfcAction() {
                return 'upload';
            }
            getIfcType() {
                return 'file';
            }
            getIfcValues() {
                return ['path/to/file'];
            }
            }(),
        new class NormalText { // catch all for input - assumes text
            matches(inputItem) {
                return true
            }
            getIfcAction() {
                return 'type'
            }
            getIfcType() {
                return 'Normal Text'
            }
            getIfcValues(inputItem) {
                let valToUse = inputItem.value
                if (!valToUse || valToUse == 'null') {
                    valToUse = 'defaultText'
                }

                return [valToUse]
            }
        }()]

    this.heuristicActionClasses = [new class Logout {
            matches(item) {
                var arr = ['logout', 'signout', 'invalidate']
                let matched = false
                let text = item.text.toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                let id = item.id.toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                if ((arr.indexOf(text) > -1) ||
                    (arr.indexOf(id) > -1)) {
                    matched = true
                }
                return matched
            }
            getIfcAction() {
                return 'non-click'
            }
            getIfcType() {
                return 'logout'
            }
        }(),
        new class Login {
            matches(item) {
                var arr = ['login', 'signin']
                let matched = false
                let text = item.text.toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                let id = item.id.toLowerCase().replace(/\s+/g, '').replace(/-/g, '')
                if ((arr.indexOf(text) > -1) ||
                    (arr.indexOf(id) > -1)) {
                    matched = true
                }
                return matched
            }
            getIfcAction() {
                return 'login-click'
            }
            getIfcType() {
                return 'login'
            }
        }()
    ]
}

OnInputItemAdded(inputItem, input) {
    //console.log('OnInputItemAdded called for ', inputItem, inputItem.nodeName, inputItem.type, inputItem.name)

    if (inputItem && inputItem.nodeName) {
        for (let heuristicClass of this.heuristicClasses) {
            if (heuristicClass.matches(inputItem, input)) {
                inputItem.ifcAction = heuristicClass.getIfcAction()
                inputItem.ifcType = heuristicClass.getIfcType()
                inputItem.ifcValues = heuristicClass.getIfcValues(inputItem)
                // respect maxlength
                if (input.maxLength > 0) {
                    inputItem.ifcValues = inputItem.ifcValues.map( val => val.substring(0,input.maxLength))
                }
                //console.log(`For inputItem ${inputItem.name} filled ifcType ${inputItem.ifcType} and ifcValues ${inputItem.ifcValues}`)
                break;
            }
        }
    }
}

OnEventItemAdded(item, element) {
    //console.log('OnEventItemAdded called for ', element)
    if (element) {
        for (let heuristicActionClass of this.heuristicActionClasses) {
            if (heuristicActionClass.matches(item, element)) {
                item.ifcAction = heuristicActionClass.getIfcAction()
                item.ifcType = heuristicActionClass.getIfcType()
                //console.log(`For item ${item} filled ifcType ${item.ifcType}`)
                break;
            }
        }
    }
}

OnLinkItemAdded(item, element) {
    //console.log('OnLinkItemAdded called for ', element)
    if (element) {
        for (let heuristicActionClass of this.heuristicActionClasses) {
            if (heuristicActionClass.matches(item, element)) {
                item.ifcAction = heuristicActionClass.getIfcAction()
                item.ifcType = heuristicActionClass.getIfcType()
                //console.log(`For item ${item} filled ifcType ${item.ifcType}`)
                break;
            }
        }
    }
}
}

module.exports = IndusfaceHeuristics