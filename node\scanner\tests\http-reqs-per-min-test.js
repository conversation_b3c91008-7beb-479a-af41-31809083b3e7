const _ = require('lodash')
const logger = require('../common/lib/haiku-logger')
const HTTPRequestMonster = require('../network-scanner/lib/http-request')
let config = require('../network-scanner/config/network-scanner-config.js')
let httpReqMonster = new HTTPRequestMonster(config)

logger.setMetadata({
    haikuProcess: 'haiku-tests',
})

// Run a bunch of requests and see 
const URIs = ["https://www.indusface.com"]
const scanId = 111
const requestsToSend = 20
const siteOptions = {
    perDomainMaxParallelRequests: 5,
    // token bucket tuning
    maxRequestsPerMin: 5, // max requests per minute
    maxBurstRate: 3 // determines bucketSize as a factor of how many intervals worth of tokens
}

httpReqMonster.scanStarted(scanId, siteOptions)

let requestTemplate = {
    scanId,
    scanlog_id: scanId,
    scanner: "haiku",
    resourceType: "mainFrame",
    method: "GET",
    headers: {
        "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36",
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        "Accept-Encoding": "gzip, deflate, br",
    }
}

for (i = 0; i < requestsToSend; i++) {
    let req = _.cloneDeep(requestTemplate)
    req.mainUrl = URIs[i % URIs.length]
    req.uri = URIs[i % URIs.length]
    //req.httpRequest.url = URIs[i % URIs.length]
    logger.log('info', `sending request ${i}`)
    httpReqMonster.queueRequest(req)
}
logger.log('info', 'sent all requests')

setInterval(() => {
    let pending = httpReqMonster.getPendingRequests(scanId);
    logger.log('debug', `Waiting for ${pending} queues/in flight requests`);
    if (pending == 0) {
        logger.log('info', `All requests complete !!`)
        process.exit(0)
    }
}, 2500)