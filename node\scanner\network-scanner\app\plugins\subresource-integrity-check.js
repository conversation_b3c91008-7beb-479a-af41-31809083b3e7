const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class SubResourceIntegrityChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-subresource-integrity-check'
    }

    wantProcessAttackResponse(attack) {
        let ResBody = _.get(attack, "result.resp.body")
        if (/<(?:link|script).+(?:href|src)\s*=\s*['"]https?:\/\/.+?\.(js|css)['"].*?>/i.test(ResBody)) {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        // This condition is to check if vuln already found then return
        if (pluginDataForRequest.SRICheckReported) {
            return
        }

        /**
         * Subresource Integrity - W3C Recommendation 23 June 2016 
         * https://www.w3.org/TR/SRI/
         * 
         * <script src="https://code.jquery.com/jquery-2.1.4.min.js" integrity="sha384-R4/ztc4ZlRqWjqIuvf6RX5yb/v90qNGx6fS48N0tRxiGkqveZETq72KgDVJCp2TC" crossorigin="anonymous"></script>
         * 
         * <link rel="stylesheet" href="https://site53.example.net/style.css" integrity="sha384-+/M6kredJcxdsqkczBUjMLvqyHb1K/JThDXWsBVxMEeZHEaMKEOEct339VItX1zB" crossorigin="anonymous">
         * */

        let details = []
        let ResBody = _.get(attack, "result.resp.body")
        let tagsDts = ResBody.match(/<(?:link|script).+(?:href|src)\s*=\s*['"]https?:\/\/.+?\.(js|css)['"].*?>/ig)
        if (tagsDts.length > 0) {
            let uriRaw = new URL(attack.href)
            tagsDts.forEach(el => {
                if (!/integrity\s*=/i.test(el)) {
                    let currval = el.match(/(?:href|src)\s*=\s*['"]https?:\/\/.+?\.(js|css)['"]/ig).toString()
                    let currLink = currval.split('=')[1].replace(/"/g, '')
                    let curruri = new URL(currLink)
                    if (uriRaw.hostname != curruri.hostname) {
                        details.push({
                            tagsFound: el,
                            urlsFound: currLink
                        })
                    }
                }
            })
            if (details.length > 0) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                pluginDataForRequest.SRICheckReported = true
                return
            }
        }
    }
}
module.exports = SubResourceIntegrityChecker

/* let $ = _.get(attack, 'result.resp.httpResponse.cheerio')
for (let tags of tagsToCheck) {
let tagElements = $(tags.tag)
tagElements.each(function (index, el) {
    let srcVal = $(el).attr(tags.attr)
    let integrityVal
    if ($(el).attr("integrity")) { integrityVal = true }
    if (srcVal) {
        if (!(srcVal.includes("../"))) {
            details.push({
                tag: tags.tag,
                src: srcVal,
                integrityAttribute_present: integrityVal
            })
        }
    }
})
}

let details1 = _.filter(details, (el) => {
// remove all the links where url is of this domain as we only need 3rd party urls
// only include urls where it start with http or //
// remove urls where integrity attribute is present

return !el.src.includes(attack.hostname) && /^(http|\/\/)/i.test(el.src) && !el.integrityAttribute_present
})

// Only process vulnerability if any resources are present
if (details1.length > 0) {
let vuln = {
    details: {
        //fetch all the tags and urls and merge as string
        tagsFound: _.map(details1, "tag").join(", "),
        urlsFound: _.map(details1, "src").join(", ")
    }
}

this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
pluginDataForRequest.SRICheckReported = true
return

}
}
}

/** verify below tags present in response
const tagsToCheck = [
{ tag: 'script', attr: 'src' },
{ tag: 'link', attr: 'href' }] */
