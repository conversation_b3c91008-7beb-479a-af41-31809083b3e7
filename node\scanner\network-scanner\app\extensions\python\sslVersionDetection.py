import socket
import ssl
import pprint
import sys
import json

vulndataList = []
weakCipherList = []
vdata = {}
protocolVer = [ssl.PROTOCOL_TLSv1_1, ssl.PROTOCOL_TLSv1, ssl.PROTOCOL_SSLv3]
global supportedCiphers

def read_json_stdin():
    infile = sys.stdin
    jsonstr = json.load(infile)
    if jsonstr != None:
        infile.close()
        return jsonstr['hostName'], jsonstr['strongCiphers']
    else:
        return None


def write_json_stdout(outdata):
    #print("writing to stdout...")
    jsonData = json.dump(outdata, sys.stdout, sort_keys=True, indent=4)
    print("\n")
    sys.stdout.flush()


def sslWeakVersion(hostNameData):
    if len(hostNameData) > 0:
        vdata['tlsV11Found'] = False
        vdata['tlsV1Found'] = False
        vdata['sslV3Found'] = False
        try:
            for i in protocolVer:

                context = ssl.SSLContext(i)

                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                ssl_sock = context.wrap_socket(s, server_hostname=hostNameData)
                ssl_sock.connect((hostNameData, 443))

                sslVersion = ssl_sock.version()
                # certificateInfo=ssl_sock.getpeercert()
                # cipherUsed=ssl_sock.cipher()
                supportedCiphers = context.get_ciphers()

                if sslVersion != None:
                    if sslVersion == 'TLSv1.1':
                        vdata['tlsV11Found'] = True
                    elif sslVersion == 'TLSv1':
                        vdata['tlsV1Found'] = True
                    elif sslVersion == 'SSLv3':
                        vdata['sslV3Found'] = True
        except Exception as e:
            pass

        # convert ciphers list obtained to json
        if len(supportedCiphers) != 0:
            jsonData = json.loads(json.dumps(
                supportedCiphers, indent=4, sort_keys=True))

            # Get all the ciphers
            for x in jsonData:
                # get description of the ciphers and exact cipher name in list
                t = x.get('description').split(' ', 1)[0]

                # create weak cipher list, only add those which are not
                # present in strong cipher list
                if t not in strongCipherList:
                    weakCipherList.append(t)

            vdata['oldCiphersFound'] = ':'.join(weakCipherList)
            # verify if the list is empty and if so then flag it false
            if len(vdata['oldCiphersFound']) == 0:
                vdata['oldCiphersFound'] = False
        else:
            vdata['oldCiphersFound'] = False

        vulndataList.append(vdata)
    return vulndataList


if __name__ == "__main__":
    hostNameData, cipherList = read_json_stdin()
    # convert ciphers obtained to list
    strongCipherList = cipherList.split(':')
    getSSLWeakVersion = sslWeakVersion(hostNameData)
    write_json_stdout(getSSLWeakVersion)
