const ActionList = require('../../datastructure/action-list.js')
const DebugWaitAction = require('../../datastructure/debug-wait-action.js')

class DebugWaitAttack {
  constructor(scanner) {
    this.config = scanner.config
  }

  getName() {
    return 'DebugWaitAttack'
  }

  initForAttack(attackContext) {
    console.log(`${this.getName()}: init for attack`)
    attackContext.executionContext.mode = 'attack-' + this.getName()
  }

  * getAttacksGen(attackContext) {
      yield new ActionList(this.getName() + '-attack-' + i).addAction(new DebugWaitAction())
  }
}
module.exports = DebugWait