const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')
const request = require('request');
class NoCaptchaOnLoginPage extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-no-captcha-on-login-page'
    }

    getAttackVectors() {
        return passwordVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
    }

    async performNetworkAttack(attack) {
        if (/password|passwd?|pwd|(?:login|auth|user)_pass|user_secret|passcode|\bsecret\b/i.test(attack.param)) {
            return await super.performNetworkAttack(attack)
        }
        else { return false }
    }

    async wantProcessAttackResponse(attack) {
        let ReqBody = _.get(attack, 'httpRequest.body', '')
        if (/password|passwd?|pwd|(?:login|auth|user)_pass|user_secret|passcode|\bsecret\b/i.test(ReqBody) || /password|passwd|pwd|(?:login|auth|user)_pass|user_secret|passcode|\bsecret\b/i.test(attack.href)) {
            let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
            if (attack.attackArea == 'original-crawler-request' && attack.pluginName == 'Original Crawler Request') {
                let loginpage = false
                let loginDts = HaikuUtils.getLoginParams(attack.httpRequest)
                if (loginDts && loginDts.usernameParams.length > 0) {
                    loginpage = true
                }
                if (!loginpage) {
                    let loginInfo = LoginDelegate.isLoginRequest(attack.httpRequest)
                    if (loginInfo == true) {
                        loginpage = true
                    }
                }

                if (!loginpage && attack.httpRequest.method === "POST") {
                    if (/password|passwd?|pwd|(?:login|auth|user)_pass|user_secret|passcode|\bsecret\b/i.test(ReqBody)) {
                        loginpage = true
                    }
                }

                if (!loginpage && attack.httpRequest.method === "GET") {
                    if (/password|passwd?|pwd|(?:login|auth|user)_pass|user_secret|passcode|\bsecret\b/i.test(attack.href)) {
                        loginpage = true
                    }
                }

                if (loginpage) {
                    let NewResponse = []
                    let uriRaw = attack.httpRequest.headers.Referer
                    if (uriRaw.length > 0) {
                        let newheader = attack.originalRequest.httpRequest.headers
                        const options = {
                            headers: newheader,
                            uri: uriRaw,
                            rejectUnauthorized: false,
                            timeout: 30000
                        };
                        NewResponse = await this.GETRequest(options)
                        if (NewResponse && NewResponse != 'NotFound' && NewResponse.statusCode != 408) {
                            let AtkResbody = NewResponse.resbody
                            if (/< ?input[^><]+type\s*=\s*['"]password|< ?input[^>]+(?:name|id) ?= ?['"](?:password|passwd?|hidepwd|pwd|(?:login|auth|user)_pass|user_secret|passcode|\bsecret\b)[^>]+>/i.test(AtkResbody)) {
                                pluginStorageScanScope.CheckCaptchaOnLogin = true
                            }
                        }
                    }
                }
            }
            if (attack.pluginName == this.getName() && pluginStorageScanScope.CheckCaptchaOnLogin == true) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (attack.pluginName == this.getName() && pluginStorageScanScope.CheckCaptchaOnLogin == true) {
            //Set the plugin scope to entire scan per site
            //if vuln already found then return
            if (pluginStorageScanScope.NoCaptchaOnLoginPage) {
                return
            }

            if (pluginStorageScanScope.loginAttempt == undefined) {
                pluginStorageScanScope.loginAttempt = 0
            }

            let body = _.get(attack, 'result.resp.httpResponse.body')
            //Increase attack count
            pluginStorageScanScope.loginAttempt++

            //Only check for vulnerability once few login attempts have been made
            if (pluginStorageScanScope.loginAttempt >= this.getMetadata(attack).LoginAttempts) {
                //Verify response of attack if account disabled after invalid logins
                if (!/captcha|i am not a robot|(?:enter|type) the (?:characters|text) (?:you|above|below|seen)/i.test(body)) {
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
                    pluginStorageScanScope.NoCaptchaOnLoginPage = true
                }

            }
        }
    }

    GETRequest(options) {
        return new Promise((resolve) => {
            try {
                request.get(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body, statusMessage: res.statusMessage }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }
}

/* //Only use valid username
const usernameVectors = [
    // all the password vectors, can use IdentityVector as well
    VectorResponseAttack.identityVector
] */

//we are going to try with  5 set of invalid password
const passwordVectors = [
    // all the password vectors, can use IdentityVector as well
    `111111`,
    `abc123`,
    `monkey`,
    `qwerty`,
    `testtest`,
    `dummytest`,
]
module.exports = NoCaptchaOnLoginPage
