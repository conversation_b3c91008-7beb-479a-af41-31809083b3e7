const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * PHP Object Injection Scanner Plugin
 */
class PHPObjectInj extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        this.vulnerabilityID = 'ID-php-object-injection'

        // Pre-compile all regex patterns for better performance
        this.patterns = {
            contentType: /multipart\/form-data|text\/(html|xml|plain)|application\/(json|xml|x-www-form-urlencoded)/i,
            defaultPage: /(?:The Apache Software Foundation)/i,
            excludeURL: /\.(?:txt|md|js|css|jpg|png|gif|ico|svg|woff|ttf|eot|map)$|\/(?:phpinfo\.php|info\.php|blog|docs?|learning|node_modules|vendor|assets|static|changelog\.txt)\/|logs?.txt|\/logs?\//i
        }

        // Pre-compile injection indicators
        this.injectionIndicators = injectionIndicators.map(pattern => new RegExp(pattern, 'gi'))
    }

    getAttackVectors() {
        return InjVectors
    }

    getAttackableEvents() {
        return [
            'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params', 'form-encoded-post', 'json-body', 'xml-post', 'post-body'
        ]
    }

    async performNetworkAttack(attack) {
        // Early return for excluded URLs
        if (this.patterns.excludeURL.test(attack.httpRequest.uri)) return false;

        const pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.PHPObjectInjFound) return false;

        return await super.performNetworkAttack(attack)
    }

    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.PHPObjectInjFound) return;

        // Early validation of response
        const contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '').toLowerCase();
        if (!this.patterns.contentType.test(contentType)) return;

        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', "");
        if (statusCode !== 200 && statusCode !== 500) return;

        if (attack.attackArea === 'original-crawler-request' && statusCode === 200 && this.patterns.excludeURL.test(attack.href)) return;

        const responseBody = _.get(attack, "result.resp.body", "").trim();
        if (!responseBody || responseBody.length < 10 || this.patterns.defaultPage.test(responseBody)) return;

        try {
            // Optimized pattern matching
            for (const pattern of this.injectionIndicators) {
                const match = responseBody.match(pattern);
                if (!match) continue;

                for (const foundMarker of match) {
                    // Escape special characters only once
                    const escapedMarker = foundMarker.replace(/[()]/g, '\\$&');
                    const contextRegex = new RegExp(`(?:\\S+\\s+){0,20}\\S*${escapedMarker}\\S*(?:\\s+\\S+){0,10}`, 'gi');
                    const errorContext = responseBody.match(contextRegex);

                    if (!errorContext) continue;

                    for (const context of errorContext) {
                        // Skip if match is inside HTML tag
                        if (context.includes('=')) {
                            let tagPattern = new RegExp(`(?:name|id|value|type|placeholder|content|property|href|rel|src|alt|title|media|class|data-[^=]+)=["'][^"']*${escapedMarker}`, 'gi');
                            if (tagPattern.test(context)) continue;
                        }

                        this.addVulnerabilitytoResult(
                            attack,
                            this.vulnerabilityID,
                            `Details: PHP object injection vulnerability detected via error message, Evidence: ${context.trim()}`
                        );
                        pluginDataForRequest.PHPObjectInjFound = true;
                        return;
                    }
                }
            }
        } catch (error) {
            return;
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID)
        if (vulnID === this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', 'param', [attack.href])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', 'param', [attack.href])
        }
    }
}

const injectionIndicators = [
    // 🔹 Unserialization & Object Handling Errors
    /(?:Fatal error|TypeError|Exception|Warning|Notice):\s*(?:Uncaught ?(?:error|Exception)?:\s*)?(?:Class 'NonExistentClass' not found in|Error in unserialize|Cannot call method|Class ['"][\w\\]+ (?:not found|has no unserializer|could not be converted|cannot be serialized)|Call to (?:undefined|private|protected) method|Cannot access (?:private|protected) property)/gi,

    // 🔹 Malformed Unserialization (Offset Errors)
    /unserialize\(\): Error at offset \d+ of \d+ bytes|Object of class [\w\\]+ could not be converted|Incomplete class: ['"]?[\w\\]+['"]?/gi,

    // 🔹 Incomplete Class Handling (Proof of POI Vulnerability)
    /__PHP_Incomplete_Class_Name|__PHP_Incomplete_Class Object/gi,

    // 🔹 Magic Method Invocation Errors (May Expose Vulnerabilities)
    /(?:\b__wakeup\(\)|\b__toString\(\)|\b__sleep\(\)|\b__invoke\(\)|\b__unset\(\)|\b__serialize\(\)|\b__unserialize\(\))/gi,

    // 🔹 Unsupported Operations on Objects
    /(?:Unsupported operand types|Return value of \w+ must be of type \w+|ReflectionException:|SplFileObject::|DateTime::__construct\(\):|ArrayObject::__construct\(\):|Invalid serialization data for)/gi,

    // 🔹 Security Restrictions in Newer PHP Versions
    /(?:Serialization of '.*?' is not allowed|Deserialization of '.*?' is not allowed due to security reasons|Cannot use object of type)/gi,

    // 🔹 Errors from PHP Internal Classes (SplFileObject, Reflection, DateTime, PDO)
    /(?:SplFileObject::__construct\(\): Failed to open stream:|ReflectionException:|DateTime::__construct\(\): Failed to parse time string|PDOException: SQLSTATE)/gi,

    // 🔹 Generic PHP Errors Related to Object Injection
    /(?:Call to undefined method [\w\\]+::\w+|Error while calling method on PDO object|Attempt to modify property of non-object)/gi,
];

const InjVectors = [
    `O:8:"stdClass":1:{s:4:"test";s:23:"HAIKU_MARKER_9_302e3135";}`,
    `O:9:"Exception":2:{s:7:"message";s:23:"HAIKU_MARKER_9_302e3135";s:9:"traceAsString";s:10:"TRACE_DATA";}`,
    `O:5:"Error":2:{s:7:"message";s:23:"HAIKU_MARKER_9_302e3135";s:4:"code";i:0;}`,
    `O:20:"NonExistentClass":1:{s:4:"test";s:23:"HAIKU_MARKER_9_302e3135";}`,
    `O:8:"stdClass":2:{s:4:"test";s:23:"HAIKU_MARKER_9_302e3135";s:8:"__wakeup";s:4:"test";}`,
    `O:8:"stdClass":1:{s:7:"private";s:23:"HAIKU_MARKER_9_302e3135";}`,
    `O:8:"stdClass":1:{s:6:"method";s:23:"HAIKU_MARKER_9_302e3135";}`,
    `O:8:"stdClass":1:{s:4:"type";i:"HAIKU_MARKER_9_302e3135";}`,
    `O:8:"stdClass":1:{s:4:"data";r:1;}`, // Recursive reference
    `O:8:"stdClass":1:{s:4:"data";s:23:"HAIKU_MARKER_9_302e3135";s:8:"__sleep";s:4:"test";}`,
    `O:8:"stdClass":2:{s:6:"__toString";s:23:"HAIKU_MARKER_9_302e3135";s:4:"data";s:5:"test";}`,
    `O:8:"stdClass":2:{s:8:"__invoke";s:23:"HAIKU_MARKER_9_302e3135";s:4:"data";s:5:"test";}`,
    `O:15:"SplFileObject":1:{s:11:"HAIKU_MARKER";s:23:"/etc/passwd";}`, // May trigger open() error
    `O:8:"ArrayObject":1:{s:9:"storage";s:23:"HAIKU_MARKER_9_302e3135";}`,
    `O:8:"DateTime":1:{s:4:"date";s:23:"HAIKU_MARKER_9_302e3135";}`, // Triggers DateTime errors
    `O:19:"ReflectionClass":1:{s:9:"name";s:23:"HAIKU_MARKER_9_302e3135";}`,
    `O:9:"PDO":0:{}`, // May trigger "could not be instantiated" errors
    `O:8:"stdClass":1:{s:8:"__unset";s:23:"HAIKU_MARKER_9_302e3135";}`
];

module.exports = PHPObjectInj