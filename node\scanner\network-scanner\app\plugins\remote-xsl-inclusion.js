const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Remote XSL plugin strategy:
 * Here for any url when attacked in postbody/uri query params with a file located in our server, if 
 * it responds with a expected response then will report it as vulnerable
 * 
 * RegEx Link - https://regex101.com/r/O2ZuMy/1
 */
class RemoteXSLInclusion extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-remote-xsl-inclusion'

    }

    getAttackVectors() {
        return xslVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginDataForRequest.rxsli) {
            return
        }

        let bodycheck = _.get(attack, 'result.resp.body')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let responseSize = _.get(attack, 'result.resp.httpResponse.headers[content-length]')
        //Check further only if status code is 200 ok
        if (statusCode == "200" && responseSize > 10) {
            //Report vuln only if below regex matches in the response obtained
            if (/version(.*\n?)(vendor)(.*\n?)(vendor\surl)/is.test(bodycheck)) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
                pluginDataForRequest.rxsli = true
                return

            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;

        }
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginDataForRequest.rxsli == true) {
            if (attack.attackArea == 'FormEncodedPost') {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, encodeURIComponent(attack.vector)]);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['version', 'vendor', 'vendor url']);

            }

            if (attack.attackArea == "UriQueryParameters") {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, ['uri', 'requestLine']);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ['uri', 'requestLine']);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['version', 'vendor', 'vendor url']);

            }
        }

    }
}


const xslVector = [
    `https://was.indusface.com/RXSLI.xsl`,
]


module.exports = RemoteXSLInclusion