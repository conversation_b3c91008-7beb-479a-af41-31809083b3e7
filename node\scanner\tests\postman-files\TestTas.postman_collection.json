{"info": {"_postman_id": "32a4009e-1efe-4fea-921f-81e51786cb74", "name": "TestTas", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Login_Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"<EMAIL>\",\n  \"password\": \"Test@1234\",\n  \"code\": \"\",\n  \"tasPortal\": true\n}"}, "url": {"raw": "https://testtas.indusface.com/wafportal/rest/loginService/login", "protocol": "https", "host": ["testtas", "indusface", "com"], "path": ["wafportal", "rest", "loginService", "login"]}}, "response": []}, {"name": "GetAllUser_Test", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "https://testtas.indusface.com/wafportal/rest/loginService/getallusers/1471", "protocol": "https", "host": ["testtas", "indusface", "com"], "path": ["wafportal", "rest", "loginService", "getallusers", "1471"]}}, "response": []}, {"name": "CreateUser_Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"customerId\": \"1471\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"userName\": \"<EMAIL>\",\r\n  \"fullName\": \"Demo Test Feb 09\",\r\n  \"twofa\": false,\r\n  \"role\":4\r\n}"}, "url": {"raw": "https://testtas.indusface.com/wafportal/rest/loginService/createuser", "protocol": "https", "host": ["testtas", "indusface", "com"], "path": ["wafportal", "rest", "loginService", "createuser"]}}, "response": []}]}