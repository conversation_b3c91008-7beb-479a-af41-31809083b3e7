const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
//const RE2 = require('re2')

class PWDinResponse extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID      
        this.vulnerabilityID = 'ID-password-in-response'
    }

    getAttackVectors() {
        return __AttackVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'json-body']
    }

    async performNetworkAttack(attack) {
        if (attack.originalRequest.httpRequest.method == 'POST') {
            let ReqBody = _.get(attack, "httpRequest.body")
            if (ReqBody.length > 10 && /\b(password|passwd?|pwd)=/i.test(ReqBody)) {
                return await super.performNetworkAttack(attack)
            }
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (attack.originalRequest.httpRequest.method == 'POST') {
            let Resbody = _.get(attack, 'result.resp.httpResponse.body', '')
            let ReqBody = _.get(attack, 'originalRequest.httpRequest.body', '')
            if (Resbody.length > 10 && (/\b(password|passwd?|pwd)=/i.test(ReqBody) || /\b(?:password|passwd?|pwd)(?:"|')? ?(?:=|:).{2,15}/i.test(Resbody))) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //if vuln already found then return
        if (pluginStorageScanScope.PasswordFoundinRes) {
            return
        }

        let Resbody = _.get(attack, 'result.resp.httpResponse.body')
        /* 
        Disabled for causing too many FPs
        if (Resbody.length > 10 && /\b(?:password|passwd?|pwd)(?:"|')? ?(?:=|:).{2,15}/i.test(Resbody)) {
            let currValue = Resbody.match(/\b(?:password|passwd?|pwd)(?:"|')? ?(?:=|:).{2,15}/gi)
            let details = {
                Credential: currValue.toString()
            }
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
            pluginStorageScanScope.PasswordFoundinRes = true
            return
        } */

        if (attack.pluginName == this.getName()) {
            let ReqBody = _.get(attack, 'originalRequest.httpRequest.body', '')
            //let contentTypeHeaderVal = _.get(attack, 'originalRequest.httpRequest.headers.Content-Type')
            let params = []
            if (/^{"/.test(ReqBody)) {
                let removesymbol = ReqBody.replace(/^{"|"}$/gm, '')
                params = removesymbol.split('","')
            }
            else {
                params = ReqBody.split('&')
            }
            let RegExp_pwd = /^(user(_|-))?(password|passwd?|pwd )=/i
            for (let i = 0; i < params.length; i++) {
                if (RegExp_pwd.test(params[i])) {
                    //let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
                    let currValue = params[i].split(/=|": ?"/gm)
                    if (/\b(password|passwd?|pwd)/i.test(currValue[0]) && currValue[1].length > 1 && currValue[1] != 'password' && !/^\*+$/.test(currValue[1]) && !/^[\w]{31,}$/.test(currValue[1])) {
                        // let caseless = currValue[1].toLowerCase();
                        const pattern = new RegExp("error.+?" + currValue[1], 'i');
                        if (Resbody.includes(currValue[1]) || pattern.test(Resbody)) {
                            let regex = new RegExp(".+" + currValue[1] + ".+", 'ig');
                            let val = Resbody.match(regex).join()
                            let details = {
                                Credential: `Password: ${currValue[1]} \n${val}`
                            }
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                            pluginStorageScanScope.PasswordFoundinRes = true
                            return
                        }
                    }
                }
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, ['password', 'passwd', 'pwd']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
    }
}

const __AttackVector = [
    `'`,
    `'"`,
    `;'or`,
    `;'or--`,
    `';\`--`,
]
module.exports = PWDinResponse