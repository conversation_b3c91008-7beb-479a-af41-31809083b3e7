let utils = require('../../ifc-utils.js')
const DateDiff = require('date-diff')
const URL = require('url').URL
const querystring = require('querystring');
const request = require('request');
const pluginName = 'external-resource'
const {
    session
} = require('electron'); // ask karthik for what this means
const fs = require('fs');
const HaikuUtils = require('../../../common/lib/haiku-utils');
const ssAPI = require('../../../common/lib/sooper-scheduler-api');
const defaultConfig = require('../../malware-scan-config');
const _ = require('lodash');
const logger = require('../../../common/lib/haiku-logger.js');
const { SCAN_TYPES } = require('../../../common/config/app-constants.js');

class ExternalResourceVisitStatus {
    constructor(urlInfo) {
        this.url = urlInfo.url;
        this.parentUrl = urlInfo.parentUrl;
        this.visited = false;
        this.uniqueKey = this.getUniqueKey();
        this.statusCode = 0; 
        this.error = '';
    }

    getUniqueKey() {
        return `GET|${this.url}`;
    }
}

class ExternalResource {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config;
        this.externalResource = new Map()

        if(this.config.scanType == SCAN_TYPES.MALWARE && this.config.malwareConfig && this.config.malwareConfig.proxy) {
            scanner.on('external-resource', this.onExternalResource.bind(this))
            scanner.on('scan-loop-done', this.onScanDone.bind(this))
        }
    }

    async onExternalResource(urlInfo) {
        let externalResourceVisitStatus = new ExternalResourceVisitStatus(urlInfo);
        let uniqueKey = externalResourceVisitStatus.uniqueKey;
        if(!this.externalResource.has(uniqueKey)) {
            this.externalResource.set(uniqueKey, externalResourceVisitStatus);
            await this.get(externalResourceVisitStatus);
        }
    }

    async get(externalResourceVisitStatus) {
        return new Promise((resolve, reject) => {
            try {
                request({
                    'url': externalResourceVisitStatus.url,
                    'method': "GET",
                    'proxy':this.config.malwareConfig.proxy,
                    headers: {
                        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36 Edg/109.0.1518.78",
                        "Scan-ID": this.config.scanId,
                        "ScanLog-ID": this.config.scanLogId,
                        "Scan-URL": externalResourceVisitStatus.url,
                        "Scan-URL-Method": "GET",
                        "Crawl-URL": externalResourceVisitStatus.parentUrl,
                        "Crawl-URL-Method": "GET",
                        "Date": new Date().toISOString()
                    },
                    strictSSL: false,
                    followAllRedirects: true,
                    rejectUnauthorized: false,
                  },function (error, response, body) {
                    externalResourceVisitStatus.statusCode = response ? response.statusCode : 0;
                    
                    if (!error && response.statusCode == 200) {
                      resolve(response);
                    }
                    else {
                        externalResourceVisitStatus.error = error.message;
                        resolve(error);
                    }
                  })
            } catch (err) {
                logger.info(`Unable to fetch external resource ${externalResourceVisitStatus.url}. Reason: ${err.toString()}`);
                resolve(err);
            }

            externalResourceVisitStatus.visited = true;
        });
    }

    onScanDone(reason, ret, logger = console) {
        this.scanner.externalResources = Array.from(this.externalResource.values());
        logger.log(`ExternalResource: scan done - ${reason}`)
    }

    static makeRequest(method, uri, headers, body = null) {
        return new Promise((resolve, reject) => {
            try {
                let options = {
                    'url': uri,
                    'method': method,
                    headers: headers,
                    strictSSL: false,
                    followRedirect: false,
                    followAllRedirects: false,
                    rejectUnauthorized: false
                }

                if (body) {
                    options.body = body;
                }

                request(options, function (error, response, body) {
                    resolve({
                        error,
                        response,
                        body
                    })
                })
            } catch (err) {
                logger.info(`ExternalResource:makeRequest error, Reason: ${err.toString()}`);
                resolve({
                    error: err
                });
            }
        });
    }
}

module.exports = ExternalResource