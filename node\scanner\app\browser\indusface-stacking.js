class IndusfaceStacking {
    constructor() {}

    name(short) {
        return short ? "IFC-Stacking" : "Indusface Stacking Context Plugin"
    }

    gotInterestingItems(rawItems, coreData) {
        let {clickableItems, inputItems, formItems, linkItems} = coreData
        
        for (var i = 0; i < rawItems.length; i++) {
            var rawItemArr = rawItems[i]
            var rawXpath = rawItemArr[0]
            var rawItem = rawItemArr[1]
            var stackIndex = this.getStackIndex(rawItem)
            var itemsToAnnotate = [clickableItems.get(rawXpath), inputItems.get(rawXpath), formItems.get(rawXpath), linkItems.get(rawXpath)]
            for (let itemToAnnotate of itemsToAnnotate) {
                if (itemToAnnotate) {
                    itemToAnnotate.stackIndex = stackIndex ? stackIndex : 0
                }
            }
        }
    }

    getStackIndex(a) {

        var ctxA = a;
        var za = this.zIndex(ctxA);
        var relativeCtxA = ctxA;

        // Finds the first ancestor with defined z-index, if any
        // The "shallowest" one is what matters, since it defined the most general
        // stacking context (affects all the descendants)
        while (ctxA && za === undefined) {
            ctxA = ctxA.parentElement
            za = this.zIndex(ctxA);
        }

        return za !== undefined ? za : this.indexInParent(relativeCtxA)
    }

    indexInParent(node) {
        var children = node.parentNode.childNodes;
        var num = 0;
        for (var i = 0; i < children.length; i++) {
            if (children[i] == node) return num;
            if (children[i].nodeType == 1) num++;
        }
        return -1;
    }

    zIndex(ctx) {

        var willChangeProperties = ['opacity']
        // Reference: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context
        // The root element (HTML) forms a stacking context
        if (!ctx || ctx === document.querySelector('html')) return;

        var computedStyle = window.getComputedStyle(ctx);

        var position = computedStyle['position'];
        var positioned = position !== 'static';
        var computedZindex = computedStyle['z-index'];

        // Positioned elements with a z-index value other than "auto" form a stacking context
        if (positioned && computedZindex !== 'auto') {
            // Return the numerical z-index value if the computedZindex is not 'auto'
            return +computedZindex;
        }

        // Reference: https://css-tricks.com/using-flexbox/
        // If element has a z-index other than auto and the parent is a flex container then a new stacking context is created
        if (computedZindex !== 'auto' && (['-webkit-box', '-ms-flexbox', '-webkit-flex', 'flex'].indexOf(window.getComputedStyle(ctx.parentNode).display) > -1)) {
            // Return the numerical z-index value if the computedZindex is not 'auto'
            return +computedZindex;
        }

        // Always create a new stacking context in case:

        // - the element has a position of 'fixed' and a z-index of auto
        var fixed = (position === "fixed" || position === "sticky");
        if (fixed) {
            return 0;
        }

        // - the element has opacity < 1 and is not positioned or has a z-index of auto
        var notOpaque = (computedStyle.opacity < 1);
        if (notOpaque) {
            return 0;
        }

        // Edge cases - experimental CSS properties
        // - the element has -webkit-overflow-scrolling set to 'touch'
        var overflowScrolling = (computedStyle.webkitOverflowScrolling === "touch"); // iOS specific
        if (overflowScrolling) {
            return 0;
        }

        // - a value for will-change with at least one of the specified properties
        if (computedStyle['willChange']) { // don't execute below in case browser doesn't support will-change
            var returnValue;
            computedStyle['willChange'].split(', ').some(function (value) {
                if (willChangeProperties.indexOf(value) > -1) {
                    returnValue = 0;
                    return true;
                }
            });
            return returnValue;
        }
    }

    init() {

    }
}

module.exports = IndusfaceStacking