/**
 * RE2 Replacement using native RegExp
 * 
 * This module provides a drop-in replacement for the RE2 module
 * using JavaScript's native RegExp. It maintains the same API
 * but without the memory issues associated with the RE2 native module.
 */

class RE2Replacement {
  /**
   * Constructor that mirrors RE2's constructor
   * @param {string|RegExp|array} pattern - Regex pattern, RegExp object, or array of patterns
   * @param {string|object} flags - Optional regex flags, either as string or object
   */
  constructor(pattern, flags) {
    // Handle combined patterns passed as an array of patterns or array.map().join()
    if (Array.isArray(pattern)) {
      pattern = pattern.join('|');
    }
    
    // If pattern is already a RegExp object, extract its pattern and combine with flags
    if (pattern instanceof RegExp) {
      const patternFlags = pattern.flags + (flags || '');
      pattern = pattern.source;
      if (patternFlags) {
        flags = patternFlags;
      }
    }
    
    // Handle flags as object (RE2 accepts this format)
    let flagsStr = '';
    if (typeof flags === 'object' && flags !== null) {
      if (flags.ignoreCase) flagsStr += 'i';
      if (flags.multiline) flagsStr += 'm';
      if (flags.global) flagsStr += 'g';
      if (flags.sticky) flagsStr += 'y';
      if (flags.unicode) flagsStr += 'u';
    } else if (typeof flags === 'string') {
      flagsStr = flags;
    }
    
    // Create the actual RegExp
    this.regexp = new RegExp(pattern, flagsStr);
    
    // Copy properties for compatibility
    this.source = this.regexp.source;
    this.flags = this.regexp.flags;
    this.global = this.regexp.global;
    this.ignoreCase = this.regexp.ignoreCase;
    this.multiline = this.regexp.multiline;
    this.unicode = this.regexp.unicode;
    this.sticky = this.regexp.sticky;
    this.lastIndex = 0;
  }

  /**
   * Test method - checks if pattern matches the string
   * @param {string} str - String to test
   * @returns {boolean} - Whether the pattern matches
   */
  test(str) {
    try {
      return this.regexp.test(str);
    } catch (err) {
      console.error('RegExp test error:', err);
      return false;
    }
  }

  /**
   * Exec method - executes a search for a match in a string
   * @param {string} str - String to search
   * @returns {Array|null} - A result array or null
   */
  exec(str) {
    try {
      return this.regexp.exec(str);
    } catch (err) {
      console.error('RegExp exec error:', err);
      return null;
    }
  }

  /**
   * Match method - returns matches of a string against the pattern
   * @param {string} str - String to match
   * @returns {Array|null} - Array of matches or null
   */
  match(str) {
    try {
      return str.match(this.regexp);
    } catch (err) {
      console.error('RegExp match error:', err);
      return null;
    }
  }

  /**
   * Replace method - replaces matches with a replacement string
   * @param {string} str - String to perform replacement on
   * @param {string|function} newSubStr - Replacement string or function
   * @returns {string} - String with replacements
   */
  replace(str, newSubStr) {
    try {
      return str.replace(this.regexp, newSubStr);
    } catch (err) {
      console.error('RegExp replace error:', err);
      return str;
    }
  }

  /**
   * Search method - searches for a match in a string
   * @param {string} str - String to search
   * @returns {number} - Index of match or -1 if not found
   */
  search(str) {
    try {
      return str.search(this.regexp);
    } catch (err) {
      console.error('RegExp search error:', err);
      return -1;
    }
  }

  /**
   * Split method - splits a string by matches of the pattern
   * @param {string} str - String to split
   * @param {number} limit - Optional limit on array length
   * @returns {array} - Array of split string sections
   */
  split(str, limit) {
    try {
      return str.split(this.regexp, limit);
    } catch (err) {
      console.error('RegExp split error:', err);
      return [str];
    }
  }

  /**
   * ToString method - returns the string representation of the regular expression
   * @returns {string} - String representation
   */
  toString() {
    return this.regexp.toString();
  }
  
  /**
   * Get/set lastIndex property (needed for g flag compatibility)
   */
  get lastIndex() {
    return this.regexp.lastIndex;
  }
  
  set lastIndex(value) {
    this.regexp.lastIndex = value;
  }
}

// Create a class with the same name as the original module for maximum compatibility
class RE2 extends RE2Replacement {}

// Export both the RE2Replacement class and the RE2 class alias
module.exports = RE2;
module.exports.RE2 = RE2;
module.exports.RE2Replacement = RE2Replacement; 