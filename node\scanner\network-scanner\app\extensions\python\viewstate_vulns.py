import json
import os,sys
from viewstate import ViewState

class ViewStateVulns(object):
    def __init__(self):
        self.vulndataList = []
     
    def read_json_stdin(self):
        #print("reading from stdin...")
        infile = sys.stdin
        jsonstr = json.load(infile)
        #print(jsonstr)
        if jsonstr != None:
            infile.close()
            return jsonstr['parameters']['viewstate']
        else:
            return None
    
    def write_json_stdout(self, outdata):
        #print("writing to stdout...")
        jsonData = json.dump(outdata, sys.stdout, sort_keys=True, indent=4)
        print( "\n")       
        sys.stdout.flush()
        #print( "\n")
    
    def detect_viewstate_vulns(self, vsdata):
        if len(vsdata) > 0:
            vs = ViewState(vsdata)
            if vs != None:
                vdata = {}
                vdata['haikuVulnId'] = "ID-unencrypted-viewstate"
                try:
                    decdata = vs.decode()
                    vdata['details'] = "ASP.NET ViewState data is decoded as: " + str(decdata)
                    vdata['vulnerabilityFound'] = True
                except Exception as e:
                    #print(str(e))
                    vdata['vulnerabilityFound'] = False
                    vdata['details'] = "ASP.NET ViewState data cannot be decoded and its encryted data is: " + vsdata
                self.vulndataList.append(vdata)
                
                vdata = {}
                vdata['haikuVulnId'] = "ID-viewstate-mac-disabled"
                try:
                    vs.decode()#permorning the decode first else vs.mac will always be none
                    decmac = vs.mac
                    if decmac==None:# this means mac is disabled and Tampering can be allowed by server refer STRIDE on https://docs.microsoft.com/en-us/archive/msdn-magazine/2010/july/security-briefs-view-state-security, for POC use this value="/wEPDwULLTE2MTY2ODcyMjkPFgIeCHBhc3N3b3JkBQlzd29yZGZpc2hkZA==" here mac is disabled
                        vdata['vulnerabilityFound'] = True
                        vdata['details'] = "ASP.NET ViewStateMac disabled and unencrypted viewstate value is: " + vsdata
                    else:
                        vdata['vulnerabilityFound'] = False
                        vdata['details'] = "ASP.NET ViewStateMac enabled using {} and its data is: ".format(decmac) + vsdata
                except Exception as e:
                    #print(str(e))
                    vdata['vulnerabilityFound'] = False
                    vdata['details'] = "ASP.NET ViewStateMac cannot be checked, data is either encryped or invalid string and data is: " + vsdata
                self.vulndataList.append(vdata)
        return self.vulndataList
        
if __name__== "__main__":
    vsvuln = ViewStateVulns()
    viewstatedata = vsvuln.read_json_stdin()
    vdata = vsvuln.detect_viewstate_vulns(viewstatedata)
    vsvuln.write_json_stdout(vdata)
