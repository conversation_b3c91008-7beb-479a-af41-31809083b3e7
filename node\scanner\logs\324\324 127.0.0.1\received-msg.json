{"msgType":"refresh-crawler-request-rpc","timestamp":"<PERSON><PERSON>, 03 Jun 2025 06:02:00 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"scan-finished","timestamp":"Wed, 11 Jun 2025 10:14:04 GMT","request":{"scanId":"324","scanner":"haiku"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:15:35 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:16:17 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:16:45 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:31:11 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:31:53 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:32:21 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:35:33 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:35:55 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:36:12 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:45:59 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:46:37 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:47:05 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:50:55 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:51:37 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:52:05 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:54:51 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:55:33 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Wed, 11 Jun 2025 10:56:19 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 08:17:40 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 08:18:31 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 08:19:18 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:31:58 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:32:20 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","admin","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:32:37 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","admin","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:34:54 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:35:16 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","admin","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:35:34 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","admin","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:40:41 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:41:07 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","admin","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:41:25 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","admin","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:49:07 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:49:31 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:51:16 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:51:58 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 12:52:28 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:00:09 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:00:48 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:01:24 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:06:38 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:07:20 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Test Payload"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:08:09 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["//*[@id=\"input\"]","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/test"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/test?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:13:05 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:13:47 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Submit"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:14:15 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:23:48 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:24:23 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Submit"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:26:00 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:33:06 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:33:48 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Submit"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:34:17 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:40:25 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:41:07 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Submit"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:41:43 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":1,"bookmarkId":"state=1|action=1","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\form-submit-action.js","args":["/html/body/form","/html/body/form/button","http://127.0.0.1:5000/"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?Submit&input"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:50:05 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":0,"action":0,"bookmarkId":"state=0|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},"rootActionCount":1},"appTranaKey":"GET-127.0.0.1:5000/?"}}{"msgType":"refresh-crawler-request-rpc","timestamp":"Thu, 12 Jun 2025 13:50:48 GMT","request":{"scanId":324,"scanlog_id":324,"scanlogId":324,"scanner":"haiku","crawlerBookmark":{"state":1,"action":0,"bookmarkId":"state=1|action=0","actionList":{"serliaziedName":"./datastructure\\action-list.js","args":["bookmark",[{"serliaziedName":"./datastructure\\action-list.js","args":["replay-ctx-1",[{"serliaziedName":"./datastructure\\action-list.js","args":["initial actions",[{"serliaziedName":"./datastructure\\load-action.js","args":["[initial]","http://127.0.0.1:5000/","initial, mustRunAtInit: true"]}],"seq",true]}],"seq",false]},{"serliaziedName":"./datastructure\\action-list.js","args":["core-heuristic generated",[{"serliaziedName":"./datastructure\\type-action.js","args":["/html/body/form/input","defaultText","input"]},{"serliaziedName":"./datastructure\\click-action.js","args":["/html/body/form/button","Submit"]}],"seq",false]}],"seq",false]},"rootActionCount":2},"appTranaKey":"GET-127.0.0.1:5000/?input"}}