const debug = require('debug')('AutocompleteHTMLForm')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class AutocompleteHTMLForm extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-autocomplete-html-form';
    }

    /* wantProcessAttackResponse(originalRequest) {
        // if response code is error then return
        if (originalRequest.result.resp.httpResponse.err) {
            return false
        }

        // check for forms in response body, if found then call processAttackResponse
        let ResBody = _.get(originalRequest, "result.resp.body")
        let regexp = /<form.+?method=.+?<\/form>/gis
        let formtag = []
        formtag = ResBody.match(regexp)
        if (formtag.length > 0) {
            if (args_regexp.test(formtag) || /type\s*=\s*["|'](?:email|password)/i.test(formtag)) {
                return true
            }
        }
        return false
    } */

    processAttackResponse(originalRequest) {
        let pluginDataForRequest = this.getPluginScopedStore(originalRequest)
        //if vuln detected for a req then return
        if (pluginDataForRequest.autocompleteVulnFound) {
            return
        }

        /**
         * <input type="password" id="passw" name="passw" style="width: 150px;">
         * <input type="email" name="Adminuser[email]" id="AdminuserUsername" autocomplete="off">
         * <td align="left" style="padding-top:16px;"><input type="password" class="hasDatepicker" id="txtPassword" name="Password" maxlength="30"></td>
         * <input type='password' name='password' id='account-password' size='25' value='' />
         **/

        let ResBody = _.get(originalRequest, "result.resp.body")
        if (/\$\(["|']form["|']\)\.attr\(["|']autocomplete["|'],\s*["|']off["|']/i.test(ResBody)) { //is a jQuery statement used to disable the browser's autocomplete feature for a form.
            return
        }

        let regexp = /<form.+?method=.+?<\/form>/gis
        // let formtag = []
        let formtag = ResBody.match(regexp)
        if (formtag != null && formtag.length > 0) {
            if (args_regexp.test(formtag) || /type\s*=\s*["|'](?:email|password)/i.test(formtag)) {
                let details = []
                // let formtag = []
                try {
                    // formtag = ResBody.match(regexp)
                    // if (formtag.length > 0) {
                    for (let tag of formtag) {
                        if (details.length > 0) { break }
                        if (tag.length > 20 && !/<\s*form[^>]+?autocomplete\s*=\s*["']off/i.test(tag)) {
                            if (args_regexp.test(tag)) {
                                let inputtag = tag.match(args_regexp)
                                if (inputtag != null) {
                                    for (let value of inputtag) {
                                        if (value.length > 0 && !/autocomplete\s*=\s*["']off/i.test(value) && /type\s*=\s*['"]text/i.test(value)) {
                                            details.push({ result: value })
                                            if (details.length > 2) { break }
                                        }
                                    }
                                }
                            }
                            if (/type\s*=\s*["'](?:email|password)/i.test(tag) && details.length == 0) {
                                let inputtag = tag.match(/<input[^>]+?type\s*=\s*["'](?:email|password)[^>]+?>/gi)
                                if (inputtag != null) {
                                    for (let value of inputtag) {
                                        if (value.length > 0 && /type\s*=\s*["'](?:email|password)/i.test(value) && !/autocomplete\s*=\s*["']off/i.test(value)) {
                                            details.push({ result: value })
                                            if (details.length > 2) { break }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (details.length > 0) {
                        this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, details)
                        pluginDataForRequest.autocompleteVulnFound = true
                    }
                }
                catch (e) {
                    return
                }
            }
        }
    }
}
const args_regexp = /<(?:td|label)>[^<>]*\b(?:(?:Credit|Debit|Identification|Insurance|Social Security|Prepaid|Gift|Loyalty|Access|ID|License|Passport|Resident Permit|Employment Authorization|policy|Aadhaar|ATM PIN|PAN|cards?|account|ac)([-_ ]Cards?)?[-_ ]?(number|no|card)s?|mobile|contact|phone|[\w_\- ]+?id|blood_group|pan|email|vehEng|vehChs|(?:user|emp|employee)[-_ ]?(?:id|name|Input)|uname|uid|user|(?:account|ac)[-_ ]?(id)?|password|passwd|pwd|api[-_]?key|ProposerPAN|kyc_id|customerId|ATM PIN).+?<input[^>]+?>/gis

module.exports = AutocompleteHTMLForm
/*
//check if autocomplete ON for password fields
let vulnDetails = this.checkIfAutocompleteOn(originalRequest);
 
/*@scope of improvement : check each elements for the autocomplete field and 
to its ancestor level if its not defined
if (vulnDetails.length > 0)
    this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, vulnDetails)
 
 
/* checkIfAutocompleteOn(originalRequest) {
let res = [];
 
let $ = _.get(originalRequest, 'result.resp.httpResponse.cheerio');
let forms = $("form");
//check if form or any of the password fields are having autocomplete value as on
if (forms.length > 0) {
 
    //check any of the password fields are having autocomplete property ON or undefined
    let pwdFields = $('input[type=password]');
    if (pwdFields.length > 0) {
        pwdFields.each(function (index) {
            if ($(this).attr('autocomplete')) {
                if ($(this).attr('autocomplete') == 'on') {
                    if ($(this).attr('id')) {
                        var element_Id = $(this).attr('id');
                    } else if ($(this).attr('name')) {
                        var element_Id = $(this).attr('name');
                    }
                    res.push(element_Id);
                    /* .push({
                        elementName: element_Id,
                        elementType: "element_Id",
                    })  *//* 
}//else check if any of the ancestor form fiels is having the property value as ON
} else if ($(this).closest("form").attr('autocomplete')) {
if ($(this).closest("form").attr('autocomplete') == 'on') {

if ($(this).attr('id')) {
var element_Id = $(this).attr('id');
} else if ($(this).attr('name')) {
var element_Id = $(this).attr('name');
}
res.push(element_Id);
/* res.push({
elementName:element_Id,
elementType: "form",
})*//* 
                                                                        }
                                                                        } else {//if the autocomplete is not efined
                                                                        
                                                                        if ($(this).attr('id')) {
                                                                          var element_Id = $(this).attr('id');
                                                                        } else if ($(this).attr('name')) {
                                                                          var element_Id = $(this).attr('name');
                                                                        }
                                                                        
                                                                        res.push(element_Id);
                                                                        /*res.push({
                                                                          elementName: element_Id,
                                                                          elementType: "form",
                                                                        })*//* 
}
})
 
 
}
 
}
return res;
} */

