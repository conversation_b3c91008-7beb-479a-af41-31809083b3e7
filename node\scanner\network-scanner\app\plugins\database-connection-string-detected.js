const NetworkAttack = require('./network-attack');
const _ = require('lodash');

const dbconnection = [

    
    /\b**************************************************{1,3}\.\d{1,3}\.\d{1,3}|192\.168\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2[0-9]|3[01])\.\d{1,3}\.\d{1,3})([0-9]{1,3}\.){3}[0-9]{1,3}:([0-9]+):([a-zA-Z0-9]+)\b/,

    
    /\bjdbc:(db2|cassandra|mongodb):\/\/(?!127\.0\.0\.1|localhost|10\.\d{1,3}\.\d{1,3}\.\d{1,3}|192\.168\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2[0-9]|3[01])\.\d{1,3}\.\d{1,3})([0-9]{1,3}\.){3}[0-9]{1,3}:\d+\/[a-zA-Z0-9\-._]+\b/,

    
    /\bjdbc:(mariadb|postgresql|sqlserver|mysql):\/\/(?!127\.0\.0\.1|localhost|10\.\d{1,3}\.\d{1,3}\.\d{1,3}|192\.168\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2[0-9]|3[01])\.\d{1,3}\.\d{1,3})([0-9]{1,3}\.){3}[0-9]{1,3}(:\d{1,5})?\b/,

    
    /\bjdbc:(mariadb|postgresql|sqlserver|mysql):\/\/(?!127\.0\.0\.1|localhost|10\.\d{1,3}\.\d{1,3}\.\d{1,3}|192\.168\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2[0-9]|3[01])\.\d{1,3}\.\d{1,3})([0-9]{1,3}\.){3}[0-9]{1,3}(:\d{1,5})?(\/[a-zA-Z0-9]+)?[?;](database=[a-zA-Z0-9]+;)?(user=[a-zA-Z0-9]+;)?(password=[a-zA-Z0-9]+;)?(user=[a-zA-Z0-9]+&password=[a-zA-Z0-9]+)?\b/
]

class DBConnectionStringDetected extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner);
        this.vulnerabilityID = 'ID-database-connection-string-detected';

        // Using PascalCase for regexPattern
        this.regexPattern = new RegExp(dbconnection.map(v => v.source).join('|'), 'ig');
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        this.detectDBConnectionStrin(originalRequest);
    }

    detectDBConnectionStrin(originalRequest) {
        let bodyCheck = _.get(originalRequest, 'result.resp.body');
        
        // Ensure the condition makes sense based on your application logic
        if (originalRequest.result.resp.httpResponse.err) {
            return;
        }
        //Below condition added to remove the FPs 
        if (/\/blog\/|\/docs\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i.test(originalRequest.href)) {
            return;
        }        
    
        // Detect Database connection string using the regex pattern
        let matches = [];
        let match;
        while ((match = this.regexPattern.exec(bodyCheck)) !== null) {
            matches.push(`${match[0]}`);
        }
    
        if (matches.length > 0) {
            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, matches.join('\n'));
        }
    }
}

module.exports = DBConnectionStringDetected;