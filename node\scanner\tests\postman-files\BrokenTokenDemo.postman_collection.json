{"info": {"_postman_id": "a1b2748a-a8b4-4eeb-a560-cceeebe327bd", "name": "Broken Token Demo", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Auth1", "event": [{"listen": "test", "script": {"exec": ["/* HAIKU_START */\r", "{\r", "    haikuEvents: {\r", "        onOriginalResponse: (args, haiku)=> {\r", "            let resp = args.resp;\r", "            if(resp.statusCode == 200) {\r", "                let jsonData = JSON.parse(resp.body);\r", "                haiku.setVar(\"token1\", jsonData.message.token);\r", "            }\r", "        }\r", "    }\r", "}\r", "\r", "/* HAIKU_END */\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"jonathanmh\",\r\n    \"password\": \"%2yx4\",\r\n    \"id\": \"1\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{URL}}/login", "protocol": "http", "host": ["{{URL}}"], "path": ["login"]}}, "response": []}, {"name": "Auth2", "event": [{"listen": "test", "script": {"exec": ["/* HAIKU_START */\r", "{\r", "    haikuEvents: {\r", "        onOriginalResponse: (args, haiku)=> {\r", "            let resp = args.resp;\r", "            if(resp.statusCode == 200) {\r", "                let jsonData = JSON.parse(resp.body);\r", "                haiku.setVar(\"token2\", jsonData.message.token);\r", "            }\r", "        }\r", "    }\r", "}\r", "\r", "/* HAIKU_END */\r", "\r", "\r", ""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"name\": \"jonathan<PERSON>h\",\r\n    \"password\": \"%2yx4\",\r\n    \"id\": \"Auth2\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{URL}}/login", "protocol": "http", "host": ["{{URL}}"], "path": ["login"]}}, "response": []}, {"name": "Improper <PERSON>", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["/* HAIKU_START */\r", "{\r", "    preq: \"Auth1,Auth2\",\r", "    onNewAttackableRequest: (req, haiku)=> {\r", "        haiku.saveHeader(req, \"Authorization\");\r", "    },\r", "    onBeforeAttack: (req, haiku)=> {\r", "        haiku.restorH<PERSON>er(req, \"Authorization\");\r", "    },\r", "    haikuEvents: {\r", "        onOriginalResponse: (args, haiku)=> {\r", "            let resp = args.resp;\r", "            if(resp.statusCode == 200) {\r", "                haiku.addVulnerabilityToResult(args, \"ID-broken-token\", 'Older JWT token does not expire when new JWT token is generated.');\r", "            }\r", "        }\r", "    }\r", "}\r", "/* HAIKU_END */"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token1}}", "type": "text"}], "url": {"raw": "http://{{URL}}/secret", "protocol": "http", "host": ["{{URL}}"], "path": ["secret"]}}, "response": []}, {"name": "Insecure Direct Object References", "event": [{"listen": "test", "script": {"exec": ["/* HAIKU_START */\r", "{\r", "    preq: \"Auth1\",\r", "    haikuEvents: {\r", "        onNewAttackableRequest: (req, haiku)=> {\r", "            haiku.saveHeader(req, \"Authorization\");\r", "        },\r", "        onBeforeAttack: (req, haiku)=> {\r", "            haiku.restorH<PERSON>er(req, \"Authorization\");\r", "        },\r", "        onOriginalResponse: (args, haiku)=> {\r", "            haiku.setAttackArea(args, 'manually reported vuln', [\"UriQueryParameters\"]);\r", "            haiku.setVectors(args, 'manually reported vuln', [\"test\"]);\r", "        },\r", "        onResponse: (args, haiku)=> {\r", "            let resp = args.resp;\r", "            if(resp.statusCode == 200) {\r", "                let jsonData = JSON.parse(resp.body);\r", "                if(jsonData.message && jsonData.message.name != \"jonathanmh\") {\r", "                    haiku.addVulnerabilityToResult(args, \"ID-idor-attack\", 'Can access user details using other user token.');\r", "                }\r", "            }\r", "        }\r", "    }\r", "}\r", "\r", "/* HAIKU_END */"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token1}}", "type": "text"}], "url": {"raw": "http://{{URL}}/user?name=jonathanmh", "protocol": "http", "host": ["{{URL}}"], "path": ["user"], "query": [{"key": "name", "value": "j<PERSON><PERSON><PERSON><PERSON>"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["/*console.info();*/", "/* HAIKU_START */", "{", "    haikuEvents: {", "        onOriginalResponse: (args, haiku)=> {", "            haiku.setAttackArea(args, 'manually reported vuln', [\"UriQueryParameters\"]);", "            haiku.setVectors(args, 'manually reported vuln', [\"test\"]);", "        }", "    }", "}", "", "/* HAIKU_END */"]}}], "variable": [{"key": "token", "value": ""}, {"key": "URL", "value": "127.0.0.1:4000"}, {"key": "token1", "value": ""}, {"key": "token2", "value": ""}, {"key": "name", "value": "j<PERSON><PERSON><PERSON><PERSON>"}]}