const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')

class EmailidFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-email-id-found'
        this.emailPattern = new RE2(/[\w\.\-\+]+@[A-Z0-9][A-Z0-9.\-]{0,61}[A-Z0-9]\.[A-Z.]{2,6}/i)
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override 
       
     * Disable wantProcessAttackResponse improve coverage  
    wantProcessAttackResponse(originalRequest) {
        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {
            return true
        }
        return false
    }*/

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.emailidFound) { return }
        let body = _.get(attack, 'result.resp.body')
        let res = body.match(this.emailPattern)
        if (res) {
            res = _.uniq(res)
            res = res.filter((el) => { return !(el.indexOf("<EMAIL>") >= 0 || el.endsWith(".jpg") || el.endsWith(".png") || el.endsWith(".jpeg")) })
        }
        if (res && res.length > 0) {
            //this.addVulnerabilitytoResult(attack, this.vulnerabilityID, res)
            this.checkBodyForVuln(attack, this.emailPattern, this.vulnerabilityID)
            pluginDataForRequest.emailidFound = true
        }
    }
}

module.exports = EmailidFound