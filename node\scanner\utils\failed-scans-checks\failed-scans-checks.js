var fs = require('fs');
const path = require('path');
const Minimist = require('minimist');
_ = require('lodash');
const ssAPI = require('../../common/lib/sooper-scheduler-api');
const logger = require('../../common/lib/haiku-logger');
const s3Utils = require('../../common/lib/s3-utils')
const HaikuUtils = require('../../common/lib/haiku-utils');
const request = require('request');
const { log } = require('console');
const hostFileQueue = require('../../common/lib/host-file-queue')

logger.setMetadata({
    haikuProcess: 'failed-scan-checks'
});

let utilMachineApiEndPoint = process.env['UTIL_MACHINE_API_ENDPOINT'] || 'http://localhost:3000';

class FailedScansChecks {
    constructor(failedScansFile) {
        this.failedScansFile = failedScansFile;
    }

    //read file and collect all fields : ServiceId	url	scanlogId	starttime	endtime	scanfeasible_msg	scanminutes	vulnerabilitiesfound
    async loadInputFileContents() {
        let failedScansArray = [];

        // read file and collect all fields : ServiceId	url	scanlogId	starttime	endtime	scanfeasible_msg	scanminutes	vulnerabilitiesfound
        await new Promise((resolve, reject) => {
            fs.readFile(this.failedScansFile, 'utf8', (err, data) => {
                if (err) {
                    console.error(err);
                    reject(err);
                }
                let rows = data.split('\n');
                let header = rows[0].trim('\n').split(',');
                for (let i = 1; i < rows.length; i++) {
                    let row = rows[i].replace(/""/g, '"');
                    if (row.length >= 1) {
                        row = row.split(',');
                        console.log(row);
                        if (row[5].includes('errorCode'))
                            row[5] = "errorCode";
                        let obj = {};
                        for (let j = 0; j < header.length; j++) {
                            obj[header[j]] = row[j];
                        }
                        failedScansArray.push(obj);
                    }
                }
                resolve(failedScansArray);
            });
        });

        return failedScansArray;
    }

    //write method which loops failedScansArray and checks scanfeasible_msg and sorts it into arrays where scanfeasible_msg is same contains substring from [timed.out,microsoft,chromewebdata,got failure in loading scan]
    async groupByScanFeasibleMsg(failedScansArray) {
        // loop through failedScansArray
        let timedOutArr = [];
        let microsoftArr = [];
        let chromewebdataArr = [];
        let gotFailureInLoadingScanURL = [];
        let scanURLRedirectsArr = [];
        let otherArr = [];
        for (let i = 0; i < failedScansArray.length; i++) {
            console.log(i);
            // check scanfeasible_msg and sort it into arrays where scanfeasible_msg is same contains substring from [timed.out,microsoft,chromewebdata,got failure in loading scan]
            try {
                if (failedScansArray[i].scanfeasible_msg && failedScansArray[i].scanfeasible_msg.includes("timed.out")) {
                    failedScansArray[i].msgKey = "timed.out";
                    timedOutArr.push(failedScansArray[i]);
                } else if (failedScansArray[i].scanfeasible_msg && failedScansArray[i].scanfeasible_msg.includes("microsoft")) {
                    failedScansArray[i].msgKey = "microsoft";
                    microsoftArr.push(failedScansArray[i]);
                } else if (failedScansArray[i].scanfeasible_msg && failedScansArray[i].scanfeasible_msg.includes("chromewebdata")) {
                    failedScansArray[i].msgKey = "chromewebdata";
                    chromewebdataArr.push(failedScansArray[i]);
                } else if (failedScansArray[i].scanfeasible_msg && failedScansArray[i].scanfeasible_msg.includes("errorCode")) {
                    failedScansArray[i].msgKey = "got failure in loading scan";
                    gotFailureInLoadingScanURL.push(failedScansArray[i]);
                } else if (failedScansArray[i].scanfeasible_msg && failedScansArray[i].scanfeasible_msg.includes("scan URL redirects")) {
                    failedScansArray[i].msgKey = "scan URL redirects";
                    scanURLRedirectsArr.push(failedScansArray[i]);
                } else {
                    failedScansArray[i].msgKey = "others";
                    otherArr.push(failedScansArray[i]);
                }
            } catch (error) {
                console.error(error);
            }
        }

        // merge all arrays into one object
        let groupedByScanFeasibleMsg = {
            timedOutArr: timedOutArr,
            microsoftArr: microsoftArr,
            chromewebdataArr: chromewebdataArr,
            gotFailureInLoadingScanURLArr: gotFailureInLoadingScanURL,
            scanURLRedirectsArr: scanURLRedirectsArr,
            otherArr: otherArr
        };

        return groupedByScanFeasibleMsg;
    }

    //write methode which analyzes groupedByScanFeasibleMsg 
    async analyzeMicrosoftArr(groupedByScanFeasibleMsg) {
        let failedScansUpdatedArray = [];
        let keys = Object.keys(groupedByScanFeasibleMsg);

        for (let index = 0; index < groupedByScanFeasibleMsg['microsoftArr'].length; index++) {
            let element = groupedByScanFeasibleMsg['microsoftArr'][index];
            let scanfeasible_msg = element.scanfeasible_msg;
            let remarkObject = {};
            try {
                // check url contains errorCode or errorDescription includes
                if (scanfeasible_msg.includes("microsoft") || scanfeasible_msg.includes("microsoftonline")) {
                    remarkObject = { "remark": "Resolved", "statusCode": "200" };
                }
            } catch (error) {
                console.error(error);
            }
            element.remark = remarkObject;
            failedScansUpdatedArray.push(element);
        }
        return failedScansUpdatedArray;
    }

    async analyzeGotFailureInLoadingScanURLArr(groupedByScanFeasibleMsg) {
        let failedScansUpdatedArray = [];
        let keys = Object.keys(groupedByScanFeasibleMsg);

        for (let index = 0; index < groupedByScanFeasibleMsg['gotFailureInLoadingScanURLArr'].length; index++) {
            let element = groupedByScanFeasibleMsg['gotFailureInLoadingScanURLArr'][index];
            let scanfeasible_msg = element.scanfeasible_msg;
            let remarkObject = {};

            if (scanfeasible_msg.includes("errorCode") || scanfeasible_msg.includes("errorDescription")) {
                remarkObject = { "remark": "please connect support and ask client to solve this issue" };
            }

            element.remark = remarkObject;
            failedScansUpdatedArray.push(element);
        }
        return failedScansUpdatedArray;
    }

    async analyzeSanURLRedirectsArr(groupedByScanFeasibleMsg) {
        let failedScansUpdatedArray = [];
        let keys = Object.keys(groupedByScanFeasibleMsg);

        for (let index = 0; index < groupedByScanFeasibleMsg['scanURLRedirectsArr'].length; index++) {
            let element = groupedByScanFeasibleMsg['scanURLRedirectsArr'][index];
            let scanfeasible_msg = element.scanfeasible_msg;
            let remarkObject = {};
            try {
                // check url contains errorCode or errorDescription includes
                if (scanfeasible_msg.includes("scan URL redirects to not allowed crawl location: http") || scanfeasible_msg.includes("scan URL redirects to not allowed crawl location: http")) {
                    remarkObject = { "remark": "Connect with support to update crawler configuration : allow third party crawl url's ", statusCode: "" };
                }
            } catch (error) {
                console.error(error);
            }
            element.remark = remarkObject;
            failedScansUpdatedArray.push(element);
        }
        return failedScansUpdatedArray;
    }

    async makeRequest(element) {
        return await new Promise((resolve, reject) => {
            request({
                'url': element.url,
                'method': "GET",
                headers: {
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.78",
                    "Date": new Date().toISOString()
                },
                timeout:15000,
                strictSSL: false,
                followAllRedirects: false,
                rejectUnauthorized: false,
            }, (error, response, body) => {
                if (!error) {
                    // console.log(response);
                    resolve({ "remark": "Site is accessible", "statusCode": response.statusCode });
                } else {
                    console.log(error);
                    resolve({ "remark": "Site is not accessible" });
                }
            });
        });
    }

    async analyzeTimedOutArr(groupedByScanFeasibleMsg) {
        let failedScansUpdatedArray = [];
        let keys = Object.keys(groupedByScanFeasibleMsg);

        for (let index = 0; index < groupedByScanFeasibleMsg['timedOutArr'].length; index++) {
            let element = groupedByScanFeasibleMsg['timedOutArr'][index];
            let scanfeasible_msg = element.scanfeasible_msg;
            let remarkObject = { remark: '', statusCode: '' };
            if (scanfeasible_msg.includes("timed.out")) {
                element.url = element.url.replace(/"/g, '');
                let url = new URL(element.url);
                let proxyPassDetails = await HaikuUtils.getProxyPass(url.host, {}, logger);
                console.log(proxyPassDetails);
                await hostFileQueue.updateHostFileWithQueue(url.host, false, {}, proxyPassDetails, logger);
                console.log(element.url);

                if (!proxyPassDetails[url.host].ip) {
                    element.remark = { "remark": "Origin IP not defined" }
                    //Check site accessibility without proxy/origin ip
                    let response = await this.makeRequest(element);
                    remarkObject.remark = "Public access : " + response.remark;
                    remarkObject.statusCode = "Public " + (response.statusCode || '');
                }
                else {
                    //Check site accessibility over proxy/origin ip
                    let response = await this.makeRequest(element);
                    remarkObject.remark = "Origin IP " + proxyPassDetails[url.host].ip + " access : " + response.remark;
                    remarkObject.statusCode = "Origin IP " + (response.statusCode || '');
                    //Over origin ip access Update remarks or column data

                    Object.values(proxyPassDetails).forEach(obj => {
                        obj.ip = false;
                    });

                    //Check site accessibility without proxy/origin ip
                    await hostFileQueue.updateHostFileWithQueue(url.host, false, {}, proxyPassDetails, logger);
                    response = await this.makeRequest(element);
                    remarkObject.remark += ";Public access : " + response.remark;
                    remarkObject.statusCode = "Public " + (response.statusCode || '');
                    //Over without access Update remarks or column data
                }

            }
            element.remark = remarkObject;
            failedScansUpdatedArray.push(element);
        }
        return failedScansUpdatedArray;
    }

    async analyzeChromewebdataArr(groupedByScanFeasibleMsg) {
        let failedScansUpdatedArray = [];
        let keys = Object.keys(groupedByScanFeasibleMsg);

        for (let index = 0; index < groupedByScanFeasibleMsg['chromewebdataArr'].length; index++) {
            let element = groupedByScanFeasibleMsg['chromewebdataArr'][index];
            let scanfeasible_msg = element.scanfeasible_msg;
            let remarkObject = { remark: '', statusCode: '' };
            if (scanfeasible_msg.includes("chromewebdata") || scanfeasible_msg.includes("chrome-error")) {

                // remove "" from url
                element.url = element.url.replace(/"/g, '');

                let url = new URL(element.url);
                let proxyPassDetails = await HaikuUtils.getProxyPass(url.host, {}, logger);
                console.log(proxyPassDetails);
                await hostFileQueue.updateHostFileWithQueue(url.host, false, {}, proxyPassDetails, logger);
                console.log(element.url);

                if (!proxyPassDetails[url.host].ip) {
                    element.remark = { "remark": "Origin IP not defined" }
                    //Check site accessibility without proxy/origin ip
                    let response;
                    try {
                        response = await this.makeRequest(element);
                    } catch (error) {
                        console.log(error)
                    }
                    remarkObject.remark = "Public access : " + response.remark;
                    remarkObject.statusCode = "Public " + (response.statusCode || '');
                }
                else {
                    //Check site accessibility over proxy/origin ip
                    let response = await this.makeRequest(element);
                    remarkObject.remark = "Origin IP " + proxyPassDetails[url.host].ip + " access : " + response.remark;
                    //Over origin ip access Update remarks or column data                  
                    remarkObject.statusCode = "Origin IP " + (response.statusCode || '');

                    Object.values(proxyPassDetails).forEach(obj => {
                        obj.ip = false;
                    });

                    //Check site accessibility without proxy/origin ip
                    await hostFileQueue.updateHostFileWithQueue(url.host, false, {}, proxyPassDetails, logger);
                    response = await this.makeRequest(element);
                    remarkObject.remark += ";Public access : " + response.remark;
                    remarkObject.statusCode = "Public " + (response.statusCode || '');
                    //Over without access Update remarks or column data
                }

            }
            element.remark = remarkObject;
            failedScansUpdatedArray.push(element);
        }
        return failedScansUpdatedArray;
    }

    async analyzeOtherArr(groupedByScanFeasibleMsg) {
        let failedScansUpdatedArray = [];
        let keys = Object.keys(groupedByScanFeasibleMsg);

        for (let index = 0; index < groupedByScanFeasibleMsg['otherArr'].length; index++) {
            let element = groupedByScanFeasibleMsg['otherArr'][index];
            let scanfeasible_msg = element.scanfeasible_msg;
            let remarkObject = { remark: '' };
            try {
                // check url contains errorCode or errorDescription includes
                remarkObject = { "remark": "Need exrtra efforts" };
            } catch (error) {
                console.error(error);
            }
            element.remark = remarkObject;
            failedScansUpdatedArray.push(element);
        }
        return failedScansUpdatedArray;
    }
}

// --- Main code
let argv = Minimist(process.argv.slice(2), {
    boolean: true,
    alias: {
        f: "oob-vuln-file"
    }
})

// --- Main code
async function main() {
    console.log("Starting failed scans checks");
    //create instance of failed scans checks which ready file argv['failedScansFile']
    let failedScansChecks = new FailedScansChecks(argv['failedScansFile']);
    let final = [];
    //call loadInputFileContents
    let failedScansArray = await failedScansChecks.loadInputFileContents();
    let groupedByScanFeasibleMsg = await failedScansChecks.groupByScanFeasibleMsg(failedScansArray);
    let reponse = [];
    reponse = await failedScansChecks.analyzeGotFailureInLoadingScanURLArr(groupedByScanFeasibleMsg);
    final = final.concat(reponse);
    reponse = await failedScansChecks.analyzeMicrosoftArr(groupedByScanFeasibleMsg);
    final = final.concat(reponse);
    reponse = await failedScansChecks.analyzeSanURLRedirectsArr(groupedByScanFeasibleMsg);
    final = final.concat(reponse);
    reponse = await failedScansChecks.analyzeChromewebdataArr(groupedByScanFeasibleMsg);
    final = final.concat(reponse);
    reponse = await failedScansChecks.analyzeTimedOutArr(groupedByScanFeasibleMsg);
    final = final.concat(reponse);
    reponse = await failedScansChecks.analyzeOtherArr(groupedByScanFeasibleMsg, () => {

    });
    final = final.concat(reponse.map(element => {
        let remarkObject = {};
        try {
            // check url contains errorCode or errorDescription includes
            remarkObject = { "remark": "Need exrtra efforts", "statusCode": element.statusCode };
        } catch (error) {
            console.error(error);
        }
        element.remark = remarkObject;
        return element;
    }));

    // loop through final and replace remark with remark.remark and update final
    final = final.map(element => {

        if (element.remark.remark) {
            let remarkObject = element.remark;
            element.remark = remarkObject.remark;
            if (remarkObject.statusCode)
                element.statusCode = remarkObject.statusCode.replace("Public ", "");
            else
                element.statusCode = "";
        }
        return element;
    });

    await writeToFile(final);
}

async function writeToFile(final) {
    // write final json array to csv file final.csv
    // having object keys as header and values as rows without using any library

    // from final json array collect only keys = ServiceId,	url	,scanlogId ,remark ,statusCode and write to csv file
    let header = ['ServiceId', 'url', 'scanlogId', 'remark', 'statusCode'];
    let csv = '';
    csv += header.join(',') + '\n';
    for (let i = 0; i < final.length; i++) {
        let row = [];
        for (let j = 0; j < header.length; j++) {
            row.push(final[i][header[j]]);
        }
        csv += row.join(',') + '\n';
    }
    fs.writeFileSync('final.csv', csv);
    console.log('The CSV file was written successfully');
}

if (argv['failedScansFile']) {
    // main / module exports based on command line args
    (async function (params) {
        await main();
    })();
} else {
    module.exports = FailedScansChecks
}