const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
// const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')
// const RegExpVari = require('./generic-regexp');

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class WebserverDefaultPage extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.DPDvulnerabilityID = 'ID-webserver-default-page-detected';
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            clearQueryParams: true,
            haveSlashAfterAttack: 'never'
        });
    }

    getAttackVectors() {
        return attackURLVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    async processAttackResponse(attack) {
        // let statusCode = _.get(attack, "result.resp.httpResponse.statusCode");
        let redirectsFollowed = _.get(attack, "result.resp.httpResponse.redirectsFollowed");
        let responseBody = _.get(attack, "result.resp.body").trim();
        if (responseBody.length == 0) return;

        if (attack.href.includes("/docs|icons/") && /The\sApache\sSoftware\sFoundation/i.test(responseBody)) return;

        if (responseBody.length >= 20 && redirectsFollowed == 0) {
            if (!this.getPluginScopedStore(attack).DPDvulnFound1 && !/\/icons/i.test(attack.httpRequest.uri)) { // Exclude /icons directory
                for (const page of defaultPages) {
                    if (page.pattern.test(responseBody)) {
                        // if (page.url.test(attack.href) && page.pattern.test(responseBody)) {
                        // const context = this.extractContext(responseBody, page.pattern);
                        const context = responseBody.match(page.pattern)[0];
                        this.addVulnerabilitytoResult(attack, this.DPDvulnerabilityID, `Web Server Default Web Page Detected. Context: ${context}`);
                        this.getPluginScopedStore(attack).DPDvulnFound1 = true;
                        break;
                    }
                }
            }
        }
    }

    extractContext(responseBody, match) {
        const contextLength = 10; // Number of words before and after the match
        const regex = new RegExp(`(?:\\S+\\s+){0,${contextLength}}\\S*${match}\\S*(?:\\s+\\S+){0,${contextLength}}`, 'i');
        const matchContext = responseBody.match(regex);

        return matchContext ? matchContext[0] : match;
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == this.DPDvulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        }
    }
}

const defaultPages = [
    { url: /loginmodulewebapi/i, pattern: /Web Api is Created for Login Module Project using asp\.net Technology/i }, // ASP.NET Web API for Login Module
    { url: /manager/i, pattern: /<title>Tomcat Web Application Manager<\/title>/i }, // Tomcat Manager Application
    { url: /readme\.html/i, pattern: /<title>README<\/title>/i }, // Common README file
    { url: /startPage/i, pattern: /<title>Start Page<\/title>/i }, // Generic start page
    // { url: /authentication/i, pattern: /<title>Authentication<\/title>/i }, // Authentication-related pages
    { url: /apache2/i, pattern: /test[.\s]{1,10}page[.\s]{1,10}(for)?[.\sa-z*]{1,10}apache|apache\.server|default\.web\.page|<title>apache2.*default.*<\/title>|<title>Apache HTTP Server Test Page powered by CentOS<\/title>/i }, // Apache default page
    { url: /iis/i, pattern: /<title>iis\d{1,2}<\/title>|<title>iis\s+windows\s+server<\/title>/i }, // IIS default page
    { url: /nginx/i, pattern: /Welcome to nginx/i }, // Nginx default page
    { url: /xampp/i, pattern: /Welcome to XAMPP/i }, // XAMPP default page
    { url: /websphere/i, pattern: /Welcome to WebSphere/i }, // WebSphere default page
    { url: /jboss/i, pattern: /Welcome to JBoss/i }, // JBoss default page
    { url: /glassfish/i, pattern: /Welcome to GlassFish/i }, // GlassFish default page
    { url: /tomcat/i, pattern: /Welcome to Tomcat/i }, // Tomcat default page
    { url: /wildfly/i, pattern: /Welcome to WildFly/i }, // WildFly default page
    { url: /weblogic/i, pattern: /Welcome to WebLogic/i }, // WebLogic default page
    { url: /jetty/i, pattern: /Welcome to Jetty/i }, // Jetty default page
    { url: /openliberty/i, pattern: /Welcome to OpenLiberty/i }, // OpenLiberty default page
    { url: /payara/i, pattern: /Welcome to Payara/i }, // Payara default page
    { url: /resin/i, pattern: /Welcome to Resin/i }, // Resin default page
    { url: /geronimo/i, pattern: /Welcome to Geronimo/i }, // Geronimo default page
    { url: /undertow/i, pattern: /Welcome to Undertow/i }, // Undertow default page
    { url: /tapestry/i, pattern: /Welcome to Tapestry/i }, // Tapestry default page
    { url: /wicket/i, pattern: /Welcome to Wicket/i }, // Wicket default page
    { url: /struts/i, pattern: /Welcome to Struts/i }, // Struts default page
    { url: /spring/i, pattern: /Welcome to Spring/i }, // Spring default page
    { url: /grails/i, pattern: /Welcome to Grails/i }, // Grails default page
    { url: /play/i, pattern: /Welcome to Play/i }, // Play default page
    { url: /vaadin/i, pattern: /Welcome to Vaadin/i }, // Vaadin default page
    { url: /gwt/i, pattern: /Welcome to GWT/i }, // GWT default page
    { url: /lift/i, pattern: /Welcome to Lift/i }, // Lift default page
    { url: /ratpack/i, pattern: /Welcome to Ratpack/i }, // Ratpack default page
    { url: /vertx/i, pattern: /Welcome to Vert\.x/i }, // Vert.x default page
    { url: /helidon/i, pattern: /Welcome to Helidon/i }, // Helidon default page
    { url: /micronaut/i, pattern: /Welcome to Micronaut/i }, // Micronaut default page
    { url: /quarkus/i, pattern: /Welcome to Quarkus/i }, // Quarkus default page
    { url: /dropwizard/i, pattern: /Welcome to Dropwizard/i }, // Dropwizard default page
    { url: /spark/i, pattern: /Welcome to Spark/i }, // Spark default page
    { url: /jooby/i, pattern: /Welcome to Jooby/i }, // Jooby default page
    { url: /javalin/i, pattern: /Welcome to Javalin/i }, // Javalin default page
    { url: /ninja/i, pattern: /Welcome to Ninja/i }, // Ninja default page
    { url: /pippo/i, pattern: /Welcome to Pippo/i }, // Pippo default page
    { url: /blade/i, pattern: /Welcome to Blade/i }, // Blade default page
    { url: /rapidoid/i, pattern: /Welcome to Rapidoid/i }, // Rapidoid default page
    { url: /restexpress/i, pattern: /Welcome to RestExpress/i }, // RestExpress default page
    { url: /restlet/i, pattern: /Welcome to Restlet/i }, // Restlet default page
    { url: /restx/i, pattern: /Welcome to Restx/i }, // Restx default page
    { url: /restli/i, pattern: /Welcome to Rest\.li/i }, // Rest.li default page
    { url: /resteasy/i, pattern: /Welcome to RestEasy/i }, // RestEasy default page
    { url: /jersey/i, pattern: /Welcome to Jersey/i }, // Jersey default page
    { url: /apachecxf/i, pattern: /Welcome to Apache CXF/i }, // Apache CXF default page
    { url: /springboot/i, pattern: /Welcome to Spring Boot/i }, // Spring Boot default page
    { url: /springmvc/i, pattern: /Welcome to Spring MVC/i }, // Spring MVC default page
    { url: /springwebflux/i, pattern: /Welcome to Spring WebFlux/i }, // Spring WebFlux default page
    { url: /springwebservices/i, pattern: /Welcome to Spring Web Services/i }, // Spring Web Services default page
    { url: /springdatarest/i, pattern: /Welcome to Spring Data REST/i }, // Spring Data REST default page
    { url: /springhateoas/i, pattern: /Welcome to Spring HATEOAS/i }, // Spring HATEOAS default page
    { url: /springcloud/i, pattern: /Welcome to Spring Cloud/i }, // Spring Cloud default page
    { url: /springsecurity/i, pattern: /Welcome to Spring Security/i }, // Spring Security default page
    { url: /springbatch/i, pattern: /Welcome to Spring Batch/i }, // Spring Batch default page
    { url: /springintegration/i, pattern: /Welcome to Spring Integration/i }, // Spring Integration default page
    { url: /springamqp/i, pattern: /Welcome to Spring AMQP/i }, // Spring AMQP default page
    { url: /springkafka/i, pattern: /Welcome to Spring Kafka/i }, // Spring Kafka default page
    { url: /springldap/i, pattern: /Welcome to Spring LDAP/i }, // Spring LDAP default page
    { url: /springshell/i, pattern: /Welcome to Spring Shell/i }, // Spring Shell default page
    { url: /springstatemachine/i, pattern: /Welcome to Spring Statemachine/i }, // Spring Statemachine default page
    { url: /springvault/i, pattern: /Welcome to Spring Vault/i }, // Spring Vault default page
    { url: /springwebsocket/i, pattern: /Welcome to Spring WebSocket/i }, // Spring WebSocket default page
    { url: /generic-web-page/i, pattern: /Web Api is Created for Login Module Project using asp\.net Technology/i },
    { url: /generic-web-page/i, pattern: /please examine the file conf\/tomcat-users\.xml in your installation/i },
    { url: /generic-web-page/i, pattern: /ASP\.NET is a free web framework for building great websites and web applications using HTML, CSS, and JavaScript\./i },
    { url: /generic-web-page/i, pattern: />wp-admin\/install\.php</i },
    { url: /generic-web-page/i, pattern: /SAP Library contains the complete documentation for SAP NetWeaver Application Server Java. You can access it by choosing SAP NetWeaver/i },
    { url: /generic-web-page/i, pattern: /Welcome to Keycloak/i },
];

const attackURLVectors = [ // Common default filenames for web servers
    // 'index.html', // Apache, Nginx, XAMPP - same attack vector using in another plugin, so we are not using it here
    'index.htm', // Apache, Nginx, XAMPP
    // 'index.php', // Apache, Nginx, XAMPP, PHP-based servers
    'default.html', // IIS
    'default.htm', // IIS
    'default.aspx', // IIS, ASP.NET
    'index.jsp', // Tomcat, GlassFish, JBoss, WebSphere
    'index.do', // Struts
    'index.action', // Struts
    'home.jsp', // Tomcat, GlassFish, JBoss, WebSphere
    'welcome.jsp', // Tomcat, GlassFish, JBoss, WebSphere
    'default.jsp', // Tomcat, GlassFish, JBoss, WebSphere
]

const requestFilenames = [ // Common default filenames for web servers - we ll move up if we find any of these
    'index.py', // Python-based servers (Django, Flask)
    'index.rb', // Ruby-based servers (Rails)
    'index.pl', // Perl-based servers
    'index.cgi', // CGI-based servers
    'index.aspx', // ASP.NET
    'index.cshtml', // ASP.NET MVC
    'index.vbhtml', // ASP.NET MVC
    'index.gsp', // Grails
    'index.ftl', // FreeMarker (Java-based)
    'index.vm', // Velocity (Java-based)
    'index.thymeleaf', // Thymeleaf (Java-based)
    'index.mustache', // Mustache (Java-based)
    'index.handlebars', // Handlebars (Java-based)
    'index.ejs', // EJS (Node.js-based)
    'index.pug', // Pug (Node.js-based)
    'index.haml', // HAML (Ruby-based)
    'index.slim', // Slim (Ruby-based)
    'index.erb', // ERB (Ruby-based)
    'index.jade', // Jade (Node.js-based)
    'index.marko', // Marko (Node.js-based)
    'index.njk', // Nunjucks (Node.js-based)
    'index.twig', // Twig (PHP-based)
    'index.liquid', // Liquid (Ruby-based)
    'index.hbs', // Handlebars (Node.js-based)
    'index.dust', // Dust.js (Node.js-based)
    'index.svelte', // Svelte (JavaScript-based)
    'index.vue', // Vue.js (JavaScript-based)
    'index.react', // React.js (JavaScript-based)
    'index.angular', // Angular (JavaScript-based)
    'index.svelte', // Svelte (JavaScript-based)
    'index.riot', // Riot.js (JavaScript-based)
    'index.ember', // Ember.js (JavaScript-based)
    'index.backbone', // Backbone.js (JavaScript-based)
    'index.meteor', // Meteor.js (JavaScript-based)
    'index.polymer', // Polymer (JavaScript-based)
    'index.aurelia', // Aurelia (JavaScript-based)
    'index.knockout', // Knockout.js (JavaScript-based)
    'index.mithril', // Mithril.js (JavaScript-based)
    'index.hyperapp', // Hyperapp (JavaScript-based)
    'index.stencil', // Stencil.js (JavaScript-based)
    'index.lit', // Lit (JavaScript-based)
    'index.sapper', // Sapper (JavaScript-based)
    'index.next', // Next.js (JavaScript-based)
    'index.nuxt', // Nuxt.js (JavaScript-based)
    'index.gatsby', // Gatsby.js (JavaScript-based)
    'index.gridsome', // Gridsome (JavaScript-based)
    'index.quasar', // Quasar (JavaScript-based)
    'index.electron', // Electron (JavaScript-based)
    'index.tauri', // Tauri (JavaScript-based)
    'index.capacitor', // Capacitor (JavaScript-based)
    'index.ionic', // Ionic (JavaScript-based)
    'index.cordova', // Cordova (JavaScript-based)
    'index.phonegap', // PhoneGap (JavaScript-based)
    'index.reactnative', // React Native (JavaScript-based)
    'index.flutter', // Flutter (Dart-based)
    'index.dart', // Dart-based servers
    'index.kivy', // Kivy (Python-based)
    'index.qt', // Qt (C++-based)
    'index.wx', // wxWidgets (C++-based)
    'index.gtk', // GTK (C-based)
    'index.swing', // Swing (Java-based)
    'index.javafx', // JavaFX (Java-based)
    'index.awt', // AWT (Java-based)
    'index.swt', // SWT (Java-based)
    'index.jme', // JME (Java-based)
    'index.lwgl', // LWJGL (Java-based)
    'index.libgdx', // libGDX (Java-based)
    'index.unity', // Unity (C#-based)
    'index.unreal', // Unreal Engine (C++-based)
    'index.godot', // Godot (GDScript-based)
    'index.cryengine', // CryEngine (C++-based)
    'index.source', // Source Engine (C++-based)
    'index.frostbite', // Frostbite Engine (C++-based)
    'index.rpgmaker', // RPG Maker (Ruby-based)
    'index.game', // Generic game engine
    'index.app', // Generic application
    'index.site', // Generic website
    'index.web', // Generic web application
    'index.portal', // Generic portal
    'index.dashboard', // Generic dashboard
    'index.admin', // Generic admin panel
    'index.control', // Generic control panel
    'index.panel', // Generic panel
    'index.console', // Generic console
    'index.monitor', // Generic monitor
    'index.viewer', // Generic viewer
    'index.explorer', // Generic explorer
    'index.browser', // Generic browser
    'index.navigator', // Generic navigator
    'index.manager', // Generic manager
    'index.editor', // Generic editor
    'index.builder', // Generic builder
    'index.creator', // Generic creator
    'index.designer', // Generic designer
    'index.developer', // Generic developer
    'index.programmer', // Generic programmer
    'index.coder', // Generic coder
    'index.hacker', // Generic hacker
    'index.geek', // Generic geek
    'index.nerd', // Generic nerd
    'index.tech', // Generic tech
    'index.engineer', // Generic engineer
    'index.scientist', // Generic scientist
    'index.researcher', // Generic researcher
    'index.inventor', // Generic inventor
    'index.creator', // Generic creator
    'index.maker', // Generic maker
    'index.builder', // Generic builder
    'index.designer', // Generic designer
    'index.architect', // Generic architect
    'index.planner', // Generic planner
    'index.strategist', // Generic strategist
    'index.analyst', // Generic analyst
    'index.consultant', // Generic consultant
    'index.advisor', // Generic advisor
    'index.expert', // Generic expert
    'index.specialist', // Generic specialist
    'index.professional', // Generic professional
    'index.guru', // Generic guru
    'index.master', // Generic master
    'index.wizard', // Generic wizard
    'index.sage', // Generic sage
    'index.oracle', // Generic oracle
    'index.mystic', // Generic mystic
    'index.seer', // Generic seer
    'index.prophet', // Generic prophet
    'index.visionary', // Generic visionary
    'index.pioneer', // Generic pioneer
    'index.trailblazer', // Generic trailblazer
    'index.pathfinder', // Generic pathfinder
    'index.explorer', // Generic explorer
    'index.adventurer', // Generic adventurer
    'index.discoverer', // Generic discoverer
    'index.innovator', // Generic innovator
    'index.creator', // Generic creator
    'index.builder', // Generic builder
    'index.designer', // Generic designer
    'index.architect', // Generic architect
    'index.planner', // Generic planner
    'index.strategist', // Generic strategist
    'index.analyst', // Generic analyst
    'index.consultant', // Generic consultant
    'index.advisor', // Generic advisor
    'index.expert', // Generic expert
    'index.specialist', // Generic specialist
    'index.professional', // Generic professional
    'index.guru', // Generic guru
    'index.master', // Generic master
    'index.wizard', // Generic wizard
    'index.sage', // Generic sage
    'index.oracle', // Generic oracle
    'index.mystic', // Generic mystic
    'index.seer', // Generic seer
    'index.prophet', // Generic prophet
    'index.visionary', // Generic visionary
    'index.pioneer', // Generic pioneer
    'index.trailblazer', // Generic trailblazer
    'index.pathfinder', // Generic pathfinder
]
module.exports = WebserverDefaultPage
