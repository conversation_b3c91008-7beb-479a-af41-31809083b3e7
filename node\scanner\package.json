{"name": "scanner", "version": "2025.26.17", "description": "New scanner to enable scanning HTML5, dynamic, Javascript heavy sites", "main": "app/main.js", "scripts": {"start": "cd app && electron main.js", "test": "echo No test implemented yet && exit 1"}, "repository": {"type": "git", "url": "git+https://bitbucket.org/amithbm911/haiku.git"}, "keywords": ["scanner", "AA", "application"], "author": "Indusface", "license": "ISC", "homepage": "https://bitbucket.org/amithbm911/haiku#readme", "dependencies": {"adm-zip": "^0.5.10", "amqplib": "^0.10.4", "archiver": "^3.1.1", "aws-sdk": "^2.305.0", "bezier-easing": "^2.1.0", "bluebird": "^3.5.0", "body-parser": "^1.18.3", "caseless": "^0.12.0", "cheerio": "1.0.0-rc.12", "chromium-net-errors": "^3.6.0", "cookie-parse": "^0.4.0", "csv-stringify": "^5.5.3", "date-diff": "^0.2.0", "dayjs": "^1.9.7", "electron": "^32.0.0", "express": "^4.16.3", "gifencoder": "^1.0.6", "glob": "^7.1.3", "he": "^1.1.1", "http-parser-js": "^0.5.0", "js-combinatorics": "^0.5.3", "jsdom": "^13.2.0", "lodash": "^4.17.10", "minimist": "^1.2.0", "mkdirp": "^0.5.1", "mutation-summary": "^0.1.1", "n-readlines": "^1.0.1", "newman": "6.1.3", "nodejs-traceroute": "^2.0.1", "nodemailer": "^4.6.8", "normalize-url": "^4.5.0", "npm": "^6.4.1", "os-utils": "0.0.14", "parse-domain": "^2.1.2", "ping": "^0.4.4", "png-file-stream": "^1.0.0", "pngjs": "^3.3.3", "postman-collection": "^3.6.9", "python-shell": "^0.5.0", "re2": "1.21.0", "request": "^2.88.0", "request-promise-native": "^1.0.4", "serve-index": "^1.9.0", "set-cookie-parser": "^2.2.1", "simhash": "^0.1.0", "uuid": "^3.3.2", "winston": "^2.4.4", "xml-to-json-promise": "0.0.3"}}