const utils = require('../../ifc-utils.js')
const _ = require('lodash')
const CheatSheetInfo = require('../../datastructure/cheatsheet-info.js')
const createActionList = require('../../datastructure/create-action-list')
const s3Utils = require('../../../common/lib/s3-utils')
const path = require("path")
const EventEmitter = require('events')

class RecordReplay extends EventEmitter {
    constructor(actionExecutor) {
        super()
        this.config = actionExecutor.config

        // init counters
        this.failedLogins = 0
        this.retry = 5;
        this.loginSucceeded = false;

        this.cheatsheets = []
        this.matchedCheatsheets = new Set()
        this.addCheatsheets()

        // event handlers
        actionExecutor.on('pre-action-execute', this.preActionExecute.bind(this))
        actionExecutor.on('post-action-execute', this.postActionExecute.bind(this))
    }

    async delay(milliseconds) {
        return new Promise(resolve => {
            setTimeout(resolve, milliseconds);
        });
    }

    async addCheatsheets() {
        // Add cheatsheets - each cheatsheet is a list of actions that need to be performed
        let guidedScans = [];
        
        if (this.config.puppeteerStepsFilePath) {
           await this.processForGuided(guidedScans);
        }

        for (let guidedScan of guidedScans) {
            // iterate all the actionConfigs. Multiple action Configs for a website. Each action configs 
            //has multiple actions. Add each actions in the actionList based on action types. 
            //Annotation need to be added later.
            let actionList = createActionList(guidedScan)
            if (actionList) {
                this.cheatsheets.push(new CheatSheetInfo(guidedScan.name || this.config.mainUrl, actionList))
            }
        }
    }

    async processForGuided(guidedScans) {
        try {
            let puppeteerJson = await this.getPuppeteerFile(this.config);
            let puppeteerSteps = puppeteerJson.steps;
            let guidedScansObject = { "name": "puppeteer-step-1", "actions": [] };

            for (let index = 1; index < puppeteerSteps.length; index++) {
                let guideActionObject = { "action": "", "xpath": "", "text": "", "annotation": "" };

                switch (puppeteerSteps[index].type) {
                    case 'change':
                        {
                            let xpath = this.getFormattedXpath(puppeteerSteps[index].selectors);

                            if (xpath) {
                                guideActionObject.action = 'type';
                                guideActionObject.xpath = xpath;
                                guideActionObject.text = puppeteerSteps[index].value;
                                guideActionObject.annotation = puppeteerSteps[index].annotation;
                            }
                        }
                        break;
                    case 'click':
                        {
                            let xpath = this.getFormattedXpath(puppeteerSteps[index].selectors);

                            if (xpath) {
                                guideActionObject.action = 'click';
                                guideActionObject.xpath = xpath;
                                guideActionObject.annotation = puppeteerSteps[index].annotation;
                            }
                        }
                        break;
                    default:
                        continue;
                }

                guidedScansObject.actions.push(guideActionObject);

                // multistep wrt "assertedEvents" as descriminator
                if (Object.keys(puppeteerSteps[index]).includes("assertedEvents")) {
                    // check next items have click or type to avoid emty action <= MIMP
                    if (guidedScansObject.actions.length)
                        guidedScans.push(guidedScansObject);
                    guidedScansObject = { "name": "puppeteer-step-" + (guidedScans.length + 1), "actions": [] };
                }

                if (puppeteerSteps.length == index + 1 && guidedScansObject.actions.length) {
                    guidedScans.push(guidedScansObject);
                }
            }
        } catch (err) {
            utils.log(`Error occured while processing record/replay file. Reason: ${err.toString()}`);
        }
    }

    getFormattedXpath(selectors) {
        let xpath = null;

        if (selectors.length > 0) {
            selectors.forEach(element => {
                element.forEach(elementChild => {
                    if (elementChild.startsWith('xpath/')) {
                        xpath = elementChild.replace('xpath/', '')
                    }
                });
            });
        }

        return xpath ? xpath : '';
    }

    async getPuppeteerFile(config) {
        // call from main js scan start and then attach to scanner config and read 
        // from deffacementcheck scan start method or constructor
        let puppeteerSteps = null
    
        let s3InPrefix = (config.puppeteerStepsFilePath)
        try {
             let resp = await s3Utils.getFile(path.dirname(config.puppeteerStepsFilePath), path.basename(config.puppeteerStepsFilePath));
             if (resp && resp.Body) {
                puppeteerSteps = resp.Body.toString()
            }
            if (puppeteerSteps) {
                puppeteerSteps = JSON.parse(puppeteerSteps);
                utils.log('info', `Found puppeteer steps for : ${JSON.stringify(puppeteerSteps)}`)
            }
        } catch (err) {
            utils.log('error', `Could not get puppeteer step file from storage location ${s3InPrefix}: ${err.toString()}`)
        }
    
        return puppeteerSteps
    }
    

    async preActionExecute(action, executionContext) {
        await this.checkForLoginPage(executionContext)
    }

    async postActionExecute(action, executionContext) {
        await this.checkForLoginPage(executionContext)
    }

    async checkForLoginPage(executionContext) {
        if (this.failedLogins >= this.retry) {
            utils.log('RecordReplay: skipping login due to too many failed logins')
            return;
        }

        let isReplayActionsTaken = false;
        let replayCount = 0;
        try {
            if(this.cheatsheets.length == 0) {
                return;
            }

            // run through all cheatsheets. If all elements of a cheatsheet exist,
            // then return those actions.
            for (let cheatSheet of this.cheatsheets) {
                let result = await cheatSheet.matches(executionContext.browser)

                if(!result) {
                    break;
                }

                //Allow external domain like SSO pages, i.e. microsoft, google for authentication 
                this.config.isLoginSteps = true;
                // do the login action
                await executionContext.executer.triggerAction(cheatSheet.getAction(), executionContext.executer.getNoPluginContext(executionContext));
                replayCount++;
                executionContext.pluginTookAction = true
                isReplayActionsTaken = true;

                if(cheatSheet.name == `puppeteer-step-${this.cheatsheets.length}`) {
                    break;
                }

                await this.delay(5 * 1000);
            }

            if(this.loginSucceeded) {
                return true;
            }

            if(isReplayActionsTaken && replayCount == this.cheatsheets.length) {
                this.failedLogins = 0;
                this.loginSucceeded = true;
                executionContext.scanner.emit('login-status', this.loginSucceeded, null, this.constructor.name);
            }
            
            if(isReplayActionsTaken && replayCount != this.cheatsheets.length) {
                this.failedLogins++;
                this.loginSucceeded = false;
                executionContext.scanner.emit('login-status', this.loginSucceeded, null, this.constructor.name);
            }
        } catch (error) {
            utils.log('RecordReplay: error occured while executing a record replay actions: ' + error.toString());
        }

        if(isReplayActionsTaken) {
            this.config.isLoginSteps = false;
        }

        return true;
    }
}

module.exports = RecordReplay