const GIFEncoder = require('gifencoder');
const fs = require('fs');
const PNG = require('pngjs').PNG
const path = require('path')
const glob = require('glob')
const s3Utils = require('../../common/lib/s3-utils')

const screenshotsRe = /crawler-screenshot-image-(\d+).png/
const screenshotsMetadataRe = /crawler-screenshot-metadata-(\d+).json/

function createGif(scanlogId, filename) {
    return new Promise(async resolve => {
        // read image directory
        let imagesFolder = './' + scanlogId
        let files = glob.sync('*.png', {
            cwd: imagesFolder
        })
        files = files.filter(a => screenshotsRe.test(a)).sort((x, y) => {
            a = +x.match(screenshotsRe)[1];
            b = +y.match(screenshotsRe)[1];
            return a - b
        })

        // find the width and height of the image
        // get the width, height from the first image since all are same width, height
        let {
            width,
            height
        } = await new Promise((res) => {
            fs.createReadStream(imagesFolder + '/' + files[0])
                .pipe(new PNG({
                    filterType: -1
                }))
                .on('metadata', function (metadata) {
                    let width = metadata.width
                    let height = metadata.height
                    res({
                        width,
                        height
                    })
                })
        })

        // start encoding gif
        const dstPath = './' + filename
        console.log(`encoding ${files.length} screenshots @ width:${width} height:${height} to: ${dstPath}`)

        // create a write stream for GIF data
        const writeStream = fs.createWriteStream(dstPath)
        // when stream closes GIF is created so resolve promise
        writeStream.on('close', () => {
            resolve(dstPath)
        })

        const encoder = new GIFEncoder(width, height)

        // pipe encoder's read stream to our write stream
        encoder.setDelay(500)
        encoder.start()
        encoder.createReadStream().pipe(writeStream)

        // add frame to encoder for each screenshot file
        let i = 1
        for (let file of files) {
            console.log(`processing ${file}: ${i++} of ${files.length}`)
            try {
                await new Promise((res, rej) => {
                    fs.createReadStream(imagesFolder + '/' + file)
                        .pipe(new PNG({
                            filterType: -1
                        }))
                        .on('parsed', function (data) {
                            encoder.addFrame(data)
                            res()
                        })
                        .on('error', (err) => {
                            rej(err)
                        })
                })
            } catch (err) {
                console.log(`Skipping ${file} : ${err.toString()}`)
            }

        }
        console.log('finishing gif ...')
        encoder.finish()
    })
}

function getRelevantMetadata(metadata) {
    return {
        location: metadata.location,
        stateId: metadata.stateId,
        actionId: metadata.actionId
    }
}

function consolidateScreenshotMetadata(scanlogId, filename) {
    // read image directory
    let imagesFolder = './' + scanlogId
    let files = glob.sync('crawler-screenshot-metadata-*.json', {
        cwd: imagesFolder
    })

    let consolidatedMetadata = {}
    const dstPath = './' + filename

    console.log(`consolidating ${files.length} metadata files to: ${dstPath}`)
    for (let file of files) {
        try {
            let metadata = JSON.parse(fs.readFileSync(`${imagesFolder}/${file}`).toString())
            let screenshotId = file.match(screenshotsMetadataRe)[1]
            consolidatedMetadata[screenshotId] = getRelevantMetadata(metadata)
        } catch (err) {
            console.log(`Skipping ${file} : ${err.toString()}`)
        }
    }

    fs.writeFileSync(dstPath, JSON.stringify(consolidatedMetadata))
    return (dstPath)
}

async function processScan(scanlogId) {
    const gifFilename = `crawl-session-${scanlogId}.gif`
    const jsonFilename = `crawl-session-metadata-${scanlogId}.json`

    try {
        console.log(`processing scan log Id ${scanlogId}. Assuming that all archives have been downloaded and png,json files extracted already`)
        let ret = await createGif(scanlogId, gifFilename)
        console.log('\t ----> created animated gif to: ', ret)

        ret = await consolidateScreenshotMetadata(scanlogId, jsonFilename)
        console.log('\t ---> consolidated metadata to: ', ret)

        await s3Utils.uploadFile('crawler/crawl-session-viz/', gifFilename, './' + gifFilename)
        await s3Utils.uploadFile('crawler/crawl-session-viz/', jsonFilename, './' + jsonFilename)
        console.log('\t ---> Uploaded files to S3 ')
        fs.unlinkSync('./' + gifFilename);
        fs.unlinkSync('./' + jsonFilename);
    } catch (err) {
        console.log('error ', err)
    }
}

// Main code
let scanlogId = process.argv[2]
processScan(scanlogId)