let utils = require('../../ifc-utils.js')
const DateDiff = require('date-diff')
const URL = require('url').URL
const querystring = require('querystring');
const _ = require('lodash');

const pluginName = 'crawlMetrics'

class CrawlMetrics {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config
        this.browser = scanner.browser
        this.defaultExcludeUrls = ['https://login.microsoftonline.com']

        // init counters and register for scan/crawl events
        // scan start
        this.scanStartTime = undefined
        scanner.on('scan-start', this.onScanStart.bind(this))

        this.config.totalCrawlDurationMins = 0;

        // next action
        this.actionsPerformed = 0
        this.maxDepth = 0
        scanner.on('next-action', this.onNextAction.bind(this))

        // skip before triggering (action dup...)
        this.skipBeforeTrigger = 0
        scanner.on('skip-before-trigger', this.onSkipBeforeTrigger.bind(this))

        // got interesting items
        this.pagesCrawled = new Set()
        scanner.on('got-interesting-items', this.onGotInterestingItems.bind(this))

        // unique state found
        this.uniqueStates = 0
        scanner.on('unique-state', this.onUniqueState.bind(this))

        // network request captured
        this.attackableNetworkRequests = 0
        scanner.on('network-action', this.onNetworkAction.bind(this))

        // sending request
        this.totalRequestsSent = 0
        this.networkRequests = new Set()
        scanner.on('processing-network-request', this.onSendingNetworkRequest.bind(this))

        // simple login status
        this.successfulLogins = 0
        this.failedLogins = 0
        scanner.on('login-status', this.onLoginStatus.bind(this))

        // Next iteration of crawl tree
        this.crawlTreeIterations = 0
        scanner.on('next-iterate-crawl-tree', this.onNextCrawlTreeIterate.bind(this))

        // scan complete
        scanner.on('scan-loop-done', this.onScanDone.bind(this))
        scanner.on('generate-crawler-metric', this.onScanDone.bind(this))

        // serialize/deserialize
        scanner.on('serialize-state', this.serializeState.bind(this))
        scanner.on('deserialize-state', this.deserializeState.bind(this))

        this.browser.webContents.on('did-navigate', this.onDidNavigation.bind(this))

        //Full object to hold all relevant unique resources links even with duplicate domains. 
        this.foundResources = new Set();
        scanner.on('found-resource', this.onFoundResource.bind(this));
    }

    onScanStart(config) {
        this.scanStartTime = new Date()
    }

    onNextAction(action, crawlState) {
        this.actionsPerformed++
        if (crawlState && crawlState.currentContext && crawlState.currentContext.depth > this.maxDepth) {
            this.maxDepth = crawlState.currentContext.depth
        }
    }

    onSkipBeforeTrigger(action) {
        this.skipBeforeTrigger++
    }

    // does not affect ret (1st param)
    onGotInterestingItems(_, interestingItems) {
        this.pagesCrawled.add(interestingItems.location)
    }

    //after navigation is done
    onDidNavigation(_, location) {
        try {
            let allExcludeUrls = this.defaultExcludeUrls.concat(this.config.excludeUrls || []);

            if (this.config.excludeFromSitemap && this.config.excludeFromSitemap.length > 0) {
                let excludeFromSitemapUrl = this.config.excludeFromSitemap.find(url => location.startsWith(url));

                if (excludeFromSitemapUrl && Array.from(this.pagesCrawled).some(page => page.startsWith(excludeFromSitemapUrl))) {
                    return;
                }
            }

            if (allExcludeUrls && allExcludeUrls.length > 0) {
                let excludeUrl = allExcludeUrls.find(url => location.startsWith(url));

                if (excludeUrl && Array.from(this.pagesCrawled).some(page => page.startsWith(excludeUrl))) {
                    return;
                }
            }
        } catch (error) {
            console.error("Error in onDidNavigation", error);
        }

        this.pagesCrawled.add(location)
    }

    onUniqueState() {
        this.uniqueStates++
    }

    onNetworkAction(networkActionInfo) {
        if (networkActionInfo && networkActionInfo.networkActions) {
            this.attackableNetworkRequests += networkActionInfo.networkActions.length
        }
    }

    onNextCrawlTreeIterate() {
        this.crawlTreeIterations++
    }

    onSendingNetworkRequest(url) {
        this.totalRequestsSent++

        let parsedUrl = new URL(url)
        parsedUrl.search = ''
        this.networkRequests.add(parsedUrl.href)
    }

    /**
     * Keep count of successful & failed logins
     * @param {boolean} loginStatus Did login succeed?
     * @param {Object} loginInfo Details about login performed
     */
    onLoginStatus(loginStatus, loginInfo, plugin) {
        if (loginStatus) {
            this.successfulLogins++
        } else {
            this.failedLogins++
        }

    }

    onScanDone(reason, ret, logger = console) {
        logger.log(`CrawlMetrics: generating metrics with scan end reason: ${reason}`)
        let scanDurationMins = Date.diff(Date.now(), this.scanStartTime).minutes();
        this.config.totalCrawlDurationMins = this.config.totalCrawlDurationMins + scanDurationMins;

        let foundResources = _.chain(Array.from(this.foundResources)).map((href) => {
            try {
                let parsedUrl = new URL(href);
                return parsedUrl.host;
            } catch (error) {
                
            }
        }).filter((parsedUrl) => {
            return parsedUrl;
        }).uniq().value();
        
        ret.metrics = {
            scanUrl: this.config.mainUrl,
            scanstart: this.scanStartTime,
            scanEnd: new Date(),
            scanDurationMins: scanDurationMins,
            scanDurationHours: Date.diff(Date.now(), this.scanStartTime).hours(),
            scanEndReason: reason,
            crawlTreeIterations: this.crawlTreeIterations,
            actionsPerformed: this.actionsPerformed,
            totalRequestsSent: this.totalRequestsSent,
            maxDepth: this.maxDepth,
            skipBeforeTrigger: this.skipBeforeTrigger,
            totalPagesCrawled: this.pagesCrawled.size,
            uniqueStates: this.uniqueStates,
            attackableNetworkActions: this.attackableNetworkRequests,
            successfulLogins: this.successfulLogins,
            failedLogins: this.failedLogins,

            //vulnsFound: this.vulnsFound,
            crawledPages: Array.from(this.pagesCrawled),
            networkRequestsSent: Array.from(this.networkRequests),
            actionMetrics: this.config.actionMetrics,
            totalCrawlDurationMins: this.config.totalCrawlDurationMins,
            foundResources: Array.from(foundResources)
        }

        logger.log(`CrawlMetrics: generated metrics with scan end reason: ${reason}`)
    }

    /**
     * return object that can be JSON.stringified and stored
     * @param {Object} pluginData Object where we can add our serialized state under our own key
     */
    serializeState(pluginData) {
        pluginData[pluginName] = {
            //counts
            actionsPerformed: this.actionsPerformed,
            totalRequestsSent: this.totalRequestsSent,
            maxDepth: this.maxDepth,
            skipBeforeTrigger: this.skipBeforeTrigger,
            uniqueStates: this.uniqueStates,
            attackableNetworkActions: this.attackableNetworkRequests,

            // sets
            pagesCrawled: Array.from(this.pagesCrawled),
            networkRequests: Array.from(this.networkRequests),
            actionMetrics: this.config.actionMetrics,
            totalCrawlDurationMins: this.config.totalCrawlDurationMins,
            foundResources: Array.from(this.foundResources)
        }
    }

    /**
     * Restore plugin data that from serialized object
     * @param {Object} pluginData Object with our serialized state under our own key
     */
    deserializeState(pluginData) {
        if (!pluginData[pluginName]) {
            return
        }

        // add to sets
        if (pluginData[pluginName].pagesCrawled) {
            for (let el of pluginData[pluginName].pagesCrawled) {
                this.pagesCrawled.add(el)
            }
        }
        if (pluginData[pluginName].networkRequests) {
            for (let el of pluginData[pluginName].networkRequests) {
                this.networkRequests.add(el)
            }
        }

        if (pluginData[pluginName].actionMetrics) {
            this.config.actionMetrics = pluginData[pluginName].actionMetrics;
        }

        if (pluginData[pluginName].totalCrawlDurationMins) {
            this.config.totalCrawlDurationMins = pluginData[pluginName].totalCrawlDurationMins;
        }

        if (pluginData[pluginName].foundResources) {
            for (let resource of pluginData[pluginName].foundResources) {
                this.foundResources.add(resource);
            }
        }

        // plain counts
        this.actionsPerformed = pluginData[pluginName].actionsPerformed || 0
        this.totalRequestsSent = pluginData[pluginName].totalRequestsSent || 0
        this.maxDepth = pluginData[pluginName].maxDepth || 0
        this.skipBeforeTrigger = pluginData[pluginName].skipBeforeTrigger || 0
        this.uniqueStates = pluginData[pluginName].uniqueStates || 0
        this.attackableNetworkRequests = pluginData[pluginName].attackableNetworkActions || 0
    }

    /**
     * Update found resources
     */ 
    onFoundResource(href) {
        this.foundResources.add(href);
    }
}

module.exports = CrawlMetrics