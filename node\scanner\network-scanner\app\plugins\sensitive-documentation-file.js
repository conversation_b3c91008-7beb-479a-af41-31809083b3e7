const debug = require('debug')('FindTxtFilePlugin')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')


/** 
 * VectorResponse style plugin that checks for sensitive txt files
 */
class FindTxtFilePlugin extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-sensitive-document-file-found'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            maxPathComponents: 4,
            clearQueryParams: true,
            haveSlashAfterAttack: 'never'
        });
    }

    getAttackVectors() {
        return PSFDVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        
        let stacode = _.get(attack,'result.resp.httpResponse.statusCode')
        let contenttype = _.get(attack,'result.resp.httpResponse.headers["content-type"]')
        let contentLength = _.get(attack,'result.resp.httpResponse.headers["content-length"]')

        // updating condition to check for content length 0 and not reporting those fp cases
        if (stacode == 200 && contenttype == 'text/plain' && contentLength !=0 ) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.httpRequest.uri)
        }
    }
}

// vectors & matches ...
const PSFDVectors = [
    `/readme.txt`,
    '/README.txt',
    '/changelog.txt',
    '/CHANGELOG.txt',
    '/error.txt',
    '/errorlog.txt',
    '/changes.txt',
    '/CHANGES.txt',
    '/wp-config-backup.txt' 
]

module.exports = FindTxtFilePlugin