const debug = require('debug')('ApplicationErrorMsgPlugin')
const RE2 = require('re2')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

// Checks for Application Errors in the response body
class ApplicationErrorMsgPlugin extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-application-error-msg-info-disclosure'
        this.matchRegexp = new RE2(appErrorMatch.map((v) => {
            return v.source
        }).join('|'), "i")

        this.matchRegexp2 = new RE2(appErrorMatch2.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    getAttackVectors() {
        return _AttackVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator', 'uri-query-params', 'form-encoded-post', 'json-body']
    }

    /**
     * checks for application error message matches in response body
     * 
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.appErrorMsgVulnFound) {
            return
        }

        let bodycheck = _.get(attack, "result.resp.body")
        let vulnFound = false

        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }

        let ExcludeURL = /\/blog\/|\/docs?|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i;
        if (ExcludeURL.test(attack.href)) {
            return
        }

        if (attack.result.resp.httpResponse.statusCode == 404 || /http\sstatus\s404/i.test(bodycheck) || /404\s-\sfile\sor\sdirectory\snot\sfound/i.test(bodycheck) ||
            /Description:\sHTTP\s404/i.test(bodycheck)) {
            return
        }

        if (/<h1>server\serror\sin/i.test(bodycheck)) {
            vulnFound = this.checkBodyForVuln(attack, /(stack\strace\:)|(source\serror\:)/i, this.vulnerabilityID)
            if (!vulnFound) {
                vulnFound = this.checkBodyForVuln(attack, /(web\.config\sconfiguration\sfile)|(customerrors\smode=&quot;off&quot)/i, this.vulnerabilityID)
            }
            if (!vulnFound) {
                vulnFound = this.checkBodyForVuln(attack, /(system\.)|(microsoft)|(asp[\s\.]+net)/i, this.vulnerabilityID)
            }
        }

        if (!vulnFound && (/server\serror\sin\sapplication/.test(bodycheck) || /detailed\serror\sinformation/i.test(bodycheck))) {
            vulnFound = this.checkBodyForVuln(attack, /physical\spath/i, this.vulnerabilityID)
        }

        if (!vulnFound && /<h1>server\serror/i.test(bodycheck) && /an error has occurred/i.test(bodycheck)) {
            vulnFound = this.checkBodyForVuln(attack, /error message:/i, this.vulnerabilityID)
        }

        if (!vulnFound && /<h1>server\serror/i.test(bodycheck)) {
            vulnFound = this.checkBodyForVuln(attack, this.matchRegexp2, this.vulnerabilityID)
        }
        
        //API
        if (!vulnFound && /invalid input syntax/i.test(bodycheck) && /\/application\/node_modules/i.test(bodycheck)) {
            vulnFound = this.checkBodyForVuln(attack, /\.js:\d+:\d+\)\"/i, this.vulnerabilityID)
        }

        if (!vulnFound && (attack.result.resp.httpResponse.statusCode == 400 || (attack.result.resp.httpResponse.statusCode >= 500 && /501 Not Implemented/i.test(bodycheck)))) {
            vulnFound = this.checkBodyForVuln(attack, /apache tomcat\/\d/i, this.vulnerabilityID)
        }

        //appErrorMatch
        if (!vulnFound) {
            vulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
        }
        if (vulnFound) {
            pluginDataForRequest.appErrorMsgVulnFound = true
        }
    }
}

const appErrorMatch = [
    /The Error Occurred in <b>(.*): line.*<\/b><br>/,
    /The error occurred while processing.*Template: (.*) <br>./,
    /The error occurred while processing.*in the template file (.*)\.<\/p><br>/,
    /<title>Invalid\sfile\sname\sfor\smonitoring:\s'([^']*)'\.\sFile\snames\sfor\smonitoring\smust\shave\sabsolute\spaths\,\sand\sno\swildcards\.<\/title>/,
    /<b>(Warning|Fatal\serror|Parse\serror)<\/b>:\s+/,
    /((?:Unknown database '.*?')|(?:No database selected)|(?:Table '.*?' doesn't exist))/,
    /Exception report.*message.*description.*exception.*note.*/,
    /<head><title>JRun Servlet Error<\title><\/head>/,
    /<h1>Servlet\sError:\s\w+?<\/h1>/,
    /System.ArgumentException:/,
    /ORA-01438:/,
    /Microsoft OLE DB Provider for ODBC Drivers/,
    /Microsoft OLE DB Provider for SQL Server/,
    /ODBC Microsoft Access Driver/,
    /Invalid parameter type/,
    /Column count doesn't match value count at row/,
    /\[SQL Server Driver\]\[SQL Server\]Line 1\: Incorrect syntax near/,
    /Supplied argument is not a valid PostgreSQL result/,
    /internal error \[IBM\]\[CLI Driver\]\[DB2\/6000\]/,
    /Fatal error: /,
    /A PHP Error Was encountered/,
    /javax.servlet.ServletException/,
    /java\.lang\.NumberFormatException\: For input string\:/,
    /java.lang.Exception:/,
    /java.lang.NullPointerException/,
    /java.lang.ClassNotFoundException/,
    /java\.lang\.IllegalArgumentException: Invalid character found/,
    /java\.lang\.RuntimeException: Failed/,
    /<span><H1>Server Error in '\/' Application/,
    /Compiler Error Message:/,
    /Notice: Undefined index:/,
    /Detailed information about ASP.NET error/,
    /Original Exception:/,
    /invalid literal for int\(\)/,
    /exceptions.ValueError/,
    /Type mismatch:/,
    /'800a0005'/,
    /Operation is not allowed when the object is closed/,
    /error 'ASP 0126'/,
    /does not contain handler parameter/,
    /PythonHandler django.core.handlers/,
    /t = loader.get_template(template_name)/,
    /Ruby on Rails application could not be started/,
    /Server.Execute Error/,
    /error '80040e10'/,
    /\[Microsoft\]\[ODBC driver for Oracle\]\[Oracle\]ORA-01008:/,
    /_ERR_CMD_CMD_NOT_FOUND/,
    /SQLSTATE\[HY000\]/,
    /error '800a0d5d'/,
    /error '800a0046'/,
    /org\.springframework\.web\.bind\.MissingServletRequestParameterException:/,
    /SqlException/,
    /java.io.FileNotFoundException/,
    /SRVE0190E/,
    /ParseError(.*)error/,
    /nested exception is org\.hibernate\.exception\.GenericJDBCException/,
    /System\.We\.UI\.Control.OnLoad\(/,
    /org\. apache\.coyote\.AbstractProtocol\$ConnectionHandler\.process\(/,
    /org\.apache\.tomcat\.util\.net\.NioEndpoint\$SocketProcessor\.doRun\(/,
    /SyntaxError: Unexpected token \w+ in JSON/,
    /System\.FormatException: Input string was not in a correct format/,
    /Newtonsoft\.Json\.JsonReaderException: Unexpected character encountered/,
    /System\.Web\.WebPages\.StartPage\.RunPage\(/,
    /The server cannot or will not process the request due to something that is perceived to be a client error/,
]
const appErrorMatch2 = [/An exception occurred while processing your request/,
    /An application error occurred on the server/,
    /The current error page you are seeing can be replaced/,
    /<configuration>\s<system.web>[\w\W]+?<\/system.web>\s<\/configuration>/,
    /Description: The current trace settings prevent trace.axd from being viewed remotely/,
    /Description: An unhandled exception occurred during the execution of the current web request/,
]

const _AttackVectors = [
    `*`,
    `{haiku*haiku}`,
    `{{haiku*haiku}}`,
]

module.exports = ApplicationErrorMsgPlugin