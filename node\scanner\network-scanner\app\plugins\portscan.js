const NetworkAttack = require('./network-attack')
const _ = require('lodash')
//const dns = require('dns');
const net = require('net');

class PortScanner extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-port-scanner'
    }

    async processAttackResponse(originalRequest) {
        let pluginStorageScanScope = this.getPluginScopedStore(originalRequest, 'this-scan')

        if (pluginStorageScanScope.OpenPortFound) {
            return
        }

        let host = originalRequest.hostname
        //let host = '**************' //Either we can use remote address - proxypass.registerdomainname.indusguard.com                
        /* https://blog.netwrix.com/2022/08/04/open-port-vulnerabilities-list/ */

        let openport = []
        let ports = [20, 21, 22, 23, 25, 53, 137, 139, 445, 80, 8080, 8443, 1433, 1434, 3306, 3389] //if want to check more ports add here
        openport = await this.findopenport(host, ports)
        if (openport.ports.open.length > 0) {
            if (this.isApiScanMode(originalRequest) && /\b80\b/.test(openport.ports.open)) {
                let details = { "result": "Found open port: 80" }
                this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, details)
                pluginStorageScanScope.OpenPortFound = true
                return
            }
            if (!this.isApiScanMode(originalRequest)) {
                let details = { "result": "Found open port: " + openport.ports.open }
                this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, details)
                pluginStorageScanScope.OpenPortFound = true
                return
            }

            /*  
            for (let port of openport.ports.open) {
                if (port == 80) {
                    let details = { "result": "Found open port: 80/tcp" }
                    this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, details)
                    pluginStorageScanScope.OpenPortFound = true
                    break
                }
            }zz
            */
        }

        /*let familyname
        //https://nodejs.org/api/dns.html#dns_dns_lookup_hostname_options_callback
        const options = {
            all: true,
            //verbatim: false //Request URL: https://was.indusface.com/, Remote Address: **************:443
        };
        dns.lookup(host, options, (err, address, family) => {
            //console.log('address: %j family: IPv%s', address, family);
            details.push({
                ipaddress: address,
                ipfamily: family,
                host: host
            })
        });
    }*/
    }

    findopenport(host, ports) {
        return new Promise((resolve, reject) => {
            // recursive function to check all port status one after the other is complete
            const connectToPorts = (host, ports, scanResults) => {

                let port = ports.shift();

                connectToPort(host, port, function (result) {
                    // add to our results based on the status of the result and scan
                    if (result.status == 'connect') {
                        scanResults.ports.open.push(result.port);
                    }
                    // recursivly go through all the ports
                    if (ports.length) {
                        connectToPorts(host, ports, scanResults);
                    }
                    // when ports are done resolve the promise
                    else {
                        resolve(scanResults);
                    }
                });

            };

            // connect to a single port and get the status
            const connectToPort = (host, port, callback) => {
                let socket = new net.Socket();
                // increase this if y'all on dial up
                let timeout = 200;

                // new properties & events for port scanner
                socket._scan = {};
                socket._scan.status = 'initialized';
                socket._scan.host = host;
                socket._scan.port = port;
                socket._scan._events = { complete: callback };

                // events for socket
                socket.on('connect', function () {
                    this._scan.status = 'connect';
                    socket.destroy();
                });
                socket.on('timeout', function () {
                    this._scan.status = 'timeout';
                    socket.destroy();
                });
                socket.on('error', function (exception) {
                    this._scan.status = 'error';
                    socket.destroy();
                });
                socket.on('close', function (exception) {
                    this._scan._events.complete(this._scan);
                });

                socket.setTimeout(timeout);
                socket.connect(port, host);

            };
            //let scanResults = { 'host': host, 'ports': { 'open': [], 'closed': [] } }; 
            let scanResults = { 'ports': { 'open': [] } };
            connectToPorts(host, ports, scanResults);
        });
    };
}

module.exports = PortScanner