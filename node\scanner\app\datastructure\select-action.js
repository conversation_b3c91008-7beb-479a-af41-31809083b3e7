const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// select action
class SelectAction {
    constructor(xpath, selectVal, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.selectVal = selectVal
        this.annotation = annotation
    }

    get actionType() {
        return 'select'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.selectVal, this.annotation]
        }
    }


    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let encXPath = utils.encode(this.xpath)
        let selString = this.selectVal != undefined ? this.selectVal.toString() : "0"
        let jscode = `indusfaceRenderer.setSelectValue('${encXPath}', '${selString}')`
        await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true))
        return true
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} val:'${this.selectVal}' for ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = SelectAction