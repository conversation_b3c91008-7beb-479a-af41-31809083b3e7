const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * A security vulnerability that occurs when an attacker can inject malicious content into the response headers returned by a web server.
 */
class HttpResponseHeaderInjection extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-http-response-header-injection'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            // alwaysIterateEnd: false,
            // skipRoot: false,
            // maxPathComponents: 0,
            // clearQueryParams: true,
            // addSlashBeforeAttack: false,
            // haveSlashAfterAttack: 'never',
            encodings: ['raw'],
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return HttpPathToAttack
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params', 'uri-path-iterator', 'form-encoded-post', 'uri-permutation']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) return;

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.HttpResponseHeaderInjection) return;

        // Test Case A: Check for the vulnerability on the redirected URL
        let redirects = _.get(attack, 'result.resp.httpResponse.redirects', []);
        if (redirects.length > 0) {
            const foundVulnerability = redirects.some(redirect => {
                let redirectedHeaders = _.get(redirect, 'headers', {});
                return /HaikuTest/i.test(redirectedHeaders['x-haiku']);
            });

            // If any redirected URL contains the custom header, alert for the vulnerability
            if (foundVulnerability) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `X-Haiku: HaikuTest`);
                pluginDataForRequest.HttpResponseHeaderInjection = true;
            }
        }

        // Test Case B: Check for successful custom header(x-haiku) injection in the application
        const potentialVulnHeader = _.get(attack, 'result.resp.httpResponse.headers["x-haiku"]', '');
        if (/HaikuTest/i.test(potentialVulnHeader)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `X-Haiku: HaikuTest`);
            pluginDataForRequest.HttpResponseHeaderInjection = true;
        }

        // This HTML Injection detection is bad idea as we don't know about the behavior of the application parameter processing and generating FPs removing it and making the plugin exclusive for the detection of HTML Response Header Injection.

        // Test Case C: Check for successful HTML Injection using custom header(x-haiku) injection in the application
        // const responseBody = _.get(attack, 'result.resp.httpResponse.body', '');
        // // const htmlInjectionPattern = /<h1>haikumsg<\/h1>/i;
        // if (/<h1>haikumsg<\/h1>/i.test(responseBody)) {
        //     this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `X-Haiku: HaikuTest`);
        //     pluginDataForRequest.HttpResponseHeaderInjection = true;
        // }
    }
}

const HttpPathToAttack = [

    // HTML Injection
    // `test%0d%0aX-Haiku: HaikuTest%0d%0a%0d%0a<h1>haikumsg</h1>`,

    // Basic CRLF injection patterns
    `test%0d%0aX-Haiku: HaikuTest`,

    // Different CRLF encodings
    `test%0D%0AX-Haiku: HaikuTest`,
    `test\r\nX-Haiku: HaikuTest`,

]

module.exports = HttpResponseHeaderInjection