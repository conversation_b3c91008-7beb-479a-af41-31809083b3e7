const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
class FindRobotTxtFilePlugin extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-robot-file-found'
    }

    getAttackVectors() {
        return AttackVectors
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            // skipRoot: true,
            maxPathComponents: 2,
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never'
        });
    }
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        //Plugin request scan scope - this scan, means to report one only in entire scan
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.robotTxtFileFound) {
            return false
        }

        if (/get/i.test(attack.originalRequest.httpRequest.method) && !/\.\w+$/.test(attack.param)) {
            return await super.performNetworkAttack(attack)
        }
    }

    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        //Plugin request scan scope - this scan, means to report one only in entire scan
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginStorageScanScope.robotTxtFileFound) {
            return
        }

        let stacode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed', '0')
        if (stacode == 200 && redirect == 0) {
            let ResBody = _.get(attack, "result.resp.body", '')
            if (/(?:disallow|allow|User-agent) ?:/i.test(ResBody)) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.httpRequest.uri)
                pluginStorageScanScope.robotTxtFileFound = true
            }
        }
    }
}

const AttackVectors = [
    `robots.txt`,
    `ROBOTS.TXT`,
]

module.exports = FindRobotTxtFilePlugin