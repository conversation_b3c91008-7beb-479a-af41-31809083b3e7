const URL = require('url').URL
const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const UriQueryParameters = require('./uri-query-parameters')
const UriPathIterator = require('./uri-path-iterator')


const _ParameterType = 'BaseURI'

// Delegate that will try permutations on the URI - attack with generated query param 
// and attack the pathname

// starting with : www.xyz.com?id=5&sid=98
//  attack value: www.xyz.com?haiku[rnd]=<Attack>
//  attack param name: www.xyz.com?<Attack>=haiku[rnd]
//  attack uri: www.xyz.com/<Attack>
class UriAndParamsPermute extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore,options) {
        super(request, scanStore, _ParameterType,options)
        this.curAttackVector = ''
        this.attackUrl = new URL(request.httpRequest.uri)
        this.attackUrl.search = ''
        this.attackUriQueryParams = false
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    * getIterator() {
        // check and yield* the URI iterator
        // then yield the uri <parameter>"
        let attackRequest = _.clone(this.originalRequest)
        attackRequest.httpRequest = _.cloneDeep(this.originalRequest.httpRequest)

        attackRequest.httpRequest.uri = this.attackUrl.toString()
        if (this.originalRequest.httpRequest.method == 'GET') {
            this.attackUriQueryParams = true

            // get a request without any query params and let uri query parameters
            // iterate over that. It will do the extra param & param name attacks.

            // don't deep-clone the response, only the request
            this.uriQueryParams = new UriQueryParameters(attackRequest, this.scanStore,this.options)
            this.uriQueryParams.setOptions(this.options)
            yield* this.uriQueryParams.getIterator()
            
            this.attackUriQueryParams = false
        }


        this.uriPathIterator = new UriPathIterator(attackRequest, this.scanStore,this.options)
        //not setting option here. Might have to re write the code here. default option.
        yield* this.uriPathIterator.getIterator()

        // now generate the URI pathname attack

    }

    getAttackType() {
        if (this.attackUriQueryParams) {
            return this.uriQueryParams.getAttackType()
        }
        return this.attackType
    }

    getParameterType() {
        if (this.attackUriQueryParams) {
            return this.uriQueryParams.getParameterType()
        }
        return this.parameterType
    }

    /**
     * get encodings suported by this type of delegate. Since this is a composite delegate, it will just 
     * return the member deletegate it is currently iterating
     * @override
     */
    getEncodings(param, value) {
        if (this.attackUriQueryParams) {
            return this.uriQueryParams.getEncodings(param, value)
        }

        return this.uriPathIterator.getEncodings(param, value)

    }

    modifyParam(param, value, encoding) {
        if (this.attackUriQueryParams) {
            return this.uriQueryParams.modifyParam(param, value, encoding)
        }
        return this.uriPathIterator.modifyParam(param, value, encoding)
    }

    // get the modified request
    getHttpRequest(encoding) {
        if (this.attackUriQueryParams) {
            return this.uriQueryParams.getHttpRequest(encoding)
        }
        return this.uriPathIterator.getHttpRequest(encoding)

    }

    // reset
    reset() {
        if (this.attackUriQueryParams) {
            return this.uriQueryParams.reset()
        }
        return this.uriPathIterator.reset()
    }
}


module.exports = UriAndParamsPermute