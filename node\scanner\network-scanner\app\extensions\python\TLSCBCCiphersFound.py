import socket
import ssl
import pprint
import sys
import json
import re

vulndataList = []
cu = []
vdata = {}
protocolVer = [ssl.PROTOCOL_TLSv1_1,ssl.PROTOCOL_TLSv1_2,ssl.PROTOCOL_TLSv1]

#hostNameData='www.divpro.hdfcbank.com'
#https://docs.python.org/3/library/ssl.html

TLSCBCCiphers = ['ECDHE-RSA-AES256-SHA384', 'DHE-RSA-AES256-SHA256', 'ECDHE-RSA-AES128-SHA256', 'DHE-RSA-AES128-SHA256', 'ECDHE-RSA-AES256-SHA', 'ECDHE-RSA-AES128-SHA', 'AES256-SHA256', 'AES128-SHA256', 'AES256-SHA', 'AES128-SHA']

def read_json_stdin():
    infile = sys.stdin
    jsonstr = json.load(infile)
    if jsonstr != None:
        infile.close()
        return jsonstr['hostName'], jsonstr['strongCiphers']
    else:
        return None

def write_json_stdout(outdata):
    #print("writing to stdout...")
    jsonData = json.dump(outdata, sys.stdout, sort_keys=True, indent=4)
    print("\n")
    sys.stdout.flush()
    
def sslWeakVersion(hostNameData):
    if len(hostNameData) > 0:                
        try:
            for i in protocolVer:

                context = ssl.SSLContext(i)
                #print(i)  

                #print(hostNameData)
                """if re.search(r'www.', str(hostNameData)):
                    www = hostNameData.split('www.')[1]
                    #print(www)
                    hostNameData = www"""          

                s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                ssl_sock = context.wrap_socket(s, server_hostname=hostNameData)
                ssl_sock.connect((hostNameData, 443))
                #supportedCiphers2 = context.get_ciphers()

                sslVersion = ssl_sock.version()
                #print(sslVersion)                
                # certificateInfo=ssl_sock.getpeercert()
                cipherUsed=ssl_sock.cipher()
                #print(cipherUsed)                
                cname = str(cipherUsed).split("'")[1]
                #print(cname)                 
                if len(cname) != 0:
                    if cname in TLSCBCCiphers:
                        Coll = " ".join((sslVersion,cname))
                        #print(Coll)
                        cu.append(Coll)
                    vdata['CBCCiphersFound']=" : ".join(cu)
                    if len(vdata['CBCCiphersFound']) == 0:
                        vdata['CBCCiphersFound'] = False
                else:
                    vdata['CBCCiphersFound'] = False

            
                                    
        except Exception as e:
            pass  

        """        
        #TO find list of CBC pls enable following lines, and capture context.get_ciphers(), but need above python 3.7.4+ with openssl 1.1.*
        # vdata['cucipher'] =" : ".join(cu)
                #Get a list of enabled ciphers. The list is in order of cipher priority            
                #SSLSocket.shared_ciphers()
                #ansa = ssl_sock.shared_ciphers()
                #vdata['xyz']=ansa
                #print("\n")
                #print(supportedCiphers2)
                #vdata['SC'] = supportedCiphers2
        if len(supportedCiphers2) != 0:
            jsonData = json.loads(json.dumps(
                supportedCiphers2, indent=4, sort_keys=True))  
            for x in jsonData:
                #print(x)
                # get description of the CBC ciphers and exact cipher name in list
                auth = x.get('auth')
                if auth == 'auth-rsa':
                #print(auth)                
                    protocol = x.get('protocol')
                    #print(protocol)
                    if protocol == 'TLSv1.2' or protocol == 'TLSv1.1' or protocol == 'TLSv1.0':                        
                        symmetric = x.get('symmetric')
                        if re.search(r'-cbc', str(symmetric)):
                            #print(symmetric)
                            desc = x.get('description').split(' ', 1)[0] 
                            #new = " ".join((str1, str2))            
                            #Coll = " ".join((protocol,desc))
                            CBCL.append(desc)  
            vdata['CBCCiphersFound']=" : ".join(CBCL) 
            if len(vdata['CBCCiphersFound']) == 0:                
                vdata['CBCCiphersFound'] = False 
        else:            
            vdata['CBCCiphersFound'] = False
        """
        vulndataList.append(vdata)
    return vulndataList


if __name__ == "__main__":
    hostNameData, cipherList = read_json_stdin()
    # convert ciphers obtained to list
    #strongCipherList = cipherList.split(':')
    getSSLWeakVersion = sslWeakVersion(hostNameData)
    write_json_stdout(getSSLWeakVersion)
