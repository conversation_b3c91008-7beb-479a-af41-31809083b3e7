{"scanId": 324, "scanlog_id": 324, "scanner": "haiku", "serviceId": 98650, "mainUrl": "https://127.0.0.1:5000", "maxCrawlTimeMins": 30, "skipUpdateHostFile": false, "excludeUrls": ["https://login.microsoftonline.com", "https://login.live.com"], "periodicSaveStateMins": 10, "crawlerIP": "************", "generateUrlAnalysis": false, "wappalyzerSourcePath": "/mnt/haiku/wappalyzer", "apiDiscovery": false, "requestId": 1, "attackRequestId": 0, "isInitializing": false, "scanLogId": 324, "revalidateVulns": {}, "revalidationsToProcess": [], "annotatedRequests": {}, "queuedRequests": [], "processedRequests": [], "uniqueCrawlerRequests": [], "attackedCrawlerRequests": [], "uniqueParams": [], "sessionInfo": {}, "stats": {"byCrawlerReq": {}, "byPlugin": {}, "byAttackArea": {}, "pluginTimings": {"version": 2}}, "crawlComplete": true, "scanComplete": true, "crawlStart": 1749636519109, "scanFeasible": false, "savedResponseCnt": 1, "responsesBeingProcessed": {"count": 0, "totalSize": 0}, "expectedCrawlEnd": 1749638319109, "nextSnapshotTime": 1749637124067, "totalScanTimeMins": 5.33, "processingRequestQueue": false, "crawlerInfo": {"scanId": 324, "scanlog_id": 324, "serviceId": 98650, "scanner": "haiku", "mainUrl": "https://127.0.0.1:5000", "scanFeasible": false, "scanFeasibilityMsg": "scan URL redirects to not allowed crawl location: chrome-error://chromewebdata/", "isPartial": true, "crawlTreeInfo": {"totalStates": 1, "unprocessedStates": 1}, "metrics": {"scanUrl": "https://127.0.0.1:5000", "scanstart": "2025-06-11T10:08:39.320Z", "scanEnd": "2025-06-11T10:13:39.529Z", "scanDurationMins": 5, "scanDurationHours": 0.1, "scanEndReason": "scan not feasible", "crawlTreeIterations": 0, "actionsPerformed": 0, "totalRequestsSent": 0, "maxDepth": 0, "skipBeforeTrigger": 0, "totalPagesCrawled": 0, "uniqueStates": 0, "attackableNetworkActions": 0, "successfulLogins": 0, "failedLogins": 0, "crawledPages": [], "networkRequestsSent": [], "actionMetrics": {}, "totalCrawlDurationMins": 5, "foundResources": ["127.0.0.1:5000"]}, "requestId": 3}, "scanFeasibleMsg": "scan URL redirects to not allowed crawl location: chrome-error://chromewebdata/", "maintenanceIterationsAfterScanComplete": 1, "replayScanInfo": {}}