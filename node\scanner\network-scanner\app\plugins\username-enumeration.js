const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const _ = require('lodash')
/* const FingerPrint = require('../../../common/lib/fingerprint')
const cheerio = require("cheerio") */
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RE2 = require('re2')

/**
 * Username Enumeration strategy:
 * Here for all the login pages we will try to login with different usernames to see if anyone of them
 * is correct and only password specific message is displayed. If so then we will report the vulnerability
 * else not
 */
class UsernameEnumeration extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-username-enumeration'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(errorMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of LFI attack vectors
     * @override
     */
    getAttackVectors() {
        return LoginDelegate.createVectorsIterator(BruteForceUNVectors, BruteForcePwdVectors)
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['login-request']
    }

    /**
     * @typedef {Object} fingerprintData
     * @property {fingerprint} structure  fingerprint of all the HTML/XML tags
     * @property {fingerprint} content fingerprint of all the text i.e. content of text nodes
     * @property {Number} statusCode status code from this response
     * @property {string} redirects entire redirect chain joined with ' + '
     */

    /**
     * Get structure and content fingerprints from the response.
     * @returns {fingerprintData} fingerprintData
     * @param {response} response The HTTP response
     */
   /*  getFingerprints(response) {
        let fingerprints = { statusCode: response.statusCode }
        Object.assign(fingerprints, HaikuUtils.getFingerprints(response))
        return fingerprints
    } */

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {

        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //if vuln already found then return
        if (pluginStorageScanScope.usernameEnumeration) {
            return
        }

        //Below scope only for all original crawler request in current scan scope
        //let pluginStorage = this.getPluginScopedStore(attack, 'original-crawler-request')

        //Below code is to fetch the body of original request made
        if (attack.pluginName != this.getName()) {

            return
        }
        //see if in the attacked login response we can enumerate if username is correct or not
        //update below function to add context and vectors
        let vulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID, { addVulnerabilitytoResult: false })
        if (vulnFound) {
            let vuln = {
                details: {
                    context: vulnFound.details,
                    username: attack.vector.unVector,
                    password: attack.vector.pwdVector
                }
            }
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginStorageScanScope.usernameEnumeration = true
            return
        }
    }
}

//most common usernames from : 
//https://github.com/danielmiessler/SecLists/blob/master/Usernames/top-usernames-shortlist.txt
const BruteForceUNVectors = [
    // all the username vectors, can use IdentityVector as well
    VectorResponseAttack.identityVector,
    'opensourcecms',
    `root`,
    `admin`,
    `test`,
    `guest`,
    `info`,
    `adm`,
    `mysql`,
    `user`,
    `administrator`,
    `oracle`,
    `ftp`,
    `pi`,
    `ec2-user`,
    `vagrant`,
    `azureuser`,
]

// only to use the valid password to see username specific message if being reflected by site
const BruteForcePwdVectors = [

    VectorResponseAttack.identityVector,
    '',
    'Invalid#@iku847pwd',
]

const errorMatch = [
    /password\sincorrect/,
    /password\sis\sincorrect/,
    /error:\sIncorrect password/,
    /not\senough\spermission\sto\saccess/,
    /invalid\spassword/,
    /password\sis\sinvalid/,
    /password\sinvalid/,
    /account\sdisabled/,
    /user\sis\snot\sactive/,
    /wrong\spassword/,
    /password\sis\swrong/,
    /(.*)password(.*)is(.*)incorrect(.*)/,
    /(.*)password(.*)incorrect(.*)/,
    /(.*)password(.*)is(.*)invalid(.*)/,
    /(.*)password(.*)invalid(.*)/,
    /(.*)password(.*)is(.*)wrong(.*)/,
    /(.*)password(.*)wrong(.*)/,
    /(.*)invalid(.*)password(.*)/,
    /(.*)incorrect(.*)password(.*)/,
    /(.*)wrong(.*)password(.*)/,
    /Invalid\suser/,
    /Email unregistered/,
    /contact the [a-z]{1,15}? to register your email/,
    /Can't find a user with that e-?mail adress/,
    /check your email for the activation/,
]

module.exports = UsernameEnumeration