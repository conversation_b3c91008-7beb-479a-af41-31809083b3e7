let simhash = require('simhash')('md5')
class FingerPrint {
    constructor(fingerPrint) {
        this.fingerPrintData = simhash(fingerPrint)
        this.length = this.fingerPrintData.length
    }

    similarity(fingerprint) {
        if ( !fingerprint) {
            return 0
        }

        let fingerprintData = fingerprint 
        if ( 'string' != typeof(fingerprint)) {
            fingerprintData = fingerprint.fingerPrintData
        }

        // count the bits in common and return ratio to length
        let commonBits = 0
        for (let i = 0; i < fingerprint.length; i++) {
            if (fingerprintData[i] == this.fingerPrintData[i]) {
                commonBits++
            }
        }

        return commonBits / this.fingerPrintData.length
    }

    toString() {
        return this.fingerPrintData.join('')
    }
}

module.exports = FingerPrint