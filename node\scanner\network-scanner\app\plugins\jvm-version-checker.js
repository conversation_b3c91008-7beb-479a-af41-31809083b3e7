const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class JVMVersionChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        const jvmVersionHeaders = ['server', 'x-powered-by']
        this.headersToIterate = {
            headers: jvmVersionHeaders,
            vulnId: 'ID-jvm-version-headers'
        }
    }

    /**
     * @param {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {})
        let vulns = [];

        // This condition is to check if vuln already found then return
        if (pluginDataForRequest[this.headersToIterate.vulnId]) {
            return
        }

        for (let headerInfo of this.headersToIterate.headers) {

            // Get the response header information
            let val = headers[headerInfo];

            if (/java\svirtual|jvm|virtual|java/i.test(val) && val != undefined) {
                let vuln = {
                    details: {
                        header: headerInfo,
                        value: val
                    }
                }
                vulns.push(vuln);
            }
        }

        /**
         * If vuln found then it will add it to the vulnerability list
         */
        if (vulns.length > 0) {
            this.addVulnerabilitytoResult(attack, this.headersToIterate.vulnId, vulns)
            pluginDataForRequest[this.headersToIterate.vulnId] = true
        }
    }
}
module.exports = JVMVersionChecker