const debug = require('debug')('UnencryptedViewState');
const NetworkAttack = require('./network-attack');
const _ = require('lodash');
const HaikuUtils = require('../../../common/lib/haiku-utils');

class UnencryptedViewState extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        this.vulnerabilityID = 'ID-viewstated-sensitive-data';
    }

    /**
     * Pre-check before expensive analysis. If this returns false,
     * processAttackResponse() will not be called.
     * @param {attack} attack - The attack object
     * @returns {boolean}
     */
    wantProcessAttackResponse(attack) {
        if (attack.attackArea === "original-crawler-request") {
            const body = _.get(attack, "result.resp.body", "");
            return /viewstate/i.test(body);
        }
        return false;
    }

    /**
     * Process HTTP response for unencrypted ViewState vulnerability.
     * @param {attack} attack - The attack object including HTTP request+response
     */
    async processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack);

        // Avoid duplicate detection
        if (pluginDataForRequest.viewStateVulnFound) {
            return;
        }

        try {
            const $ = _.get(attack, 'result.resp.httpResponse.cheerio');
            const viewState = $("input[name='__VIEWSTATE']").attr('value');

            // Check if the ViewState looks like a base64 string
            if (viewState && /^[A-Za-z0-9+/=]+$/.test(viewState)) {
                try {
                    const decodedValue = Buffer.from(viewState, 'base64').toString('utf-8');

                    // Look for sensitive keywords in the decoded value
                    const matchedIndicators = decodedValue.match(/\b(password|pass|passwd|pwd|secret|token|access_token|refresh_token|auth|authentication|apikey|api_key|key|bearer|session|sessionid|jsessionid|phpsessid|sid|userid|username|email|user|uid|guid|ssn|creditcard|ccnum|cardnumber|cvc|cvv|iban|bank|accountnumber|routingnumber|pan|firstname|lastname|fullname|dob|dateofbirth|address|phone|mobile|zipcode|postalcode|otp|pin|2fa|mfa|securityquestion|answer|challenge|salt|hash)\b/gi);

                    if (matchedIndicators) {
                        const uniqueMatches = [...new Set(matchedIndicators.map(e => e.toLowerCase()))];
                        const vulnerabilityDetails = `Sensitive Data in Unencrypted ViewState Detected: ${uniqueMatches.join(', ')}`;

                        this.addVulnerabilitytoResult(
                            attack,
                            this.vulnerabilityID,
                            vulnerabilityDetails
                        );
                        pluginDataForRequest.viewStateVulnFound = true;
                    }
                } catch (decodeError) {
                    debug('Error decoding ViewState:', decodeError);
                }
            }
        } catch (e) {
            debug('Error in processAttackResponse:', e);
        }
    }

    /**
     * Auto Proof-of-Concept Annotation
     * @param {attack} attack - The attack object
     * @param {string} vulnID - The vulnerability ID
     */
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID !== this.vulnerabilityID) return;

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', 'param', [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', 'param', ['statusCode']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', 'text', ['VIEWSTATE', 'viewstate']);
    }
}

module.exports = UnencryptedViewState;
