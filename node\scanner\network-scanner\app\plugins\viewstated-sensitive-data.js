const debug = require('debug')('UnencryptedViewState');
const NetworkAttack = require('./network-attack');
const _ = require('lodash');
const HaikuUtils = require('../../../common/lib/haiku-utils');

class UnencryptedViewState extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        this.vulnerabilityID = 'ID-viewstated-sensitive-data';
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea === "original-crawler-request") {
            const body = _.get(attack, "result.resp.body", "");
            return /viewstate/i.test(body);
        }
        return false;
    }

    async processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.viewStateVulnFound) return;

        try {
            const $ = _.get(attack, 'result.resp.httpResponse.cheerio');
            const viewState = $("input[name='__VIEWSTATE']").attr('value');

            if (viewState && /^[A-Za-z0-9+/=]+={0,2}$/.test(viewState)) {
                try {
                    const decodedValue = Buffer.from(viewState, 'base64').toString('utf-8');
                    debug('Decoded ViewState:', decodedValue);

                    const viewstate_sensitive_keys = [
                        // Credentials
                        'password', 'pass', 'passwd', 'pwd', 'secret',
                        'access_token', 'refresh_token',
                        'apikey', 'api_key', 'bearer',
                        'client_secret', 'client_id', 'consumer_key', 'consumer_secret',

                        // Session IDs
                        'sessionid', 'jsessionid', 'phpsessid', 'sid',

                        // Cloud Access
                        'aws_access_key_id', 'aws_secret_access_key', 'aws_session_token',
                        'azure_client_secret', 'azure_client_id', 'azure_tenant_id',
                        'gcp_private_key', 'gcp_client_email',
                        'slack_token', 'github_token', 'google_token',
                        'aws_key', 'aws_secret', 'azure_key', 'gcp_key',

                        // Financial
                        'creditcard', 'ccnum', 'cardnumber', 'cvc', 'cvv',
                        'iban', 'accountnumber', 'routingnumber', 'pan',

                        // MFA / Challenge
                        'otp', 'pin', '2fa', 'mfa',
                        'securityquestion', 'securityanswer', 'challenge', 'totp',

                        // Cryptographic Secrets
                        'private_key', 'rsa', 'dsa', 'pem', 'cert', 'keystore', 'pfx'
                    ];

                    const findings = [];

                    // 1. URL-encoded
                    const urlEncodedRegex = /\b([a-zA-Z0-9_]+)=([^&\s]+)/g;
                    let match;
                    while ((match = urlEncodedRegex.exec(decodedValue)) !== null) {
                        const key = match[1].toLowerCase();
                        const value = match[2];
                        if (viewstate_sensitive_keys.includes(key)) {
                            findings.push(`${key}=${value}`);
                        }
                    }

                    // 2. JSON
                    const jsonRegex = /"([^"]+)":"([^"]+)"/g;
                    while ((match = jsonRegex.exec(decodedValue)) !== null) {
                        const key = match[1].toLowerCase();
                        const value = match[2];
                        if (viewstate_sensitive_keys.includes(key)) {
                            findings.push(`${key}=${value}`);
                        }
                    }

                    // 3. XML
                    const xmlRegex = /<([^>]+)>([^<]+)<\/\1>/g;
                    while ((match = xmlRegex.exec(decodedValue)) !== null) {
                        const key = match[1].toLowerCase();
                        const value = match[2];
                        if (viewstate_sensitive_keys.includes(key)) {
                            findings.push(`${key}=${value}`);
                        }
                    }

                    // 4. Object style
                    const objectRegex = /{([^}]+)}/g;
                    while ((match = objectRegex.exec(decodedValue)) !== null) {
                        const content = match[1];
                        const keyValueMatches = content.match(/([^:]+):\s*([^,]+)/g);
                        if (keyValueMatches) {
                            keyValueMatches.forEach(kv => {
                                const [key, value] = kv.split(':').map(s => s.trim());
                                if (viewstate_sensitive_keys.includes(key.toLowerCase())) {
                                    findings.push(`${key}=${value}`);
                                }
                            });
                        }
                    }

                    // 5. Type declaration
                    const typeRegex = /__type=([^:]+):([^&]+)&([^&]+)/g;
                    while ((match = typeRegex.exec(decodedValue)) !== null) {
                        const typeInfo = match[0];
                        if (viewstate_sensitive_keys.some(key => typeInfo.toLowerCase().includes(key))) {
                            findings.push(typeInfo);
                        }
                    }

                    // 6. Label-style
                    const labelRegex = /([a-zA-Z0-9_]+):\s*([^\n]+)/g;
                    while ((match = labelRegex.exec(decodedValue)) !== null) {
                        const key = match[1].toLowerCase();
                        const value = match[2].trim();
                        if (viewstate_sensitive_keys.includes(key)) {
                            findings.push(`${key}=${value}`);
                        }
                    }

                    // 7. HTML input tag value
                    const htmlInputRegex = /<input[^>]+value=['"]([^'"]+)['"][^>]*>/g;
                    while ((match = htmlInputRegex.exec(decodedValue)) !== null) {
                        const inputTag = match[0];
                        if (viewstate_sensitive_keys.some(key => inputTag.toLowerCase().includes(key))) {
                            findings.push(inputTag);
                        }
                    }

                    // 8. JWT
                    const jwtRegex = /(eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+)/g;
                    const jwtMatches = decodedValue.match(jwtRegex);
                    if (jwtMatches) {
                        findings.push(jwtMatches[0]);
                    }

                    if (findings.length > 0) {
                        const vulnerabilityDetails = `Sensitive Data in Unencrypted ViewState Detected:\n${findings.join('\n')}`;

                        this.addVulnerabilitytoResult(
                            attack,
                            this.vulnerabilityID,
                            vulnerabilityDetails
                        );

                        pluginDataForRequest.viewStateVulnFound = true;
                    }
                } catch (decodeError) {
                    debug('Error decoding ViewState:', decodeError);
                }
            }
        } catch (e) {
            debug('Error in processAttackResponse:', e);
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID !== this.vulnerabilityID) return;

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', 'param', [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', 'param', ['statusCode']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', 'text', ['VIEWSTATE', 'viewstate']);
    }
}

module.exports = UnencryptedViewState;
