const querystring = require('querystring')
const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = 'FormEncodedPost'

// Delegate that can iterate form encoded POSTs and update name-value pairs
// Later, it will have the ability to re-encode values by detecting encoding type.
// Starting with : www.xyz.com, post body = id=5&sid=98
//  iterate all parameters, attacking values 
//  extra parameter, attack value: post body = haiku[rnd]=<Attack>
//  extra parameter, attack param name: post body = <Attack>=haiku[rnd]
class FormEncodedPost extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    static isFormEncodedPostRequest(httpRequest) {
        return httpRequest.method == 'POST' &&
            (_.get(httpRequest, "headers['Content-Type']", '').includes('application/x-www-form-urlencoded') ||
            _.get(httpRequest, "headers['content-type']", '').includes('application/x-www-form-urlencoded')) &&
            _.get(httpRequest, 'body.length') > 0
    }


    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore, options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType, options)
        this.postBody = querystring.parse(request.httpRequest.body)
        this.originalPostbody = _.cloneDeep(this.postBody)
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    * getIterator() {
        // iterate through all params
        for (let param in this.postBody) {
            yield {
                name: param,
                val: this.postBody[param],
                resetRequired: false
            }
        }

        // yield the extra param 
        if (this.options.addExtraParam) {
            yield {
                name: this.extraParam,
                val: this.extraParam,
                resetRequired: false
            }
        }

        // attack the parameter name 
        if (this.options.attackParamName) {
            this.attackType = 'name'
            yield {
                name: this.extraParam,
                val: this.extraParam,
                resetRequired: true
            }
        }
    }

    modifyParam(param, value) {
        if (this.attackType == 'name') {
            // attack parameter name so param & value meanings are reversed
            this.postBody[value] = param
        } else {
            this.postBody[param] = value
        }
    }

    // get the modified request
    getHttpRequest(encoding) {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)
        let options = {}
        if (encoding == 'raw') {
            options = {
                encodeURIComponent: uri => uri
            }
        }
        req.body = querystring.stringify(this.postBody, null, null, options)
        return req
    }

    // reset post body
    reset() {
        this.postBody = _.cloneDeep(this.originalPostbody)
    }
}

module.exports = FormEncodedPost