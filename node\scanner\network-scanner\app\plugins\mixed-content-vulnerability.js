const NetworkAttack = require('./network-attack')
const _ = require('lodash')
// const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Mixed content occurs when a secure HTTPS page includes resources (like images, scripts, or stylesheets) that are loaded over an insecure HTTP connection. 
 **/

class MixedContent extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.activeVulnerabilityID = 'ID-active-mixed-content-vulnerability'
        this.passiveVulnerabilityID = 'ID-passive-mixed-content-vulnerability'
        this.mixedcontentvulnID = 'ID-mixed-content-vulnerability'
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let activelist = []
        let passivelist = []
        let mixedlist = []

        // Skip if URL contains docs
        if (attack.href.toLowerCase().includes('/docs/') || 
            attack.href.toLowerCase().includes('/documentation/')) {
            return;
        }

        if (attack.href.startsWith("https")) {
            let CSPheader = _.get(attack, 'result.resp.httpResponse.headers["content-security-policy"]', false)
            if (/upgrade-insecure-requests/i.test(CSPheader)) { return }
            let ResBody = _.get(attack, "result.resp.body", '')
            
            // Skip if response contains specific URLs
            if (ResBody.includes('http://www.entrust.') || 
                ResBody.includes('http://demo.testfire.net')) {
                return;
            }

            if (!ResBody.includes("http://")) { return }
            // Remove comments
            ResBody = ResBody.replace(/<!--[\s\S]*?-->/g, '');
            let CollectURLs = ResBody.match(/<[^>]+=\s*['"]?http:\/\/[^>]+>|@import\surl\([^\)]+http:\/\/[^\)]+\)/g)
            if (CollectURLs != null) {
                for (let val of CollectURLs) {
                    if (val.includes("http://") && !/haiku|lang=["']http|xml:lang|xmlns[:=]|<p>|data-url="http|content=.*http:|var url\s*=\s*"http:|<input|value\s*=.*http:/i.test(val)) {
                        if (/http:\/\/.+?\.(?:js|css|html?|xml|swf|class|jar|json|wasm|svg|php|aspx?)(?!\.)/i.test(val) && activelist.length < 2 && !pluginDataForRequest.activelistVulnFound) {
                            activelist.push(val)
                        }
                        else if (/http:\/\/.+?\.(?:jpe?g|png|gif|bmp|webp|ico|mp3|wav|ogg|mp4|webm|avi|mov|mkv|pdf|txt|docx?|xlsx?|pptx?)/i.test(val) && passivelist.length < 2 && !pluginDataForRequest.passivelistVulnFound) {
                            passivelist.push(val)
                        }
                        else if (/\b(?:href|src|data|code(?:base)?|classid|archive|manifest|poster|cite|background|formaction|icon|longdesc|profile|usemap|ping|srcset)\s*=\s*["']http:\/\/[^"']*["']|@import\s+url\([^\)]+http:\/\/[^\)]+\)|http-equiv\s*=\s*['"]refresh/i.test(val) && mixedlist.length < 2 && !pluginDataForRequest.mixedlistVulnFound) {
                            mixedlist.push(val)
                        }
                    }
                }
                if (activelist.length > 0) {
                    this.activelistVulnFound(attack, activelist)
                }
                if (passivelist.length > 0) {
                    this.passivelistVulnFound(attack, passivelist)
                }
                if (mixedlist.length > 0) {
                    this.mixedlistVulnFound(attack, mixedlist)
                }
            }
        }
    }

    activelistVulnFound(attack, activelist) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.activelistVulnFound) {
            return
        }

        let vulns = {
            details: activelist
        }
        this.addVulnerabilitytoResult(attack, this.activeVulnerabilityID, vulns)
        activelist = []
        vulns = {}
        pluginDataForRequest.activelistVulnFound = true
    }
    passivelistVulnFound(attack, passivelist) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.passivelistVulnFound) {
            return
        }

        let vulns = {
            details: passivelist
        }
        this.addVulnerabilitytoResult(attack, this.passiveVulnerabilityID, vulns)
        passivelist = []
        vulns = {}
        pluginDataForRequest.passivelistVulnFound = true
    }
    mixedlistVulnFound(attack, mixedlist) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.mixedlistVulnFound) {
            return
        }

        let vulns = {
            details: mixedlist
        }
        this.addVulnerabilitytoResult(attack, this.mixedcontentvulnID, vulns)
        mixedlist = []
        vulns = {}
        pluginDataForRequest.mixedlistVulnFound = true
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.activeVulnerabilityID && vulnID != this.passiveVulnerabilityID && vulnID != this.mixedcontentvulnID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
    }
}
module.exports = MixedContent