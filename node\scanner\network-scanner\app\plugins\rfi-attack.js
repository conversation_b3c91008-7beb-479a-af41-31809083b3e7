const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class RfiAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.RfivulnerabilityID = 'ID-remote-file-inclusion'
        this.UnvalidatedRedirectionID = 'ID-unvalidated-redirection'
    }

    getAttackVectors() {
        return RfiVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers', 'json-body']
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (attack.pluginName == this.getName()) {
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects', '')
            if (!pluginDataForRequest.forwordFound && redirects.length > 0) {
                let reirectURI = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', false)
                if (reirectURI && /https?:/i.test(reirectURI)) {
                    let uriRaw2 = new URL(reirectURI)
                    if (/was.indusface.com/i.test(uriRaw2.hostname)) {
                        let details = `Redirected URL: ${reirectURI}`
                        this.addVulnerabilitytoResult(attack, this.UnvalidatedRedirectionID, details)
                        pluginDataForRequest.forwordFound = true
                        return
                    }
                }
            }
            else if (!pluginDataForRequest.RFIFound) {
                this.checkBodyForVuln(attack, /bddee2cc027b267b395c1499534ee6e6/g, this.RfivulnerabilityID)
                pluginDataForRequest.RFIFound = true
            }
        }
    }
}

const RfiVector = [
    `https://was.indusface.com/rfi.txt`,
    `//was.indusface.com`
]

module.exports = RfiAttack