const NetworkAttack = require('./network-attack')
const tls = require('tls');
const _ = require('lodash')

class oldCipherVuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.oldCipherVulnerabilityID = 'ID-old-ciphers-found'
    }

    async processAttackResponse(attack) {
        //Verify below only once per scan
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (attack.attackArea != "original-crawler-request") {
            return
        }

        let parsedUrl = new URL(attack.httpRequest.uri)
        if (parsedUrl.protocol != 'https:') {
            return
        }

        //if vuln detected for a req then return
        if (pluginDataForRequest.oldCiphers) {
            return
        }
        // List of all ciphers supported by OpenSSL
        // const cipher = tls.getCiphers();
        let protocolList = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2']
        //Find protocol
        let protocol = []
        for (let val of protocolList) {
            const options = {
                host: attack.hostname, // Replace with the target host
                port: 443, // Standard HTTPS port
                method: 'GET',
                rejectUnauthorized: false, // Allow self-signed certificates
                // You can specify the TLS version here
                minVersion: val,
                maxVersion: val,
                servername: attack.hostname, // This is where you specify the SNI
                // ciphers: ele
            };
            let sslInfo = await this.alldts(options)
            if (sslInfo && sslInfo != 'Not Found') {
                protocol.push(val)
            }
        }
        // let protocol = ['SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', 'TLSv1.3']
        let certInfo = []
        if (protocol.length > 0) {
            for (let val of protocol) {
                if (val === 'SSLv3') {
                    let count = 0
                    let cipher = [
                        'SSL_RSA_WITH_3DES_EDE_CBC_SHA',
                        'SSL_RSA_WITH_DES_CBC_SHA',
                        'SSL_RSA_WITH_RC4_128_MD5',
                        'SSL_RSA_WITH_RC4_128_SHA',
                        'TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_DES_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_EXPORT_WITH_DES40_CBC_SHA',
                        'TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5',
                        'TLS_RSA_EXPORT_WITH_RC4_40_MD5',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_RC2_CBC_MD5',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                    ]
                    let reason = [
                        'Uses 3DES, stronger than DES but still weak by modern standards.These weak cipher suites are vulnerable to heartbleed attack and Ticketbleed (CVE-2016-9244) and sweet32 vulnerability.',
                        'Uses DES with a 56-bit key, which is considered weak.These weak cipher suites are vulnerable to heartbleed attack and CCS (CVE-2014-0224)',
                        'Uses RC4 with a 128-bit key and MD5 for hashing, both weak. These weak cipher suites are vulnerable to heartbleed attack. Heartbleed (CVE-2014-0160)',
                        'Uses RC4 with a 128-bit key and SHA-1 for hashing, vulnerable.These weak cipher suites are vulnerable to heartbleed attack, CCS (CVE-2014-0224).',
                        'Uses DES with a 40-bit key and Diffie-Hellman key exchange. Vulnerable to TLS_FALLBACK_SCSV (RFC 7507)',
                        'Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Uses 3DES with Diffie-Hellman key exchange, still weak. Vulnerable to FREAK (CVE-2015-0204)',
                        'Uses DES, which is considered weak. Uses DES with a 56-bit key and Diffie-Hellman key exchange. Vulnerable to SWEET32 (CVE-2016-2183, CVE-2016-6329)',
                        'Uses 3DES with Elliptic Curve Diffie-Hellman, still weak. Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Vulnerable to LOGJAM (CVE-2015-4000)',
                        'Uses RC4 with a 128-bit key and Elliptic Curve Diffie-Hellman. Vulnerable to DROWN (CVE-2016-0800, CVE-2016-0703)',
                        'Uses DES with a 40-bit key, which is easily broken. These Weak cipher suites are vulnerable to heart bleed attacks. Heartbleed (CVE-2014-0160)',
                        'Uses RC2 with a 40-bit key and MD5 for hashing, both weak. These weak cipher suites are vulnerable to heartbleed attack and Secure denegotiation (RFC 5746) and POODLE, SSL (CVE-2014-3566)',
                        'Uses RC4 with a 40-bit key and MD5 for hashing, both weak. These weak cipher suites are vulnerable to ROBOT and Breach (CVE-2013-3587) and FREAK (CVE-2015-0204) attack',
                        'Uses AES with a 128-bit key, considered weak by modern standards. Vulnerable to BEAST (CVE-2011-3389)',
                        'Uses AES with a 256-bit key, considered weak by modern standards. Could leads to LUCKY13 (CVE-2013-0169)',
                        'Uses Camellia with a 128-bit key, considered weak. Could leads to Winshock (CVE-2014-6321)',
                        'Uses Camellia with a 256-bit key, considered weak. Could leads to RC4 (CVE-2013-2566, CVE-2015-2808)',
                        'Uses IDEA, which is not widely supported and considered less secure.',
                        'No encryption, only MD5 for hashing, which is insecure. these weak cipher suites are vulnerable to Secure Client-Initiated Renegotiation',
                        'No encryption, only SHA-1 for hashing, which is vulnerable. These weak cipher suites are vulnerable to CRIME, TLS (CVE-2012-4929), SLOTH Attack, ROBOT ataack.',
                        'Uses RC2, which is considered weak and outdated.',
                        'Reason: Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'Also uses RC4, which is considered insecure.',
                        'Uses SEED with CBC mode, vulnerable to Padding Oracle (CVE-2016-2107)',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1') {
                    let count = 0
                    let cipher = [
                        'SSL_RSA_WITH_3DES_EDE_CBC_SHA',
                        'SSL_RSA_WITH_DES_CBC_SHA',
                        'SSL_RSA_WITH_RC4_128_MD5',
                        'SSL_RSA_WITH_RC4_128_SHA',
                        'TLS_DHE_RSA_EXPORT_WITH_DES40_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_DES_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_NULL_SHA',
                        'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_NULL_SHA',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_EXPORT_WITH_DES40_CBC_SHA',
                        'TLS_RSA_EXPORT_WITH_RC2_CBC_40_MD5',
                        'TLS_RSA_EXPORT_WITH_RC4_40_MD5',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_IDEA_CBC_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_RC2_CBC_MD5',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                    ]
                    let reason = [
                        'Uses 3DES, stronger than DES but still weak by modern standards.These weak cipher suites are vulnerable to heartbleed attack and Ticketbleed (CVE-2016-9244) and sweet32 vulnerability.',
                        'Uses DES with a 56-bit key, which is considered weak.These weak cipher suites are vulnerable to heartbleed attack and CCS (CVE-2014-0224)',
                        'Uses RC4 with a 128-bit key and MD5 for hashing, both weak. These weak cipher suites are vulnerable to heartbleed attack. Heartbleed (CVE-2014-0160)',
                        'Uses RC4 with a 128-bit key and SHA-1 for hashing, vulnerable.These weak cipher suites are vulnerable to heartbleed attack, CCS (CVE-2014-0224).',
                        'Uses DES with a 40-bit key and Diffie-Hellman key exchange. Vulnerable to TLS_FALLBACK_SCSV (RFC 7507)',
                        'Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Uses 3DES with Diffie-Hellman key exchange, still weak. Vulnerable to FREAK (CVE-2015-0204)',
                        'Uses DES, which is considered weak. Uses DES with a 56-bit key and Diffie-Hellman key exchange. Vulnerable to SWEET32 (CVE-2016-2183, CVE-2016-6329)',
                        'Also uses 3DES, which is considered weak.',
                        'Also provides no encryption, only authentication.',
                        'Reason: Uses RC4, which is weak and susceptible to biases1.',
                        'Uses 3DES with Elliptic Curve Diffie-Hellman, still weak. Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Vulnerable to LOGJAM (CVE-2015-4000)',
                        'Provides no encryption, only authentication.',
                        'Uses RC4 with a 128-bit key and Elliptic Curve Diffie-Hellman. Vulnerable to DROWN (CVE-2016-0800, CVE-2016-0703)',
                        'Uses DES with a 40-bit key, which is easily broken. These Weak cipher suites are vulnerable to heart bleed attacks. Heartbleed (CVE-2014-0160)',
                        'Uses RC2 with a 40-bit key and MD5 for hashing, both weak. These weak cipher suites are vulnerable to heartbleed attack and Secure denegotiation (RFC 5746) and POODLE, SSL (CVE-2014-3566)',
                        'Uses RC4 with a 40-bit key and MD5 for hashing, both weak. These weak cipher suites are vulnerable to ROBOT and Breach (CVE-2013-3587) and FREAK (CVE-2015-0204) attack',
                        'Uses AES with a 128-bit key, considered weak by modern standards. Vulnerable to BEAST (CVE-2011-3389)',
                        'Uses AES with a 256-bit key, considered weak by modern standards. Could leads to LUCKY13 (CVE-2013-0169)',
                        'Uses Camellia with a 128-bit key, considered weak. Could leads to Winshock (CVE-2014-6321)',
                        'Uses Camellia with a 256-bit key, considered weak. Could leads to RC4 (CVE-2013-2566, CVE-2015-2808)',
                        'Uses IDEA, which is not widely supported and considered less secure.',
                        'No encryption, only MD5 for hashing, which is insecure. these weak cipher suites are vulnerable to Secure Client-Initiated Renegotiation',
                        'No encryption, only SHA-1 for hashing, which is vulnerable. These weak cipher suites are vulnerable to CRIME, TLS (CVE-2012-4929), SLOTH Attack, ROBOT ataack.',
                        'Uses RC2, which is considered weak and outdated.',
                        'Reason: Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'Also uses RC4, which is considered insecure.',
                        'Uses SEED with CBC mode, vulnerable to Padding Oracle (CVE-2016-2107)',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress}\n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1.1') {
                    let count = 0
                    let cipher = [
                        'SSL_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_NULL_SHA',
                        'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_NULL_SHA',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                    ]
                    let reason = [
                        'Uses 3DES, stronger than DES but still weak by modern standards.These weak cipher suites are vulnerable to heartbleed attack and Ticketbleed (CVE-2016-9244) and sweet32 vulnerability.',
                        'Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Uses 3DES with Diffie-Hellman key exchange, still weak. Vulnerable to FREAK (CVE-2015-0204)',
                        'Also uses 3DES, which is considered weak.',
                        'Also provides no encryption, only authentication.',
                        'Reason: Uses RC4, which is weak and susceptible to biases1.',
                        'Uses 3DES with Elliptic Curve Diffie-Hellman, still weak. Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Vulnerable to LOGJAM (CVE-2015-4000)',
                        'Provides no encryption, only authentication.',
                        'Uses RC4 with a 128-bit key and Elliptic Curve Diffie-Hellman. Vulnerable to DROWN (CVE-2016-0800, CVE-2016-0703)',
                        'Uses AES with a 128-bit key, considered weak by modern standards. Vulnerable to BEAST (CVE-2011-3389)',
                        'Uses AES with a 256-bit key, considered weak by modern standards. Could leads to LUCKY13 (CVE-2013-0169)',
                        'Uses Camellia with a 128-bit key, considered weak. Could leads to Winshock (CVE-2014-6321)',
                        'Uses Camellia with a 256-bit key, considered weak. Could leads to RC4 (CVE-2013-2566, CVE-2015-2808)',
                        'No encryption, only MD5 for hashing, which is insecure. these weak cipher suites are vulnerable to Secure Client-Initiated Renegotiation',
                        'No encryption, only SHA-1 for hashing, which is vulnerable. These weak cipher suites are vulnerable to CRIME, TLS (CVE-2012-4929), SLOTH Attack, ROBOT ataack.',
                        'Reason: Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'Also uses RC4, which is considered insecure.',
                        'Uses SEED with CBC mode, vulnerable to Padding Oracle (CVE-2016-2107)',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
                if (val === 'TLSv1.2') {
                    let count = 0
                    let cipher = [
                        'SSL_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_DHE_RSA_WITH_AES_128_GCM_SHA256',
                        'TLS_DHE_RSA_WITH_AES_256_GCM_SHA384',
                        'TLS_ECDHE_ECDSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_ECDSA_WITH_NULL_SHA',
                        'TLS_ECDHE_ECDSA_WITH_NULL_SHA256',
                        'TLS_ECDHE_ECDSA_WITH_RC4_128_SHA',
                        'TLS_ECDHE_RSA_WITH_3DES_EDE_CBC_SHA',
                        'TLS_ECDHE_RSA_WITH_NULL_SHA',
                        'TLS_ECDHE_RSA_WITH_NULL_SHA256',
                        'TLS_ECDHE_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_AES_128_CBC_SHA',
                        'TLS_RSA_WITH_AES_256_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_128_CBC_SHA',
                        'TLS_RSA_WITH_CAMELLIA_256_CBC_SHA',
                        'TLS_RSA_WITH_NULL_MD5',
                        'TLS_RSA_WITH_NULL_SHA',
                        'TLS_RSA_WITH_RC4_128_MD5',
                        'TLS_RSA_WITH_RC4_128_SHA',
                        'TLS_RSA_WITH_SEED_CBC_SHA',
                    ]
                    let reason = [
                        'Uses 3DES, stronger than DES but still weak by modern standards.These weak cipher suites are vulnerable to heartbleed attack and Ticketbleed (CVE-2016-9244) and sweet32 vulnerability.',
                        'Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Uses 3DES with Diffie-Hellman key exchange, still weak. Vulnerable to FREAK (CVE-2015-0204)',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'These cipher suites use Diffie-Hellman Ephemeral (DHE) for key exchange, which is vulnerable to the Raccoon Attack due to the timing vulnerability in the Diffie-Hellman key exchange process',
                        'Also uses 3DES, which is considered weak.',
                        'Also provides no encryption, only authentication.',
                        'Also provides no encryption, only authentication.',
                        'Reason: Uses RC4, which is weak and susceptible to biases1.',
                        'Uses 3DES with Elliptic Curve Diffie-Hellman, still weak. Uses 3DES, which is vulnerable to meet-in-the-middle attacks. Vulnerable to LOGJAM (CVE-2015-4000)',
                        'Provides no encryption, only authentication.',
                        'Provides no encryption, only authentication.',
                        'Uses RC4 with a 128-bit key and Elliptic Curve Diffie-Hellman. Vulnerable to DROWN (CVE-2016-0800, CVE-2016-0703)',
                        'Uses AES with a 128-bit key, considered weak by modern standards. Vulnerable to BEAST (CVE-2011-3389)',
                        'Uses AES with a 256-bit key, considered weak by modern standards. Could leads to LUCKY13 (CVE-2013-0169)',
                        'Uses Camellia with a 128-bit key, considered weak. Could leads to Winshock (CVE-2014-6321)',
                        'Uses Camellia with a 256-bit key, considered weak. Could leads to RC4 (CVE-2013-2566, CVE-2015-2808)',
                        'No encryption, only MD5 for hashing, which is insecure. these weak cipher suites are vulnerable to Secure Client-Initiated Renegotiation',
                        'No encryption, only SHA-1 for hashing, which is vulnerable. These weak cipher suites are vulnerable to CRIME, TLS (CVE-2012-4929), SLOTH Attack, ROBOT ataack.',
                        'Reason: Uses RC4, which is weak and susceptible to biases, and MD5, which is cryptographically broken.',
                        'Also uses RC4, which is considered insecure.',
                        'Uses SEED with CBC mode, vulnerable to Padding Oracle (CVE-2016-2107)',
                    ]
                    // Options for the TLS connection
                    for (let ele of cipher) {
                        const options = {
                            host: attack.hostname, // Replace with the target host
                            port: 443, // Standard HTTPS port
                            method: 'GET',
                            rejectUnauthorized: false, // Allow self-signed certificates
                            // You can specify the TLS version here
                            minVersion: val,
                            maxVersion: val,
                            servername: attack.hostname, // This is where you specify the SNI
                            ciphers: ele
                        };
                        let sslInfo = await this.alldts(options)
                        if (sslInfo && sslInfo != 'Not Found' && sslInfo.Cipher.standardName === options.ciphers) {
                            certInfo.push({ result: `Server IP address: ${sslInfo.IPaddress} \n Protocol ${val} and weak cipher: ${ele} is supported. Details: ${reason[count]}` })
                        }
                        count++;
                    }
                }
            }
        }
        if (certInfo.length > 0) {
            this.addVulnerabilitytoResult(attack, this.oldCipherVulnerabilityID, certInfo)
            pluginDataForRequest.oldCiphers = true
        }
        else {
            // checked - avoid repeating the check
            pluginDataForRequest.oldCiphers = true
        }
    }

    alldts(options) {
        return new Promise((resolve) => {
            try {
                // Create a TLS connection
                const socket = tls.connect(options, () => {
                    // console.log('Connected to server');
                    const tlsInfo = {
                        // Certificate: socket.getPeerCertificate(),
                        // Version: socket.getProtocol(),
                        Cipher: socket.getCipher(),
                        IPaddress: socket.remoteAddress
                    }
                    socket.end();
                    resolve(tlsInfo)
                });

                // Handle errors
                socket.on('error', (err) => {
                    resolve('Not Found');
                });
            }
            catch (e) {
                resolve('Not Found')
            }
        });
    }
}

module.exports = oldCipherVuln