const debug = require('debug')('PossibleBackupFile')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class PossibleBackupFile extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-possible-backup-file'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            skipRoot: true,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    getAttackVectors() {
        return PBFVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**      
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        // check for content-type for mitigating FPs and fix '/' thing
        let uri = new URL(attack.href)
        let uripath = uri.pathname
        const excludeparam = '/' + attack.vector
        if (!uripath.includes(excludeparam)) {
            let stacode = attack.result.resp.httpResponse.statusCode
            if (stacode == 200) {
                if (attack.httpRequest.uri.indexOf('.php') != -1) {
                    if ((/<\?/).test(attack.result.resp.body) || (/\?>/).test(attack.result.resp.body)) {
                        let details = { result: attack.httpRequest.uri }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        return
                    }
                }
                if (attack.httpRequest.uri.indexOf('.jsp') != -1) {
                    if ((/<%@ page import/).test(attack.result.resp.body) || (/<%/).test(attack.result.resp.body)) {
                        let details = { result: attack.httpRequest.uri }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        return
                    }
                }
                if (attack.httpRequest.uri.indexOf('.asp') != -1) {
                    if ((/dim /).test(attack.result.resp.body) || (/<%/).test(attack.result.resp.body)) {
                        let details = { result: attack.httpRequest.uri }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        return
                    }
                }
                if (attack.httpRequest.uri.indexOf('.cs') != -1) {
                    if ((/using System.Web.UI;/).test(attack.result.resp.body) || (/using System.Web/).test(attack.result.resp.body) || (/public partial class/).test(attack.result.resp.body) || (/protected void Page_Load/).test(attack.result.resp.body) || (/System.Web.UI.Page/).test(attack.result.resp.body)) {
                        let details = { result: attack.httpRequest.uri }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        return
                    }
                }
                if (attack.httpRequest.uri.indexOf('.vb') != -1) {
                    if ((/Inherits System.Web.UI;/).test(attack.result.resp.body) || (/Inherits System.Web/).test(attack.result.resp.body) || (/Partial Class/).test(attack.result.resp.body) || (/Protected Sub Page_Load/).test(attack.result.resp.body)) {
                        let details = { result: attack.httpRequest.uri }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        return
                    }
                }

            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

// `.gz`,`.tar.gz`,`.bzip`,`.7z`,`.tar`,`.tgz`,`.bzip`,`.bzip2`,`.zip`,`.ZIP`,`.rar` are the vectors to download zip.
// vectors & matches ...
const PBFVectors = [
    `.bkp`,
    `.bak`,
    `.BAK`,
    `.backup`,
    `.OLD`,
    `.old`,
    `.temp`,
    `.tmp`,
    `.inc`,
    `.copy`,
    `.swp`,
    `~`,
    `.1`,
    `.~1~`,
    `.cs`,
    `.vb`,
    '.gz',
    '.tar.gz',
    '.bzip',
    '.7z',
    '.tar',
    '.tgz',
    '.bzip',
    '.bzip2',
    '.zip',
    '.ZIP',
    '.rar',
    '.RAR',
]

module.exports = PossibleBackupFile