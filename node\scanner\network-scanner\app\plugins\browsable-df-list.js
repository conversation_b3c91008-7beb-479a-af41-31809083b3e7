const VectorResponseAttack = require('./vector-response-attack')
// const NetworkAttack = require('./network-attack')
const _ = require('lodash')
// const http = require('http')
// const https = require('https');
fs = require('fs')
const URL = require('url').URL

class BrowsableDirFile extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // this.vulnerabilityID = 'ID-sensitive-file-directory'
        this.vulnerabilityID = 'ID-browsable-web-directory'
        this.backupvulnerabilityID = 'ID-archive-backup-file'
        this.possibleBackupFilevulnerabilityID = 'ID-possible-backup-file'
        this.coreDumpFileID = 'ID-core-dump-file'
        this.PSCvulnerabilityID = 'ID-possible-sensitive-file-or-directory'
    }

    getAttackVectors() {
        // AttackVectors = AttackVectors
        return DLAttackVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        if (parameterizedDeletage.getParameterType() == 'BaseURI') {
            parameterizedDeletage.setOptions({
                alwaysIterateEnd: true,
                clearQueryParams: true,
                skipRoot: false,
                maxPathComponents: 3,
                addSlashBeforeAttack: true,
                haveSlashAfterAttack: 'always',
                replaceValue: true
            });
        }
    }

    async performNetworkAttack(attack) {
        if (!/\b\/\w+\.\w+(?:\/|$|\?)/.test(attack.httpRequest.uri)) {
            let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
            let uriRaw = new URL(attack.httpRequest.uri)
            if (pluginStorageScanScope.Browsable && pluginStorageScanScope.Browsable.includes(uriRaw.pathname)) {
                return false
            }
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let uri = new URL(attack.href)
        let uripath = uri.pathname
        if (pluginStorageScanScope.Browsable && pluginStorageScanScope.Browsable.includes(uripath)) {
            return false
        }
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (!/\b\/\w+\.\w+(?:\/|$|\?)/.test(attack.href) && statusCode == 200) {
            let host = attack.hostname
            const patternindex = new RegExp("<(h1|title)> ?Index of +" + uripath + "?.*?</(h1|title)>", 'i');
            const patternhost = new RegExp("<(h1|title)> ?" + host + "[ -]*" + uripath + "?.*?</(h1|title)>", 'i');
            let ResBody = attack.result.resp.body.replace(/\r\n/g, '')
            if ((patternindex.test(ResBody) || patternhost.test(ResBody)) && />(?:\[To | )?Parent Directory\]?</i.test(ResBody)) {
                return true
            }
        }
        return false
    }

    async processAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let uriRaw = new URL(attack.href)

        if (pluginStorageScanScope.Browsable && pluginStorageScanScope.Browsable.includes(uriRaw.pathname)) {
            return
        }
        let ResBody = _.get(attack, "result.resp.body")
        /* let uriRaw = new URL(attack.href)
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
        let newresponse = []
        let uripath = uriRaw.pathname
        let hostname = uriRaw.hostname
        let protocol = uriRaw.protocol
        let details = []
        let data = []
        let currhref = uriRaw.href */
        if (/>(?:\[To | )?Parent Directory\]?</i.test(ResBody)) {
            let filedts = []
            let list = []
            const regexparchive = /[\w\-\.]+\.(?:gz|tar\.gz|bzip|7z|tar|tgz|bzip|bzip2|zip|rar|7z)/ig
            const regexpbackup = /[\w\-\.]+\.(?:bkp|bak|backup|old|temp)/ig
            const regexpcoreDump = /[\w\-\.]+\.(?:dmp|elf|out)/ig
            const regexptr = /<tr>.+?<\/tr>/gi
            const regexppre = /<pre>[\w\W]+?<\/pre>/gi
            const regexpli = /<li>.+?<\/li>/gi
            const regexphref = /<a href=\"[\w\.\-\/]+\">[\w\.\- \/]+?<\/a/gi
            const regexpFrDName = />[\w\.\- \/]+</gi
            const regexpDateTime = /> *[\w\d\-\/]+ +\d+:\d/i
            const regexplog = /[\w\-\.]+\.(?:log(\d*\.txt)?|evtx)/ig
            try {
                let matchedtr = ResBody.match(regexptr)
                let matchedpre = ResBody.match(regexppre)
                let matchedli = ResBody.match(regexpli)
                if (matchedtr || matchedpre || matchedli) {
                    if (matchedtr && /^<tr>/i.test(matchedtr) && matchedtr.length > 0) {
                        let filecount = 0
                        for (let currValue of matchedtr) {
                            if (/<a href=\"[\/\w]/i.test(currValue)) {
                                if (filecount == 0 && /> ?Parent Directory</i.test(currValue)) {
                                    filecount++
                                }
                                else if (filecount == 1 && regexpDateTime.test(currValue)) {
                                    list = matchedtr.toString().match(regexphref).toString().match(regexpFrDName).toString().replace(/>|</g, "").split(',')
                                    filedts = { result: "Directory Listing Found:\n" + list.slice(1,) }
                                    break
                                }
                            }
                        }
                    }
                    else if (matchedpre && /^<pre>/i.test(matchedpre) && matchedpre.length > 0) {
                        for (let currValue of matchedpre) {
                            if (/<a href=\"/i.test(currValue) && />(?:\[To | )?Parent Directory\]?</i.test(currValue) && regexpDateTime.test(currValue)) {
                                list = matchedpre.toString().match(regexphref).toString().match(regexpFrDName).toString().replace(/>|</g, "").split(',')
                                filedts = { result: "Directory Listing Found:\n" + list.slice(1,) }
                                break
                            }
                        }
                    }
                    else if (matchedli && /^<li>/i.test(matchedli) && matchedli.length > 0) {
                        let filecount = 0
                        for (let currValue of matchedli) {
                            if (filecount == 0 && /<a href=\"[\/\w]/i.test(currValue) && /> ?Parent Directory</i.test(currValue)) {
                                filecount++
                            }
                            else if (filecount == 1 && /<li><a href=\"/i.test(currValue)) {
                                list = matchedli.toString().match(regexphref).toString().match(regexpFrDName).toString().replace(/>|</g, "").split(',')
                                filedts = { result: "Directory Listing Found:\n" + list.slice(1,) }
                                break
                            }
                        }
                    }

                    if (filedts.length == 0) {
                        filedts = { result: "Directory Listing Vuln Exist." }
                    }

                    if (filedts.result.length > 0) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, filedts)
                        let Dirlist = []
                        if (pluginStorageScanScope.Browsable && pluginStorageScanScope.Browsable.length > 0) {
                            Dirlist = pluginStorageScanScope.Browsable
                        }
                        Dirlist.push(uriRaw.pathname)
                        pluginStorageScanScope.Browsable = Dirlist
                        let archiveList = []
                        let backupList = []
                        let coreDumpList = []
                        let loglist = []
                        list.forEach(el => {
                            if (regexparchive.test(el)) {
                                archiveList.push(el)
                            }
                            if (regexpbackup.test(el)) {
                                backupList.push(el)
                            }
                            if (regexpcoreDump.test(el)) {
                                coreDumpList.push(el)
                            }
                            if (regexplog.test(el)) {
                                loglist.push(el)
                            }
                            if (/[\w\-\.]+\.txt/i.test(el) && attack.href.includes('log')) {
                                loglist.push(el)
                            }
                        })
                        if (archiveList.length > 0) {
                            let vuln = { result: "File Found: \n" + archiveList.slice(0, 10).join(', ') }
                            this.addVulnerabilitytoResult(attack, this.backupvulnerabilityID, vuln)
                        }
                        if (backupList.length > 0) {
                            let vuln = { result: "File Found: \n" + backupList.slice(0, 10).join(', ') }
                            this.addVulnerabilitytoResult(attack, this.possibleBackupFilevulnerabilityID, vuln)
                        }
                        if (coreDumpList.length > 0) {
                            let vuln = { result: "File Found: \n" + coreDumpList.slice(0, 5).join(', ') }
                            this.addVulnerabilitytoResult(attack, this.coreDumpFileID, vuln)
                        }
                        if (loglist.length > 0) {
                            let vuln = { result: "File Found: \n" + loglist.slice(0, 5).join(', ') }
                            this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, vuln.result)
                        }
                    }
                }

            }
            catch (e) {
                return
            }
            /* if (list.length > 1) {
                data = `Scanned URL: ${currhref}. DIR List vuln Exist. List: ${list}`
                data = data.replace(/Parent Directory,/ig, '')
                details = data
 
            }
            else {
                data = `Scanned URL: ${currhref}. DIR List vuln Exist: Yes`
                data = data.replace(/Parent Directory,/gi, '')
                details = data
            }
            if (details.length > 0) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                return
            } */
        }
    }

    /* getResBody(url, protocol) {
        return new Promise((resolve) => {
            // let UpdatedURL = 'http://localhost/admin/test/'
            let UpdatedURL = url
            if (protocol == 'http:') {
                http.get(UpdatedURL, function (res) {
                    if (res.statusCode == 200) {
                        res.setEncoding('utf8');
                        res.on('data', function (chunk) {
                            resolve(chunk);
                        });
                    }
                    else {
                        resolve("HaikuTestSkip")
                    }
                });
            }
            else if (protocol == 'https:') {
                https.get(UpdatedURL, function (res) {
                    if (res.statusCode == 200) {
                        res.setEncoding('utf8');
                        res.on('data', function (chunk) {
                            resolve(chunk);
                        });
                    }
                    else {
                        resolve("HaikuTestSkip")
                    }
                });
            }
        })
    } */

    /* getResponse(UpdatedURL, protocol) {
        return new Promise((resolve) => {
            if (protocol == 'https:') {
                https.get(UpdatedURL, (resp) => {
                    if (resp.statusCode == 200) {
                        resolve('200')
                    }
                    else {
                        resolve('100')
                    }
                }).on("error", (err) => { resolve("error", err) });
            }
            else if (protocol == 'http:') {
                http.get(UpdatedURL, (resp) => {
                    if (resp.statusCode == 200) {
                        resolve('200')
                    }
                    else {
                        resolve('100')
                    }
                }).on("error", (err) => { resolve("error", err) });
            }
        })
    } */
}

// let DirectoryFound = []
let DLAttackVectors = []
let DirNameList = []
// let FileNameList = []
try {
    // DirNameList = fs.readFileSync('DirNameList.txt', 'utf8').split('\r\n')
    DirNameList = fs.readFileSync('./network-scanner/app/plugins/DirNameList.js', 'utf8').replace(/`/g, '').split('\r\n')
    DLAttackVectors = DirNameList
    // FileNameList = fs.readFileSync('./network-scanner/app/plugins/FileNameList.js', 'utf8').replace(/`/g, '').split('\r\n')
    if (DirNameList.length == 0) {
        DirNameList = [
            `admin`,
            `documents`,
            `ansari`,
            `test`,
            `ansaritest`,
            `test22`,
            `soap`,
            `js`,
            `db`,
            `fonts`,
            `passwords`,
        ]
        DLAttackVectors = DirNameList
    }
    /* if (FileNameList.length == 0) {
        FileNameList = [
            `phpinfo.php`,
            `install.php`,
            `robots.txt`,
            `login.php`,
            `ansa.php`,
            `index.php`,
            `settings.php`,
        ]
    } */
}
catch (err) {
    console.error(err);
    return
}
module.exports = BrowsableDirFile