let utils = require('../../ifc-utils.js')
const DateDiff = require('date-diff')
const URL = require('url').URL
const querystring = require('querystring');
const pluginName = 'defacementCheck'
const {
    session
} = require('electron');
const fs = require('fs');
const HaikuUtils = require('../../../common/lib/haiku-utils');
const ssAPI = require('../../../common/lib/sooper-scheduler-api');
const { type } = require('os');
const cheerio = require('cheerio');
const FingerPrint = require('../../../common/lib/fingerprint');
const path = require('path');
const _ = require('lodash');
const s3Utils = require('../../../common/lib/s3-utils')


class DefacementCheck {
    constructor(scanner) {
        this.currentVersion = '1.0.0';
        this.scanner = scanner
        this.config = scanner.config
        this.uniqueStates = 0;
        this.olderDate = null;
        this.isDateRangeCrossed = false;
        this.customerThreshold = this.config.customerThreshold;
        this.maxPageFailedCount = this.config.maxPageFailedCount;
        this.maxSpreadDiff = 0.5;       // 50%
        this.changedParam = [];
        this.similarityChange = {};

        this.scoreWeightage = {
            depth: 5,
            externalLinks: 5,
            internalLinks: 1,
            externalJS: 5,
            internalJS: 1,
            imgTag: 10,
            audioTag: 10,
            videoTag: 10,
            scriptTag: 5,
            styleTag: 1,
            structureSimilarity: 50,
            contentSimilarity: 20
        }

        this.totalScoreWeightage = _.sum(Object.values(this.scoreWeightage));

        // init counters and register for scan/crawl events
        scanner.on('scan-start', this.onScanStart.bind(this))

        scanner.on('got-interesting-items', this.collectDomInfo.bind(this));

        scanner.on('scan-loop-done', this.onScanDone.bind(this))
    }

    createDefacementObject() {
        return { "bookmarks": {}, "fingerprints": {}, "full": {}, "raw": {}, "urlInfos": {}, "defacementDetails": {}, "version": this.currentVersion };
    }

    async onScanStart() {
        // load from S3
        if (!_.isEmpty(this.scanner.defacementHistory) && !_.isEmpty(this.scanner.defacementHistory.raw)) {
            let version = this.scanner.defacementHistory.version;

            if(version) {
                this.currentVersion = version;
                this.prepareActionList();
            }
            else {
                this.scanner.defacementHistory = this.createDefacementObject();
            }
        }
        else {
            this.scanner.defacementHistory = this.createDefacementObject();
        }

        this.scanner.defacementHistory.urls = [];
        this.scanner.defacementHistory.urlInfos = {};
        this.scanner.defacementHistory['defacementDetails'] = {};
    }

    async prepareActionList() {
        let actionListCount = 0;
        let bookmarkPages = Object.keys(this.scanner.defacementHistory.bookmarks);
        let bookmarkActions = [];

        try {
            /** Collect action List */
            bookmarkPages.forEach(page => {

                //IMP
                let actionsList = this.scanner.crawlState.getActionsForBookmark(this.scanner.defacementHistory.bookmarks[page]);
                bookmarkActions.push(actionsList)
                actionListCount++;
            });
        } catch (error) {
            utils.log("Error : prepareActionList -> serializedDataToObject: ", error);
        }

        if (actionListCount > 0) {
            this.scanner.crawlState.resetCrawlTree(bookmarkActions);   // IMP
        }
    }

    prepareDatewise() {
        // remove from raw
        let removedDates = null;

        if (_.has(this.scanner.defacementHistory, "raw")) {
            (Object.keys(this.scanner.defacementHistory.raw)).forEach(key => {
                if (Object.keys(this.scanner.defacementHistory.raw[key]).length <= 7)
                    return
                if (_.has(this.scanner.defacementHistory.raw, key)) {
                    let dates = Object.keys(this.scanner.defacementHistory.raw[key]); // for each url having more than 7 daays data
                    dates.sort((a, b) => { var aa = b, bb = a; return aa < bb ? -1 : (aa > bb ? 1 : 0); });
                    removedDates = dates[dates.length - 1];

                    if (this.scanner.defacementHistory.raw[key][removedDates]) {
                        delete this.scanner.defacementHistory.raw[key][removedDates]
                    }
                }
            });
        }
    }

    async collectDomInfo(_, interestingItems, crawlState) {   // onUniqueState
        let location = interestingItems.location
        
        let statusInfo = this.scanner.statusInfo[location]; 
        if (!statusInfo)
            return;
        //a 301 redirect indicates that a page has permanently moved to a new location,
        if (statusInfo.statusCode >= 400) { // first time never ever consider 400 errored page
            if (!this.scanner.defacementHistory.raw[location])
                return;
        }

        this.scanner.statusInfo = {};
        this.scanner.defacementHistory.urlInfos[location] = statusInfo; 
       
        if (this.scanner.defacementHistory.urls.length >= this.config.maxUniqueStates) {
            this.finalizeData();
            utils.log(`aborting ${this.config.scanType} crawl since crawler reached maxUniqueStates. uniqueStates ${this.scanner.defacementHistory.urls.length} >= maxUniqueStates = ${this.config.maxUniqueStates}`)
            this.scanner.emit('stop-automated-crawl', `${this.config.scanType}: max unique states exceeded`)
            return;
        }

        // scan will either wnd with 10 urls hit,max time over or site scanned full even having less than 10 pages
        if (this.scanner.defacementHistory.urls.includes(location)) {
            return;
        }
        else {
            // check with raw and then push move 400 code here
            // if already in raw and 400 error then still increment and send error message
            this.scanner.defacementHistory.urls.push(location);    // add only status code is 200- move this to >= 400 else part
        }
        
        try {
            let haikuKey = location;            
            let currentObject = {};
            currentObject.scanId = this.config.scanId;
            currentObject.scanLogId = this.config.scanLogId;
            currentObject.depth = interestingItems.depth;
            currentObject.screenshotID = await this.screenshotToS3(interestingItems.serverData.screenshotID, this.getDateKey(), this.config.scanId, this.config.serviceId);

            let crawlerBookmark = crawlState.getCrawlerBookmark();
            this.scanner.defacementHistory.bookmarks[haikuKey] = JSON.parse(JSON.stringify(crawlerBookmark)); 

            let domString = interestingItems.document;
            currentObject.fingerprints = this.getFingerprints(domString);
            currentObject.structure = currentObject.fingerprints.structure['fingerPrintData'];
            currentObject.content = currentObject.fingerprints.content['fingerPrintData'];
            
            //first time then current and history will have same structure
            if (!this.scanner.defacementHistory.fingerprints[haikuKey]) {
                this.scanner.defacementHistory.fingerprints[haikuKey] = {
                    "structure": currentObject.structure,
                    "content": currentObject.content
                }
            }

            currentObject.structure = currentObject.fingerprints.structure['fingerPrintData'];
            currentObject.content = currentObject.fingerprints.content['fingerPrintData'];

            this.collectLinks(interestingItems, currentObject, haikuKey, statusInfo);
        }
        catch (err) {
            utils.log("Error : collectDomInfo method", err);
        }

    }

    async collectLinks(interestingItems, currentObject, haikuKey, statusInfo) {

        //check already exists
        currentObject['externalLinks'] = [];
        currentObject['internalLinks'] = [];
        currentObject['externalJS'] = [];
        currentObject['internalJS'] = [];
        currentObject['script'] = 0;
        currentObject['link'] = 0;

        currentObject['scriptTag'] = 0;
        currentObject['styleTag'] = 0;
        currentObject['imgTag'] = 0;
        currentObject['videoTag'] = 0;
        currentObject['audioTag'] = 0;

        //link items
        for (let index = 0; index < interestingItems.linkItems.length; index++) {
            this.checkExternal(interestingItems.linkItems[index][1].href, currentObject)
        }

        let collectionArr = ['script', 'link'];

        collectionArr.forEach(element => {
            this.collectElements(interestingItems.document, element, currentObject);
        });

        let externalSet = new Set(currentObject['externalLinks']);
        let internalSet = new Set(currentObject['internalLinks']);
        let externalJSSet = new Set(currentObject['externalJS']);
        let internalJSSet = new Set(currentObject['internalJS']);

        currentObject['externalLinks'] = Array.from(externalSet);
        currentObject['internalLinks'] = Array.from(internalSet);
        currentObject['externalJS'] = Array.from(externalJSSet);
        currentObject['internalJS'] = Array.from(internalJSSet);

        let date = this.getDateKey();

        // for reference unique urls only
        
        // check with lead for statically served paged having same ending as haiku key like it returns and does not processes
        // expected : http://testhtml5.vulnweb.com/#/about 
        // returning static page in status info: http://testhtml5.vulnweb.com/static/app/partials/about.html
        // which results in fail of sttement : let statusInfo = this.scanner.statusInfo[haikuKey]; 
       
        // for first scan full is empty
        if (!this.scanner.defacementHistory['full'][haikuKey]) {
            this.scanner.defacementHistory['full'][haikuKey] = {};
            this.scanner.defacementHistory['full'][haikuKey]['notAccessibleCount'] = 0;
            this.scanner.defacementHistory['full'][haikuKey]['lastCrawlDate'] = this.getDateKey();     // save todays date
        }

        let isValidForClear = false;
         // check page error
        if (statusInfo.statusCode >= 400) { 
            // same day count should only once incremented
            if (_.has(this.scanner.defacementHistory.raw, haikuKey) && Object.keys(this.scanner.defacementHistory.raw[haikuKey]).length > 1) {
                if (this.scanner.defacementHistory['full'][haikuKey]['lastCrawlDate'] !== this.getDateKey()) {
                    this.scanner.defacementHistory['full'][haikuKey]['notAccessibleCount']++;

                    isValidForClear = this.scanner.defacementHistory['full'][haikuKey]['lastCrawlDate'] < this.getDateKey();
                    if (this.scanner.defacementHistory['full'][haikuKey]['notAccessibleCount'] >= this.maxPageFailedCount && isValidForClear) {
                        this.clearUnaccesibleUrl(haikuKey);                        
                    }
                    else {
                        this.scanner.defacementHistory['full'][haikuKey]['lastCrawlDate'] = this.getDateKey();     // update date by today
                    }
                }

                // remove url entry from file
                // same day count should only once incremented
                // send only existing pages call for following error from second scan onward only
                let request = {
                    "scanid": this.config.scanId,
                    "scanlogid": this.config.scanLogId,
                    "key": haikuKey,
                    "founddate": new Date().toISOString(),
                    "scanner": "haiku",
                    "vulns": {
                        "ID-defacement": {
                            "foundby": this.constructor.name,
                            "productionReady": true,
                            "vulnerabilityid": 14, // check and verify             
                            "details": {
                                "title": "Page Error",
                                "description": `Page Error ${statusInfo.url}, status code ${statusInfo.statusCode}`,
                                "url": haikuKey,
                                "statusCode": statusInfo.statusCode
                            },
                        }
                    }
                };

                utils.log(`Calling from  Defacement : Page error ${statusInfo.url}, status code ${statusInfo.statusCode}`)
                await ssAPI.malwareVulnerabilityFound(request);
            }

            return;
            // utils.log(`Ignoring page for defacement matrix update ${statusInfo.url}, status code ${statusInfo.statusCode}`);
            // return;  // you can count here how many times such given page is allwed with error code replace with another page i.e remove it from full,raw and mattrix and add to ignorePages
        }
        else {
            // reset if page available after 1st day => available - unavailable - available ... so on
            if (this.scanner.defacementHistory['full'][haikuKey]['notAccessibleCount'] > 0) {
                this.scanner.defacementHistory['full'][haikuKey]['notAccessibleCount'] = 0;
                this.scanner.defacementHistory['full'][haikuKey]['lastCrawlDate'] = this.getDateKey();
            } 
        }  
       
        let processedData = this.processRawData(date, currentObject, this.scanner.defacementHistory, haikuKey);

        if(!isValidForClear)
            this.scanner.defacementHistory['full'][haikuKey] = _.merge(this.scanner.defacementHistory['full'][haikuKey], currentObject); // update full object
        this.scanner.defacementHistory = processedData;
    }

    // Processing wrt currentData
    processRawData(date, currentData, history, key) {
        this.changedParam = [];
        let previousDateOfKeyurl = "";
        let additionalInfo = [];

        if (_.has(this.scanner.defacementHistory, "raw") && _.has(this.scanner.defacementHistory.raw, key)) {
            let dates = Object.keys(this.scanner.defacementHistory.raw[key]);
            dates.sort((a, b) => { var aa = b, bb = a; return aa < bb ? -1 : (aa > bb ? 1 : 0); });
            this.olderDate = dates[dates.length - 1];
            previousDateOfKeyurl = dates[0];
        }
        try {
            let score = {};
            let _history = history;
            _history['raw'] = _.cloneDeep(history['raw']);

            if(!_history['raw'][key]){
                _history['raw'][key] = {},
                _history['fingerprints'][key] = {}
            }

            let rawDateKey, structureSimilarityValue, contentSimilarityValue;

            if (_history['fingerprints'][key]['structure'])
                structureSimilarityValue = ((this.similarity(currentData['structure'], _history['fingerprints'][key]['structure'])) * 100);
            else
                structureSimilarityValue = ((this.similarity(currentData['structure'], currentData['structure'])) * 100);

            if (_history['fingerprints'][key]['content'])
                contentSimilarityValue = ((this.similarity(currentData['content'], _history['fingerprints'][key]['content'])) * 100);
            else
                contentSimilarityValue = ((this.similarity(currentData['content'], currentData['content'])) * 100);


            rawDateKey = {
                "URL_KEY": key,
                "structureSimilarity": structureSimilarityValue,
                "contentSimilarity"  : contentSimilarityValue
            }

            let keyPair = ['internalJS', 'depth', 'externalJS', 'externalLinks', 'internalLinks',
                'script', 'link', "scriptTag", "styleTag", "imgTag", 
                "videoTag", "audioTag"];
            
           
            keyPair.forEach(pair => {
                let val = 0;
                Array.isArray(currentData[pair]) ? (val = currentData[pair].length) : (val = currentData[pair]);   // keyPair values in the form of digits only not type
                rawDateKey[pair] = val;

                if (Object.keys(_history.raw[key]).length >= 7) {
                    let prevMax = this.getMax(undefined, _history['raw'][key], pair);
                    let prevMin = this.getMin(undefined, _history['raw'][key], pair);

                    let _tempHistory = _.cloneDeep(_history['raw']);
                    delete _tempHistory[key][this.olderDate];      

                    //temp = _history['raw'][key] - previous date data;
                    let updatedMax = this.getMax(val, _tempHistory[key], pair);
                    let updatedMin = this.getMin(val, _tempHistory[key], pair);

                    let currMax = updatedMax || 0;
                    let currMin = updatedMin || 0;
                    // if 7 days exceeds

                    let currentSpread = currMax - currMin;  // 6
                    let prevSpread = prevMax - prevMin;  // 0
                    score[pair] = this.calculateScore(currentSpread, prevSpread, pair, additionalInfo);
                }

            });
            
            // merge following 2 isDateRangeCrossed blocks in one
            if (Object.keys(_history.raw[key]).length >= 7) {
                // structure and content similarity from last 7
                //7 days history/previous minimum for structureSimilarity and contentSimilarity
                let previousMinStructureSimilarity = this.getMinSimilarity(_history['raw'][key], 'structureSimilarity');
                let previousMinContentSimilarity = this.getMinSimilarity(_history['raw'][key], 'contentSimilarity');

                // Content Similarity and Structural similarity should also work based on baseline.                        5 days
                // rawDateKey['structureSimilarity'] = 85; // todays 
                // rawDateKey['structureSimilarity'] = 86;  // todays

                let percentStructureSimilarityChange = ((previousMinStructureSimilarity - rawDateKey['structureSimilarity']) / previousMinStructureSimilarity) * 100;
                let percentContentSimilarityChange = ((previousMinContentSimilarity - rawDateKey['contentSimilarity']) / previousMinContentSimilarity) * 100;

                if (percentStructureSimilarityChange > 5) {
                    score['structureSimilarity'] = this.calculateFingerprintScore(rawDateKey['structureSimilarity'], previousMinStructureSimilarity, 'structureSimilarity');
                }

                if (percentContentSimilarityChange > 5) {
                    score['contentSimilarity'] = this.calculateFingerprintScore(rawDateKey['contentSimilarity'], previousMinContentSimilarity, 'contentSimilarity');
                }
            }

            if (Object.keys(_history.raw[key]).length >= 7) {
                //TO DO: Calculate total score & check against sevarity.
                let scoreTotal = _.sum(Object.values(score));
                let scoreTotalPercentage = (scoreTotal / this.totalScoreWeightage) * 100;
                let currentThreshold = this.checkThreshold(parseFloat(scoreTotalPercentage.toFixed(2)));

                if (currentThreshold) { // high low,medium
                    _history.currentVariation = scoreTotalPercentage.toFixed(2);
                    if(additionalInfo.length) {
                        currentData.additionalInfo = additionalInfo;
                    }
                    this.defacementAlert(currentThreshold, currentData, this.scanner.defacementHistory['full'][key], key, _history.currentVariation, this.scanner.defacementHistory['raw'][key][previousDateOfKeyurl])
                }
                else {
                    if (scoreTotal > 0) {
                        utils.log("Appending defacementDetails object to haiku info for job-session complete");
                        this.scanner.defacementHistory.defacementDetails['pageDetails'] = {};
                        this.scanner.defacementHistory.defacementDetails['pageDetails'][key] = {};

                        let diffObject = this.generateDifferenceObject(currentThreshold, currentData, this.scanner.defacementHistory['full'][key], _history.currentVariation, this.scanner.defacementHistory['raw'][key][previousDateOfKeyurl])
                        this.scanner.defacementHistory.defacementDetails['pageDetails'][key] = diffObject;
                        this.scanner.defacementHistory.defacementDetails['pageDetails'][key]['score'] = scoreTotalPercentage;
                    }
                }
            }

            //assign todays data as 7th day data
            rawDateKey.screenshotID = currentData.screenshotID;
            _history['raw'][key][date] = rawDateKey;
            _history['fingerprints'][key]['structure'] = currentData.structure;
            _history['fingerprints'][key]['content'] = currentData.content;

            if (!_history) {
                console.log("");
            }
            return _history;
        } catch (error) {
            utils.log("Error : Defacement -> processRawData : ", error);
        }
        console.log('')
    }

    calculateFingerprintScore(current, previous, pair) {
        let oldestDate = this.olderDate;
        //TO DO: Refer WAS-308 - Content similarity should be checked on <body> and not on< head> tag. To avoid any FP for calcuating score
        // on head vs body data. This code has been addded. After one month we can remove this code because most of the defacement scan
        // will have proper page body data updated as fingerprint.
        if (this.olderDate) {
            let previousDate = new Date(oldestDate.substr(0, 4), parseInt(oldestDate.substr(4, 2)) - 1, oldestDate.substr(6, 2));
            if (previousDate.getTime() <= new Date(2023, 1, 15).getTime()) {
                return 0;
            }
        }

        this.similarityChange[pair] = {};

        this.changedParam.push(pair);
        this.similarityChange[pair]['current'] = current;
        this.similarityChange[pair]['previous'] = previous;

        return this.scoreWeightage[pair]

    }

    checkThreshold(scoreTotal) {
        if (scoreTotal >= this.customerThreshold.high) {
            utils.log(`Returning high for score total : ${scoreTotal}`);
            return "high"
        }
        else if (scoreTotal < this.customerThreshold.high && scoreTotal >= this.customerThreshold.medium) {
            utils.log(`Returning medium for score total : ${scoreTotal}`);
            return "medium"
        }
        else if (scoreTotal < this.customerThreshold.medium && scoreTotal >= this.customerThreshold.low) {
            utils.log(`Returning low for score total : ${scoreTotal}`);
            return "low"
        }

        utils.log(`Returning null for score total : ${scoreTotal}`);

        return null;
    }

    calculateScore(currentSpread, prevSpread, pair, additionalInfo) {

        switch (pair) {
            case "depth":
            case "externalLinks":
            case "internalLinks":
            case "externalJS":
            case "internalJS":
            case "scriptTag":
            case "styleTag":
            case "imgTag":
            case "audioTag":
            case "videoTag":
                let spreadDiff = 0;

                if (currentSpread > prevSpread) {
                    //30-20 / 20
                    if (prevSpread <= 0)
                        return 0;
                    spreadDiff = ((currentSpread - prevSpread) / prevSpread);
                }

                if (prevSpread > currentSpread) {
                    //30-15 / 15
                    if (currentSpread <= 0)
                        return 0;
                    spreadDiff = ((prevSpread - currentSpread) / currentSpread);
                }

                /**
                //33-6 = 94 current spread
                //61-6 = 55 prev spread

                //55-27 / 27 = 1.03 * 100 = 100% change in spread
                //add scrore
                */

                // this.maxSpreadDiff = 0.5;   // This is current value i.e. 50%
                if (spreadDiff > this.maxSpreadDiff) {
                    this.changedParam.push(pair);
                    return this.scoreWeightage[pair]
                }
                else if(spreadDiff > 0) {
                    additionalInfo.push(pair);
                }
                
                break;
            default:
                break;
        }

        return 0;

    }

    // calculate max wrt to previous max from metrix
    getMax(current, history, pair) {
        let values = []
        if (current)
            values.push(current);

        for (let key in history) {
            let element = history[key];
            element[pair] ? values.push(element[pair]) : '';
        }
        return Math.max(...values) == -Infinity ? 0 : Math.max(...values);
    }

    // calculate min wrt to previous max from metrix
    getMin(current, history, pair) {
        let values = []
        if (current)
            values.push(current);

        for (let key in history) {
            let element = history[key];
            element[pair] ? values.push(element[pair]) : '';
        }

        return Math.min(...values) == Infinity ? 0 : Math.min(...values);
    }

    getMinSimilarity(history, pair) {
        let values = []
        for (let key in history) {
            let element = history[key];
            element[pair] ? values.push(element[pair]) : '';
        }

        return Math.min(...values);
    }

    checkExternal(url, matrix) {
        let isExteralUrl = this.isExternal(url, this.config.mainUrl);

        if (isExteralUrl) {
            if (HaikuUtils.hasExtension(url, 'js'))
                matrix['externalJS'].push(url)
            else
                matrix['externalLinks'].push(url)
        }
        else {
            if (HaikuUtils.hasExtension(url, 'js'))
                matrix['internalJS'].push(url)
            else
                matrix['internalLinks'].push(url)
        }
    }

    collectElements(domString, tag, matrix) {
        //check for
        let typeKey = tag == 'script' ? 'src' : 'href';   //for script src and for link href comes
        let $ = cheerio.load(domString);

        $(tag).each((index, element) => {
            //if document script or link
            if (element.attribs[typeKey]) {
                this.checkExternal(element.attribs[typeKey], matrix)
            }

            // for total scripts and and links change logic here
            // either ways script or link item found just increment specific counter
            matrix[tag] += 1;
        });

        //add consolidated page script page styles and inline style and css
        matrix['scriptTag'] = parseInt($('script').length)
        matrix['styleTag'] = parseInt($("style").length)
        matrix['imgTag'] = parseInt($("img").length)
        matrix['videoTag'] = parseInt($("video").length)
        matrix['audioTag'] = parseInt($("audio").length)
    }

    isExternal(url, mainUrl) {
        var r = new RegExp('^(?:[a-z]+:)?//', 'i');  // check relative path only
        if (!r.test(url))
            return false;
        return HaikuUtils.canonacalizeHost(url) !== HaikuUtils.canonacalizeHost(mainUrl);
    }

    /**
     * Get structure and content fingerprints from the response.
     * @returns {fingerprints} fingerprints
     * @param {response} response The HTTP response
     */
    getFingerprints(response) {
        let tags = []
        let lines = []

        let success = false
        try {
            let $ = cheerio.load(response)

            // get the tags for structure fingerprint
            tags = $('*').map((i, el) => {
                return el.tagName;
            }).get();

            // get lines (text) as context
            lines = $('*').contents().filter((i, e) => {
                return e.type == 'text';
            }).map((i, e) => {
                return e.data.trim();
            }).get();
            lines = lines.filter(line => !/^\s*$/.test(line)) // remove blank lines
            success = true
        } catch (err) {
            logger.log('error', `returning dummy fingerprints due to error in generating fingerprints ${err.toString()}`)
            tags = ['-error-']
            lines = ['-error-']
        }

        // generate the fingerprints
        let fingerprints = {
            structure: new FingerPrint(HaikuUtils.bigrams(tags)),
            content: new FingerPrint(lines),
            success
        }
        return fingerprints
    }

    getDateKey() {
        let today = new Date();
        let dd = String(today.getDate()).padStart(2, '0');
        let mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
        let yyyy = today.getFullYear();

        return parseInt(yyyy + mm + dd);
    }

    /**
     * Save screenshot to S3 with the name given
     * @param {string} name base name of object in S3, prefix is same for entire scan and created using scanlogid, date etc.
     */
    async screenshotToS3(screenshotId, date, scanId, serviceId) {
        let img, s3OutPrefix, s3name;
        try {
            img = await this.scanner.browser.webContents.capturePage();

            s3name = screenshotId + '.png'
            s3OutPrefix = (`crawler/defacement/${serviceId}/${scanId}/screenshots/${date}/`)

            s3Utils.upload(s3OutPrefix, s3name, img.toPNG(), {
                ContentType: 'image/png'
            });
        } catch (error) {
            logger.log('error', `could not write screenshot image: ${err.toString()}`)
        }

        return JSON.stringify({
            prefix: s3OutPrefix,
            name: s3name
        });
    }

    similarity(currentFingerprint, previousFingerprint) {
        if (!previousFingerprint) {
            return 0
        }

        let fingerprintData = previousFingerprint

        // count the bits in common and return ratio to length
        let commonBits = 0
        for (let i = 0; i < previousFingerprint.length; i++) {
            if (fingerprintData[i] == currentFingerprint[i]) {
                commonBits++
            }
        }

        return commonBits / currentFingerprint.length
    }

    async defacementAlert(currentThreshold, currentObject, previousObject, key, currentVariation, rawObject) {
        let diffObject = this.generateDifferenceObject(currentThreshold, currentObject, previousObject, currentVariation, rawObject)
        // api call
        let request = {
            "scanid": this.config.scanId,
            "scanlogid": this.config.scanLogId,
            "key": key,
            "founddate": new Date().toISOString(),
            "scanner": "haiku",
            "vulns": {
                "ID-defacement": {
                    "foundby": this.constructor.name,
                    "productionReady": true,
                    "vulnerabilityid": 7,
                    "details": diffObject,
                }
            }
        };

        utils.log("Calling from  Defacement :malwareVulnerabilityFound")
        let res = await ssAPI.malwareVulnerabilityFound(request);
    }

    generateDifferenceObject(currentThreshold, currentObject, previousObject, currentVariation, rawObject) {
        let diffObject = {};
        diffObject['additionalInfo'] = {};
        diffObject['customerThreshold'] = this.customerThreshold;
        
        // check similarity and add in curreent and previous objects
        if (this.similarityChange['structureSimilarity']) {
            currentObject['structureSimilarity'] = this.similarityChange['structureSimilarity'].current;
            previousObject['structureSimilarity'] = this.similarityChange['structureSimilarity'].previous;
        }

        if (this.similarityChange['contentSimilarity']) {
            currentObject['contentSimilarity'] = this.similarityChange['contentSimilarity'].current;
            previousObject['contentSimilarity'] = this.similarityChange['contentSimilarity'].previous;
        }
        for (let key in currentObject) {
            if (Object.hasOwnProperty.call(previousObject, key)) {
                //IMP : use this.changedParam for following not keypair

                if (this.changedParam.includes(key)) {
                    diffObject[key] = {};
                    switch (typeof currentObject[key]) {
                        case "object":
                            diffObject[key] = this.isAddedOrRemoved(currentObject, previousObject, key);
                            break;

                        default:
                            if (!_.isEqual(currentObject[key], previousObject[key])) {
                                diffObject[key]["currentVal"] = currentObject[key];
                                diffObject[key]["previousVal"] = previousObject[key];
                            }
                            break;
                    }
                }                
            }
        }

        if (currentObject.additionalInfo) {
            for (let key in currentObject) {
                if (Object.hasOwnProperty.call(previousObject, key)) {
                    //IMP : use this.changedParam for following not keypair

                    if (currentObject.additionalInfo.includes(key)) {
                        diffObject['additionalInfo'][key] = {};
                        switch (typeof currentObject[key]) {
                            case "object":
                                diffObject['additionalInfo'][key] = this.isAddedOrRemoved(currentObject, previousObject, key);
                                break;

                            default:
                                if (!_.isEqual(currentObject[key], previousObject[key])) {
                                    diffObject['additionalInfo'][key]["currentVal"] = currentObject[key];
                                    diffObject['additionalInfo'][key]["previousVal"] = previousObject[key];
                                }
                                break;
                        }
                    }
                }
            }
        }

        //attach screenshot
        if (currentThreshold) {
            diffObject['screenshotInfo'] = {};
            diffObject['screenshotInfo']['current'] = currentObject['screenshotID'];
            if (!rawObject['screenshotID'])
                diffObject['screenshotInfo']['previous'] = previousObject['screenshotID'];
            else
                diffObject['screenshotInfo']['previous'] = rawObject['screenshotID'];
            diffObject['threshold'] = currentThreshold;
            diffObject['currentVariation'] = currentVariation;
        }

        return diffObject;
    }

    isAddedOrRemoved(currentObj, previousObj, key) {
        let current = currentObj[key];
        let previous = previousObj[key];
        let diff = {};
        diff['current'] = [];
        diff['previous'] = [];
        let changeReason = null;

        if (current.length > previous.length) {
            diff["added"] = _.difference(current, previous);
            changeReason = "added";
        }
        else
            if (current.length < previous.length) {
                diff["removed"] = _.difference(previous, current);
                changeReason = "removed";
            }
            else
                if (current.length == previous.length) {
                    if (!_.isEqual(current, previous)) {
                        diff["added"] = _.difference(previous, current);
                        changeReason = "added";
                    }
                }

        if (changeReason) {
            if (Object.keys(this.similarityChange).includes(key)) {
                diff['current'] = currentObj[key];
                diff['previous'] = previousObj[key];
            }
            else {
                diff['current'] = currentObj[key];
                diff['previous'] = previousObj[key];
            }

        }

        return diff;
    }

    clearUnaccesibleUrl(url) {
        Object.keys(this.scanner.defacementHistory).forEach(historyKey => {
            if(_.has(this.scanner.defacementHistory[historyKey],url))
                delete this.scanner.defacementHistory[historyKey][url];
        });

        let index = this.scanner.defacementHistory.urls.indexOf(url);
        if (index > -1) { // only splice array when item is found
            this.scanner.defacementHistory.urls.splice(index, 1); // 2nd parameter means remove one item only
        }

    }

    async onScanDone(reason, ret, logger = console) {
        await this.finalizeData();
    }

    async finalizeData(){
        await this.prepareDatewise();
        if (this.scanner.statusInfo) {
            delete this.scanner.statusInfo;
            // delete this.scanner.defacementHistory.urls;
        }
    }

}

module.exports = DefacementCheck