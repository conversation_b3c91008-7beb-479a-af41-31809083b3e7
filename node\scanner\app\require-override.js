/**
 * Module require override 
 * Intercepts requires for 're2' and redirects them to our replacement
 */

const path = require('path');
const Module = require('module');

// Save the original require
const originalRequire = Module.prototype.require;

// Override the require function
Module.prototype.require = function(modulePath) {
  // If the module being required is 're2', redirect to our replacement
  if (modulePath === 're2') {
    // Use absolute path to our replacement module
    const replacementPath = path.resolve(__dirname, '../common/lib/re2-replacement');
    return originalRequire.call(this, replacementPath);
  }
  
  // For all other modules, use the original require
  return originalRequire.apply(this, arguments);
};

// Export a function to restore the original require if needed
module.exports = function restoreOriginalRequire() {
  Module.prototype.require = originalRequire;
}; 