// Browser actions related methods 
// Any interaction with elements takes XPath as parameter and the parameter is assumed to be 
// base64 encoded to ensure that it can be passed between renderer and main easily. 
// specifically, prameter us run through JSON.parse(atob(..))
class IndusfaceActions {
    constructor() {}

    name(short) {
        return short ? "IFC-Actions" : "Indusface Browser Actions Plugin"
    }

    init() {
        indusfaceRenderer.submitForm = this.submitForm.bind(this)
        indusfaceRenderer.allElementsExist = this.allElementsExist.bind(this)
        indusfaceRenderer.allElementsVisible = this.allElementsVisible.bind(this)
        indusfaceRenderer.triggerMouseEvent = this.triggerMouseEvent.bind(this)
        indusfaceRenderer.searchStr = this.searchStr.bind(this)
        indusfaceRenderer.setFocus = this.setFocus.bind(this)
        indusfaceRenderer.getBoundingRect = this.getBoundingRect.bind(this)
        indusfaceRenderer.scrollIntoView = this.scrollIntoView.bind(this)
        indusfaceRenderer.setSelectValue = this.setSelectValue.bind(this)
        indusfaceRenderer.setCheckboxValue = this.setCheckboxValue.bind(this)
        indusfaceRenderer.setTextValue = this.setTextValue.bind(this)
        indusfaceRenderer.getCheckboxValue = this.getCheckboxValue.bind(this)
        indusfaceRenderer.getLocation = this.getLocation.bind(this)
        indusfaceRenderer.getDocSize = this.getDocSize.bind(this)
        indusfaceRenderer.elementContainsText = this.elementContainsText.bind(this)
        indusfaceRenderer.dispatchInputEventOnElement = this.dispatchInputEventOnElement.bind(this)
        indusfaceRenderer.setInputValue = this.setInputValue.bind(this)
        indusfaceRenderer.uploadFile = this.uploadFile.bind(this)
    }

    triggerMouseEvent(encXpath, event) {
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)
        if (el) {
            var mouseEvt = new MouseEvent(event, {
                bubbles: true,
                cancelable: true,
                view: window
            });

            el.dispatchEvent(mouseEvt)
            return true
        } else {
            return false
        }
    }

    // submit the form directly using .submit()
    submitForm(encXpath, encSubmitAction) {
        function appendInputtoForm(parentForm, name, value) {
            let el = document.createElement('input')
            el.name = name
            el.value = value
            parentForm.appendChild(el)
        }

        let xpath = JSON.parse(atob(encXpath))
        let formEl = indusfaceRenderer.getElementFromXPath(xpath)
        // sanity check
        if (!formEl || 'function' != typeof (formEl.submit)) {
            return false
        }

        // get the submit action if it exists and add as an input type so that it gets submitted along with the form
        let xpathSubmitAction = JSON.parse(atob(encSubmitAction))
        if (xpathSubmitAction.trim().length) {
            let submitActionEl = indusfaceRenderer.getElementFromXPath(xpathSubmitAction)
            if (submitActionEl) {
                //turn off validation when submitting via this item
                submitActionEl.formnovalidate = true
                let isAspPage = !!document.getElementById('__VIEWSTATE')

                // special case for EVENTTARGET
                if (isAspPage) {
                    //__EVENTTARGET
                    let eventTargetEl = formEl.querySelector('[id="__EVENTTARGET"]')
                    if (eventTargetEl) {
                        eventTargetEl.value = submitActionEl.id
                    } else {
                        appendInputtoForm(formEl, '__EVENTTARGET', submitActionEl.id)
                    }
                } 
                
                if (submitActionEl.type == 'image') {
                    let name = submitActionEl.name ? submitActionEl.name + '.' : ''
                    appendInputtoForm(formEl, name + 'x', '40')
                    appendInputtoForm(formEl, name + 'y', '30')
                } else {
                    appendInputtoForm(formEl, submitActionEl.name || 'Submit', submitActionEl.value || 'Submit')
                }
            }
        }

        // also add all other inputs that are not part of other forms into this form so they
        // get submitted along with this form
        let inputElems = Array.from(document.querySelectorAll("select,textarea,input"))

        // only add elements that are not part of any other form, have a name attribute and are not submit/image/button
        inputElems = inputElems.filter(i => !i.form && i.name && i.type != 'submit' && i.type != 'button')

        // move these elements to the form being submitted
        for (let inputEl of inputElems) {
            formEl.appendChild(inputEl)
        }

        // submit  form
        // fix the target attribute if it is present
        if ( formEl.target == '_blank') {
            formEl.target = '_top'
        }

        // turn off validation for form
        formEl.novalidate = true
        formEl.submit()

        return true
    }

    // paramter is array of xpaths that are JSON encoded. 
    allElementsExist(xpaths) {
        let elementsExist = true
        try {
            xpaths = JSON.parse(atob(xpaths))
            elementsExist = xpaths.length > 0
            for (let xpath of xpaths) {
                elementsExist = elementsExist && !!indusfaceRenderer.getElementFromXPath(xpath)
                if (!elementsExist) {
                    break
                }
            }
        } catch (e) {
            elementsExist = false
        }
        return elementsExist
    }

    // paramter is array of xpaths that are JSON encoded. 
    allElementsVisible(xpaths) {
        let elementsVisible = true
        try {
            xpaths = JSON.parse(atob(xpaths))
            elementsVisible = xpaths.length > 0
            for (let xpath of xpaths) {
                let rect = indusfaceRenderer.getBoundingClientRect(xpath)
                elementsVisible = rect && rect.inViewport
                if (!elementsVisible) {
                    break
                }
            }
        } catch (e) {
            elementsVisible = false
        }
        return elementsVisible
    }

    // returns if the focus has changed to this element.
    setFocus(encXpath) {
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)
        if (el) {
            el.focus()
        }

        return document.activeElement === el
    }

    scrollIntoView(elOrEncXpath, block) {
        let scrollToTop = (block != 'end')
        let el = elOrEncXpath
        if ('string' == typeof (el)) {
            let xpath = JSON.parse(atob(elOrEncXpath))
            el = indusfaceRenderer.getElementFromXPath(xpath)
        }

        if (el) {
            // When electron upgrades to chromium 61: el.scrollIntoView( { behavior : "instant", block: block})
            el.scrollIntoView({ block: block || 'nearest', behavior: 'instant' })
            if (block == 'center') {
                // centering taken from https://github.com/Treora/scroll-into-view
                // until electron upgrades to Chromium 61

                // Fetch positional information.
                var rect = el.getBoundingClientRect();

                // Determine location to scroll to.
                var targetY = window.scrollY + rect.top - (window.innerHeight - el.offsetHeight) * 0.5
                var targetX = window.scrollX + rect.left - (window.innerWidth - el.offsetWidth) * 0.5

                // Scroll.
                window.scroll(targetX, targetY);
            }
        }
    }

    getBoundingRect(elOrEncXpath) {
        let el = elOrEncXpath
        if ('string' == typeof (el)) {
            let xpath = JSON.parse(atob(elOrEncXpath))
            el = indusfaceRenderer.getElementFromXPath(xpath)
        }

        let boundingRect
        if (el) {
            function isInViewport(rect) {
                // this is from puppeteer code. 
                const style = window.getComputedStyle(el);
                let isVisible = style && style.display !== 'none' && style.visibility !== 'hidden'
                if (!isVisible) {
                    return false
                }

                let windowInnerHeight = window.innerHeight || document.documentElement.clientHeight /*or $(window).height() */
                let windowInnerWidth = window.innerWidth || document.documentElement.clientWidth /*or $(window).width() */

                // do we need to do part of rect in viewport check?
                let inViewport = rect.width > 0 && rect.height > 0 &&
                    rect.top >= 0 &&
                    rect.left >= 0 &&
                    rect.bottom <= windowInnerHeight &&
                    rect.right <= windowInnerWidth
                return inViewport
            }

            // get the bounding rect & visibiity state
            let rect = el.getBoundingClientRect()
            let inViewport = isInViewport(rect)

            // not in viewport? see if we can bring it into viewport
            if (!inViewport) {
                indusfaceRenderer.scrollIntoView(el, "center")
                rect = el.getBoundingClientRect()
                inViewport = isInViewport(rect)
            }

            boundingRect = {
                left: rect.left,
                top: rect.top,
                right: rect.right,
                bottom: rect.bottom,
                width: rect.width,
                height: rect.height,
                inViewport: inViewport
            }
            //console.log( 'bound rect:', el, window.innerHeight, window.innerWidth, boundingRect)
        }

        return boundingRect
    }

    // returns substring with some context if found
    searchStr(encRegexStr, flags, charsContext) {
        if (!charsContext) {
            charsContext = 50
        }

        let regexStr = JSON.parse(atob(encRegexStr))
        let regex = new RegExp(regexStr, flags)
        let res = document.body.innerHTML.match(regex)
        if (res) {
            let start = res.index - charsContext
            return document.body.innerHTML.substr(start < 0 ? 0 : start, res[0].length + 2 * charsContext)
        } else {
            return ""
        }
    }

    setSelectValue(encXpath, selectedIndices) {
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)

        if (el && selectedIndices && el.options) {
            selectedIndices = selectedIndices.split(",")
            selectedIndices.forEach(function (selectedIndex) {
                if (selectedIndex in el.options) {
                    el.options[selectedIndex].selected = true
                }
            });

            el.dispatchEvent(new Event('change', {
                bubbles: true,
                cancelable: true
            }))
            return true
        } else {
            return false
        }
    }

    setCheckboxValue(encXpath, val) {
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)

        if (!el) {
            return false
        }

        el.checked = val
        el.dispatchEvent(new Event('change', {
            bubbles: true,
            cancelable: true,
        }))
        return true
    }

    setTextValue(encXpath, text) {
        indusfaceRenderer.setFocus(encXpath)
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)

        if (el) {
            el.value = text
            el.dispatchEvent(new Event('change', {
                bubbles: true,
                cancelable: true
            }))
            return true
        } else {
            return false
        }
    }

    /**
     * Dispatches an input event on the element specified by the `encXpath` parameter.
     *
     * @param encXpath The XPath of the element to dispatch the event on.
     * @param inputEventType The type of input event to dispatch.
     * @param keyEvent The key event to dispatch, if any.
     */
    dispatchInputEventOnElement(encXpath, inputEventType, keyEvent) {
        indusfaceRenderer.setFocus(encXpath);
        let xpath = JSON.parse(atob(encXpath));
        let el = indusfaceRenderer.getElementFromXPath(xpath);

        if (el) {
            //let tabKeyEvent = new InputEvent('input', { data: '\t' });
            let inputEvent = new InputEvent(inputEventType, { data: keyEvent });
            el.dispatchEvent(inputEvent);
            return true
        } else {
            return false
        }
    }

    /**
     * Sets the value of an input element in a way that ensures compatibility with various frameworks.
     * 
     * @param {string} encXpath - The XPath of the input element to set the value of.
     * @param {string} value - The value to set.
     */
    setInputValue(encXpath, value) {
        indusfaceRenderer.setFocus(encXpath);
        let xpath = JSON.parse(atob(encXpath));
        let el = indusfaceRenderer.getElementFromXPath(xpath);

        if (el) {
            // Set the value using the setter function
            let valueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
            valueSetter.call(el, value);
        
            // Optionally, dispatch an input event to notify any listeners
            el.dispatchEvent(new Event('input', { bubbles: true }));
        }
    }

    getCheckboxValue(encXpath) {
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)
        return el ? el.checked : ''
    }

    getLocation() {
        return document.location.href
    }

    getDocSize() {
        return document.body.outerHTML.length
    }

    // will check innnerText and Value. 
    elementContainsText(encXpath, text) {
        let xpath = JSON.parse(atob(encXpath))
        let el = indusfaceRenderer.getElementFromXPath(xpath)
        let matched = false
        if (el) {
            let content = el.innerText + '_value_=' + el.value
            content = content.toLowerCase()
            matched = content.includes(text.toLowerCase())
        }
        return matched
    }

    uploadFile(encXpath, fileContent, fileName, fileType = 'text/plain') {
        //indusfaceRenderer.setFocus(encXpath);
        let xpath = JSON.parse(atob(encXpath));
        let el = indusfaceRenderer.getElementFromXPath(xpath);
        if (el) {
            let dataTransfer = new DataTransfer();
            let file = new File([`${fileContent}`], fileName, { type: fileType });
            dataTransfer.items.add(file);
            el.files = dataTransfer.files;
            let event = new Event('change', { bubbles: true });
            el.dispatchEvent(event);
        }
    }
}

module.exports = IndusfaceActions