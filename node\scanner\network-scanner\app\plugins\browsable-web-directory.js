const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Browsable Web Directory Detection Plugin
 * 
 * Description:
 * Detects exposed directory listings that could reveal sensitive information about the web application structure.
 * Focuses on identifying actual directory listings while avoiding false positives from similar-looking content.
 * 
 * Severity: Medium
 * CWE ID: CWE-548 (Information Exposure Through Directory Listing)
 * OWASP Categories: A05:2021 (Security Misconfiguration)
 * CVSS Score: 5.3
 * CVSS Vector: CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N
 */
class DIRList extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-browsable-web-directory'
        this.backupvulnerabilityID = 'ID-archive-backup-file'
        this.possibleBackupFilevulnerabilityID = 'ID-possible-backup-file'
        this.coreDumpFileID = 'ID-core-dump-file'
        this.PSCvulnerabilityID = 'ID-possible-sensitive-file-or-directory'

        // Pre-compile regex patterns for better performance
        this.excludeURLRegex = /\/(?:phpinfo|info)\.php|\/(?:blog|docs?|learning|readme|changelog|license|contributing|api-docs?|documentation|help|support|guide|tutorial|faq)\/|\.(?:txt|md|rst|pdf)$/i

        // Directory listing indicators
        this.dirListingPatterns = [
            // Apache directory listing
            /<title>\s*(?:listing directory|Index of) [^<]+<\/title>/i,
            /<h1>\s*(?:listing directory|Index of) [^<]+<\/h1>/i,
            // /<pre>(\s*<a href="[^"]*">[^<]*<\/a>\s*\n?)+<\/pre>/i, // Ensure multiple links in <pre> block

            // Nginx directory listing
            /<table>\s*<tr>\s*<th>\s*Name\s*<\/th>\s*<th>\s*Last modified\s*<\/th>\s*<th>\s*Size\s*<\/th>\s*<\/tr>[\s\S]*?<\/table>/i,

            // IIS directory listing
            /<h1>\s*Directory Listing -- [^<]+<\/h1>/i,
            /<table class="filelist">/i,

            // Generic directory listing patterns
            /<a href="[^"]+">\s*[^<]+\s*<\/a>\s*<\/td>\s*<td(?: align="right")?>\s*\d{4}-\d{2}-\d{2}/i,
            /<a href="[^"]+">\s*[^<]+\s*<\/a>\s*<\/td>\s*<td>\s*\d{2}-[a-z]{3,4}-\d{4}/i,
            /<li>\s*<a href="[^"]+">\s*[^<]+\s*<\/a>\s*\([^,]+,\s*\d{4}-\d{2}-\d{2}\)\s*<\/li>/i,
            /<li>\s*<a href="[^"]+">\s*[^<]+\s*<\/a>\s*\([a-z]+\)\s*<\/li>/i,
            /<li>\s*<a href="[^"]+">\s*[^<]+\s*<\/a>\s*-[^-]+-\s*\d{4}-\d{2}-\d{2}\s*<\/li>/i,
            /<li>\s*<a href="[^"]+">\s*[^<]+\s*<\/a>\s*-[^\d]+\s*\d{4}-\d{2}-\d{2}\s*<\/li>/i,
            /<h[1-3]>\s*(Index of|Files:)\s*<\/h[1-3]>\s*<ul>/i,

            // **🔹 NEW: Your Missing Patterns** - If need, we ll enabke back.
            /* new RegExp("<(h1|title)>\\s*listing directory\\s*" + _.escapeRegExp(uripath) + "?.*?</(h1|title)>", 'i'),
            new RegExp("<(h1|title)>\\s*" + host + "\\s*[-/]\\s*" + _.escapeRegExp(uripath) + "?.*?</(h1|title)>", 'i'), */
        ];

        // Sensitive file patterns to look for
        this.sensitiveFilePatterns = [
            /[^\/"]+\.(?:zip|tar|gz|rar|7z|bz2|xz|lzma)/i,
            /[^\/"]+\.(?:bak|backup|old|save|swp|swo|~|tmp|temp)/i,
            /[^\/"]+\.(?:core|dump|crash)/i,
            /[^\/"]+\.(?:config|conf|ini|xml|json|yaml|yml|env|properties|sql|sqlite|db|sqlite3|log|logs)/i,
            /[^\/"]+\.(?:password|secret|key|config|backup|dump|log|database|admin|private|secure)/i,
            /[^\/"]+(?:admin|backup|config|data|database|logs|private|secret|secure|temp|tmp|upload|uploads|user|users|webadmin|webmail|wwwroot)/i,
        ];

        // Patterns that indicate it's not a directory listing
        this.excludePatterns = [
            // Common web application patterns
            /<div class="(?:header|footer|nav|menu|sidebar)">/i,
            /<nav class="(?:main|top|bottom)">/i,
            /<ul class="(?:menu|nav|list)">/i,
            /<div class="(?:content|main|container)">/i,

            // Common frameworks
            /<div class="(?:bootstrap|material|foundation)">/i,
            /<div class="(?:react|vue|angular)">/i,

            // Common CMS patterns
            /<div class="(?:wordpress|joomla|drupal)">/i,

            // Common error pages
            /<div class="(?:error|404|500)">/i,

            // Common documentation
            /<div class="(?:docs|documentation|api)">/i
        ];
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            clearQueryParams: true,
            skipRoot: false,
            maxPathComponents: 3,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'always'
        });
    }
    getAttackVectors() {
        return DirNames
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        if (this.excludeURLRegex.test(attack.httpRequest.uri)) {
            return false
        }
        return await super.performNetworkAttack(attack)
    }

    processAttackResponse(attack) {
        if (this.excludeURLRegex.test(attack.httpRequest.uri)) {
            return false
        }

        // Get scan-scoped storage
        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        const pluginDataForRequest = this.getPluginScopedStore(attack);

        if (pluginDataForRequest.dirListingFound) return;

        let responseBody = _.get(attack, "result.resp.body", "").trim();
        if (responseBody?.length < 10) return;

        // Add maximum response size limit
        const MAX_RESPONSE_SIZE = 100000;
        if (responseBody.length > MAX_RESPONSE_SIZE) {
            responseBody = responseBody.substring(0, MAX_RESPONSE_SIZE);
        }

        // Check if it's likely a directory listing
        if (!this.isLikelyDirectoryListing(responseBody)) return;

        // Get the base path without query parameters
        const url = new URL(attack.httpRequest.uri, 'http://dummy')
        const basePath = url.pathname

        // Initialize reported paths set for this scan if not exists
        if (!pluginStorageScanScope.reportedPaths) {
            pluginStorageScanScope.reportedPaths = new Set()
        }

        // Skip if this path was already reported for this scan
        if (pluginStorageScanScope.reportedPaths.has(basePath)) {
            return
        }

        // Add the path to reported paths for this scan
        pluginStorageScanScope.reportedPaths.add(basePath)
        this.checkDirectoryListing(attack);

        // Extract all href values
        const hrefMatches = responseBody.match(/href="([^"]+)"/g) || [];
        if (hrefMatches.length == 0) return;
        const files = this.sensitiveFilePatterns.reduce((matches, pattern) => {
            const match = pattern.exec(hrefMatches);
            if (match && !matches.includes(match[0])) {
                matches.push(match[0]);  // Only add if not already in array
            }
            return matches;
        }, []);
        if (files.length == 0) return;
        // Scan files once and collect all types
        const fileTypes = this.categorizeFiles(files);
        if (fileTypes.archiveFiles.length == 0 && fileTypes.backupFiles.length == 0 && fileTypes.coreDumpFiles.length == 0 && fileTypes.sensitiveFiles.length == 0) return;
        // Report vulnerabilities based on collected files
        if (fileTypes.archiveFiles.length > 0) this.checkArchiveFiles(attack, fileTypes);
        if (fileTypes.backupFiles.length > 0) this.checkBackupFiles(attack, fileTypes);
        if (fileTypes.coreDumpFiles.length > 0) this.checkCoreDumpFiles(attack, fileTypes);
        if (fileTypes.sensitiveFiles.length > 0) this.checkSensitiveFiles(attack, fileTypes);

        // Mark as processed to avoid duplicate reports
        pluginDataForRequest.dirListingFound = true;
    }

    categorizeFiles(files) {
        const fileTypes = {
            archiveFiles: [],
            backupFiles: [],
            coreDumpFiles: [],
            sensitiveFiles: []
        };

        // Scan each file once and categorize it
        files.forEach(file => {
            // Archive/Compression files
            if (fileTypes.archiveFiles.length < 5 && /\.(zip|tar|gz|rar|7z|bz2|xz|lzma)$/i.test(file)) {
                fileTypes.archiveFiles.push(file);
            }

            // Backup files
            if (fileTypes.backupFiles.length < 5 && /\.(bak|backup|old|save|swp|swo|~|tmp|temp)$/i.test(file)) {
                fileTypes.backupFiles.push(file);
            }

            // Core dump files
            if (fileTypes.coreDumpFiles.length < 5 && /\.(core|dump|crash)$/i.test(file)) {
                fileTypes.coreDumpFiles.push(file);
            }

            // Sensitive files/directories
            const hasSensitiveExt = /\.(config|conf|ini|xml|json|yaml|yml|env|properties|sql|sqlite|db|sqlite3|log|logs)$/i.test(file);
            const hasSensitiveDir = /(?:admin|backup|config|data|database|logs|private|secret|secure|temp|tmp|upload|uploads|user|users|webadmin|webmail|wwwroot)/i.test(file);
            const hasSensitiveName = /(?:password|secret|key|config|backup|dump|log|database|admin|private|secure)/i.test(file);

            if (fileTypes.sensitiveFiles.length < 5 && (hasSensitiveExt || hasSensitiveDir || hasSensitiveName)) {
                fileTypes.sensitiveFiles.push(file);
            }
        });

        return fileTypes;
    }

    checkDirectoryListing(attack) {
        const details = {
            result: `Exposed Directory Listing Vulnerability Detected.`
        }
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
    }

    checkArchiveFiles(attack, fileTypes) {
        // 2. Archive/Compression Files
        const details = {
            result: `Possible Archive/Compression Files Detected: ${fileTypes.archiveFiles.join(', ')}`
        }
        this.addVulnerabilitytoResult(attack, this.backupvulnerabilityID, details);
    }

    checkBackupFiles(attack, fileTypes) {
        const details = {
            result: `Possible Backup Files Detected: ${fileTypes.backupFiles.join(', ')}`
        }
        // 3. Backup Files
        this.addVulnerabilitytoResult(attack, this.possibleBackupFilevulnerabilityID, details);
    }

    checkCoreDumpFiles(attack, fileTypes) {
        const details = {
            result: `Core Dump Files Detected: ${fileTypes.coreDumpFiles.join(', ')}`
        }
        // 4. Core Dump Files
        this.addVulnerabilitytoResult(attack, this.coreDumpFileID, details);
    }

    checkSensitiveFiles(attack, fileTypes) {
        // 5. Sensitive Files/Directories
        const details = `Possible Sensitive Files/Directories Detected: ${fileTypes.sensitiveFiles.join(', ')}`
        this.addVulnerabilitytoResult(attack, this.PSCvulnerabilityID, details);
    }

    isLikelyDirectoryListing(responseBody) {
        // First check if it matches any directory listing patterns
        const hasDirListingPattern = this.dirListingPatterns.some(pattern => pattern.test(responseBody));
        if (!hasDirListingPattern) return false;

        // Then check if it's not a false positive
        const isExcluded = this.excludePatterns.some(pattern => pattern.test(responseBody));
        if (isExcluded) return false;

        // Additional checks to reduce false positives
        const hasTooManyLinks = (responseBody.match(/<a href/g) || []).length > 50;
        const hasTooManyTables = (responseBody.match(/<table/g) || []).length > 5;
        const hasTooManyDivs = (responseBody.match(/<div/g) || []).length > 100;

        // If it has too many structural elements, it's probably not a directory listing
        if (hasTooManyLinks || hasTooManyTables || hasTooManyDivs) return false;

        return true;
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }

    
}
// vectors & matches ...
const DirNames = [
    `_private`, `_tests`, `_vti_bin`,
    `account`, `accounts`, `acct`,
    `adm`, `admin`, `admins`, `administrator`, `adminpanel`, `admincp`,
    `api`, `app`, `application`, `apps`, `auth`,
    `backend`, `backup`, `backups`,
    `bin`, `cfdocs`, `cgi-bin`, `cgi-sys`, `config`, `configs`,
    `creditcards`, `customer`, `customers`,
    `data`, `database`, `databases`, `db`, `db_backup`, `db_backups`,
    `dev`, `devel`, `development`, `dump`, `dumps`,
    `encryptionkeys`, `error_log`, `errors`, `etc`, `exports`,
    `files`, `filemanager`, `filestorage`, `ftp`,
    `inc`, `include`, `includes`, `internal`, `intranet`,
    `keys`, `key`, `keystore`,
    `library`, `libraries`, `libs`, `logs`, `logfiles`,
    `manager`, `management`,
    `password`, `passwords`, `passwd`, `pass`,
    `payment`, `payments`, `paypal`, `billing`, `invoice`, `invoices`,
    `phpMyAdmin`, `phpmyadmin`,
    `private`, `priv`, `privdata`,
    `report`, `reports`, `resource`, `resources`,
    `root`, `rootfiles`,
    `secure`, `security`,
    `server`, `servers`,
    `service`, `services`, `shell`, `sh`,
    `siteadmin`, `src`, `ssl`, `ssh`,
    `static`, `storage`, `storages`, `support`,
    `system`, `sys`, `sysadmin`,
    `temp`, `templates`, `test`, `testing`, `tests`, `tmp`,
    `upload`, `uploads`, `user`, `users`,
    `var`, `var_backups`, `var_log`,
    `webmail`, `web`, `WebService`, `website`, `websites`,
    `wp-admin`, `wp-content`, `wp-includes`,
    `www`, `wwwroot`,
    `www-data`, `www-html`, `www-includes`, `www-pages`, `www-public`, `www-root`, `www-uploads`, `www-users`,
];

module.exports = DIRList