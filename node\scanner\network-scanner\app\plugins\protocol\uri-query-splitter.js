const debug = require('debug')('UriQuerySplitter')
const querystring = require('querystring')
const BasePlugin = require('../base-plugin')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const UriQueryParameters = require('../../../lib/uri-query-parameters')
const HaikuUtils = require('../../../../common/lib/haiku-utils')

class UriQuerySplitter extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the netork scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }

    /**
     * New request received from crawler - Kick off paramter iteration if request is viable
     * @param {request} originalRequest untampered request
     * @override
     */
    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http headers
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area')
            if (originalRequest.revalidationInfo && UriQueryParameters.ParameterType != attackArea) {
                logger.log('info', `uri query splitter - not an interesting request since request being revalidated is attacking '${attackArea}'`, HaikuUtils.getMetadataForLog(originalRequest))
                return
            }
        }

        // only process successful requests and those that have query params
        if (originalRequest.httpResponse.err || !UriQueryParameters.isUriQueryParamsRequest(originalRequest.httpRequest)) {
            logger.log('info', `uri query splitter - not an interesting request ${originalRequest.httpRequest.method} ${originalRequest.httpRequest.uri}  err=${originalRequest.httpResponse.err}`, HaikuUtils.getMetadataForLog(originalRequest))
            return
        }

        // Create object that can iterate and manipulate params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan')
        let options = this.getMetadata(originalRequest).options;
        let createUriSearchParser = function () {
            return new UriQueryParameters(originalRequest, scanStore,options)
        }
        this.networkScanner.emit('uri-query-params', createUriSearchParser)
    }

}

module.exports = UriQuerySplitter