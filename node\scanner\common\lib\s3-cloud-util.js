// Load the SDK and UUID
const AWS = require('aws-sdk');
const fs = require('fs')
const path = require('path')
const debug = require('debug')('HaikuS3Utils')

const HaikuBucket = process.env['HAIKU_BUCKET'] || 'haiku-store'
/**
 * Utility for S3 operations. Hai<PERSON> will use S3 as an external store both for output like logs and
 * for data like list of URLs to try at start of crawl.
 * Promise based where possible
 */
class HaikuS3AwsUtils {

    constructor(options) {
        this.options = options
        this.s3 = new AWS.S3() // credentials from common init file ~/.aws/credentials
    }

    /**
     * get a 'file' from S3
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname like crawler-metrics.json
     */
    getFile(prefix, name) {
        let params = this._getParamsObject(prefix, name)
        return this.s3.getObject(params).promise()
    }

    /**
     * Upload data to S3
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname (analogous to filename) like crawler-metrics.json
     * @param {any} Body the data to upload (Buffer, Typed Array, Blob, String, ReadableStream)
     * @param {any} additionalOptions any extra fields to pass in params eg. content-type 
     * See: https://docs.aws.amazon.com/AWSJavaScriptSDK/latest/AWS/S3.html#upload-property
     */
    upload(prefix, name, Body, additionalOptions = {}) {
        let params = this._getParamsObject(prefix, name)
        params.Body = Body
        Object.assign(params, additionalOptions)
        return this.s3.upload(params).promise()
    }

    // add the upload/download folder options later...
    // internal methods
    /**
     * Returns the common params (eg. storage class) and bucket, key set up.
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname like crawler-metrics.json
     */
    _getParamsObject(prefix, name) {
        if (!prefix.endsWith('/')) {
            prefix = prefix + '/'
        }

        return {
            Bucket: HaikuBucket,
            Key: prefix + name
        };
    }
}
const haikuS3AwsUtils = new HaikuS3AwsUtils()
module.exports = haikuS3AwsUtils