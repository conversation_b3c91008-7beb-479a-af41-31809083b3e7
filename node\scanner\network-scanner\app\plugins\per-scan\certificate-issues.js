const debug = require('debug')('CertificateIssuesPlugin')
const NetworkAttack = require('../network-attack')
const chromiumNetErrors = require('chromium-net-errors')
const URL = require('url').URL
const https = require('https')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const { options } = require('../../../../common/config/logger-config')
const { includes } = require('lodash')
require('tls').DEFAULT_MIN_VERSION = 'TLSv1'

/** 
 * Check for server certificate issues. Checks for:
 * certificate errors - this is passed to us from teh chrome crawler to leverage the browser certificate checks
 * Other issues like, certificate will expire soon. 
 */
class CertificateIssuesPlugin extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the ClickjackingHeaderCheck plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // event handlers
        networkScanner.on('electron-cert-error', this.certIssueFromElectron.bind(this))
        networkScanner.on('resumed-scan', this.onResumedScan.bind(this))
    }

    /**
     * Ensure that we check for certificate expiry again in this resetting flag 
     * @param {Number} scanId ScanId beng resumed
     * @param {Object} pluginData scan scoped plugin data
     */
    onResumedScan(scanId, pluginData) {
        if (pluginData) {
            pluginData[this.getName()] = {}
        }
    }

    /**
     * Errors reported by Haiku crawler (electron = chromium)
     * See: {@link https://cs.chromium.org/chromium/src/net/base/net_error_list.h}
     * 
     * A subset is reported directly, rest are picked up as a generic certificate error.
     *  Invalid TLS/SSL certificate
     *  Untrusted TLS/SSL server X.509 certificate
     *  HTTPS server certificate expired
     * 
     * @param {ElectronCertificateErrorMsgContent} electronCertError Details of the certificate error
     * @returns Event handler, annotates attack parameter, no return value.
     */
    async certIssueFromElectron(electronCertError) {
        // sanity
        if (!electronCertError || !electronCertError.chromiumNetErr) {
            logger.log('info', `invalid electron certificate error object ${electronCertError} `, HaikuUtils.getMetadataForLog(electronCertError))
            return
        }

        // convert chromium error code to error structure
        let ErrFactory = chromiumNetErrors.getErrorByDescription(electronCertError.chromiumNetErr.replace('net::ERR_', ''))
        let certErr = new ErrFactory()
        if (certErr.type != 'certificate') {
            logger.log('error', `Error is not a certificate type: ${certErr.type} `, HaikuUtils.getMetadataForLog(electronCertError))
            return
        }

        // fix up the times (electron has in seconds node has dates in milliseconds)
        electronCertError.partialCert.validStart *= 1000
        electronCertError.partialCert.validExpiry *= 1000

        let commonName = _.get(electronCertError, 'partialCert.subject.commonName', '')
        let valid_to = new Date(electronCertError.partialCert.validExpiry)
        electronCertError.description = ' Issued to Common Name (CN): ' + commonName + ', valid till: ' + valid_to + '\nDescription: ' + certErr.message
        let uri = new URL(electronCertError.uri)

        let scanId = electronCertError.scanId
        let scanlogId = electronCertError.scanlog_id
        delete electronCertError.scanId
        delete electronCertError.scanlog_id

        //diff vulnerability id and descriptions based on error codes
        let vulnId = 'ID-certificate-error'
        let cert = await this.getdts(uri.hostname)
        switch (certErr.code) {
            case -200:
                CertErrdts = {
                    Code: certErr.code,
                    URI: uri.hostname
                }
                if (cert && cert.bits.toString().length > 0 && cert != 'Error') {
                    let CNameList = cert.subject.CN + ', ' + cert.subjectaltname
                    CNameList = CNameList.replace(/DNS:/g, '').split(', ')
                    // let vuln = false
                    // let CNameAlt = cert.subjectaltname
                    if (!CNameList.includes(uri.hostname) && !/\*./.test(CNameList)) {
                        vulnId = 'ID-cert-common-name-invalid'
                        break
                    }
                    else if (/\*./.test(CNameList)) {
                        for (let ele of CNameList) {
                            if (ele.includes('*.')) {
                                ele = ele.replace('*.', '.*.')
                                let regexp = new RegExp(ele, 'i')
                                if (!regexp.test(hostname)) {
                                    vulnId = 'ID-cert-common-name-invalid'
                                    break
                                }
                            }
                        }
                    }
                    // Check CName
                    // let electronCertError = []
                    /* let CName = cert.subject.CN
                    let CNameAlt = cert.subjectaltname
                    if (!CName.includes(uri.hostname) && !CNameAlt.includes(uri.hostname)) {
                        if (CName.includes('*.')) {
                            CName = CName.replace('*.', '')
                            //hostname = hostname.match(/\w+(?:\.co)?\.\w+$/)[0]
                        }
                        if (!uri.hostname.includes(CName)) {
                            vulnId = 'ID-cert-common-name-invalid'
                            break
                        } break;
                    } break; */
                } break;
            case -201:
                vulnId = 'ID-cert-has-expired'
                break;
            case -202:
                if (cert && cert.bits.toString().length > 0 && cert != 'Error') {
                    let CNameIssuedBy = cert.issuer.CN
                    if (!/emSign SSL CA - G1|DigiCert|Let\'s Encrypt|Entrust|GlobalSign|GeoTrust|Symantec|Comodo|Sectigo|GoDaddy|Thawte|Certum|IdenTrust|QuoVadis|Trustwave|Amazon|Microsoft|Google Trust Services/i.test(CNameIssuedBy)) {
                        vulnId = 'ID-untrusted-server-cert'
                    }
                }
                break;
            case -207:
                vulnId = 'ID-cert-invalid'
                break;
            case -208:
                vulnId = 'ID-cert-weak-signature-algo'
                break;
            case -211:
                vulnId = 'ID-cert-weak-public-key'
                break;
        }

        // add this as vulnerability
        let dummyAttack = {
            scanId,
            scanlogId,
            href: electronCertError.uri,
            httpRequest: {
                uri: electronCertError.uri,
                method: 'GET',
                headers: {}
            }
        }
        this.addVulnerabilitytoResult(dummyAttack, vulnId, electronCertError)

        //TLS/SSL Server Certificate Expired if vuln missing, ll excute below 
        if (certErr.code != -201) {
            let validExpiry = (new Date(electronCertError.partialCert.validExpiry) - new Date()) / (24 * 60 * 60 * 1000)
            if (validExpiry <= 0) {
                vulnId = 'ID-cert-has-expired'
                electronCertError = {
                    chromiumNetErr: "net::ERR_CERT_DATE_INVALID",
                    description: ` Issued to Common Name(CN): ${commonName}, valid till: ${valid_to}.\nDescription: The server responded with a certificate that, by our clock, appears to either not yet be valid or to have expired.This could mean: \n1.An attacker is presenting an old certificate for which they have managed to obtain the private key.\n2.The server is misconfigured and is not presenting a valid cert.\n3.Our clock is wrong.`
                }
                this.addVulnerabilitytoResult(dummyAttack, vulnId, electronCertError)
            }
        }

        let certErrVuln = {
            // common info
            scanId,
            scanlogId,
            uri: electronCertError.uri,

            // 'attack' info so vuln is more standard
            attack: dummyAttack,

            // the cert vuln
            vulns: dummyAttack.result.vulns
        }

        this.networkScanner.emit('vulnerability-found', certErrVuln)
    }

    /**
     * New request received from crawler - check for upcoming SSL expiry
     * @param {request} originalRequest request as received from crawler
     * @override
     */
    async processNewRequest(originalRequest) {
        let pluginStorageScanScope = this.getPluginScopedStore(originalRequest, 'this-scan')
        if (!pluginStorageScanScope.checkedUpcomingCertErr) {
            pluginStorageScanScope.checkedUpcomingCertErr = true
            let parsedUrl = new URL(originalRequest.httpRequest.uri)
            let hostname = parsedUrl.hostname
            if (parsedUrl.protocol != 'https:') {
                return
            }
            let cert = []
            cert = await this.getdts(hostname)

            if (cert && cert.bits.toString().length > 0 && cert != 'Error') {
                // Check CName
                if (CertErrdts.length == 0) {//avoid recheck and duplicate
                    let electronCertError = []
                    let CNameList = cert.subject.CN + ', ' + cert.subjectaltname
                    CNameList = CNameList.replace(/DNS:/g, '').split(', ')
                    let vuln = false
                    // let CNameAlt = cert.subjectaltname
                    if (!CNameList.includes(hostname) && !/\*./.test(CNameList)) {
                        vuln = true
                    }
                    if (/\*./.test(CNameList)) {
                        for (let ele of CNameList) {
                            if (!vuln && ele.includes('*.')) {
                                ele = ele.replace('*.', '.*.')
                                let regexp = new RegExp(ele, 'i')
                                if (!regexp.test(hostname)) {
                                    vuln = true;
                                    break
                                }
                            }
                        }
                    }
                    if (vuln) {
                        electronCertError = {
                            chromiumNetErr: "net::ERR_CERT_COMMON_NAME_INVALID",
                            description: ` Issued to Common Name(CN): ${CNameList.join()}, valid till: ${cert.valid_to}.\nDescription: The server responded with a certificate whose common name did not match the host name.This could mean: \n\n1.An attacker has redirected our traffic to their server and is presenting a certificate for which they know the private key.\n\n2. The server is misconfigured and responding with the wrong cert.\n\n3.The user is on a wireless network and is being redirected to the network's login page.\n\n4. The OS has used a DNS search suffix and the server doesn't have a certificate for the abbreviated name in the address bar.`
                        }
                    }
                }

                // checkUpcomingCertificateExpiry
                let expiryDate = new Date(cert.valid_to)
                let Expdetails = []
                // if its going to expire soon, report it
                let daysToExpire = (expiryDate - new Date()) / (24 * 60 * 60 * 1000) // millis -> days
                if (daysToExpire > 0 && daysToExpire < this.getMetadata(originalRequest).vulnerabilities['ID-certificate-will-expire-soon'].reportCertWillExpireInDays) {
                    Expdetails = {
                        daysToExpire,
                        cert,
                    }
                }
                if (Expdetails.length > 0 || electronCertError.length > 0) {
                    // report vulnerability
                    let attack = {
                        originalRequest,
                        httpRequest: originalRequest.httpRequest,
                        attackArea: 'certificate-check',
                        hostname: parsedUrl.hostname,
                        href: parsedUrl.href,
                        method: 'GET',
                        headers: res.req.getHeaders(),
                        scanId: originalRequest.scanId,
                        scanlogId: originalRequest.scanlog_id,
                    }
                    if (Expdetails.length > 0) {
                        this.addVulnerabilitytoResult(attack, 'ID-certificate-will-expire-soon', details)
                    }

                    if (electronCertError.length > 0) {
                        this.addVulnerabilitytoResult(attack, 'ID-cert-common-name-invalid', electronCertError)
                    }
                    // create a dummy attack and vulnerability structure
                    let certErrVuln = {
                        // common info
                        scanId: originalRequest.scanId,
                        scanlogId: originalRequest.scanlog_id,
                        originalRequest: attack.originalRequest,

                        // attack response objects.
                        attack,
                        response: {
                            headers: res.headers,
                            statusCode: res.statusCode,
                            statusMessage: res.statusMessage
                        },

                        // the cert vuln
                        vulns: _.get(attack, "result.vulns")
                    }
                    this.networkScanner.emit('vulnerability-found', certErrVuln)
                }
            }
        }

        // this.checkUpcomingCertificateExpiry(hostname, parsedUrl.port)
        // this.checkCName(hostname)
    }

    /* async checkCName(hostname) {
        let cert = await this.getdts(hostname)
        if (cert && cert != 'Error') {
            let CName = cert.subject.CN
            let CNameAlt = cert.subjectaltname
            if (!CName.includes(hostname) && !CNameAlt.includes(hostname)) {
                if (CName.includes('*.')) {
                    CName = CName.replace('*.', '')
                    //hostname = hostname.match(/\w+(?:\.co)?\.\w+$/)[0]
                }
                if (!hostname.includes(CName)) {
                    electronCertError = {
                        chromiumNetErr: "net::ERR_CERT_COMMON_NAME_INVALID",
                        description: ` Issued to Common Name(CN): ${cert.subject.CN}, valid till: ${cert.valid_to}.\nDescription: The server responded with a certificate whose common name did not match the host name.This could mean: \n\n1.An attacker has redirected our traffic to their server and is presenting a certificate for which they know the private key.\n\n2. The server is misconfigured and responding with the wrong cert.\n\n3.The user is on a wireless network and is being redirected to the network's login page.\n\n4. The OS has used a DNS search suffix and the server doesn't have a certificate for the abbreviated name in the address bar.`
                    }
                    this.addVulnerabilitytoResult(originalRequest, 'ID-cert-common-name-invalid', electronCertError)
                }
            }
        }
    }/*  */

    getdts(hostname) {
        return new Promise((resolve) => {
            let URL = `https://${hostname}/`
            let options = {
                rejectUnauthorized: false, // Don't bother with SSL validation
            }
            https.get(URL, options, (resp) => {
                if (resp.statusCode) {
                    let cert = this.alldts(resp)
                    //resp.socket.getPeerCertificate()
                    resolve(cert)
                }
                else {
                    resolve("Error")
                }
            });
        });
    }
    alldts(resp) {
        return new Promise((resolve) => {
            let certdts = resp.socket.getPeerCertificate()
            resolve(certdts)
        });
    }
}

let CertErrdts = []

module.exports = CertificateIssuesPlugin