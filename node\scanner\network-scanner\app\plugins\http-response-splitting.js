const debug = require('debug')('HttpResponseSplitting')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class HttpResponseSplitting extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-http-response-splitting'
    }

    /**
     * forms attack request and performs network attack
     * @param {request} request new request received from crawler
     * @override
     */
    processNewRequest(request) {
        let attack = {
            httpRequest: _.cloneDeep(request.httpRequest),
            originalRequest: request,
            encoding: "raw"
        }
        attack.httpRequest.followRedirect = () => { return false }
        this.performNetworkAttack(attack)
    }


    getAttackVectors() {
        return HRPVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator', 'uri-query-params', 'uri-permutation']
    }

    /**
      
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let randomheaderval = _.get(attack, 'result.resp.httpResponse.headers["randomheader"]', '')
        if (randomheaderval.length == 0) { return }

        let headers = Object.keys(attack.result.resp.httpResponse.headers)
        for (var i = 0; i < headers.length - 1; i++) {
            if (headers[i] == 'randomheader') {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.result.resp.httpResponse.headers)
            }
        }
    }
}
// vectors & matches ...
const HRPVectors = [
    `%0d%0aRandomHeader:NO`,
    `%0d%0a%20RandomHeader:NO`
]

module.exports = HttpResponseSplitting