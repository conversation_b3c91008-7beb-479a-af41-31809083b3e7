const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// real click action - send input actions for mouseup, mousedown
class RealClickAction {
    constructor(xpath, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.annotation = annotation
    }

    get actionType() {
        return 'real-click'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let actionSucceeded = true // optimistic approach

        // generate click at that position if element is visible.
        let encXPath = utils.encode(this.xpath)
        let jscode = `indusfaceRenderer.scrollIntoView('${encXPath}', 'center')`
        await utils.timedPromise(browser.webContents.executeJavaScript(jscode))
        jscode = `indusfaceRenderer.getBoundingRect('${encXPath}')`
        let rect = await utils.timedPromise(browser.webContents.executeJavaScript(jscode), null)
        if (rect && rect.inViewport) {
            let center = {
                x: Math.floor(rect.left + (rect.width / 2)),
                y: Math.floor(rect.top + (rect.height / 2))
            }

            // send mouse down and mouse up with small delay to simulate a click.
            await browser.webContents.sendInputEvent({
                type: 'mousedown',
                x: center.x,
                y: center.y,
                clickCount: 1
            })
            await executer.waitForDocSteady(200, 500)

            await browser.webContents.sendInputEvent({
                type: 'mouseup',
                x: center.x,
                y: center.y,
                clickCount: 1
            })
            await executer.waitForDocSteady(200, 500)

        } else {
            utils.log('\tSkipping - element is hidden/0-size')
            actionSucceeded = false
        }

        return actionSucceeded
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = RealClickAction