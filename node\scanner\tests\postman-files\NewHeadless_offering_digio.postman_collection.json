{"info": {"_postman_id": "72054959-1908-4e9d-9fde-0695c77feec6", "name": "VAPT Headless", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Offerings API", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Authorization", "value": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}, {"key": "x-api-key", "value": "8gkiWwUoYD66uYcyzV1iU9Quq5VDUoxf8GXTROV3", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"dateOfBirth\": \"1673-08-20\",\r\n  \"gender\": \"M\",\r\n  \"mobileNumber\": \"9975846280\",\r\n  \"pan\": \"**********\",\r\n  \"schemeCode\": \"ARMP0000109\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://service-api.qa-aegonlife.com/autouw/offerings", "protocol": "https", "host": ["service-api", "qa-aegonlife", "com"], "path": ["autouw", "offerings"]}}, "response": []}, {"name": "DIGIO WebHook", "request": {"method": "POST", "header": [{"key": "X-Digio-Checksum", "value": "d7c14d44500beab4591fb6709eb8e85b824fa5a3742cbc35b4d121529c8cc349", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"entities\": [\r\n        \"KYC_REQUEST\"\r\n    ],\r\n    \"payload\": {\r\n        \"KYC_REQUEST\": {\r\n            \"id\": \"KID\",\r\n            \"status\": \"Status Value\",\r\n            \"reference_id\": \"CRN\",\r\n            \"transaction_id\": \"TXN\"\r\n        }\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://service-api.qa-aegonlife.com/digioevents", "protocol": "https", "host": ["service-api", "qa-aegonlife", "com"], "path": ["digioevents"]}}, "response": []}], "protocolProfileBehavior": {}}