/**
 * Common utilities for Haiku. In a class primariliy for namespace
 */

const URL = require('url').URL
const querystring = require('querystring')
const dns = require('dns')
var fs = require('fs')
const he = require('he')
const FingerPrint = require('./fingerprint')
const logger = require('./haiku-logger')
const normalizeUrl = require('normalize-url')
const archiver = require('archiver')
const cookieParse = require('cookie-parse')
const caseless = require('caseless')
const _ = require('lodash')
const ParseDomain = require('parse-domain')
const cheerio = require('cheerio')
const { SSO_PROVIDERS } = require('../config/app-constants')
let Duplex = require('stream').Duplex;
const os = require('os');
const crypto = require('crypto')
let networkScannerConfig = require('../../network-scanner/config/network-scanner-config');
const { execSync } = require('child_process')
const ping = require('ping');
const Traceroute = require('nodejs-traceroute');
const newman = require('newman');
const PostmanCollection = require('postman-collection')
let { TextDecoder } = require('util');

/**
 * Regex used to get session id/token from path component. Also used to replace session id with fresh session
 * Regexes are expected to have three capturing parantheses 
 *      The first will have the part before session token 
 *      The second is the session token 
 *      The third will have the part after session token 
 * The regexpes will be run on each path component
 */
__sessionIdPathRegex = [
    //  asp style session using () in path eg. /somepath/(S(unzwpk2x3gdbcqbzfcjz3lfc))/web/xyz.aspx
    /^(\()(.+)(\))$/,
    //  jsessionid style session ;sessionid eg. /somepath/j_security_check;jsessionid=...
    /(.*;.*(?:(?:sess|session)[_\-]?(?:id|key|name)[=]?))(.*)($)/
]

/**
 * HTTP headers considered to be authorization headers. For now have to provide all case combinations to try for
 */
__authHeaders = ['Cookie', 'cookie', 'Authorization', 'authorization', 'Sessionid', 'sessionId']


class HaikuUtils {

    /**
     * returns a promise that resolves after the specified time.
     * Usage : 
     * await( sleep(1000)) // sleep for 1 second
     * @param {Number} ms milliseconds to sleep for
     */
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Resolves with a specified value if the passed promise is pending after a timeout. 
     * @param {Promise} promise promise to wrap with timeout
     * @param {Number} timeout milliseconds to wait for promis to resolve
     * @param {*} valOnTimeout value to resolve to on timeout
     */
    static timedPromise(promise, timeout, valOnTimeout = 'timed out',) {
        let timeoutPromise = new Promise((resolve) => {
            let timer = setTimeout(() => {
                resolve(valOnTimeout)
            }, timeout)
        })
        return Promise.race([promise, timeoutPromise])
    }

    /**
     * Return the converted string if it's an Object then stringify otherwise return the default string
     * @param {String} input param str 
     */
    static stringifyParam(strOrOb) {
        if (_.isObject(strOrOb)) {
            strOrOb = JSON.stringify(strOrOb);
        }
        return strOrOb;
    }

    /**
     * Get a random interger in specified range
     * @param {Number} min low end of the range
     * @param {Number} max high end of teh range
     */
    static getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }

    /**
     * Return parsed header values
     */
    static parseHeaderVal(headerVal) {
        let list = {};

        if (headerVal) {
            headerVal.split(',').forEach(function (headerKeyVal) {
                let parts = headerKeyVal.split('=');
                list[parts.shift().trim()] = decodeURI(parts.join('='));
            });
        }

        return list;
    }

    /**
     * return the bigram array of tokens
     * @param {array} tokens array of tokens
     */
    static bigrams(tokens) {
        let bigrams = []
        for (let i = 0; i < tokens.length - 1; i++) {
            bigrams.push([tokens[i], tokens[i + 1]])
        }
        return bigrams
    }

    /**
     * @typedef {Object} fingerprints
     * @property {fingerprint} structure  fingerprint of all the HTML/XML tags without content
     * @property {fingerprint} content fingerprint of all the text i.e. content of text nodes
     */

    /**
     * Get structure and content fingerprints from the response.
     * @returns {fingerprints} fingerprints
     * @param {response} response The HTTP response
     */
    static getFingerprints(response) {
        let tags = []
        let lines = []

        let success = false
        try {
            let $ = response.cheerio

            // get the tags for structure fingerprint
            tags = $('*').map((i, el) => {
                return el.tagName;
            }).get();

            // get lines (text) as context
            lines = $('*').contents().filter((i, e) => {
                return e.type == 'text';
            }).map((i, e) => {
                return e.data.trim();
            }).get();
            lines = lines.filter(line => !/^\s*$/.test(line)) // remove blank lines
            success = true
        } catch (err) {
            logger.log('error', `returning dummy fingerprints due to error in generating fingerprints ${err.toString()}`)
            tags = ['-error-']
            lines = ['-error-']
        }

        // generate the fingerprints
        let fingerprints = {
            structure: new FingerPrint(HaikuUtils.bigrams(tags)),
            content: new FingerPrint(lines),
            success
        }
        return fingerprints
    }

    /**
     * Generate the prev style key so that refresh requests can work correctly. Time limited since after a 
     * point all rrevalidation files will have latest haiku key
     */
    static _PREV_DELETE_AFTER_15_FEB_2020_genHaikuKey(httpRequest) {
        // normalize URL
        let url = new URL(httpRequest.uri || httpRequest.url) // scanner uses uri, crawler uses url
        let normHref = normalizeUrl(url.href, {
            stripHash: true
        })
        url = new URL(normHref)

        // ignore session-ish information in pathname. 
        // asp style session using () in path -> remove entire path component eg. /somepath/(S(unzwpk2x3gdbcqbzfcjz3lfc))/web/xyz.aspx
        // jsessionid style session ;sessionid -> remove part after the ; eg. /somepath/j_security_check;jsessionid=...
        url.pathname = HaikuUtils.canonacalizePathname(url.pathname)

        let {
            params
        } = HaikuUtils.splitIntoParamsAndValues(httpRequest)

        // sort so that even the POST params are sorted. normalize will only do that for query params
        params.sort()
        let haikuKey = httpRequest.method + '-' + url.host + url.pathname + '?' + params.join('&')
        return haikuKey
    }

    /**
     * Normalize including removing www, hash, sorting params
     * Haiku key is method + normalizeed host+path + param names
     * @param {HTTPRequest} httpRequest Request to generate a normalized Key from
     */
    static genHaikuKey(httpRequest) {
        // normalize URL
        let url = new URL(httpRequest.uri || httpRequest.url) // scanner uses uri, crawler uses url
        let normHref = normalizeUrl(url.href, {
            stripHash: true,
            stripWWW: true,
            removeTrailingSlash: true
        })
        url = new URL(normHref)

        // ignore session-ish information in pathname. 
        // asp style session using () in path -> remove entire path component eg. /somepath/(S(unzwpk2x3gdbcqbzfcjz3lfc))/web/xyz.aspx
        // jsessionid style session ;sessionid -> remove part after the ; eg. /somepath/j_security_check;jsessionid=...
        url.pathname = HaikuUtils.canonacalizePathname(url.pathname)

        let {
            params
        } = HaikuUtils.splitIntoParamsAndValues(httpRequest)

        // sort so that even the POST params are sorted. normalize will only do that for query params
        params.sort()
        let haikuKey = httpRequest.method + '-' + url.host + url.pathname + '?' + params.join('&')
        return haikuKey
    }

    /**
     * Returns canonacalized host - 
     *  remove www. if it's there 
     * @param {string|URL} inUrl The url to canonacalize
     */
    static canonacalizeHost(inUrl) {
        try {
            let theUrl = new URL(inUrl)
            return theUrl.host.replace(/^www\./, '')
        } catch (e) {
            return `error.in.parsing.${inUrl}`
        }
    }

    /**
     * Returns pathname with session info replaced and no trailing /
     * 
     * @param {string} pathname pathname to parse
     */
    static canonacalizePathname(pathname) {
        let canPathname = HaikuUtils.pathnameSansSession(pathname);

        // also strip any trailing '/' in the pathname
        if (canPathname.endsWith('/')) {
            canPathname = canPathname.slice(0, -1);
        }

        return canPathname
    }

    /**
     * Returns pathname with session info replaced with a constant SESSIONID
     * ignore session-ish information in pathname. 
     * asp style session using () in path -> remove entire path componet eg. /somepath/(S(unzwpk2x3gdbcqbzfcjz3lfc))/web/xyz.aspx
     * jsessionid style session ;sessionid -> remove part after the ; eg. /somepath/j_security_check;jsessionid=...
     *
     * @param {string} pathname pathname to parse
     */
    static pathnameSansSession(pathname) {
        let pathComponents = pathname.split('/')
        let pathSansSession = []
        for (let component of pathComponents) {
            for (let regex of __sessionIdPathRegex) {
                component = component.replace(regex, '$1SESSIONID$3')
            }
            pathSansSession.push(component)
        }
        return pathSansSession.join('/')
    }

    /**
     * Returns Info about sesssion that can be used to update a future HTTP Request
     * Session info is looked for in:
     * 1. HTTP Headers: from __authHeaders (cookie, Authorization,...)
     * 2. Pathname: path component/subcomponents that look session-ish (see {@link pathnameSansSession} )
     * 3. Query/POST params: params that look session-ish
     * @param {HTTPRequest} httpRequest Request to extract sessionish info from
     */
    static getSessionInfo(httpRequest) {
        let sessionInfo = {
            uriPath: null,
            headers: {},
            status: null
        }

        // 1. HTTP Headers: cookie, Authorization
        for (let header of __authHeaders) {
            if (httpRequest.headers && httpRequest.headers[header]) {
                sessionInfo.headers[header] = httpRequest.headers[header]

                let authHeaderExists = false;
                let authKey = '';

                if (httpRequest.headers['authorization']) {
                    authHeaderExists = true;
                    authKey = 'authorization';
                }
                else if (httpRequest.headers['Authorization']) {
                    authHeaderExists = true;
                    authKey = 'Authorization';
                }

                //Check if bearer token is present, if not then continue checking other session headers
                if (authHeaderExists && httpRequest.headers[authKey].toLowerCase() == 'bearer') {
                    continue;
                }

                //check if value exists in sessionInfo.headers
                if (sessionInfo.status == null && httpRequest.headers[header] != null) {
                    sessionInfo.status = true
                }
            }
        }

        // 2. Pathname: path component/subcomponents that look sessionish (see {@link pathnameSansSession} )
        let url = new URL(httpRequest.uri || httpRequest.url) // scanner uses uri, crawler uses url
        let components = url.pathname.split('/')
        for (let componentIdx = 0; componentIdx < components.length; componentIdx++) {
            for (let regexIdx = 0; regexIdx < __sessionIdPathRegex.length; regexIdx++) {
                let component = components[componentIdx]
                let regex = __sessionIdPathRegex[regexIdx]
                let match = regex.exec(component)
                if (match) {
                    sessionInfo.uriPath = {
                        componentIdx,
                        regexIdx,
                        session: match[2]
                    }
                    break
                }
            }

            // if we found session in path, we are done
            if (sessionInfo.uriPath) {
                break
            }
        }

        // 3. Query/POST params: params that look session-ish 
        // ---------- FUTURE after seeing real life cases ----------

        // give the session info
        return sessionInfo
    }

    /**
     * 
     * @param {HTTPRequest} httpRequest Request in which to update session
     * @param {sessionInfo} newSessionInfo as returned by {@link getSessionInfo}
     * @param {Boolean} smartUpdate if true, only updates HTTP request where it has session components,
     *                               false means always blindly add all session components
     * We can be not-smart only about headers (params in future), for path components, have to match and replace always
     */
    static updateSession(httpRequest, newSessionInfo, smartUpdate = true) {
        // get session info for the current Request
        let origSessionInfo = HaikuUtils.getSessionInfo(httpRequest)

        // -- Update httpRequest to the new Session info. --

        // 1. HTTP Headers: cookie, Authorization
        if (newSessionInfo.headers) {
            for (let header of __authHeaders) {
                // sanity
                if (!newSessionInfo.headers[header]) {
                    continue
                }

                if (smartUpdate && !origSessionInfo.headers[header]) {
                    // smart update means only update not add a session 
                    continue
                }
                _.set(httpRequest, `headers.${header}`, newSessionInfo.headers[header])
            }
        }

        // 2. Pathname: path component/subcomponents that look sessionish (see {@link pathnameSansSession} )
        // ensure that the session info is the same in new & orig sessions and session info is different
        let okToUpdatePathSession = newSessionInfo.uriPath && origSessionInfo.uriPath
        okToUpdatePathSession = okToUpdatePathSession && (newSessionInfo.uriPath.componentIdx == origSessionInfo.uriPath.componentIdx)
        okToUpdatePathSession = okToUpdatePathSession && (newSessionInfo.uriPath.regexIdx == origSessionInfo.uriPath.regexIdx)
        okToUpdatePathSession = okToUpdatePathSession && (newSessionInfo.uriPath.session != origSessionInfo.uriPath.session)

        if (okToUpdatePathSession) {
            // update session in the appropriate path component
            let destUrl = new URL(httpRequest.uri)
            let destPathComponents = destUrl.pathname.split('/')
            let updatedComponent = destPathComponents[newSessionInfo.uriPath.componentIdx]
            let sessionRegex = __sessionIdPathRegex[newSessionInfo.uriPath.regexIdx]
            updatedComponent = updatedComponent.replace(sessionRegex, `$1${newSessionInfo.uriPath.session}$3`)

            // replace in destination path
            destPathComponents[newSessionInfo.uriPath.componentIdx] = updatedComponent
            destUrl.pathname = destPathComponents.join('/')

            // update the http request
            httpRequest.uri = destUrl.href
        }

        // 3. Query/POST params: params that look session-ish 
        // ---------- FUTURE after seeing real life cases ----------

    }

    /**
     * Clear the existing session values of request. i.e. Used during get privilege status of url.
     * @param {HTTPRequest} httpRequest Request in which to clear session
     */
    static clearSession(httpRequest) {
        // get session info for the current Request
        let origSessionInfo = HaikuUtils.getSessionInfo(httpRequest);

        let headers = origSessionInfo.headers;

        if (headers) {
            for (let authHeader of __authHeaders) {
                if (headers[authHeader]) {
                    _.set(httpRequest, `headers.${authHeader}`, '');
                }
            }
        }
    }

    /**
     * Returns 2 arrays, all parameter names and all parameter values across query (request line) and POST body
     * @param {httpRequest} httpRequest The http request to parse
     */
    static splitIntoParamsAndValues(httpRequest) {
        let params = []
        let values = []
        try {
            let url = new URL(httpRequest.uri || httpRequest.url) // scanner uses uri, crawler uses url
            if (url.searchParams) {
                // query params & vlues
                params.push(...Array.from(url.searchParams.keys()))
                values = Array.from(url.searchParams.values())
            }

            // if we have a form encoded POST body, add its params, values as well
            if (_.get(httpRequest, "headers['Content-Type']", '').includes('application/x-www-form-urlencoded') ||
                _.get(httpRequest, "headers['content-type']", '').includes('application/x-www-form-urlencoded')) {
                if (httpRequest.method == "POST" && _.get(httpRequest, 'body.length')) {
                    let searchParams = querystring.parse(httpRequest.body)
                    params.push(...Object.keys(searchParams))
                    values.push(...Object.values(searchParams))
                }
            }
        } catch (err) {
            // silently eat error.
        }
        return {
            params,
            values
        }
    }

    /**
     * Returns 2 arrays, all parameter names and all parameter values across query (requst line) and POST body
     * @param {httpRequest} httpRequest The http request to parse
     * @param {*} loginInfo Login info as captured by the crawler during login (usernames, passwords and the metadata on the inputs used)
     */
    static getLoginParams(httpRequest) {
        let loginInfo = httpRequest.loginInfo
        let usernameParams = []
        let pwdParams = []

        // sanity check
        if (loginInfo) {
            let paramsAndValues = HaikuUtils.splitIntoParamsAndValues(httpRequest)

            // figure out which params are username and which are password
            for (let i = 0; i < paramsAndValues.values.length; i++) {
                if (paramsAndValues.values[i] == loginInfo.username) {
                    usernameParams.push(paramsAndValues.params[i])
                }
                if (paramsAndValues.values[i] == loginInfo.password) {
                    pwdParams.push(paramsAndValues.params[i])
                }
            }

            // do we have a non unique set of username and password params
            if (_.intersection(usernameParams, pwdParams).length > 0) {
                // for cases where username and password params have some in common, eg. username and password are the same,
                // need to figure out which params are usernames and which params are password
                // identify the password fields, assume the rest are username fields
                let addlInfo = loginInfo.additionalInfo
                if (addlInfo) {
                    let pwdNames = addlInfo.passwords.map(e => e.name)
                    // from the username params, remove all params that look like passwords AND exist in the password param names
                    let uniqueUsernameParams = usernameParams.filter(u => {
                        return !(pwdNames.includes(u) && pwdParams.includes(u))
                    })
                    // from the password params, keep only params that look like passwords
                    let uniquePwdParams = pwdParams.filter(p => pwdNames.includes(p))

                    if (uniqueUsernameParams.length > 0 && uniquePwdParams.length > 0 &&
                        _.intersection(uniqueUsernameParams, uniquePwdParams).length == 0) {
                        // we have found our set of un, pwd
                        usernameParams = uniqueUsernameParams
                        pwdParams = uniquePwdParams
                    }
                }
            }

            // Still not unique, just remove all params in password that also are there in username
            if (_.intersection(usernameParams, pwdParams).length > 0) {
                pwdParams = pwdParams.filter(p => !usernameParams.includes(p))
            }

            // -- 
            // be careful adding too many heuristics, what we have now should cover most cases. Add any more only
            // after analysing in field use cases.
            // --
        }

        return {
            usernameParams,
            pwdParams
        }
    }
    /**
     * Get substring from string or Buffer object. Return is always a string. Mostly used to get 
     * parts of a HTTP response body eg. for vulnerability context.
     * @param {string|Buffer} stringOrBuffer string or Buffer to get sub string of
     * @param {Number} start start offset
     * @param {Number} length number of bytes in substring to return
     */
    static getSubString(stringOrBuffer, start, length) {
        if ('string' == typeof (stringOrBuffer)) {
            return stringOrBuffer.substr(start, length)
        } else {
            // assuming buffer
            return stringOrBuffer.toString(undefined, start, start + length)
        }
    }

    /**
     * Get a sanitized version of key from URL by replacing unsafe characters incuding path separator = '/'. 
     * eg. http://www.example.com:9999/a/b?x=y -> http-__www-example-com-9999_a_b-x-y
     * @param {string} theUrl Url to sanitize for AWS Key name
     */
    static getAWSKeyFromUrl(theUrl) {
        return theUrl.replace(/\//g, '_').replace(/[^a-zA-z0-9_]/g, '-')
    }

    /**
     * Add a host entry for both www.<site> and naked domain pointing  to the proxypass.<site>.indusguard.com i.e. skip WAF
     * @param {string} hostname Hostname for which we will add hosts file entry to bypass WAF
     * @param {boolean} scanViaWAF Update the hosts (/etc/hosts) file to either
     * 1. When true, scan via WAF by ensuring there is no entry for this site in /etc/hosts OR
     * 2. When false, scan origin directly bypassing waf by adding a host entry for both www.<site> and naked domain pointing to the proxypass.<site>.indusguard.com
     * @param {Object} logger any object with a .log method that will be called to log
     */
    static async updateHostFileIfNeeded(hostname, scanViaWAF, request, proxyPassDetails, logger = console) {
        let _hotsname
        if (hostname.startsWith("www.")) {
            _hotsname = hostname.replace("www.", "")
        } else {
            _hotsname = "www." + hostname
        }
        await this._updateHostFileIfNeeded(hostname, scanViaWAF, request, proxyPassDetails, logger)
        await this._updateHostFileIfNeeded(_hotsname, scanViaWAF, request, proxyPassDetails, logger)
        return proxyPassDetails;
    }

    /**
     * Add a host entry pointing site to the proxypass.<site>.indusguard.com i.e. skip WAF
     * @param {string} hostname Hostname for which we will add hosts file entry to bypass WAF
     * @param {boolean} scanViaWAF Update the hosts (/etc/hosts) file to either
     * 1. When true, scan via WAF by ensuring there is no entry for this site in /etc/hosts OR
     * 2. When false, scan origin directly bypassing waf by adding a host entry for both www.<site> and naked domain pointing to the proxypass.<site>.indusguard.com
     * @param {Object} logger any object with a .log method that will be called to log
     */
    static async _updateHostFileIfNeeded(hostname, scanViaWAF, request, proxyPassDetails, logger = console) {
        // strategy is to resolve scannerproxy.<site>.indusinfra.local/proxypass.<website>.indusguard.com
        // if we get an IP fromscannerproxy.<site>.indusinfra.local, it has a sslproxy setup and we will scan via proxy 
        // to handle teh case of site having old SSL protocol not supported by node.
        // if we get an IP for proxypass.<website>.indusguard.com, it is a SaaS site behind WAF and we will
        // scan the origin server directly instead of going via SaaS.
        try {
            let curHosts = fs.readFileSync('/etc/hosts', 'utf8').split('\n')
            let regex = "\\s+" + hostname
            let re = new RegExp(regex);
            let hosts = curHosts.filter((line) => !re.test(line)) // delete any existig host entry for this site
            let updateHostsFile = curHosts.length != hosts.length

            if (!scanViaWAF) {
                updateHostsFile = !!proxyPassDetails[hostname].ip || updateHostsFile;

                if (proxyPassDetails[hostname].ip) {
                    hosts.push(`${proxyPassDetails[hostname].ip}\t${hostname}`) // add entry for site being scanned
                }
                else {
                    if (proxyPassDetails) {
                        if (proxyPassDetails[hostname] && proxyPassDetails[hostname].ip) {
                            hosts.push(`${proxyPassDetails[hostname].ip}\t${hostname}`) // add entry for site being scanned
                            updateHostsFile = true;
                        }
                        else {
                            for (let url in proxyPassDetails) {
                                if (proxyPassDetails[url].ip) {
                                    hosts.push(`${proxyPassDetails[url].ip}\t${hostname}`) // add entry for site being scanned
                                    updateHostsFile = true;
                                }
                            }
                        }
                    }
                }
            }
            else {
                logger.log('info', `Ignoring update host file.`, HaikuUtils.getMetadataForLog(request));
            }

            if (updateHostsFile) {
                fs.writeFileSync('/etc/hosts', hosts.join('\n'))
                logger.log('info', `updated hosts file for ${hostname}`, HaikuUtils.getMetadataForLog(request))
            }
        } catch (err) {
            logger.log('error', `** Error updating /etc/hosts for ${hostname} (scan will proceed): ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
        }

    }

    /**
     * Get proxy pass address pointing site to the proxypass.<site>.indusguard.com
     * @param {string} hostname Hostname for which we will get proxy pass host detail to bypass WAF
     * @param {Object} logger any object with a .log method that will be called to log
     */
    static async getProxyPass(hostname, request, logger = console) {
        let proxyPassDetails = {};
        let _hostname;
        if (hostname.startsWith("www.")) {
            _hostname = hostname.replace("www.", "")
        } else {
            _hostname = "www." + hostname
        }

        let proxyhostname = await HaikuUtils.proxyPassLookup(hostname, request, logger);
        proxyPassDetails[hostname.toLowerCase()] = proxyhostname;

        proxyhostname = await HaikuUtils.proxyPassLookup(_hostname, request, logger);
        proxyPassDetails[_hostname.toLowerCase()] = proxyhostname;

        if (!proxyPassDetails[hostname.toLowerCase()].ip && !proxyPassDetails[_hostname.toLowerCase()].ip) {
            logger.log('info', `origin ip not found for host = ${hostname}`, HaikuUtils.getMetadataForLog({
                scanId: request.scanId,
                scanlogId: request.scanLogId || request.scanlog_id,
                uri: request.mainUrl,
                haikuKey: request.mainUrl
            }));
        }
        else {
            if (!proxyPassDetails[hostname.toLowerCase()].ip) {
                proxyPassDetails[hostname.toLowerCase()].ip = proxyPassDetails[_hostname.toLowerCase()].ip
            }

            if (!proxyPassDetails[_hostname.toLowerCase()].ip) {
                proxyPassDetails[_hostname.toLowerCase()].ip = proxyPassDetails[hostname.toLowerCase()].ip
            }
        }

        return proxyPassDetails;
    }

    /**
     * Loopk up proxy pass address which will resolv with IP & pointing site to the proxypass.<site>.indusguard.com
     * @param {string} hostname Hostname for which we will get proxy pass host detail to bypass WAF
     * @param {Object} logger any object with a .log method that will be called to log
     */
    static async proxyPassLookup(hostname, request, logger = console) {
        try {
            logger.log('info', `Attempting dnsLookUp for hostname = ${hostname}`, HaikuUtils.getMetadataForLog({
                scanId: request.scanId,
                scanlogId: request.scanLogId || request.scanlog_id,
                uri: request.mainUrl,
                haikuKey: request.mainUrl
            }))

            let proxyhostname = 'scannerproxy.' + hostname + '.indusinfra.local';
            let ip = await HaikuUtils.doDNSLookup(proxyhostname)

            //Prod Proxy
            if (!ip) {
                proxyhostname = 'proxypass.' + hostname + '.indusguard.com';
                ip = await HaikuUtils.doDNSLookup(proxyhostname)
            }

            //QA Proxy
            if (!ip) {
                proxyhostname = 'proxypass.' + hostname + '.indussecure.com';
                ip = await HaikuUtils.doDNSLookup(proxyhostname)
            }

            logger.log('info', `got proxypass IP = ${ip} for ${hostname} via ${proxyhostname}`, HaikuUtils.getMetadataForLog({
                scanId: request.scanId,
                scanlogId: request.scanLogId || request.scanlog_id,
                uri: request.mainUrl,
                haikuKey: request.mainUrl
            }))

            return {
                ip,
                proxyhostname
            }
        } catch (err) {
            logger.log('error', `** Error occured while dns lookup for ${hostname} (scan will proceed): ${err.toString()}`, HaikuUtils.getMetadataForLog({
                scanId: request.scanId,
                scanlogId: request.scanLogId || request.scanlog_id,
                uri: request.mainUrl,
                haikuKey: request.mainUrl
            }))
        }
    }


    /**
     * Get the IP from DNS hostname
     * @param {string} hostname Hostname for which to get the  IP
     */
    static doDNSLookup(hostname, logger = console) {
        return new Promise((resolve) => {
            dns.lookup(hostname, 4, (err, addr, family) => {
                if (err) {
                    logger.log('info', `could not resolve ${hostname} : ${err}`);
                    resolve(false);
                } else {
                    resolve(addr);
                }
            });
        });
    }

    /**
     * Construct result like IGW would do. Not completely eg. no HTML creation
     * but match context where relevant and english language of vulnerability details
     * in other cases. When we move to Haiku only, need to remove this and let the 
     * SOC portal format using the details directly
     * 
     * @todo - SHOULD MOVE TO EACH PLUGIN WHILE CALLING addVUlnerabilityToResult()
     * 
     * @param {object} vuln Vulnerability info
     * @param {string} vulnId  Which vulnerabiity to get details for
     */
    static getIGWResult(vuln, vulnId) {
        // Define a maximum length for the result string to prevent OOM issues
        const MAX_RESULT_LENGTH = 5 * 1024; // 5 KB limit, adjust as needed

        // specific for each vuln ID
        let result = ''
        try {
            let thisVulnDetails = vuln.vulns[vulnId].details

            switch (vulnId) {
                case 'ID-clickjacking-header-check':
                    result = thisVulnDetails.result
                    break;
                case 'ID-cookie-httponly-not-set':
                case 'ID-cookie-secure-not-set':
                case 'ID-broad-cookie-path':
                case 'ID-session-cookie-scoped-parent-domain':
                case 'ID-samesite-not-implemented':
                    result = 'The following cookies have potential vulnerabilities due to missing security attributes:\n '
                    result += thisVulnDetails.details.map(v => v.fullCookie).join(', \n')
                    break;
                case 'ID-cookie-weak-sessionid':
                    result = 'The following cookies have weak session id\'s in them:\n'
                    result += thisVulnDetails.details.map(v => v.name + ': ' + v.value).join('\n')
                    break;
                case 'ID-xss-header-protection-low':
                case 'ID-xss-header-protection-medium':
                case 'ID-csp-headers-not-set':
                case 'ID-unauth-next-js':
                case 'ID-unauth-next-js-blackbox-test':
                case 'ID-mime-sniffing':
                case 'ID-server-content-sniffing-low':
                case 'ID-host-header-injection':
                case 'ID-websocket-url-poisoning':
                case 'ID-xst-attack':
                case 'ID-php-object-injection':
                case 'ID-exposure-of-sensitive-filenames':
                case 'ID-webadmin-php-file':
                case 'ID-unvalidated-redirection':
                case 'ID-WebDav-OPTIONS-enabled':
                case 'ID-predictable-resource-location':
                case 'ID-put-method-enabled':
                case 'ID-delete-method-enabled':
                case 'ID-insecure-flash-embed-param':
                case 'ID-possible-sensitive-file-or-directory':
                case 'ID-webserver-default-page-detected':
                case 'ID-permissive-cross-domain-policy':
                case 'ID-struts2-dev-mode':
                case 'ID-cvs-repository-detected':
                case 'ID-epmm-authentication-bypass':
                case 'ID-apache-server-status-page':
                case 'ID-apache-tomcat-rce-found':
                case 'ID-readable-htaccess':
                case 'ID-remote-admin-interface':
                case 'ID-client-access-policy-FD':
                case 'ID-insecure-http-transport':
                case 'ID-csrf-vuln':
                case 'ID-csd-attack':
                case 'ID-unencrypted-viewstate':
                case 'ID-viewstate-mac-disabled':
                case 'ID-hsts-header-missing':
                case 'ID-Application-Detection':
                case 'ID-clickjacking-vulnerability':
                case 'ID-sensitive-file-directory':
                case 'ID-sensitive-file-directory':
                case 'ID-no-sql-injection':
                case 'ID-uncontrolled-format-string':
                case 'ID-ssrf':
                case 'ID-jquery-version-check':
                case 'ID-jsframework-version-check':
                case 'ID-account-lockout':
                case 'ID-no-captcha-on-login-page':
                case 'ID-client-side-renegotiation':
                case 'ID-server-side-renegotiation':
                case 'ID-dnssec-not-signed':
                case 'ID-session-resumption':
                case 'ID-dom-location-manipulation':
                case 'ID-user-controllable-tag-parameter':
                case 'ID-partial-user-controllable-script':
                case 'ID-cross-site-flashing':
                case 'ID-proxy-bypass':
                case 'ID-self-signed-certificate':
                    result = thisVulnDetails
                    break;
                case 'ID-local-file-inclusion':
                case 'ID-os-cmd-injection':
                case 'ID-sql-injection':
                case 'ID-xpath-injection':
                case 'ID-remote-file-inclusion':
                case 'ID-db-error-disclosure':
                case 'ID-log-injection':
                case 'ID-php-nginx-cmd-injection':
                case 'ID-sql-injection_Part2_AtVectors':
                case 'ID-code-injection':
                case 'ID-jenkins-args4j':
                case 'ID-orm-injection':
                case 'ID-apache-struts':
                case 'ID-html-form-found-in-redirect':
                    result = thisVulnDetails.details
                    break;
                case 'ID-perl-deserialization':
                    result = "Below url is vulnerable to perl deserialization:"
                    result += thisVulnDetails
                    break;
                case 'ID-oracle-weblogic-deserialization':
                    result = "Website vulnerable as response status code is:"
                    result += thisVulnDetails
                    break;
                case 'ID-oracle-weblogic-deserialization-bypass':
                    result = "Website vulnerable to oracle weblogic deserialization bypass attack as response status code is:"
                    result += thisVulnDetails
                    break;
                case 'ID-dotNet-deserialization-found':
                    result = "Website vulnerable to deserialization attack: "
                    result += thisVulnDetails
                    break;
                case 'ID-apache-httpd-dos':
                    result = "Website vulnerable to dos attack: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-vbulletin-RCE':
                    result = "Website vulnerable to RCE attack: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-ssjs':
                    result = "Website vulnerable to server side javascript injection attack: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-serverside-template-injection':
                    result = "Website vulnerable to server side template injection attack: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-rails-deserialization-found':
                    result = "Website vulnerable to rails deserialization attack: "
                    result += thisVulnDetails
                    break;
                case 'ID-application-error-msg-info-disclosure':
                    result = thisVulnDetails.context
                    break;
                case 'ID-session-id-in-url':
                case 'ID-sensitive-information-url':
                case 'ID-improper-token-handling':
                case 'ID-reveals-sensitive-info':
                case 'ID-reveals-sensitive-low':
                    result = 'Sensitive data disclosed: \n'
                    result += thisVulnDetails.map(v => v.result).join('\n')
                    break;
                case 'ID-robot-file-found':
                    result = 'robots.txt file found: '
                    result += thisVulnDetails
                    break;
                case 'ID-sensitive-document-file-found':
                    result = 'The sensitive txt files found: \n'
                    result += thisVulnDetails
                    break;
                case 'ID-xss-injection':
                case 'ID-html-injection':
                    result = thisVulnDetails.map(v => v.context).join('\n')
                    break;
                case 'ID-blind-sql-true-false':
                    result = 'Possible Blind SQL injection found: '
                    result += thisVulnDetails
                    break;
                case 'ID-debug-feature-enabled':
                    result = 'ASP.Net Debug feature enabled'
                    break;
                case 'ID-slow-response-time':
                    result = "Response time is : " + thisVulnDetails.allTimings.response +
                        " and First Byte Response time is: " + thisVulnDetails.allTimingPhases.firstByte
                    break;
                case 'ID-mixed-content-vulnerability':
                case 'ID-active-mixed-content-vulnerability':
                case 'ID-passive-mixed-content-vulnerability':
                    result = 'SSL pages load external resources using the http: protocol instead of the https: protocol.\n'
                    result += thisVulnDetails.details.join(';\n');
                    break;
                case 'ID-cross-origin-resource-sharing':
                    result = 'Cross Origin Resource Sharing is enabled.'
                    break;
                case 'ID-http-response-splitting':
                    result = 'Http Response splitting Vulnerability detected:'
                    result += thisVulnDetails
                    break;
                case 'ID-http-response-header-injection':
                    result = 'Http Response Header Injection Vulnerability detected:'
                    result += thisVulnDetails
                    break;
                case 'ID-serialized-object-http-message':
                    result = 'Serialized Object in HTTP Message Vulnerability detected:'
                    result += thisVulnDetails
                    break;
                case 'ID-ldap-injection':
                    result = 'LDAP Injection Vulnerability detected:'
                    result += thisVulnDetails
                    break;
                case 'ID-OPTIONS-enabled':
                    result = 'The following methods are enabled:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-sql-statement-check':
                    result = 'These are the SQL statements found:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-propfind-enabled':
                    result = 'The following internal IPs found:\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-external-entity-vuln': //this is for xml-post iterator
                    result = 'The following website is vulnerable to XXE injection :\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-external-entity-post-body-vuln': //this is for post-body iterator
                    result = 'The following website is vulnerable to XXE injection :\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-possible-physical-path-disclosure':
                    result = 'The following path disclosed:\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-cookie-manipulation':
                    result = thisVulnDetails.attackedCookieName + ' cookie can be misconfigured for the url ' + thisVulnDetails.href + '\n'
                    result += 'Attacked cookie: ' + thisVulnDetails.wholeCookie
                    break;
                case 'ID-remote-xsl-inclusion':
                    result = 'Below URL is vulnerable to remote xsl inclusion :\n'
                    result += thisVulnDetails
                    break;
                case 'ID-sensitive-data-submitted-without-ssl':
                case 'ID-insecure-transition-from-https':
                case 'ID-insecure-transition-from-http':
                    result = 'The following vulnerable parameters found:\n'
                    result += thisVulnDetails.map(v => v.result).join('\n')
                    break;
                case 'ID-pwd-submitted-using-http-GET':
                    result = 'Vulnerability details:\n'
                    result += thisVulnDetails.map(v => v.result).join(',\n')
                    break;
                case 'ID-source-code-disclosure':
                    result = 'Following sourcecode disclosed:\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-autocomplete-html-form':
                    result = 'autocomplete="off" attribute is missing in the form/input tag:\n'
                    result += thisVulnDetails.map(v => v.result).join('\n');
                    break;
                case 'ID-unencoded-characters':
                    result = 'The following unencoded characters are found:\n'
                    result += thisVulnDetails.result
                    break;
                case 'ID-email-id-found':
                    result = 'Following email address found:\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-TRACE-method-enabled':
                    result = 'TRACE method enabled'
                    break;
                case 'ID-TRACK-method-enabled':
                    result = 'TRACK method enabled'
                    break;
                case 'ID-ASPNET-TRACE-method-enabled':
                    result = 'Trace method enabled for asp net vulnerability found: \n'
                    result += thisVulnDetails
                    break;
                case 'ID-internal-ip-leak-found':
                    result = 'The following internal IP(s) found:\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-form-action-hijacking':
                    result = 'The following vulnerable parameters found:\n'
                    result += thisVulnDetails.map(v => v.dom_element).join('\n')
                    break;
                case 'ID-Http-basic Authentication':
                    result = `${thisVulnDetails.details.header} : ${thisVulnDetails.details.value}`
                    break;
                case 'ID-certificate-error':
                case 'ID-cert-common-name-invalid':
                case 'ID-cert-has-expired':
                case 'ID-untrusted-server-cert':
                case 'ID-cert-invalid':
                case 'ID-cert-weak-signature-algo':
                case 'ID-cert-weak-public-key':
                    result = `(${thisVulnDetails.chromiumNetErr}) :${thisVulnDetails.description}`
                    break;
                case 'ID-certificate-will-expire-soon':
                    result = `Certificate ${_.get(thisVulnDetails.cert, 'subject.CN')} will expire in ${thisVulnDetails.daysToExpire.toFixed(0)} days, valid until ${thisVulnDetails.cert.valid_to}`
                    break;
                case 'ID-hidden-form-input-field':
                    if (thisVulnDetails.details === undefined) return
                    result = 'The following header parameter is vulnerable:\n'
                    result += thisVulnDetails.details.map(d => "InputType= " + d.inputType + " : " + "name= " + d.name).join(', ')
                    break;
                case 'ID-jsf-viewstate-found':
                    if (thisVulnDetails.details === undefined) return
                    result = 'The following header parameter is vulnerable:\n'
                    if (thisVulnDetails.details.map(d => d.name) != null) {
                        result += thisVulnDetails.details.map(d => "inputType= " + d.inputType + "\n" + "name= " + d.name +
                            "\n" + "value= " + d.value + "\n" + "Element= " + d.dom_element).join(', ')
                    } else {
                        result += thisVulnDetails.details.map(d => "inputType= " + d.inputType + "\n" + "id= " + d.id +
                            "\n" + "value= " + d.value + "\n" + "Element= " + d.dom_element).join(', ')
                    }
                    break;
                case 'ID-hidden-field-susceptible-to-spam':
                    result = "HTML form susceptible to spam due to following harcoded email ids:\n"
                    result += thisVulnDetails.details.map(d => d.name + " : " + d.value).join(',')
                    break;
                case 'ID-html-form-without-csrf-protection':
                    result = "Following page has html form without csrf protection in it: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-credit-card-number-found':
                    result = 'Following credit card numbers found:\n'
                    result += thisVulnDetails.details.map(d => d.Card_Brand + " : " + d.Card_Number).join(',')
                    break;
                case 'ID-Microsoft-Server-Version-Disclosure':
                case 'ID-Web-Server-Version-Disclosure':
                    result = 'The following header has the vulnerability:\n'
                    result += thisVulnDetails.result
                    break;
                case 'ID-asp-net-version-headers':
                case 'ID-info-disclosure-http-headers':
                    result = 'Details below:\n'
                    result += thisVulnDetails.map(v => v.result).join('\n')
                    break;
                case 'ID-suspicious-comment-disclosure':
                    result = 'These are the Suspicious comments found:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-trojan-shell-script-detected':
                    result = 'Script that is suspected to be a trojan shell:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-database-connection-string-detected':
                    result = 'Possible Database Connection String Detected:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-sensitive-information-cached':
                    if (thisVulnDetails.details.msg) {
                        result = thisVulnDetails.details.msg
                        break;
                    } else {
                        result = 'The following headers are vulnerable:\n'
                        result += thisVulnDetails.details.header + ":" + thisVulnDetails.details.value
                        break;
                    }

                case 'ID-xml-rpc-detected':
                    result = 'The following URL is vulnerable to xml-rpc attack:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-php-deserialization':
                    result = 'The following URL is vulnerable to php deserialization attack:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-atlassian-confluence-rce':
                    result = 'Atlassian Confluence RCE Detected:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-viewstated-sensitive-data':
                    result = thisVulnDetails; // Use only the details passed from the plugin
                    break;
                case 'ID-php-cgi-argument-injection-vulnerability':
                    result = 'CGI Argument Injection Vulnerability Detected:\n'
                    result += thisVulnDetails
                    break;
                case 'ID-expression-language-injection':
                    result = 'Expression Language Injection Detected:\n'
                    result += thisVulnDetails.details
                    break;
                case 'ID-username-password-guess':
                    result = "Website is vulnerable to credential guessing attack with below username and password: \n"
                    result += "username: " + thisVulnDetails.details.username + "\n" +
                        "password: " + thisVulnDetails.details.password
                    break;
                case 'ID-common-credential':
                    if (thisVulnDetails.details.isPasswordCommon && thisVulnDetails.details.isUserNameCommon) {
                        result = "Username and password provided for testing site are found in top 100 commonly used usernames and passwords"
                        break;
                    }
                    if (thisVulnDetails.details.isPasswordCommon) {
                        result = "Password provided for testing site is found in top 100 commonly used passwords"
                        break;
                    }
                    if (thisVulnDetails.details.isUserNameCommon) {
                        result = "Username provided for testing site is found in top 100 commonly used usernames"
                        break;
                    }
                    break;
                case 'ID-authentication-bypass':
                    result = "Website is vulnerable to authentication bypass attack with below payloads in username and password: \n"
                    result += "username: " + thisVulnDetails.details.username + "\n" +
                        "password: " + thisVulnDetails.details.password
                    break;
                case 'ID-username-enumeration':
                    result = "Website is vulnerable to username enumeration attack where below error message appears when credentials tried are : \n"
                    result += " Username: " + thisVulnDetails.details.username + "\n Password: " + thisVulnDetails.details.password + "\n Message: " + thisVulnDetails.details.context
                    break;
                case 'ID-waf-ips-found':
                    result = 'We have observed with few attack requests that it\'s getting blocked by either WAF/IDS/IPS or some security measure which is in place.\n Captured details as below:\n'
                    result += ' Response message for triggering vulnerability: ' + thisVulnDetails.details.context + '\n Header Name: ' + thisVulnDetails.details.header + '\n Server Name: ' + thisVulnDetails.details.serverHeader +
                        '\n Cookie Name: ' + thisVulnDetails.details.cookie + '\n  '
                    break;
                case 'ID-cache-expired':
                    result = 'Found below cache control header set which will reduce performance of your server:\n'
                    result += thisVulnDetails.details.header + ":" + thisVulnDetails.details.value
                    break;
                case 'ID-ssl-old-version':
                    result = 'Older SSL/TLS versions found are listed below:\n'
                    result += thisVulnDetails.map(v => v.result).join(',\n ')
                    break;
                case 'ID-old-ciphers-found':
                    result = 'Weak or vulnerable SSL/TLS cipher suites found are listed below:\n'
                    result += thisVulnDetails.map(v => v.result).join(',\n ')
                    break;
                case 'ID-CBC-Ciphers-found':
                    result = 'Weak or vulnerable SSL/TLS cipher suites found are listed below:\n'
                    result += thisVulnDetails.map(v => v.result).join(',\n ')
                    break;
                case 'ID-apache-axis2-lfi':
                    result = "Website is vulnerable to apache axis2 local file inclusion: \n"
                    result += "Context: " + thisVulnDetails.context
                    break;
                case 'ID-weak-cryptography-detected':
                    result = 'Weak/Insecure cryptography is detected in website as per below:\n'
                    result += "Weak cryptography detected is: " + thisVulnDetails.matchedCrypto + "\n" +
                        "Context: " + thisVulnDetails.context
                    break;
                case 'ID-aws-ssrf':
                    result = "This site is vulnerable to SSRF attack against aws metadata: \n"
                    result += "Context: " + thisVulnDetails.context
                    break;
                case 'ID-lfi-ssrf':
                    result = "This site is vulnerable to local file inclusion SSRF attack: \n"
                    result += "Context: " + thisVulnDetails.context
                    break;
                case 'ID-ssrf-jsmol-lfi':
                    result = "This site is vulnerable to wordpress plugin JSMOL LFI SSRF attack: \n"
                    result += "Context: " + thisVulnDetails.context
                    break;
                case 'ID-http-verb-tampering':
                    result = "HTTP arbitrary methods are enabled in the application: \n"
                    result += "Method used is : " + thisVulnDetails.method + " and uri is : " + thisVulnDetails.href
                    break;
                case 'ID-insecure-transition-from-post-get':
                case 'ID-insecure-transition-from-get-post':
                    result += "HTTP GET method was used " + thisVulnDetails.href
                    break;
                case 'ID-script-source-code-disclosure':
                    result = "Website vulnerable to script source code disclosure: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-long-password-dos':
                    result = "Website is vulnerable to long password dos attacks as with very lengthy passwords response. "
                    result += thisVulnDetails
                    break;
                case 'ID-extjs-arbitrary-file-read':
                    result = "ExtJs Arbitrary File Read Detected 1 : \n"
                    result += thisVulnDetails
                    break;
                case 'ID-python-code-injection':
                    result = "Python Code Injection Detected\n"
                    result += thisVulnDetails
                case 'ID-ssi-injection':
                    result = "Possible SSI Injection Detected\n"
                    result += thisVulnDetails
                    break;
                case 'ID-json-injection':
                    result = "Possible Json Injection Detected\n"
                    result += thisVulnDetails
                    break;
                case 'ID-apache-ofbiz-authentication-bypass':
                    result = "Apache OfBiz Authentication bypass Detected\n"
                    result += thisVulnDetails
                case 'ID-apache-ofbiz-path-traversal':
                    result = "Context: "
                    result += thisVulnDetails.details[0]
                    break;
                case 'ID-insecure-websocket':
                    result += thisVulnDetails
                    break;
                case 'ID-css-injection':
                    result = thisVulnDetails;
                    break;
                case 'ID-vmware-ssti-rce':
                    result = "Vmware SSTI RCE Detected: \n"
                    result += thisVulnDetails
                    break;
                case 'ID-chrome-log-info-disclosure':
                    result = 'The following headers are vulnerable:\n'
                    result += thisVulnDetails.map(v => v.details.header + ': ' + v.details.value).join('\n')
                case 'ID-jvm-version-headers':
                    result = "JVM Version leakage: \n"
                    result += thisVulnDetails.details.header + ":" + thisVulnDetails.details.value
                    break;
                case 'ID-social-security-number-found':
                    result = 'Possible social security number found:\n'
                    result += thisVulnDetails.details.map(d => d.Card_Number).join(',')
                    break;
                case 'ID-w3-total-cache-debug-mode':
                    result = 'The site/page contains caching enabled by W3 Total Cache plugin. Found as below: \n'
                    result += thisVulnDetails
                    break;
                case 'ID-subresource-integrity-check':
                    result = "This site has following tags & there respective resources which are  missing integrity check or has not implemented the same: \n"
                    result += "List of tags: " + thisVulnDetails.map(d => d.tagsFound).join(',\n') + "\nList of urls: " + thisVulnDetails.map(d => d.urlsFound).join(',\n')
                    break;
                case 'ID-http-request-smuggling':
                    result += "HTTP Request Smuggling Variant = " + thisVulnDetails.attackVariant + " \n Variant Vector = " + thisVulnDetails.attackVector
                    break;
                case 'ID-external-entity-dos-vuln': //this is for xml-post iterator
                    result = "This site is vulnerable to XXE DOS attack: \n"
                    result += "Context: " + thisVulnDetails.context
                    break;
                case 'ID-external-entity-dos-post-body-vuln': //this is for post-body-iterator
                    result = "This site is vulnerable to XXE DOS attack: \n"
                    result += "Context: " + thisVulnDetails.context
                    break;
                case 'ID-slowloris-ddos-attack':
                    result = "This site is vulnerable to Slowloris DDOS attack because resources were kept busy for " + thisVulnDetails.detectionTimeoutVal + " seconds or more"
                    break;
                case 'ID-iframe-injection':
                    result = `An injected <iframe> was detected on the site, indicating a potential vulnerability.\n ${thisVulnDetails.description}\n\n`;
                    break;
                case 'ID-link-injection':
                    result = "We have found the link injection in the site. Below is the context: \n" + thisVulnDetails.context
                    break;
                case 'ID-cgi-generic-unseen-parameter':
                    result = "This site is vulnerable to cgi-generic-unseen-parameter. Below are similarity stats: similarity=" + thisVulnDetails.similarpercentage + ', initial_uri:' + thisVulnDetails.initial_uri + ', attack_uri:' + thisVulnDetails.attack_uri
                    break;
                case 'ID-oracle-weblogic-auth-bypass':
                    result = "This site is vulnerable to oracle weblogic authentication bypass. kindly patch or update to newer safe version. context: " + thisVulnDetails.context
                    break;
                case 'ID-web-cache-poisoning':
                    result = "This site is vulnerable to web cache poisoning via unkeyed value in response. Below unkeyed value is reflected in response:"
                    result += thisVulnDetails.map(v => v.dom_element).join('\n')
                    break;
                case 'ID-x-forwarded-security-bypass':
                    result = "This site is vulnerable to x forwarded security bypass."
                    break;
                case 'ID-ssrf-exchange-server-attack':
                    result = "We have found the microsoft exchange server RCE vulnerability in the site. This can lead to auth bypass, RCE, and various post authentication flaws. It is recommended to install latest patch rolled out by vendor."
                    break;
                case 'ID-breach-attack':
                case 'ID-breach-attack-low':
                    result = "This site is vulnerable to possible BREACH attack because of following conditions: 1) Reflects user-input in the HTTP response bodies. 2) Served from a server that uses HTTP-level compression: " + thisVulnDetails
                    break;
                case 'ID-http-sys-rce':
                    result = "This site is vulnerable to HTTP.sys remote code execution vulnerability. A remote attacker can exploit this to execute arbitrary code with SYSTEM privileges. Kindly install latest security update according to Microsoft Bulletin MS15-034 and disable kernel caching feature to prevent exploitation."
                    break;
                case 'ID-ghost-vuln-detected':
                    result = "This site is vulnerable to GHOST vulnerability. The identified php version is below 5.6.6 kindly update to higher version or install patch to not use os version of glibc"
                    break;
                case 'ID-cgi-ssi-found':
                    result = "This site is vulnerable to CGI server side include vulnerability. Below is the context: \n" + thisVulnDetails.context
                    break;
                case 'ID-JWT-misconfiguration':
                    result = "Found user info: \n "
                    result += thisVulnDetails.result
                    break;
                case 'ID-JWT-none-algorithm':
                    result = "Found Unsecured JWT: \n "
                    result += thisVulnDetails.result
                    break;
                case 'ID-JWT-Incorrect-Session-Timeout':
                    result = "This site is vulnerable to JWT Incorrect Session Timeout vulnerability. Below is the context: \n Expired Date:" + thisVulnDetails.ToExpDate + ',\n Days to Expired: ' + thisVulnDetails.Days + '.'
                    break;
                case 'ID-apache-mod-cgi-lfi':
                    result = "This site is vulnerable to Path Traversal in " + thisVulnDetails.version + '(CVE-2021-41773). Found info: \n' + thisVulnDetails.data + '.'
                    break;
                case 'ID-esi-injection':
                    result = "This site is vulnerable to ESI Injection\nContext: " + thisVulnDetails.context + '.'
                    break;
                case 'ID-Apache-ETag-Header-Info':
                    result = "This site is vulnerable to Apache ETag inforamtion disclousure. Context: \n Node:" + thisVulnDetails.Inode + ',\n Size: ' + thisVulnDetails.Size + ',\n Modified Time: ' + thisVulnDetails.ModTime + '.'
                    break;
                case 'ID-password-in-response':
                    result = "Password Found in Response: " + thisVulnDetails.Credential
                    break;
                case 'ID-malicious-content-found':
                    result = "Malicious Content Found: " + thisVulnDetails.map(v => v.dom_element).join('\n')
                    break;
                case 'ID-credential-in-token':
                    result = "Found user info: \n"
                    result += thisVulnDetails.result
                    break;
                case 'ID-path-relative-stylesheet-import':
                    result = "Relative Path CSS Links found: \n" + thisVulnDetails.map(v => v.dom_element + '\nhref: ' + v.href).join('\n')
                    break;
                case 'ID-port-scanner':
                case 'ID-X-Permitted-CDP':
                case 'ID-browsable-web-directory':
                case 'ID-archive-backup-file':
                case 'ID-core-dump-file':
                case 'ID-accessible-by-ip-address':
                case 'ID-ms-exchange-proxyshell':
                case 'ID-ms-exchange-proxynotshell':
                    result = thisVulnDetails.result
                    break;
                case 'ID-possible-backup-file':
                    result = 'File Found: ' + thisVulnDetails.map(v => v.result).join('\n ')
                    break;
                case 'ID-target-blank':
                case 'ID-target-blank-low':
                    result = 'Insecure Third Party Link found: ' + thisVulnDetails.map(v => v.result).join('\n ')
                    break;
                case 'ID-text-injection':
                    result = 'Text Injected: '
                    result += thisVulnDetails.details
                    break;
                case 'ID-weak-encoding':
                    result = thisVulnDetails.map(v => v.result).join('\n')
                    break;
                case 'ID-Unknown':
                default:
                    logger.log('info', `Unknown Vulnerability ID ${vulnId}`, HaikuUtils.getMetadataForLog(vuln))
                    result = 'Vulnerabilty Found'
                    break;
            }
        } catch (err) {
            logger.log('info', `Error formating Vulnerability ID ${vulnId}, ${err.toString()}`, HaikuUtils.getMetadataForLog(vuln))
            result = 'Vulnerabilty Found'
        }

        // Truncate the result string if it exceeds the defined maximum length
        if (result && result.length > MAX_RESULT_LENGTH) {
            let actualLength = result.length;
            result = result.substring(0, MAX_RESULT_LENGTH) + '... [truncated]';
            logger.log('warn', `Truncated long result for vulnId ${vulnId}, actual length: ${actualLength}, truncated length: ${MAX_RESULT_LENGTH}`, HaikuUtils.getMetadataForLog(vuln));
        }

        return result
    }

    /**
     * Convert URL + param into IGW style key
     * eg for a POST http://www.example.com?a=b&c=d and body x=1&y=2 with y being attacked
     * the key would be http://www.example.com?a=&c=||y
     * Should be improved / replaced once we move to Haiku only eg. consider method, body
     * canonacalize url etc. Can be transitioned by sending both a HaikuKey (or object) and the
     * IGW key so SOC can migrate DB without losing 'first found' and whitelisted vulns etc.
     * @param {string} href the url to convert into key
     * @param {string} attackedParam 
     */
    static genIGWKey(href, attackedParam = '') {
        // remove all the values of query params
        try {
            let url = new URL(href)
            for (let p of url.searchParams.keys()) {
                url.searchParams.set(p, '')
            }
            return url.href + '||' + HaikuUtils.stringifyParam(attackedParam)
        } catch (err) {
            logger.log('error', `error generating IGW key ${err.toString()}`)
            return href + HaikuUtils.stringifyParam(attackedParam)
        }
    }

    /**
     * Gets the IGW (WAS) key based on the data we have from attack (original request or attack.href)
     * Also a special case when attack is on baseURI to avoid reporting duplicates due to atatcked URI being the key
     * @param {vuln} vuln Object describing vulnerability found
     */
    static getIGWKey(vuln) {
        let igwKeyUri = vuln.uri || _.get(vuln, 'attack.originalRequest.httpRequest.uri') || _.get(vuln, 'attack.href') || _.get(vuln, 'attack.httpRequest.uri') || 'http://no.uri.in.attack'
        let param = _.get(vuln, 'attack.param', '');
        if (vuln.attack && vuln.attack.area == 'BaseURI') {
            // if it is attack type 'BaseURI' create href as origin + param and nuke param 
            // like igwKeyUri='http://www.xyz.com/a/b/c', param=''
            let url = new URL(vuln.attack.href);
            url.pathname = param;
            igwKeyUri = url.href;
            param = '';
        }

        return HaikuUtils.genIGWKey(igwKeyUri, param)
    }

    /**
     * check for null or undefined for given value 
     * @param {*} value for which null or undefined need to be check
     */
    static isNullOrUndefined(value) {
        return value == null || value == undefined
    }

    /**
     * Returns a key that can be used to group vulnerabilities AND is human friendly.  
     * eg. a URL like https://example.com?a=1&b=2 would have the key generated as
     * example.com?a={...}&b={...}
     * --- Currently this is grouped by Original URL + params ---
     * 
     * @param {HTTPRequest} httpRequest Generate grouping key for this request
     */
    static generateGroupingKeyForRequest(httpRequest) {
        let groupingKey = '-no-uri-'
        try {
            let url = new URL(httpRequest.uri)

            // --- Generate grouping Key

            // ignore session-ish information in pathname. 
            // asp style session using () in path -> remove entire path componet eg. /somepath/(S(unzwpk2x3gdbcqbzfcjz3lfc))/web/xyz.aspx
            // jsessionid style session ;sessionid -> remove part after the ; eg. /somepath/j_security_check;jsessionid=...
            url.pathname = HaikuUtils.canonacalizePathname(url.pathname)

            let {
                params
            } = HaikuUtils.splitIntoParamsAndValues(httpRequest)

            // generate grouping key
            groupingKey = httpRequest.method + '-' + HaikuUtils.canonacalizeHost(url) + url.pathname
            if (params.length > 0) {
                groupingKey += '?' + params.map(p => p + '={...}').join('&')
            }
        } catch (err) {
            logger.log('error', `could not generate groupingKey ${err.toString()}`, HaikuUtils.getMetadataForLog(httpRequest))
        }
        return groupingKey
    }

    /**
     * For logging, get scanId from object by trying various properties since, based on where the
     * log is being called from, code may have access to different objects like request, vulnerability etc.
     * Only needed by scanner - each crawler runs one scan so it sets up common metadata right at the start
     * and all logs include that metadata
     * @param {Object} ob object to get metadata from
     */
    static getMetadataForLog(ob) {
        // sanity
        if (!ob) {
            return {
                scanId: -1,
                scanlogId: -1,
                uri: 'unknown',
                haikuKey: 'unknown',
                serviceId: -1,
                uploadId: -1
            };
        }

        // try and get metadata
        try {
            // Initialize with direct properties or null, trying the most common names first
            let scanId = ob.scanId;
            let scanlogId = ob.scanlogId || ob.scanLogId || ob.scanlog_id;
            let uri = ob.uri;
            let haikuKey = ob.haikuKey;
            let serviceId = ob.serviceId;
            let uploadId = ob.uploadId;

            // is it a request object
            let reqHttpReq = ob.httpRequest;
            if (reqHttpReq) {
                uri = uri || reqHttpReq.uri;
                haikuKey = haikuKey || reqHttpReq.haikuKey;
                serviceId = serviceId || reqHttpReq.serviceId;
            }

            // is it an attack object
            let originalRequest = ob.originalRequest;
            if (originalRequest) {
                uri = uri || (originalRequest.httpRequest && originalRequest.httpRequest.uri);
                haikuKey = haikuKey || (originalRequest.httpRequest && originalRequest.httpRequest.haikuKey);
                scanId = scanId || originalRequest.scanId;
                scanlogId = scanlogId || originalRequest.scanlogId || originalRequest.scanlog_id;
                serviceId = serviceId || originalRequest.serviceId;
            }

            // is it a vuln object
            let attack = ob.attack;
            if (attack) {
                let attackOriginalRequest = attack.originalRequest;
                if (attackOriginalRequest) {
                    uri = uri || (attackOriginalRequest.httpRequest && attackOriginalRequest.httpRequest.uri);
                    haikuKey = haikuKey || (attackOriginalRequest.httpRequest && attackOriginalRequest.httpRequest.haikuKey);
                    scanId = scanId || attackOriginalRequest.scanId;
                    scanlogId = scanlogId || attackOriginalRequest.scanlogId || attackOriginalRequest.scanlog_id;
                    serviceId = serviceId || attackOriginalRequest.serviceId;
                }
                let attackHttpReq = attack.httpRequest;
                if (attackHttpReq) {
                    serviceId = serviceId || attackHttpReq.serviceId;
                }
            }

            // Re-check serviceId from originalRequest/httpRequest if not found via attack structure
            if (originalRequest) {
                serviceId = serviceId || originalRequest.serviceId;
            }
            if (reqHttpReq) {
                serviceId = serviceId || reqHttpReq.serviceId;
            }

            // is it a 'options' as found in from SopperSchedulerAPI
            let body = ob.body;
            if (body) {
                scanId = scanId || body.session_id || body.scanId;
                scanlogId = scanlogId || body.scanlog_id || body.scanlogId;
            }

            // Return the values directly, they might be undefined if not found
            // This matches the original code's behavior in the try block.
            return {
                scanId,
                scanlogId,
                uri,
                haikuKey,
                serviceId,
                uploadId
            }
        } catch (err) { // Keep original catch block formatting
            return {
                scanId: -1,
                scanlogId: -1,
                uri: 'unknown',
                haikuKey: 'unknown',
                serviceId: -1,
                uploadId: -1
            }
        }
    }

    /**
     * Zips set of files based on the file spec
     * @param {string} filesSpec glob specifying files to zip
     * @param {string} outFilename output file name
     * @param {string} cwd directory where files matching fielspec are to be found
     * @param {string} prefix output directory prefix for the files.
     * @link https://www.archiverjs.com/archiver#glob
     * 
     * Depending on how you want the archive to be:
     * Keep directory structure: build filespec with directory and last 2 params blank
     *      like - zipFiles('/tmp/abc/*.txt', 'tmp/out.tar.gz')
     *      tar -tf => tmp/abc/out/1.txt, tmp/abc/2.abc etc.
     * Put files in differnt dir structure: build filespec with relative or no directory and pass last 2 params
     *      like - zipFiles('*.txt', '/tmp/out.tar.gz', '/tmp/abc', 'out-abc')
     *      tar -tf => out-abc/1.txt, out-abc/2.abc etc.
     */
    static zipFiles(filesSpec, outFilename, cwd, prefix) {
        // see https://www.archiverjs.com/index.html#quick-start
        let archive = archiver('tar', {
            gzip: true
        })

        // good practice to catch warnings (ie stat failures and other non-blocking errors)
        archive.on('warning', function (err) {
            logger.log('warning', `zipFiles - ${err.ToString()}`)
        })

        // good practice to catch this error explicitly
        archive.on('error', function (err) {
            throw err
        })
        archive.pipe(fs.createWriteStream(outFilename))

        // do the zip
        archive.glob(filesSpec, {
            cwd
        }, {
            prefix
        })
        return archive.finalize()
    }

    /**
     * Calculate fβ value
     * @param {Number} TP 
     * @param {Number} FN 
     * @param {Number} FP 
     * @param {Number} β 1 -> equal weightage to FP, FN, < 1 -> bias towards FN, >1 -> bias towards FPs.
     */
    static fBeta(TP, FN, FP, β = 1) {
        let βsq = β * β
        return ((1 + βsq) * TP) / (((1 + βsq) * TP) + βsq * FN + FP)
    }

    /**
     * Calculate precision value
     * @param {Number} TP 
     * @param {Number} FP 
     */
    static precision(TP, FP) {
        return TP / (TP + FP)
    }

    /**
     * Custom replacer to serialize config properties that the default JSON.stringify cannot handle
     * Specifically handles:  RegExp.
     * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify
     */
    static configReplacer(key, value) {
        if (_.isRegExp(value))
            return ({
                type: 'regex',
                source: value.source,
                flags: value.flags
            })
        else
            return value;
    }

    /**
     * Custom reviver to deserialize properties encoded by configReplacer()
     * Specifically handles:  RegExp.
     * See: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/parse
     */
    static configReviver(key, value) {
        if (value.type == 'regex')
            return (new RegExp(value.source, value.flags))
        else
            return value;
    }

    /**
     * Add any headers that must be included in every request eg. a specific header or session token.
     * If a header already exists, it will be overwritten
     * The method can handle headers and cookies. Cookies are treated specially in that you can set/overwrite 
     * sepcific cookies without affecting the other cookies.
     * eg.
     * A request with headers like:
     *    {
     *        referer:'https://www.indusface.com',
     *        test-header:666,
     *        AUTH-token: 'old-auth-token',
     *        Cookie: 'a=b; test1=111;test2=000'
     *    }
     * And having addToRequest in config set to
     * {
     *      headers: {
     *          auth-token: 'EBH-iHu*8'
     *      },
     *      cookie: {
     *          test1: 99999,
     *      }
     * }
     * Would result in
     * { 
     *      referer: 'https://www.indusface.com',
     *      'test-header': 666,
     *      'AUTH-token': 'EBH-iHu*8',
     *      Cookie: 'a=b; test1=99999; test2=000' 
     * }     
     * ==> This method modifies the object passed in  <==
     * @param {Object} originalHeaders headers that were be sent in request
     * @param {Object} addToRequest headers & cookies to add/update
     */
    static addAdditionalPerRequestHeaders(originalHeaders, addToRequest) {
        if (addToRequest) {
            originalHeaders = originalHeaders || {}
            let headers = caseless(originalHeaders)

            // add headers
            if (_.isObject(addToRequest.headers)) {
                for (let hdr of Object.keys(addToRequest.headers)) {
                    headers.set(hdr, addToRequest.headers[hdr])
                }
            }

            // add cookies
            if (addToRequest.cookie) {
                let curCookie = headers.get('Cookie')
                let parsedCookie = curCookie ? cookieParse.parse(curCookie, {
                    decode: x => x
                }) : {}
                let caselessParsedCookie = caseless(parsedCookie)
                for (let specifiedCookie of Object.keys(addToRequest.cookie)) {
                    caselessParsedCookie.set(specifiedCookie, addToRequest.cookie[specifiedCookie])
                }

                // set cookie header again
                let modifiedCookieHeader = Object.keys(parsedCookie).map(x => x + '=' + parsedCookie[x]).join('; ')
                headers.set('Cookie', modifiedCookieHeader)
            }
        }
    }

    /**
     * Check given url has same parent domain with respect to main url. 
     * @param {String} url url to be check
     * @param {String} mainUrl Main url to checked against 
     * @returns {Boolean} Returns true if both url has same parent domain otherwise returns false. 
     */
    static isSameDomain(url, mainUrl) {
        let parsedDomain = ParseDomain(url);
        let parsedMainDomain = ParseDomain(mainUrl);

        if (parsedDomain && parsedMainDomain) {
            if (parsedDomain && parsedMainDomain && `${parsedDomain.domain.toLowerCase()}.${parsedDomain.tld.toLowerCase()}` ==
                `${parsedMainDomain.domain.toLowerCase()}.${parsedMainDomain.tld.toLowerCase()}`) {
                return true;
            }
        }
    }
    /** @param {any} content the content need to be check if its json parsable or not.
     * @returns {boolean} true if content is json parsable otherwise return false
     */
    static isJsonParsable(content) {
        try {
            JSON.parse(content);
            return true;
        } catch (err) {

        }

        return false;
    }

    /**
     * Annotate request script
     * @param {any} request request with annotated script
     */
    static annotateScript(request) {
        try {
            let annotations = _.get(request, "annotations");

            if (annotations) {
                request.annotations = new Function(`return (${annotations})`)();
            }
        }
        catch (err) {
            logger.log('error', `Could not create script function : ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
        }
    }

    /**
     * Update script variables in request. i.e. url, token etc. 
     * @param {any} request which has script variables need to be replace
     * @param {any} scriptVariables script variables need to be replace in request 
     */
    static updateScriptVariables(request, scriptVariables) {
        for (let variableKey of Object.keys(scriptVariables)) {
            let val = scriptVariables[variableKey]

            if (val) {
                let replacer = new RegExp(`{{${variableKey}}}`, 'g');
                let replacerEncoded = new RegExp(`${encodeURIComponent('{{')}${variableKey}${encodeURIComponent('}}')}`, 'g');

                let annotations = _.get(request.httpRequest, "annotations");
                let annotationsClone = _.cloneDeep(annotations);
                let savedHeaders = request.httpRequest.savedHeaders;
                request.httpRequest = JSON.parse(JSON.stringify(request.httpRequest).replace(replacer, val));
                request.httpRequest = JSON.parse(JSON.stringify(request.httpRequest).replace(replacerEncoded, val));
                request.httpRequest.annotations = annotationsClone;
                request.httpRequest.savedHeaders = savedHeaders;
            }
        }
    }

    /**
 * get the standardized vulnerability as described in document @link: <need link here>
 * @param {Object} vuln Object describing attack and vulns found.
 */
    static getStandardizedAttackObject(attack) {
        let standardizedAttack = {
            // common info
            scanId: attack.httpRequest.scanId,
            scanlogId: attack.httpRequest.scanlog_id,
            vulnId: attack.vulnId,
            productionReady: attack.productionReady,
            attack: {},
            key: HaikuUtils.getIGWKey({
                attack: attack
            }),
            uriGroupingKey: HaikuUtils.generateGroupingKeyForRequest(attack.httpRequest),
            autoPOC: attack.autoPOC
        }

        // copy the attack properties over
        let attackProperties = ['name', 'hostname', 'href', 'area', 'type', 'param', 'vector', 'encoding']
        for (let prop of attackProperties) {
            standardizedAttack.attack[prop] = _.get(attack, prop);
        }

        standardizedAttack.attack.type = standardizedAttack.attack['type'] || _.get(attack, 'attackType');
        standardizedAttack.attack.area = standardizedAttack.attack['area'] || _.get(attack, 'attackArea');
        standardizedAttack.attack.name = standardizedAttack.attack['name'] || _.get(attack, 'pluginName');

        standardizedAttack.attack.httpRequest = HaikuUtils.getHttpRequest(attack);
        standardizedAttack.attack.httpResponse = HaikuUtils.getHttpResponse(attack);

        // original request
        standardizedAttack.original = {};
        standardizedAttack.original.scanner = _.get(attack, 'originalRequest.scanner')
        standardizedAttack.original.haikuResourceType = _.get(attack, 'originalRequest.httpRequest.haikuResourceType')
        standardizedAttack.original.haikuKey = _.get(attack, 'originalRequest.httpRequest.haikuKey')
        standardizedAttack.original.httpRequest = HaikuUtils.getHttpRequest(_.get(attack, 'originalRequest'));

        // if the attack was the original crawler request, original request response is the attack response 
        standardizedAttack.original.httpResponse = (attack.attackArea == 'original-crawler-request') ? attack.httpResponse : HaikuUtils.getHttpResponse(_.get(attack, 'originalRequest'));

        return standardizedAttack;
    }

    /**
     * Get standardized httpRequest 
     * @param {Object} attack Object containing attack information 
     */
    static getHttpRequest(attack) {
        let httpRequestProperties = ['method', 'headers', 'body', 'uri']
        let httpReq = {}
        for (let prop of httpRequestProperties) {
            httpReq[prop] = _.get(attack, 'httpRequest.' + prop);
        }

        // Add http request line
        try {
            let parsedUrl = new URL(httpReq['uri'])
            let httpVersion = 'HTTP/1.1'
            httpReq['requestLine'] = httpReq['method'] + ' ' + parsedUrl.pathname + parsedUrl.search + ' ' + httpVersion

            // add host header if it is not already there
            if (!httpReq['headers']['host'] && !httpReq['headers']['Host']) {
                // before setting Host header do a deep clone so that it does not alter original request
                // this fix is done for avoing host and Host in report
                httpReq = _.cloneDeep(httpReq)
                httpReq['headers']['Host'] = parsedUrl.host
            }
        } catch (err) {
            logger.log('error', `Unable to standardized httpRequest ${err.toString()}`);
        }

        return httpReq
    }

    /**
     * Get standardized httpResponse
     * @param {Object} attack Object containing attack information 
     */
    static getHttpResponse(attack) {
        // only return an objevt if we had got a response
        if (!_.get(attack, 'httpResponse')) {
            return null
        }

        let httpResponseProperties = ['statusCode', 'statusMessage', 'headers', 'body']
        let httpResp = {};

        for (let prop of httpResponseProperties) {
            httpResp[prop] = _.get(attack, 'httpResponse.' + prop);
        }

        // Add http response line
        let httpVersion = httpResp['httpVersion'] || 'HTTP/1.1'
        httpResp['responseLine'] = httpVersion + ' ' + httpResp['statusCode'] + ' ' + httpResp['statusMessage']

        return httpResp
    }

    static forAll(
        obj,
        callback = () => { },
        options = {},
        currentPath = null
    ) {
        if (!currentPath) {
            currentPath = [];
        }

        if (obj) {
            Object.keys(obj).forEach(compute);
        }

        return obj;

        function compute(key) {
            const value = obj[key];
            if (typeof value !== 'object') {
                callback(currentPath, key, obj);
            } else if (_.isArray(value)) {
                const path = currentPath.slice(0);
                //Only iterate first element of array to avoid large number of array item processing.
                key = `${key}[0]`;
                path.push(key);
                HaikuUtils.forAll(_.get(obj, key), callback, options, path);
            } else {
                const path = currentPath.slice(0);
                path.push(key);
                HaikuUtils.forAll(value, callback, options, path);
            }
        }
    }

    /**
     * Return true if value is of basic type. i.e. string, number etc. Not return object, function, null or undefined types 
     * @param {Any} value value for which basic type need to be check.
     */
    static isBasicType(value) {
        return !_.isObject(value) && !HaikuUtils.isNullOrUndefined(value)
            && !_.isFunction(value);
    }

    /**
     * Return array of property names to iterate
     * @param {Object} dataObject object for which basic property names to be return having basic value types.
     */
    static getPropetiesToIterate(dataObject) {
        let pathPrefixes = [];

        HaikuUtils.forAll(dataObject, function (path, key, obj, pathPrefix) {
            if (path.length == 0) {
                let value = _.get(dataObject, key);

                if (HaikuUtils.isBasicType(value)) {
                    pathPrefixes.push(key);
                }
            }
            else {
                let prefix = _.join(path, '.') + '.' + key;
                let value = _.get(dataObject, prefix);

                if (HaikuUtils.isBasicType(value)) {
                    pathPrefixes.push(prefix);
                }
            }
        });

        return pathPrefixes;
    }
    /**
     * Check given url has extension 'ext'. 
     * @param {String} url url to be check
     * @param {String} ext extension to checked against 
     * @returns {Boolean} Returns true if url has valid given extension.
     */
    static hasExtension(url, ext) {
        return url.split(/[#?]/)[0].split('.').pop().trim() === ext
    }

    /**
     * Info message for malware checks
     */
    static getMalwareMsg(vulnType, vulnConfig) {
        let msg = "";
        switch (vulnType) {
            case 'ID-external-resource':
                if (vulnConfig.vulnid === 2) {
                    msg = `A new external URL: '${vulnConfig.url}' has been found on '${vulnConfig.location}'`
                }
                else {
                    msg = `We have found an external javascript on this URL: '${vulnConfig.url}' on '${vulnConfig.location}'`
                }

                break;
            case 'ID-suspicious-iframe':
                msg = `We have found a suspicious iFrame(s) at : ${vulnConfig.location}'`
                break;

            default:
                break;
        }

        return msg;
    }


    /**
    * Check given url has extension 'ext'. 
    * @param {String} url url to be check
    * @param {String} ext extension to checked against 
    * @returns {Boolean} Returns true if url har valid given extension.
    */
    static hasExtension(url, ext) {
        return url.split(/[#?]/)[0].split('.').pop().trim() === ext
    }

    /**
     * GET MAX depth from dom
     */
    static getDOMMaxDepth(domString) {
        if (HaikuUtils.isNullOrUndefined(domString)) {
            return 0;
        }

        let maxDepth = 0;

        function element_list(el, depth) {
            if (depth > maxDepth) {
                maxDepth = depth;
            }

            for (let i = 0; i < el.children.length; i++) {
                element_list(el.children[i], depth + 1);
            }
        }
        let $ = cheerio.load(domString)
        element_list(domString, 0);

        return maxDepth;
    }

    /**
     * Add auto poc details
     * @param {Object} attack attack obbject for which autoPOC details need to be added
     * @param {String} vulnID vulneravility id 
     * @param {String} type type of autoPOC request/response i.e. original, attack
     * @param {String} path json path which need to highlight. i.e. httpRequest.headers
     * @param {String} highlightType highlight types. i.e. text, param, missing etc 
     * @param {Array} details values which need to be highlighted. 
     * @param {String} description description text for autoPOC.
     * @returns 
     */
    static addToAutoPOCAnnotation(attack, vulnID, type, path, highlightType, details = null, description = null) {
        let autoPOC = attack.uploadAttackToS3 ? attack.autoPOC : _.get(attack, `result.vulns.${vulnID}.autoPOC`, null);

        if (!attack.uploadAttackToS3 && !autoPOC) {
            return;
        }
        else if (attack.uploadAttackToS3 && !autoPOC) {
            attack.autoPOC = [];
            autoPOC = attack.autoPOC;
        }

        if (details && !_.isArray(details)) {
            details = [details]
        }

        if (!description) {
            let existingDetails = _.get(attack, `result.vulns.${vulnID}.details`, null);

            if (existingDetails) {
                try {
                    let result = this.getIGWResult(attack.result, vulnID);

                    if (result) {
                        description = result;
                    }
                } catch (error) {
                    logger.log('error', `Unable to get result for autoPOC for the vulnerability id ${vulnID}. Reason: ${error}`)
                }
            }
        }

        autoPOC.push({
            type,
            path,
            highlightType,
            details,
            description
        });
    }

    static bufferToStream(buffer) {
        let stream = new Duplex();
        stream.push(buffer);
        stream.push(null);
        return stream;
    }

    static removeHashFromURL(url) {
        let updatedURL = url.replace(/\/#$/, '');
        updatedURL = updatedURL.replace(/#$/, '');
        return updatedURL;
    }

    static getUrlRegex(url) {
        // Replace GUID-like segments with a placeholder
        const placeholderRegexUrl = url.replace(
            /[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/g,
            'GUID_PLACEHOLDER'
        );

        // Escape special characters in the input URL
        const escapedUrl = placeholderRegexUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // Replace numeric segments with \d+
        const numericRegexUrl = escapedUrl.replace(/\d+/g, '\\d+');

        // Convert the placeholder back to the actual regex pattern
        const patternRegexUrl = numericRegexUrl.replace(/GUID_PLACEHOLDER/g, '[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}');

        // Add anchors to match the beginning and end of the string
        const anchoredRegexUrl = `^${patternRegexUrl}$`;

        // Add the case-insensitive flag to the regular expression
        return new RegExp(anchoredRegexUrl, 'i');
    }

    static getSSOProviderFromRedirectURL(url) {
        if (url.includes('https://login.microsoftonline.com')) {
            return SSO_PROVIDERS.MICROSOFT;
        } else if (url.includes('facebook.com')) {
            return null;
        } else if (url.includes('google.com')) {
            return null;
        } else if (url.includes('twitter.com')) {
            return null;
        } else if (url.includes('github.com')) {
            return null;
        } else {
            return null;
        }
    }

    static getHostMapEntries(proxyPassDetail) {
        let hostRules = {};

        for (const domain in proxyPassDetail) {
            const entry = proxyPassDetail[domain];

            if (entry.ip) {

                const mapEntry = `MAP ${domain} ${entry.ip}`;

                hostRules[domain] = mapEntry;
            }
        }

        let hostEntries = Object.values(hostRules);

        return hostEntries.length > 0 ? hostEntries.join(", ") : '';
    }

    /**
     * Delay the exeuction
     * @param {number} milliseconds The `milliseconds` argument specifies the number of milliseconds to delay the execution.
     * @returns 
     */
    static async delay(milliseconds) {
        return new Promise(resolve => {
            setTimeout(resolve, milliseconds);
        });
    }

    /**
     * Retrieves the current private IP address of the machine.
     *
     * @returns {string} The private IP address.
     */
    static getPrivateIP() {
        try {
            // Get a list of network interfaces on the machine
            const networkInterfaces = os.networkInterfaces();

            // Iterate through each network interface
            for (const interfaceName in networkInterfaces) {
                const interfaces = networkInterfaces[interfaceName];

                // Iterate through each IP configuration on the interface
                for (const iface of interfaces) {
                    // Check if the IP configuration is an IPv4 address and not internal
                    if (iface.family === 'IPv4' && !iface.internal) {
                        // Return the first non-internal IPv4 address found
                        return iface.address;
                    }
                }
            }
        } catch (error) {
            logger.log('error', `Unable to get private IP of machine. Reason: ${error.toString()}`);
        }
    }

    static getRevalidationAndAttackKey(vuln) {
        try {
            let haikuKey = _.get(vuln, 'attack.originalRequest.httpRequest.haikuKey')

            return {
                attackKey: `v=${vuln.attack.vector}||e=${vuln.attack.encoding}||t=${vuln.attack.type}`,
                revalidationKey: haikuKey + '||n=' + vuln.attack.name + '||a=' + vuln.attack.area + '||p=' + vuln.attack.param
            }
        } catch (error) {
            logger.log('info', `getRevalidationAndAttackKey() - no haiku key so ignoring vuln=${JSON.stringify(vuln)}, reason: ${error.toString()}`, HaikuUtils.getMetadataForLog(vuln))
        }
    }

    /**
     * Get the vulnerability object along with respective plugin name by vulnerability id
     * @param {String} vulnerabilityId vulnerability id
     * @returns {Object} vulnerabilityId object
     */
    static getVulnerabilityById(vulnerabilityId) {
        try {
            let Plugins = networkScannerConfig.Plugins;

            // Search for the vulnerability
            for (let plugin in Plugins) {
                if (Plugins[plugin].vulnerabilities) {
                    for (let vulnName of Object.keys(Plugins[plugin].vulnerabilities)) {
                        if (Plugins[plugin].vulnerabilities[vulnName].igwId == vulnerabilityId) {
                            //also return plugin name
                            let vulnObject = _.cloneDeep(Plugins[plugin].vulnerabilities[vulnName]);
                            vulnObject.pluginName = plugin;
                            return vulnObject;
                        }
                    }
                }
            }
        } catch (error) {
            logger.log('error', `Unable to get vulnerability by id ${vulnerabilityId}. Reason: ${error.toString()}`);
        }


        return null;
    }

    /**
     * Get the vulnerability object along with respective plugin name by vulnerability name
     * @param {String} vulnerabilityName vulnerability name
     * @returns {Object} vulnerability object
     */
    static getVulnerabilityByVulnerabilityName(vulnerabilityName) {
        try {
            let Plugins = networkScannerConfig.Plugins;

            // Search for the vulnerability
            for (let plugin in Plugins) {
                if (Plugins[plugin].vulnerabilities) {
                    for (let vuln of Object.keys(Plugins[plugin].vulnerabilities)) {
                        if (vuln == vulnerabilityName) {
                            //also return plugin name
                            let vulnObject = _.cloneDeep(Plugins[plugin].vulnerabilities[vuln]);
                            vulnObject.pluginName = plugin;
                            return vulnObject;
                        }
                    }
                }
            }
        } catch (error) {
            logger.log('error', `Unable to get vulnerability by name ${vulnerabilityName}. Reason: ${error.toString()}`);
        }
    }

    /*
    * Get alertmd5 for a vulnerability
    * @param {String} serviceId service id
    * @param {String} vulnerabilityId vulnerability id
    * @param {String} key key
    * @param {String} port port
    * @param {String} protocol protocol
    */
    static getAlertMD5(serviceId, vulnerabilityId, key, port = '', protocol = '') {
        return crypto.createHash('md5').update(`${serviceId}${vulnerabilityId}${key}${port}${protocol}`).digest('hex');
    }

    /**
    * Analyze urls using Masswappalyzer
    * @param {String} wappalyzerSourcePath wappalyzer source path
    * @param {Array} urlInfos url information
    */
    static async analyzeUrls(wappalyzerSourcePath, urlInfos) {
        //command example to run the wappalyzer command to analyze the urls
        //node wappalyzerSourcePath/src/drivers/npm/cli.js http://dvwascantest.testapptrana.net/dvwa/dvwa
        if (!_.isArray(urlInfos)) {
            return;
        }

        try {
            for (let i = 0; i < urlInfos.length; i++) {
                let urlInfo = urlInfos[i];
                let serviceId = urlInfo.serviceId;
                let url = urlInfo.url;

                if (!url || !serviceId) {
                    continue;
                }

                try {
                    let output = execSync(`node ${wappalyzerSourcePath}/src/drivers/npm/cli.js ${url}`, {
                        maxBuffer: 1024 * 5000
                    });

                    if (output && output.toString().length > 0) {
                        urlInfo.result = JSON.parse(output);
                    }
                } catch (error) {
                    logger.log('error', `analyzeUrls exec error for url: ${url}, error: ${error.toString()}`);
                }
            }
        }
        catch (err) {
            logger.log('error', `analyzeUrls error: ${err.toString()}`);
            return;
        }

        return urlInfos;
    }

    /**
     * Generates network statistics for a given host.
     * @param {string} host - The host to generate network statistics for.
     * @param {Object} logger - The logger to use for logging.
     * @returns {Promise<Object>} - A promise that resolves to an object containing network statistics.
     */
    static async generateNetworkStats(host, logger = console) {
        const stats = {};

        try {
            // Ping
            const pingRes = await ping.promise.probe(host);
            stats.ping = pingRes;

            // nodejs-traceroute for the host
            const traceroute = new Traceroute();
            let hops = [];

            await new Promise((resolve) => {
                try {
                    traceroute
                        .on('hop', (hop) => {
                            hops.push(hop);
                        })
                        .on('error', (error) => {
                            logger.log('error', `Unable to traceroute to host ${host}. Reason: ${error.toString()}`);
                            stats.traceroute = { error: error.toString() };
                            resolve();
                        })
                        .on('close', () => {
                            logger.log('info', `Traceroute to host ${host} completed.`);
                            stats.traceroute = { hops };
                            resolve();
                        })
                        .trace(host);
                }
                catch (error) {
                    logger.log('error', `Unable to traceroute to host ${host}. Reason: ${error.toString()}`);
                    stats.traceroute = { error: error.toString() };
                    resolve();
                }
            });

            // DNS Lookup
            await new Promise((resolve, reject) => {
                dns.lookup(host, (err, address, family) => {
                    if (err) {
                        logger.log('error', `Unable to perform DNS lookup for host ${host}. Reason: ${err.toString()}`);
                        stats.dnsLookup = { error: err.toString() };
                    }
                    else {
                        stats.dnsLookup = { address, family };
                    }
                    logger.log('info', `DNS lookup for host ${host} completed.`);
                    resolve();
                });
            });
        } catch (error) {
            logger.log('error', `Unable to get private IP of machine. Reason: ${error.toString()}`);
        }

        return stats;
    }

    static async runPostmanRequest(scanId, postmanCollection, apiName, timeoutRequestInSeconds = 10, logger = console) {
        let skipRequest = false;
        timeoutRequestInSeconds = timeoutRequestInSeconds * 1000;

        let apiEndpoints = {};

        let haikuRequestFormat = {
            name: '',
            description: '',
            scriptVariables: {
            },
            haikuRequests: []
        }

        return new Promise((resolve, reject) => {
            try {
                newman.run({
                    collection: postmanCollection,
                    reporters: 'cli',
                    timeoutRequest: timeoutRequestInSeconds
                })
                    .on('exception', function (err, args) {
                        logger.log('error', `runPostmanCollection scanId: ${scanId} exception: ${err.toString()}`);
                    })
                    .on('beforeScript', function (err, args) {
                        let postScript = args.item.getEvents();

                        if (_.isArray(postScript) && !_.isEmpty(postScript) && postScript[0].listen == 'test' && postScript[0].script && postScript[0].script.exec) {
                            apiEndpoints[args.item.id].annotations = HaikuUtils.parseHaikuScript(postScript[0].script.exec);
                        }

                        if (skipRequest) {
                            args.script.exec.unshift('pm.execution.skipRequest();');
                            args.exec = false;
                        }
                    })
                    .on('script', function (err, args) {
                        if (args.script.exec[0] == 'pm.execution.skipRequest();') {
                            args.script.exec.shift();
                        }
                    })
                    .on('beforeItem', function (err, args) {
                        let path = _.join(args.item.getPath().slice(1), '|'); //['CleverTap API Test', 'Report API Endpoints', 'Trends API', 'Partial API']

                        let headers = {};

                        _.forEach(args.item.request.headers.members, (header) => {
                            headers[header.key] = header.value;
                        });

                        apiEndpoints[args.item.id] = {
                            method: args.item.request.method,
                            name: path,
                            description: args.item.request.description,
                            baseUri: args.item.request.url.toString(),
                            headers: headers,
                            body: args.item.request.body ? args.item.request.body : null,
                            uri: '',
                            annotations: '',
                            statusCode: 0,
                        };

                        if (!_.isEmpty(apiName) && _.join(args.item.getPath().slice(1), '|') != apiName) {
                            skipRequest = true;
                        }
                        else {
                            skipRequest = false;
                        }
                    })
                    .on('beforeRequest', function (err, args) {
                        if (err) {
                            logger.log('error', `processApiScanFileViaNewman metaInfo: ${scanId} beforeRequest error: ${err.toString()}`);
                        }
                        else {
                            let id = args.item.id;

                            if (apiEndpoints[id]) {
                                apiEndpoints[id].uri = args.request.url.toString();

                                _.forEach(args.request.headers.members, (header) => {
                                    apiEndpoints[id].headers[header.key] = header.value;
                                });

                                apiEndpoints[id].body = args.request.body ? args.request.body : null;
                            }
                        }
                    })
                    .on('request', function (err, args) {
                        if (err) {
                            if (args) {
                                let id = args.item.id;

                                if (apiEndpoints[id]) {
                                    apiEndpoints[id].requestError = err.message;
                                }
                            }

                            logger.log('error', `processApiScanFileViaNewman scanId: ${scanId} request error: ${err.toString()}`);
                        }
                        else {
                            let id = args.item.id;

                            if (apiEndpoints[id]) {
                                apiEndpoints[id].statusCode = args.response.code;
                            }
                        }
                    })
                    .on('done', function (err, summary) {
                        if (err) {
                            logger.log('error', `processApiScanFileViaNewman for scanId: ${scanId} error: ${err.toString()}`);
                            reject(err);
                        }
                        else {
                            logger.log('info', `processApiScanFileViaNewman for scanId: ${scanId} completed`);

                            haikuRequestFormat.name = postmanCollection.info.name;
                            haikuRequestFormat.description = postmanCollection.info.description;
                            let scriptVariables = {};

                            //Copy environment variables to postman variables, like token, session values etc.
                            summary.environment.values.members.forEach((variable) => {
                                let isFound = false;

                                if (!postmanCollection.variable) {
                                    postmanCollection.variable = [];
                                }

                                Object.values(postmanCollection.variable).forEach((postmanVariable) => {
                                    if (postmanVariable.key == variable.key) {
                                        postmanVariable.value = variable.value;
                                        isFound = true;
                                    }
                                });

                                if (!isFound) {
                                    postmanCollection.variable.push({
                                        key: variable.key,
                                        value: variable.value,
                                        type: variable.type
                                    });
                                }
                            });

                            _.forEach(postmanCollection.variable, (variable) => {
                                scriptVariables[variable.key] = variable.value;
                            });

                            for (let key in apiEndpoints) {
                                let apiEndpoint = apiEndpoints[key];

                                if (scriptVariables) {
                                    haikuRequestFormat.scriptVariables = scriptVariables;
                                }

                                haikuRequestFormat.haikuRequests.push({
                                    name: apiEndpoint.name,
                                    description: apiEndpoint.description,
                                    method: apiEndpoint.method,
                                    uri: apiEndpoint.uri,
                                    headers: apiEndpoint.headers,
                                    body: apiEndpoint.body,
                                    statusCode: apiEndpoint.statusCode,
                                    annotations: apiEndpoint.annotations,
                                    baseUri: apiEndpoint.baseUri,
                                });
                            }

                            resolve(haikuRequestFormat);
                        }
                    });
            }
            catch (error) {
                logger.log('error', `processApiScanFileViaNewman for scanId: ${scanId} error: ${error.toString()}`);
                reject(err);
            }
        });
    }

    /**
     * Parse haiku script from postman script
     * @param {String} script 
     * @returns {String} haiku script
     */
    static parseHaikuScript(script) {
        if (!script) {
            return;
        }

        if (_.isArray(script) && _.find(script, (item) => item.includes('/* HAIKU_START */'))) {
            return _.chain(script)
                .map((item) => item.replace(new RegExp('\r', 'g'), ''))
                .dropWhile(item => item != '/* HAIKU_START */')
                .dropRightWhile(item => item != '/* HAIKU_END */')
                .pull("/* HAIKU_START */", "/* HAIKU_END */")
                .join("")
                .value();
        }

        return;
    }

    /**
     * Check if the request is an API request
     * @param {Object} details request details
     * @returns {Boolean} true if the request is an API request, false otherwise
     */
    static isApiRequest(details) {
        try {
            let request = details.httpRequest ? details.httpRequest : details;

            if (request && request.headers) {
                let headers = request.headers;

                let contenType = headers['Content-Type'];

                if (!contenType) {
                    contenType = headers['content-type'];
                }

                if (contenType && ((contenType.includes('application') && contenType.includes('json')) || contenType.includes('x-www-form-urlencoded'))) {
                    return true;
                }
            }

            if (request && request.responseHeaders) {
                let responseHeaders = request.responseHeaders;

                let contenType = responseHeaders['Content-Type'];

                if (!contenType) {
                    contenType = responseHeaders['content-type'];
                }

                if (contenType && ((contenType.includes('application') && contenType.includes('json')) || contenType.includes('x-www-form-urlencoded'))) {
                    return true;
                }
            }
        }
        catch (error) {
            logger.log('error', `isApiRequest error: ${error.toString()}`);
        }

        return false;
    }

    /**
     * Validate whether the given file content is a valid postman file
     * @param {String} fileContent file content
     * @returns {Boolean} true if the file content is a valid postman file, false otherwise
     */
    static isValidPostmanFile(fileContent) {
        try {
            let postmanFile = new PostmanCollection.Collection(JSON.parse(fileContent));

            if (postmanFile.items.members.length > 0) {
                return true;
            }
        }
        catch (error) {

        }

        return false;
    }

    /**
     * Get http error status description by status code
     * @param {Number} statusCode status code
     * @returns {String} http status description
     */
    static getErrorHTTPStatusDescription(statusCode) {
        if (statusCode >= 400 && statusCode < 500) {
            switch (statusCode) {
                case 400:
                    return "Bad Request: The server could not understand the request due to invalid syntax.";
                case 401:
                    return "Unauthorized: The request requires user authentication.";
                case 402:
                    return "Payment Required: This code is reserved for future use.";
                case 403:
                    return "Forbidden: The server understood the request, but it refuses to authorize it.";
                case 404:
                    return "Not Found: The server can't find the requested resource.";
                case 405:
                    return "Method Not Allowed: The method specified in the request is not allowed for the resource identified by the request URI.";
                case 406:
                    return "Not Acceptable: The server cannot produce a response matching the list of acceptable values defined in the request's headers.";
                case 407:
                    return "Proxy Authentication Required: This status code is similar to 401 (Unauthorized), but indicates that the client must first authenticate itself with the proxy.";
                case 408:
                    return "Request Timeout: The server would like to shut down this unused connection.";
                case 409:
                    return "Conflict: This response is sent when a request conflicts with the current state of the server.";
                case 410:
                    return "Gone: This response is sent when the requested content has been permanently deleted from server.";
                case 411:
                    return "Length Required: The server refuses to accept the request without a defined Content-Length.";
                case 412:
                    return "Precondition Failed: The client has indicated preconditions in its headers which the server does not meet.";
                case 413:
                    return "Payload Too Large: Request entity is larger than limits defined by server.";
                case 414:
                    return "URI Too Long: The URI requested by the client is longer than the server is willing to interpret.";
                case 415:
                    return "Unsupported Media Type: The media format of the requested data is not supported by the server.";
                case 416:
                    return "Range Not Satisfiable: The range specified in the Range header field in the request can't be fulfilled.";
                case 417:
                    return "Expectation Failed: This response code means the expectation indicated by the Expect request header field can't be met by the server.";
                case 418:
                    return "I'm a teapot: This code was defined in 1998 as one of the traditional IETF April Fools' jokes.";
                case 421:
                    return "Misdirected Request: The request was directed at a server that is not able to produce a response.";
                case 422:
                    return "Unprocessable Entity: The request was well-formed but was unable to be followed due to semantic errors.";
                case 423:
                    return "Locked: The resource that is being accessed is locked.";
                case 424:
                    return "Failed Dependency: The request failed due to failure of a previous request.";
                case 425:
                    return "Too Early: Indicates that the server is unwilling to risk processing a request that might be replayed.";
                case 426:
                    return "Upgrade Required: The server refuses to perform the request using the current protocol but might be willing to do so after the client upgrades to a different protocol.";
                case 428:
                    return "Precondition Required: The origin server requires the request to be conditional.";
                case 429:
                    return "Too Many Requests: The user has sent too many requests in a given amount of time.";
                case 431:
                    return "Request Header Fields Too Large: The server is unwilling to process the request because its header fields are too large.";
                case 451:
                    return "Unavailable For Legal Reasons: The user requests an illegal resource, such as a web page censored by a government.";
                default:
                    return "Client Error: The request was invalid or could not be processed.";
            }
        } else if (statusCode >= 500) {
            switch (statusCode) {
                case 500:
                    return "Internal Server Error: The server has encountered a situation it doesn't know how to handle.";
                case 501:
                    return "Not Implemented: The request method is not supported by the server and cannot be handled.";
                case 502:
                    return "Bad Gateway: This error response means that the server, while working as a gateway to get a response needed to handle the request, got an invalid response.";
                case 503:
                    return "Service Unavailable: The server is not ready to handle the request.";
                case 504:
                    return "Gateway Timeout: This error response is given when the server is acting as a gateway and cannot get a response in time.";
                case 505:
                    return "HTTP Version Not Supported: The HTTP version used in the request is not supported by the server.";
                case 506:
                    return "Variant Also Negotiates: The server has an internal configuration error.";
                case 507:
                    return "Insufficient Storage: The server is unable to store the representation needed to complete the request.";
                case 508:
                    return "Loop Detected: The server detected an infinite loop while processing a request with 'Depth: infinity'. This status indicates that the entire operation failed.";
                case 510:
                    return "Not Extended: Further extensions to the request are required for the server to fulfill it.";
                case 511:
                    return "Network Authentication Required: The client needs to authenticate to get network access.";
                default:
                    return "Server Error: The server encountered an unexpected error while processing the request.";
            }
        } else {
            return "Unknown Status Code Error: The server encountered an unexpected error while processing the request.";
        }
    }

    /**
     * Determines the appropriate Content-Type based on file extension
     * @param {string} filePath - The file path to analyze
     * @returns {string} The appropriate Content-Type for the file extension
     */
    static getContentTypeFromFilePath(filePath) {
        // Extract extension from file path
        const ext = filePath.split('.').pop().toLowerCase();

        // Common MIME type mappings
        const mimeTypes = {
            'txt': 'text/plain',
            'html': 'text/html',
            'css': 'text/css',
            'js': 'application/javascript',
            'json': 'application/json',
            'xml': 'application/xml',
            'csv': 'text/csv',
            'pdf': 'application/pdf',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'svg': 'image/svg+xml',
            'zip': 'application/zip',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        };

        return mimeTypes[ext] || 'application/octet-stream';
    }

    /**
     * Check if a given hostname is an IPv4 address.
     * @param {string} hostname - The hostname to check.
     * @returns {boolean} - True if the hostname is an IP address, false otherwise.
     */
    static isIpAddress(hostname) {
        try {
            const ipRegex = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
            return ipRegex.test(hostname);
        } catch (error) {
            logger.log('error', `Error checking if hostname is IP address: ${error.toString()}`);
            return false;
        }
    }
}


module.exports = HaikuUtils