const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class HTMLInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-host-header-inj-oob'
    }

    getAttackVectors(baseAttack) {
        return HTMLInjAtVector
    }

    getAttackableEvents() {
        return ['http-headers']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Origin', 'Host', 'X-Forwarded-Host', 'X-Host', 'X-Forwarded-Server', 'X-HTTP-Host-Override', 'Forwarded']

        })
    }
}
const HTMLInjAtVector = [
    '{{scannerVector}}.haikuscan.indusfacefinder.in',
]
module.exports = HTMLInjOOB