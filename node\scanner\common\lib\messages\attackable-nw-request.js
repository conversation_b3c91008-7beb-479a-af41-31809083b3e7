const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:AttackableNetworkRequest')


/** 
 * Message from crawler -> scanner specifying a new netwok request to attack.
 * @extends QueueMessage
 */
class AttackableNetworkRequest extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }

    /**
     * @typedef {Object} attackableNetworkRequestMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request
     * @property {httpRequest} httpRequest Full HTTP request (method, headers, body).
     */
    /**
     * @param {attackableNetworkRequestMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner'
        this.routingKey = 'request.new-attackable'
        this.msgType = AttackableNetworkRequest.msgType
    }

    deserialize(content, contentType) {
        let ret = super.deserialize(content, contentType)

        // clean up the http request
        if (ret && ret.httpRequest) {
            // Always delete 'key' parameter - totally messes up HTTPS sites
            // since 'key' means client side SSL key for https Agent !!
            // Rename the key
            ret.httpRequest.crawlerKey = ret.httpRequest.appTranaKey || ret.httpRequest.key 
            delete ret.httpRequest.key

            // Network scanner and Request object use 'uri' and crawler uses 'url'. Remove url to avoid confusion
            ret.httpRequest.uri = ret.httpRequest.url
            delete ret.httpRequest.url

            // Make method uppercase for consistency
            if (ret.httpRequest.method) {
                ret.httpRequest.method = ret.httpRequest.method.toUpperCase()
            }
        }

        return ret
    }

}

module.exports = AttackableNetworkRequest