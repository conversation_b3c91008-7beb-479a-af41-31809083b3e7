const debug = require('debug')('IISVersionChecker')
const NetworkAttack = require('./network-attack')
const URL = require('url').URL
const _ = require('lodash')


class IISVersionChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.iisVersionDisclosureVulnID = 'ID-Microsoft-Server-Version-Disclosure'

        const microsoftIISServerVersionDisclosureHeader = ['server']

        this.headersToIterate = [{
            headers: microsoftIISServerVersionDisclosureHeader,
            vulnId: 'ID-Microsoft-Server-Version-Disclosure'
        }]
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        // cheching for request specific plugin data in specific functions
        this.checkIISVersionDisclosureVulnerability(attack) // iis version discousre is specialzied case of info disclousre
    }


    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     *
     */
    checkIISVersionDisclosureVulnerability(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {})

        for (let headerInfo of this.headersToIterate) {
            if (pluginDataForRequest[headerInfo.vulnId]) {
                continue
            }
            let vuln = [];
            for (let headerName of headerInfo.headers) {
                let val = headers[headerName];

                if (/iis/i.test(val)) {
                    let vuln = {
                        details: {
                            header: headerName,
                            value: val
                        }
                    }
                    this.addVulnerabilitytoResult(attack, headerInfo.vulnId, vuln)
                    pluginDataForRequest[headerInfo.vulnId] = true
                }
            }
        }
    }

}
module.exports = IISVersionChecker