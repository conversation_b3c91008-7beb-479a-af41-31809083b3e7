const NetworkAttack = require('./network-attack')
const _ = require('lodash')

/**
 * Slow Response Time plugin strategy:
 * Here for every request made, the response time from server is measured and if greater than a specific 
 * value then it's tagged as a vulnerability
 */
class SlowResponseTime extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-slow-response-time'
    }

    /**
     * 
     * @param {attack} attack new requst received from crawler
     */
    processAttackResponse(attack) {

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginDataForRequest.SlowResponseTime) {
            return
        }

        let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        let CType = /(application\/json|text\/(?:plain|xml))/i
        if (CType.test(RescontentType)) {
            return
        }

        // fetch all timing objects and status code for request made
        let allTimings = _.get(attack, 'result.resp.fullResponse.timings')
        let allTimingPhases = _.get(attack, 'result.resp.fullResponse.timingPhases')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')

        //Dont report vuln, if redirect is present - 3** series.
        let redirect = _.get(attack, 'result.resp.httpResponse.redirects')
        if (redirect.length > 0) { return }

        //adding for undefined
        if (allTimings === undefined) return

        // check if response time and status code are falling under a value, if not then report vulnerability
        if (allTimings.response > this.getMetadata(attack).vulnerabilities['ID-slow-response-time'].maxSlowResponseTime
            && statusCode == 200) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, { allTimings, allTimingPhases })
            pluginDataForRequest.SlowResponseTime = true
        }
    }
}

module.exports = SlowResponseTime