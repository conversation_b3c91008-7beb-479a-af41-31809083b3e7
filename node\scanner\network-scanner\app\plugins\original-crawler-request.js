const debug = require('debug')('OriginalCrawlerRequest')
const NetworkAttack = require('./network-attack')
const logger = require('../../../common/lib/haiku-logger')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')
const HaikuScanScriptHelper = require('../../../common/lib/haiku-scan-script-helper')


/** 
 * Performs the original crawler request so that response to original request can 
 * be checked for vulns (typically passive vulnerabities)
 * This plugin does not listen to events, the method is directly called by the network scanner
 * @todo See if this should change
 */
class OriginalCrawlerRequest extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner the netork scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
        networkScanner.on('new-crawler-request', this.performOriginalRequest.bind(this))
    }

    /**
     * Set up the allowedContentTypes as a superset of the other plugins' allowedContentType. This is done
     * once per scan to account for site specific config.
     */
    setupMetadataForScan(request) {
        // collect the super set of the allowed content types and all allowed resource types
        let config = this.getConfig(request)
        let allAllowedContentTypes = [config.HTTPRequestMonster.allowedContentTypes.source]
        let allAllowedResourceTypes = new Set(config.DefaultPluginSettings.resourcesToAttack)
        for (let pluginName of Object.keys(config.Plugins)) {
            let plugin = config.Plugins[pluginName]
            if (plugin.load) {
                if (plugin.allowedContentTypes) {
                    allAllowedContentTypes.push(plugin.allowedContentTypes.source)
                }

                if (plugin.resourcesToAttack) {
                    plugin.resourcesToAttack.forEach(r => allAllowedResourceTypes.add(r))
                }
            }
        }

        let metadata = this.getMetadata(request)
        // of the form /(?:aaa)|(?:bbb)|(?:ccc)/i
        metadata.allowedContentTypes = new RE2('(?:' + allAllowedContentTypes.join(')|(?:') + ')', 'i')
        // resources
        metadata.resourcesToAttack = [...allAllowedResourceTypes]

        metadata.setupCompleted = true
    }

    /**
     * replay the original request and return the response. Sicne ths is a child of 
     * the NetworkAttack class, it will send the event to check response for vulnerabilities
     * 
     * @param {request} request The request object as captured by the crawler
     */
    async performOriginalRequest(request) {
        // do one time setup for this scan if needed
        if (!this.getMetadata(request).setupCompleted) {
            this.setupMetadataForScan(request)
        }

        let _self = this
        request.inProgessRequest = new Promise(async (resolve, reject) => {
            // 'attack' metadata
            let crawlerCapturedRequest = {
                originalRequest: request,
                httpRequest: request.httpRequest,
                attackArea: 'original-crawler-request'
            }

            // send the request. base class will emit the event that plugins can use to check response
            // for vulnerbilities
            let origReqStatus
            try {
                origReqStatus = await _self.performNetworkAttack(crawlerCapturedRequest)
            } catch (err) {
                logger.log('error', `exception: ${err}`, HaikuUtils.getMetadataForLog(request))
            }
            resolve({
                origReqStatus,
                res: crawlerCapturedRequest.result
            })
        })
    }

    /**
     * processAttackResponse override
     * @param {attack} attack the attack that was performed
     */
    processAttackResponse(attack) {
    }
}

module.exports = OriginalCrawlerRequest