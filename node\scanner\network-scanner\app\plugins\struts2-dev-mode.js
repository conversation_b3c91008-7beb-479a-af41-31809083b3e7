const debug = require('debug')('Struts2DevMode')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class Struts2DevMode extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-struts2-dev-mode'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (attack.attackArea == "original-crawler-request") {
            return true
        }
        return false
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        let mybody = attack.result.resp.body
        // creating array as res and storing all instance of match. Checked with multiple IPs.
        if (/<title>struts\sproblem\sreport<\/title>/i.test(mybody) && /struts\shas\sdetected\san\sunhandled\sexception:/i.test(mybody) && /you\sare\sseeing\sthis\spage\sbecause\sdevelopment\smode\sis\senabled/i.test(mybody)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['struts', 'development mode is enabled']);
    }
}

module.exports = Struts2DevMode