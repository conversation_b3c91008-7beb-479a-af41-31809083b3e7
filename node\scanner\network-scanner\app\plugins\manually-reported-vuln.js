const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const HaikuScanScriptHelper = require('../../../common/lib/haiku-scan-script-helper')

class ManualAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.IDORVulnerabilityID = 'ID-idor-attack';
        this.IBrokenTokenVulnerabilityID = 'ID-broken-token';
        this.IDAuthenticationByPassVulnID = 'ID-authentication-bypass'
    }

    /**
     * get array of SQL Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return manualVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'json-body']
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            if (attack.attackArea == "original-crawler-request") {
                let onOriginalResponse = _.get(attack, "httpRequest.annotations.haikuEvents.onOriginalResponse", null);

                if (onOriginalResponse) {
                    try {
                        onOriginalResponse(HaikuScanScriptHelper.getArgs(attack, this), this.getHaikuScanScriptHelper(attack.httpRequest.scanId));
                    }
                    catch (err) {
                        logger.log('error', `Could not evaluate annonated onOrignalResponse script: ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
                    }
                }
                return;
            }
        }

        let globalOnResponse = _.get(this.networkScanner.getScanInfo(attack.httpRequest.scanId), "annotatedRequests.annotations.haikuEvents.onResponse", null);

        if(globalOnResponse) {
            try {
                globalOnResponse(HaikuScanScriptHelper.getArgs(attack, this), this.getHaikuScanScriptHelper(attack.httpRequest.scanId));
            }
            catch (err) {
                logger.log('error', `Could not evaluate annonated global onResponse function, reason: ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
            }
        }

        let onResponse = _.get(attack, "httpRequest.annotations.haikuEvents.onResponse", null);

        if (onResponse) {
            try {
                onResponse(HaikuScanScriptHelper.getArgs(attack, this), this.getHaikuScanScriptHelper(attack.httpRequest.scanId));
            }
            catch (err) {
                logger.log('error', `Could not evaluate annonated onResponse function, reason: ${err.toString()}`, HaikuUtils.getMetadataForLog(attack))
            }
        }
    }
}

// vectors
const manualVectors = [
    `OTM=A`
]

module.exports = ManualAttack