const fs = require('fs')
const AWS = require('aws-sdk');
const mkdirp = require('mkdirp')

const cloudfront = new AWS.CloudFront

// easier to use AWS cli to get JSO<PERSON>, write to file and parse file than 
// paginate using API
// aws cloudfront list-distributions >all-dists.json
let allDists = require('./all-dists.json')

// Get all tags to update
function getDistsTagsToUpdate(allDists) {
    console.log(`processing ${allDists.DistributionList.Items.length} distributions`)

    let tagsToUpdate = new Set()
    for (let dist of allDists.DistributionList.Items) {
        //console.log( `processing Distribution/: ${dist.Id}`)
        for (let origin of dist.Origins.Items) {
            if (origin.CustomOriginConfig.OriginReadTimeout != 180) {
                tagsToUpdate.add(dist.Id)
            }
        }
    }
    return Array.from(tagsToUpdate)
}

// get distributio0n config by ID
function getDistConfig(Id) {
    return new Promise((resolve, reject) => {
        cloudfront.getDistributionConfig({
            Id,
        }, function (err, data) {
            if (err) {
                reject(err)
            } else {
                resolve(data)
            }
        })
    })
}

// update the config
function updateDistConfig(config, distId) {
    return new Promise((res, rej) => {
        let updateParams = {
            DistributionConfig: config.DistributionConfig,
            Id: distId,
            IfMatch: config.ETag
        };
        cloudfront.updateDistribution(updateParams, function (err, data) {
            if (err)
                rej(err)
            else
                res(data)
        })
    })
}

// Update all dists with timeout = 180
async function updateDistsTimeout(distsToUpdate, newTimeout = 180) {
    let successDists = []
    let failedDists = []
    for (let distId of distsToUpdate) {
        try {
            console.log(`getting dist config for ${distId}`)
            let config = await getDistConfig(distId)
            fs.writeFileSync(`./original/original-${distId}.json`, JSON.stringify(config))
            for (origin of config.Origins.Items) {
                console.log(`\tUpdating ${origin.DomainName} from ${origin.CustomOriginConfig.OriginReadTimeout} to ${newTimeout}`)
                origin.CustomOriginConfig.OriginReadTimeout = newTimeout
            }
            fs.writeFileSync(`./updated/updated-${distId}.json`, JSON.stringify(config))

            // call the update Dist API
            await updateDistConfig(config, distId)
            successDists.push(distsToUpdate)
            console.log(`done Updating distribution ${distId}`)

        } catch (err) {
            failedDists.push(distsToUpdate)
            console.log(`FAILED updating distribution ${distId} : ${err.toString()}`)
        }
    }

    return {
        successDists,
        failedDists
    }
}

// main
mkdirp.sync('./original')
mkdirp.sync('./updated')

let distsToUpdate = getDistsTagsToUpdate(allDists)
console.log(`Updating ${distsToUpdate.length}`)
updateDistsTimeout(distsToUpdate, 180).then(({
    successDists,
    failedDists
}) => {
    console.log(`Succeeded: ${successDists.length}, failed : ${failedDists.length}`)
})

console.log(`Processed ${distsToUpdate.length} distributions`)