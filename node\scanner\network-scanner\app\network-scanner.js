// https://github.com/creationix/http-parser-js
// we do this to avid http header errors when servers return invalid headers
// ... addresses issues such as corrupt HTTP headers, which would otherwise cause <PERSON><PERSON>'s parser 
//      to throw a fatal error (HPE_INVALID_HEADER_TOKEN)
// Monkey patch before you require http for the first time.
// process.binding('http_parser').HTTPParser = require('http-parser-js').HTTPParser;

const EventEmitter = require('events');
const HTTPRequestMonster = require('../lib/http-request')
const ConfigProxy = require('../lib/config-proxy')
const path = require('path')
const RabbitMQ = require('../../common/lib/rabbitmq')
const QueueMessage = require('../../common/lib/queue-message')
const SitemapParser = require('../lib/sitemap-parser')
const debug = require('debug')('NetworkScanner')
const fs = require('fs')
const v8 = require('v8')
const mkdirp = require('mkdirp')
const _ = require('lodash')
const URL = require('url').URL
const ssAPI = require('../../common/lib/sooper-scheduler-api')
const PythonExtension = require('./extensions/python-extension')
const HaikuScanScriptHelper = require('../../common/lib/haiku-scan-script-helper')
const { MACHINE_TYPES } = require('../../common/config/app-constants')
const logger = require('../../common/lib/haiku-logger')
logger.setMetadata({
    haikuProcess: MACHINE_TYPES.SCANNER,
})

const dns = require('dns')
const HaikuUtils = require('../../common/lib/haiku-utils')
const s3Utils = require('../../common/lib/s3-utils')
const os = require('os')
const osUtils = require('os-utils')

// incoming messages
const AttackableNetworkRequests = require('../../common/lib/messages/attackable-nw-request')
const UpdateSessionInfo = require('../../common/lib/messages/update-session-info')
const GetSitemapUrlsRpc = require('../../common/lib/messages/get-sitemap-urls-rpc')
const CrawlStartedRpc = require('../../common/lib/messages/crawl-started-rpc')
const CrawlFinished = require('../../common/lib/messages/crawl-finished')
const CrawlStartedPublish = require('../../common/lib/messages/crawl-started-publish')
const ElectronCertificateError = require('../../common/lib/messages/electron-certificate-error')
const PauseScan = require('../../common/lib/messages/pause-scan')

// messges to crawler rabbitmq
const RefreshCrawlerRequestRpc = require('../../common/lib/messages/refresh-crawler-request-rpc')
const ScanFinished = require('../../common/lib/messages/scan-finished');
const hostFileQueue = require('../../common/lib/host-file-queue');
const FileUploadDelegate = require('../lib/file-upload-delegate');

class NetworkScanner extends EventEmitter {
    // will need to get config...
    constructor(config) {
        super()
        this.defaultConfig = config
        this.isHeapDumpWritten = false;

        // set the active scanner type
        let scannerType = process.env['HAIKU_SCANNER_TYPE']
        if (scannerType) {
            scannerType = scannerType.toLowerCase()
        }
        if (!config.ScannerType[scannerType]) {
            throw new TypeError(`invalid scanner type ${scannerType}. Please ensure environment variable HAIKU_SCANNER_TYPE is set to the corect scanner type (dev, test, prod)`)
        }
        config.ScannerType.active = config.ScannerType[scannerType]
        config.ScannerType.active.type = scannerType
        logger.transports.console.silent = config.ScannerType.active.logConsoleSilent

        // persistent storage
        this.vulnsFoundPrefix = 'scanner/vulnsFound/'
        this.pausedScansPrefix = 'scanner/paused/'
        this.crawlerPauseNewPrefix = 'crawler/paused-new/'
        this.httpResponsePrefix = 'scanner/httpResponse/'
        this.oobRevalidationPrefix = 'scanner/oob-revalidation/'
        this.replayAttackInfoPrefix = 'scanner/replayAttackInfo/'
        this.urlAnalysisPrefix = 'scanner/urlAnalysis/'

        // load plugins
        this.plugins = []
        this.loadPlugins()

        // Scan specific data store for plugins.
        this.runningScans = {}

        // Store received requests
        this.requestId = 1 // simple counter for request ID. Ideally should be per scan
        this.receivedRequestsPath = './requests'
        mkdirp.sync(this.receivedRequestsPath)
        mkdirp.sync(this.receivedRequestsPath + '/received')
    }

    /**
     * returns settings for the active scanner type  (dev/prod/test/...)
     */
    get activeScannerType() {
        return this.getDefaultConfig().ScannerType.active
    }


    /**
     * returns the default config i.e. config used to create this scanner.
     */
    getDefaultConfig() {
        return this.defaultConfig
    }

    /**
     * Gets the config proxy that represents merged site and default configs
     * Does not expect to be called for a non existent scan so no error checking here
     * @param {Number} scanId Scan Id to get config for
     */
    getConfig(scanId) {
        return this.getScanInfo(scanId)._private_.siteConfig
    }

    async init() {
        //If its window system then don't create the directory because of path issues
        try {
            if(os.platform() !== 'win32') { 
                if(!fs.existsSync('/mnt/haiku/temp/heapdump-heapsnapshots')) {
                    fs.mkdirSync('/mnt/haiku/temp/heapdump-heapsnapshots');
                }
            }
        }
        catch(err) {
            logger.log('error', `error in init(): ${err}`);
        }

        this.scannerIP = HaikuUtils.getPrivateIP();
        
        // The HTTP request monster
        this.httpReqMonster = new HTTPRequestMonster(this.getDefaultConfig())

        // message queue
        this.msgQ = new RabbitMQ(MACHINE_TYPES.SCANNER, { scannerMachineUID: this.scannerIP })
        //this.msgQ = new RabbitMQ('scanner')
        await this.msgQ.init()

        // set up the Rabbit MQ msg handlers. Both normal fanout ad the
        // Load Balanced (competing consumer/round robin) messages are handled by same handler
        this.msgQ.consume('request', this.mqRequestHandler.bind(this))
        this.msgQ.consume('lbRequest', this.mqRequestHandler.bind(this))
        // direct messaging between other process to scanner. i.e. crawler -> scanner
        let scannerQueueName = this.msgQ.getQueueName('directMessage');

        if(scannerQueueName && !scannerQueueName.includes('{{scannerMachineUID}}')) {
            this.msgQ.consume(scannerQueueName, this.mqRequestHandler.bind(this));
        }

        // event handlers
        this.on('vulnerability-found', this.updateFoundVulns.bind(this))
        this.on('revalidation-no-vulnerability-found', this.updateFoundVulns.bind(this))
        this.on('response-processing-started', this.onResponseProcessingStarted.bind(this))
        this.on('response-processing-completed', this.onResponseProcessingCompleted.bind(this))

        // Extensions
        this.pythonExtension = new PythonExtension(this.getDefaultConfig())

        // maintenance 'thread' 
        this.maintenanceIntervalId = setInterval(this.maintenanceProc.bind(this), this.getDefaultConfig().maintenanceIntervalSecs * 1000)
        this.saveScannerStateIntervalId = setInterval(this.saveScannerState.bind(this), 10 * 1000)

        // check for trigger heap dump every 1 minute
        // only run when scanner type is prod
        if(this.activeScannerType.type === 'prod') {
            //check if there is a file with same name in the directory, if so do not create a new one
            let files = fs.readdirSync('/mnt/haiku/temp/heapdump-heapsnapshots');
            let fileExists = false;

            if (files.length > 0 && files.find(file => file.includes(`${this.scannerIP}.heapsnapshot`))) {
                fileExists = true;
            }

            if(!fileExists) {
                this.checkForHeapDumpIntervalId = setInterval(this.checkForHeapDump.bind(this), 60 * 1000);
            }
        }
    }

    /**
     * event handler, attack response processing has started
     * @param {Number} scanId the scan where response processing started
     * @param {attack} attack the attack just performed
     */
    onResponseProcessingStarted(scanId, attack) {
        let scanData = this.getScanInfo(scanId)
        if (!scanData) {
            logger.log('error', 'ResponseStarted processing cannot be done because scan is complete/not started', HaikuUtils.getMetadataForLog(attack))
            return
        }

        scanData.responsesBeingProcessed.count++
        let body = _.get(attack, 'result.resp.body', '')
        scanData.responsesBeingProcessed.totalSize += body.length

        // *** Right now we are tracking only, not taking action when thresholds breached. ***
    }

    /**
     * event handler, attack response processing has completed
     * @param {Number} scanId the scan where response processing started
     * @param {attack} attack the attack just performed
     */
    onResponseProcessingCompleted(scanId, attack) {
        let scanData = this.getScanInfo(scanId)
        if (!scanData) {
            logger.log('error', 'ResponseCompleted processing cannot be done because scan is complete/not started', HaikuUtils.getMetadataForLog(attack))
            return
        }
        scanData.responsesBeingProcessed.count--
        let body = _.get(attack, 'result.resp.body', '')
        scanData.responsesBeingProcessed.totalSize -= body.length

        // some sanity checks
        if (scanData.responsesBeingProcessed.count < 0) {
            logger.log('error', `Whoa Whoa Whoa ${scanData.responsesBeingProcessed.count} is negative ... wtf?`, HaikuUtils.getMetadataForLog(scanData))
            scanData.responsesBeingProcessed.count = 0
            scanData.responsesBeingProcessed.totalSize = 0
        }
        if (scanData.responsesBeingProcessed.totalSize < 0) {
            logger.log('error', `Whoa Whoa Whoa ${scanData.responsesBeingProcessed.totalSize} is negative ... wtf?`, HaikuUtils.getMetadataForLog(scanData))
            scanData.responsesBeingProcessed.totalSize = 0
        }

        // *** Right now we are tracking only, not taking action when thresholds breached. ***
    }

    /**
     * accessor
     */
    getHttpRequestMonster() {
        return this.httpReqMonster
    }

    /**
     * Get the entry for the running scan with given ID. This will have data about scan like 
     * config and plugin strorage specific to that scan
     * 
     * @param {Number} scanId The scan id
     */
    getScanInfo(scanId) {
        return this.runningScans[scanId]
    }

    /**
     * Get HaikuScanScriptHelper instance
     * @param {String} scanId scanId of site (being scanned) to get HaikuScanScriptHelper instance
     * @returns {HaikuScanScriptHelper} HaikuScanScriptHelper instance specific to this plugin
     */
    getHaikuScanScriptHelper(scanId) {
        return _.get(this.runningScans[scanId], "haikuScanScriptHelper");
    }

    mqRequestHandler(mqMsg) {
        let msgBeingProcessed = 'unknown'
        try {
            // crack open the msg and perform whatever action we need to 
            let requestMsg = QueueMessage.createMessage(mqMsg)
            // ack/nack to rabbitMQ
            if (requestMsg && requestMsg.isRpc && this.activeScannerType.nackRpcMessages) {
                this.msgQ.nack(mqMsg)
            } else {
                // no selective acking yet
                this.msgQ.ack(mqMsg)
            }

            // always process the load balanced (RPC) ones. Other than that, only process requests where
            // this scanner is the one performing the scan
            let request = requestMsg ? requestMsg.getContent() : null
            let thisScannerShouldProcessMessage = request && (requestMsg.isRpc || this.runningScans[request.scanId])
            if (thisScannerShouldProcessMessage) {
                msgBeingProcessed = requestMsg.msgType
                request.requestId = this.requestId++;
                let tolog = {
                    msgType: msgBeingProcessed,
                    timestamp: (new Date()).toUTCString(),
                    request
                }
                fs.appendFile(`${this.receivedRequestsPath}/received/received-msg-${request.scanId}.json`, `${JSON.stringify(tolog)}`, (err) => {
                    if (err) {
                        logger.log('error', `could not append request to file: ${err}`, HaikuUtils.getMetadataForLog(request));
                    }
                })

                logger.log('info', `received msg: ${JSON.stringify(request)}`, HaikuUtils.getMetadataForLog(request))
                switch (msgBeingProcessed) {
                    case AttackableNetworkRequests.msgType:
                        this.newRequest(request)
                        break;

                    case UpdateSessionInfo.msgType:
                        this.updateSessionInfo(request)
                        break;

                    case GetSitemapUrlsRpc.msgType:
                        this.getSitemapUrls(request, requestMsg)
                        break;

                    case CrawlStartedRpc.msgType:
                        this.crawlStarted(request, requestMsg)
                        break;

                    case CrawlFinished.msgType:
                        this.crawlFinished(request)
                        break;

                    case CrawlStartedPublish.msgType:
                        this.crawlStartedPublish(request)
                        break;

                    case ElectronCertificateError.msgType:
                        this.electronCertificateError(request)
                        break;

                    case PauseScan.msgType:
                        this.pauseScan(request)
                        break

                    default:
                        logger.log('info', `UNKNOWN MESSAGE ${msgBeingProcessed}`, HaikuUtils.getMetadataForLog(request))
                }
            }
        } catch (err) {
            logger.log('error', `error processing message ${msgBeingProcessed} - ${err}`)
        }
    }

    /**
     * Annotates request with metadata like unique key & priority info
     * @param {request} request New request sent by crawler
     * 
     * annotations:
     *  Haiku key => used to uniquely identify the request
     *  Haiku priority => used to prioritize next url to attacks 
     */
    annotateRequest(request) {
        let url = new URL(request.httpRequest.uri)

        // --- Generate Key

        // ignore session-ish information in pathname. 
        // asp style session using () in path -> remove entire path component eg. /somepath/(S(unzwpk2x3gdbcqbzfcjz3lfc))/web/xyz.aspx
        // jsessionid style session ;sessionid -> remove part after the ; eg. /somepath/j_security_check;jsessionid=...
        url.pathname = HaikuUtils.canonacalizePathname(url.pathname)

        let {
            params,
            values
        } = HaikuUtils.splitIntoParamsAndValues(request.httpRequest)
        request.httpRequest.haikuKey = request.httpRequest.method + '-' + HaikuUtils.canonacalizeHost(url) + url.pathname + '?' + params.join('&')

        // -- resource type
        request.httpRequest.haikuResourceType = this.getHaikuResourceType(request)

        // --- some internal info to help in prioritizing requests
        request.haikuPriority = {}
        request.haikuPriority.priority = _.isNumber(request.priority) ? request.priority : 100
        request.haikuPriority.requestParams = params
        request.haikuPriority.nonEmptyValueCount = values.filter(v => v).length
        request.haikuPriority.pathComponentsCount = url.pathname.split('/').filter(p => p).length
    }

    /**
     * convert the resource type sent by the crawler to standardised haiku resource type
     * for now this is core (mainframe etc.), resource (js, stylesheet) and other
     * @param {request} request the request sent by crawler
     */
    getHaikuResourceType(request) {
        let haikuResourceType = 'other';
        if (request && /haiku/i.test(request.scanner)) {
            switch (request.httpRequest.resourceType) {
                case "main_frame":
                case "mainFrame": // electron seems to change main_frame to this
                case "sub_frame":
                case "subFrame": // electron seems to change sub_frame to this
                case "xmlhttprequest":
                case "xhr": // another alias ?
                    // always process these requests
                    haikuResourceType = 'core';
                    break;

                case "stylesheet":
                case "script":
                case "image":
                case "font":
                    haikuResourceType = 'resource';
                    break;

                case "object":
                case "ping":
                case "csp_report":
                case "media":
                case "other": // one example of this is font loaded from css  processing
                case "websocket":
                    break;
                default:
                    logger.log('warn', `treating unknown resourcetype ${request.httpRequest.resourceType} as 'other'`, HaikuUtils.getMetadataForLog(request));
            }
        }
        return haikuResourceType;
    }

    // got a new request - 
    async newRequest(request) {
        try {
            // sanity checks
            // unknown scan id
            if (!this.runningScans[request.scanId]) {
                logger.log('info', `Unknown scan ID ${request.scanId}, href: ${request.httpRequest.uri}. Most likely cause is that network scanner was restarted in the middle of a crawl.`, HaikuUtils.getMetadataForLog(request))
                return
            }

            // if we are only revalidating, don't accept this request
            if (this.getConfig(request.scanId).ScannerSettings.doRevalidate == 'only') {
                logger.log('info', `Revalidation set to 'only', ignoring request`, HaikuUtils.getMetadataForLog(request))
                return
            }

            if (_.get(this.getConfig(request.scanId), 'ScannerSettings.skipCrawl')) {
                logger.log('info', `skipCrawl setting is true but looks like crawler did not get the message. Ignoring request`, HaikuUtils.getMetadataForLog(request))
                return
            }

            // Don't accept requests if we are not allowed to attack the URI.
            if (!this.canAttackUrl(request.scanId, request.httpRequest.uri)) {
                logger.log('info', `Ignoring since not allowed to attack URI`, HaikuUtils.getMetadataForLog(request))
                return
            }

            this.annotateRequest(request)

            // check for requests that we cannot handle like captchas
            if (!this.attackableByHaiku(request)) {
                logger.log('info', `Ignoring since Haiku will not/cannot attack this request.`, HaikuUtils.getMetadataForLog(request))
                return
            }

            // Check for duplicate requests
            if (-1 != this.runningScans[request.scanId].uniqueCrawlerRequests.indexOf(request.httpRequest.haikuKey)) {
                logger.log('info', `Ignoring duplicate request, key: ${request.httpRequest.haikuKey}`, HaikuUtils.getMetadataForLog(request))
                return
            }
            this.runningScans[request.scanId].uniqueCrawlerRequests.push(request.httpRequest.haikuKey)
            this.runningScans[request.scanId].stats.byCrawlerReq[request.httpRequest.haikuKey] = {
                received: new Date(),
                numHttpRequests: 0
            }

            // if the crawl has already finished, don't accept this request - this could be 
            // because of timeout / config mismatch between crawler & scanner
            if (this.runningScans[request.scanId].crawlComplete || this.runningScans[request.scanId].scanComplete) {
                logger.log('info', `Crawl/Scan is already complete`, HaikuUtils.getMetadataForLog(request))
                return
            }

            // fix up the scanlogID since SOC does not pass it when relaying the
            // network request from IGW crawler
            request.scanlog_id = this.runningScans[request.scanId].scanLogId

            // add to the queue of requests to  be processed for this scanId
            this.runningScans[request.scanId].queuedRequests.push(request)

            // process the request queue for this scan
            this.processRequestsForScan(request.scanId)
        } catch (err) {
            logger.log('error', `error in newRequest(): ${err}`, HaikuUtils.getMetadataForLog(request))
        }
    }

    /**
     * is this request attackable by us?
     * @param {request} request crawler request to attack
     */
    attackableByHaiku(request) {
        let attackable = true

        // cannot attack any request with captcha
        if (attackable) {
            attackable = !/captcha/i.test(request.httpRequest.haikuKey)
        }

        return attackable
    }

    /**
     * 
     * @param {request} request Update session content
     */
    updateSessionInfo(request) {
        try {
            // sanity checks
            // unknown scan id
            if (!this.runningScans[request.scanId]) {
                logger.log('info', `Unknown scan ID, sessioninfo: ${JSON.stringify(request.sessionInfo)}.`, HaikuUtils.getMetadataForLog(request))
                return
            }

            this.runningScans[request.scanId].sessionInfo = request.sessionInfo
        } catch (err) {
            logger.log('error', `error in newRequest(): ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
        }
    }

    getSitemapUrls(request, requestMsg) {
        logger.log('info', `getting sitemap URLS for ${request.mainUrl}`, HaikuUtils.getMetadataForLog(request))
        let sitemapParser = new SitemapParser(request.mainUrl, request.maxSitemapLinks)
        sitemapParser.getSitemapUrls().then(function (urls) {
            // respond if allowed to by scanner type
            if (this.activeScannerType.sendRpcReply) {
                let response = {
                    sitemapUrls: urls,
                    scannerIP: this.scannerIP
                }
                requestMsg.rpcReply(this.msgQ, response)
            } else {
                logger.log('info', 'skipping rpc reply as specified in config', HaikuUtils.getMetadataForLog(request))
            }
        }.bind(this))
    }

    /**
     * Crawler informing us that crawl has started. Used to set up structures for a new scan
     * like scan specific storage and metrics. 
     * @param {crawlStartedMsgContent} request contents of the message
     * @param {QueueMessage} requestMsg The actual Msg (needed to send RPC response)
     */
    async crawlStarted(request, requestMsg) {
        let scanLogId
        try {
            scanLogId = await this.startScan(request)

            // ----
            // After this point, jobstart has already been called. So if we encounter any fatal errors now,
            // we have to call jobcomplete to do things cleanly. See startScan for how to clean up
            //
            // Falling back to full scan + crawl is not a fatal error as the fallback actions are fine.
            // ----

            let usePreviousCrawlInfo = _.get(this.getConfig(request.scanId), 'ScannerSettings.usePreviousCrawlInfo')
            if (request.resumeScan) {
                // resume scan 
                await this.resumeScan(request)
            } else if (usePreviousCrawlInfo) {
                // set up queue to be previous crawl info
                await this.reusePreviousCrawlInfo(request)
            }
        } catch (err) {
            logger.log('error', `error in crawlStarted(): ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
        }

        logger.log('info', `Start scan routines has been processed.`, HaikuUtils.getMetadataForLog(request))
        // Reply to RPC if we are allowed to
        if (this.activeScannerType.sendRpcReply) {
            let skipCrawl = _.get(this.getConfig(request.scanId), 'ScannerSettings.skipCrawl')
            let addToRequest = {}
            if (_.get(this.getConfig(request.scanId), 'ScannerSettings.reflectAddToRequestToCrawler')) {
                addToRequest = _.get(this.getConfig(request.scanId), 'ScannerSettings.addToRequest', {})
            }

            // send RPC response
            let response = {
                scanLogId: scanLogId,
                addToRequest,
                skipCrawl,
                pid: process.pid,
                hostname: os.hostname(),
                scannerIP: this.scannerIP
            }
            requestMsg.rpcReply(this.msgQ, response)
        } else {
            logger.log('info', 'skipping rpc reply as specified in config', HaikuUtils.getMetadataForLog(request))
        }
    }

    async readOOBRevalidation(siteConfig, request) {
        try {
           let oobRevalidationResp = await s3Utils.getFile(this.oobRevalidationPrefix, request.serviceId + '.json');

           if(oobRevalidationResp) {
                let oobRevalidationData = JSON.parse(oobRevalidationResp.Body.toString());

                if(oobRevalidationData) {                    
                    logger.log('info', `Found oob revalidation details => '${JSON.stringify(oobRevalidationData)}'`);
                    let plugins = this.getDefaultConfig(request.scanId).Plugins;
                    let isUpload = false;

                    for (let index = 0; index < oobRevalidationData.plugins.length; index++) {
                        let oobVuln = oobRevalidationData.plugins[index];
                        
                        for(let pluginName in plugins) {
                            let pluginData = plugins[pluginName];

                            for(let vulnTitle in pluginData.vulnerabilities) {
                                let vuln = pluginData.vulnerabilities[vulnTitle];
                                if(vuln.igwId == oobVuln.vulnId && _.has(pluginData, 'canAttackInOOB') && pluginData.canAttackInOOB) {
                                    siteConfig.Plugins = _.get(siteConfig, 'Plugins', {});
                                    siteConfig.Plugins[pluginName] = _.cloneDeep(pluginData);
                                    
                                    if(oobRevalidationData.plugins[index].totalScansAfterFound == 0) {
                                        oobRevalidationData.plugins[index].totalScansAfterFound++;
                                        oobRevalidationData.plugins[index].scanId = parseFloat(request.scanId);
                                        isUpload = true;
                                    }
                                    else {
                                        if(parseFloat(request.scanId) > parseFloat(oobRevalidationData.plugins[index].scanId)) {
                                            oobRevalidationData.plugins[index].totalScansAfterFound++;
                                            oobRevalidationData.plugins[index].scanId = parseFloat(request.scanId);
                                            isUpload = true;
                                        }
                                    }

                                    if(oobRevalidationData.plugins[index].totalScansAfterFound > 7) {
                                        logger.log('info', `Total maximum scan reached to store OOB attack data. Only reset to 0 by OOB utility once new valid dns entry found.`);
                                        return;
                                    }
                                    else {
                                        siteConfig.Plugins[pluginName].uploadAttackToS3 = true;
                                    }

                                    logger.log('info', `uploadAttackToS3 set to true for plugin '${pluginName}'`)    
                                }
                            }
                        }
                    }

                    if(isUpload) {
                        await s3Utils.upload(this.oobRevalidationPrefix, request.serviceId + '.json', JSON.stringify(oobRevalidationData));
                    }
                }
           }
        } catch (err) {
           logger.log(`error', 'Unable to read oob revalidation file, continue scan anyway, Reason ${err.toString()}`);
        }
   }

    /**
     * Start a new scan. Used to set up structures for a new scan like scan specific storage and metrics. 
     * @param {crawlStartedMsgContent} request contents of the message (crawl started/resume scan)
     */
    async startScan(request, requestMsg) {
        let scanLogId
        try {
            scanLogId = await ssAPI.scanStarted(request) // inform SS API that scan has started            

            // Dont reset all data structures but reply anyway since someone's waiting on the reply
            if (this.runningScans[request.scanId]) {
                logger.log('info', `CrawlStarted already called - checking if we need to update scanlogId`, HaikuUtils.getMetadataForLog(request))
                if (scanLogId != this.runningScans[request.scanId].scanLogId) {
                    // should send mail to Jitu, Ashok etc. For now, just log this
                    logger.log('error', `Tell Jitu, Ashok: ${request.scanId} - updating scanlogId from ${this.runningScans[request.scanId].scanLogId} to ${scanLogId}`, HaikuUtils.getMetadataForLog(request))
                    this.runningScans[request.scanId].scanLogId = scanLogId
                }
            } else {
                // flag this early so that even is ssapi takes time to return, we dont process multiple 
                // crawlStarted requests. This is extremely unlikely to happen in production but could 
                // face it if the only scanner NACKs a crawlstarted Request -> Rabbit MQ requeues
                let scanData = request
                this.runningScans[request.scanId] = scanData
                this.runningScans[request.scanId].attackRequestId = this.runningScans[request.scanId].attackRequestId || 0; //Initialize unique attack request id for request traking in httpRequestMonster
                scanData.isInitializing = true // ensure this.runningScans[scanId] is not used before we are done initializing

                if (scanLogId) {
                    // set up basic structures for this scan
                    scanData.scanLogId = scanLogId
                    scanData._private_ = {}

                    // site specific config 
                    let siteConfig = _.get(request, 'networkScanner', {})
                    siteConfig = JSON.parse(JSON.stringify(siteConfig, HaikuUtils.configReplacer), HaikuUtils.configReviver)

                    logger.log('info', `readOOBRevalidation file being read for scanId ${request.scanId}`, HaikuUtils.getMetadataForLog(request))
                    await this.readOOBRevalidation(siteConfig, request);
                    logger.log('info', `readOOBRevalidation file read successfully for scanId ${request.scanId}`, HaikuUtils.getMetadataForLog(request))
                    
                    // START ----- HACK HACK HACK -----
                    // This should be changed to have WAS generate a recipe step instead of this 'global' setting
                    // This ugly choice was made because recipes are available only to the CS team initially. When
                    // we allow recipes for everyone, this should be fixed to be clean and below code deleted
                    if (!siteConfig.HTTPRequestMonster && // setting has not been customized by a recipe
                        request.perDomainMaxParallelRequests && // this field has been set in UI
                        request.perDomainMaxParallelRequests != this.getDefaultConfig().HTTPRequestMonster.perDomainMaxParallelRequests // not same as default
                    ) {
                        siteConfig.HTTPRequestMonster = _.cloneDeep(this.getDefaultConfig().HTTPRequestMonster)
                        siteConfig.HTTPRequestMonster.perDomainMaxParallelRequests = request.perDomainMaxParallelRequests
                    }
                    let maxParallelReqs = _.get(siteConfig, 'HTTPRequestMonster.perDomainMaxParallelRequests', this.getDefaultConfig().HTTPRequestMonster.perDomainMaxParallelRequests)
                    logger.log('info', `max Parallel Requests for this site is ${maxParallelReqs}`, HaikuUtils.getMetadataForLog(request))

                    if(siteConfig.doRevalidate || siteConfig.isApiScan) {
                        //Force proxy scanner setting properties
                        scanData._private_.siteConfig = ConfigProxy.proxyConfig(siteConfig, this.getDefaultConfig(), 'ScannerSettings')
                    }
                    else {
                        scanData._private_.siteConfig = ConfigProxy.proxyConfig(siteConfig, this.getDefaultConfig())
                    }

                    //Skip the crawl & skipRefreshRequestBeforeAttack while in api scan mode.
                    if(this.getConfig(request.scanId).ScannerSettings.isApiScan) {
                        this.getConfig(request.scanId).ScannerSettings.skipCrawl = this.getConfig(request.scanId).ScannerSettings.isApiScan;
                        this.getConfig(request.scanId).ScannerSettings.skipRefreshRequestBeforeAttack = true;
                    }

                    if(this.getConfig(request.scanId).ScannerSettings.replayScanInfo.isReplayScan) {
                        this.getConfig(request.scanId).ScannerSettings.doRevalidate = 'only';
                        request.replayScanInfo = this.getConfig(request.scanId).ScannerSettings.replayScanInfo;

                        //During api replay scan, skip crawl already set to true. So, no need to set it again.
                        //Otherwise it will cause write to read only property error.
                        if(this.getConfig(request.scanId).ScannerSettings.skipCrawl === false) {
                            this.getConfig(request.scanId).ScannerSettings.skipCrawl = true;
                        }

                        request.skipUpdateHostFile = true;
                    }

                    // Do not revalidate, attack or process attack response if api discovery scan mode
                    if(request.apiDiscovery) {
                        this.getConfig(request.scanId).ScannerSettings.doRevalidate = 'never';
                        this.getConfig(request.scanId).DefaultPluginSettings.canAttack = false;
                        this.getConfig(request.scanId).DefaultPluginSettings.processAttackResponse = false;
                    }

                    //Update revalidation config
                    if(siteConfig.doRevalidate) {
                        logger.log('info', `Revalidation value set to this site is '${siteConfig.doRevalidate}'`, HaikuUtils.getMetadataForLog(request))    

                        //Only assign skip crawl if its not api scan mode - To avoid assign value to read only property
                        if(siteConfig.doRevalidate == 'only' && !siteConfig.isApiScan) {
                            this.getConfig(request.scanId).ScannerSettings.skipCrawl = true;
                        }
                    }

                    logger.log('info', `Revalidation file being read for scanId ${request.scanId}`, HaikuUtils.getMetadataForLog(request))
                    // get the info about previously found vulnerabilities
                    await this.processRevalidationInfo(request.scanId)
                    logger.log('info', `Revalidation file read successfully for scanId ${request.scanId}`, HaikuUtils.getMetadataForLog(request))

                    // Read url analysis data. Gives us a lot of info about the site framework, tech stack etc.
                    if(request.serviceId) {
                        let urlAnalysisData = await this.readUrlAnalysis(request.serviceId);
                        request.urlAnalysisData = urlAnalysisData;
                    }

                    // See if we have additional requests to run
                    let additionalHTTPRequests = _.get(this.getConfig(request.scanId), 'ScannerSettings.additionalRequests', [])

                    scanData.annotatedRequests = {}
                    scanData.haikuScanScriptHelper = new HaikuScanScriptHelper(this, request.scanId);
                    
                    if (request.isApiScan) {
                        logger.log('info', `Scan mode is api scan.`, HaikuUtils.getMetadataForLog(request));

                        let annotatedRequestsFile = _.get(this.getConfig(request.scanId), 'ScannerSettings.annotatedRequestsFromFile');

                        if (annotatedRequestsFile) {
                            try {
                                let resp = await s3Utils.getFile(path.dirname(annotatedRequestsFile), path.basename(annotatedRequestsFile));
                                let annotationContent = null;

                                if (resp && resp.Body) {
                                    annotationContent = resp.Body;
                                }

                                if (annotationContent) {
                                    scanData.annotatedRequests = JSON.parse(annotationContent);

                                    if(scanData.annotatedRequests.info) {
                                        //This is postman file & not haiku request file
                                        scanData.isPostmanFile = true;
                                        scanData.postmanCollection = _.cloneDeep(scanData.annotatedRequests);
                                        let apiRequestTimeoutInSec = this.getConfig(request.scanId).ScannerSettings.apiRequestTimeoutInSec;
                                        scanData.annotatedRequests = await HaikuUtils.runPostmanRequest(request.scanId, _.cloneDeep(scanData.annotatedRequests), '', apiRequestTimeoutInSec, logger);
                                    }

                                    //If scriptVariables passed from site config, override those over annotatedRequestsFile scriptVariables.
                                    let scriptVariables = _.get(this.getConfig(request.scanId), 'scriptVariables', null);
                                    scanData.annotatedRequests.scriptVariables = scriptVariables ? Object.assign(scanData.annotatedRequests.scriptVariables, scriptVariables) : scanData.annotatedRequests.scriptVariables;
                                    
                                    if(scriptVariables) {
                                        logger.log('info', `scriptVariables passed from site config. After merge scriptVariables=${JSON.stringify(scanData.annotatedRequests.scriptVariables)}`, HaikuUtils.getMetadataForLog(request));
                                    }

                                    HaikuUtils.annotateScript(scanData.annotatedRequests);

                                    // Filter haikuRequests where URL is not empty
                                    scanData.annotatedRequests.haikuRequests = scanData.annotatedRequests.haikuRequests.filter(req => req.uri && req.uri.trim() !== '');

                                    //Ignore site config apiIds if replay scan is enabled
                                    if (siteConfig.apiIds && !this.getConfig(request.scanId).ScannerSettings.replayScanInfo.isReplayScan) {
                                        let filteredRequests = _.chain(scanData.annotatedRequests.haikuRequests)
                                            .map((haikuRequest) => {
                                                if (siteConfig.apiIds.includes(haikuRequest.name)) {
                                                    haikuRequest.canAttackInApiScan = true;
                                                    return haikuRequest;
                                                }
                                                else {
                                                    logger.log('info', `Filtering request with name=${haikuRequest.name} in api scan annotatedRequestsFromFile=${annotatedRequestsFile}.`, HaikuUtils.getMetadataForLog(request));
                                                    haikuRequest.canAttackInApiScan = false;
                                                    return haikuRequest;
                                                }
                                            })
                                            .compact()
                                            .value();

                                        if (filteredRequests.length > 0) {
                                            scanData.annotatedRequests.haikuRequests = filteredRequests;
                                            additionalHTTPRequests = scanData.annotatedRequests.haikuRequests;
                                            logger.log('info', `annotatedRequestsFromFile=${annotatedRequestsFile} read successfully with filtered api list=${JSON.stringify(filteredRequests)}.`, HaikuUtils.getMetadataForLog(request));
                                        }
                                        else {
                                            logger.log('error', `After filtering annotated, haiku requests are empty and api scan will not work for annotatedRequestsFromFile=${annotatedRequestsFile}`, HaikuUtils.getMetadataForLog(request));
                                        }
                                    }
                                    else {
                                        additionalHTTPRequests = scanData.annotatedRequests.haikuRequests;
                                        logger.log('info', `Filter api list not provided. Including all api in scan from annotatedRequestsFromFile=${annotatedRequestsFile} read successfully.`, HaikuUtils.getMetadataForLog(request));
                                    }
                                }
                            }
                            catch (err) {
                                logger.log('error', `Could not process annotatedRequestsFromFile=${annotatedRequestsFile} file: ${err.toString()}`, HaikuUtils.getMetadataForLog(request));
                            }
                        }
                        else {
                            logger.log('error', `Could not get annotatedRequestsFromFile becuase no file location provided.`, HaikuUtils.getMetadataForLog(request));
                        }
                    }

                    let additionalRequests = additionalHTTPRequests.map((req) => {
                        HaikuUtils.annotateScript(req);

                        return {
                            httpRequest: req
                        }
                    })

                    // add info we need to keep additional request similar to what crawler sends.
                    for (let addlReq of additionalRequests) {
                        // manually entered ones are always 'high' priority - default from crawler is 100, lower is more priority
                        addlReq.priority = addlReq.priority || 10

                        // add scanId, scanlog id
                        addlReq.scanId = request.scanId
                        addlReq.scanlog_id = request.scanlogId = scanLogId

                        // anything additional to add/fixup
                        let httpRequest = addlReq.httpRequest
                        httpRequest.resourceType = httpRequest.resourceType || "mainFrame"

                        // add enough info to refresh requests. If browser actions or bookmark is not provided, 
                        // create the browser actions as a load of the URI to give a chance of session refresh
                        if (!httpRequest.crawlerBookmark && !httpRequest.wizardGuide) {
                            httpRequest.wizardGuide = {
                                name: 'auto-generated',
                                actions: [{
                                    action: 'load',
                                    uri: httpRequest.uri,
                                    xpath: "[auto-generated]",
                                    annotation: 'auto-generated'
                                }]
                            }
                        }

                        if (request.isApiScan) {
                            //In api scan, If URL is dynamic varible which not yet expanded & this result into invalid URL error in HaikuUtils.genHaikuKey
                            //method. To resolve this issue we expand clone httpRequest first & then generate crawlerKey/appTranaKey 
                            let scriptVariables = _.get(this.getScanInfo(request.scanId), "annotatedRequests.scriptVariables", null);

                            if(scriptVariables) {
                                let clonedRequest = _.cloneDeep(addlReq);
                                HaikuUtils.updateScriptVariables(clonedRequest, scriptVariables);
                                httpRequest.uri = clonedRequest.httpRequest.uri;
                            }
                        }

                        // crawler/apptrana key
                        httpRequest.crawlerKey = httpRequest.crawlerKey || httpRequest.appTranaKey || HaikuUtils.genHaikuKey(httpRequest)

                        // annotate this request
                        addlReq.scanner = addlReq.scanner || 'haiku'
                        this.annotateRequest(addlReq)
                    }

                    // set up other data
                    scanData.queuedRequests = additionalRequests
                    scanData.processedRequests = []
                    scanData.uniqueCrawlerRequests = []
                    scanData.attackedCrawlerRequests = []
                    scanData.uniqueParams = []
                    scanData.sessionInfo = {}
                    scanData.stats = {
                        byCrawlerReq: {},
                        byPlugin: {},
                        byAttackArea: {},
                        pluginTimings: {
                            version: 2
                        }
                    }
                    scanData.crawlComplete = false
                    scanData.scanComplete = false
                    scanData.crawlStart = Date.now()
                    scanData.scanFeasible = true
                    scanData.savedResponseCnt = 1
                    scanData.responsesBeingProcessed = {
                        count: 0,
                        totalSize: 0
                    }

                    // crawl informs us of its max time, clean up if it does not complete by then
                    // don't set a timer for cleaning up scandata. Why set so many timers when we can set one and periodically 
                    // check instead for all scans that are completed. This is done in init()
                    scanData.expectedCrawlEnd = scanData.crawlStart + (request.maxCrawlTimeMins * 60 * 1000) // when its expected to end (milliseconds from now)

                    // Update hosts file so that scan skips WAF
                    try {
                        logger.log('info', `updateHostFileWithQueue is being called for ${request.scanId}`, HaikuUtils.getMetadataForLog(request))
                        let hostname = new URL(request.mainUrl).hostname

                        // if wafIpToUse is set, then we need to update the host file for CDN bypass
                        if(request.wafIpToUse) {
                            request.skipUpdateHostFile = false;
                        }

                        await hostFileQueue.updateHostFileWithQueue(hostname, request.skipUpdateHostFile, {
                            scanId: request.scanId,
                            scanlogId: request.scanLogId || request.scanlog_id,
                            uri: request.mainUrl,
                            haikuKey: request.mainUrl
                        }, request.proxyPassDetails, logger)
                        logger.log('info', `proxyPassDetails: ${JSON.stringify(request.proxyPassDetails)}`, HaikuUtils.getMetadataForLog(request));
                    } catch (err) {
                        logger.log('error', `Could not update /etc/hosts (continuing with scan anyway): ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
                    }
                    
                    logger.log('info', `httpRequestMonster initializing for scanId ${request.scanId}`, HaikuUtils.getMetadataForLog(request))
                    // let httpReqMonster know that this scan is starting
                    this.httpReqMonster.scanStarted(request, this.getConfig(request.scanId).HTTPRequestMonster) 
                    logger.log('info', `httpRequestMonster initialized for scanId ${request.scanId}`, HaikuUtils.getMetadataForLog(request))                   

                    scanData.isInitializing = false
                } else {
                    logger.log('info', `Could not get scanlogId for ${request.mainUrl}}`, HaikuUtils.getMetadataForLog(request))
                    delete this.runningScans[request.scanId]
                }
            }
        } catch (err) {
            logger.log('error', `error in startScan(): ${err.toString()}`, HaikuUtils.getMetadataForLog(request))

            // cleanup
            if (scanLogId) { // called jobstart api, need to call jobend
                ssAPI.scanStopped(request.scanId, {
                    msg: 'exception in startScan method',
                    error: err.toString()
                })
            }

            this.httpReqMonster.scanComplete(request.scanId) // safe to call even if scanStarted was not called.
            delete this.runningScans[request.scanId]
            scanLogId = null
        }

        return scanLogId
    }

    /**
     * 
     * @param {Object} scanData 
     */
    async processRevalidationInfo(scanId) {
        let scanData = this.runningScans[scanId]
        if (!scanData) {
            logger.log('info', `processRevalidationInfo - Ignoring unknown scanID ${scanId}.`, HaikuUtils.getMetadataForLog(scanData))
            return
        }

        let revalidateVulns = {};
        if (this.getConfig(scanId).ScannerSettings.doRevalidate != 'never') {
            try {
                let revalidateContents;
                let serviceIdPrefix = this.runningScans[scanId].serviceId + "/"
                let resp = await s3Utils.getFile(this.vulnsFoundPrefix + serviceIdPrefix, 'revalidateVulns.json')
                if (resp && resp.Body) {
                    revalidateContents = resp.Body;
                }
                if (revalidateContents) {
                    revalidateVulns = JSON.parse(revalidateContents);
                }
                // dec count and remove decayed ones.
                for (let key of Object.keys(revalidateVulns)) {
                    revalidateVulns[key].scansToLive--;
                    if (revalidateVulns[key].scansToLive < 0) {
                        delete revalidateVulns[key];
                        continue
                    }

                    // fix up the successful attacks objects
                    //  - clean up any 'successful attacks' that were not found in previous scan i.e. where foundVulns is empty
                    //  - vulns found in previous scan are now the expected ones used to determine missing vulns.
                    let successfulAttacks = revalidateVulns[key].successfulAttacks
                    if (successfulAttacks) {
                        for (let attackKey of Object.keys(successfulAttacks)) {
                            if (!successfulAttacks[attackKey].foundVulns || successfulAttacks[attackKey].foundVulns.length == 0) {
                                // clean up any 'successful attacks' that were not found in previous scan i.e. where foundVulns is empty
                                delete successfulAttacks[attackKey]
                            } else {
                                // vulns found in previous scan are now the expected ones used to determine missing vulns.
                                successfulAttacks[attackKey].expectedVulns = successfulAttacks[attackKey].foundVulns
                                delete successfulAttacks[attackKey].foundVulns
                                delete successfulAttacks[attackKey].foundVulnsIgId
                            }
                        }
                    }
                }
            } catch (err) {
                logger.log('error', `Could not get previously found vulns from S3 : ${err.toString()}`, HaikuUtils.getMetadataForLog(scanData));
            }
        }
        scanData.revalidateVulns = revalidateVulns || {};
        scanData.revalidationsToProcess = Object.keys(scanData.revalidateVulns);
        scanData.revalidationsToProcess.sort()

        //Read alertdetails for replay scan
        try {
            let replayScanInfo = scanData.replayScanInfo;
            if (replayScanInfo && replayScanInfo.isReplayScan) {
                let alertContents
                let serviceIdPrefix = this.runningScans[scanId].serviceId + "/";
                let resp = await s3Utils.getFile(this.vulnsFoundPrefix + serviceIdPrefix, 'alertDetails.json');
                if (resp && resp.Body) {
                    alertContents = resp.Body;
                }
                if (alertContents) {
                    scanData.alertDetails = JSON.parse(alertContents);
                }
            }
        } catch (error) {
            logger.log('error', `Could not get alert details from last scan : ${error.toString()}`, HaikuUtils.getMetadataForLog(scanData));
        }
    }

    /**
     * Read url analysis details from storage
     * @param {Number} serviceId serviceId of the scan 
     * @returns {Object} url analysis details
     */
    async readUrlAnalysis(serviceId) {
        try {
            let urlAnalysis = await s3Utils.getFile(this.urlAnalysisPrefix + serviceId, 'analysis.json');
            if (urlAnalysis) {
                let urlAnalysisData = JSON.parse(urlAnalysis.Body.toString());
                return urlAnalysisData;
            }
        } catch (error) {
            logger.log('error', `Could not get url analysis details from storage for serviceId ${serviceId} : ${error.toString()}`);
        }
    }

    /**
     * Crawler informing us that crawl has finished. Now we can wait for all network requests to complete 
     * and then tear down this scan specific structures.
     * @param {crawlFinishedMsgContent} request 
     */
    crawlFinished(request) {
        // sanity check
        if (!this.runningScans[request.scanId]) {
            logger.log('info', `Ignoring unknown scanID. Most likely cause is that network scanner was restarted in the middle of a crawl.`, HaikuUtils.getMetadataForLog(request))
            return
        }

        logger.log('info', `Crawl finished message received from crawler for scanId ${request.scanId}, request: ${JSON.stringify(request)}`, HaikuUtils.getMetadataForLog(request));

        // set the flag that scan has completed - maintenence proc will do the rest
        this.runningScans[request.scanId].crawlerInfo = request
        this.runningScans[request.scanId].scanFeasible = request.scanFeasible // set scan feasible from crawler info.
        this.runningScans[request.scanId].scanFeasibleMsg = request.scanFeasibilityMsg // set scan feasible from crawler info.

        this.runningScans[request.scanId].crawlComplete = true
    }

    /**
     * Crawler started notification along with private IP during resume scan. 
     * If cralwer IP do not match with existing IP in scan list. Then kill scan on this scanner instance because already another 
     * scanner taken over same scan and avoid running orphan scan on this scanner due to no pause scan called by WAS portal before resume.
     * @param {crawlStartedPublishMsgContent} request 
     */
    crawlStartedPublish(request) {
        try {
            let scanId = request.scanId;

            if(this.runningScans[scanId] && this.runningScans[scanId].crawlerIP && request.crawlerIP && this.runningScans[scanId].crawlerIP != request.crawlerIP) {
                logger.log('info', `New Crawler IP = ${request.crawlerIP}, Old Crawler IP = ${this.runningScans[scanId].crawlerIP}.`, HaikuUtils.getMetadataForLog(request))
                logger.log('info', `Killing the scan because another scan is running with same scanId.`, HaikuUtils.getMetadataForLog(request))
                this.httpReqMonster.scanDone(scanId);
                this.httpReqMonster.forcePauseScan(scanId);
                this.httpReqMonster.scanComplete(scanId);
                delete this.runningScans[scanId];
                logger.log('info', `Killed the scan successfully.`, HaikuUtils.getMetadataForLog(request))
            }
        } catch (error) {
            logger.log('error', `Unable to kill scan, ${error.toString()}.`, HaikuUtils.getMetadataForLog(request))
        }
    }

    /**
     * Crawler informing us that SSL error found while browsing
     * @param {ElectronCertificateErrorMsgContent} request 
     */
    electronCertificateError(request) {
        // unknown scan id
        if (!this.runningScans[request.scanId]) {
            logger.log('info', `Unknown scan ID. Most likely cause is that network scanner was restarted in the middle of a crawl.`, HaikuUtils.getMetadataForLog(request))
            return
        }

        // if the crawl has already finished, don't accept this request - this could be 
        // because of timeout / config mismatch between crawler & scanner
        if (this.runningScans[request.scanId].crawlComplete || this.runningScans[request.scanId].scanComplete) {
            logger.log('info', 'Crawl/Scan is already complete', HaikuUtils.getMetadataForLog(request))
            return
        }

        // Send event that we got a SSL error
        this.emit('electron-cert-error', request);
    }

    /**
     * Pause scan for specific scan ID
     * @param {PauseScanMsgContent} request 
     */
    pauseScan(request) {
        // sanity check
        if (!this.runningScans[request.scanId]) {
            logger.log('info', `pauseScan(): Unknown scan ID. Most likely cause is that network scanner was restarted in the middle of a crawl.`, HaikuUtils.getMetadataForLog(request))
            return
        }

        logger.log('info', `Pausing scan`, HaikuUtils.getMetadataForLog(request))

        // stop the scan by forcing timeout, data will be saved automatically
        this.runningScans[request.scanId].expectedCrawlEnd = Date.now()
        this.runningScans[request.scanId].pauseScan = true;
    }

    /**
     * serialize running state ofa scan so it can be resumed later
     * @param {Number} scanId The scanid to serialize running state
     * @param {ScanData} scanData Data to serialize
     */
    async serializeRunningScan(scanId, scanData) {
        if (this.runningScans[scanId]) {
            logger.log('warn', `Most irregular, please investigate. Asked to serialize state for running scan ID. Going ahead with that.`, HaikuUtils.getMetadataForLog(scanData))
        }

        logger.log('info', `Saving scan info`, HaikuUtils.getMetadataForLog(scanData))

        // save running state sans 'private' settings
        let serializedScanInfo = JSON.stringify(scanData, (key, val) => {
            //Removed script object from store due to circular error
            if (val && val.haikuScanScriptHelper) {
                delete val.haikuScanScriptHelper;
            }

            return key == '_private_' ? undefined : val
        })

        // write the current state to the persistent storage
        logger.log('info', `saving scan info`, HaikuUtils.getMetadataForLog(scanData))
        await s3Utils.upload(this.pausedScansPrefix, `${scanId}.json`, serializedScanInfo)
        logger.log('info', `done saving scan info`, HaikuUtils.getMetadataForLog(scanData))

        //Upload alertdetails with alertmd5 values to be used in upcoming replay scans.
        try {
            let replayScanInfo = scanData.replayScanInfo;
            if(replayScanInfo && !replayScanInfo.isReplayScan) {
                if(scanData.alertDetails) {
                    logger.log('info', `saving alert details`, HaikuUtils.getMetadataForLog(scanData));
                    let serviceIdPrefix = scanData.serviceId + "/"
                    await s3Utils.upload(this.vulnsFoundPrefix + serviceIdPrefix, `alertDetails.json`, JSON.stringify(scanData.alertDetails));
                    logger.log('info', `done saving alert details`, HaikuUtils.getMetadataForLog(scanData));
                }
                else {
                    logger.log('error', `alertDetailsError, ${JSON.stringify(scanData.alertDetails)}`, HaikuUtils.getMetadataForLog(scanData));
                }
            }
        } catch (error) {
            logger.log('error', `Error uploading alertDetails, ${error.toString()}`, HaikuUtils.getMetadataForLog(scanData));
        }

        try {
            let replayScanInfo = scanData.replayScanInfo;

            if(replayScanInfo && replayScanInfo.isReplayScan) {
                if(scanData.alertmd5Payload) {
                    logger.log('info', `saving alertmd5Payload`, HaikuUtils.getMetadataForLog(scanData));
                    let serviceIdPrefix = scanData.serviceId + "/"
                    scanData.alertmd5PayloadFilePrefix = this.vulnsFoundPrefix + serviceIdPrefix + `alertmd5Payload.json`;
                    await s3Utils.upload(this.vulnsFoundPrefix + serviceIdPrefix, `alertmd5Payload.json`, JSON.stringify(scanData.alertmd5Payload));
                    logger.log('info', `done saving alertmd5Payload`, HaikuUtils.getMetadataForLog(scanData));
                }
            }
        } catch (error) {
            logger.log('error', `Error uploading alertmd5Payload, ${error.toString()}`, HaikuUtils.getMetadataForLog(scanData));
        }
    }

    /**
     * Resume scan for specific scan ID
     * @param {ResumeScanMsgContent} request 
     */
    async resumeScan(request) {
        logger.log('info', `Resuming scan`, HaikuUtils.getMetadataForLog(request))

        // deserialize the paused info
        let pausedScan = await this.getPausedScanInfo(request.scanId)

        // do the crawlstart type initialization
        if (!pausedScan) {
            // cannot proceed
            logger.log('warn', `resumeScan(): no paused info found -> cannot resume scan, falling back to a spanking new scan`, HaikuUtils.getMetadataForLog(this.runningScans[request.scanId]))
            this.runningScans[request.scanId].resumeSuccessful = false
            return request.scanLogId
        }

        let scanData = this.runningScans[request.scanId]
        scanData.resumeSuccessful = true

        // Most properties should be set anew. Just fix up the ones that need to come from the paused scan data
        // properties present from the beginning
        scanData.queuedRequests = pausedScan.queuedRequests
        scanData.uniqueCrawlerRequests = pausedScan.uniqueCrawlerRequests
        scanData.uniqueParams = pausedScan.uniqueParams
        scanData.stats = pausedScan.stats
        scanData.pluginData = pausedScan.pluginData
        scanData.metrics = pausedScan.metrics
        scanData.revalidateVulns = pausedScan.revalidateVulns
        scanData.revalidationsToProcess = pausedScan.revalidationsToProcess
        scanData.savedResponseCnt = pausedScan.savedResponseCnt
        scanData.attackedCrawlerRequests = pausedScan.attackedCrawlerRequests
        scanData.totalScanTimeMins = pausedScan.totalScanTimeMins;
        // added in v2
        scanData.processedRequests = pausedScan.processedRequests || []
        // When the scan is resumed, recover the previous scan's alertDetails for upcoming replay scans.
        scanData.alertDetails = pausedScan.alertDetails;
        scanData.alertmd5Payload = pausedScan.alertmd5Payload;

        //Re-Attack last processed request during pause scan, cause it might be partially attacked.
        //TO DO: Build staus of attacked plugin vs this last request. When resumed do not attack with same plugins to reduce duplicate attacks
        if (scanData.queuedRequests.length == 0 && scanData.processedRequests.length > 0) {
            try {
                let lastProcessedRequest = scanData.processedRequests[scanData.processedRequests.length - 1];
                let haikuKey = lastProcessedRequest.httpRequest.haikuKey;
                if (haikuKey) {
                    scanData.queuedRequests.push(lastProcessedRequest);
                    scanData.attackedCrawlerRequests = scanData.attackedCrawlerRequests.filter(attackedHaikuKey => {
                        return attackedHaikuKey != haikuKey
                    })
                    scanData.processedRequests.pop();
                }
            } catch (err) {
                logger.log('info', `Unable to pull last processed request during resume scan. Reason: ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
            }
        }

        // tell plugins to do any post resume fixups.
        this.emit('resumed-scan', request.scanId, request.pluginData)

        // we have resumed ...
        return request.scanLogId
    }

    /**
     * Instead of waiting for crawler to run and send network requests, simply attack the network
     * requests from previous crawl.
     * @param {crawlStartedMsgContent} request contents of the message (crawl started)
     * @returns {Promise<boolean>} true on success.  
     */
    async reusePreviousCrawlInfo(request) {
        logger.log('info', `reusing previous crawl info`, HaikuUtils.getMetadataForLog(request))

        // get the previous scan info
        let prevScanInfo = await ssAPI.getPreviousScanDetails(request.scanId, request.serviceId)
        if (!prevScanInfo || !prevScanInfo.scanId) {
            logger.log('error', `reusePreviousCrawlInfo() Could not get previous crawl info for serviceId ${request.serviceId}`, HaikuUtils.getMetadataForLog(request))
            return false
        }

        // deserialize the paused info
        let pausedScan = await this.getPausedScanInfo(prevScanInfo.scanId)
        if (!pausedScan) {
            // cannot proceed
            logger.log('warn', `reusePreviousCrawlInfo(): no paused info found -> cannot reuse previous crawl data, falling back to a full crawl`, HaikuUtils.getMetadataForLog(request))
            return false
        }

        // Set up the queued requests from paused data
        let scanData = this.runningScans[request.scanId]
        scanData.queuedRequests = [...scanData.queuedRequests, ...pausedScan.processedRequests, ...pausedScan.queuedRequests]

        return true
    }

    async getPausedScanInfo(pausedScanId) {
        let pausedScan;

        // get saved state
        try {
            let pausedScanContents;
            let resp = await s3Utils.getFile(this.pausedScansPrefix, `${pausedScanId}.json`);
            if (resp && resp.Body) {
                pausedScanContents = resp.Body;
            }

            if (pausedScanContents) {
                pausedScan = JSON.parse(pausedScanContents);
            }
        } catch (err) {
            logger.log('error', `error in getPausedScanInfo : ${err.toString()}`, HaikuUtils.getMetadataForLog(request));
        }
        return pausedScan;
    }

    loadPlugins() {
        // prevent node warning - set max listeners to number of plugins
        this.setMaxListeners(Object.keys(this.getDefaultConfig().Plugins).length)
        for (let pluginName of Object.keys(this.getDefaultConfig().Plugins)) {
            let plugin = this.getDefaultConfig().Plugins[pluginName]
            if (plugin.load) {
                if (this.activeScannerType.onlyLoadPluginsMatchingPattern && this.activeScannerType.loadPluginMatchingPattern) {
                    if (!this.activeScannerType.loadPluginMatchingPattern.test(plugin.file)) {
                        logger.log('info', `Skipping plugin ${plugin.file}, does not match ${this.activeScannerType.loadPluginMatchingPattern}`)
                        continue
                    }
                }
                let Plugin = require(path.join(__dirname, '../app/plugins/' + plugin.file))
                let plug = new Plugin(this, this.getDefaultConfig()) // plugin will use it's part of config

                // By the time we realized this needs to be told to the plugin, there was a large hierarchy and did 
                // not have time to update the constructor for all plugins. So set it here
                // *** This is a technical debt that we should fix. ***
                plug.pluginName = pluginName
                plug.pluginFile = plugin.file;
                plug.pluginIds = plugin.vulnerabilities ? Object.keys(plugin.vulnerabilities) : [];

                plug.init() // any post create initialization

                this.plugins.push(plug)
                logger.log('info', `Plugin loaded :  ${pluginName} (${plug.constructor.name})`)
            } else {
                logger.log('info', `Skippig Plugin : ${pluginName} load attribute is missing/false`)
            }
        }
    }

    /**
     * Get plugin by name 
     * @param {String} pluginName plugin name
     * @returns {any} plugin
     */
     getPluginByName(pluginName) {
        let pluginInstance = _.chain(this.plugins)
            .find(plugin => {
                return plugin.pluginName.toLowerCase() == pluginName.toLowerCase();
            })
            .value();
        return pluginInstance;
    }

    /** 
     * @param {vuln} vuln Information on vulnerablities found in one network request
     */
    updateFoundVulns(vuln) {
        if (!this.getScanInfo(vuln.scanId)) {
            logger.log('error', `updateFoundVulns() - Unknown scan ID ${vuln.scanId}. vuln=${JSON.stringify(vuln)}`, HaikuUtils.getMetadataForLog(vuln))
            return
        }

        let haikuKey = _.get(vuln, 'attack.originalRequest.httpRequest.haikuKey')
        if (!haikuKey) {
            logger.log('info', `updateFoundVulns() - no haiku key so ignoring vuln=${JSON.stringify(vuln)}`, HaikuUtils.getMetadataForLog(vuln))
            return
        }

        try {
            // See if we need to update this revaluidatio info. Dont consider ones found in this scan - original crawler 
            // request will be fired for all requests & so can be fired for multiple revalidation requests. we could end
            // up in a paradoxical situation where we flag vulns we find in this scan as missing in this scan.
            let revalidateVulns = this.getScanInfo(vuln.scanId).revalidateVulns

            // revalidation key also captures attacking plugin name, attack area, param
            let revalidationKey = haikuKey + '||n=' + vuln.attack.name + '||a=' + vuln.attack.area + '||p=' + vuln.attack.param
            // construct this attack specific key - plugin, area, param already covered in revalidation key, need method, vector, type and encoding
            let attackKey = `v=${vuln.attack.vector}||e=${vuln.attack.encoding}||t=${vuln.attack.type}`

            let foundVulns = Object.keys(vuln.vulns).map(key => key + '/' + (HaikuUtils.getVulnerabilityByVulnerabilityName(key) ? HaikuUtils.getVulnerabilityByVulnerabilityName(key).igwId : undefined));

            // get previous/existing successful attacks
            let successfulAttacks = _.get(revalidateVulns[revalidationKey], 'successfulAttacks', {})
            if (successfulAttacks[attackKey] && successfulAttacks[attackKey].foundVulns && successfulAttacks[attackKey].scanId == vuln.scanId) {
                // merge in the ones found in this attack. Happens for original crawler requests and passive vulns since 
                // cookies may be set in a future request. All this is because for revalidation we may send the same crawler
                // request more than once (for each active attack that finds vuln) and so will result in multiple original
                // crawler plugin request to be sent
                foundVulns.push(...successfulAttacks[attackKey].foundVulns)
            }

            let foundOn = (new Date).toISOString()
            // If we did not find a vulnerability, don't reset the ttl
            let scansToLive = _.get(revalidateVulns[revalidationKey], 'scansToLive', 1)
            if (foundVulns.length > 0) {
                scansToLive = this.getDefaultConfig().revalidateDecayInitialVal
            }

            // get just the relevant info from the vuln data
            let revalidateInfo = {
                scanId: vuln.scanId,
                scanlogId: vuln.scanlogId,
                scansToLive,
                foundOn,
                igKey: HaikuUtils.getIGWKey(vuln),
                attack: {
                    method: vuln.attack.method,
                    name: vuln.attack.name,
                    hostname: vuln.attack.hostname,
                    href: vuln.attack.href,
                    area: vuln.attack.area,
                    param: vuln.attack.param,
                },
                successfulAttacks,
                originalRequest: {
                    httpRequest: vuln.attack.originalRequest.httpRequest
                }
            }

            // check expected, found vulns and get missing vulns
            let expectedVulns = revalidateInfo.successfulAttacks[attackKey] ? revalidateInfo.successfulAttacks[attackKey].expectedVulns : []
            expectedVulns = expectedVulns.map((key) => {
                key = key.split('/')[0];
                let vulnId = HaikuUtils.getVulnerabilityByVulnerabilityName(key) ? HaikuUtils.getVulnerabilityByVulnerabilityName(key).igwId : undefined;
                return key + '/' + vulnId;
            });
            let missingVulns = _.difference(expectedVulns, foundVulns)

            let replayVulnsToSend = null;

            if(revalidateInfo.successfulAttacks && successfulAttacks[attackKey] && revalidateInfo.successfulAttacks && successfulAttacks[attackKey].replayVulnsToSend) {
                replayVulnsToSend = revalidateInfo.successfulAttacks[attackKey].replayVulnsToSend;
            }

            // add info about this successful attack
            revalidateInfo.successfulAttacks[attackKey] = {
                foundOn,
                scanId: vuln.scanId,
                scanlogId: vuln.scanlogId,
                type: vuln.attack.type,
                vector: vuln.attack.vector,
                encoding: vuln.attack.encoding,
                foundVulns,
                expectedVulns,
                missingVulns,
                httpRequest: vuln.attack.httpRequest,
                httpResponse: vuln.attack.httpResponse,
                replayVulns: vuln.vulns
            }

            if(replayVulnsToSend) {
                revalidateInfo.successfulAttacks[attackKey].replayVulnsToSend = replayVulnsToSend;
            }

            // make it easy for IG to get the found and missing vulns IDs
            revalidateInfo.successfulAttacks[attackKey].foundVulnsIgId = foundVulns.map(v => v.split('/')[1])
            revalidateInfo.successfulAttacks[attackKey].missingVulnsIgId = missingVulns ? missingVulns.map(v => v.split('/')[1]) : [];

            revalidateVulns[revalidationKey] = revalidateInfo
        } catch (err) {
            logger.log('error', `updateFoundVulns() - ${err.toString()}`, HaikuUtils.getMetadataForLog(vuln))
        }
    }

    /**
     * Send crawler requests from the queue if the currently pending http requests for this scan
     * has room to add more. The idea is to avoid queueing a lot of HTTP requests since attack plugins generate
     * multiple HTTP requests from one crawler captured request.
     */
    async processRequestsForScan(scanId) {
        if (this.runningScans[scanId].processingRequestQueue || this.runningScans[scanId].isInitializing) {
            return // already processing queue (maintenance thread/new request) or scan data not yet ready (middle of crawlstarted processing)
        }

        try {
            this.runningScans[scanId].processingRequestQueue = true

            // Keep sending requests while request monster has room for more http requests.
            let pendingHttpReqsForScan = this.httpReqMonster.getPendingRequests(scanId)
            while (this.getQueuedRequests(scanId) > 0 && pendingHttpReqsForScan < this.httpReqMonster.getMaxParallelRequests(scanId)) {
                // if the crawl has already finished, don't accept this request - this could be 
                // because of timeout / config mismatch between crawler & scanner
                if (this.runningScans[scanId].scanComplete) {
                    logger.log('info', `Scan is already complete - not processing ${this.runningScans[scanId].queuedRequests.length} requests`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                    break
                }

                // once we pick a request, we will attack entire redirect chain instead of sending the redirected requets 
                // as 'new attackable requests' to be added to the queue. That was an alternate way done earlier but is 
                // more confusing when checking what was attacked. It makes sense to completely finish attacks on the
                // crawler request picked.
                let potentialAttackableRequests = []

                // perform the crawler requst 
                let request = this.getNextRequestToProcess(scanId)

                if(!request) {
                    continue;
                }

                //Filter request in api scan
                if(_.isBoolean(request.httpRequest.canAttackInApiScan) && !request.httpRequest.canAttackInApiScan) {
                    continue;
                }

                //onNewAttackableRequest
                let onNewAttackableRequest = _.get(request, "httpRequest.annotations.haikuEvents.onNewAttackableRequest");

                if(onNewAttackableRequest) {
                    try {
                        onNewAttackableRequest(request.httpRequest, this.getHaikuScanScriptHelper(request.scanId));
                    }
                    catch(err) {
                        logger.log('info', `processRequestsForScan onNewAttackableRequest error ${err}`, HaikuUtils.getMetadataForLog(request));
                    }
                }

                // Don't accept requests if we are not allowed to attack the URI.
                if (!this.canAttackUrl(scanId, request.httpRequest.uri)) {
                    logger.log('info', `not attacking URL ${request.httpRequest.uri}`, HaikuUtils.getMetadataForLog(request))
                    continue
                }
                // get the refreshed crawler request by telling crawler to run this request again.
                if (!this.getConfig(scanId).ScannerSettings.skipRefreshRequestBeforeAttack) {
                    await this.refreshRequest(scanId, request)
                }

                let clonedRequest = _.cloneDeep(request)
                let isPostmanFile = this.runningScans[scanId].isPostmanFile;

                //File upload rquest boundary update
                try {
                    let isFileUploadRequest = FileUploadDelegate.isFileUploadRequest(request.httpRequest);

                    //Create request boundary for original request
                    if (isFileUploadRequest) {
                        // Generate a new boundary
                        let newBoundary = request.httpRequest.parsedData.boundary; //Use existing boundary

                        // Reassemble the multipart data with the new boundary
                        let body = '';

                        for (let [name, value] of Object.entries(request.httpRequest.parsedData.fields)) {
                            body += `--${newBoundary}\r\n`;
                            body += `Content-Disposition: form-data; name="${name}"\r\n\r\n`;
                            body += `${value}\r\n`;
                        }

                        for (let [name, files] of Object.entries(request.httpRequest.parsedData.files)) {
                            // Read the file from the given location
                            let fileContent = await s3Utils.getFile(path.dirname(files.filename), path.basename(files.filename));
                            body += `--${newBoundary}\r\n`;
                            body += `Content-Disposition: form-data; name="${name}"; filename="${path.basename(files.filename)}"\r\n`;
                            body += `Content-Type: ${files.contentType}\r\n\r\n`;
                            body += fileContent.Body;
                            body += `\r\n`;
                        }

                        body += `--${newBoundary}--\r\n`;
                        request.httpRequest.body = body;
                    }
                } catch (error) {
                    logger.log('info', `File upload request boundary failed: ${error.toString()}`, HaikuUtils.getMetadataForLog(request));
                }

                //Old haiku scan format - perform prerequest if it exist against request
                if(!isPostmanFile) {
                    //Perform prerequest if it exist against request 
                    let preRequest = this.performPreRequest(request);
                    let scriptVariables = this.runningScans[scanId].annotatedRequests ?  this.runningScans[scanId].annotatedRequests.scriptVariables : null;

                    if(preRequest) {
                        while(preRequest) {
                            HaikuUtils.updateScriptVariables(preRequest, scriptVariables);
                            await this.performCrawlerRequest(preRequest, false);
                            preRequest = this.performPreRequest(request);
                        }
                    }

                    if(this.runningScans[scanId].isApiScan) {
                        HaikuUtils.updateScriptVariables(request, scriptVariables);
                    }
                }
                else {
                    if(this.runningScans[scanId].isApiScan) {
                        let preRequests = _.get(request.httpRequest, "annotations.preq") || '';
                        let apiRequestTimeoutInSec = this.getConfig(scanId).ScannerSettings.apiRequestTimeoutInSec;

                        //Run if any pre requests & update variables in postman collections.
                        if(preRequests) {
                            preRequests = preRequests.split(',');

                            if(preRequests.length > 0) {
                                for(let requestIndex = 0; requestIndex < preRequests.length; requestIndex++) {
                                    let requestName = preRequests[requestIndex];
    
                                    if(requestName) {
                                        await HaikuUtils.runPostmanRequest(scanId, this.runningScans[scanId].postmanCollection, requestName, apiRequestTimeoutInSec, logger); 
                                    }
                                }
                            }
                        }

                        let haikuRequestFormat = await HaikuUtils.runPostmanRequest(scanId, this.runningScans[scanId].postmanCollection, request.httpRequest.name, apiRequestTimeoutInSec, logger);

                        if(haikuRequestFormat && haikuRequestFormat.haikuRequests) {
                            let updatedRequest = _.find(haikuRequestFormat.haikuRequests, (haikuRequest) => haikuRequest.name === request.httpRequest.name);

                            if(updatedRequest) {
                                request.httpRequest.uri = updatedRequest.uri;
                                request.httpRequest.headers = updatedRequest.headers;
                                request.httpRequest.body = updatedRequest.body ? updatedRequest.body.raw : null;
                            }
                        }
                    }
                }

                let res = await this.performCrawlerRequest(request)
                if (res) {
                    // save the processed request
                    let hostname = res.req.parsedURL.hostname;
                    fs.appendFile(`${this.receivedRequestsPath}/${hostname}-${request.scanlog_id}.json`, JSON.stringify(request), (err) => {
                        if (err) {
                            logger.log('info', `could not append request to file: ${err.toString()}`, HaikuUtils.getMetadataForLog(request));
                        }
                    })

                    // save the potential attackable request
                    potentialAttackableRequests.push(request)

                    // If we got redirected, attack the redirected pages as well.
                    if (request.httpResponse.redirectsFollowed) {
                        for (let redirectInfo of request.httpResponse.redirects) {
                            redirectInfo.redirectUri = new URL(redirectInfo.redirectedUri, this.runningScans[scanId].mainUrl).href

                            // Don't accept requests if we are not allowed to attack the URI.
                            if (!this.canAttackUrl(scanId, redirectInfo.redirectedUri)) {
                                logger.log('info', `not attacking redirected URL ${redirectInfo.redirectedUri}`, HaikuUtils.getMetadataForLog(request))
                                continue
                            }

                            // create attackable network request for the redirect & see if it is a potential attackable request
                            let redirectedRequest = this.getRedirectedRequest(clonedRequest, redirectInfo)
                            let redirectedRes = await this.performCrawlerRequest(redirectedRequest)
                            if (redirectedRes) {
                                potentialAttackableRequests.push(redirectedRequest)
                            }
                        }
                    }
                }


                // filter out the potentials...
                // if there are duplicate haiku keys eg. http -> https redirection, keep the last redirected request
                if (potentialAttackableRequests.length > 1) {
                    potentialAttackableRequests = potentialAttackableRequests.filter((val, idx, reqs) => {
                        return idx == _.findLastIndex(reqs, o => {
                            return o.httpRequest.haikuKey == val.httpRequest.haikuKey
                        })
                    })
                }

                // even if URI is different but we have the same method & exact same parameters, assume it's same 
                // request with url rewriting
                if (potentialAttackableRequests.length > 0) {
                    potentialAttackableRequests = potentialAttackableRequests.filter((val, idx, reqs) => {
                        if (val.haikuPriority.requestParams.length == 0) {
                            // no parameters, keep this request (unique because dup haiku keys have been filtered out above)
                            return true
                        }

                        return idx == _.findLastIndex(reqs, o => {
                            // check - same method
                            if (o.httpRequest.method != val.httpRequest.method) {
                                return false
                            }
                            // check - both have params and same number of params
                            if (o.haikuPriority.requestParams.length != val.haikuPriority.requestParams.length) {
                                return false
                            }
                            // check - same params
                            if (_.difference(o.haikuPriority.requestParams, val.haikuPriority.requestParams).length > 0) {
                                return false
                            }

                            return true
                        })
                    })
                }

                // remove the already attacked requests
                potentialAttackableRequests = potentialAttackableRequests.filter(val => {
                    return this.runningScans[scanId].attackedCrawlerRequests.indexOf(val.httpRequest.haikuKey) == -1
                })

                // start the attacks
                logger.log('info', `sending ${potentialAttackableRequests.length} attack requests`, HaikuUtils.getMetadataForLog(clonedRequest))
                for (let request of potentialAttackableRequests) {
                    logger.log('info', `attacking - ${request.httpRequest.haikuKey}`, HaikuUtils.getMetadataForLog(request))
                    if (!clonedRequest.revalidationInfo) {
                        this.runningScans[scanId].attackedCrawlerRequests.push(request.httpRequest.haikuKey)
                    }
                    this.emit('new-request', request)
                }

                // get updated pending requests since processing a request could have queued many
                // http requests
                pendingHttpReqsForScan = this.httpReqMonster.getPendingRequests(scanId)
            }
        } catch (err) {
            logger.log('processRequestsForScan error', err.toString(), HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
        }

        // reset flag
        this.runningScans[scanId].processingRequestQueue = false
    }


    /**
     * Refresh the crawler request if possible otherwise update the session info.
     * @param {Number} scanId the scan id with request to refresh
     * @param {Request} request crawler requst to refresh
     * @param {Number} maxWaitForSecs Wait for this much time for 'refresh' reply from crawler
     */
    async refreshRequest(scanId, request, maxWaitForSecs = 4 * 60) {
        // simple sanity check
        if (!this.runningScans[scanId]) {
            logger.log('warn', `Unknown scan id in refreshRequest(), ignoring...`, {
                scanId
            })
            return
        }

        // send teh  refresh request and wait for crawler to respond.
        let msgContent = {
            scanId,
            scanlog_id: request.scanlog_id,
            scanlogId: request.scanlog_id,
            scanner: 'haiku',
            crawlerBookmark: request.httpRequest.crawlerBookmark,
            wizardGuide: request.httpRequest.wizardGuide,
            appTranaKey: request.httpRequest.crawlerKey,
        }
        
        //During replay scan, persist X-IFC-REPLAY header for the request even its refresh with new headers   
        let replayHeader = null;
        let replayScanInfo = this.getConfig(scanId).ScannerSettings.replayScanInfo;

        if(replayScanInfo.isReplayScan) {
            replayHeader = _.get(request, 'httpRequest.headers.X-IFC-REPLAY')
        }

        let refreshCrawlerRequest = new RefreshCrawlerRequestRpc(msgContent)
        let refreshedRequest = await refreshCrawlerRequest.rpcRequest(this.msgQ, maxWaitForSecs * 1000)
        let didRefreshRequest = false

        if (refreshedRequest) {
            if (refreshedRequest.httpRequest) {
                // got a new refreshed request, best case scenario -> use this new request
                request.httpRequest = refreshedRequest.httpRequest
                this.annotateRequest(request)
                didRefreshRequest = true
            } else if (refreshedRequest.sessionInfo) {
                // update to latest session from crawler
                HaikuUtils.updateSession(request.httpRequest, refreshedRequest.sessionInfo)
                didRefreshRequest = true
            }
        }

        //if nothing else worked, at least update to the latest session that the scanner knows about
        if (!didRefreshRequest && this.runningScans[scanId].sessionInfo) {
            HaikuUtils.updateSession(request.httpRequest, this.runningScans[scanId].sessionInfo)
        }

        //Reassign X-IFC-REPLAY header during replay scan
        if(replayHeader) {
            request.httpRequest.headers['X-IFC-REPLAY'] = replayHeader;
        }

        return request
    }

    /**
     * Gets the currently queued requests for this scan incluing the revalidation & crawler request queue.
     * @param {Number} scanId Scan fro which info is being requested
     */
    getQueuedRequests(scanId) {
        let scanData = this.runningScans[scanId]
        let pendingRequests = 0
        if (this.getConfig(scanId).ScannerSettings.doRevalidate != 'never') {
            pendingRequests += scanData.revalidationsToProcess.length
        }
        if (this.getConfig(scanId).ScannerSettings.doRevalidate != 'only') {
            pendingRequests += this.runningScans[scanId].queuedRequests.length
        }

        return pendingRequests
    }

    /**
     * Get a attackable network request for a redirection
     * @param {request} originalRequest original request from crawler
     * @param {redirectInfo} redirectInfo uri (location header fqdn) and status code of the redirection
     */
    getRedirectedRequest(originalRequest, redirectInfo) {
        let redirectedRequest = _.cloneDeep(originalRequest)
        redirectedRequest.scanner = 'haiku-network-scanner'
        redirectedRequest.httpRequest.uri = redirectInfo.redirectUri // since we are not sending via rabbitmq msg, its uri not url

        // if its not the status codes that preserve method, change to GET
        if (redirectInfo.statusCode != 307 && redirectInfo.statusCode != 308 &&
            redirectedRequest.httpRequest.method != 'GET') {
            // change method to GET
            redirectedRequest.httpRequest.method = 'GET'
            // GET does not have body
            delete redirectedRequest.httpRequest.body;
            // delete the content-type & content-length headers for GET
            delete redirectedRequest.httpRequest.headers['Content-Type']
            delete redirectedRequest.httpRequest.headers['content-type']
            delete redirectedRequest.httpRequest.headers['Content-Length']
            delete redirectedRequest.httpRequest.headers['content-length']
        }
        // annotate request
        this.annotateRequest(redirectedRequest)
        return redirectedRequest
    }

    /**
     * Perform the pre request if it exist.
     * @param {request} request request for which pre request to be perform
     */
     performPreRequest(request) {
        try {
            let preq = _.get(request.httpRequest, "annotations.preq");

            if (preq) {
                let preRequests = preq.split(',');
                let previousRequestName = "";

                if (preRequests.length > 0) {
                    previousRequestName = preRequests.shift();
                }

                if (preRequests.length > 0) {
                    request.httpRequest.annotations.preq = _.join(preRequests, ',');
                }
                else {
                    request.httpRequest.annotations.preq = '';
                }

                if (previousRequestName) {
                    let preRequest = null;
                    let processedRequests = _.get(this.runningScans[request.scanId], "processedRequests");
                    let queuedRequests = _.get(this.runningScans[request.scanId], "queuedRequests");

                    if (processedRequests) {
                        preRequest = _.chain(processedRequests).find(processedRequest => {
                            return processedRequest.httpRequest.name.toLowerCase() == previousRequestName.toLowerCase()
                        }).value();
                    }

                    if (!preRequest && queuedRequests) {
                        preRequest = _.chain(queuedRequests).find(queuedRequest => {
                            return queuedRequest.httpRequest.name.toLowerCase() == previousRequestName.toLowerCase()
                        }).value();
                    }

                    if (preRequest) {
                        return preRequest;
                    }
                }
            }
        } catch (err) {
            logger.log('error', `performPreRequest() error: ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
        }

        return null;
    }

    /**
     * Perform the original crawler request and send response.
     * @param {request} request request to perform
     * @param {boolean}  storeResponseBody default to true for storing original request response. Cases where storing response is not required are API scan prerequests.
     */
    async performCrawlerRequest(request, storeResponseBody = true) {
        this.emit('new-crawler-request', request); // request.inProgessRequest is the HTTP request promise 
        let res;
        try {
            if (request.inProgessRequest) {
                let ret = await request.inProgessRequest
                if (ret) {
                    res = ret.res
                    if (res && storeResponseBody) {
                        // get response object as data that we really need. The actual response has a lot of info that we don't really 
                        // need to keep duping.
                        request.httpResponse = _.cloneDeep(_.get(res, 'resp.httpResponse', ''))

                        // write body to persistent storage and put link as body
                        let respType = 'original'
                        let storageKey = await this.storeResponseBody(request.scanId, respType, request.httpResponse.body)
                        if (storageKey) {
                            request.httpResponse.body = storageKey
                        }
                    }
                }
                delete request.inProgessRequest;
            }
        } catch (err) {
            logger.log('error', `performCrawlerRequest() error: ${err.toString()}`, HaikuUtils.getMetadataForLog(request))
        }
        return res;
    }

    /**
     * Store the http response body to persistent storage and return the prefix, name. This allows persisting
     * the body separately from request/response JSON used in APIs
     * This can also be used to reduce mem usage by replacing body with the prefix, name.
     * @returns JSON of prefix, name as string if successful, null otherwise 
     * @param {Number} scanId the scan (session) Id
     * @param {String} respType response type eg. original or attack
     * @param {String/Buffer} body HTTP Response body
     */
    async storeResponseBody(scanId, respType, body) {
        // running count of saved responses as unique id
        let savedResponseCnt = this.getScanInfo(scanId).savedResponseCnt++

        // save to S3. If not provided, do not save.
        let prefix = this.httpResponsePrefix + scanId
        let name = `${respType}-${savedResponseCnt}.body`
        logger.log('info', `offloading ${respType} response body: ${prefix}/${name}`, HaikuUtils.getMetadataForLog(this.getScanInfo(scanId)))
        if (body == null || body == undefined) {
            body = "EMPTY_RESPONSE"
        }
        await s3Utils.upload(prefix, name, body)


        return JSON.stringify({
            prefix,
            name
        })

        return null
    }

    /**
     * See if we are allowed to attack URL in the given scan
     * 
     * *** Technical debt ***
     * Very similar code in crawler (as allowedToCrawlUrl) & scanner, should refactor to common lib
     * *** Technical debt ***
     * 
     * @param {number} scanId scan id to get scan config from
     * @param {string} url url to check 
     */
    canAttackUrl(scanId, url) {
        let scanInfo = this.runningScans[scanId]
        if (!scanInfo) {
            logger.log('error', `skipping unknown scan id passed to canAttackUrl (url=${$url})`, {
                scanId
            })
            return false
        }

        // Check allowed domain and then site specifc crawl restrictions
        let okToCrawl = false

        try {
            let apiDiscovery = scanInfo.apiDiscovery;
            
            //Allow all urls in api discovery to be queued but will not be attacked. Refer site config 
            //for apiDiscovery flag & scannerSetting passed.
            //In order to store found urls into processedRequests and queuedRequests.
            //so it can be posted back to WAS portal at the end of scan
            if(apiDiscovery) {
                return true;
            }
            
            // We will only accept exact domain that has been registered. This means that the below will not be crawled
            // Registered xyz.com and link to blogs.xyz.com (no child domains)
            // Regsitered blogs.xyz.com and link to xyz.com (no parent domains)
            // Registered blogs.xyz.com and link to products.xyz.com (no sibling domains)
            // One exception is that we will allow both www.<domain> and <domain> when either
            // <domain> or <www.domain...> is registered
            if (HaikuUtils.canonacalizeHost(url) == HaikuUtils.canonacalizeHost(scanInfo.mainUrl)) {
                okToCrawl = true
            }

            // JIRA TAS-2219 - Allow sub domain. scan url = abc.indusface.com, allow http://abc.indusface.com , http://indusface.com , http://site1.abc.indusface.com , blogs.indusface.com etc.
            if(scanInfo.allowSubDomain) {
                okToCrawl = HaikuUtils.isSameDomain(url, scanInfo.mainUrl);
            }

            // check if we are restricting crawl to a sub path hierarchy
            if (okToCrawl && scanInfo.restrictCrawlToPath) {
                okToCrawl = url.includes(scanInfo.restrictCrawlToPath)
            }

            // also check if the URL is in excludeist
            if (okToCrawl && scanInfo.excludeUrls) {
                for (let exludedPath of scanInfo.excludeUrls) {
                    if (url.includes(exludedPath)) {
                        okToCrawl = false
                        break
                    }
                }
            }
        } catch (err) {
            logger.log('error', `canAttackUrl, returning false due to exception ${err}`, HaikuUtils.getMetadataForLog(scanInfo))
            okToCrawl = false
        }

        return okToCrawl
    }

    /**
     * Gets the next request to process in this priority order:
     *  Max # of unique params (params we have not seen before)
     *  Max # of params with values
     *  Max # of params
     *  in the order of what we received
     * @param {Number} scanId the scan for which we want this info
     */
    getNextRequestToProcess(scanId) {
        // simple sanity check
        if (!this.runningScans[scanId]) {
            return undefined
        }

        // pick the revalidation requests first if we need to
        if (this.getConfig(scanId).ScannerSettings.doRevalidate != 'never') {
            let scanData = this.runningScans[scanId]
            if (scanData.revalidationsToProcess.length) {
                let replayScanInfo = this.getConfig(scanId).ScannerSettings.replayScanInfo;
                let chmVulnsToReplay = replayScanInfo.chmVulnsToReplay;

                //Clone replay vulns during replay scan so scanner can duplicate similar attacks based on number of vulnerabilities 
                //present in that respective attack. 
                if (replayScanInfo.isReplayScan) {
                    for (let index = 0; index < scanData.revalidationsToProcess.length; index++) {
                        let revalidationKey = scanData.revalidationsToProcess[index];

                        scanData.revalidateVulns[revalidationKey];
                        let revalidationInfo = scanData.revalidateVulns[revalidationKey];

                        for(let attackKey in revalidationInfo.successfulAttacks) {
                            let attackInfo = revalidationInfo.successfulAttacks[attackKey];
        
                            if(attackInfo.replayVulns) {
                                // Check if replayVulnsToSend is empty, if yes then generate it from expectedVulns.
                                if(Object.keys(attackInfo.replayVulns).length == 0) {
                                    logger.log('info', `No replay vulns found for revalidationKey = ${revalidationKey} & attackKey = ${attackKey}, generating it from expectedVulns.`, HaikuUtils.getMetadataForLog({
                                        scanId: scanId
                                    }));

                                    _.forEach(attackInfo.expectedVulns, (expectedVuln) => {
                                        let vulnId = expectedVuln.split('/')[1];
                                        let vulnName = expectedVuln.split('/')[0];

                                        // check revalidationKey -> attackKey -> vulnName present in alertDetails so we can confirm if alertmd5 present.
                                        let alertDetails = scanData.alertDetails;

                                        if(alertDetails && alertDetails[revalidationKey] && alertDetails[revalidationKey][attackKey] && alertDetails[revalidationKey][attackKey][vulnName]) {
                                            let alertmd5 = alertDetails[revalidationKey][attackKey][vulnName].alertmd5;
                                            let uniqueId = alertDetails[revalidationKey][attackKey][vulnName].uniqueId;
                                            if(alertmd5) {
                                                let vulnObj = vulnId && vulnId.toLowerCase() != 'undefined' ? HaikuUtils.getVulnerabilityById(vulnId) : HaikuUtils.getVulnerabilityByVulnerabilityName(vulnName);
                                                
                                                if(vulnObj) {
                                                    attackInfo.replayVulns[vulnName] = {
                                                        alertmd5,
                                                        uniqueId,
                                                        vulnerabilityId: vulnObj.igwId,
                                                        vulnerabilityName: vulnName,
                                                        productionReady: vulnObj.productionReady,
                                                        foundBy: vulnObj.pluginName
                                                    }
                                                }
                                            }
                                        }
                                    });
                                }
                                
                                if(!attackInfo.replayVulnsToSend) {
                                    attackInfo.replayVulnsToSend = _.cloneDeep(attackInfo.replayVulns);
                                }
                            }
                        }
                    }
                }

                let revalidationKey = scanData.revalidationsToProcess.shift()
                let revalidationInfo = scanData.revalidateVulns[revalidationKey]

                // make sure this is in the format expected by the rest of the code.
                let candidate = {
                    revalidationInfo,
                    scanner: 'haiku-revalidation',
                    //During replay scan, scanner will send duplicate candidate for same attack request if more than one vulnerabilites present for 
                    //attack. This will preserve alertmd5 in header respective with vulenerability which received from WAS & can be replayed accordingly.
                    httpRequest: replayScanInfo.isReplayScan ? _.cloneDeep(revalidationInfo.originalRequest.httpRequest) : revalidationInfo.originalRequest.httpRequest
                }

                //If api scan, copy annotation from annotatedRequests.haikuRequests[] by api name
                if(scanData.isApiScan) {
                    let annotatedRequests = scanData.annotatedRequests;
                    let apiName = candidate.httpRequest.name;

                    if(apiName && annotatedRequests && annotatedRequests.haikuRequests) {
                        let apiRequest = _.find(annotatedRequests.haikuRequests, (haikuRequest) => haikuRequest.name === apiName);

                        if(apiRequest) {
                            candidate.httpRequest = _.merge(candidate.httpRequest, apiRequest);
                        }
                    }
                }

                // fix up scanId, scanlogId
                candidate.scanId = scanId
                revalidationInfo.originalRequest.httpRequest.scanId = scanId
                candidate.scanlog_id = candidate.scanlogId = scanData.scanLogId
                revalidationInfo.originalRequest.httpRequest.scanlog_id = scanData.scanLogId

                this.annotateRequest(candidate)

                logger.log('info', `picking candidate from revalidate list -> ${candidate.httpRequest.method} revalidationKey=${revalidationKey}`, HaikuUtils.getMetadataForLog(candidate));

                //Update respective alertmd5 for the request during replay scan
                if(replayScanInfo.isReplayScan) {
                    // Check if there are any vulns to replay for this request. Do not replay request because last regular scan it did not find any vulns for this request. refer foundVulns array.
                    if(_.isEmpty(revalidationInfo.successfulAttacks)) {
                        logger.log('info', `No replay vulns found for revalidationKey = ${revalidationKey}, skipping this request. Because last regular scan it did not find any vulns for this request.`, HaikuUtils.getMetadataForLog({ scanId: scanId }));
                        return undefined;
                    }

                    for(let attackKey in revalidationInfo.successfulAttacks) {
                        let attackInfo = revalidationInfo.successfulAttacks[attackKey];
    
                        if(attackInfo.replayVulns) {
                            let replayVulnKey = _.take(Object.keys(attackInfo.replayVulnsToSend), 1);
    
                            if(replayVulnKey.length) {
                                let vulnName = replayVulnKey[0];
                                let alertmd5 = attackInfo.replayVulnsToSend[replayVulnKey].alertmd5;
                                
                                //Do not replay vulnerabilities which have productionReady flag false, hence WAS has not provided alertmd5 for same.
                                if(!alertmd5) {
                                    let alertDetails = scanData.alertDetails;
                                    if(alertDetails && alertDetails[revalidationKey] && alertDetails[revalidationKey][attackKey]
                                        &&  alertDetails[revalidationKey][attackKey][replayVulnKey]) {
                                        alertmd5 = alertDetails[revalidationKey][attackKey][replayVulnKey].alertmd5;
                                    }
                                    else {
                                        return undefined;
                                    }
                                }

                                //Backward compatibility for replay scan, if chmVulnsToReplay is not present then replay all alertmd5.
                                let replayAlertmd5 = chmVulnsToReplay ? false : true;

                                //Check if alertmd5 is present in chmVulnsToReplay, if yes then replay it.
                                //Use to replay only critical, high & medium vulnerabilities.
                                if(chmVulnsToReplay && Object.keys(chmVulnsToReplay).length > 0) {
                                    replayAlertmd5 = chmVulnsToReplay[vulnName] ? true : false;
                                }
                                

                                replayVulnKey = replayVulnKey[0];
                                alertmd5 = `${replayScanInfo.replayScanId}_${alertmd5}`;

                                candidate.httpRequest.headers['X-IFC-REPLAY'] = alertmd5;
                                delete attackInfo.replayVulnsToSend[replayVulnKey];

                                let attackKeys = Object.keys(revalidationInfo.successfulAttacks);
                                
                                for (let index = 0; index < attackKeys.length; index++) {
                                    let extraAttackKey = attackKeys[index];
                                    
                                    if(revalidationInfo.successfulAttacks[extraAttackKey].replayVulnsToSend && 
                                        Object.keys(revalidationInfo.successfulAttacks[extraAttackKey].replayVulnsToSend).length > 0) {
                                            scanData.revalidationsToProcess.push(revalidationKey);
                                            scanData.revalidationsToProcess.sort();
                                            break;
                                    }
                                }

                                logger.log('info', `Replay vuln found for revalidationKey = ${revalidationKey}, attackKey = ${attackKey}, replayVulnKey = ${replayVulnKey}, alertmd5 = ${alertmd5}`, HaikuUtils.getMetadataForLog({ scanId: scanId }));

                                //Use to replay only critical, high & medium vulnerabilities.
                                if(!replayAlertmd5) {
                                    logger.log('info', `Replay vuln found for revalidationKey = ${revalidationKey}, attackKey = ${attackKey}, replayVulnKey = ${replayVulnKey}, alertmd5 = ${alertmd5}, but not present in alertmd5ToReplay, hence skipping this candidate.`, HaikuUtils.getMetadataForLog({ scanId: scanId }));
                                    return undefined;
                                }
                                
                                return candidate;
                            }
                        }
                    }

                    //If no replay vulns found for this request, add log and for tracking reason
                    logger.log('info', `No replay vulns found on successfulAttacks for revalidationKey = ${revalidationKey}, skipping this request.`, HaikuUtils.getMetadataForLog({ scanId: scanId }));
                    return undefined;
                }

                return candidate
            }
        }

        // pick from the queued requests
        // convenience var
        let queuedRequests = this.runningScans[scanId].queuedRequests
        // sanity check
        if (queuedRequests.length == 0) {
            return undefined
        }

        // loop and pick best candidate
        let candidateIdx = 0
        let candidate = queuedRequests[0]
        let candidateUniqueParams = _.difference(candidate.haikuPriority.requestParams, this.runningScans[scanId].uniqueParams)
        for (let i = 1; i < queuedRequests.length; i++) {
            let cur = queuedRequests[i]
            let curUniqueParams = _.difference(cur.haikuPriority.requestParams, this.runningScans[scanId].uniqueParams)

            // start the prioritization
            let gotNewCandidate = false

            // priority order (eg. manually added requests should be run first)
            // lower number is higher priority 
            //  >>> yeah, yeah, looks silly to check something that's just been set to false but helps
            //  >>> when moving blocks of prioritization around or adding new conditions
            if (!gotNewCandidate) {
                if (cur.haikuPriority.priority > candidate.haikuPriority.priority) {
                    continue
                }
                if (cur.haikuPriority.priority < candidate.haikuPriority.priority) {
                    gotNewCandidate = true
                }
            }

            // unique params first
            if (!gotNewCandidate) {
                if (curUniqueParams.length < candidateUniqueParams.length) {
                    continue
                }

                if (curUniqueParams.length > candidateUniqueParams.length) {
                    gotNewCandidate = true
                }
            }
            if (!gotNewCandidate) {
                // if it reached here, unique param count is the same
                // *  Max # of params with values
                if (cur.haikuPriority.nonEmptyValueCount < candidate.haikuPriority.nonEmptyValueCount) {
                    continue
                }

                if (cur.haikuPriority.nonEmptyValueCount > candidate.haikuPriority.nonEmptyValueCount) {
                    gotNewCandidate = true
                }
            }

            if (!gotNewCandidate) {
                // if it reached here, unique param count & params with values is the same
                // *  Max # of params
                if (cur.haikuPriority.requestParams.length < candidate.haikuPriority.requestParams.length) {
                    continue
                }
                if (cur.haikuPriority.requestParams.length > candidate.haikuPriority.requestParams.length) {
                    gotNewCandidate = true
                }
            }

            // we have a new candidate
            if (gotNewCandidate) {
                // we have a new candidate
                candidateIdx = i
                candidate = cur
                candidateUniqueParams = curUniqueParams
            }
        }

        // update the 'seen' unique params for scan
        this.runningScans[scanId].uniqueParams.push(...candidateUniqueParams)
        logger.log('info', `picking candidate at index ${candidateIdx} -> ${candidate.httpRequest.method}`, HaikuUtils.getMetadataForLog(candidate))

        let nextReqToProcess = queuedRequests.splice(candidateIdx, 1)[0]
        this.runningScans[scanId].processedRequests.push(nextReqToProcess)
        return nextReqToProcess
    }

    /**
     * Perform periodic maintenace like cleaning up scan related data after scan completes etc. 
     */
    maintenanceProc() {
        let start = new Date()
        logger.log('info', `------ Maintenance Proc start ${start}------`)
        let allRunningScans = Object.keys(this.runningScans)
        for (let scanId of allRunningScans) {
            try {
                if (this.runningScans[scanId].isInitializing) {
                    continue // data structure not reliable yet
                }

                // see if scan has timed out - we will finish at the same time as crawl
                // since that's the seting from customer
                if (Date.now() > this.runningScans[scanId].expectedCrawlEnd) {
                    // flag crawl & scan as complete => we will not accept more requests but allow
                    // in flight requests to complete
                    this.runningScans[scanId].scanComplete = true

                    if(this.runningScans[scanId].pauseScan) {
                        this.runningScans[scanId].scanComplete = true
                        logger.log('info', `Completing scan due to pause scan in maintenanceProc: ${scanId}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                    }

                    if (!this.runningScans[scanId].crawlComplete) {
                        logger.log('info', `Crawl timed out for scan ID: ${scanId}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                    }
                }

                // see if scan is complete - crawl complete & no in flight requests means we do not
                // have to wait for the scan to time out
                if (this.runningScans[scanId].crawlComplete) {
                    // see if all requests are completed and nothing queued.
                    // If time was up, scanComplete would already be set in above condition.
                    if (0 == (this.httpReqMonster.getPendingRequests(scanId) + this.getQueuedRequests(scanId))) {
                        logger.log('info', `Setting scanComplete=true, No pending requests for scan ID: ${scanId}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                        this.runningScans[scanId].scanComplete = true
                    } else {
                        logger.log('info', `Crawl complete for scan ID: ${scanId} but waiting for pending requests to finish`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                    }
                }

                // Process crawler requests queue for this scan. Since that method checks for scan complete
                // and has a loop, don't guard with thing like: if( crawlComplete/scanComplete)
                this.processRequestsForScan(scanId);

                // see if scan is done and all in flight requests are processed.
                if (this.runningScans[scanId].scanComplete) {
                    this.httpReqMonster.scanDone(scanId) // freeze queue for this scan ID

                    if(this.runningScans[scanId].pauseScan) {
                        logger.log('info', `setting force pause scan in httpReqMonster, scanId: ${scanId}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                        this.httpReqMonster.forcePauseScan(scanId) // set flag to clean up all queued requests.
                    }

                    if (!this.runningScans[scanId].maintenanceIterationsAfterScanComplete) {
                        this.runningScans[scanId].maintenanceIterationsAfterScanComplete = 0
                    }
                    this.runningScans[scanId].maintenanceIterationsAfterScanComplete++

                    logger.log('info', `Current maintenanceIterationsAfterScanComplete: ${this.runningScans[scanId].maintenanceIterationsAfterScanComplete}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))

                    // Can we process the scan complete event?
                    // 1. check that crawl complete (scan complete checked in outer if)
                    let processScanComplete = this.runningScans[scanId].crawlComplete
                    // 2. If there are pending network requests, wait some more, if it is pause scan force complete the scan so it can resume later by WAS portal
                    if (!this.runningScans[scanId].pauseScan && this.httpReqMonster.getPendingRequests(scanId) > 0) {
                        logger.log('info', `setting processScanComplete=false, pauseScan: ${this.runningScans[scanId].pauseScan}, pendingRequests: ${this.httpReqMonster.getPendingRequests(scanId)}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                        processScanComplete = false
                    }
                    // 3. If enough iterations have passed after scan complete, assume that crawler done & no real pending requests.
                    if (this.runningScans[scanId].maintenanceIterationsAfterScanComplete > this.getDefaultConfig().maxMaintenanceIterationsAfterScanComplete) {
                        logger.log('info', `setting processScanComplete=true, maintenanceIterationsAfterScanComplete: ${this.runningScans[scanId].maintenanceIterationsAfterScanComplete}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                        processScanComplete = true
                    }

                    // all in flight requests complete as well?
                    if (processScanComplete) {
                        // do scan complete processing
                        if (!this.runningScans[scanId].processingRequestQueue || this.runningScans[scanId].pauseScan) {
                            // need to guard with processingRequestQueue since that method does a bunch of work and scan could complete
                            // while it is still chugging away.
                            this.scanComplete(scanId)
                            // --- After this point this.runningScans[scanId] cannot be used ---

                            // send message to crawler that the scanner is done
                            let msgContent = {
                                scanId,
                                scanner: 'haiku'
                            }
                            let scanFinishedMsg = new ScanFinished(msgContent)
                            scanFinishedMsg.publish(this.msgQ)
                        }
                    }
                } else {
                    try {
                        logger.log('info',
                            `crawler/revalidation requests in queue ${this.getQueuedRequests(scanId)}, unique crawler requests: ${this.runningScans[scanId].uniqueCrawlerRequests.length}, responses being processed ${JSON.stringify(this.getScanInfo(scanId).responsesBeingProcessed)}`,
                            HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                        //logger.log('debug', `stats: ${JSON.stringify(this.runningScans[scanId].stats)}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                    } catch (err) {
                        logger.log('error', `---> look at this scanid : ${err.toString()}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                    }
                }
            } catch (err) {
                logger.log('error', `---> caught error in maintenance for loop: ${err.toString()}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
            }
        }

        // Print stats on requests
        this.httpReqMonster.printStats()

        this.logMemUsage()
        let end = new Date()
        let elapsedSeconds = (end - start) / 1000;
        logger.log('info', `------ Maintenance Proc end ${end}: total time (${elapsedSeconds}s)------`);
        logger.log('info', {
            message: 'ScanInfo',
            totalScans: allRunningScans ? allRunningScans.length : 0,
            ScanIds: allRunningScans
        });
    }

    //check for os memory usage and trigger heap dump if it is close to 7GB
    checkForHeapDump() {
        try {
            if(this.isHeapDumpWritten) {
                //Kill the timer which is checking for heap dump
                clearInterval(this.checkForHeapDumpIntervalId);
                return;
            }

            let totalMemoryGB = os.totalmem() / (1024 * 1024 * 1024);
            let freeMemoryGB = os.freemem() / (1024 * 1024 * 1024);
            let usedMemoryGB = totalMemoryGB - freeMemoryGB;
            let usedPercentage = (usedMemoryGB / totalMemoryGB) * 100;
            logger.log('info', `Total OS Memory: ${totalMemoryGB.toFixed(2)} GB, Free Memory: ${freeMemoryGB.toFixed(2)} GB, Used Memory: ${usedMemoryGB.toFixed(2)} GB, Used Percentage: ${usedPercentage.toFixed(2)}%`, {
                scanId: 0
            });

            if (usedMemoryGB >= 6.5) { // Trigger heap dump when system *physical* used memory is close to 7GB 
                if (!this.isHeapDumpWritten) {
                    this.isHeapDumpWritten = true;
                    logger.log('error', 'System physical memory usage approaching 7GB. Triggering heap snapshot...', {
                        scanId: 0
                    });
                    this.generateHeapSnapshot(`/mnt/haiku/temp/heapdump-heapsnapshots/${this.scannerIP}.heapsnapshot`); // Create unique filename, using scanner IP, keep only one copy for analysis
                }
            }
        } catch (err) {
            logger.log('error', `Unable to check for heap dump. Reason: ${err.toString()}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]));
        }
    }

    /**
     * log process.memoryUsage with properties converted to MB
     */
    logMemUsage() {
        let memInfo = `Free Mem = ${(osUtils.freememPercentage() * 100).toFixed(2)}%`
        let memUsage = process.memoryUsage();
        for (let key of ['rss', 'heapTotal', 'heapUsed', 'external']) {
            memUsage[key] /= (1024 * 1024.0);
            memInfo += `, ${key} = ${memUsage[key].toFixed(2)} MB`
        }
        logger.log('info', JSON.stringify(memInfo), {
            scanId: 0
        })
    }

    async saveScannerState() {
        try {
            for (let scanId of Object.keys(this.runningScans)) {
                let scanData = this.runningScans[scanId];   

                try {
                    if(scanData) {
                        if(!scanData.nextSnapshotTime) {
                            scanData.nextSnapshotTime = Date.now() + scanData.periodicSaveStateMins * 60 * 1000;
                            
                            if(!scanData.totalScanTimeMins) {
                                scanData.totalScanTimeMins = 0;
                            }
                        }
    
                        let currentTime = Date.now();
    
                        // Check if scan reach minimum periodicSaveStateMins
                        if (currentTime > scanData.nextSnapshotTime) {
                            let elapsedTimeInMins = parseFloat((((currentTime - scanData.nextSnapshotTime) / 1000) / 60).toFixed(2)) + scanData.periodicSaveStateMins;
                            scanData.nextSnapshotTime = currentTime + (scanData.periodicSaveStateMins * 60 * 1000);
                            //Keep track of total scan time elapsed whenever saveScannerState trigger
                            scanData.totalScanTimeMins = scanData.totalScanTimeMins +  elapsedTimeInMins;
                            await this.serializeRunningScan(scanId, scanData);
                        }
                    }

                } catch (err) {
                    logger.log('error', `Unable to save scanner state. Reason: ${err.toString()}`, HaikuUtils.getMetadataForLog(scanData));
                }
            }
        }
        catch(err) {
            logger.log('error', `Unable to save scanner state at global level. Reason: ${err.toString()}`);
        }
    }

    async scanComplete(scanId) {
        logger.log('info', `Scan complete for scan ID: ${scanId}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
        try {
            // Tell http requst monster to clean up     
            this.httpReqMonster.scanComplete(scanId)

            // save the scan info since we are about to tear down our structures 
            let scanData = this.runningScans[scanId];
            
            let isRevalidationPartial = false;
            
            if(scanData.revalidationsToProcess && scanData.revalidationsToProcess.length && scanData.revalidationsToProcess.length > 0) {
                isRevalidationPartial = true;
            }

            //scan stats
            let scanStats = this.getScanStats(scanData);
            let replayScanInfo = this.getConfig(scanId).ScannerSettings.replayScanInfo;
            scanData.replayScanInfo = _.cloneDeep(replayScanInfo);
            let doRevalidate = this.getConfig(scanId).ScannerSettings.doRevalidate
            delete this.runningScans[scanId];

            try {
                if (scanData.replayAttackInfo && Object.keys(scanData.replayAttackInfo).length > 0) {

                    // write the replay attack info for SaaSInfra block status check
                    logger.log('info', `saving replay attack info`, {
                        scanId
                    });
                    await s3Utils.upload(this.replayAttackInfoPrefix, `${scanId}.json`, JSON.stringify(scanData.replayAttackInfo));
                    logger.log('info', `done replay attack info`, {
                        scanId
                    });

                    delete scanData.replayAttackInfo;
                }
            } catch (error) {
                logger.log('error', `unable to upload replay attack info, reason: ${error.toString()}`, {
                    scanId
                });
            }

            // Save the scan info so that we can resume this scan later
            await this.serializeRunningScan(scanId, scanData);
            //Only write the vulnerabilities found to storage - S3/local file when it is not replay scan. 
            //Because in replay scan traffic sent over WAF to check protection status of vulnerability & it will block replay request
            //Hence revlidation file should not be updated at end of replay scan as it give false resolution of vulnerabilities to customer.
            if (Object.keys(scanData.revalidateVulns).length && !replayScanInfo.isReplayScan && !scanData.apiDiscovery) {
                // only update if we processed all the revalidate vulnerabilities.
                if (0 == scanData.revalidationsToProcess.length) {
                    let serviceIdPrefix = scanData.serviceId + "/"
                    await s3Utils.upload(this.vulnsFoundPrefix + serviceIdPrefix, 'revalidateVulns.json', JSON.stringify(scanData.revalidateVulns))
                }

                // tell WAS about all the missing vulns
                for (let revalidateKey of Object.keys(scanData.revalidateVulns)) {
                    // see if we have any missing vulns
                    let successfulAttacks = scanData.revalidateVulns[revalidateKey].successfulAttacks
                    if (successfulAttacks) {
                        for (let attackKey of Object.keys(successfulAttacks)) {
                            let missingVulnsIgId = successfulAttacks[attackKey].missingVulnsIgId
                            if (missingVulnsIgId && missingVulnsIgId.length > 0) {
                                // tell WAS about all these missing vuln IDs
                                for (let missingVuln of missingVulnsIgId) {
                                    // call API for each missing vulnerability
                                    let missingVulnInfo = {
                                        scanId: scanData.revalidateVulns[revalidateKey].scanId,
                                        scanlogId: scanData.revalidateVulns[revalidateKey].scanlogId,
                                        wasKey: scanData.revalidateVulns[revalidateKey].igKey,
                                        httpRequest: successfulAttacks[attackKey].httpRequest,
                                        httpResponse: successfulAttacks[attackKey].httpResponse,
                                        missingVulnWasId: missingVuln
                                    }

                                    ssAPI.vulnerabilityMissing(missingVulnInfo)
                                }
                            }
                        }
                    }
                }

            }

            let attackInfo = await this.getAttackInfo(scanData);

            // let SOC know that we are done.
            let scannerInfo = {
                scanner: 'haiku',
                scanId: scanData.scanId,
                scanLogId: scanData.scanLogId,
                mainUrl: scanData.mainUrl,
                resumeRequested: scanData.resumeScan,
                resumeSuccessful: scanData.resumeSuccessful,
                isPartial: scanData.queuedRequests.length > 0,
                metrics: scanStats,
                processedAttackInfo: attackInfo.processedAttackInfo || [],
                queuedAttackInfo: attackInfo.queuedAttackInfo || [],
                alertmd5PayloadFilePrefix: scanData.alertmd5PayloadFilePrefix,
            }

            let crawlerInfo = await this.getCrawlerInfo(scanId);
            let scanFeasible = crawlerInfo ? crawlerInfo.scanFeasible : scanData.scanFeasible;
            let scanFeasibleMsg = crawlerInfo ? crawlerInfo.scanFeasibilityMsg : scanData.scanFeasibleMsg;

            let haikuInfo = {
                scanFeasible: scanFeasible,
                scanFeasibleMsg: scanFeasibleMsg,
                scanFeasibleMsg: scanFeasibleMsg,
                scanner: scannerInfo,
                crawler: crawlerInfo || scanData.crawlerInfo
            }

            //Set force crawler completed in replay & revalidation scans
            // Set scanner isPartial flag to true if scan is partial due to revalidation or replay scan if 
            // revalidationsToProcess is not empty.
            if(replayScanInfo.isReplayScan || doRevalidate == 'only') {
                haikuInfo.scanner.isPartial = isRevalidationPartial;
                haikuInfo.crawler.isPartial = false;
            }

            //Set crawler isPartial flag to false in case of API scan, because we are skipping crawler in API scan.
            if(scanData.isApiScan) {
                haikuInfo.crawler.isPartial = false;
            }

            if (scanData.scanFeasible) {
                logger.log('info', `calling scanStopped: ${scanData.scanFeasible}`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                ssAPI.scanStopped(scanId, haikuInfo)
            } else {
                // isBlocked = 2 => Scan failed due to issue with initial scan, typically site down
                // blocked reason = 2 => blocked on connectria and AWS
                logger.log('info', `calling scanStopped: ${scanData.scanFeasible}, isBlocked: 2, blockedReason: 2`, HaikuUtils.getMetadataForLog(this.runningScans[scanId]))
                ssAPI.scanStopped(scanId, haikuInfo, /*isBlocked =*/ 2, /*blockedReason =*/ 2);
            }

            // write metrics to log as well.
            logger.log('info', 'SCANNERMETRICS : ' + JSON.stringify(scannerInfo.metrics), {
                scanId
            })

            // write the stats to local disk (helps in debugging)
            this.writeScanStats(scanId, scanStats)
        } catch (err) {
            //Log error and also put stack trace in log
            logger.log('error', `error in scanComplete: ${err.toString()}, stack: ${err.stack}`, {
                scanId
            })
        }
        logger.log('info', `all post scan complete operations done for scan`, {
            scanId
        });
    }

    /**
     * Write the scan stats to file.
     */
    writeScanStats(scanId, scanStats) {
        scanStats.scanId = scanId
        fs.appendFile(`${this.receivedRequestsPath}/stats-${scanId}.json`, `${JSON.stringify(scanStats)}`, (err) => {
            if (err) {
                logger.log('error', `could not append stats to file: ${err.toString()}`, {
                    scanId
                })
            }
        })
    }

    /**
     * 
     * @param {scanData} scanData The runtime Info about running scan
     * @param {*} scanId 
     */
    getScanStats(scanData) {
        let scanStats = {
            byCrawlerReq: {}
        };
        let totalHttpReqs = 0;
        for (let url of Object.keys(scanData.stats.byCrawlerReq)) {
            totalHttpReqs += scanData.stats.byCrawlerReq[url].numHttpRequests;
            if (scanData.stats.byCrawlerReq[url].numHttpRequests > 0) {
                scanStats.byCrawlerReq[url] = scanData.stats.byCrawlerReq[url];
            }
        }
        scanStats.byPlugin = scanData.stats.byPlugin;
        scanStats.byAttackArea = scanData.stats.byAttackArea;
        scanStats.pluginTimings = scanData.stats.pluginTimings
        scanStats.scanStats = {
            start: new Date(scanData.crawlStart),
            end: new Date(),
            totalHttpReqs,
            uniqueCrawlerRequests: scanData.uniqueCrawlerRequests.length,
            unprocessedRequests: scanData.queuedRequests.length,
            attackedCrawlerRequests: scanData.attackedCrawlerRequests.length,
        };
        scanStats.scanStats.timeMins = (scanStats.scanStats.end - scanStats.scanStats.start) / (60 * 1000);

        let currentTime = Date.now();
        let elapsedTimeInMins = parseFloat((((currentTime- scanData.nextSnapshotTime) / 1000) / 60).toFixed(2)) + scanData.periodicSaveStateMins;   
        scanData.totalScanTimeMins = parseFloat((scanData.totalScanTimeMins + elapsedTimeInMins).toFixed(2));
        return scanStats;
    }

    /**
     * @param {scanData} scanData The runtime Info about running scan
     * returns attack info regarding found urls 
     */
    getAttackInfo(scanData) {
        let processedRequestsInfo = [];
        let queuedRequestsInfo = [];
        let filterKeys = ['method', 'haikuKey', 'headers', 'uri', 'body', 'ip', 'SiteType', 'DataCenter', 'statusCode', 'responseHeaders'];

        try {
            if (scanData.processedRequests.length) {
                scanData.processedRequests.forEach(request => {
                    let temp = {};
                    if (request['httpRequest']) {
                        filterKeys.forEach(key => {
                            temp[key] = request['httpRequest'][key] || null
                        });
                    }

                    processedRequestsInfo.push(temp);
                });
            }

            if (scanData.queuedRequests.length) {
                scanData.queuedRequests.forEach(request => {
                    let temp = {};

                    if (request['httpRequest']) {
                        filterKeys.forEach(key => {
                            temp[key] = request['httpRequest'][key] || null
                        });
                    }
                    queuedRequestsInfo.push(temp)
                });
            }


        } catch (error) {
            logger.log('error', `error in method getAttackInfo ${error.toString()}`);
        }

        return { 'processedAttackInfo': processedRequestsInfo, 'queuedAttackInfo': queuedRequestsInfo };
    }

    async getCrawlerInfo(scanId) {
        try {
            let currentCrawlerState;
            let resp = await s3Utils.getFile(this.crawlerPauseNewPrefix, scanId + '.json');

            if (resp && resp.Body) {
                currentCrawlerState = resp.Body
            }

            if (currentCrawlerState) {
                return JSON.parse(currentCrawlerState)
            }
        } catch (error) {
            logger.log('error', `error in method getCrawlerInfo: ${error.toString()}`, {
                scanId
            });
        }
    }

    /**
     * Generate a heap snapshot of the current V8 heap
     * @param {string} filename - The filename to save the heap snapshot
     */
    async generateHeapSnapshot(filename) {
        let snapshot = v8.getHeapSnapshot();
        let fileStream = fs.createWriteStream(filename);

        snapshot.pipe(fileStream);

        fileStream.on('finish', () => {
            logger.log('info', `Heap snapshot saved to ${filename}`);
        });
    }
}


module.exports = NetworkScanner