const utils = require('../ifc-utils.js')
const RealClickAction = require( './real-click-action.js')
const JSClickAction = require( './javascript-click-action.js')
const XPathClickAction = require('./xpath-click-action.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// click action - try real click first, if that fails, try JS click
class ClickAction {
    constructor(xpath, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.annotation = annotation
    }

    get actionType() { return 'click' }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let actionSucceeded = await executer.triggerAction( new RealClickAction( this.xpath, this.annotation), executionContext )
        if ( !actionSucceeded ) {
            actionSucceeded = await executer.triggerAction( new JSClickAction( this.xpath, this.annotation), executionContext )
        }

        //Special case for customer https://mg-netremitv21-vapt-web.uat.macroglobal.io/, both real & js click doesn't work properly
        if(this.annotation == 'login-button') {
            actionSucceeded = await executer.triggerAction( new XPathClickAction( this.xpath, this.annotation), executionContext )
        }

        return actionSucceeded
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = ClickAction