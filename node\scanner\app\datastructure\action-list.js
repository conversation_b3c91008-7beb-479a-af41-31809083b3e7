const utils = require('../ifc-utils.js')
const _ = require('lodash')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// all the short circuit operations
const _shortCircuitEvals = {
    'or': (actionSucceeded) => {
            return actionSucceeded;
        } // short circuit boolean OR => 1st success - goodbye 
        ,
    'and': (actionSucceeded) => {
            return !actionSucceeded;
        } // short circuit boolean AND => 1st failure - goodbye 
        ,
    'seq':
        // do all controlled by ignoreFailures flag
        (actionSucceeded, executionContext) => {
            return !actionSucceeded && !executionContext.ignoreFailures;
        }
}

// composite action that runs a list of actions in sequence
class ActionList {
    constructor(name, actions = [], combinator = 'seq', mustRunAtInit = false) {
        this.setCombinator(combinator)
        this.setMustRunAtInit(mustRunAtInit)
        this.name = name
        this.actions = actions

        // see if we are are deserializing and if so, deserialize our array of actions
        if ( actions.length > 0 && utils.isSerializedObject(actions[0])) {
            this.actions = actions.map(utils.serializedDataToObject.bind(utils))
        }
    }

    get actionType() {
        return 'action-list'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.name, this.actions, this.combinator, this.mustRunAtInit]
        }
    }

    setCombinator(combinator) {
        if ( _shortCircuitEvals[combinator.toLowerCase()] ) {
            this.combinator = combinator.toLowerCase()
        } else {
            utils.log(`ActionList::setCombinator() -> ignoring unknown combinator: ${combinator}. defaulting to 'seq'`)
            this.combinator = 'seq'
        }
        this.shortCircuit = _shortCircuitEvals[this.combinator]
    }

    setMustRunAtInit(mustRunAtInit) {
        this.mustRunAtInit = mustRunAtInit
    }

    addAction(action) {
        if (action) {
            this.actions.push(action)
        }

        return this // for chaining
    }

    getActions() {
        return this.actions
    }

    getNumActions() {
        return this.actions.length
    }

    getXPaths() {
        let xpaths = []
        for (let action of this.actions) {
            if (action.getXPath) {
                xpaths.push(action.getXPath())
            } else if (action.getXPaths) {
                xpaths.push(...action.getXPaths())
            }
        }

        return xpaths
    }

    async execute(executionContext) {
        let executer = executionContext.executer

        let actionSucceeded = true
        for (let action of this.actions) {
            let actionSucceeded = await executer.triggerAction(action, executionContext)
            if (this.shortCircuit(actionSucceeded, executionContext)) {
                break
            }
        }
        return actionSucceeded
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return _.flatten(this.actions.map( a => a.flatten()))
    }

    toString() {
        let actionStr = ''
        let maxActions = 3
        for (let action of this.actions) {
            actionStr += action.actionType
            if (maxActions-- < 1) {
                actionStr += '... '
                break
            }
            actionStr += ' '
        }
        return `ACTION: ${this.actionType}-${this.combinator} ${this.name} mustrun:${this.mustRunAtInit} [${actionStr}]`
    }
}

module.exports = ActionList