const logger = require('../common/lib/haiku-logger')
const express = require('express')
const path = require("path")
const mkdirp = require('mkdirp')
const AdmZip = require('adm-zip')
const ModifyConfig = require('../utils/generate-haiku-site-config/generate-haiku-site-config')
const TestRegexGen = require('../utils/analyze-dup-urls.js')
const HaikuUtils = require('../common/lib/haiku-utils')
const fs = require('fs')
const _ = require('lodash')
const PostmanToHaiku = require('../utils/postman-to-haiku/postman-to-haiku')
const s3Utils = require('../common/lib/s3-utils')
const osUtils = require('os-utils')
const newman = require('newman');
const ssAPI = require('../common/lib/sooper-scheduler-api')
const hostFileQueue = require('../common/lib/host-file-queue')
let networkScannerConfig = require('../network-scanner/config/network-scanner-config');

/**
 * Haiku Utils API specific code 
 */
class HaikuUtilsAPI {
    /**
     * @constructor
     */
    constructor(logAndSendResponse) {
        this.httpResponsePrefix = 'scanner/httpResponse/'
        this.efsPrefix = '/mnt/haiku/'
        this.urlAnalysisPrefix = 'scanner/urlAnalysis/'
        this.archiveHttpResponsePrefix = this.efsPrefix + 'archives/' + this.httpResponsePrefix;
        mkdirp.sync(this.archiveHttpResponsePrefix); 
        this.logAndSendResponse = logAndSendResponse
        this.defaultNetworkScannerConfig = 'loading...'
        fs.readFile(path.resolve(__dirname, '../network-scanner/config/network-scanner-config.js'), (err, data) => {
            this.defaultNetworkScannerConfig = data ? data.toString() : `Default config API not available - ${err.toString()}`
        })
    }

    async getCpuUsage() {
        return new Promise((resolve) => {
            osUtils.cpuUsage(function (v) {
                resolve(v);
            });
        })
    }

    /**
     * initialize - add API handlers
     * @param {app} Express app to add the handlers
     */
    init(app) {
        // static handlers
        app.use('/utils', express.static(path.join(__dirname, '../utils')))
        app.use('/genSiteConfig', express.static(path.join(__dirname, '../utils/generate-haiku-site-config/gen-site-config.html')))

        // app handlers
        app.get('/utils/getDefaultConfig', this.getDefaultConfig.bind(this))
        app.post('/utils/generateSiteConfig', this.genSiteSpecificConfig.bind(this))
        app.post('/utils/regexFromUrls', this.regexFromUrls.bind(this))
        app.post('/utils/doesUrlMatchRegex', this.doesUrlMatchRegex.bind(this))
        app.post('/utils/uploadApiScanFile', this.processApiScanFile.bind(this))
        app.post('/utils/getScanApis', this.getScanApis.bind(this))
        app.post('/utils/getFileFromStorage', this.getFileFromStorage.bind(this))
        app.get('/utils/healthCheckAPI', this.healthCheckAPI.bind(this))
        app.post('/utils/analyzeUrls', this.analyzeUrls.bind(this))
        app.post('/utils/processApiScanFileViaNewman', this.processApiScanFileViaNewman.bind(this))
        app.get('/utils/getNetworkScannerConfig',  this.getNetworkScannerConfig.bind(this))
    }

    /**
     * Get the current default network scanner config
     * @param {request} req request object
     * @param {response} res response object
     */
    getNetworkScannerConfig(req, res) {
        this.logAndSendResponse(req, res, networkScannerConfig);
    }

    /**
     * Get the current default network scanner config
     * @param {request} req request object
     * @param {response} res response object
     */
    getDefaultConfig(req, res) {
        this.logAndSendResponse(req, res, this.defaultNetworkScannerConfig)
    }

    /**
     * Generate the site specific configuration from the recipe
     * @param {request} req request object, JSON body is recipe
     * @param {response} res response object
     */
    genSiteSpecificConfig(req, res) {
        let result = {
            success: true
        }
        try {
            let recipe = {}
            if (_.isObject(req.body.recipe)) {
                recipe = req.body.recipe
            } else if (_.isString(req.body.recipe)) {
                recipe = JSON.parse(req.body.recipe)
            }

            let success = true
            if (!_.isArray(recipe)) {
                result = {
                    success: false,
                    message: "recipe passed must be an array",
                    recipe
                }
            }
            if (success) {
                let modifyCfg = new ModifyConfig(recipe)
                modifyCfg.process()
                result = {
                    success: true,
                    siteConfig: JSON.parse(JSON.stringify(modifyCfg.getModifiedConfig(), HaikuUtils.configReplacer)),
                    diff: JSON.parse(JSON.stringify(modifyCfg.getDiff(), HaikuUtils.configReplacer)),
                }
            }
        } catch (err) {
            result = {
                success: false,
                message: err.toString(),
                recipe: req.body
            }
        }
        this.logAndSendResponse(req, res, result)
    }

    regexFromUrls(req, res) {
        let result = {
            success: true,
            canUrls: [],
            output: ""
        }
        try {
            let {
                dupUrls,
                nonDupUrls
            } = req.body && req.body.data && JSON.parse(req.body.data)
            let success = true
            if (!_.isArray(dupUrls)) {
                result = {
                    success: false,
                    canUrls: [],
                    output: "dupUrls passed must be an array",
                }
            }
            if (success) {
                // generate the regex
                let ret = TestRegexGen.regexFromUrls(dupUrls, nonDupUrls)
                Object.assign(result, ret)
            }
        } catch (err) {
            result = {
                success: false,
                output: err.toString(),
            }
        }
        this.logAndSendResponse(req, res, result)
    }

    doesUrlMatchRegex(req, res) {
        let result = {
            success: true,
            canUrls: [],
            matches: false
        }
        try {
            let {
                regex,
                url
            } = req.body && req.body.data && JSON.parse(req.body.data)
            // generate the regex
            let ret = TestRegexGen.doesUrlMatchRegex(regex, url)
            Object.assign(result, ret)
        } catch (err) {
            result = {
                success: false,
                output: err.toString(),
            }
        }
        this.logAndSendResponse(req, res, result)
    }

    /** */
    async processApiScanFile(req, res) {
        let result = {};

        try {
            let data = req.body.data;
            let inputFilePath = data.inputFilePath;
            let outputFilePath = data.outputFilePath;
            let postmanToHaiku = new PostmanToHaiku(inputFilePath, outputFilePath);
            let failedMesssage = await postmanToHaiku.generateAnnotatedFile();

            if (failedMesssage) {
                throw failedMesssage;
            }

            result = {
                success: true
            }
        }
        catch (err) {
            logger.log('error', `processApiScanFile error: ${err.toString()}`);

            result = {
                success: false,
                output: err.toString()
            }
        }

        this.logAndSendResponse(req, res, result);
    }

    async getScanApis(req, res) {
        let result = null;

        try {
            let data = req.body.data;
            let contents = await s3Utils.getFile(path.dirname(data.filePath), path.basename(data.filePath));
            contents = JSON.parse(contents.Body);
            let scriptVariables = contents.scriptVariables;

            contents = contents.haikuRequests.map((haikuRequest) => {
                return { 
                    name: haikuRequest.name, 
                    uri: haikuRequest.uri, 
                    method: haikuRequest.method
                }
            });

            result = {
                success: true,
                apis: contents,
                scriptVariables: scriptVariables
            }
        } catch (err) {
            logger.log('error', `getScanApis error: ${err.toString()}`);

            result = {
                success: false,
                output: err.toString(),
            }
        }

        this.logAndSendResponse(req, res, result);
    }

    /** */
    async getFileFromStorage(req, res) {
        let result = {};

        try {
            let data = req.body.data;
            let folderPath = data.folderPath;
            let fileName = data.fileName;

            try {
                let resp = {
                    Body: null
                };

                try {
                    resp = await s3Utils.getFile(folderPath, fileName);
                } catch (error) {
                }

                if (!resp && folderPath.includes(this.httpResponsePrefix)) {
                    let scanId = folderPath.split('/')[2];

                    //Check at if file contains at one level down the folder
                    resp = await s3Utils.getFile(folderPath + '/' + scanId, fileName);

                    if(!resp) {
                        if (!fs.existsSync(this.efsPrefix + this.httpResponsePrefix + scanId)) {
                            try {
                                resp = await s3Utils.getFile(this.httpResponsePrefix, scanId + '.zip');
    
                                if (resp && resp.Body) {
                                    fs.writeFileSync(this.archiveHttpResponsePrefix + scanId + '.zip', resp.Body);
                                    let zip = new AdmZip(this.archiveHttpResponsePrefix + scanId + '.zip');
                                    zip.extractAllTo(this.efsPrefix + this.httpResponsePrefix + scanId, true);
                                    fs.unlinkSync(this.archiveHttpResponsePrefix + scanId + '.zip');
    
                                    if (fs.existsSync(this.efsPrefix + this.httpResponsePrefix + scanId + '/' + fileName)) {
                                        let data = fs.readFileSync(this.efsPrefix + this.httpResponsePrefix + scanId + '/' + fileName);
                                        resp.Body = data;
                                    }
                                    else {
                                        //Check at if file contains at one level down the folder
                                        if (fs.existsSync(this.efsPrefix + this.httpResponsePrefix + scanId + '/' + scanId + '/' + fileName)) {
                                            let data = fs.readFileSync(this.efsPrefix + this.httpResponsePrefix + scanId + '/' + scanId + '/' + fileName);
                                            resp.Body = data;
                                        }
                                        else {
                                            resp = null;
                                        }
                                    }
                                }
                            } catch (error) {
                            }
                        }
                    }
                }

                result = {
                    success: resp ? true : false,
                    result: resp ? resp.Body : `Unable to read file content from location ${folderPath}/${fileName}`
                }
            } catch (error) {
                result = {
                    success: false,
                    result: `Unable to get file from storage, Reason: ${error.toString()}`
                }
            }
        }
        catch (err) {
            logger.log('error', `getFile error: ${err.toString()}`);

            result = {
                success: false,
                result: err.toString()
            }
        }

        this.logAndSendResponse(req, res, result);
    }

    /**
     * Performs a health check of the API.
     *
     * @param {Object} req - The request object.
     * @param {Object} res - The response object.
     * @returns {Promise} A promise that resolves to the health check result.
     */
    async healthCheckAPI(req, res) {
        let result = {};

        try {
            let cpuUsage = await this.getCpuUsage()

            result = {
                success: true,
                result: {
                    id: -101,
                    icpuusage: cpuUsage * 100,
                    dmemoryusage: osUtils.freememPercentage() * 100
                }
            }
        }
        catch (err) {
            logger.log('error', `healthCheckAPI error: ${err.toString()}`);

            result = {
                success: false,
                result: err.toString()
            }
        }

        this.logAndSendResponse(req, res, result);
    }

    /**
     * Analyze urls using Masswappalyzer
     * @param {Object} req - The request object.
     * @param {Object} res - The response object.
     * @returns {Object} result - The result of the analysis.
     */
    async analyzeUrls(req, res) {
        //Wappalyzer Command -> node wappalyzerSourcePath/src/drivers/npm/cli.js http://dvwascantest.testapptrana.net/dvwa/dvwa
        let result = {};

        try {
            let urlInfos = req.body.data;
            let wappalyzerSourcePath = req.body.wappalyzerSourcePath || '/mnt/haiku/wappalyzer';

            if(!urlInfos) {
                result = {
                    success: false,
                    result: 'No urls found to analyze'
                }
                this.logAndSendResponse(req, res, result);
                return;
            }

            urlInfos = await HaikuUtils.analyzeUrls(wappalyzerSourcePath, urlInfos);

            try {
                if(urlInfos) {
                    for (let index = 0; index < urlInfos.length; index++) {
                        let urlInfo = urlInfos[index];
                        let saveInStorage = urlInfo.saveInStorage;
                        let serviceId = urlInfo.serviceId;
    
                        if(serviceId && saveInStorage && urlInfo.result) {
                            try {
                                await s3Utils.upload(this.urlAnalysisPrefix + serviceId, `analysis.json`, JSON.stringify(urlInfo.result));
                            } catch (error) {
                                logger.log('error', `analyzeUrls upload error for serviceId: ${serviceId} url: ${urlInfo.url} error: ${error.toString()}`);
                            }
                        }
                    }
                }
                else {
                    result = {
                        success: false,
                        result: 'No urls found to analyze'
                    }
                    this.logAndSendResponse(req, res, result);
                    return;
                }
            } catch (error) {
                logger.log('error', `analyzeUrls upload error: ${error.toString()}`);
                result = {
                    success: false,
                    result: error.toString()
                }

                this.logAndSendResponse(req, res, result);
                return;
            }

            result = {
                success: true,
                result: urlInfos
            }
        }
        catch (err) {
            logger.log('error', `analyzeUrls error: ${err.toString()}`);

            result = {
                success: false,
                result: err.toString()
            }
        }

        this.logAndSendResponse(req, res, result);
    }

    /** 
     * Process API scan file via Numan
     * @param {Object} req - The request object.
     * @param {Object} res - The response object.
    */
    async processApiScanFileViaNewman(req, res) {
        let result = {};

        try {
            let data = req.body.data;
            let inputFilePath = data.inputFilePath;
            let outputFilePath = data.outputFilePath;
            let uploadId = data.uploadId;
            let scanUrl = data.scanUrl;
            let serviceId = data.serviceId;
            let skipUpdateHostFile = data.skipUpdateHostFile;
            let timeoutRequestInSeconds = data.timeoutRequestInSeconds  || 10; 
            let proxyPassDetails = null;

            let postManContent = await s3Utils.getFile(path.dirname(inputFilePath), path.basename(inputFilePath));
            let isValidPostmanFile =  HaikuUtils.isValidPostmanFile(postManContent.Body);

            if(!isValidPostmanFile) {
                logger.log('error', `Invalid postman collection file, please check the file and try again.`, {
                    uploadId: uploadId,
                    uri: scanUrl,
                    serviceId: serviceId
                });

                result = {
                    success: false,
                    uploadId: uploadId,
                    serviceId: serviceId,
                    message: `Invalid postman collection file, please check the file and try again.`,
                }

                this.logAndSendResponse(req, res, result);
                return;
            }

            let postmanCollection = JSON.parse(postManContent.Body);

            let scriptVariables = {};

            _.forEach(postmanCollection.variable, (variable) => {
                scriptVariables[variable.key] = variable.value;
            });

            let parsedUrl = null;

            try {
                parsedUrl = new URL(scanUrl); 
            } catch (error) {
                logger.log('error', `Invalid scan url ${scanUrl}`, {
                    uploadId: uploadId,
                    uri: scanUrl,
                    serviceId: serviceId
                });

                result = {
                    success: false,
                    uploadId: uploadId,
                    serviceId: serviceId,
                    message: `Invalid scan url ${scanUrl}`,
                }

                this.logAndSendResponse(req, res, result);
                return;
            }

            result = {
                success: true,
                uploadId: uploadId,
                serviceId: serviceId,
                message: 'Postman file is in progress to execute. Please check the status after some time.',
            }

            this.logAndSendResponse(req, res, result);
            req.sentResponse = true;

            // Update hosts file with the scanUrl
            try {
                proxyPassDetails = await HaikuUtils.getProxyPass(parsedUrl.hostname, {
                    uploadId: uploadId,
                    uri: scanUrl,
                    serviceId: serviceId
                }, logger);

                await hostFileQueue.updateHostFileWithQueue(parsedUrl.hostname, skipUpdateHostFile, {
                    uploadId: uploadId,
                    uri: scanUrl,
                    serviceId: serviceId
                }, proxyPassDetails, logger)
                logger.log('info', `proxyPassDetails: ${JSON.stringify(proxyPassDetails)}`, {
                    uploadId: uploadId,
                    uri: scanUrl,
                    serviceId: serviceId
                });
            } catch (err) {
                logger.log('error', `Could not update /etc/hosts continuing anyway: ${err.toString()}`, {
                    uploadId: uploadId,
                    uri: scanUrl,
                    serviceId: serviceId
                });
            }

            let apiEndpoints = {};
            let haikuRequestFormat = {
                name: '',
                description: '',
                scriptVariables: {
                },
                haikuRequests: []
            }
            
            try {
                let totalRequests = 0;
                let processedRequest = 0;

                newman.run({
                    collection: postmanCollection,
                    reporters: 'cli',
                    timeoutRequest: timeoutRequestInSeconds * 1000
                })
                .on('start', function (err, args) {
                    totalRequests = args.cursor.length;
                })
                .on('exception', function (err, args) {
                    if(err) {
                        logger.log('error', `runPostmanCollection uploadId: ${uploadId} exception: ${err.toString()}`, {
                            uploadId
                        });
                    }
                    else if(args) {
                        logger.log('error', `runPostmanCollection uploadId: ${uploadId} exception: ${args.error.message}`, {
                            uploadId
                        });
                    }
                })
                .on('beforeScript', function (err, args) {
                    let postScript = args.item.getEvents();

                    if (_.isArray(postScript) && !_.isEmpty(postScript) && postScript[0].listen == 'test' && postScript[0].script && postScript[0].script.exec) {
                        apiEndpoints[args.item.id].annotations = HaikuUtils.parseHaikuScript(postScript[0].script.exec);
                    }
                })
                .on('script', function (err, args) {
                })
                .on('beforeItem', function (err, args) {
                    let path = _.join(args.item.getPath().slice(1), '|'); //['CleverTap API Test', 'Report API Endpoints', 'Trends API', 'Partial API']

                    let headers = {};

                    _.forEach(args.item.request.headers.members, (header) => {
                        headers[header.key] = header.value;
                    });

                    apiEndpoints[args.item.id] = {
                        method: args.item.request.method,
                        name: path,
                        description: args.item.request.description,
                        baseUri: args.item.request.url.toString(),
                        headers: headers,
                        body: args.item.request.body ? args.item.request.body.raw : null,
                        uri: '',
                        annotations: '',
                        statusCode: 0,
                        requestError: ''
                    };
                })
                .on('beforeRequest', function (err, args) {
                    if (err) {
                        logger.log('error', `processApiScanFileViaNewman metaInfo: ${uploadId} beforeRequest error: ${err.toString()}`, {
                            uploadId
                        });
                    }
                    else {
                        let id = args.item.id;

                        if (apiEndpoints[id]) {
                            apiEndpoints[id].uri = args.request.url.toString();
                        }

                        if(apiEndpoints[id].uri && (HaikuUtils.canonacalizeHost(apiEndpoints[id].uri) != HaikuUtils.canonacalizeHost(scanUrl))) {
                            apiEndpoints[id].requestError = `Api endpoint is not part of the scan url ${scanUrl}`;
                        }
                    }
                })
                .on('request', async function (err, args) {
                    processedRequest++;
                    let id = '';

                    if (err) {
                        if (args) {
                            id = args.item.id;

                            if (apiEndpoints[id]) {
                                //if requestError is already set then don't overwrite it, i.e. if api endpoint is not part of the scan url
                                apiEndpoints[id].requestError = apiEndpoints[id].requestError ? apiEndpoints[id].requestError : err.message;
                            }
                        }

                        logger.log('error', `processApiScanFileViaNewman uploadId: ${uploadId} request error: ${err.toString()}`, {
                            uploadId
                        });
                    }
                    else {
                        id = args.item.id;

                        if (apiEndpoints[id]) {
                            let statusCode = args.response.code;
                            apiEndpoints[id].statusCode = statusCode;

                            if(_.isNumber(statusCode) && statusCode >= 400) {
                                apiEndpoints[id].requestError = apiEndpoints[id].requestError ? apiEndpoints[id].requestError : HaikuUtils.getErrorHTTPStatusDescription(statusCode);

                                try {
                                    apiEndpoints[id].response = args.response.stream.toString();
                                } catch (error) {
                                    logger.log('error', `processApiScanFileViaNewman uploadId: ${uploadId} request error: ${error.toString()}`);
                                }
                            }
                        }
                    }

                    if(id && apiEndpoints[id]) {
                        await ssAPI.updateWASPortalWithProcessedPostmanFile(uploadId, {
                            success: true,
                            haikuRequestFormat: {
                                name: postmanCollection.info.name,
                                description: postmanCollection.info.description,
                                haikuRequests: [
                                    {
                                        name: apiEndpoints[id].name,
                                        description: apiEndpoints[id].description,
                                        method: apiEndpoints[id].method,
                                        uri: apiEndpoints[id].uri,
                                        headers: apiEndpoints[id].headers,
                                        body: apiEndpoints[id].body,
                                        statusCode: apiEndpoints[id].statusCode,
                                        annotations: apiEndpoints[id].annotations,
                                        baseUri: apiEndpoints[id].baseUri,
                                        requestError: apiEndpoints[id].requestError
                                    }
                                ],
                                scriptVariables: scriptVariables
                            },
                            proxyPassDetails,
                            totalRequests: totalRequests,
                            processedRequest: processedRequest
                        }, {
                            uploadId: uploadId,
                            uri: scanUrl,
                            serviceId: serviceId
                        }, true);
                    }
                })
                .on('done', async function (err, summary) {
                    if (err) {
                        logger.log('error', `processApiScanFileViaNewman for uploadId: ${uploadId} error: ${err.toString()}`, {
                            uploadId
                        });

                        await ssAPI.updateWASPortalWithProcessedPostmanFile(uploadId, {
                            success: false,
                            error: `processApiScanFileViaNewman for uploadId ${uploadId} not processed.}`,
                        }, {
                            uploadId: uploadId,
                            uri: scanUrl,
                            serviceId: serviceId
                        });
                    }
                    else {
                        logger.log('info', `processApiScanFileViaNewman for uploadId: ${uploadId} completed`, {
                            uploadId
                        });

                        haikuRequestFormat.name = postmanCollection.info.name;
                        haikuRequestFormat.description = postmanCollection.info.description;

                        for (let key in apiEndpoints) {
                            let apiEndpoint = apiEndpoints[key];

                            if (scriptVariables) {
                                haikuRequestFormat.scriptVariables = scriptVariables;
                            }

                            haikuRequestFormat.haikuRequests.push({
                                name: apiEndpoint.name,
                                description: apiEndpoint.description,
                                method: apiEndpoint.method,
                                uri: apiEndpoint.uri,
                                headers: apiEndpoint.headers,
                                body: apiEndpoint.body,
                                statusCode: apiEndpoint.statusCode,
                                annotations: apiEndpoint.annotations,
                                baseUri: apiEndpoint.baseUri,
                                requestError: apiEndpoint.requestError
                            });
                        }

                        await s3Utils.upload(path.dirname(outputFilePath), path.basename(outputFilePath), JSON.stringify(postmanCollection));
                        await ssAPI.updateWASPortalWithProcessedPostmanFile(uploadId, {
                            success: !summary.error,
                            haikuRequestFormat,
                            proxyPassDetails,
                            error: summary.error,
                            totalRequests: totalRequests,
                            processedRequest: processedRequest
                        }, {
                            uploadId: uploadId,
                            uri: scanUrl,
                            serviceId: serviceId
                        });
                    }
                });
            }
            catch (error) {
                logger.log('error', `processApiScanFileViaNewman for uploadId: ${uploadId} error: ${error.toString()}`, {
                    uploadId
                });
            }
        }
        catch (err) {
            logger.log('error', `processApiScanFile error: ${err.toString()}`, {
                uploadId
            });

            result = {
                success: false,
                output: err.toString()
            }

            if(!req.sentResponse) {
                this.logAndSendResponse(req, res, result);
            }
        }
    }
}

module.exports = HaikuUtilsAPI