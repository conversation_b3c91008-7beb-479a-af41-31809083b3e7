const { ResourceLoader } = require('jsdom');
const { CookieJar } = require('tough-cookie');
const _ = require('lodash');

class CustomResourceLoader extends ResourceLoader {
    constructor(attack, cookieJar) {
        super();
        this.attacks = {};  // Store attack instances per hostname
        this.activeRequests = {};
        this.count = {};
        this.cookieJars = {};  // Use an object to store multiple cookie jars by hostname
        const attackHostname = attack.hostname;
        this.attacks[attackHostname] = attack;
        this.cookieJars[attackHostname] = cookieJar || new CookieJar();  // Use provided cookieJar or create new
    }

    async fetch(url, options) {
        console.error = console.warn = console.log = () => { };
        const urlObj = new URL(url);
        const attackHostname = Object.keys(this.attacks).find(key => urlObj.hostname.includes(key));

        if (!attackHostname) {
            return Buffer.from('');  // No matching attack instance
        }

        this.initializeRequestState(attackHostname);

        // Ensure separate cookieJar for each attack
        options = { ...options, cookieJar: this.cookieJars[attackHostname] };

        const attackInstance = this.attacks[attackHostname];
        const transformedHeaders = this.transformHeaders(attackInstance.httpRequest.headers);

        let response = null;
        let additionalFetchSent = false;  // Flag to ensure only one additional fetch without cookies

        try {
            if (this.isAllowedDomain(urlObj.hostname, attackHostname)) {
                options.headers = { ...transformedHeaders };
                const redirectedUri = _.get(this.attacks[attackHostname], 'result.resp.httpResponse.redirects[0].redirectedUri', false);

                if (attackInstance.httpRequest.method === 'GET' || redirectedUri) {
                    response = await this.handleGetRequest(url, attackHostname, options);
                } else if (attackInstance.httpRequest.method === 'POST' && !redirectedUri) {
                    response = await this.handlePostRequest(url, options, attackInstance.originalRequest.httpRequest.body, attackHostname);
                }
                // Send additional fetch without cookies once, before handling normal requests
                if (!additionalFetchSent) {
                    response = await this.sendFetchWithoutCookies(urlObj.origin, options);  // Send fetch without cookies
                    additionalFetchSent = true;  // Ensure it's only sent once
                }
            }
        } catch (error) {
            response = Buffer.from('');
        } finally {
            console.error = console.warn = console.log = () => { };
            this.cleanup(attackHostname);
        }

        return response || Buffer.from('');
    }

    initializeRequestState(attackHostname) {
        if (!this.activeRequests[attackHostname]) this.activeRequests[attackHostname] = 0;
        if (!this.count[attackHostname]) this.count[attackHostname] = 0;
        this.activeRequests[attackHostname]++;
    }

    transformHeaders(originalHeaders) {
        return Object.fromEntries(
            Object.entries(originalHeaders).map(([key, value]) => [key.toLowerCase(), value])
        );
    }

    async safeFetch(fetchFunction) {
        try {
            return await fetchFunction();
        } catch (error) {
            return Buffer.from('');
        }
    }

    isAllowedDomain(hostname, allowedDomain) {
        return hostname === allowedDomain || hostname.endsWith(`.${allowedDomain.split('.').slice(-2).join('.')}`);
    }

    // This is the method that sends a fetch without cookies, ensuring it's only done once.
    async sendFetchWithoutCookies(url, options) {
        // Create a temporary options object without the cookieJar
        delete options.headers.cookie
        // Send the fetch request without cookies
        return this.safeFetch(() => super.fetch(url, options));
        // await super.fetch(url, optionsWithoutCookies);

    }

    async handleGetRequest(url, attackHostname, options) {
        if (this.count[attackHostname] < 6) {
            this.count[attackHostname]++;
            return this.safeFetch(() => super.fetch(url, options));
        }

        const redirectedUri = _.get(this.attacks[attackHostname], 'result.resp.httpResponse.redirects[0].redirectedUri', '');
        const setCookieHeader = _.get(this.attacks[attackHostname], 'result.resp.httpResponse.redirects[0].headers["set-cookie"]', '');

        if (redirectedUri && !setCookieHeader) {
            return this.safeFetch(() => super.fetch(redirectedUri, options));
        }

        return this.safeFetch(() => super.fetch(new URL(url).origin, options));
    }

    async handlePostRequest(url, options, originalRequestBody, attackHostname) {
        options = { ...options, body: originalRequestBody, method: 'POST' };

        if (this.count[attackHostname] < 6) {
            this.count[attackHostname]++;
            return this.safeFetch(() => super.fetch(url, options));
        }

        return this.safeFetch(() => super.fetch(new URL(url).origin, options));
    }

    cleanup(attackHostname) {
        if (--this.activeRequests[attackHostname] === 0) {
            delete this.activeRequests[attackHostname];
            delete this.count[attackHostname];
            delete this.attacks[attackHostname];
            setTimeout(() => {
                delete this.cookieJars[attackHostname];  // Delay deletion to prevent conflicts
            }, 500);
        }
    }
}

module.exports = CustomResourceLoader;