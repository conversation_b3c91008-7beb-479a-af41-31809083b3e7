const RpcQueueMessage = require('../rpc-queue-message')
const path = require('path')
const debug = require('debug')('Messages:GetSitemapUrlsRpc')

/** 
 * RPC message  crawler <-> scanner to get sitemap Urls. All it does is define the routing key & the msgType
 * @extends QueueMessage
*/
class GetSitemapUrlsRpc extends RpcQueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} getSiteMapMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request 
     * @property {string} mainUrl Full URI that is the root of the scan/crawl
     * @property {Number} maxSitemapLinks Maximum links to return
     * @property {string} scannerMachineUID Unique ID of the machine that sent this request
     */
    /**
     * @param {getSiteMapMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner';
        this.routingKey = 'lb.request.rpc.get-sitemap-url';
        this.msgType = GetSitemapUrlsRpc.msgType;

        // if we have a machineUID, we can send a direct message to that machine. i.e. crawler > scanner
        if(content && content.scannerMachineUID) {
            this.routingKey = `direct.message.${content.scannerMachineUID}.rpc.get-sitemap-url`
        }
    }
}

module.exports = GetSitemapUrlsRpc