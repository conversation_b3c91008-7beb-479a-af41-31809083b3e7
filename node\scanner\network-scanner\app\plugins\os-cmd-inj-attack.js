const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class OSCommandInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-os-cmd-injection'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(osCmdInjMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of SQL Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return osCmdInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-permutation', 'form-encoded-post', 'json-body']
    }

    // event handler, annotates attack parameter, no return value
    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return
        }
        this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
    }
}

// vectors & matches ...
const osCmdInjVectors = [
    `| cat /etc/passwd `, // Blocked under file injection attempt rule id 100
    `&& cat /etc/passwd `, // Blocked under file injection attempt rule id 100
    `&& dir `,// Blocked under command injection rule id 432
    `| /bin/cat /etc/passwd`, // Blocked under file injection attempt rule id 100
    `\`set|set&set\` `,
    `set|set&set `,
    `set|set&set`,
    `&& type %SYSTEMROOT%\win.ini `,
    `&& run type %SYSTEMROOT%\win.ini `,
    

    // Disabling below vectors as we observed FP cases : Alert ID - *********
    //` | echo "Haiku-205484ea-99a2-481a-8b2e-01eb43dce185" | base64 `,
    //` && echo "Haiku-205484ea-99a2-481a-8b2e-01eb43dce185" | base64 `,
]

const osCmdInjMatch = [
    /IkhhaWt1LTIwNTQ4NGVhLTk5YTItNDgxYS04YjJlLTAxZWI0M2RjZTE4NSIgDQo=/, // echo haikuText with quote
    /SGFpa3UtMjA1NDg0ZWEtOTlhMi00ODFhLThiMmUtMDFlYjQzZGNlMTg1/, // echo haikuText without quote
    /root:x:0:0:/,
    /daemon:x:1:1:/,
    /:\/bin\/bash\//,
    /:\/bin\/sh/,
    /root:!:x:0:0:/,
    /daemon:!:x:1:1:/,
    /:usr\/bin\/ksh /,
    / \[boot loader\]/,
    /default=multi\(/,
    /  \[operating systems\]/,
    / \[fonts\]/,
    /Volume in drive/,
    /^USER=/,
    / ^USERNAME=/,
    / SystemDrive=/,
    / SystemRoot=/,
    /Usage: ping/
]
module.exports = OSCommandInjAttack