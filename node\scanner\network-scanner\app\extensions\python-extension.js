const debug = require('debug')('PythonExtension')
const PythonShell = require('python-shell');
const path = require('path')
const domain = require('domain')
const _ = require('lodash')
const logger = require('../../../common/lib/haiku-logger')

// set up domain - this is the only way to catch the error emited from the stream
// when, due to error in a python script, the script terminates before we send data on stdin
// Please note that this module is deprecated and is waiting replacement (which may take years)
// https://nodejs.org/api/domain.html
let domainPython = domain.create();
domainPython.on('error', (e) => {
    logger.log('error', 'Python domain caught error:', e.toString())
    // let the 'end' callback resolve the promise
});

/**
 * Run external python script. Communication is JSON using stdin/stdout
 * Instance will be created in network scanner.
 */
class PythonExtension {
    /**
     * @param {config} config config object
     */
    constructor(config) {
        this.options = _.get(config, 'Extensions.python') || {}

        let pythonPath = process.env.HAIKU_PYTHON_PATH
        if (pythonPath) {
            // env var always wins over config setting.
            _.set(this.options, 'pyshellOptions.pythonPath', pythonPath)
        } else if (config.ScannerType.active.pythonPath) {
            // if we are using config, see if there is anything specific to the current scanner type.
            _.set(this.options, 'pyshellOptions.pythonPath', config.ScannerType.active.pythonPath)
        }

        this.options.pyshellOptions.scriptPath = this.options.pyshellOptions.scriptPath || path.join(__dirname, 'python')
        logger.log('info', `Python extension started with options: ${JSON.stringify(this.options)}`)
    }

    /**
     * Execute a python extension that takes no args, reads JSON from stdin
     * and writes output to stdout in JSON format
     * @param {string} script name of script to run
     * @param {object} inputJson parms to script (will be sent in stdin)
     * @param {number} maxTimeSecs timeout
     */
    executeCommand(script, inputJson, maxTimeSecs) {
        maxTimeSecs = maxTimeSecs || this.options.maxTimeForScriptSecs
        return new Promise((resolve, reject) => {
            let response = {
                raw: ''
            }

            try {
                logger.log('info', `executing python script ${script} with inputJson ${JSON.stringify(inputJson).substr(0,256)}`)
                let pyshell = new PythonShell(script, this.options.pyshellOptions)

                // respect the max time
                let timerId = setTimeout(() => {
                    pyshell.terminate()
                }, maxTimeSecs * 1000);

                // response processing - concatanate lines of text sent from the Python script (print/write to stdout)
                pyshell.on('message', message => response.raw += message)

                // wrap and send the JSON params
                domainPython.run(() => pyshell.send(JSON.stringify(inputJson)))

                // end the input stream and allow the process to exit
                pyshell.end((err, code, signal) => {
                    try {
                        clearTimeout(timerId) // process terminated, cancel out timeout

                        if (err) {
                            throw err
                        }

                        // parse the response into JSON
                        response.json = JSON.parse(response.raw)
                        resolve(response)
                    } catch (e) {
                        logger.log('error', `caught error in end method ${e.toString()}`)
                        response.err = e
                        resolve(response)
                    }
                })
            } catch (e) {
                logger.log('error', `caught error in execCommand ${e.toString()}`)
                response.err = e
                resolve(response)
            }
        })
    }
}

module.exports = PythonExtension