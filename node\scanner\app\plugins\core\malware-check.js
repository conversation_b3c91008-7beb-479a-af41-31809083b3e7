let utils = require('../../ifc-utils.js')
const DateDiff = require('date-diff')
const URL = require('url').URL
const querystring = require('querystring');
const pluginName = 'malwareCheck'
const {
    session
} = require('electron'); // ask karth<PERSON> for what this means
const fs = require('fs');
const HaikuUtils = require('../../../common/lib/haiku-utils');
const ssAPI = require('../../../common/lib/sooper-scheduler-api');
const defaultConfig = require('../../malware-scan-config');
const _ = require('lodash')


class MalwareCheck {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config
        let host = this.config.parsedUrl.hostname
        this.externalUrls = [];
        this.iframes = {};
        this.suspiciousIframe = [];

        // init counters and register for scan/crawl events
        // scan start
        this.scanStartTime = undefined
        //scanner.on('scan-start', this.onScanStart.bind(this))

        // got interesting items
        this.pagesCrawled = new Set()
        scanner.on('got-interesting-items', this.onGotInterestingItems.bind(this));

        // network request captured
        this.attackableNetworkRequests = 0
        // scanner.on('network-action', this.onNetworkAction.bind(this))

        //
        scanner.on('scan-loop-done', this.onScanDone.bind(this))

        // serialize/deserialize
        scanner.on('serialize-state', this.serializeState.bind(this))
        scanner.on('deserialize-state', this.deserializeState.bind(this))

    }

    onGotInterestingItems(_, interestingItems, crawlState) {

        //collect urls and avoid duplication
        interestingItems.linkItems.forEach(element => {
            this.checkExternal(element, crawlState, interestingItems.location)
        });

        //collect iframes and avoid duplication
        if (interestingItems.iframeItems.length > 0) {
            interestingItems.iframeItems.forEach(element => {
                this.findAndReportSuspiciousIframe(element, interestingItems.location);
            });
        }
    }

    //
    async checkExternal(element, crawlState, location) {

        let url = element[1].href;
        let isExteralUrl = await this.isExternal(url, this.config.mainUrl);
        let vulnConfig = {
            url: url,
            location: location
        };

        if (isExteralUrl && !this.externalUrls.includes(url)) {
            this.externalUrls.push(url);   //avoid duplicate check

            vulnConfig = HaikuUtils.hasExtension(url, 'js') ? defaultConfig.pluginData.malwarecheck.vuln['externalJS'] : vulnConfig = defaultConfig.pluginData.malwarecheck.vuln['externalUrl'];
            vulnConfig = { ...vulnConfig, ...{ 'url': url, 'location': location } };

            let urlObj = {
                'url': url,
                'location': location,
                "meta": element[1],
                'vulnId': vulnConfig['vulnid'],
                'msg': HaikuUtils.getMalwareMsg('ID-external-resource', vulnConfig),
                'isJS': HaikuUtils.hasExtension(url, 'js')
            };

            let request = {
                "scanid": this.config.scanId,
                "scanlogid": this.config.scanLogId,
                "key": this.config.mainUrl,
                "founddate": new Date().toISOString(),
                "scanner": "haiku",
                "vulns": {
                    "ID-external-resource": {
                        "foundby": this.constructor.name,
                        "productionReady": vulnConfig.productionReady,
                        "vulnerabilityid": urlObj.vulnId,
                        "details": {
                            "externalResource": url,
                            "foundIn": location,
                            "isJavascript": urlObj['isJS'],
                            'result': urlObj.msg,
                            "crawldetails": urlObj
                        }
                    }
                }
            };

            // inform WAS for new malware vulnerability
            let res = await ssAPI.malwareVulnerabilityFound(request);
        }
    }

    isExternal(url, mainUrl) {
        var r = new RegExp('^(?:[a-z]+:)?//', 'i');  // check relative path only
        if (!r.test(url))
            return false;
        return HaikuUtils.canonacalizeHost(url) !== HaikuUtils.canonacalizeHost(mainUrl);
    }

    async findAndReportSuspiciousIframe(element, location) {
      
        if(!this.iframes.hasOwnProperty(element[0]))
            this.iframes[element[0]] = element[1];
         
        for (let key in this.iframes) {
            if (this.iframes.hasOwnProperty(key) &&  !this.suspiciousIframe[key]) {
               
                let allAttr = {};
                 _.forEach(this.iframes[key].allAttrs, o => {
                    allAttr[o.name] = o.value
                });
                
                if ((this.iframes[key].rect.width == 0 && this.iframes[key].rect.height == 0) ||
                (allAttr['width'] == 0 && allAttr['height'] == 0)) {
                    this.suspiciousIframe[key] = this.iframes[key];

                    let vulnConfig = defaultConfig.pluginData.malwarecheck.vuln['suspiciousIframe'];
                    vulnConfig = { ...vulnConfig, ...{ 'location': location } };

                    let urlObj = {
                        'location': location,
                        "meta": element[1],
                        'vulnId': vulnConfig['vulnid'],
                        'msg': HaikuUtils.getMalwareMsg('ID-suspicious-iframe', vulnConfig),
                    };

                    let request = {
                        "scanid": this.config.scanId,
                        "scanlogid": this.config.scanLogId,
                        "key": this.config.mainUrl,
                        "founddate": new Date().toISOString(),
                        "scanner": "haiku",
                        "vulns": {
                            "ID-suspicious-iframe": {
                                "foundby": this.constructor.name,
                                "productionReady": vulnConfig.productionReady,
                                "vulnerabilityid": urlObj.vulnId,
                                "details": {
                                    "suspiciousIframe": JSON.stringify(this.suspiciousIframe[key]),
                                    "foundIn": location,
                                    'result': urlObj.msg,
                                    "crawldetails": urlObj
                                }
                            }
                        }
                    };

                    // inform WAS for new malware vulnerability
                    let res = await ssAPI.malwareVulnerabilityFound(request);
                }
            }
        }       
    }

    onScanDone(reason, ret, logger = console) {
        
    }

    /**
     * return object that can be JSON.stringified and stored
     * @param {Object} pluginData Object where we can add our serialized state under our own key
     */
     serializeState(pluginData) {
        pluginData[pluginName] = {
            //counts
            externalUrls: this.externalUrls,
            iframes : this.iframes,
            suspiciousIframe : this.suspiciousIframe
        }
    }

    /**
     * Restore plugin data that from serialized object
     * @param {Object} pluginData Object with our serialized state under our own key
     */
    deserializeState(pluginData) {
        if (!pluginData[pluginName]) {
            return
        }

        this.externalUrls =  pluginData[pluginName].externalUrls || []
        this.iframes =  pluginData[pluginName].iframes || {}
        this.suspiciousIframe =  pluginData[pluginName].suspiciousIframe || []
    }
}

module.exports = MalwareCheck