const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class HTTPRequestSmuggling extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-http-request-smuggling'
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */

    async processAttackResponse(attack) {
        let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')

        if (attack.attackArea != "original-crawler-request") {
            return
        }

        if (pluginStorage.smuggleVulnFound) {
            return
        }
        try {
            let parsedUrl = new URL(attack.httpRequest.uri)
            if (parsedUrl.hostname) {
                // Input json data for python script
                let jsonData = {
                    "protocol": parsedUrl.protocol,
                    "hostName": parsedUrl.hostname,
                    "port": (parsedUrl.protocol == 'https:') ? 443 : 80
                }
                let output = await this.networkScanner.pythonExtension.executeCommand('httpRequestSmugglingDetection.py', jsonData)
                let vulnObj = output.json;
                if (_.isObject(vulnObj)) {
                    // checking if the smuggle request is successful then it checks output json and decides the vulnerability
                    if (vulnObj.attackVector && vulnObj.attackRequest) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnObj)
                        pluginStorage.smuggleVulnFound = true
                    }
                }
            }
        } catch (e) { return }
    }
}

module.exports = HTTPRequestSmuggling