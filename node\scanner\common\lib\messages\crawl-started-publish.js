const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:CrawlStartedPublish')

/** 
 * crawler -> scanner indicating crawl has started
 * @extends QueueMessage
*/
class CrawlStartedPublish extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} crawlStartedPublishMsgContent
     * @property {Number} scanId Scan ID (session ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} scanner scanner/crawler that sent this request (haiku)
     * @property {string} mainUrl Full URI that is the root of the scan/crawl
     * @property {string} crawlerIP Private IP address of crawler which sent this message
     */
    /**
     * @param {crawlStartedPublishMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'scanner'
        this.routingKey = 'request.crawl-started-publish'
        this.msgType = CrawlStartedPublish.msgType
    }
}

module.exports = CrawlStartedPublish