const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const { spawn } = require('child_process');
const dns = require('dns')

class tlsvuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Vulnerability IDs
        this.sessionresumptionID = 'ID-session-resumption'
        this.dnssecnotsignedID = 'ID-dnssec-not-signed'
        // Client-side Renegotiation Supported
        this.clientSideRenegotiationID = 'ID-client-side-renegotiation'
        // Server-side Renegotiation Supported
        this.serverSideRenegotiationID = 'ID-server-side-renegotiation'
        // Self-signed certificate
        this.selfSignedCertID = 'ID-self-signed-certificate'
        this.commandTimeout = 30000; // 30 seconds timeout for commands
    }

    // Main entry point for TLS vulnerability checks
    async processAttackResponse(attack) {
        if (!attack || typeof attack !== 'object') {
            return;
        }

        if (attack.attackArea !== 'original-crawler-request' || attack.pluginName !== 'Original Crawler Request') {
            return;
        }

        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        if (!pluginStorageScanScope || pluginStorageScanScope.tlsvulnchecked) {
            return;
        }

        if (!attack.hostname || !attack.href) {
            return;
        }

        let parsedUrl;
        try {
            parsedUrl = new URL(attack.href);
        } catch (error) {
            return;
        }

        if (parsedUrl.protocol !== 'https:') {
            return;
        }

        try {
            let host = attack.hostname;
            let gethost = await this.dnsresolve(host);
            if (!gethost) {
                return;
            }

            // Get IP address once and reuse
            // Skip IPv6
            const ipAddress = await this.getipaddress(gethost);
            if (!ipAddress || ipAddress === 'not found' || ipAddress.includes(':')) {  // Skip IPv6
                pluginStorageScanScope.tlsvulnchecked = true;
                return;
            }

            // Run single OpenSSL command for all TLS checks // Send 'Q' as input
            const tlsResult = await this.executeFile('openssl', [
                's_client',
                '-connect', `${ipAddress}:443`,
                '-servername', host,
                '-tls1_2',
                '-reconnect'
            ], 'Q');

            if (!tlsResult || !tlsResult.trim() || tlsResult.includes('output: Command not executed') || (!tlsResult.includes('Reused,') && !tlsResult.includes('Secure Renegotiation'))) {
                pluginStorageScanScope.tlsvulnchecked = true;
                return;
            }

            // Filter the output for relevant information
            const seenPatterns = new Set();
            const relevantLines = tlsResult.split('\n').filter(line => {
                // Early exit if we've found all patterns
                if (seenPatterns.size === 14) return false;

                // Skip if line is empty
                if (!line.trim()) return false;

                // Check for each pattern only once
                if (line.includes('New,') && !seenPatterns.has('New')) {
                    seenPatterns.add('New');
                    return true;
                }
                if (line.includes('Reused,') && !seenPatterns.has('Reused')) {
                    seenPatterns.add('Reused');
                    return true;
                }
                if (line.includes('Secure Renegotiation') && !seenPatterns.has('Secure Renegotiation')) {
                    seenPatterns.add('Secure Renegotiation');
                    return true;
                }
                if (line.match(/Cipher\s*:/) && !seenPatterns.has('Cipher')) {
                    seenPatterns.add('Cipher');
                    return true;
                }
                if (line.match(/Protocol\s*:/) && !seenPatterns.has('Protocol')) {
                    seenPatterns.add('Protocol');
                    return true;
                }
                if (line.includes('SSL handshake') && !seenPatterns.has('SSL handshake')) {
                    seenPatterns.add('SSL handshake');
                    return true;
                }
                if (line.match(/Verification( error)?:/) && !seenPatterns.has('Verification')) {
                    seenPatterns.add('Verification');
                    return true;
                }
                if (line.includes('Verify return code:') && !seenPatterns.has('Verify return code')) {
                    seenPatterns.add('Verify return code');
                    return true;
                }
                if (line.includes('Session-ID:') && !seenPatterns.has('Session-ID')) {
                    seenPatterns.add('Session-ID');
                    return true;
                }
                if (line.includes('Session-ID-ctx:') && !seenPatterns.has('Session-ID-ctx')) {
                    seenPatterns.add('Session-ID-ctx');
                    return true;
                }
                if (line.includes('Master-Key:') && !seenPatterns.has('Master-Key')) {
                    seenPatterns.add('Master-Key');
                    return true;
                }
                if (line.match(/Timeout\s*:/) && !seenPatterns.has('Timeout')) {
                    seenPatterns.add('Timeout');
                    return true;
                }
                if (line.includes('subject=')) {
                    seenPatterns.add('subject');
                    return true;
                }
                if (line.includes('issuer=')) {
                    seenPatterns.add('issuer');
                    return true;
                }
                return false;
            }).join('\n');

            if (!relevantLines) {
                pluginStorageScanScope.tlsvulnchecked = true;
                return;
            }

            try {
                if (relevantLines.includes('Secure Renegotiation IS supported')) {
                    // Check for renegotiation support
                    const renegotiationResult = await this.checkRenegotiationFromOutput(relevantLines, ipAddress);
                    if (renegotiationResult && !renegotiationResult.error) {
                        if (renegotiationResult.clientSide?.supported && renegotiationResult.clientSide?.protocol?.includes('TLSv1.2')) {
                            this.reportClientSideRenegotiation(attack, renegotiationResult);
                        }
                        if (renegotiationResult.serverSide?.supported && renegotiationResult.serverSide?.protocol?.includes('TLSv1.2')) {
                            this.reportServerSideRenegotiation(attack, renegotiationResult);
                        }
                        if (renegotiationResult.selfSignedCert && renegotiationResult.selfSignedCert.ipAddress) {
                            this.reportSelfSignedCertificate(attack, renegotiationResult.selfSignedCert);
                        }
                    }
                }
                if (relevantLines.includes('Reused,')) {
                    // Check for session resumption
                    await this.checkSessionResumptionFromOutput(attack, relevantLines, ipAddress);
                }

                // Check for DNSSEC
                await this.checkDNSSEC(attack, gethost, host);

            } catch (error) {
                pluginStorageScanScope.tlsvulnchecked = true;
            }
        } catch (error) {
            pluginStorageScanScope.tlsvulnchecked = true;
        }
    }

    getipaddress(host) {
        return new Promise((resolve) => {
            const args = ['+short', host];
            const dig = spawn('dig', args);
            let output = '';

            const timeout = setTimeout(() => {
                dig.kill('SIGTERM');
                resolve('not found');
            }, 10000); // 10 second timeout is enough for dig

            dig.stdout.on('data', (data) => {
                output += data.toString();
            });

            dig.on('close', () => {
                clearTimeout(timeout);
                const ips = output.trim().split('\n');
                // Find first IPv4 address
                const ipv4 = ips.find(ip => /^\d+\.\d+\.\d+\.\d+$/.test(ip));
                resolve(ipv4 || 'not found');
            });

            dig.on('error', () => {
                clearTimeout(timeout);
                resolve('not found');
            });
        });
    }

    // DNS resolution with CDN check
    async dnsresolve(host) {
        try {
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    resolve(host);
                }, this.commandTimeout);

                dns.resolveCname(host, (err, addresses) => {
                    clearTimeout(timeout);
                    if (err) {
                        resolve(host);
                    }

                    if (addresses && addresses.some(addr => /\.induscdn\.com/i.test(addr))) {
                        resolve(`proxypass.${host}.indusguard.com`);
                    } else {
                        resolve(host);
                    }
                });
            });
        }
        catch (error) {
            return null;
        }
    }

    // Check for TLS renegotiation vulnerabilities
    async checkRenegotiationFromOutput(output, ipAddress) {
        const result = {
            clientSide: { supported: false },
            serverSide: { supported: false },
            ipAddress: ipAddress
        };

        try {
            const lines = output.split('\n');
            const state = {
                hasInitialConnection: false,
                hasReconnections: false,
                hasSecureRenegotiation: false,
                hasValidHandshake: false,
                hasValidVerification: false,
                protocol: 'Unknown',
                cipher: 'Unknown',
                certificate: 'Unknown',
                handshakeBytes: { read: 0, written: 0 },
                hasSelfSignedCert: false
            };

            for (const line of lines) {
                if (line.includes('SSL handshake has read') && line.includes('bytes and written')) {
                    const bytesMatch = line.match(/read (\d+) bytes and written (\d+) bytes/);
                    if (bytesMatch) {
                        state.handshakeBytes = {
                            read: parseInt(bytesMatch[1], 10),
                            written: parseInt(bytesMatch[2], 10)
                        };
                        state.hasValidHandshake = state.handshakeBytes.read > 0 && state.handshakeBytes.written > 0;
                    }
                }
                if (line.includes('Verification: OK') && line.includes('Verify return code: 0')) {
                    state.hasValidVerification = true;
                } else if (line.includes('Verify return code: 19') || (line.includes('Verification error:') && line.includes('self signed certificate in certificate chain'))) {
                    state.hasSelfSignedCert = true;
                }
                if (line.match(/Protocol\s*:/)) {
                    const protocolMatch = line.match(/Protocol\s*:\s*([^\n]+)/i);
                    state.protocol = protocolMatch?.[1]?.trim() || 'Unknown';
                }
                if (line.match(/Cipher\s*:/)) {
                    const cipherMatch = line.match(/Cipher\s*:\s*([^\n]+)/i);
                    state.cipher = cipherMatch?.[1]?.trim() || 'Unknown';
                }
                if (line.includes('subject=')) {
                    const subject = line.split('subject=')[1].trim().replace(/\r$/, '');
                    const subjectParts = subject.split(',');
                    const formattedSubject = subjectParts.map(part => {
                        const [key, value] = part.split('=');
                        return `${key}: ${value}`;
                    }).join('\n');
                    state.certificate = `Subject:\n${formattedSubject}`;
                }
                if (line.includes('issuer=')) {
                    const issuer = line.split('issuer=')[1].trim().replace(/\r$/, '');
                    const issuerParts = issuer.split(',');
                    const formattedIssuer = issuerParts.map(part => {
                        const [key, value] = part.split('=');
                        return `${key}: ${value}`;
                    }).join('\n');
                    state.certificate = state.certificate ?
                        `${state.certificate}\nIssuer:\n${formattedIssuer}` :
                        `Issuer:\n${formattedIssuer}`;
                }
            }

            // Set connection states based on relevantLines content
            state.hasInitialConnection = output.includes('New,');
            state.hasReconnections = output.includes('Reused,');
            state.hasSecureRenegotiation = output.includes('Secure Renegotiation IS supported');

            // Validate TLS version and cipher
            const isValidTLSVersion = state.protocol.includes('TLSv1.2') || state.protocol.includes('TLSv1.3');
            const isValidCipher = state.cipher && !state.cipher.includes('NULL') && !state.cipher.includes('EXPORT');

            // Check for server-side renegotiation
            if (state.hasSecureRenegotiation &&
                state.hasValidHandshake &&
                isValidTLSVersion &&
                isValidCipher) {
                result.serverSide = {
                    supported: true,
                    protocol: state.protocol,
                    cipher: state.cipher,
                    certificate: state.certificate,
                    handshakeBytes: state.handshakeBytes
                };
            }

            // Check for client-side renegotiation
            if (state.hasInitialConnection &&
                state.hasReconnections &&
                state.hasSecureRenegotiation &&
                isValidTLSVersion &&
                isValidCipher) {
                result.clientSide = {
                    supported: true,
                    protocol: state.protocol,
                    cipher: state.cipher,
                    certificate: state.certificate,
                    handshakeBytes: state.handshakeBytes
                };
            }

            // Report self-signed certificate if found
            if (state.hasSelfSignedCert) {
                result.selfSignedCert = {
                    ipAddress: ipAddress,
                    protocol: state.protocol,
                    cipher: state.cipher,
                    certificate: state.certificate
                };
            }

        } catch (error) {
            return null;
        }

        return result;
    }

    // Check for session resumption vulnerabilities
    async checkSessionResumptionFromOutput(attack, output, ipAddress) {
        try {
            const lines = output.split('\n');
            let hasSessionResumption = false;
            let sessionDetails = {
                protocol: 'Unknown',
                cipher: 'Unknown',
                sessionId: 'Unknown',
                masterKey: 'Unknown',
                timeout: 'Unknown'
            };

            for (const line of lines) {
                if (line.includes('Reused,')) {
                    hasSessionResumption = true;
                    const match = line.match(/Reused, (TLSv\d+\.\d+), Cipher is ([^,]+)/);
                    if (match) {
                        sessionDetails.protocol = match[1];
                        sessionDetails.cipher = match[2];
                    }
                }
                if (line.includes('Session-ID:')) {
                    sessionDetails.sessionId = line.split('Session-ID:')[1].trim();
                }
                if (line.includes('Master-Key:')) {
                    sessionDetails.masterKey = line.split('Master-Key:')[1].trim();
                }
                if (line.includes('Timeout')) {
                    const timeoutMatch = line.match(/Timeout\s*:\s*(\d+)/);
                    if (timeoutMatch) {
                        sessionDetails.timeout = timeoutMatch[1];
                    }
                }
            }

            if (hasSessionResumption) {
                const timeout = parseInt(sessionDetails.timeout);
                if (timeout > 300) {
                    let riskLevel = 'Medium';
                    let riskDescription = '';

                    if (timeout > 3600) {
                        riskLevel = 'High';
                        riskDescription = 'Extended session timeout increases the window of opportunity for session hijacking.';
                    } else {
                        riskLevel = 'Medium';
                        riskDescription = 'Session timeout exceeds recommended 5-minute threshold.';
                    }

                    const details = `Session Resumption Found\n` +
                        `IP Address: ${ipAddress}\n` +
                        `Protocol: ${sessionDetails.protocol}\n` +
                        `Cipher: ${sessionDetails.cipher}\n` +
                        `Session ID: ${sessionDetails.sessionId}\n` +
                        `Timeout: ${sessionDetails.timeout} seconds\n` +
                        `Risk Level: ${riskLevel}\n` +
                        `Risk Description: ${riskDescription}`;

                    this.addVulnerabilitytoResult(attack, this.sessionresumptionID, details);
                }
            }

        } catch (error) {
            // Error handled by caller
        }
    }

    // Check for DNSSEC vulnerabilities
    async checkDNSSEC(attack, host, originalHost) {
        try {
            const dnssec = await this.dnssecrrsig(host);
            if (!dnssec || dnssec.includes('output: Not found')) {
                return;
            }

            const lines = dnssec.split('\n');
            let hasRRSIG = false;
            let targetIPs = new Set(); // Use Set to avoid duplicate IPs
            let hasUnsignedAnswer = false;
            let hasValidStatus = false;

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine) continue;

                if (trimmedLine.includes('status:')) {
                    hasValidStatus = trimmedLine.split('status:')[1].trim() === 'success';
                    continue;
                }

                if (trimmedLine.includes('unsigned_answer:')) {
                    hasUnsignedAnswer = true;
                    continue;
                }

                if (trimmedLine.includes('RRSIG')) {
                    hasRRSIG = true;
                }

                if (trimmedLine.includes('IN A')) {
                    const ipMatch = trimmedLine.match(/\b(?:\d{1,3}\.){3}\d{1,3}\b/);
                    if (ipMatch && this.isValidIPv4(ipMatch[0])) {
                        targetIPs.add(ipMatch[0]);
                    }
                }
            }

            // Only report if we have valid status and no RRSIG
            if (hasValidStatus && !hasRRSIG && targetIPs.size > 0) {
                const details = [
                    `Domain: ${originalHost}`,
                    'Status: DNSSEC Disabled',
                    `IPs: ${Array.from(targetIPs).join(', ')}`,
                    hasUnsignedAnswer ? 'Note: Domain uses unsigned DNS records' : null
                ].filter(Boolean).join('\n');

                if (details.trim()) {
                    this.addVulnerabilitytoResult(attack, this.dnssecnotsignedID, details);
                }
            }

        } catch (error) {
            return null;
        }
    }

    // Helper method to validate IPv4 addresses
    isValidIPv4(ip) {
        const parts = ip.split('.');
        return parts.length === 4 && parts.every(part => {
            const num = parseInt(part, 10);
            return num >= 0 && num <= 255 && part === num.toString();
        });
    }

    // Report client-side renegotiation vulnerability
    reportClientSideRenegotiation(attack, renegotiationResult) {
        if (!attack || !renegotiationResult || !renegotiationResult.ipAddress) {
            return;
        }

        const protocol = renegotiationResult.clientSide?.protocol || 'Unknown';
        const cipher = renegotiationResult.clientSide?.cipher || 'Unknown';
        const certificate = renegotiationResult.clientSide?.certificate || 'Unknown';
        const handshakeBytes = renegotiationResult.clientSide?.handshakeBytes || { read: 0, written: 0 };

        if (!protocol || !cipher || !certificate || protocol === 'Unknown' || cipher === 'Unknown' || certificate === 'Unknown') {
            return;
        }

        const details = `Client-side renegotiation is supported\n` +
            `IP Address: ${renegotiationResult.ipAddress}\n` +
            `Protocol: ${protocol}\n` +
            `Cipher: ${cipher.split(' ')[0]}\n` +
            `SSL Handshake: Read ${handshakeBytes.read} bytes, Written ${handshakeBytes.written} bytes\n` +
            `Certificate Details:\n${certificate}\n`;

        if (details.trim()) {
            this.addVulnerabilitytoResult(attack, this.clientSideRenegotiationID, details);
        }
    }

    // Report server-side renegotiation vulnerability
    reportServerSideRenegotiation(attack, renegotiationResult) {
        if (!attack || !renegotiationResult || !renegotiationResult.ipAddress) {
            return;
        }

        const protocol = renegotiationResult.serverSide?.protocol || 'Unknown';
        const cipher = renegotiationResult.serverSide?.cipher || 'Unknown';
        const certificate = renegotiationResult.serverSide?.certificate || 'Unknown';
        const handshakeBytes = renegotiationResult.serverSide?.handshakeBytes || { read: 0, written: 0 };

        if (!protocol || !cipher || !certificate || protocol === 'Unknown' || cipher === 'Unknown' || certificate === 'Unknown') {
            return;
        }

        const details = `Server-side renegotiation is supported\n` +
            `IP Address: ${renegotiationResult.ipAddress}\n` +
            `Protocol: ${protocol}\n` +
            `Cipher: ${cipher.split(' ')[0]}\n` +
            `SSL Handshake: Read ${handshakeBytes.read} bytes, Written ${handshakeBytes.written} bytes\n` +
            `Certificate Details:\n${certificate}\n`;

        if (details.trim()) {
            this.addVulnerabilitytoResult(attack, this.serverSideRenegotiationID, details);
        }
    }

    // Report self-signed certificate vulnerability
    reportSelfSignedCertificate(attack, certInfo) {
        if (!attack || !certInfo || !certInfo.ipAddress) {
            return;
        }

        const protocol = certInfo.protocol || 'Unknown';
        const cipher = certInfo.cipher || 'Unknown';
        const certificate = certInfo.certificate || 'Unknown';

        if (!protocol || !cipher || !certificate || protocol === 'Unknown' || cipher === 'Unknown' || certificate === 'Unknown') {
            return;
        }

        const details = `Self-signed certificate detected in certificate chain (Verify return code: 19)\n` +
            `IP Address: ${certInfo.ipAddress}\n` +
            `Protocol: ${protocol}\n` +
            `Cipher: ${cipher.split(' ')[0]}\n` +
            `Certificate Details:\n${certificate}\n` +
            `Risk: High - Self-signed certificates cannot be verified by trusted Certificate Authorities\n` +
            `Impact: This can lead to man-in-the-middle attacks as the certificate cannot be verified\n` +
            `Recommendation: Replace with a valid certificate from a trusted Certificate Authority`;

        if (details.trim()) {
            this.addVulnerabilitytoResult(attack, this.selfSignedCertID, details);
        }
    }

    // Execute command with optional piping to another command
    executeFile(command, args, input) {
        return new Promise((resolve) => {
            let output = '';
            let errorOutput = '';

            const timeout = setTimeout(() => {
                mainProcess.kill();
                resolve('output: Command not executed');
            }, this.commandTimeout);

            const mainProcess = spawn(command, args);

            mainProcess.stdout.on('data', (data) => {
                output += data.toString();
            });

            mainProcess.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            mainProcess.stdin.write(input);
            mainProcess.stdin.end();

            mainProcess.on('error', (err) => {
                clearTimeout(timeout);
                resolve('output: Command not executed');
            });

            mainProcess.on('close', () => {
                clearTimeout(timeout);
                resolve(output || errorOutput);
            });
        });
    }

    // Execute DNS query with RRSIG check
    dnssecrrsig(host) {
        return new Promise((resolve) => {
            const args = ['+yaml', host, '@*******'];
            const delv = spawn('delv', args);

            let output = '';
            const timeout = setTimeout(() => {
                delv.kill('SIGTERM');
                resolve('output: Not found');
            }, this.commandTimeout);

            delv.stdout.on('data', (data) => {
                output += data.toString();
            });

            delv.on('close', () => {
                clearTimeout(timeout);
                resolve(output || 'output: Not found');
            });

            delv.on('error', () => {
                clearTimeout(timeout);
                resolve('output: Not found');
            });
        });
    }
}

module.exports = tlsvuln