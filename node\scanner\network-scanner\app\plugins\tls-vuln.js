const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const { spawn } = require('child_process');
const dns = require('dns')

class tlsvuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Vulnerability IDs
        this.sessionresumptionID = 'ID-session-resumption'
        //Client-Side Renegotiation Without RFC 5746
        this.clientWithoutRFC5746ID = 'ID-client-side-without-rfc-5746'
        // Server-side Renegotiation Without RFC 5746
        this.serverWithoutRFC5746ID = 'ID-server-side-without-rfc-5746'
        // Client-side Renegotiation Supported
        this.clientSideRenegotiationID = 'ID-client-side-renegotiation'
        // Server-side Renegotiation Supported
        this.serverSideRenegotiationID = 'ID-server-side-renegotiation'
        // Self-signed certificate
        this.selfSignedCertID = 'ID-self-signed-certificate'
        this.commandTimeout = 30000; // 30 seconds timeout
    }

    // Main entry point for TLS vulnerability checks
    async processAttackResponse(attack) {
        if (!this.isValidAttack(attack)) {
            return;
        }

        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        if (!pluginStorageScanScope || pluginStorageScanScope.tlsvulnchecked) {
            return;
        }

        try {
            const host = attack.hostname;
            const replayHeader = _.get(attack, 'originalRequest.httpRequest.headers["X-IFC-REPLAY"]', false);

            // Skip DNS resolution if replay header exists
            const gethost = replayHeader ? host : await this.dnsresolve(host);
            if (!gethost) {
                return;
            }

            // Get IP address once and reuse
            const ipAddress = await this.getipaddress(gethost);
            if (!this.isValidIPAddress(ipAddress)) {
                pluginStorageScanScope.tlsvulnchecked = true;
                return;
            }

            // Check renegotiation using OpenSSL
            const opensslResult = await this.checkOpenSSLRenegotiation(ipAddress, host);
            if (opensslResult === 'Command not executed') {
                pluginStorageScanScope.tlsvulnchecked = true;
                return;
            }

            // Report vulnerabilities
            this.reportVulnerabilities(attack, opensslResult, ipAddress);
            pluginStorageScanScope.tlsvulnchecked = true;
        } catch (error) {
            pluginStorageScanScope.tlsvulnchecked = true;
        }
    }

    isValidAttack(attack) {
        return attack &&
            typeof attack === 'object' &&
            attack.attackArea === 'original-crawler-request' &&
            attack.pluginName === 'Original Crawler Request' &&
            attack.hostname &&
            attack.href;
    }

    isValidIPAddress(ipAddress) {
        return ipAddress &&
            ipAddress !== 'not found' &&
            !ipAddress.includes(':');
    }

    reportVulnerabilities(attack, opensslResult, ipAddress) {
        const resultData = {
            ipAddress,
            protocol: opensslResult.protocol,
            cipher: opensslResult.cipher,
            certificate: opensslResult.certificate,
            issues: opensslResult.issues
        };

        if (opensslResult.clientSideVulnerable) {
            this.reportClientSideRenegotiation(attack, resultData);
        }

        if (opensslResult.serverSideVulnerable) {
            this.reportServerSideRenegotiation(attack, resultData);
        }

        if (opensslResult.clientSideWithoutRFC5746) {
            this.reportClientSideWithoutRFC5746(attack, resultData);
        }

        if (opensslResult.serverSideWithoutRFC5746) {
            this.reportServerSideWithoutRFC5746(attack, resultData);
        }

        if (opensslResult.hasSelfSignedCert) {
            this.reportSelfSignedCertificate(attack, resultData);
        }

        if (opensslResult.vulnerableSessionResumption) {
            const sessionDetails = {
                protocol: opensslResult.sessionProtocol,
                cipher: opensslResult.sessionCipher,
                sessionId: opensslResult.sessionId,
                masterKey: opensslResult.masterKey,
                timeout: opensslResult.timeout,
                severity: opensslResult.severity
            };
            this.reportSessionResumption(attack, sessionDetails);
        }
    }

    async checkOpenSSLRenegotiation(ipAddress, host) {
        const protocols = ['-tls1', '-tls1_1', '-tls1_2'];

        for (const protocol of protocols) {
            const result = await this.checkProtocol(ipAddress, host, protocol);
            if (result !== 'Command not executed' && result !== 'Protocol mismatch' && result.cipher !== 'Unknown') {
                return result;
            }
        }

        return {
            clientSideVulnerable: false,
            serverSideVulnerable: false,
            sessionResumption: false,
            vulnerableSessionResumption: false,
            hasSelfSignedCert: false,
            protocol: 'Unknown',
            cipher: 'Unknown',
            certificate: 'Unknown',
            sessionId: null,
            masterKey: null,
            timeout: null,
            issues: ['No supported TLS protocol found'],
            severity: 'Informational',
            clientSideWithoutRFC5746: false,
            serverSideWithoutRFC5746: false
        };
    }

    async checkProtocol(ipAddress, host, protocol) {
        return new Promise((resolve) => {
            try {
                const args = [
                    's_client',
                    '-connect', `${ipAddress}:443`,
                    '-servername', host,
                    protocol,
                    '-reconnect'
                ];

                const openssl = spawn('openssl', args);
                let output = '';
                let result = this.initializeResult();
                let vulnFound = false;
                let renegotiationTested = false;

                const timeout = setTimeout(() => {
                    openssl.kill('SIGTERM');
                    resolve(result);
                }, this.commandTimeout);

                openssl.stdout.on('data', (data) => {
                    output += data.toString();
                    // If we see the initial connection and haven't tested renegotiation yet
                    if (/CONNECTED\(\w+\)/i.test(output) && !renegotiationTested) {
                        renegotiationTested = true;
                        // Send R command to test renegotiation
                        openssl.stdin.write('R');
                        // Give it a moment to process
                        setTimeout(() => {
                            openssl.stdin.end();
                        }, 1000);
                    }
                });

                openssl.stderr.on('data', (data) => {
                    output += data.toString();
                });

                openssl.on('close', () => {
                    clearTimeout(timeout);
                    if (output.length > 5 && /CONNECTED\(\w+\)/i.test(output) && !output.includes('connect:errno=0') && !output.includes('New, (NONE)')) {
                        // Skip if we get invalid cipher or session results
                        if (output.includes('Cipher is (NONE)') ||
                            /Cipher\s*:\s*0000/i.test(output) ||
                            !output.includes('Session-ID:') ||
                            /Session-ID:\s*[\r\n]/i.test(output)) {
                            resolve('Command not executed');
                            return;
                        }
                        this.processOpenSSLOutput(output, result, vulnFound, protocol, resolve);
                    } else {
                        resolve('Command not executed');
                    }
                });

                openssl.on('error', () => {
                    clearTimeout(timeout);
                    resolve('Command not executed');
                });
            } catch (error) {
                resolve('Command not executed');
            }
        });
    }

    initializeResult() {
        return {
            clientSideVulnerable: false,
            serverSideVulnerable: false,
            sessionResumption: false,
            vulnerableSessionResumption: false,
            hasSelfSignedCert: false,
            protocol: 'Unknown',
            cipher: 'Unknown',
            certificate: 'Unknown',
            sessionId: null,
            masterKey: null,
            timeout: null,
            issues: [],
            severity: 'Informational'
        };
    }

    processOpenSSLOutput(output, result, vulnFound, protocol, resolve) {
        // Extract protocol and cipher
        const protocolMatch = output.match(/Protocol\s*:\s*([^\r\n]+)/i);
        if (protocolMatch) {
            result.protocol = protocolMatch[1].trim();
            // Normalize protocol name for comparison
            const normalizedProtocol = result.protocol.toLowerCase().replace(/\./g, '_').replace('v', '');
            const normalizedInputProtocol = protocol.replace('-', '').toLowerCase();
            if (normalizedProtocol !== normalizedInputProtocol) {
                resolve('Protocol mismatch');
                return;
            }
        } else {
            resolve('Command not executed');
            return;
        }

        const cipherMatch = output.match(/Cipher\s*:\s*([^\r\n]+)/i);
        const subjectMatch = output.match(/subject=([^\r\n]+)/i);
        const issuerMatch = output.match(/issuer=([^\r\n]+)/i);

        if (cipherMatch) result.cipher = cipherMatch[1].trim();
        if (subjectMatch && issuerMatch) {
            result.certificate = `Subject: ${subjectMatch[1].trim()}\nIssuer: ${issuerMatch[1].trim()}`;
        }

        // Check for RFC 5746 implementation
        if (output.includes('Secure Renegotiation IS NOT supported')) {
            result.clientSideWithoutRFC5746 = true;
            result.serverSideWithoutRFC5746 = true;
            result.issues.push('Secure renegotiation (RFC 5746) is not supported. This is a critical vulnerability. ');
            vulnFound = true;
        }

        // Check for renegotiation support and test actual renegotiation
        if (output.includes('Secure Renegotiation IS supported')) {
            if (output.includes('RENEGOTIATING') && !/RENEGOTIATING$/.test(output) && /RENEGOTIATING[\n\r\s]+.{1,1000}/i.test(output)) {
                let denied = /warning close_notify|no renegotiation|no_renegotiation|fatal handshake_failure|SSL alert number 100/i.test(output);
                if (!denied) {
                    result.clientSideVulnerable = true;
                    result.serverSideVulnerable = true;
                    result.issues.push('Both client and server-initiated TLS renegotiation are supported. This could potentially be used in DoS attacks. ');
                    vulnFound = true;
                }
            }
        }

        // Check for self-signed certificate
        if (output.includes('Verify return code: 19') ||
            (output.includes('Verification error:') &&
                output.includes('self signed certificate in certificate chain'))) {
            result.hasSelfSignedCert = true;
            result.issues.push('Self-signed certificate detected. ');
            vulnFound = true;
        }

        // Check for weak protocols
        const weakProtocols = ['SSLv2', 'SSLv3', 'TLSv1', 'TLSv1.1'];
        let weakProtocolFound = false;
        if (weakProtocols.includes(result.protocol)) {
            result.issues.push(`Insecure protocol used: ${result.protocol}. `);
            weakProtocolFound = true;
            vulnFound = true;
        }

        // Check for SSL protocols specifically
        if (result.protocol.includes('SSL')) {
            result.issues.push(`Deprecated SSL protocol detected: ${result.protocol}. `);
            vulnFound = true;
            if (result.protocol === 'SSLv2' || result.protocol === 'SSLv3') {
                result.severity = 'Critical';
            } else {
                result.severity = 'High';
            }
        }

        // Check for weak ciphers
        const weakCiphersRegex = /RC4|3DES|NULL|EXPORT|LOW/i;
        let weakCipherFound = false;
        if (weakCiphersRegex.test(result.cipher)) {
            result.issues.push(`Weak cipher used: ${result.cipher}. `);
            weakCipherFound = true;
            vulnFound = true;
        }

        // Session resumption checks
        const reusedMatches = output.match(/Reused, (TLSv\d+\.\d+), Cipher is ([^\r\n]+)/gi);
        const sessionIdMatches = output.match(/Session-ID: ([^\r\n]+)/gi);
        const masterKeyMatches = output.match(/Master-Key: ([^\r\n]+)/gi);
        const timeoutMatches = output.match(/Timeout\s*:\s*(\d+)/gi);

        if (reusedMatches) {
            const lastReused = reusedMatches[reusedMatches.length - 1];
            const match = lastReused.match(/Reused, (TLSv\d+\.\d+), Cipher is ([^\r\n]+)/i);
            if (match) {
                result.sessionResumption = true;
                result.sessionProtocol = match[1];
                result.sessionCipher = match[2].trim();
            }
        }

        if (sessionIdMatches) {
            const lastSessionId = sessionIdMatches[sessionIdMatches.length - 1];
            const match = lastSessionId.match(/Session-ID: ([^\r\n]+)/i);
            if (match) {
                result.sessionId = match[1].trim();
            }
        }

        if (masterKeyMatches) {
            const lastMasterKey = masterKeyMatches[masterKeyMatches.length - 1];
            const match = lastMasterKey.match(/Master-Key: ([^\r\n]+)/i);
            if (match) {
                result.masterKey = match[1].trim();
            }
        }

        if (timeoutMatches) {
            const lastTimeout = timeoutMatches[timeoutMatches.length - 1];
            const match = lastTimeout.match(/Timeout\s*:\s*(\d+)/i);
            if (match) {
                result.timeout = parseInt(match[1]);
            }
        }

        // Initialize severity and flags if not already
        result.severity = 'Informational'; // Default if nothing is wrong

        // Check for weak session even without long timeout
        if (result.sessionResumption && (weakProtocolFound || weakCipherFound)) {
            vulnFound = true;
            result.vulnerableSessionResumption = true;
            result.issues.push(`Session resumption used with weak protocol or cipher. `);

            if (weakCipherFound && (result.cipher.includes('RC4') || result.cipher.includes('NULL') || result.cipher.includes('EXPORT'))) {
                result.severity = 'High';
            } else if (weakProtocolFound && (result.protocol.includes('TLSv1.0') || result.protocol.includes('TLSv1.1'))) {
                result.severity = 'High';
            } else if (weakCipherFound && result.cipher.includes('3DES')) {
                result.severity = 'Medium';
            } else {
                result.severity = 'Low';
            }
        }

        // Check for long session timeout (regardless of weak cipher/protocol)
        if (parseInt(result.timeout) > 21600) { // 6 hours
            result.issues.push(`Above 6 hours long session timeout detected: ${result.timeout} seconds. `);

            if (result.sessionResumption) {
                vulnFound = true;
                result.vulnerableSessionResumption = true;

                // Upgrade severity only if it's not already high
                if (result.severity === 'Informational' || result.severity === 'Low') {
                    result.severity = 'Low'; // Still low by itself
                }
            }
        }

        if (vulnFound) {
            resolve(result);
        } else {
            resolve('Command not executed');
        }
    }

    getipaddress(host) {
        return new Promise((resolve) => {
            try {
                const args = ['+short', host];
                const dig = spawn('dig', args);
                let output = '';

                const timeout = setTimeout(() => {
                    dig.kill('SIGTERM');
                    resolve('not found');
                }, 10000);

                dig.stdout.on('data', (data) => {
                    output += data.toString();
                });

                dig.on('close', () => {
                    clearTimeout(timeout);
                    const ips = output.trim().split('\n');
                    const ipv4 = ips.find(ip => /^\d+\.\d+\.\d+\.\d+$/.test(ip));
                    resolve(ipv4 || 'not found');
                });

                dig.on('error', () => {
                    clearTimeout(timeout);
                    resolve('not found');
                });
            } catch (error) {
                resolve('not found');
            }
        });
    }

    async dnsresolve(host) {
        try {
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    resolve(host);
                }, this.commandTimeout);

                dns.resolveCname(host, (err, addresses) => {
                    clearTimeout(timeout);
                    if (err) {
                        resolve(host);
                    }

                    if (addresses && addresses.some(addr => /\.induscdn\.com/i.test(addr))) {
                        resolve(`proxypass.${host}.indusguard.com`);
                    } else {
                        resolve(host);
                    }
                });
            });
        }
        catch (error) {
            return null;
        }
    }

    reportClientSideRenegotiation(attack, renegotiationResult) {
        if (!attack || !renegotiationResult || !renegotiationResult.ipAddress) {
            return;
        }

        const details = `Client-side renegotiation detected. \n` +
            `IP Address: ${renegotiationResult.ipAddress}; \n` +
            `Protocol: ${renegotiationResult.protocol}; \n` +
            `Cipher: ${renegotiationResult.cipher}; \n` +
            `Issues Found: \n${renegotiationResult.issues ? renegotiationResult.issues.map(issue => `- ${issue}`).join('\n') : 'None'}`;

        this.addVulnerabilitytoResult(attack, this.clientSideRenegotiationID, details);
    }

    reportServerSideRenegotiation(attack, renegotiationResult) {
        if (!attack || !renegotiationResult || !renegotiationResult.ipAddress) {
            return;
        }

        const details = `Server-side renegotiation detected. \n` +
            `IP Address: ${renegotiationResult.ipAddress}; \n` +
            `Protocol: ${renegotiationResult.protocol}; \n` +
            `Cipher: ${renegotiationResult.cipher}; \n` +
            `Issues Found:\n${renegotiationResult.issues ? renegotiationResult.issues.map(issue => `- ${issue}`).join('\n') : 'None'}`;

        this.addVulnerabilitytoResult(attack, this.serverSideRenegotiationID, details);
    }

    reportClientSideWithoutRFC5746(attack, renegotiationResult) {
        if (!attack || !renegotiationResult || !renegotiationResult.ipAddress) {
            return;
        }

        const details = `Client-side renegotiation without RFC 5746 support detected. \n` +
            `IP Address: ${renegotiationResult.ipAddress}; \n` +
            `Protocol: ${renegotiationResult.protocol}; \n` +
            `Cipher: ${renegotiationResult.cipher}; \n` +
            `Issues Found: \n${renegotiationResult.issues ? renegotiationResult.issues.map(issue => `- ${issue}`).join('\n') : 'None'}`;

        this.addVulnerabilitytoResult(attack, this.clientWithoutRFC5746ID, details);
    }

    reportServerSideWithoutRFC5746(attack, renegotiationResult) {
        if (!attack || !renegotiationResult || !renegotiationResult.ipAddress) {
            return;
        }

        const details = `Server-side renegotiation without RFC 5746 support detected. \n` +
            `IP Address: ${renegotiationResult.ipAddress}; \n` +
            `Protocol: ${renegotiationResult.protocol}; \n` +
            `Cipher: ${renegotiationResult.cipher}; \n` +
            `Issues Found: \n${renegotiationResult.issues ? renegotiationResult.issues.map(issue => `- ${issue}`).join('\n') : 'None'}`;

        this.addVulnerabilitytoResult(attack, this.serverWithoutRFC5746ID, details);
    }

    reportSelfSignedCertificate(attack, certInfo) {
        if (!attack || !certInfo || !certInfo.ipAddress) {
            return;
        }

        const details = `Self-signed certificate detected in certificate chain (Verify return code: 19). \n` +
            `IP Address: ${certInfo.ipAddress}; \n` +
            `Protocol: ${certInfo.protocol}; \n` +
            `Cipher: ${certInfo.cipher}; \n` +
            `Risk: High - Self-signed certificates cannot be verified by trusted Certificate Authorities. \n` +
            `Impact: This can lead to man-in-the-middle attacks as the certificate cannot be verified. \n` +
            `Issues Found:\n${certInfo.issues ? certInfo.issues.map(issue => `- ${issue}`).join('\n') : 'None'}`;

        this.addVulnerabilitytoResult(attack, this.selfSignedCertID, details);
    }

    reportSessionResumption(attack, details) {
        if (!attack || !details) {
            return;
        }

        this.addVulnerabilitytoResult(attack, this.sessionresumptionID, details);
    }
}

module.exports = tlsvuln