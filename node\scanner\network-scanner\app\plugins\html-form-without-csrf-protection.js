const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * HTML form without CSRF Protection plugin strategy:
 * Here for every request having HTML form submitted in it, will be checking for the presence of 
 * CSRF token into the form which usually is in input type hidden field. If form is without
 * this token then we will be reporting it vulnerable
 * 
 * If above condition is found then vulnerability will be reported
 */
class HTMLFormWithoutCSRFProtection extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-html-form-without-csrf-protection'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {
        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {
            let body = _.get(originalRequest, "result.resp.body")
            // If captcha is not present in the response body then only proceed ahead
            if (!(/captcha/i.test(body))) {
                // check if form method post is present in response body, then only call processAttackResponse
                if (/<form(.*)method="post"/i.test(body)) {
                    return true
                }
            }
        }
        return false
    }

    /**
     * Check response 
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {

        let pluginDataForRequest = this.getPluginScopedStore(originalRequest)
        if (pluginDataForRequest.htmlFormWithoutCSRFProtection) {
            return
        }

        let $ = _.get(originalRequest, 'result.resp.httpResponse.cheerio');
        let forms = $("form")

        //check if form has any hidden csrf field value present in it
        // if so don't report the vulnerability
        if (forms.length > 0 && (/get/i.test(originalRequest.httpRequest.method) || /post/i.test(originalRequest.httpRequest.method))) {
            let hiddenInputField = $('input[type=hidden]', forms)
            let vulnExists = true
            if (hiddenInputField.length > 0) {
                // check each hidden input name if any of them has csrf token present
                //30th july - Update Exclude for wordpress token id="_wpnonce" name="_wpnonce"
                hiddenInputField.each(function (index) {
                    if ($(this).attr('name') != undefined) {
                        let res = $(this).attr('name')
                        if (/csrf|token|_Requestverification|xsrf|__AntiXsrfToken|__TokenAntiXsrf|_build_id|_wpnonce|hdfgsajkl|__VIEWSTATE|__EVENTVALIDATION/i.test(res)) {
                            vulnExists = false
                        }
                    }
                })
            }

            //check if csrf header is in request cookie header
            let requestCookie = _.get(originalRequest, 'httpRequest.headers.Cookie', {});
            if (/csrf|_Requestverification|xsrf|__AntiXsrfToken|KSRF-TOKEN|__TokenAntiXsrf|hdfgsajkl/i.test(requestCookie)) {
                vulnExists = false
            }

            /*<form action="" name="csrf_form" id="csrf_form" method="post">
            <h2>Attack Detected Request Token Does Not Match Page Token!!!!!!!!</h2>
            </form> if above content present, dont report vuln*/
            //hdfgsajkl=2cc1cd26-351d-41a5-babe-40ad0ca01ba9 - this is for etender -60054
            forms.each(function (index, el) {
                if ($(el).attr('name') != undefined) {
                    let res = $(el).attr('name')
                    if (/csrf|token|_Requestverification|xsrf|__AntiXsrfToken|__TokenAntiXsrf|_wpnonce|hdfgsajkl/i.test(res)) {
                        let BodyField = _.get(originalRequest, "result.resp.body")
                        if (/Attack Detected/i.test(BodyField)) {
                            vulnExists = false
                        }
                    }
                }
            })

            // If csrf token not found in response body or in request header cookie then only
            // report the vulnerability
            if (vulnExists) {
                this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, originalRequest.href)
                pluginDataForRequest.htmlFormWithoutCSRFProtection = true
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['<form','method=\"post\"','type=\"hidden\"']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
    }
}

module.exports = HTMLFormWithoutCSRFProtection