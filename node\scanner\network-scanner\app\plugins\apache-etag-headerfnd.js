const debug = require('debug')('HttpHeadersChecker')
const NetworkAttack = require('./network-attack')
const URL = require('url').URL
const _ = require('lodash')
const caseless = require('caseless')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class ApacheETagHeader extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID      
        this.vulnerabilityID = 'ID-Apache-ETag-Header-Info'
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea == "original-crawler-request") {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.ApacheETagVulnFound) {
            return
        }

        let etag = _.get(attack, 'result.resp.httpResponse.headers["etag"]', "")
        let contentLength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', "")
        if (etag.length > 0) {
            let rmvquotes = etag.split('"').join('')
            let part = rmvquotes.split('-')
            if (part.length > 2) {
                //hex to decimal
                let inotedec = parseInt(part[0], 16).toString()
                let sizedec = parseInt(part[1], 16).toString()
                let timedec = parseInt(part[2], 16).toString()
                let mtime = new Date(timedec * 1000 / 1000000)
                if (!(/invalid date|NaN|undefined/i.test(mtime) || /NaN|undefined/i.test(sizedec) || /NaN|undefined/i.test(inotedec)) && sizedec == contentLength) {
                    let details = {
                        "Inode": inotedec,
                        "Size": sizedec,
                        "ModTime": mtime
                    }
                    if (details) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                        pluginDataForRequest.ApacheETagVulnFound = true
                    }
                }
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["etag"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);        
    }
}
module.exports = ApacheETagHeader