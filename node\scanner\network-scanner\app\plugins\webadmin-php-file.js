const debug = require('debug')('WebadminPHPFile')
const querystring = require('querystring')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class WebadminPHPFile extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-webadmin-php-file'
    }

    /**
     * get array of Xpath attack vectors
     * @override
     */
    getAttackVectors() {
        return WPFVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        if (attack.httpRequest.uri.indexOf('/info.php') != -1 || attack.httpRequest.uri.indexOf('/phpinfo.php') != -1) {
            if (/expose_php/i.test(attack.result.resp.body) && /file_uploads/i.test(attack.result.resp.body) && /error_log/i.test(attack.result.resp.body) && /upload_max_filesize/i.test(attack.result.resp.body)) {
                return
            }
        }
        let body = attack.result.resp.httpResponse.body
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        // Only check if status code is 200 ok
        if (statusCode == "200") {
            let match = [
                'change',
                'upload',
                'create',
                'Directory',
                'File',
                'Filename',
                'Size',
                'Functions'
            ]
            let flag = [false, false, false, false, false, false, false, false]

            for (var i = 0; i < match.length; i++) {
                //Includes will test for case sensitive and if found then only mark flag as true
                if (body.includes(match[i])) {
                    flag[i] = true
                }
            }

            //Only report vuln if all words are found with case sensitivity
            if (flag[0] && flag[1] && flag[2] && flag[3] &&
                flag[4] && flag[5] && flag[6] && flag[7]) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.httpRequest.uri)
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        
        if(vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, [statusCode]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['change', 'upload', 'create', 'Directory', 'File', 'Filename', 'Size', 'Functions']);
    }

}


const WPFVectors = [
    `_site_map.php`,
    `webadmin.php`
]

module.exports = WebadminPHPFile