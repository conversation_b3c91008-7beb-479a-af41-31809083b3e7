const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const UriQueryParameters = require('./uri-query-parameters')
const FormEncodedPost = require('./form-encoded-post')
const HaikuUtils = require('../../common/lib/haiku-utils')
const _ParameterType = 'LoginRequest'

class LoginDelegate extends ParameterizedDelegate {
    static get ParameterType() {
        return _ParameterType
    }

    /**
     * Returns true if the request is a login request, false otherwise
     * @param {Object} httpRequest haiku http request object
     * @returns {Boolean} true if the request is a login request, false otherwise
     */
    static isLoginRequest(httpRequest) {
        // see if we have all the login info we need
        if (!httpRequest || !httpRequest.loginInfo) {
            return false
        }

        if (!httpRequest.loginInfo.username || !httpRequest.loginInfo.password) {
            return false
        }

        // for now we only handle one username, one password
        let addlInfo = _.get(httpRequest, 'loginInfo.additionalInfo')
        if (!addlInfo || !addlInfo.usernames || !addlInfo.passwords ||
            addlInfo.usernames.length != 1 || addlInfo.passwords.length != 1) {
            return false
        }

        let {
            usernameParams,
            pwdParams
        } = HaikuUtils.getLoginParams(httpRequest)
        return usernameParams.length > 0 && pwdParams.length > 0
    }

    /**
     * Returns true if the login was successful, false otherwise
     * @param {Object} httpRequest haiku http request object
     * @returns {Boolean} true if login was successful, false otherwise
     */
    static isSuccessfullLogin(httpRequest) {
        return _.get(httpRequest, 'loginInfo.loginStatus') === true
    }

    /**
     * Returns a generator that can be used by plugins as return value to the override getAttackVectors()
     * and will be used by VectorResponseAttack to attack parameters.
     * This will return a complete set of attacks i.e. 3 un vectors and 2 pwd vectors will yield 6 vectors 
     * eg.unVecs: [u1,u2,u2], pwdVecs: [p1,p2] will yield:
     *  {unVector:u1, pwdVector:p1}, {unVector:u1, pwdVector:p2}, {unVector:u2, pwdVector:p1},
     *  {unVector:u2, pwdVector:p2}, {unVector:u3, pwdVector:p1}, {unVector:u3, pwdVector:p2} 
     * use ParameterizedDelegate.identityVector if you don't want to change the parameter
     * @param {Array} unVectors Vectors to attack username parameters
     * @param {Array} pwdVectors Vectors to attack password parameters
     */
    static * createVectorsIterator(unVectors, pwdVectors) {
        for (let unVector of unVectors) {
            for (let pwdVector of pwdVectors) {
                yield( {unVector, pwdVector})
            }
        }
    }

    /**
     * @param {request} request the request whose login params we are tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     * @param {object} options config related to plugin
     */
    constructor(request, scanStore, options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType, options)
        this._delegate = this.getDelegate(request, scanStore, options)

        // store login info
        this.username = request.httpRequest.loginInfo.username
        this.password = request.httpRequest.loginInfo.password
        let {
            usernameParams,
            pwdParams
        } = HaikuUtils.getLoginParams(request.httpRequest)
        this.unParams = usernameParams
        this.pwdParams = pwdParams
    }

    getDelegate(request, scanStore, options) {
        if (FormEncodedPost.isFormEncodedPostRequest(request.httpRequest)) {
            return new FormEncodedPost(request, scanStore, options)
        } else if (UriQueryParameters.isUriQueryParamsRequest(request.httpRequest)) {
            return new UriQueryParameters(request, scanStore, options)
        }

        return null
    }

    * getIterator() {
        // starting iteration, keep track of original post body.
        this.originalPostbody = _.cloneDeep(this.postBody)

        yield {
            name: {
                unParams: this.unParams,
                pwdParams: this.pwdParams
            },
            val: {
                un: this.username,
                pwd: this.password
            },
            resetRequired: true
        }
    }

    /**
     * 
     * @param {Object} param Details of param(s) to modify, has array of username params and password params
     * @param {Object} value contains Username and paassword vector
     * @param {encoding} encoding passed on to delegate to set the encoding
     */
    modifyParam(param, value, encoding) {
        let unVector = value.unVector
        let pwdVector = value.pwdVector

        // adjust for identity vectors
        if ( unVector == ParameterizedDelegate.identityVector) {
            unVector = this.username
        }
        if ( pwdVector == ParameterizedDelegate.identityVector) {
            pwdVector = this.password
        }

        // modify params
        for (let unParam of param.unParams) {
            this._delegate.modifyParam(unParam, unVector, encoding)
        }
        for (let pwdParam of param.pwdParams) {
            this._delegate.modifyParam(pwdParam, pwdVector, encoding)
        }
    }

    // -- pass through to delegate --

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this._delegate.getEncodings()
    }

    getHttpRequest(encoding) {
        return this._delegate.getHttpRequest(encoding)
    }

    // reset post body
    reset() {
        this._delegate.reset()
    }
}
module.exports = LoginDelegate