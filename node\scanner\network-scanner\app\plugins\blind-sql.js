const debug = require('debug')('BlindSQLInjection')
const querystring = require('querystring')
const VectorResponseAttack = require('./vector-response-attack')
const jsdom = require("jsdom")
const HaikuUtils = require('../../../common/lib/haiku-utils')
const FingerPrint = require('../../../common/lib/fingerprint')
const fs = require('fs')
const mkdirp = require('mkdirp')
const _ = require('lodash')
const logger = require('../../../common/lib/haiku-logger')
const cheerio = require("cheerio")

/** 
 * VectorResponse style plugin that checks for Blind SQL - content based (Boolean)
 * Strategy:
 * Set of vectors that are templated for True/False.
 * Send the vectors with true and false conditions and calculate fngerprint of
 *  1. HTML structure (tags only - no attrs & contenxt) 
 *  2. text content only 
 * Compare the true & false cases and if they are significantly different, flag as a potential vuln
 * Next do a validation step for the vector that matched by adding calculations to the mix
 */
class BlindSQLInjection extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        mkdirp.sync('./htmlfiles')
    }

    /**
     * initilize plugin - sets up attack patterns etc.
     * @override
     */
    init() {
        super.init()

        /** 
         *  Full template will be along the lines of: (xxx) is the templated string in vetor that will be replaced
         *      leading: none,', ", <sp>             <not yet used>
         *      conditional (_COND_): AND, OR        <only AND is supported>
         *      quoting('): none (number), ', "
         *      trailing(#): none, --, #, /*
         *      check (_OneOrZero_): 1, 0
         *  but that explodes the vectors too much eg. with above list, *each* vector will expand to 4*2*3*4*2 = 192
         *  conditional is only AND to prevent dangerous actions being performed by server when OR is used
         *  for now, just using quote, check, trailing => each templated vector generates max 24 vectors
         */
        let conditions = ['AND']
        this.conditionalVectors = this.expandTemplate(_ConditionalVectors, /_COND_/g, conditions)
        let quotes = [`'`, `"`, ``]
        this.conditionalVectors = this.expandTemplate(this.conditionalVectors, /'/g, quotes)
        let comments = [`--`, ``, `#`, `/*`, ]
        this.conditionalVectors = this.expandTemplate(this.conditionalVectors, /#/g, comments)

        this.conditionalVectors = Array.from(new Set(this.conditionalVectors)).map(v => {
            return {
                trueFalseTemplate: v,
                t: _.clone(v).replace('_OneOrZero_', '1'),
                f: _.clone(v).replace('_OneOrZero_', '0')
            }
        })
    }

    /**
     * Expand an array of string templates using multiple expansions
     * eg. expandTemplate( ['a _test_ b _test_', '123 _test_ 123], '_test_', ['xx', 'yy', 'zz']) will return
     * [ 
     *      'a xx b xx',
     *      '123 xx 123',
     *      'a yy b yy',
     *      '123 yy 123',
     *      'a zz b zz',
     *      '123 zz 123',
     * ] 
     * @param {array} templateArray array of temaplets (strings)
     * @param {string} templatePattern what to replace (replaces all occurances in template)
     * @param {array} expansions array of strings to expand template with
     */
    expandTemplate(templateArray, templatePattern, expansions) {
        let tmpArr = []
        for (let exp of expansions) {
            tmpArr = tmpArr.concat(templateArray.map(v => _.clone(v).replace(templatePattern, exp)));
        }
        return tmpArr
    }


    /**
     * New request received from crawler - compute the original response fingerprint.
     * @param {request} originalRequest new request received from crawler
     * @override
     */
    processNewRequest(originalRequest) {
        super.processNewRequest(originalRequest)
        try {
            if (originalRequest.httpResponse.err) {
                return
            }

            let resp = _.clone(originalRequest.httpResponse)
            resp.cheerio = cheerio.load(resp.body)
            let fingerprints = this.getFingerprints(resp)

            let pluginDataForRequest = this.getPluginScopedStore(originalRequest, 'current-request')
            _.set(pluginDataForRequest, 'originalResponse.fingerprint', fingerprints)
        } catch (err) {
            logger.log('error', `blindsql - new request${err.toString()}`, HaikuUtils.getMetadataForLog(originalRequest))
        }
    }

    /**
     * @typedef {Object} fingerprintData
     * @property {fingerprint} structure  fingerprint of all the HTML/XML tags
     * @property {fingerprint} content fingerprint of all the text i.e. content of text nodes
     * @property {Number} statusCode status code from this response
     * @property {string} redirects entire redirect chain joined with ' + '
     */

    /**
     * Get structure and content fingerprints from the response.
     * @returns {fingerprintData} fingerprintData
     * @param {response} response The HTTP response
     */
    getFingerprints(response) {
        let fingerprints = HaikuUtils.getFingerprints(response)
        let fingerprintsData = {
            structure: fingerprints.structure,
            content: fingerprints.content,
            statusCode: response.statusCode,
            redirects: response.redirects.map(e => e.redirectUri).join(' + ')
        }
        return fingerprintsData
    }

    /**
     * set up options to customzize the parametrized delegate
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     */
    initParameterizedDelegate(parameterizedDelegate) {
        parameterizedDelegate.setOptions({
            addExtraParam: false,
            attackParamName: false
        })
    }

    /**
     * More than just returng a simple list of vectors. Blind SQL detection algo is driven from here:
     * Iterate the vectors in append & replace mode.
     * for AND condition...
     *  try the false condition first, if it is different from original response (<90% match)
     *      try the true condition, if it is very similar to original response (>95% match)
     *          try the validation calculations on this vector:
     *              if all true calcs are very similar to the true response (>98% match)
     *                  AND 
     *              all false conditions are very similar to the false response  (>98% match)
     *          => we have a blind sql.
     * We don't yet use OR conditions due to teh high risk of damage to customer's backend DB.
     * @param {baseAttack} baseAttack base attack - attack object without vector, encoding.
     * @param {string} encoding the encoding being tried now
     * @override
     */
    * getVectorIterator(baseAttack, encoding) {
        try {
            let pluginDataForRequest = this.getPluginScopedStore(baseAttack)

            // sanity checks - if teh original request had failed, dont go through with attacks
            pluginDataForRequest.similarities = {}

            // Algo is driven from here
            // first get send untampered to get any variance...
            pluginDataForRequest.phase = 'baselineRequestVariance'
            yield baseAttack.paramVal

            // any error in request, dont continue
            if (!pluginDataForRequest.originalResponse) {
                logger.log('info', `blindsql - not a potential since original request itself resulted in error.`, HaikuUtils.getMetadataForLog(baseAttack))
                return
            }

            // if the status code/redirect is not the same, dont continue - this is the original - untampered comparaision
            // dont allow more than 15% variance in structure & 20% variance in content
            if (pluginDataForRequest.originalResponse.statusCode != pluginDataForRequest.fingerPrints.statusCode ||
                pluginDataForRequest.originalResponse.redirects != pluginDataForRequest.fingerPrints.redirects) {
                logger.log('info', `blindsql - not a potential since staus code and/or redirect chain do not match in untampered requests`, HaikuUtils.getMetadataForLog(baseAttack))
                return
            }

            // calculate variance - original - original
            let untamperedFingerPrint = pluginDataForRequest.fingerPrints
            let originalFingerprint = pluginDataForRequest.originalResponse.fingerprint
            let variances = {
                structure: 1 - originalFingerprint.structure.similarity(untamperedFingerPrint.structure),
                content: 1 - originalFingerprint.content.similarity(untamperedFingerPrint.content),
            }

            // dont allow more than 15% variance in structure & 20% variance in content
            if (variances.structure > 0.15 || variances.content > 0.2) {
                logger.log('info', `blindsql - not a potential since there is too much variance in untampered requests : ${JSON.stringify(variances)}`, HaikuUtils.getMetadataForLog(baseAttack))
                return
            }

            // Iterate the vectors in append & replace mode.
            for (let vector of this.conditionalVectors) {
                for (let mode of ['append', 'replace']) {
                    pluginDataForRequest.phase = 'initialANDVectors'

                    // first check if the AND <FALSE> vector indicates difference from original
                    // yield the false case
                    yield mode == 'append' ? baseAttack.paramVal + ' ' + vector.f : vector.f

                    // compute similarity to original.
                    let falseFingerPrint = pluginDataForRequest.fingerPrints
                    let similarities = {
                        vector,
                        mode,
                        paramVal: baseAttack.paramVal,
                        variances,
                        structure: {
                            OF: originalFingerprint.structure.similarity(falseFingerPrint.structure),
                        },
                        content: {
                            OF: originalFingerprint.content.similarity(falseFingerPrint.content),
                        },
                        falseStatusCode: pluginDataForRequest.fingerPrints.statusCode,
                        falseRedirects: pluginDataForRequest.fingerPrints.redirects
                    }

                    // see if the AND <FALSE> vector found anything. If the original & false are very similar => not vulnerable
                    if (similarities.structure.OF > (0.90 - variances.structure) &&
                        similarities.content.OF > (0.85 - variances.content)) {
                        logger.log('info', `blindsql - not a potential since false case is too similar : ${JSON.stringify(similarities)}`, HaikuUtils.getMetadataForLog(baseAttack))
                        continue
                    }

                    // yield the true case
                    yield mode == 'append' ? baseAttack.paramVal + ' ' + vector.t : vector.t
                    if (pluginDataForRequest.originalResponse.statusCode != pluginDataForRequest.fingerPrints.statusCode ||
                        pluginDataForRequest.originalResponse.redirects != pluginDataForRequest.fingerPrints.redirects) {
                        logger.log('info', `blindsql - not a potential since staus code and/or redirect chain do not match in original & true requests`, HaikuUtils.getMetadataForLog(baseAttack))
                        continue
                    }

                    // fill in the other similarties.
                    let trueFingerPrint = pluginDataForRequest.fingerPrints
                    similarities.structure.OT = originalFingerprint.structure.similarity(trueFingerPrint.structure)
                    similarities.structure.TF = trueFingerPrint.structure.similarity(falseFingerPrint.structure)
                    similarities.content.OT = originalFingerprint.content.similarity(trueFingerPrint.content)
                    similarities.content.TF = trueFingerPrint.content.similarity(falseFingerPrint.content)

                    // only if Original-TRUE condition is very similar AND Original-FALSE condition is not similar AND
                    // TRUE-FALSE is ot very similar do we have a potential and go for the validation step
                    if (similarities.structure.OT < (0.95 - variances.structure) && similarities.content.OT < (0.90 - variances.content)) {
                        logger.log('info', `blindsql - not a potential since true case is not similar enough: ${JSON.stringify(similarities)}`, HaikuUtils.getMetadataForLog(baseAttack))
                        continue
                    }

                    // true and false should also not be very similar
                    if (similarities.structure.TF > (0.90 - variances.structure) && similarities.content.TF > (0.85 - variances.content)) {
                        logger.log('info', `blindsql - not a potential since true vs false is too similar: ${JSON.stringify(similarities)}`, HaikuUtils.getMetadataForLog(baseAttack))
                        continue
                    }


                    // now we have a potential so lets validate.
                    similarities.originalFingerprint = originalFingerprint
                    similarities.trueFingerPrint = trueFingerPrint
                    similarities.falseFingerPrint = falseFingerPrint

                    // potential found
                    logger.log('info', `potential blind SQL found, moving to validation phase: ${pluginDataForRequest.similarities}`, HaikuUtils.getMetadataForLog(baseAttack))

                    // validation phase
                    pluginDataForRequest.phase = 'validation'
                    yield* this.getValidationIterator(baseAttack, similarities)

                    // if we confirm the blind SQL, just send a simple attack so we can log the vulnerability
                    // otherwise we will have to set up a lot of the metadata ourselves
                    // see if that makes more sense :-)
                    if (pluginDataForRequest.phase == 'confirmed') {
                        let similaritiesClone = _.cloneDeep(similarities)
                        delete similaritiesClone.originalFingerprint
                        delete similaritiesClone.trueFingerPrint
                        delete similaritiesClone.falseFingerPrint
                        pluginDataForRequest.similarities = similaritiesClone
                        yield mode == 'append' ? baseAttack.paramVal + ' ' + vector.f : vector.f
                        break
                    }
                }

                // found vuln, we are done.
                if (pluginDataForRequest.phase == 'confirmed') {
                    break
                }
            }
        } catch (err) {
            logger.log('error', `Error in bllindSQL vector iterator ${err.toString()}`, HaikuUtils.getMetadataForLog(baseAttack))
        }
    }

    /**
     * Validate that the blind SQL is not a FP by using calculations instead of simple equalities.
     * @param {baseAttack} baseAttack base attack - attack object without vector, encoding.
     * @param {similarities} similarities The similarities data struct contaiing info about the potential Blind SQL to validate
     */
    * getValidationIterator(baseAttack, similarities) {
        try {
            let pluginDataForRequest = this.getPluginScopedStore(baseAttack)
            if (pluginDataForRequest.phase != 'validation') {
                logger.log('info', `Whoa!! validation called but not in validation phase...`, HaikuUtils.getMetadataForLog(baseAttack))
                return
            }

            // Run through true and false cals and ensure that they match the corresponding
            // true/false fingerprints to a high degree (98%)
            similarities.validation = []
            similarities.rejected = []
            let variances = similarities.variances
            for (let truecalc of _ValidationCalcs) {
                // try the true calc case
                let vector = _.clone(similarities.vector.trueFalseTemplate).replace('_OneOrZero_', truecalc)
                yield similarities.mode == 'append' ? baseAttack.paramVal + ' ' + vector : vector
                let fingerPrint = pluginDataForRequest.fingerPrints
                let validation = {
                    trueCalc: {
                        structure: fingerPrint.structure.similarity(similarities.trueFingerPrint.structure),
                        content: fingerPrint.content.similarity(similarities.trueFingerPrint.content),
                        statusCode: fingerPrint.statusCode,
                        redirects: fingerPrint.redirects
                    }
                }
                // Validation should be bang on => 95% for structure and for content adjusting for variance. 
                // For content however, allow minimum 90% 
                if (validation.trueCalc.structure < (0.95 - variances.structure) ||
                    validation.trueCalc.content < Math.min(0.90, (0.95 - variances.content)) ||
                    validation.trueCalc.statusCode != pluginDataForRequest.originalResponse.statusCode ||
                    validation.trueCalc.redirects != pluginDataForRequest.originalResponse.redirects) {
                    logger.log('info', `blindsql - rejecting potential blind SQL since true fingerprint differes by more than 70% or status code/redirects don't match: ${baseAttack.paramVal} - ${vector}`, HaikuUtils.getMetadataForLog(baseAttack))
                    pluginDataForRequest.phase += '-failed'
                    similarities.rejected.push({
                        at: 'true',
                        vector,
                        validation
                    })
                    break
                }

                // try the false calc case. Since all true cases result in 1, just adding a -1 makes the case false
                // and exactly = 0 (in case some servers treat only 0 as false)
                vector = _.clone(similarities.vector.trueFalseTemplate).replace('_OneOrZero_', truecalc + '-1')
                yield similarities.mode == 'append' ? baseAttack.paramVal + ' ' + vector : vector
                fingerPrint = pluginDataForRequest.fingerPrints
                _.set(validation, 'falseCalc.structure', fingerPrint.structure.similarity(similarities.falseFingerPrint.structure))
                _.set(validation, 'falseCalc.content', fingerPrint.content.similarity(similarities.falseFingerPrint.content))
                _.set(validation, 'falseCalc.statusCode', fingerPrint.statusCode)
                _.set(validation, 'falseCalc.redirects', fingerPrint.redirects)

                // should be 95% for structure
                if (validation.falseCalc.structure < (0.95 - variances.structure) ||
                    validation.falseCalc.content < Math.min(0.90, (0.95 - variances.content)) ||
                    validation.falseCalc.statusCode != similarities.falseStatusCode ||
                    validation.falseCalc.redirects != similarities.falseRedirects) {
                    logger.log('info', `blindsql - rejecting potential blind SQL since false fingerprint differes by more than 70% or status code/redirects don't match: ${baseAttack.paramVal} - ${vector}`, HaikuUtils.getMetadataForLog(baseAttack))
                    pluginDataForRequest.phase += '-failed'
                    similarities.rejected.push({
                        at: 'false',
                        vector,
                        validation
                    })
                    break
                }

                // add the stats.
                similarities.validation.push(validation)
            }

            if (pluginDataForRequest.phase == 'validation') {
                pluginDataForRequest.phase = 'confirmed'
            }
        } catch (err) {
            logger.log('error', `Error in blindSQL validation uterator ${err.toString()}`, HaikuUtils.getMetadataForLog(baseAttack))
        }
    }

    /**
     * @override
     */
    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers', 'uri-permutation']
    }

    /**
     * This does not check so much as fingerprint the response and leave it at that.
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.phase == 'confirmed') {
            this.addVulnerabilitytoResult(attack, 'ID-blind-sql-injection', pluginDataForRequest.similarities)
        }

        // Fingerprint the response and save it in the plugin store scoped to crawler request.
        pluginDataForRequest.fingerPrints = this.getFingerprints(attack.result.resp.httpResponse)
    }
}

// Attack vectors.

/** 
 *  Full template will be along the lines of: (xxx) is the templated string in vetor that will be replaced
 *      leading: none,', ", <sp>             <not yet used>
 *      conditional (_COND_): AND, OR        <only AND is supported>
 *      quoting('): none (number), ', "
 *      trailing(#): none, --, #, /*
 *      check (_OneOrZero_): 1, 0
 *  but that explodes the vectors too much eg. with above list, *each* vector will expand to 4*2*3*4*2 = 192
 *  conditional is only AND to prevent dangerous actions being performed by server when OR is used
 *  for now, just using quote, check, trailing => each templated vector generates max 24 vectors
 */
const _ConditionalVectors = [
    `'_COND_ '1'='_OneOrZero_ #`,
    `_COND_ _OneOrZero_ #`,
]

/**
 * True calculations => calculations result in 1. We will compute a FALSE calc by 
 * adding something like -1 or a random digit
 * Decide calculations with care eg. + is uri decoded as ' ', / and <, > may be blocked etc.
 */
const _ValidationCalcs = [
    `2*2-3*1`,
    `1*1*1*1*1*1*1*1*1*1`, // 10 *1
    `100-99`
]

// -- not used
const _ConditionalVectorsFromRahul = [
    `'or'1'='_OneOrZero_`,
    `'and'1'='_OneOrZero_`,
    `AND 1=_OneOrZero_`,
    `OR 1=_OneOrZero_`,
    `AND _OneOrZero_`,
    `OR _OneOrZero_`,
    `_OneOrZero_`,
    `||1=_OneOrZero_`,
    `or "1"="_OneOrZero_"#`,
    `or "1"="_OneOrZero_"/*`,
]


module.exports = BlindSQLInjection