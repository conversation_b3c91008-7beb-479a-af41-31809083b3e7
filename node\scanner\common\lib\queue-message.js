// common class for messages. Most messages should not need to change much
const debug = require('debug')('Messages:QueueMessage')
const assert = require('assert')
const logger = require('./haiku-logger')
/**
 * common class for messages. Most messages should not need to change much
 */
class QueueMessage {
    /**
     * static factory method to create messages of the appropriate type
     * this is why type is the actual implementation filename inside 'messages/'
     * @param {QueueMessage} msg The incoming AMQP message on for which we need to create our message object
     */
    static createMessage(msg) {
        try {
            let type = msg.properties.type
            let msgRequest = new(require('./messages/' + type))()
            msgRequest.parseContent(msg)
            msgRequest.mqMsg = msg
            assert(msgRequest.msgType == msg.properties.type)
            return msgRequest
        } catch (err) {
            logger.log('error',`could not create message ${err}, ${JSON.stringify(msg)}`)
            return null
        }
    }

    /**
     * @param {JSON} content the message content
     */
    constructor(content) {
        if (content) {
            this.setContent(content)
        }
    }

    /**
     * getter
     */
    getContent() {
        return this.content
    }

    /**
     * setter
     */
    setContent(content) {
        this.content = content
    }

    /**
     * Get JSON from the message contents
     * @param {JSON} content incoming message content
     * @param {string} contentType expect application/json now
     */
    deserialize(content, contentType) {
        if (contentType != 'application/json') {
            logger.log('warn',`warning: Base class can only handle JSON content Type, not ${contentType}`)
            // but go ahead and try anyway
        }

        return JSON.parse(content.toString())
    }

    /**
     * parse amqp message contents into JSON 
     * @param {amqpMessage} msg Incoming message to parse content from
     */
    parseContent(msg) {
        this.content = this.deserialize(msg.content, msg.properties.contentType)
    }

    /**
     * Serialize object for publishing to rabbitMQ
     * @param {options} options AMQP options object
     * @param {*} content Message content - will ne JSONified
     */
    serialize(options, content) {
        content = content || this.content
        options.contentType = 'application/json'
        return Buffer.from(JSON.stringify(content))
    }

    /**
     * publish a message to the queue. routing key will be set by the sub classes.
     * @param {RabbitMQ} msgQ the queue instance 
     */
    publish(msgQ) {
        // heres where any common envelope can be done.
        let options = {
            type: this.msgType
        }

        let content = this.serialize(options)
        return msgQ.publish(this.exchange, this.routingKey, content, options)
    }
}

module.exports = QueueMessage