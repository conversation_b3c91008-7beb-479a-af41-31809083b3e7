const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const cheerio = require('cheerio')

// Optimize sensitive attributes by removing duplicates
const sensitiveAttributes = {
    urlAttributes: {
        'src': ['iframe', 'img', 'script', 'embed', 'source', 'track', 'video', 'audio', 'input'],
        'href': ['a', 'link', 'base', 'area'],
        'action': ['form'],
        'data': ['object'],
        'codebase': ['object'],
        'archive': ['object'],
        'classid': ['object'],
        'usemap': ['img', 'input', 'object'],
        'longdesc': ['img', 'frame', 'iframe'],
        'poster': ['video'],
        'background': ['body', 'table', 'td', 'th'],
        'cite': ['blockquote', 'del', 'ins', 'q'],
        'formaction': ['button', 'input'],
        'icon': ['command'],
        'manifest': ['html'],
        'ping': ['a', 'area'],
        'srcset': ['img', 'source']
    },
    // Value attributes for checking unquoted attribute values
    valueAttributes: {
        'value': ['input', 'button', 'option', 'param']
    },
    // Event handler attributes for checking event handler injection
    eventAttributes: {
        'onabort': ['*'],
        'onblur': ['*'],
        'onchange': ['*'],
        'onclick': ['*'],
        'ondblclick': ['*'],
        'onerror': ['*'],
        'onfocus': ['*'],
        'onkeydown': ['*'],
        'onkeypress': ['*'],
        'onkeyup': ['*'],
        'onload': ['*'],
        'onmousedown': ['*'],
        'onmousemove': ['*'],
        'onmouseout': ['*'],
        'onmouseover': ['*'],
        'onmouseup': ['*'],
        'onreset': ['*'],
        'onselect': ['*'],
        'onsubmit': ['*'],
        'onunload': ['*']
    }
}
// Optimize attack vectors by combining similar patterns and removing redundant ones
const _attackVectors = {
    // URL-based injection vectors (for src, href, action, etc.)
    urlVectors: [
        'indusface9868',  // Changed from (indusface9868) to avoid FN/FP
        'javascript:haikumsg(1)',
        'data:text/html,<script>haikumsg(1)</script>'
    ],

    // Event handler injection vectors - consolidated common patterns
    eventVectors: [
        'haikumsg(1)',
        'console.log(haikumsg(1))'
    ],

    // HTML attribute injection vectors - optimized patterns
    attributeVectors: [
        'x" onerror="haikumsg(1)',
        'x" onmouseover="haikumsg(1)',
        'x" onload="haikumsg(1)'
    ],

    // Unquoted attribute value vectors - consolidated patterns
    unquotedVectors: [
        'test onclick=haikumsg(1)',
        'test onmouseover=haikumsg(1)',
        'test onload=haikumsg(1)'
    ],

    // HTML injection vectors - optimized patterns
    htmlVectors: [
        '"><script>haikumsg(1)</script>',
        '"><img src=x onerror=haikumsg(1)>',
        '"><svg onload=haikumsg(1)>'
    ],

    // DOM Location injection attack vectors - optimized patterns
    domlocVectors: [
        'http://www.haikumsg.com',
        'https://www.haikumsg.com',
        'http://www.haikumsg.com/test',
        'https://www.haikumsg.com/test',
        // Protocol-relative URLs
        '//www.haikumsg.com',
        '//www.haikumsg.com/test',

    ]
}

// Optimize DOM location identifiers by removing duplicates
const domlocidentifiers = [
    'location',
    'window.location',
    'document.location',
    'location.href',
    'location.assign',
    'location.replace',
    'location.reload',
    'location.search',
    'location.hash',
    'location.pathname',
    'location.hostname',
    'location.origin',
    'location.protocol',
    'location.port'
]

class UserControllableTagsParameters extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-user-controllable-tag-parameter'
        this.partialusercont = 'ID-partial-user-controllable-script'
        this.crosssiteflashing = 'ID-cross-site-flashing'

        // Compile regex patterns once
        this._patterns = {
            staticResource: /\.(?:js|css|jpg|jpeg|png|gif|ico|woff|woff2|ttf|eot|svg|txt|pdf|doc|docx|xls|xlsx|zip|rar|tar|gz|mp3|mp4|avi|mov|wmv|flv|swf|exe|dll|sys|bin|dat|log|ini|conf|xml|json|yaml|yml|md|rst|asc|key|pem|crt|cer|p12|pfx|p7b|p7c|p7m|p7s|spc|sst|stl|der|csr|crl|ocsp)$/i,
            excludePaths: /\/blog\/|\/doc\/|\/docs\/|\/documentation\/|\/help\/|\/support\/|\/faq\/|\/about\/|\/contact\/|\/privacy\/|\/terms\/|\/legal\/|\/sitemap\/|\/robots\.txt|\/humans\.txt|\/security\.txt/i,
            vulnerableTag: /<(?:\s*)(?:iframe|a|img|form|input|link|base|script|param|embed)(?:\s+[^>]*)?>/i,
            domLocation: /(?:^|\s)(?:location|window\.location|document\.location|location\.href|location\.assign|location\.replace|location\.reload)(?:\s|$)/i
        }
    }

    getAttackVectors() {
        // Combine all attack vectors into a single array
        return [
            ..._attackVectors.urlVectors,
            ..._attackVectors.eventVectors,
            ..._attackVectors.attributeVectors,
            ..._attackVectors.unquotedVectors,
            ..._attackVectors.htmlVectors,
            ..._attackVectors.domlocVectors
        ]
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: [
                'Referer', 'User-Agent', 'Host', 'Origin',
                'X-Forwarded-Host', 'X-Host', 'X-Forwarded-Server',
                'X-HTTP-Host-Override', 'Forwarded', 'Cookie',
                'Accept-Language', 'X-Forwarded-For'
            ]
        })
    }

    getAttackableEvents() {
        // return ['http-headers']
        return ['http-headers', 'uri-query-params', 'form-encoded-post']
    }

    async performNetworkAttack(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.userControllableTagVuln &&
            pluginDataForRequest.partialUserContScript &&
            pluginDataForRequest.userContDOMLocVuln) return false;

        return await super.performNetworkAttack(attack)
    }

    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.userControllableTagVuln &&
            pluginDataForRequest.partialUserContScript &&
            pluginDataForRequest.userContDOMLocVuln) return

        if (attack.pluginName !== this.getName()) return false

        // Optimize static resource exclusion
        if (this._patterns.staticResource.test(attack.href)) return false

        // Optimize path exclusion
        if (this._patterns.excludePaths.test(attack.href)) return false

        const responseBody = _.get(attack, "result.resp.body").trim().toLowerCase()
        if (!responseBody || typeof responseBody !== 'string') return false

        if (!responseBody.includes('<html')) return false
        if (!this._patterns.vulnerableTag.test(responseBody) &&
            !this._patterns.domLocation.test(responseBody)) return false

        // Use a single cheerio instance for all parsing
        const page = cheerio.load(responseBody, {
            baseURI: attack.href,
            decodeEntities: false
        })
        const vector = attack.vector

        // Find all elements containing our payloads
        const elementsWithPayload = this._findElementsWithPayload(page, vector)
        if (elementsWithPayload.length === 0) return false

        // Process vulnerabilities
        let results = {
            details: null,
            dts_src: null,
            dts_xsf: null,
            domloc_dts: null
        }

        // Check each element for vulnerabilities
        for (const element of elementsWithPayload) {
            // Stop if we've found all types of vulnerabilities
            if (pluginDataForRequest.userControllableTagVuln &&
                pluginDataForRequest.partialUserContScript &&
                pluginDataForRequest.userContDOMLocVuln) break
            // Check for user controllable tag vulnerability
            if (!pluginDataForRequest.userControllableTagVuln) {
                const tagVuln = this._checkUserControllableTag(element, vector)
                if (tagVuln) {
                    results.details = tagVuln
                    pluginDataForRequest.userControllableTagVuln = true
                }
            }

            // Check for partial script source vulnerability
            if (!pluginDataForRequest.partialUserContScript && element.type === 'script') {
                const scriptVuln = this._checkScriptInjection(element, vector)
                if (scriptVuln) {
                    results.dts_src = scriptVuln
                    pluginDataForRequest.partialUserContScript = true
                }
            }

            // Check for Flash vulnerability
            if (!pluginDataForRequest.XSFVulnFound && element.type === 'flash') {
                const flashVuln = this._checkFlashVars(element, vector)
                if (flashVuln) {
                    results.dts_xsf = flashVuln
                    pluginDataForRequest.XSFVulnFound = true
                }
            }

            // Check for DOM location was injected successfully
            if (!pluginDataForRequest.userContDOMLocVuln && element.type === 'domloc') {
                const domlocVuln = this._checkDOMLocation(element, vector)
                if (domlocVuln) {
                    results.domloc_dts = domlocVuln
                    pluginDataForRequest.userContDOMLocVuln = true
                }
            }
        }

        // Report vulnerabilities if found
        if (results.details || results.dts_src || results.dts_xsf || results.domloc_dts) this._reportVulnerabilities(attack, results, pluginDataForRequest)
    }

    _findElementsWithPayload(page, vector) {
        const elements = []

        // Find elements with URL attributes containing payloads
        for (const [attr, tags] of Object.entries(sensitiveAttributes.urlAttributes)) {
            const selector = tags.map(tag => `${tag}[${attr}]`).join(',')
            page(selector).each((i, el) => {
                const $el = page(el)
                const val = $el.prop(attr) || $el.attr(attr) // Try prop first, fallback to attr
                if (val && this._isExactMatch(val, vector)) {
                    // Store all necessary information for later use
                    elements.push({
                        el: $el,
                        type: 'url',
                        attr,
                        value: val,
                        tagName: $el.prop('tagName')?.toLowerCase() || 'unknown',
                        text: $el.text(),
                        html: $el.html()
                    })
                }
            })
        }

        // Find elements with event handlers containing payloads
        for (const [attr, tags] of Object.entries(sensitiveAttributes.eventAttributes)) {
            const selector = `[${attr}]`
            page(selector).each((i, el) => {
                const $el = page(el)
                const val = $el.prop(attr) || $el.attr(attr) // Try prop first, fallback to attr
                if (val && this._isExactMatch(val, vector)) {
                    elements.push({
                        el: $el,
                        type: 'event',
                        attr,
                        value: val,
                        tagName: $el.prop('tagName')?.toLowerCase() || 'unknown',
                        text: $el.text(),
                        html: $el.html()
                    })
                }
            })
        }

        // Find script tags containing payloads
        page('script').each((i, el) => {
            const $el = page(el)
            const src = $el.prop('src') || $el.attr('src') // Try prop first, fallback to attr
            const content = $el.html()
            if ((src && this._isExactMatch(src, vector)) ||
                (content && this._isExactMatch(content, vector))) {
                elements.push({
                    el: $el,
                    type: 'script',
                    src,
                    content,
                    html: $el.html()
                })
            }
        })

        // Find FlashVars elements containing payloads
        page('embed[FlashVars], embed[flashvars], param[name=FlashVars], param[name=flashvars]').each((i, el) => {
            const $el = page(el)
            const val = $el.prop('FlashVars') || $el.prop('flashvars') || $el.attr('FlashVars') || $el.attr('flashvars')
            if (val && this._isExactMatch(val, vector)) {
                elements.push({
                    el: $el,
                    type: 'flash',
                    value: val,
                    html: $el.html()
                })
            }
        })

        // Find DOM location elements containing payloads
        const domlocSelector = domlocidentifiers.map(id =>
            `input[name*="${id}"], input[name*="${id}.href"], input[name*="${id}.assign"], input[name*="${id}.replace"]`
        ).join(',')

        page(domlocSelector).each((i, el) => {
            const $el = page(el)
            const name = $el.prop('name') || $el.attr('name')
            const val = $el.prop('value') || $el.val() // Try prop first, then val()
            if (name && val && this._isExactMatch(val, vector)) {
                elements.push({
                    el: $el,
                    type: 'domloc',
                    name,
                    value: val,
                    html: $el.html()
                })
            }
        })

        return elements
    }

    _checkUserControllableTag(element, vector) {
        const { el, type, attr, value, tagName, text } = element
        if (!el || type !== 'url' && type !== 'event') return null

        if (type === 'url') {
            // We already know the value matches from _findElementsWithPayload
            if (value.toLowerCase().startsWith('javascript:')) {
                return `[JavaScript URL Injection] ${tagName} tag with ${attr}="${value}" contains ${vector} - Element: <${tagName} ${attr}="${value}">${text}</${tagName}>`
            }
            if (value.toLowerCase().startsWith('data:')) {
                return `[Data URL Injection] ${tagName} tag with ${attr}="${value}" contains ${vector} - Element: <${tagName} ${attr}="${value}">${text}</${tagName}>`
            }
            return `[URL Parameter Injection] ${tagName} tag with ${attr}="${value}" contains ${vector} - Element: <${tagName} ${attr}="${value}">${text}</${tagName}>`
        } else if (type === 'event') {
            return `[Event Handler Injection] ${tagName} tag with ${attr}="${value}" contains ${vector} - Element: <${tagName} ${attr}="${value}">${text}</${tagName}>`
        }

        return null
    }

    _checkScriptInjection(element, vector) {
        const { el, type, src, content, html } = element
        if (!el || type !== 'script') return null

        if (src) {
            return `[Script Source Injection] Script tag with src="${src}" contains ${vector} - Element: ${html}`
        }

        if (content) {
            return `[Script Content Injection] Script tag contains ${vector} - Element: ${html}`
        }

        return null
    }

    _checkFlashVars(element, vector) {
        const { el, type, value, html } = element
        if (!el || type !== 'flash') return null

        return `[FlashVars Injection] FlashVars contains ${vector} (value: ${value}) - Element: ${html}`
    }

    _checkDOMLocation(element, vector) {
        const { el, type, name, value, html } = element
        if (!el || type !== 'domloc') return null

        // Skip if the value is a JavaScript expression
        if (value.includes('+') || value.includes('document.') || value.includes('window.') ||
            value.includes('(') || value.includes(')') || value.includes(';')) {
            return null
        }

        // Skip if it's a relative URL or path
        if (value.startsWith('/') || value.startsWith('./') || value.startsWith('../') ||
            !value.includes('://') && !value.includes('.')) {
            return null
        }

        // Additional validation to prevent false positives
        try {
            const url = new URL(value, 'http://dummybase');
            // Skip if it's a safe domain
            if (url.hostname === 'dummybase' ||
                url.hostname === 'localhost' ||
                url.hostname === '127.0.0.1') {
                return null;
            }
            return `[DOM Location Injection] Input with name="${name}" (value: ${value}) contains ${vector} - Element: ${html || ''}`
        } catch (e) {
            // If URL parsing fails, it's not a valid URL, so not a vulnerability
            return null;
        }
    }

    _isExactMatch(val, vector) {
        // Convert values to strings and do initial validation
        val = String(val || '').toLowerCase()
        vector = String(vector || '').toLowerCase()
        if (!val || !vector || !val.includes(vector)) return false

        const result = val.includes(vector)
        // console.log(`${attr} - ${val} - ${vector} - ${result}`)
        return result
    }

    _reportVulnerabilities(attack, results, pluginDataForRequest) {
        if (results.details && pluginDataForRequest.userControllableTagVuln) {
            let title = "User Controllable HTML Attribute"
            const url = results.details.match(/(?:href|src|action)="([^"]+)"/)?.[1] || ''
            const attr = results.details.match(/(href|src|action|on[a-z]+|value|name)="?/i)?.[1] || ''

            // Skip if no attack vector is found
            if (!attack.vector || !url.includes(attack.vector.toLowerCase())) {
                return;
            }

            // Determine vulnerability type and title
            title = this._determineVulnerabilityType(url, attr, attack);

            const details = `${results.details}\nImpact: {"attackType": "${title}", "risk": "Script execution, DOM manipulation, URL manipulation, unauthorized redirects, path traversal, data theft", "severity": "High"}`
            this.reportUserControllableTagVuln(attack, details)
        }

        // Report other vulnerabilities if found
        if (results.dts_src && pluginDataForRequest.partialUserContScript) {
            if (!attack.vector || !results.dts_src.includes(attack.vector.toLowerCase())) return;
            const details = `${results.dts_src}\nImpact: {"attackType": "Partial User Controllable Script", "risk": "Script injection, unauthorized code execution", "severity": "High"}`
            this.reportpartialUserContScript(attack, details)
        }

        if (results.dts_xsf && pluginDataForRequest.XSFVulnFound) {
            if (!attack.vector || !results.dts_xsf.includes(attack.vector.toLowerCase())) return;
            const details = `${results.dts_xsf}\nImpact: {"attackType": "Cross-Site Flashing", "risk": "Flash object manipulation, unauthorized actions", "severity": "Medium"}`
            this.reportXSFVuln(attack, details)
        }

        if (results.domloc_dts && pluginDataForRequest.userContDOMLocVuln) {
            if (!attack.vector || !results.domloc_dts.includes(attack.vector.toLowerCase())) return;
            const details = `${results.domloc_dts}\nImpact: {"attackType": "DOM Location Injection", "risk": "URL manipulation, phishing, redirect attacks", "severity": "Medium"}`
            this.reportDOMLocationVuln(attack, details)
        }
    }

    _determineVulnerabilityType(url, attr, attack) {
        // Check for indusface9868 payload
        if (url.includes('indusface9868')) {
            try {
                const urlObj = this._parseUrl(url, attack.hostname);
                const { hostname, pathname, searchParams } = urlObj;

                // Check for open redirect in search parameters
                if (searchParams.has('indusface9868')) {
                    for (const [param, value] of searchParams.entries()) {
                        if (/(redirect|url|next|return|continue|dest|target)/i.test(param) &&
                            value && value.includes('indusface9868')) {
                            return this._getVulnerabilityTitle('open_redirect', attr);
                        }
                        // Check for object reference injection
                        if (/(id|ref|object|obj|reference)/i.test(param) &&
                            value && value.includes('indusface9868')) {
                            return this._getVulnerabilityTitle('object_reference', attr);
                        }
                    }
                }

                // Check for host header injection
                if (hostname === 'indusface9868' || hostname.endsWith('.indusface9868') ||
                    (!url.startsWith('http://') && !url.startsWith('https://') && url === 'indusface9868')) {
                    return this._getVulnerabilityTitle('host_header', attr);
                }

                // Check for path traversal
                if (pathname.includes('/indusface9868/') || pathname.endsWith('/indusface9868') ||
                    (!url.startsWith('http://') && !url.startsWith('https://') && url.includes('/indusface9868/'))) {
                    return this._getVulnerabilityTitle('path_traversal', attr);
                }

                // Default URL parameter injection
                return this._getVulnerabilityTitle('url_parameter', attr);
            } catch (e) {
                // If URL parsing fails, check for raw value
                if (url === 'indusface9868') {
                    return this._getVulnerabilityTitle('host_header', attr);
                }
            }
        }

        // Check for XSS/HTML Injection
        if (this._isXSSVulnerable(url, attr, attack)) {
            return this._getVulnerabilityTitle('xss', attr);
        }

        // Default title
        return "User Controllable HTML Attribute";
    }

    _parseUrl(url, hostname) {
        let urlToCheck = url;
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            urlToCheck = url.startsWith('/')
                ? `http://${hostname}${url}`
                : `http://${hostname}/${url}`;
        }
        return new URL(urlToCheck);
    }

    _getVulnerabilityTitle(type, attr) {
        const titles = {
            open_redirect: {
                href: "Possible Open Redirect via User-Controllable Link",
                src: "Possible Open Redirect via User-Controllable Script Source",
                action: "Possible Open Redirect via User-Controllable Form Action",
                default: "Possible Open Redirect via User-Controllable HTML Attribute"
            },
            host_header: {
                href: "Possible Host Header Injection via User-Controllable Link",
                src: "Possible Host Header Injection via User-Controllable Script Source",
                action: "Possible Host Header Injection via User-Controllable Form Action",
                default: "Possible Host Header Injection via User-Controllable HTML Attribute"
            },
            path_traversal: {
                href: "Possible Path Traversal via User-Controllable Link",
                src: "Possible Path Traversal via User-Controllable Script Source",
                action: "Possible Path Traversal via User-Controllable Form Action",
                default: "Possible Path Traversal via User-Controllable HTML Attribute"
            },
            object_reference: {
                href: "Possible Object Reference Injection via User-Controllable Link",
                src: "Possible Object Reference Injection via User-Controllable Script Source",
                action: "Possible Object Reference Injection via User-Controllable Form Action",
                default: "Possible Object Reference Injection via User-Controllable HTML Attribute"
            },
            url_parameter: {
                href: "URL Parameter Injection via User-Controllable Link",
                src: "URL Parameter Injection via User-Controllable Script Source",
                action: "URL Parameter Injection via User-Controllable Form Action",
                default: "URL Parameter Injection via User-Controllable HTML Attribute"
            },
            xss: {
                href: "Possible XSS/HTML Injection via User-Controllable Link",
                src: "Possible XSS/HTML Injection via User-Controllable Script Source",
                action: "Possible XSS/HTML Injection via User-Controllable Form Action",
                default: "Possible XSS/HTML Injection via User-Controllable HTML Attribute"
            }
        };

        return titles[type]?.[attr] || titles[type]?.default || "User Controllable HTML Attribute";
    }

    _isXSSVulnerable(url, attr, attack) {
        // Skip if it's a known safe value
        if (attr.includes('indusface9868')) {
            return false;
        }

        // Skip if no attack vector is found
        if (!attack.vector) {
            return false;
        }

        // Check for event handler injection
        if (attr.startsWith('on')) {
            const handlerContent = attr.substring(2);
            return handlerContent.includes(attack.vector.toLowerCase()) &&
                !handlerContent.includes('function') &&
                !handlerContent.includes('return');
        }

        // Check for script tag injection
        if (url.includes('<script')) {
            const scriptContent = url.match(/<script[^>]*>([^<]*)<\/script>/i)?.[1] || '';
            return scriptContent.includes(attack.vector.toLowerCase()) &&
                !scriptContent.includes('function') &&
                !scriptContent.includes('return');
        }

        // Check for attribute value injection
        if (attr) {
            const hasPayloadBreakout = /(<|>|"|')/.test(url);
            const hasVectorInjection = attr.includes(attack.vector.toLowerCase());
            const isSafeValue = /^[a-zA-Z0-9\-_\.]+$/.test(attr);
            const isStaticResource = /\.(js|css|jpg|jpeg|png|gif|ico|woff|woff2|ttf|eot|svg)$/i.test(attr);

            return (hasPayloadBreakout || hasVectorInjection) &&
                !isSafeValue &&
                !isStaticResource;
        }

        return false;
    }

    reportUserControllableTagVuln(attack, details) {
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
    }

    reportpartialUserContScript(attack, dts_src) {
        this.addVulnerabilitytoResult(attack, this.partialusercont, dts_src)
    }

    reportXSFVuln(attack, dts_xsf) {
        this.addVulnerabilitytoResult(attack, this.crosssiteflashing, dts_xsf)
    }

    reportDOMLocationVuln(attack, domloc_dts) {
        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, domloc_dts)
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID)

        if (vulnID === this.vulnerabilityID || vulnID === this.partialusercont) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"])
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['indusface', 'https://www.indusface.com'])
        }
    }
}

module.exports = UserControllableTagsParameters