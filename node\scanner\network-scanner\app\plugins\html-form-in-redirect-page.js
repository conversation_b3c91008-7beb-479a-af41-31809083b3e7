const debug = require('debug')('HtmlFormInRedirectPage')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')

class HtmlFormInRedirectPage extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-html-form-found-in-redirect'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {

        // check if form present in response body, then only call processAttackResponse
        let body = _.get(attack, "result.resp.body")
        if (/<form\s/i.test(body)) {
            return true
        }
        return false
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //if vuln detected for a req then return

        if (pluginDataForRequest.HtmlFormInRedirectPageVuln) {
            return
        }
        let vuln = ''
        let $ = _.get(attack, 'result.resp.httpResponse.cheerio');
        let forms = $("form")
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (forms.length > 0 && (statusCode == 302 || statusCode == 301)) {
            let form_ids = ""
            forms.each(function (index) {
                form_ids += $(this).attr('name') + " "
            })
            let details = "status: " + statusCode + " form :" + form_ids

            vuln = {
                name: 'HTML Form found in Redirect Page',
                details: details
            }
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginDataForRequest.HtmlFormInRedirectPageVuln = true
        }
    }
}

module.exports = HtmlFormInRedirectPage