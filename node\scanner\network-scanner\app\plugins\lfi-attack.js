const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class LFIAttack extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-local-file-inclusion'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(lfiMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of LFI attack vectors
     * @override
     */
    getAttackVectors() {
        return lfiVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params', 'uri-path-iterator']
    }

    /**
     * Will check if response has data from specific (common) local files.
     * See: {@link https://www.owasp.org/index.php/Testing_for_Local_File_Inclusion}
     * 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        // only check response for the attack sent by this specific plugin
        if (attack.pluginName != this.getName()) {
            return
        }
        this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
    }
}

// vectors & matches ...
const lfiVectors = [
    //below are file vectors for unix
    `/etc/passwd`,
    `/boot.ini\x00.htm`,
    `../../../../../../../../../../boot.ini`,
    `../../../../../../../../../../boot.ini\x00.htm`,
    `../../../../../../../../../../etc/passwd`,
    `../../../../../../../../../../etc/passwd\x00.htm`,
    `../../../../../etc/passwd`, //174759134

    //below are file vectors for windows
    `/windows\/win.ini`,
    `\/\/windows\/system.ini`,
    `../../../../../../../../windows\/win.ini`,
    `../../../../../../../../windows\/system.ini`,
    `..\/\/..\/\/..\/\/..\/\/..\/\/windows\/win.ini`,
    `..\/\/..\/\/..\/\/..\/\/..\/\/windows\/system.ini`,
    `..\/\/..\/\/..\/\/..\/\/..\/\/windows\/system32\/drivers\/etc\/hosts`,

]

const lfiMatch = [
    /\[boot loader\]/,
    /\[operating systems\]/,
    /multi\(0\)disk\(0\)rdisk\(1\)partition\(1\)/,
    /bin:x:1:1:Binary/,
    /daemon:\/sbin:\/sbin\/nologin/,
    /adm:\/var\/adm:\/sbin\/nologin/,
    /adm:\/var\/adm:\//,
    /var\/spool\/lpd:/,

    /EGA80WOA.FON=EGA80WOA.FON|woafont=dosapp.fon|CGA80WOA.FON=CGA80WOA.FON/i, //for system.ini detection
    /MAPI=1|mci\sextensions/i, //for win.ini detection
    /\sThis\sis\sa\ssample\sHOSTS\sfile\sused\sby\sMicrosoft\sTCP\/IP\sfor\sWindows./i, //for hosts detection
]

module.exports = LFIAttack