// Load the SDK and UUID
const AWS = require('aws-sdk');
const fs = require('fs')
const path = require('path')
const debug = require('debug')('HaikuS3Utils')
const s3AwsUtils = require('../../common/lib/s3-cloud-util')
const s3FileUtils = require('../../common/lib/s3-file-utils')
const efsFileUtils = require('../../common/lib/efs-file-utils')
const HaikuBucket = process.env['HAIKU_BUCKET'] || 'haiku-store'
const HaikuUtils = require('../../common/lib/haiku-utils')
const logger = require('../../common/lib/haiku-logger');
const stream = require('stream');

/**
 * Utility for S3 operations. Haiku will use S3 as an external store both for output like logs and
 * for data like list of URLs to try at start of crawl.
 * Promise based where possible
 */
class HaikuS3Utils {
    /**
     * 
     * @param {options} options Need to define options
     */
    constructor(options) {
        this.options = options
        this.s3 = new AWS.S3() // credentials from common init file ~/.aws/credentials
        this.fileImpl = (process.env['HAIKU_LOCAL_S3']) ? s3FileUtils : efsFileUtils
        this.backupFileImpl =  s3AwsUtils;
    }

    /**
     * get a 'file' from S3
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname like crawler-metrics.json
     */
    async getFile(prefix, name) {
        let fileContent
        
        try {
            fileContent = await this.fileImpl.getFile(prefix, name);   
        } catch (err) {
            logger.log('error', `getFile() failed trying S3 : ${err.toString()}`)        
        }

        if(!fileContent) {
            try {
                fileContent = await this.backupFileImpl.getFile(prefix, name);
            } catch (e) {
                logger.log('error', `getFile() failed from s3 ${e.toString()}`)
            }
        }

        return fileContent
    }


    /**
     * get a 'file' from S3 synchronously
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname like crawler-metrics.json
     */
    downloadFileSync(prefix, name, targetDir) {
        let filename = path.resolve(targetDir, name)
        this.getFile(prefix, name).then((data) => {
            fs.writeFileSync(filename, data.Body)
        }).catch(debug)
    }

    /**
     * Upload data to S3
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname (analogous to filename) like crawler-metrics.json
     * @param {any} Body the data to upload (Buffer, Typed Array, Blob, String, ReadableStream)
     * @param {any} additionalOptions any extra fields to pass in params eg. content-type 
     * See: https://docs.aws.amazon.com/AWSJavaScriptSDK/latest/AWS/S3.html#upload-property
     */
    async upload(prefix, name, Body, additionalOptions = {}) {
        return this.fileImpl.upload(prefix, name, Body, additionalOptions)
    }

    /**
     * Upload a file to S3
     * @param {string} prefix prefix of the AWS key eg. crawler/logs/123/5678/
     * @param {string} name the keyname like crawler-metrics.json
     * @param {string} filename the local file to upload
     * @param {any} additionalOptions any extra fields to pass in params eg. content-type 
     */
    async uploadFile(prefix, name, filename, additionalOptions = {}) {
        try {
            return this.upload(prefix, name, fs.createReadStream(filename), additionalOptions)
        } catch (err) {
            return err
        }
    }

}

const haikuS3Utils = new HaikuS3Utils()
module.exports = haikuS3Utils