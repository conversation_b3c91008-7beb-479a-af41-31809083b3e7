const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const http = require('http')

class InsecureHTTPTransport extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        /** However, the problem occurs when a web application allows users to access a website via "HTTP" instead of "HTTPS" and does not automatically redirect users to HTTPS.*/
        this.vulnerabilityID = 'ID-insecure-http-transport'
    }

    getAttackVectors() {
        return nothingToAttack
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    async performNetworkAttack(attack) {
        let respStatus = _.get(attack, 'originalRequest.httpResponse.statusCode')
        let redirect = _.get(attack, 'originalRequest.httpResponse.redirects', '')
        if (attack.httpRequest.uri.includes('https://') && respStatus == 200 && attack.attackArea == "HTTPMethods" && attack.originalRequest.httpRequest.method == 'GET' && redirect.length == 0) {
            attack.httpRequest.uri = attack.httpRequest.uri.replace('https:', 'http:')
            return await super.performNetworkAttack(attack)
        }
    }

    wantProcessAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let ResBody = _.get(attack, "result.resp.body", '')
        if (attack.attackArea == 'original-crawler-request' && ResBody.length > 0) {
            pluginDataForRequest.insecureCount = ResBody.length
        }
        if (attack.attackArea == "HTTPMethods" && attack.pluginName == this.getName() && pluginDataForRequest.insecureCount == ResBody.length) {
            let protocol = _.get(attack, 'result.req.parsedURL.protocol');
            // attack.result.resp.httpResponse.redirects[0].redirectedUri
            let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
            let stacode = _.get(attack, "result.resp.httpResponse.statusCode")
            let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
            let location = _.get(attack, "result.resp.httpResponse.headers.location", '')

            if (protocol == 'http:' && attack.httpRequest.method == 'GET' && !redirectedUri.includes('https:') && OristatusCode == stacode && !location.includes('https:')) {
                return true
            }
        }
        return false
    }

    async processAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginStorageScanScope.InsecureHTTPVulnFound) {
            return
        }
        let redirect = _.get(attack, 'originalRequest.httpResponse.redirects', '')
        let redirectURL = _.get(attack, 'result.resp.httpResponse.redirectsFollowed', '')
        let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')

        if (redirect.length == 0 && redirectURL >= 1 && !redirectedUri.includes('https:')) {
            let resheaderdts = await this.getResdts(attack.href)
            if (resheaderdts == 'return') {
                return false
            }
        }
        let ResBody = _.get(attack, "result.resp.body")

        /**
         * ;header("Location: http://{{scannerVector}}.haikuscan.indusfacefinder.in");exit;`,
         * `;response.sendRedirect("http://{{scannerVector}}.haikuscan.indusfacefinder.in");`, //java
         * `;Response.Redirect("http://{{scannerVector}}.haikuscan.indusfacefinder.in")`, //ASP.NET C#
         * `'"><head><meta http-equiv="Refresh" content="0; URL=http://{{scannerVector}}.haikuscan.indusfacefinder.in" /></head>`, //HTML redirections
         * `;redirect_to "http://{{scannerVector}}.haikuscan.indusfacefinder.in"`, //Rails
         */
        if (!/(?:Location:|Redirect ?\((?:"|')|url ?=|redirect_to ?") ?https:/i.test(ResBody)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `Insecure HTTP Transport URL: ${attack.href}`)
            pluginStorageScanScope.InsecureHTTPVulnFound = true
        }
    }

    getResdts(url) {
        return new Promise((resolve) => {
            http.get(url, (res) => {
                // ststuscode >=300 && ststuscode <=308
                if (res.statusCode >= 300 && res.statusCode <= 308) {
                    if (/https:/i.test(res.headers.location)) {
                        resolve('return');
                    }
                    else {
                        resolve("HaikuTestSkip")
                    }
                }
                else {
                    resolve("HaikuTestSkip")
                }
            });
        })
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID == this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        }
    }
}

const nothingToAttack = [
    VectorResponseAttack.identityVector
]

module.exports = InsecureHTTPTransport