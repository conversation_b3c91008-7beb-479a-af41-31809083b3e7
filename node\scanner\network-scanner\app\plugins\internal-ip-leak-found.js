const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')
class InternalIPLeakFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-internal-ip-leak-found'
        // from below regex removing the loopback address 127.0.0.1 to remove for FPs
        this.matchRegexp = new RE2(/(10\.([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5])\.([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5])\.(([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5]){1,3}))|(172\.([1][6-9]|[2][0-9]|[3][0-1])\.([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5])\.(([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5]){1,3}))|(192\.168\.([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5])\.(([0-9]|[1-9][0-9]|[1][0-9][0-9]|[2][0-4][0-9]|[2][5][0-5]){1,3}))/g)        
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        this.checkBodyForVuln(originalRequest, this.matchRegexp, this.vulnerabilityID)
    }
}
module.exports = InternalIPLeakFound
