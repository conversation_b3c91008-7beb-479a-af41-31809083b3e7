const debug = require('debug')('REST:messageQ')
const RabbitMQ = require('../common/lib/rabbitmq')
const CrawlStartedRpc = require('../common/lib/messages/crawl-started-rpc')
const CrawlFinished = require('../common/lib/messages/crawl-finished')
const AttackableNetworkRequest = require('../common/lib/messages/attackable-nw-request')
const PauseScan = require('../common/lib/messages/pause-scan')
const logger = require('../common/lib/haiku-logger')
const productName = 'scanapi'

// Simple logging of request
function __logAndSendResponse(req, res, respJSON) {
    // send the response so other processing continues
    res.json(respJSON)

    // now log    
    let reqJSON = {
        method: req.method,
        url: req.originalUrl,
        params: req.params,
        body: req.body ? JSON.stringify(req.body).slice(0, 200) : req.body, // limit output
    }

    let logObject = {
        request: reqJSON,
        response: respJSON
    }
    logger.log('info', `${JSON.stringify(logObject)}`)
}

/**
 * Haiku MQ specific code 
 */
class HaikuMessageQueue {
    /**
     * @constructor
     */
    constructor() {
        this.rabbitMsgQ = new RabbitMQ('scanapi')
    }

    /**
     * initialize the msg queue
     * @param {app} Express app to add the handlers
     */
    init(app) {
        // init the message Queue
        this.rabbitMsgQ.init()

        // app POST handlers
        this.relayToMsgQ(app, '/relay/CrawlStartedRpc', CrawlStartedRpc, true)
        this.relayToMsgQ(app, '/relay/CrawlFinished', CrawlFinished)
        this.relayToMsgQ(app, '/relay/AttackableNetworkRequest', AttackableNetworkRequest)
        this.relayToMsgQ(app, '/relay/PauseScan', PauseScan)

        // Network actions is array of attackable requests
        app.post('/relay/NetworkActions', function (req, res) {
            let networkActionInfo = req.body
            let rets = []
            for (let networkAction of networkActionInfo.networkActions) {
                let attackableRequest = {
                    scanId: networkActionInfo.scanId,
                    scanlog_id: networkActionInfo.scanlog_id,
                    scanner: networkActionInfo.scanner,
                    httpRequest: networkAction
                }
                let msg = new AttackableNetworkRequest(attackableRequest)
                rets.push(msg.publish(this.rabbitMsgQ))
            }

            __logAndSendResponse(req, res, {
                status: "OK",
                message: `DEPRECATED: Queued ${networkActionInfo.networkActions.length} requests ...`,
                details: rets.join(',')
            })

        }.bind(this))
    }

    /**
     * Helper to relay from REST to message Queue. Will relay the JSON body as is to the message 
     * @param {app} app 
     * @param {string} endpoint REST endpoint to listen 
     * @param {Class} MessageClass Message class to instantiate for this relayed message like CrawlStarted
     * @param {boolean} IsRpc Is this a RPC style message
     */
    relayToMsgQ(app, endpoint, MessageClass, isRpc = false) {
        app.post(endpoint, function (req, res) {
            let msg = new MessageClass(req.body)
            let ret
            if (isRpc) {
                msg.rpcRequest(this.rabbitMsgQ).then(reply => {
                    let message = 'DEPRECATED: ' + ( reply ? 'Queued...' : 'timed out')
                    __logAndSendResponse(req, res, {
                        status: "OK",
                        message,
                        details: reply
                    })
                }).catch(err => {
                    __logAndSendResponse(req, res, {
                        status: "NOK",
                        message: 'Error queing request',
                        details: err
                    })
                })
            } else {
                ret = msg.publish(this.rabbitMsgQ)
                __logAndSendResponse(req, res, {
                    status: "OK",
                    message: 'DEPRECATED: Queued...',
                    details: ret
                })
            }
        }.bind(this))
    }

    /**
     * Send message to network scanner to pause a scan
     * @param {Number} scanId Scan to pause
     */
    pauseScan(scanId) {
        let pauseScanMsg = new PauseScan({
            scanId,
            source: 'haiku'
        })

        let ret = pauseScanMsg.publish(this.rabbitMsgQ)
        logger.log('info', `sent pause scan message : ${JSON.stringify(pauseScanMsg)}, ${ret}`, {scanId})
        return ret
    }
}

module.exports = HaikuMessageQueue