const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class ServerMethodsPlugin extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-propfind-enabled'
        this.ipMatchRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g
    }

    getAttackVectors(){
        return httpAttackVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * @param {method} attack
     * Overriding the performNetworkAttack method to add host and content-length header in the request
     */
    async performNetworkAttack(attack) {
        attack.httpRequest.headers = {
            "host": "",
            "content-length": 0
        }
        return await super.performNetworkAttack(attack)
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {

        /**
         * If it's another plugin then it will return
         */
        if (attack.pluginName != this.getName()) {
            return
        }

        //here check for result in each case
        if (attack.result.resp.httpResponse.statusCode != 405) {
            //check for match pattern in response 
            let body = _.get(attack, 'result.resp.body')
            if (!body) {
                return false
            }

            let ipsFound = []
            let foundIndices = []
            let matches
            let regex = new RegExp(this.ipMatchRegex)
            while ((matches = regex.exec(body)) !== null) {
                //check for localhost found in IPs returned and bypass it
                let ip = matches[0]
                if (ip.indexOf("127.0.0.1") == -1) {
                    ipsFound.push(ip)
                    foundIndices.push(regex.index)
                }
                // create vulnerability and add IPs in it
            }

            if (ipsFound.length) {

                //remove duplicates
                ipsFound = _.uniq(ipsFound)

                let vuln = {
                    details: `Found internal IP(s) ${ipsFound.join(',')} at position(s) ${foundIndices.join(',')}`,
                    contextBytes: this.getConfig(attack).DefaultPluginSettings.contextBytes
                }
                let start = foundIndices[0] - vuln.contextBytes
                vuln.context = HaikuUtils.getSubString(body, start < 0 ? 0 : start, ipsFound[0].length + 2 * vuln.contextBytes)
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
                return true
            }
        }
    }
}

const httpAttackVectors = [
    `PROPFIND`
]

module.exports = ServerMethodsPlugin