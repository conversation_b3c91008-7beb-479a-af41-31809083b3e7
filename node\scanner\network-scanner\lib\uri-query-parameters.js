const debug = require('debug')('UriQueryParameters')
const querystring = require('querystring')
const URL = require('url').URL
const _ = require('lodash')

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = "UriQueryParameters"

// Delegate that can iterate query parameters and update name-value pairs
// Later, it will have the ability to re-encode values by detecting encoding type.
// starting with : www.xyz.com?id=5&sid=98
//  iterate all parameters, attacking values 
//  extra parameter, attack value: www.xyz.com?haiku[rnd]=<Attack>
//  extra parameter, attack param name: www.xyz.com?<Attack>=haiku[rnd]
class UriQueryParameters extends ParameterizedDelegate {
    static isUriQueryParamsRequest(httpRequest) {
        let url = new URL(httpRequest.uri)
        return url.search.length > 0
    }

    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore,options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType,options)
        let url = new URL(request.httpRequest.uri)
        this.searchParams = querystring.parse(url.searchParams.toString())
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings(param, value) {
        return this.options.encodings
    }

    * getIterator() {
        // iterate through all params
        for (let param in this.searchParams) {
            yield({
                name: param,
                val: this.searchParams[param],
                resetRequired: false
            })
        }

        // yield the extra param 
        if (this.options.addExtraParam) {
            yield {
                name: this.extraParam,
                val: this.extraParam,
                resetRequired: false
            }
        }

        // attack the parameter name 
        if (this.options.attackParamName) {
            this.attackType = 'name'
            yield {
                name: this.extraParam,
                val: this.extraParam,
                resetRequired: true
            }
        }
    }

    modifyParam(param, value, encoding) {
        if (this.attacktype == 'name') {
            // attack parameter name so param & value meanings are reversed
            this.searchParams[value] = param
        } else {
            this.searchParams[param] = value
        }
    }

    // get the modified request
    getHttpRequest(encoding) {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)
        let url = new URL(req.uri)
        let options = {}
        if (encoding == 'raw') {
            options = {
                encodeURIComponent: uri => uri
            }
            
        }
        
        let searchParam = querystring.stringify(this.searchParams, null, null, options)
        url.search = 'HAIKUPARAM'
        req.uri = url.toString().replace('HAIKUPARAM',searchParam)
         if(encoding == 'raw') {
            let symobjArrays = Object.getOwnPropertySymbols(url)
            let index = symobjArrays.findIndex(obj => obj.toString() == "Symbol(context)")
            searchParam = searchParam.replace(/ /g, '%20')
            searchParam = searchParam.replace(/\#/g, '%23')
            searchParam = searchParam.replace(/\n/g, '%0A')
            url[symobjArrays[index]].query=searchParam
            req.rawURl = url 
         }
        return req
    }

    // reset
    reset() {
        let url = new URL(this.originalRequest.httpRequest.uri)
        this.searchParams = querystring.parse(url.searchParams.toString())
    }
}

module.exports = UriQueryParameters