{"info": {"_postman_id": "69716c39-68be-40f3-a4b8-05328bf3e237", "name": "for-demo-tiredful", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Books", "request": {"method": "GET", "header": [], "url": {"raw": "http://{{URL}}/api/v1/books/978-93-80658-74-2/", "protocol": "http", "host": ["{{URL}}"], "path": ["api", "v1", "books", "978-93-80658-74-2", ""]}}, "response": []}, {"name": "Trains", "request": {"method": "POST", "header": [{"key": "Content-Type", "name": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": " {\n    \"PNR\": \"9875-4581-234\" \n }"}, "url": {"raw": "http://**********:8000/api/v1/trains/", "protocol": "http", "host": ["10", "0", "0", "232"], "port": "8000", "path": ["api", "v1", "trains", ""]}}, "response": []}, {"name": "Activities", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n      \"month\": 1\n    }"}, "url": {"raw": "http://{{URL}}/api/v1/activities/", "protocol": "http", "host": ["{{URL}}"], "path": ["api", "v1", "activities", ""]}}, "response": []}, {"name": "Advertisements, Create", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"headline\": \"Test\",\n    \"info\": \"Test\",\n    \"price\": 15.50\n}"}, "url": {"raw": "http://{{URL}}/api/v1/advertisements/", "protocol": "http", "host": ["{{URL}}"], "path": ["api", "v1", "advertisements", ""]}}, "response": []}, {"name": "Advertisements, Read", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "http://{{URL}}/api/v1/advertisements/", "protocol": "http", "host": ["{{URL}}"], "path": ["api", "v1", "advertisements", ""], "query": [{"key": "<PERSON>i", "value": "", "disabled": true}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "URL", "value": "localhost:8000"}, {"key": "token", "value": "222nXV9LANEAAUp8Mf4NXRacGivOP0"}]}