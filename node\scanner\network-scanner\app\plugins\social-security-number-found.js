const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')

class SocialSecurityNumberFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-social-security-number-found'

        /** Below is the pattern of different SSN numbers to try
         * get sample from https://regex101.com/r/FjJg6H/1 
         * https://regex101.com/r/HM5RFe/1
         **/

        //Removed re2 as it does not support positive look ahead and look after due to
        // reDos vulnerability hence changing the below code to RegExp

        // Combine the regexps - more efficient than running 'n' matches  
        this.matchRegex = new RegExp(SSNMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {
            return true
        }
        return false
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        let body = _.get(originalRequest, 'result.resp.body')
        if (!body) {
            return
        }
        let cardNumber_M = this.matchRegex.exec(body)

        //Below conditional check is for SSN number presence
        if (cardNumber_M != undefined && cardNumber_M.length > 0) {
            this.checkVuln(cardNumber_M, originalRequest)
            return
        }

    }

    checkVuln(cardNumber, originalRequest) {
        let details = []
        let vuln
        cardNumber = _.uniq(cardNumber)
        cardNumber = _.omitBy(cardNumber, _.isNil)
        for (let f in cardNumber) {
            details.push({
                Card_Number: cardNumber[f]
            })
        }
        vuln = {
            details: details
        }
        this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, vuln)
        return
    }
}

const SSNMatch = [/\s(?!(000|666|9))\d{3}-(?!00)\d{2}-(?!0000)\d{4}\s/]

module.exports = SocialSecurityNumberFound