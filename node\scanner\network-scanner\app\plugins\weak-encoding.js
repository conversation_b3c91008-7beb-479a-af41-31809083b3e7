const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

class WeakEncoding extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-weak-encoding'
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea == "original-crawler-request" && attack.httpRequest.method == 'POST') {
            //let uri = new URL(attack.href) - args_regexp.test(uri.search) || 
            let OriReqBody = _.get(attack, 'originalRequest.httpRequest.body')
            let args_regexp = /user_?(id|name)|uname|uid|userInput|email_?(id)?|(mobile|phone|card)_?(number)?|password|passwd|pwd/i
            if (args_regexp.test(OriReqBody)) {
                return true
            }
            return false
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        //if vuln detected for a req then return
        if (pluginDataForRequest.WeakEncodingFound) {
            return
        }
        //Sensitive data disclosed - Add argsnames below - args_regexp
        let args_regexp = /user_?(?:id|name)|uname|uid|userInput|email_?(id)?|(mobile|phone|card)_?(number)?|password|passwd|pwd/i
        let details = []
        let OriReqBody = _.get(attack, 'originalRequest.httpRequest.body')
        let OriReq_ContentType = _.get(attack, 'originalRequest.httpRequest.headers.Content-Type')
        try {
            if (OriReq_ContentType == 'application/json' || /^{"/.test(OriReqBody)) {
                let Jsonparse = JSON.parse(OriReqBody)
                let argNames = Object.keys(Jsonparse).toString().split(',')
                for (let currName of argNames) {
                    if (args_regexp.test(currName)) {
                        let currValue = Jsonparse[currName]
                        if (!/^\d+$/.test(currValue)) {
                            let decode64 = Buffer.from(currValue, 'base64').toString()
                            if (decode64.length > 0 && decode64.length < 25 && !/[�٢ںמϽ]/.test(decode64)) {
                                details.push({
                                    result: `${currName}=${currValue}, Decoded Value: ${decode64}`
                                })
                            }
                        }
                    }
                }
            }
            else {
                let params = OriReqBody.split('&')
                for (let checkparam of params) {
                    if (args_regexp.test(checkparam)) {
                        let args = checkparam.split('=')
                        if (args[1].length > 3 && !/^\d+$/.test(args[1])) {
                            let decode64 = Buffer.from(args[1], 'base64').toString()
                            if (decode64.length > 0 && decode64.length < 25 && !/[�٢ںמϽ]/.test(decode64)) {
                                details.push({
                                    result: `${args[0]}=${args[1]}, Decoded Value: ${decode64}`
                                })
                            }
                        }
                    }
                }
            }
            if (details.length > 0) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                pluginDataForRequest.WeakEncodingFound = true
            }
        }
        catch (e) {
            return
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID == this.vulnerabilityID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, [attack.originalRequest.httpRequest.body]);

        }
    }
}

module.exports = WeakEncoding