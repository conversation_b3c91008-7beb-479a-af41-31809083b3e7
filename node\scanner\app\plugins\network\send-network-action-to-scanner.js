const AttackableNetworkRequest = require('../../../common/lib/messages/attackable-nw-request')
const UpdateSessionInfo = require('../../../common/lib/messages/update-session-info')
let utils = require('../../ifc-utils.js')

/**
 * Uses MsgQ to send network action to scanner
 * @see LogNetworkActions (in crawler)
 */
class SendNetworkActionToScanner {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config
        this.msgQ = this.config.msgQ

        // network request captured
        scanner.on('network-action', this.onNetworkAction.bind(this))
        scanner.on('current-session', this.onCurrrentSession.bind(this))
    }

    async onNetworkAction(networkActionInfo) {
        if (networkActionInfo && networkActionInfo.networkActions) {
            for (let networkAction of networkActionInfo.networkActions) {
                let attackableRequest = {
                    scanId: networkActionInfo.scanId,
                    scanlog_id: networkActionInfo.scanlog_id,
                    scanner: networkActionInfo.scanner,
                    httpRequest: networkAction
                }
                let msg = new AttackableNetworkRequest(attackableRequest)
                let ret = await msg.publish(this.msgQ)
                utils.log(`Send attackable network request ${attackableRequest.httpRequest.appTranaKey} returned ${ret}`)
            }
        }
    }

    /**
     * Send current session info to scanner
     * @param {sessionInfo} sessionInfo as returned by {@link HaikuUtils.getSessionInfo}
     */
    async onCurrrentSession(sessionInfo) {
        if (!sessionInfo) {
            return
        }

        // send the session info to scanner
        let updateSessionInfoContent = {
            scanId: this.config.scanId,
            scanlogId: this.config.scanLogId,
            scanner: 'haiku',
            sessionInfo
        }

        let msg = new UpdateSessionInfo(updateSessionInfoContent)
        let ret = await msg.publish(this.msgQ)
        utils.log(`Send update session request ${JSON.stringify(updateSessionInfoContent)} returned ${ret}`)
    }

}

module.exports = SendNetworkActionToScanner