const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
// const SetCookie = require('set-cookie-parser');
// const ParseDomain = require('parse-domain')
const RegExpVari = require('./generic-regexp');

class <PERSON><PERSON><PERSON>hecker extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.httpOnlyVulnerabilityID = 'ID-cookie-httponly-not-set'
        this.secureVulnerabilityID = 'ID-cookie-secure-not-set'
        this.sessionCookieScopedToParentDomainVulnerabilityID = 'ID-session-cookie-scoped-parent-domain'
        this.cookiePathOverlyBroadVulnerabilityID = 'ID-broad-cookie-path'
        this.SameSiteCookieNotImplemented = 'ID-samesite-not-implemented'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            replaceValue: true,
            appendVector: false,
            encodings: ['raw']
        });
    }

    getAttackVectors() {
        return cookieVectors
    }

    getAttackableEvents() {
        return ['cookie-params']
    }

    processAttackResponse(attack) {
        const fullCookie = []
            .concat(_.get(attack, 'result.resp.httpResponse.headers.set-cookie', []) || [])
            .concat(_.get(attack, 'result.resp.httpResponse.redirects[0].headers["set-cookie"]', []) || []);

        if (!fullCookie.length) return;

        const resBody = _.get(attack, "result.resp.body", "");
        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 0);
        const impRegExp = /(JSESSIONID|PHPSESSID|ASP\.NET_SessionId|sid|sessionid|auth_?Token|id_token|access_token|refresh_token|jwt|x-access-token|csrfToken|__RequestVerificationToken|XSRF-TOKEN|user_id|uid|profile_id|SSO_SESSIONID|sso_token|_?identity|OAuth_Token|paymentSessionId|secureToken|cartId|user_role|account_type|remember_?me|cart|secure|session|payment|basket|config|token|auth|sessid)/i;

        // Skip if no sensitive cookies are found
        if (!impRegExp.test(fullCookie.join("\n"))) {
            const errorMessages = [
                RegExpVari.RegExp.CustomErrMsg1,
                RegExpVari.RegExp.CustomErrMsg2,
                RegExpVari.RegExp.CustomErrMsg3,
                RegExpVari.RegExp.CustomErrMsg4,
                RegExpVari.RegExp.CustomErrMsg5
            ];

            if (errorMessages.some(regex => regex.test(resBody)) || statusCode >= 400) {
                return;
            }
        }

        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        const pluginDataForRequest = this.getPluginScopedStore(attack);

        if (pluginDataForRequest.httpOnlyVulnerabilityFound &&
            pluginDataForRequest.secureVulnerabilityFound &&
            pluginDataForRequest.sessionCookieScopeFound &&
            pluginDataForRequest.cookiePathOverlyBroadFound &&
            pluginDataForRequest.SamesiteVulnerabilityFound) {
            return;
        }

        // Parse cookies efficiently
        let Allcookies = fullCookie.flatMap(cookie => cookie.split(/\r?\n/).map(c => c.trim()))
            .filter(c => {
                const cookieName = c.split('=')[0].trim();
                return !["Domain", "Path", "Max-Age", "Expires", "Secure", "HttpOnly", "SameSite", "Version"].includes(cookieName);
            });

        const existingCookies = new Set(pluginStorageScanScope.cookieName ? pluginStorageScanScope.cookieName.split(',') : []);

        let httpOnlyMissing = [], secureMissing = [], domainScopeVulnerable = [], pathVulnerable = [], sameSiteMissing = [];

        for (const rawCookie of Allcookies) {
            const ckNameMatch = rawCookie.match(/^\s*([^=;]+)/);
            if (!ckNameMatch) continue;
            const ckName = ckNameMatch[1];

            if (existingCookies.has(ckName) || /^TS[\da-f]+$/i.test(ckName)) continue;

            existingCookies.add(ckName);

            if (!/;\s*HttpOnly/i.test(rawCookie) && !pluginDataForRequest.httpOnlyVulnerabilityFound) {
                httpOnlyMissing.push({ fullCookie: rawCookie });
            }
            if (attack.result.req.parsedURL.protocol === 'https:' && !/;\s*Secure/i.test(rawCookie) && !pluginDataForRequest.secureVulnerabilityFound) {
                secureMissing.push({ fullCookie: rawCookie });
            }
            if (/;\s*Domain=/i.test(rawCookie) && !pluginDataForRequest.sessionCookieScopeFound) {
                const domainMatch = rawCookie.match(/;\s*Domain=([^;]+)(;|$)/i);
                if (domainMatch) {
                    const cookieDomain = domainMatch[1].trim();
                    const attackParts = attack.hostname.split('.').filter(Boolean);
                    const cookieParts = cookieDomain.split('.').filter(Boolean);

                    if (
                        attack.hostname !== cookieDomain &&
                        !cookieDomain.includes(attack.hostname) &&
                        attack.hostname.endsWith(cookieDomain) &&
                        cookieParts.length < attackParts.length
                    ) {
                        domainScopeVulnerable.push({ fullCookie: rawCookie });
                    }
                }
            }
            if (!pluginDataForRequest.cookiePathOverlyBroadFound && !/;\s*Path=[^/]/i.test(rawCookie)) {
                pathVulnerable.push({ fullCookie: rawCookie });
            }
            if (!/;\s*SameSite=(None|Strict|Lax)/i.test(rawCookie) && !pluginDataForRequest.SamesiteVulnerabilityFound) {
                sameSiteMissing.push({ fullCookie: rawCookie });
            }
        }

        pluginStorageScanScope.cookieName = Array.from(existingCookies).join(',');
        if (httpOnlyMissing.length) this.httpOnlyVulnerabilityFound(attack, httpOnlyMissing, pluginDataForRequest);
        if (secureMissing.length) this.SecureVulnerabilityFound(attack, secureMissing, pluginDataForRequest);
        if (domainScopeVulnerable.length) this.sessionCookieScopeFound(attack, domainScopeVulnerable, pluginDataForRequest);
        if (pathVulnerable.length) this.PathVulnerabilityFound(attack, pathVulnerable, pluginDataForRequest);
        if (sameSiteMissing.length) this.SamesiteVulnerabilityFound(attack, sameSiteMissing, pluginDataForRequest);
    }

    httpOnlyVulnerabilityFound(attack, httpOnlyMissing, pluginDataForRequest) {
        let vulns = {
            details: httpOnlyMissing
        }
        this.addVulnerabilitytoResult(attack, this.httpOnlyVulnerabilityID, vulns)
        pluginDataForRequest.httpOnlyVulnerabilityFound = true
    }

    SecureVulnerabilityFound(attack, secureMissing, pluginDataForRequest) {
        let vulns = {
            details: secureMissing
        }
        this.addVulnerabilitytoResult(attack, this.secureVulnerabilityID, vulns)
        pluginDataForRequest.secureVulnerabilityFound = true
    }

    sessionCookieScopeFound(attack, domainScopeVulnerable, pluginDataForRequest) {
        let vulns = {
            details: domainScopeVulnerable
        }
        this.addVulnerabilitytoResult(attack, this.sessionCookieScopedToParentDomainVulnerabilityID, vulns)
        pluginDataForRequest.sessionCookieScopeFound = true
    }

    PathVulnerabilityFound(attack, pathCookieScope, pluginDataForRequest) {
        let vulns = {
            details: pathCookieScope
        }
        this.addVulnerabilitytoResult(attack, this.cookiePathOverlyBroadVulnerabilityID, vulns)
        pluginDataForRequest.cookiePathOverlyBroadFound = true
    }

    SamesiteVulnerabilityFound(attack, sameSiteMissing, pluginDataForRequest) {
        let vulns = {
            details: sameSiteMissing
        }
        this.addVulnerabilitytoResult(attack, this.SameSiteCookieNotImplemented, vulns)
        pluginDataForRequest.SamesiteVulnerabilityFound = true
    }
}
const cookieVectors = [
    ';'
]

module.exports = CookieChecker