const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class AccountLockoutAPI extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-account-lockout'
    }

    getAttackVectors() {
        return passwordVectors
    }

    getAttackableEvents() {
        return ['json-body']
    }

    async performNetworkAttack(attack) {
        // always perform the initial attack
        let OriReqBody = _.get(attack, 'originalRequest.httpRequest.body')
        let OriReq_ContentType = _.get(attack, 'originalRequest.httpRequest.headers.Content-Type')
        if (/\b(password|passwd|pwd)\b/i.test(OriReqBody) && OriReq_ContentType == 'application/json') {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        let OriReqBody = _.get(attack, 'originalRequest.httpRequest.body')
        let OriReq_ContentType = _.get(attack, 'originalRequest.httpRequest.headers.Content-Type')
        if (/\b(password|passwd|pwd)\b/i.test(OriReqBody) && OriReq_ContentType == 'application/json') {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //if vuln already found then return
        if (pluginStorageScanScope.accountLockoutAPI) {
            return
        }

        let ResBody = _.get(attack, "result.resp.body")

        //original-crawler-request - Login Request then continue
        let OriReq_ContentType = _.get(attack, 'originalRequest.httpRequest.headers.Content-Type')
        if (OriReq_ContentType == 'application/json' && ResBody && pluginStorageScanScope.JSONPostdata != 1 && attack.attackArea == "original-crawler-request") {
            //update below RegEX if login page has different response body details.Now RestAPI
            if (/"Authorization":|authentication|access_?Token/i.test(ResBody) && /"success":|bearer/i.test(ResBody) && !/error/i.test(ResBody)) {
                pluginStorageScanScope.JSONPostdata = 1
            }
            return
        }

        if (attack.pluginName != this.getName()) { return }

        if (pluginStorageScanScope.attackCount == undefined) {
            pluginStorageScanScope.attackCount = 0
        }

        if (!/\b(password|passwd|pwd)\b/i.test(attack.param)) { return }

        //Check login request
        if (pluginStorageScanScope.JSONPostdata == 1 && pluginStorageScanScope.JSONPostdatalogin != 1) {
            let OriReqBody = _.get(attack, 'originalRequest.httpRequest.body')
            let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
            let Authorization = _.get(attack, 'originalRequest.httpRequest.headers.Authorization', '')
            if (/\b(password|passwd|pwd)\b/i.test(OriReqBody) && OriReq_ContentType == 'application/json' && OristatusCode == "200" && Authorization.length == 0) {
                pluginStorageScanScope.JSONPostdatalogin = 1
            }
            else { return }
        }

        //Increase attack count if req is login
        if (pluginStorageScanScope.JSONPostdatalogin == 1) {
            pluginStorageScanScope.attackCount++
        }

        //Only check for vulnerability once few login attempts have been made
        if (pluginStorageScanScope.attackCount >= this.getMetadata(attack).verifyAfterNumberOfLoginAttempts) {
            //Verify response of attack if account disabled after invalid logins
            if (/(.*)account(.*)locked(.*)|(.*)account(.*)disabled(.*)|(.*)user(.*)locked(.*)|(.*)user(.*)disabled(.*)|(.*)locked(.*)account(.*)|(.*)disabled(.*)account(.*)|(.*)locked(.*)user(.*)|(.*)disabled(.*)user(.*)|You are being rate limited/i.test(ResBody)) {
                pluginStorageScanScope.accountLockoutFlag = true
            }
            //If account not disabled then report vulnerability
            if (!pluginStorageScanScope.accountLockoutFlag) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
                pluginStorageScanScope.accountLockoutAPI = true
            }

        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

//we are going to try with  5 set of invalid password
const passwordVectors = [
    // all the password vectors, can use IdentityVector as well
    `111111`,
    `abc123`,
    `monkey`,
    `qwerty`,
    `testtest`,
]

module.exports = AccountLockoutAPI