const debug = require('debug')('HttpHeadersChecker')
const NetworkAttack = require('./network-attack')
const URL = require('url').URL
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { exec } = require('child_process');
const RegExpVari = require('./generic-regexp');

class HttpHeadersChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.hSTSVulnID = 'ID-hsts-header-missing'

    }

    async processAttackResponse(attack) {
        if (attack.attackArea !== "original-crawler-request") return;

        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginDataForRequest.HSTSVulnFound) {
            return
        }
        let protocol = _.get(attack, 'result.req.parsedURL.protocol');
        let respStatus = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (respStatus !== 200 || protocol !== 'https:') return;

        let ResBody = _.get(attack, 'result.resp.body', '')
        if (!ResBody.trim()) return;

        //condition to skip for the custom error pages
        const responseBody = ResBody.toLowerCase();

        // Check each category of error messages
        const errorCategories = [
            RegExpVari.ErrorMessages.HttpErrors,
            RegExpVari.ErrorMessages.SecurityErrors,
            RegExpVari.ErrorMessages.SessionErrors,
            RegExpVari.ErrorMessages.SystemErrors,
            RegExpVari.ErrorMessages.WafErrors,
            RegExpVari.ErrorMessages.GeneralErrors
        ];

        // Early return if any error message is found
        for (const category of errorCategories) {
            if (category.some(error => responseBody.includes(error))) {
                return;
            }
        }

        // Check for WAF servers in headers
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

        if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
            return;
        }

        let HSTSHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["strict-transport-security"]', '');
        let hstsVal = []
        let invaliddata = []
        let preload = false

        /**
        * report vulnerability only if header value is null / invalid which means that includeSubDomains and preload is missing
        * Ex: 
        * 1. Strict-Transport-Security: max-age=31536000; includeSubDomains
        * or 2. Strict-Transport-Security: max-age=63072000; includeSubDomains; preload   
        */

        if (!HSTSHeaderVal) {
            let result = `No HSTS header is present on the response.`
            pluginDataForRequest.HSTSVulnFound = true
            this.addVulnerabilitytoResult(attack, this.hSTSVulnID, result)
            return
        }
        else if (HSTSHeaderVal) {
            let list = HSTSHeaderVal.split(/;/g)
            list.forEach(el => {
                if (el.includes('max-age')) {
                    let currValue = el.split('=')
                    if (currValue[1] < 31536000) {
                        invaliddata.push(`Max-age too low. The max-age must be at least 31536000.\n`)
                    }
                    else if (currValue[1] >= 63072000 && !/preload/i.test(HSTSHeaderVal)) {
                        preload = true
                    }
                }
            })
            if (!/includeSubDomains/i.test(HSTSHeaderVal)) {
                invaliddata.push(`Error: includeSubDomains directive was missing.`)
            }

            if (preload) {
                try {
                    let uriRaw = new URL(attack.href)
                    let hostname = uriRaw.hostname
                    let regexp = /[\w\-]+?((\.[a-z]{2}){2}|\.[a-z]{2}\.[a-z]{2}|\.[a-z]{3}\.[a-z]{2}|\.[a-z]{2}|\.[a-z]{3,4}|\.(?:agency|social|space|digital|online|store|works|cloud|engineering|world|health|website|exchange|finance|games))$/ig
                    let UpdatedURL = `https://` + hostname.match(regexp)[0]
                    hstsVal = await this.gethstsdts(UpdatedURL)
                    if (!/preload/i.test(hstsVal)) {
                        let UpdatedURL = `https://www.` + hostname.match(regexp)[0]
                        hstsVal = await this.gethstsdts(UpdatedURL)
                        if (!/preload/i.test(hstsVal)) {
                            invaliddata.push(`Error: preload directive was missing. It may allow attackers to bypass HSTS.\n`)
                        }
                    }
                }
                catch (e) {
                    return
                }
            }
            if (invaliddata.length > 0) {
                pluginDataForRequest.HSTSVulnFound = true
                let result = invaliddata.join(', ')
                this.addVulnerabilitytoResult(attack, this.hSTSVulnID, `Strict-Transport-Security: ${HSTSHeaderVal}. ${result}`)
            }
        }
    }

    gethstsdts(UpdatedURL) {
        // let cmd = `curl -o /dev/null -s -w "%header{Strict-Transport-Security}" https://www.indusface.com||echo "bye"`
        let cmd = `curl -o /dev/null -s -w "%header{Strict-Transport-Security}" ${UpdatedURL}`
        return new Promise((resolve) => {
            try {
                exec(cmd, (err, stdout, stderr) => {
                    if (/preload/i.test(stdout)) {
                        resolve(stdout)
                    }
                    else {
                        resolve('Not found')
                    }
                });
            }
            catch (e) {
                resolve('Error')
            }
        })
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID == this.hSTSVulnID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        }
    }
}

module.exports = HttpHeadersChecker