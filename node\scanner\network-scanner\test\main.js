const fs = require('fs')
// https://github.com/creationix/http-parser-js
// we do this to avid http header errors when servers return invalid headers
// ... addresses issues such as corrupt HTTP headers, which would otherwise cause <PERSON><PERSON>'s parser 
//      to throw a fatal error (HPE_INVALID_HEADER_TOKEN)
// Monkey patch before you require http for the first time.
// process.binding('http_parser').HTTPParser = require('http-parser-js').HTTPParser;
const HTTPRequestMonster = require('../lib/http-request')
let config = require('../config/network-scanner-config.js')
let httpReqMonster = new HTTPRequestMonster(config)
const RabbitMQ = require('../../common/lib/rabbitmq')
const AttackableNetworkRequest = require('../../common/lib/messages/attackable-nw-request')
const GetSitemapUrlsRPC = require('../../common/lib/messages/get-sitemap-urls-rpc')
const CrawlStartedRpc = require('../../common/lib/messages/crawl-started-rpc')
const CrawlFinished = require('../../common/lib/messages/crawl-finished')
const PauseScan = require('../../common/lib/messages/pause-scan')
const _ = require('lodash')
let siteConfig = fs.readFileSync('./REST/sites-config.json', 'utf8');
siteConfig = JSON.parse(siteConfig);

let theNetworkScanner

function httpMonsterTest() {
    let urls = ['https://www.indusface.com', 'https://www.google.com', 'https://www.indusface.com/products',
        'https://www.indusface.com/blogs', 'https://www.google.com?q=test', 'https://www.indusface.com/careers',
        'https://www.microsoft.com', 'https://www.microsoft.com/products', 'https://www.microsoft.com/careers',
        'https://www.indusface.com/products', 'https://www.indusface.com/management',
        'https://www.indusface.com/productsssss', 'https://news.google.com'
    ]

    urls.push(...urls)

    for (uri of urls) {

        let req = {
            method: 'GET',
            uri: uri
        }

        let dp = httpReqMonster.queueRequest(req)
        dp.then((result) => console.log(`req: ${result.req.parsedURL.href}, err: ${result.resp.err}, statusCode: ${result.resp.fullResponse?result.resp.fullResponse.statusCode:''}`))
    }

    httpReqMonster.processQueue()
}

function httpMonsterPerfTest() {
    const DateDiff = require('date-diff')

    let uri = process.argv[3] || 'http://172.31.50.161/test.html'

    let numUrls = process.argv[2] || 5
    console.log(`sending ${numUrls} requests`)
    let waitingForReqsToComplete = numUrls
    let succReqs = 0
    let failedReqs = 0
    let testStartTime = new Date()
    for (let i = 0; i < numUrls; i++) {
        let req = {
            method: 'GET',
            url: uri
        }

        httpReqMonster.queueRequest(req).then((cur) => {
            waitingForReqsToComplete--
            if (!cur.resp || cur.resp.err || !cur.resp.response || cur.resp.response.statusCode != 200) {
                failedReqs++
            } else {
                succReqs++

                if (cur.resp.err) {
                    console.log(cur.resp.err)
                }
                if (cur.resp.response && cur.resp.fullResponse.statusCode != 200) {
                    console.log(r.resp.fullResponse.statusCode)
                }
            }
        })


        //promises.push(httpReqMonster.queueRequest(req))
    }

    //testStartTime = new Date()
    //httpReqMonster.processQueue()
    let testEndTime
    let p = new Promise((resolve) => {
        let intervalId
        intervalId = setInterval(() => {
            if (waitingForReqsToComplete == 0) {
                console.log('done')
                testEndTime = new Date()
                clearInterval(intervalId)
                resolve()
            } else {
                console.log(`${waitingForReqsToComplete} still in flight`)
            }
        }, 100)
    })

    p.then(() => {
        //let testEndTime = new Date()
        let diff = Date.diff(testEndTime, testStartTime)
        console.log(`${numUrls} requests complete in ${diff.seconds()} s = ${testEndTime-testStartTime} ms`)
        console.log(`max in flight requests = ${httpReqMonster.maxInFlightRequests}`)

        console.log(`success: ${succReqs}, failed: ${failedReqs}`)
    })
}

async function networkRequestTest() {
    let filename = process.argv[2] || './network-scanner/test/network-requests.json'
    networkRequests = JSON.parse(fs.readFileSync(filename, 'utf8'))
    if (!networkRequests) {
        console.log(`could not find file ${filename}, exiting`)
        process.exit(1)
    }

    let NetworkScanner = require('../app/network-scanner')

    let networkScnr = new NetworkScanner(config)
    await networkScnr.init()

    for (let request of networkRequests) {
        networkScnr.newRequest(request)
    }
}

async function msgQueueTest() {
    console.log('running msgQueueTest')
    let filename = './network-scanner/test/network-requests.json'
    networkRequests = JSON.parse(fs.readFileSync(filename, 'utf8'))
    if (!networkRequests) {
        console.log(`could not find file ${filename}, exiting`)
        process.exit(1)
    }

    // set up message Q - pretend to be the crawler sending the messgaes.
    // now set up RabbitMQ for the crawler.
    let msgQ = new RabbitMQ('crawler')
    await msgQ.init()

    // collect all the scanIds
    let scanInfo = [...new Set(networkRequests.map(nr => [nr.scanId, nr.mainUrl||nr.httpRequest.url, nr.networkScanner, nr.serviceId]))]

    sentCrawlStartedMsg = []
    for (let scan of scanInfo) {
        let scanId = scan[0]
        let mainUrl = scan[1]
        let haikuScanner = siteConfig[scanId].haikuScanner;
        let networkScanner = haikuScanner ? haikuScanner.networkScanner : null;
        let serviceId = scan[3]
        let msgContent = {
            scanId,
            scanlog_id: scanId,
            scanner: 'haiku-test',
            mainUrl,
            maxCrawlTimeMins: 30,
            networkScanner,
            serviceId
        }
        if (!sentCrawlStartedMsg.includes(scanId)) {
            let msg = new CrawlStartedRpc(msgContent)
            let ret = await msg.rpcRequest(msgQ) 
            console.log(`reply to crawlstarted rpc = ${JSON.stringify(ret)}`)
            sentCrawlStartedMsg.push(scanId)
        } else {
            console.log( `Already sent crawl started for ${scanId}`)
        }
    }

    for (let request of networkRequests) {
        // send as message to Q
        let msg = new AttackableNetworkRequest(request)
        msg.publish(msgQ)
    }

    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }

    for (let scan of scanInfo) {
        let scanId = scan[0]
        let mainUrl = scan[1]
        // Pause scan
        // setTimeout(() => {
        //     let msgContent = {
        //         scanId,
        //         scanlog_id: scanId,
        //         scanner: 'haiku-test',
        //         mainUrl
        //     }
        //     let msg = new PauseScan(msgContent)
        //     msg.publish(msgQ)
        // }, getRandomInt(5000, 60000))

        // Crawl Finsihed 
        setTimeout(() => {
            let msgContent = {
                scanId,
                scanlog_id: scanId,
                scanner: 'haiku-test',
                mainUrl
            }
            let msg = new CrawlFinished(msgContent)
            msg.publish(msgQ)
        }, getRandomInt(60000, 360000))
    }
}

async function sitemapUrlsTest() {
    console.log(`running sitemapUrlsTest...`)
    // set up message Q - pretend to be the crawler sending the messgaes.
    // now set up RabbitMQ for the crawler.
    let msgQ = new RabbitMQ('crawler')
    await msgQ.init()

    // indusface from scan URL
    let request = {
        mainUrl: 'https://www.indusface.com/products/application-security/web-application-firewall',
        maxSitemapLinks: 100
    }
    getSitemapUrls(request);

    // JeevanSaathi via robots.txt, sitemaplinks and .gz sitemaps
    request.mainUrl = 'https://www.jeevansathi.com'
    getSitemapUrls(request);

    // ndtv via robots.txt, sitemaplinks and XML using CDATA tags
    request.mainUrl = 'https://www.ndtv.com'
    getSitemapUrls(request);

    function getSitemapUrls(request) {
        console.log(`requesting sitemap urls for:  ${request.mainUrl}`);
        let msg = new GetSitemapUrlsRPC(request);
        msg.rpcRequest(msgQ).then((resp) => {
            console.log(`got reply with ${resp.sitemapUrls.length} URLs ->`);
            console.log(`${resp.sitemapUrls.slice(0, 5).join('\t\n')}\n...`);
        });
    }
}

async function directTest() {
    console.log('running directTest')
    let filename = './network-scanner/test/network-requests.json'
    networkRequests = JSON.parse(fs.readFileSync(filename, 'utf8'))
    if (!networkRequests) {
        console.log(`could not find file ${filename}, exiting`)
        process.exit(1)
    }

    // collect all the scanIds
    let scanIds = [...new Set(networkRequests.map(nr => nr.scanId))]

    for (let scanId of scanIds) {
        let msgContent = {
            scanId: scanId,
            scanlog_id: scanId,
            scanner: 'haiku-test',
            mainUrl: 'unknown', // leave as unknown for now
            maxCrawlTimeMins: 30
        }
        await theNetworkScanner.crawlStarted(msgContent)
    }

    for (let request of networkRequests) {
        // send as message to Q
        request.httpRequest.uri = request.httpRequest.url
        delete request.httpRequest.url
        await theNetworkScanner.newRequest(request)
    }

    function getRandomInt(min, max) {
        min = Math.ceil(min);
        max = Math.floor(max);
        return Math.floor(Math.random() * (max - min)) + min;
    }

    for (let scanId of scanIds) {
        setTimeout(() => {
            let msgContent = {
                scanId: scanId,
                scanlog_id: scanId,
                scanner: 'haiku-test',
                mainUrl: 'unknown', // leave as unknown for now
            }
            theNetworkScanner.crawlFinished(msgContent)
        }, getRandomInt(60000, 360000))
    }
}

// test to do
function doTest() {
    if (process.argv[3] == 'yes') {
        //httpMonsterTest()
        //networkRequestTest()
        //httpMonsterPerfTest()
        msgQueueTest()
        //sitemapUrlsTest()
        //directTest()
    }
}

if (process.argv[2] == 'yes') {
    // start the network scanner
    let NetworkScanner = require('../app/network-scanner')
    let networkScnr = new NetworkScanner(config)
    theNetworkScanner = networkScnr
    networkScnr.init().then(() => {
        setTimeout(doTest, 1000) // after 1 sec
    }).catch((e) => {
        console.log('Error thrown ', e)
    })
    console.log('Network scanner started')
} else {
    // jump straight to test
    doTest()
}