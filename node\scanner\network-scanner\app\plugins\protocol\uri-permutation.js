const debug = require('debug')('UriPermutation')
const querystring = require('querystring')
const BasePlugin = require('../base-plugin')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const UriAndParamsPermute = require('../../../lib/uri-and-parameters-permute')

// original: www.xyz.com?id=5&sid=98
// ALL -> www.xyz.com/<Attack>
// GET -> www.xyz.com?haiku[rnd]=<Attack>
// GET -> www.xyz.com?<Attack>=haiku[rnd]
class UriPermutation extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the netork scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }

    /**
     * New request received from crawler - Kick off paramter iteration if request is viable
     * @param {request} originalRequest untampered request
     * @override
     */
    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http headers
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area')
            if (originalRequest.revalidationInfo && UriAndParamsPermute.ParameterType != attackArea) {
                logger.log('info', `uri permutation - not an interesting request since request being revalidated is attacking '${attackArea}'`, HaikuUtils.getMetadataForLog(originalRequest))
                return
            }
        }

        if (originalRequest.httpResponse.err) {
            logger.log('info', `uri permutation - not an interesting request ${originalRequest.httpRequest.method} ${originalRequest.httpRequest.uri}  err=${originalRequest.httpResponse.err}`, HaikuUtils.getMetadataForLog(originalRequest))
            return
        }

        // Create object that can iterate and manipulate params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan')
        let options = this.getMetadata(originalRequest).options;
        let createUriPermuter = function () {
            return new UriAndParamsPermute(originalRequest, scanStore,options)
        }
        this.networkScanner.emit('uri-permutation', createUriPermuter)
    }

}

module.exports = UriPermutation