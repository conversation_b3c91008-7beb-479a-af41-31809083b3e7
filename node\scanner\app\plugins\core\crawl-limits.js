let utils = require('../../ifc-utils.js')
const DateDiff = require('date-diff')

class CrawlLimits {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config
        
        // init counters
        this.scanStartTime = new Date()

        // check for scan exceeded time every minute
        this.intervalId = setInterval(this.checkTimeExpiry.bind(this), 60 * 1000)

        // check for events and update counters
        // for maxactions
        this.actionsPerformed = 0
        scanner.on('next-action', this.onNextAction.bind(this))
        scanner.on('skip-before-trigger', this.onSkipBeforeTrigger.bind(this))
        scanner.on('scan-loop-done', this.onScanDone.bind(this))

        // can do other things like maxvulns eg. - later ....
    }

    onNextAction(action, crawlState) {
        this.actionsPerformed++;
        utils.log(`Global -  doneActions/totalActions = ${this.actionsPerformed}/${this.config.maxActions}`)
        if (this.actionsPerformed > this.config.maxActions) {
            utils.log(`aborting crawl since actions ${this.actionsPerformed} > max = ${this.config.maxActions}`)
            this.scanner.emit('stop-automated-crawl', 'max actions exceeded')
        }
    }

    onSkipBeforeTrigger(action) {
        this.actionsPerformed--
    }

    checkTimeExpiry() {
        if (Date.diff(Date.now(), this.scanStartTime).minutes() > this.config.maxScanTime) {
            utils.log(`aborting crawl since crawler exceeded max time = ${this.config.maxScanTime} minutes`)
            clearInterval(this.intervalId)
            this.scanner.emit('stop-automated-crawl', 'max time exceeded')
        }
    }
    onScanDone(reason, ret, logger = console) {
        clearInterval(this.intervalId);
        logger.log(`CrawlLimits: scan done - ${reason}`)
    }
}

module.exports = CrawlLimits