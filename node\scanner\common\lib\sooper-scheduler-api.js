const Request = require('request-promise-native')
const debug = require('debug')('SooperSchedulerApi')
const logger = require('./haiku-logger')
const HaikuUtils = require('./haiku-utils')

//const APIEndPoint = 'http://***********' // connectria scan api IP 
//const APIEndPoint = 'http://api.indusguard.com'
//const APIEndPoint = 'http://localhost:80'
//const APIEndPoint = 'http://apislim2.indusguard.com';//'http://igslim.indussecure.com'
APIEndPoint = process.env['SCAN_API_ENDPOINT'] || 'http://***********' // connectria scan api IP 
if (process.env['SCAN_API_LISTEN_PORT']) {
    APIEndPoint += ":" + process.env['SCAN_API_LISTEN_PORT']
}

class SooperSchedulerApi {
    constructor() {
        this.headers = {
            'accessKey': 'cashoktl31lyepk2pYmQa3IB0RUyt2im',
            'secretKey': 'QsahdevxqinGwwkLqooUvswcZaelHcpg'
        }

    }

    async scanStarted(request) {
        let uri = '';
        if (request.scanType) {
            uri = `${APIEndPoint}/scanner/v2/mm-jobsession-jobstart`;
        }
        else {
            uri = `${APIEndPoint}/scanner/v1/jobsession-jobstart`;
        }

        var options = {
            method: 'POST',
            uri: uri,
            headers: this.headers,
            body: {
                session_id: request.scanId,
                scanlog_id: request.scanlog_id,
                maxScanTimeMins: request.maxCrawlTimeMins
            },
            json: true // Automatically parses the JSON string in the response

        }

        let scanlogId
        try {
            let ret = await Request(options)
            logger.log('info', `called jobsession-jobstart API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                scanlogId = ret.result.scanlog_id
            }
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }

        return scanlogId
    }

    async scanStopped(id, haikuInfo, isBlocked = 1, blockedReason = 0) {
        let uri = '';
        if (haikuInfo.scanType) {
            uri = `${APIEndPoint}/scanner/v2/mm-jobsession-completed`;
        }
        else {
            uri = `${APIEndPoint}/scanner/v1/jobsession-completed`;
        }

        var options = {
            method: 'POST',
            uri: uri,
            headers: this.headers,
            body: {
                session_id: id,
                blocked: isBlocked,
                blockedreason: blockedReason,
                haikuInfo
            },
            json: true // Automatically parses the JSON string in the response
        }

        try {
            logger.log('info', `calling jobsession-completed API, request: ${JSON.stringify(options)}`, HaikuUtils.getMetadataForLog(options))
            let ret = await Request(options)
            logger.log('info', `called jobsession-completed API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    async getScanConfig(id) {
        // get the config from the REST API
        let options = {
            uri: `${APIEndPoint}/scanner/v1/aaservices-scannerconfig/${id}`,
            headers: this.headers,
            json: true // Automatically parses the JSON string in the response
        }

        let siteConfig
        try {
            let ret = await Request(options)
            logger.log('info', `called get scan config API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, {scanId: id})
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                siteConfig = ret.result[0]
            }
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, {scanId:id})
        }

        return siteConfig
    }

    async getMalwareScanConfig(id) {
        // get the config from the REST API
        let options = {
            uri: `${APIEndPoint}/scanner/v2/mmservices-scannerconfig/${id}`,
            headers: this.headers,
            json: true // Automatically parses the JSON string in the response
        }

        let siteConfig
        try {
            let ret = await Request(options)
            logger.log('info', `called get malware scan config API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, {scanId: id})
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                siteConfig = ret.result[0]  
            }
        } catch (err) {
            logger.log('error', `failed request malware scan config: ${JSON.stringify(options)} : ${err.toString()}`, {scanId:id})
        }

        return siteConfig
    }

    /**
     * Get info about previous normal (non CS) scan like scanID, scanlog ID. 
     * 
     * This is not a great way to do it and will be improved to be managed by Haiku :
     *  => at end of scan, Haiku gives blob that WAS will store and return with a retreive API similar to this one
     * @param {Integer} curScanId scan Id that is currently running
     * @param {Integer} serviceID Service ID of URI for which scan is running
     */
    async getPreviousScanDetails(curScanId, serviceID) {
        // get the config from the REST API
        let options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/previous-scan-details`,
            headers: this.headers,
            body: {
                serviceid: serviceID,
                sessionid: curScanId,
            },
            json: true // Automatically parses the JSON string in the response
        }

        let prevScanDetails
        try {
            let ret = await Request(options)
            logger.log('info', `called get previous scan details  API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, {scanId: curScanId})
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                prevScanDetails = ret.result[0]
            }
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, {scanId:curScanId})
        }

        return prevScanDetails
    }

    /**
     * Inform WAS that crawl visualization has been created.
     * 
     * @param {Number} scanlogId scanlog ID
     * @param {String} vizFilename filename of created (animated gif) vizualiation in S3. Path is known so 
     *                              only filename needs to be specified
     */
    async crawlVisualizationCreated(scanlogId, vizFilename) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/scan-animated-gif`,
            headers: this.headers,
            body: {
                scanlog_id: scanlogId,
                filename: vizFilename
            },
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `called crawlVisualizationCreated API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    async vulnerabilityFound(vulnerabilityInfo) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/post-result`,
            headers: this.headers,
            body: vulnerabilityInfo,
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `sent vuln info to portal (post-result API), request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    /**
     * Send vulnerability info to WAS portal
     * @param {vulnerabilityInfo} standarizedVulnInfo standardized vuln info as documented in @link <todo>
     */
    async vulnerabilityFoundV2(standarizedVulnInfo) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/post-poc-result `,
            headers: this.headers,
            body: standarizedVulnInfo,
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `sent standarized vuln info to portal (post-poc-result API), request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
            return ret;
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    /**
     * Send missing vulnerability info to WAS portal
     * @param {missingVulnInfo} information about vulnerability found in previous scan and missing in this one as documented in @link <todo>
     */
    async vulnerabilityMissing(missingVulnInfo) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/post-missing-result`,
            headers: this.headers,
            body: missingVulnInfo,
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `sent missing vuln info to portal (post-missing-result API), request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    async sendNetworkActions(networkActionInfo) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/network-actions`,
            headers: this.headers,
            body: networkActionInfo,
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `sent network action to portal (network-actions API), request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    async oobVulnerabilityFound(vulnerabilityInfo) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v2/oob-vulnerability-found`,
            headers: this.headers,
            body: vulnerabilityInfo,
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `sent vuln info to portal (oob-vulnerability-found), request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    async malwareVulnerabilityFound(vulnerabilityInfo) {
        var options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v2/malware-vulnerability-found`,
            headers: this.headers,
            body: vulnerabilityInfo,
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options)
            logger.log('info', `sent malware vuln info to portal (malware-vulnerability-found API), request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(options))
        } catch (err) {
            logger.log('error', `malware vuln info failed request: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(options))
        }
    }

    /** 
     * Get alert details from WAS portal
     * @param {Number} alertId alert ID to get details for
     * @returns {Object} alert details
    */
    async getAlertDetail(alertId) {
        let options = {
            method: 'GET',
            uri: `${APIEndPoint}/GetAlertDetails/${alertId}`,
            headers: this.headers,
            json: true // Automatically parses the JSON string in the response
        }

        let alertDetails
        try {
            let ret = await Request(options);
            logger.log('info', `called get alert details API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, {alertId: alertId})
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                if(ret.result && ret.result.data && ret.result.data.length > 0) {
                    alertDetails = ret.result.data[0];
                }
            }
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, {alertId: alertId})
        }

        return alertDetails
    }

    /**
     * Get alerts from WAS portal
     * @param {Number} scanLogId scanlog ID to get alerts for
     * @returns {Array} array of alerts
    */
    async getAlerts(scanLogId) {
        let options = {
            method: 'GET',
            uri: `${APIEndPoint}/GetHaikuAlertsByScanlogID?scanLogId=${scanLogId}`,
            headers: this.headers,
            json: true // Automatically parses the JSON string in the response
        }

        let alerts
        try {
            let ret = await Request(options);
            logger.log('info', `called get alerts API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, {scanLogId: scanLogId})
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                alerts = ret.result.data;
            }
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, {scanLogId: scanLogId})
        }

        return alerts
    }

    //https://was.indusface.com/GetHaikuAlertDetails?scanLogId=13775474&alertmd5=fcf9aa194acf554eb49193eb1ead49b2
    /**
     * Get alert details from WAS portal by scanlog ID and alert MD5
     * @param {Number} scanLogId scanlog ID to get alerts for
     * @param {String} alertMd5 alert MD5 to get details for
     * @returns {Object} alert details
    */
    async getHaikuAlertDetailByAlertMd5(scanLogId, alertMd5) {
        let options = {
            method: 'GET',
            uri: `${APIEndPoint}/GetHaikuAlertDetails?scanLogId=${scanLogId}&alertmd5=${alertMd5}`,
            headers: this.headers,
            json: true // Automatically parses the JSON string in the response
        }

        let alertDetails
        try {
            let ret = await Request(options);
            logger.log('info', `called get haiku alert details API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, {scanLogId: scanLogId})
            if (ret && ret.status && ret.status.toLowerCase() === 'ok') {
                alertDetails = ret.result
            }
        } catch (err) {
            logger.log('error', `failed request: ${JSON.stringify(options)} : ${err.toString()}`, {scanLogId: scanLogId})
        }

        return alertDetails
    }

    /**
     * Update WAS portal with processed postman file
     * @param {Number|String} uploadId upload ID of postman file
     * @param {Object} report report of postman file processing
     * @param {Object} metadata metadata for logging
     * @param {Boolean} isPartial true if only few APIs processed, false if full API processed.
     */
    async updateWASPortalWithProcessedPostmanFile(uploadId, report, metadata, isPartial = false) {
        let options = {
            method: 'POST',
            uri: `${APIEndPoint}/scanner/v1/postman-collection-processing-completed`,
            headers: this.headers,
            body: {
                uploadId,
                report,
                isPartial: isPartial // true if all api endpoints com, false if full API: 
            },
            json: true // Automatically parses the JSON string in the response
        }

        try {
            let ret = await Request(options);
            logger.log('info', `called postman-collection-processing-completed API, request: ${JSON.stringify(options)}\nret code : ${JSON.stringify(ret)}`, HaikuUtils.getMetadataForLog(metadata))
        } catch (err) {
            logger.log('error', `updateWASPortalWithProcessedPostmanFile request failed: ${JSON.stringify(options)} : ${err.toString()}`, HaikuUtils.getMetadataForLog(metadata));
        }
    }
}

const ssApi = new SooperSchedulerApi()
module.exports = ssApi