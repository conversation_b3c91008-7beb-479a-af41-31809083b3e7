let rabbitmqConfig = {
    server: {
        protocol: 'amqp',
        hostname: '***********',
        port: 5672,
        username: 'haiku', // should come from env ..
        password: 'haiku', // or something like that
        // locale: 'en_US',
        // frameMax: 0,
        heartbeat: 60, // heartbeat interval every 60 seconds
        vhost: 'haiku',

        // connection recovery
        expBackoffConnectionRetry: 1000, // in ms, exponential, doubles on each connection failure
        maxBackoff: 512, // maxBackoff * expBackoffConnectionRetry is max we will wait between connection retries
    },

    // above is common, below is specific to process like crawler/scanner
    // scanner publishes to crawler exchange & listens on scanner exchange
    scanner: {
        exchanges: [{
            name: 'scanner',
            type: 'topic',
            options: {
                durable: true,
            }
        }],
        queues: [{
                // regular reuests, sent to all scanners
                name: 'request',
                queueName: '',
                options: {
                    exclusive: true
                }
            },
            {
                // Load balanced reqests - sent round robin across scanners
                name: 'lbRequest',
                queueName: 'lbRequest',
            },
            {
                // Direct messaging between other process to scanner, i.e. crawler > scanner
                name: 'directMessage',
                queueName: 'directMessage.{{scannerMachineUID}}',
            },
        ],
        bindings: [{
                queue: 'request',
                exchange: 'scanner',
                pattern: 'request.#'
            },
            {
                queue: 'lbRequest',
                exchange: 'scanner',
                pattern: 'lb.request.#'
            },
            {
                name: 'directMessage',
                queue: 'directMessage.{{scannerMachineUID}}',
                exchange: 'scanner',
                pattern: 'direct.message.{{scannerMachineUID}}.#'
            }
        ]
    },

    // crawler publishes to scanner exchange & listens on crawler exchange
    crawler: {
        exchanges: [{
                name: 'crawler',
                type: 'topic',
                options: {
                    durable: true,
                }
            }, {
                name: 'scanner',
                type: 'topic',
                options: {
                    durable: true,
                }
            },
            {
                name: 'utils',
                type: 'topic',
                options: {
                    durable: true,
                }
            }
        ],
        queues: [{
            // regular reuests, sent to all crawlers
            name: 'crawler-request',
            queueName: '',
            options: {
                exclusive: true
            }
        }],
        bindings: [{
            queue: 'crawler-request',
            exchange: 'crawler',
            pattern: 'request.#'
        }]
    },
    // for now scan api only publishes so no request queue necc.
    scanapi: {
        channel: {
            prefetch: 1
        },
        exchanges: [{
            name: 'scanner',
            type: 'topic',
            options: {
                durable: true,
            }
        }, {
            name: 'utils',
            type: 'topic',
            options: {
                durable: true,
            }
        }],
        queues: [{
            // Load balanced reqests - sent round robin across scanners
            name: 'lb-utils-request',
            queueName: 'lb-utils-request',
        }],
        bindings: [{
            queue: 'lb-utils-request',
            exchange: 'utils',
            pattern: 'lb.request.#'
        }]
    }

}

module.exports = rabbitmqConfig