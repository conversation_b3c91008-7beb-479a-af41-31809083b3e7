const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class BrowsableWebDirectory extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-browsable-web-directory'
        this.backupvulnerabilityID = 'ID-archive-backup-file'
        this.possibleBackupFilevulnerabilityID = 'ID-possible-backup-file'
        this.coreDumpFileID = 'ID-core-dump-file'
    }


    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            clearQueryParams: true,
            skipRoot: false,
            maxPathComponents: 3,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'always'
        });
    }
    getAttackVectors() {
        return BWDVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let bodycheck = _.get(attack, "result.resp.body")
        let vulnFound = false
        if (/<HTML><HEAD><TITLE>Directory:/.test(bodycheck) || />\[To Parent Directory\]/.test(bodycheck)) {
            vulnFound = true
        } else if (/Parent Directory/.test(bodycheck) && /Index of/.test(bodycheck)) {
            vulnFound = true
        } else if (/<title>Index of \//.test(bodycheck) && /<pre><a href=&..\/<\/a>/.test(bodycheck)) {
            vulnFound = true
        } else if (/<title>Index of /.test(bodycheck) && /<pre></.test(bodycheck) && /<a href=/.test(bodycheck)) {
            vulnFound = true
        } else if (/Directory Listing For/.test(bodycheck) && /Sun GlassFish Enterprise Server/.test(bodycheck)) {
            vulnFound = true
        }

        if (vulnFound) {
            let vuln = {
                details: attack.href
            }

            // for context get the names of all the links inside td elements (to skip links in header)
            // see browsable directory in chrome browser to unerstand
            let resourceList = []
            let archiveList = []
            let backupList = []
            let coreDumpList = []

            // creating two arrays list, one for first 20  found 
            // and another for backup and compression files array
            // Filter attack cheerio response when "td" is there in source code
            attack.result.resp.httpResponse.cheerio("td>a").contents().each((i, el) => {
                if (/\.gz|\.tar\.gz|\.bzip|\.7z|\.tar|\.tgz|\.bzip|\.bzip2|\.zip|\.rar|\.7z/i.test(el.data)) {
                    archiveList.push(el.data)
                }
                if (/\.bkp|\.bak|\.backup|\.old|\.temp/i.test(el.data)) {
                    backupList.push(el.data)
                }
                // Below code to check only for core dump files and report it individually
                if (/\.dmp|\.elf|\.out/i.test(el.data)) {
                    coreDumpList.push(el.data)
                }
                resourceList.push(el.data)
            })

            // Filter attack cheerio response when "li" is there in source code
            attack.result.resp.httpResponse.cheerio("li>a").contents().each((i, el) => {
                if (/\.gz|\.tar\.gz|\.bzip|\.7z|\.tar|\.tgz|\.bzip|\.bzip2|\.zip|\.rar|\.7z/i.test(el.data)) {
                    archiveList.push(el.data)
                }
                if (/\.bkp|\.bak|\.backup|\.old|\.temp/i.test(el.data)) {
                    backupList.push(el.data)
                }
                // Below code to check only for core dump files and report it individually
                if (/\.dmp|\.elf|\.out/i.test(el.data)) {
                    coreDumpList.push(el.data)
                }
                resourceList.push(el.data)
            })

            // Filter attack cheerio response when "img" is there in source code
            attack.result.resp.httpResponse.cheerio("img>a").contents().each((i, el) => {
                if (/\.gz|\.tar\.gz|\.bzip|\.7z|\.tar|\.tgz|\.bzip|\.bzip2|\.zip|\.rar|\.7z/i.test(el.data)) {
                    archiveList.push(el.data)
                }
                if (/\.bkp|\.bak|\.backup|\.old|\.temp/i.test(el.data)) {
                    backupList.push(el.data)
                }
                // Below code to check only for core dump files and report it individually
                if (/\.dmp|\.elf|\.out/i.test(el.data)) {
                    coreDumpList.push(el.data)
                }
                resourceList.push(el.data)
            })

            // if any value present in archive list then only report it
            if (archiveList.length > 0) {
                archiveList = archiveList.slice(0, 10)
                vuln.archiveContext = archiveList.join(', ')
                this.addVulnerabilitytoResult(attack, this.backupvulnerabilityID, vuln)
            }

            // if any value present in archive list then only report it
            if (backupList.length > 0) {
                backupList = backupList.slice(0, 10)
                vuln.backupContext = backupList.join(', ')
                this.addVulnerabilitytoResult(attack, this.possibleBackupFilevulnerabilityID, vuln)
            }

            // if any dump file found then report it and only report first 5 
            if (coreDumpList.length > 0) {
                coreDumpList = coreDumpList.slice(0, 5)
                vuln.coreDumpList = coreDumpList.join(', ')
                this.addVulnerabilitytoResult(attack, this.coreDumpFileID, vuln)
            }

            // To report only first 20 links  hence slicing the array as below
            resourceList = resourceList.slice(0, 20)
            vuln.context = resourceList.join(', ')
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);         
    }
}

// vectors & matches ...
const BWDVectors = [
    VectorResponseAttack.identityVector,
    `help`,
    `resources`,
    `resource`,
    `config` //vector added for dvwa coverage of remote admin interface found
]

module.exports = BrowsableWebDirectory