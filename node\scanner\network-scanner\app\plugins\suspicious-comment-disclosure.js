const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')

/**
 * Suspicious Comment Disclosure plugin strategy:
 * Here for every request made, response is matched against regular expression for suspicious comments 
 * to know if vulnerability exists and report the same
 */
class SuspiciousCommentDisclosure extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-suspicious-comment-disclosure'
        this.matchRegexps = []
        for (let regex of SuspiciousComments) {
            this.matchRegexps.push(new RE2(regex))
        }
    }


    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {

        if (originalRequest.result.resp.httpResponse.err) {
            return
        }

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {

            //check if any comment present in response body, then only call processAttackResponse
            let body = _.get(originalRequest, "result.resp.body")
            if (/<!--/i.test(body)) {
                return true
            }
        }
        return false
    }

    /**
     * @param {attack} originalRequest verifiying for all the original crawler requests 
     * @override
     */
    processAttackResponse(originalRequest) {


        //Using cheerio to find all comments in the page source/response body
        let $ = _.get(originalRequest, 'result.resp.httpResponse.cheerio');
        let comments = $('*').contents().filter((i, e) => {
            return e.type == "comment";
        }).get();

        //Check is any comments match regular expression
        let matchRes = this.checkAnyRegexMatch(comments);

        //if any match found then report vulnerability
        if (matchRes) {
            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, matchRes)
        }

    }

    //For all the suspicious vectors provided check if its present in any comment
    // and if present then store it in result 
    checkAnyRegexMatch(comments) {
        let res = '';
        let pattern = '';
        for (let i = 0; i < comments.length; i++) {
            let lineOfQuery = comments[i];
            for (let pattern of this.matchRegexps) {
                if (pattern.test(lineOfQuery.data)) {
                    res += lineOfQuery.data + '\n'
                }
            }

        }
        return res;
    }
}

//Suspicious comments regex
const SuspiciousComments = [
    /bug/i,
    /admin/i,
    /credential/i,
    /username/i,
    /password/i,
    /fixme/i,
    /todo/i,
    /hack/i
]

module.exports = SuspiciousCommentDisclosure