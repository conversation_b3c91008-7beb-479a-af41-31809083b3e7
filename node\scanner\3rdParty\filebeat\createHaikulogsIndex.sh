# https://vpc-datapipeline-waflogs-hwmv3ssksjgf44jnuvi2viz4iu.ap-south-1.es.amazonaws.com/_plugin/kibana/
export es_endpoint=https://haikues.indusface.com
curl -XGET $es_endpoint/_cat/indices
curl -XGET $es_endpoint/_cat/indices/haiku*?h=i,dc,cds,p,ss\&v

# ---- create index template and first rollover index
# for use in kibana devtools, just use PUT /_template/haikulogs followed by the JSON
# curl -XPUT -H 'Content-Type:application/json' $es_endpoint/_template/haikulogs -d '{
#     "index_patterns": [
#       "haikulogs-*"
#     ],
#     "settings": {
#         "number_of_shards": 3
#     },
#     "mappings": {
#         "doc": {
#             "properties": {
#                 "timestamp": {
#                     "type": "date"
#                 },
#                 "message": {
#                     "type": "text"
#                 },
#                 "level": {
#                     "type": "keyword"
#                 },
#                 "scanId": {
#                     "type": "integer"
#                 },
#                 "scanlogId": {
#                     "type": "integer"
#                 },
#                 "haikuProcess": {
#                     "type": "keyword"
#                 },
#                 "machineId": {
#                     "type": "keyword"
#                 }
#             }
#         }
#     }
# }'

# ---- create first rollover index
# for use in kibana devtools, just use PUT /haikulogs-000001 followed by the JSON
# curl -XPUT -H 'Content-Type:application/json' $es_endpoint/haikulogs-000001 -d '{
#     "aliases": {
#         "haikulogs-write": {}
#     },
#     "settings": {
#         "number_of_shards": 3
#     },
#     "mappings": {
#         "doc": {
#             "properties": {
#                 "timestamp": {
#                     "type": "date"
#                 },
#                 "message": {
#                     "type": "text"
#                 },
#                 "level": {
#                     "type": "keyword"
#                 },
#                 "scanId": {
#                     "type": "integer"
#                 },
#                 "scanlogId": {
#                     "type": "integer"
#                 },
#                 "haikuProcess": {
#                     "type": "keyword"
#                 },
#                 "machineId": {
#                     "type": "keyword"
#                 }
#             }
#         }
#     }
# }'

# --- rollover
curl -XPOST -H 'Content-Type:application/json' $es_endpoint/haikulogs-write/_rollover -d '
{
  "conditions": {
    "max_age":   "1d"
  }
}'

# curl -XGET $es_endpoint/_cat/indices
# curl -XGET $es_endpoint/haikulogs-write
# curl -XGET $es_endpoint/haikulogs-write/_mapping

# delete all haikulogs indices
# curl -X DELETE $es_endpoint/haikulogs-000001

# run filebeat locally (replace with full path is haiku-log-filebeat.yml is not in filebeat config location)
# filebeat -e -c haiku-log-filebeat.yml

# force reprocessing of logs (on mac withbre install of filebeat-oss)
# rm -f /usr/local/var/lib/filebeat/registry/filebeat/*.json

# curl -XPOST $es_endpoint/haikulogs*/_search -H 'Content-Type: application/json' -d'
# {
#    "_source": ["message", "timestamp"],
#    "size": "100000",
#   "query": {
#     "bool": {
#       "should": [
#         {
#           "match": {
#             "message": "sending request:"
#           }
#         },
#         {
#           "match": {
#             "message": "request finished"
#           }
#         }
#       ],
#       "filter": [
#         {
#           "term": {
#             "scanId": "1444148"
#           }
#         }
#       ]
#     }
#   }
# }'