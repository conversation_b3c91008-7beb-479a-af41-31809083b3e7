var fs = require('fs');
const path = require('path');
const Minimist = require('minimist');
_ = require('lodash');
const ssAPI = require('../../common/lib/sooper-scheduler-api');
const logger = require('../../common/lib/haiku-logger');
const s3Utils = require('../../common/lib/s3-utils')
const HaikuUtils = require('../../common/lib/haiku-utils');
const Request = require('request-promise-native')

// global
let utilMachineApiEndPoint = process.env['UTIL_MACHINE_API_ENDPOINT'] || 'http://localhost:3000';

logger.setMetadata({
    haikuProcess: 'replay-vulnerability-checks'
});

let vulnsFoundPrefix = 'scanner/vulnsFound/';

class ProtectionStatus {
    constructor(customer_id, customer_name, website_id, website_name, regular_alert_id, alert_md5, url, vuln_name, alert_impact, replayalertid) {
        this.customer_id = customer_id;
        this.customer_name = customer_name;
        this.website_id = website_id;
        this.website_name = website_name;
        this.regular_alert_id = regular_alert_id;
        this.alert_md5 = alert_md5;
        this.url = url;
        this.vuln_name = vuln_name;
        this.alert_impact = alert_impact;
        this.replayalertid = replayalertid;
        this.haikuReplayStatus = 'not_replayed';
        this.replayStatusInfo = {};
        this.haikuRemarks = '';
        this.isManualAlert = false;
    }
}

/** 
 * Out of band vulnerability check util class. i.e. log4j vuln
 */
class ReplayVulnerabilityChecks {
    constructor(protectionStatusFile) {
        this.protectionStatusFile = protectionStatusFile;
        this.replayStatus = {};
        this.replayIds = {};
        this.customer_id_column_index = -1;
        this.customer_name_column_index = -1;
        this.website_id_column_index = -1;
        this.website_name_column_index = -1;
        this.regular_alert_id_column_index = -1;
        this.alert_md5_column_index = -1;
        this.url_column_index = -1;
        this.vuln_name_column_index = -1;
        this.alert_impact_column_index = -1;
        this.remark_column_index = -1;
        this.comments_column_index = -1;
        this.replayedscanlogid_column_index = -1;
        this.regularscanlogid_column_index = -1;
        this.replayid_column_index = -1;
        this.replayalertid_column_index = -1;
    }

    async getProtectionStatusData() {
        let replayIdData = fs.readFileSync('haiku-store/protection-status/Replay-scan-details-new.csv', 'utf8');
        let replayIdLines = replayIdData.split(/\r?\n/);
        
        for (let i = 1; i < replayIdLines.length; i++) {
            let line = replayIdLines[i];
            let values = line.split(',');

            if(values.length < 3) {
                continue;
            }

            let replay_id = values[0].replace(/"/g, '').trim();
            let replay_scanlog_id = values[1].replace(/"/g, '').trim();
            let regular_scanlog_id = values[3].replace(/"/g, '').trim();

            if (replay_id) {
                this.replayIds[replay_id] = this.replayIds[replay_id] || {};
                this.replayIds[replay_id].replay_scanlog_id = replay_scanlog_id;
                this.replayIds[replay_id].regular_scanlog_id = regular_scanlog_id;
                this.replayIds[replay_id].replay_scan_start_time = values[2].replace(/"/g, '').trim();
                this.replayIds[replay_id].regular_scan_start_time = values[4].replace(/"/g, '').trim();
            }
        }

        let data = fs.readFileSync(this.protectionStatusFile, 'utf8');
        let lines = data.split(/\r?\n/);
        let columns = lines[0].split(',');

        for (let i = 0; i < columns.length; i++) {
            //replay " character with empty string
            columns[i] = columns[i].replace(/"/g, '');
            columns[i] = columns[i].trim();
        }

        this.customer_id_column_index = columns.indexOf('customer_id') > -1 ? columns.indexOf('customer_id') : -1;
        this.customer_name_column_index = columns.indexOf('customer_name') > -1 ? columns.indexOf('customer_name') : -1;
        this.website_id_column_index = columns.indexOf('website_id') > -1 ? columns.indexOf('website_id') : -1;
        this.website_name_column_index = columns.indexOf('website_name') > -1 ? columns.indexOf('website_name') : -1;
        this.regular_alert_id_column_index = columns.indexOf('alert_id') > -1 ? columns.indexOf('alert_id') : -1;
        this.alert_md5_column_index = columns.indexOf('alert_md5') > -1 ? columns.indexOf('alert_md5') : -1;
        this.url_column_index = columns.indexOf('url') > -1 ? columns.indexOf('url') : -1;
        this.vuln_name_column_index = columns.indexOf('vuln_name') > -1 ? columns.indexOf('vuln_name') : -1;
        this.alert_impact_column_index = columns.indexOf('alert_impact') > -1 ? columns.indexOf('alert_impact') : -1;
        this.remark_column_index = columns.indexOf('Remark') > -1 ? columns.indexOf('Remark') : -1;
        this.comments_column_index = columns.indexOf('Comments') > -1 ? columns.indexOf('Comments') : -1;
        this.replayedscanlogid_column_index = columns.indexOf('replayedscanlog') > -1 ? columns.indexOf('replayedscanlog') : -1;
        this.regularscanlogid_column_index = columns.indexOf('latestregularscanlog') > -1 ? columns.indexOf('latestregularscanlog') : -1;
        this.replayid_column_index = columns.indexOf('replayid') > -1 ? columns.indexOf('replayid') : -1;
        this.replayalertid_column_index = columns.indexOf('replayalertid') > -1 ? columns.indexOf('replayalertid') : -1;

        if(this.customer_id_column_index == -1 || this.customer_name_column_index == -1 || 
            this.website_id_column_index == -1 || this.website_name_column_index == -1 || 
            this.regular_alert_id_column_index == -1 || this.alert_md5_column_index == -1 || 
            this.url_column_index == -1 || this.vuln_name_column_index == -1 || 
            this.alert_impact_column_index == -1 || this.remark_column_index == -1 || 
            this.comments_column_index == -1 || this.replayedscanlogid_column_index == -1 || 
            this.regularscanlogid_column_index == -1 || this.replayid_column_index == -1 || 
            this.replayalertid_column_index == -1) {
            logger.error(`one or more columns are missing in protection status file. customer_id_column_index=${this.customer_id_column_index}, customer_name_column_index=${this.customer_name_column_index}, website_id_column_index=${this.website_id_column_index}, website_name_column_index=${this.website_name_column_index}, regular_alert_id_column_index=${this.regular_alert_id_column_index}, alert_md5_column_index=${this.alert_md5_column_index}, url_column_index=${this.url_column_index}, vuln_name_column_index=${this.vuln_name_column_index}, alert_impact_column_index=${this.alert_impact_column_index}, remark_column_index=${this.remark_column_index}, comments_column_index=${this.comments_column_index}, replayedscanlogid_column_index=${this.replayedscanlogid_column_index}, regularscanlogid_column_index=${this.regularscanlogid_column_index}, replayid_column_index=${this.replayid_column_index}, replayalertid_column_index=${this.replayalertid_column_index}`);
            return;
        }

        for (let i = 1; i < lines.length; i++) {
            let line = lines[i];
            let values = line.split(',');

            let customer_id = values[this.customer_id_column_index];
            let customer_name = values[this.customer_name_column_index];
            let website_id = values[this.website_id_column_index];
            let website_name = values[this.website_name_column_index];
            let regular_alert_id = values[this.regular_alert_id_column_index];
            let alert_md5 = values[this.alert_md5_column_index];
            let url = values[this.url_column_index];
            let vuln_name = values[this.vuln_name_column_index];
            let alert_impact = values[this.alert_impact_column_index];
            let regularscanlogid = values[this.regularscanlogid_column_index];
            let replayid = values[this.replayid_column_index];
            let replayalertid = values[this.replayalertid_column_index];

            if (website_id) {
                let protectionStatus = new ProtectionStatus(customer_id, customer_name, website_id, website_name, regular_alert_id, alert_md5, url, vuln_name, alert_impact, replayalertid);
                this.replayStatus[website_id] = this.replayStatus[website_id] || {};
                this.replayStatus[website_id].fetchAlertDetails = this.replayStatus[website_id].fetchAlertDetails || false;
                this.replayStatus[website_id].alertmd5s = this.replayStatus[website_id].alertmd5s || {};
                this.replayStatus[website_id].alertmd5s[alert_md5] = protectionStatus;
                this.replayStatus[website_id].regularscanlogid = regularscanlogid;
                this.replayStatus[website_id].replayid = replayid;
                this.replayStatus[website_id].replayedscanlogid = this.replayIds[replayid].replay_scanlog_id;
            }
        }
    }

    async processEachWebsite() {
        let websiteIds = Object.keys(this.replayStatus);

        for (let websiteId of websiteIds) {
            
            let alertMd5s = Object.keys(this.replayStatus[websiteId].alertmd5s);

            if (alertMd5s.length == 0) {
                continue;
            }

            // get scanlogid & serviceid from first alert detail
            if (!this.replayStatus[websiteId].fetchAlertDetails) {
                this.replayStatus[websiteId].fetchAlertDetails = true;
                let alertmd5 = Object.keys(this.replayStatus[websiteId].alertmd5s)[0];
                let alertId = this.replayStatus[websiteId].alertmd5s[alertmd5].regular_alert_id;
                let alertDetail = await ssAPI.getAlertDetail(alertId);

                if (alertDetail) {
                    this.replayStatus[websiteId].regular_scanlog_id = alertDetail.scanlogid;
                    this.replayStatus[websiteId].regularscanlogid = this.replayStatus[websiteId].regularscanlogid;
                    this.replayStatus[websiteId].serviceId = alertDetail.serviceid;
                    this.replayStatus[websiteId].replay_scanlog_id = this.replayStatus[websiteId].replayedscanlogid;
                }
            }

            await this.generateReplayVulnReport(websiteId, this.replayStatus[websiteId].regular_scanlog_id, this.replayStatus[websiteId].replay_scanlog_id, 
                this.replayStatus[websiteId].serviceId, alertMd5s);
        }
    }

    async getFileFromStorage(pathPrefix, fileName) {
        let result = null;

        try {
            // use http request module to get file from storage
            let options = {
                uri: utilMachineApiEndPoint + '/utils/getFileFromStorage',
                method: 'POST',
                json: true,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'PostmanRuntime/7.36.0',
                    'Accept': '*/*',
                    'Postman-Token': 'cf57402f-af0b-46fa-83f5-628958266b1a',
                    'Host': '***********',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Connection': 'keep-alive'
                },
                body: {
                    data: {
                        folderPath: pathPrefix,
                        fileName: fileName
                    }
                }
            }

            return await Request(options)
        } catch (error) {
            logger.error(`error while getting file from storage. error=${error}`);
        }

        // return new Promise((resolve, reject) => {
        //     Request(options, (error, response, body) => {
        //         if(error) {
        //             reject(error);
        //         }
        //         else {
        //             resolve(body);
        //         }
        //     });
        // });


        // // Create a write stream to a file
        // const fileStream = fs.createWriteStream(path.join(__dirname, fileName));

        // // Make the request and pipe the response to the write stream
        // return new Promise((resolve, reject) => {
        //     Request(options)
        //         .on('error', reject)
        //         .pipe(fileStream)
        //         .on('finish', () => resolve(fileName))
        //         .on('error', (error)=> {
        //             reject('error while downloading file')
        //         });
        // });
    }

    async getRevalidateVulns() {
        let revalidateVulns = await this.getFileFromStorage(vulnsFoundPrefix + this.serviceId + '/', 'revalidateVulns.json');
        let revalidateVulnsInfo = revalidateVulns;
        return revalidateVulnsInfo;
    }

    async getAlertDetails() {
        let alertDetails = await this.getFileFromStorage(vulnsFoundPrefix + this.serviceId + '/', 'alertDetails.json');
        let alertDetailsInfo = alertDetails;
        return alertDetailsInfo;
    }

    async generateReplayVulnReport(websiteId, regularScanLogId, replayScanLogId, serviceId, alertmd5ToCheck) {
        //get all alerts from lastRegularScanLogId
        let regularAlerts = await ssAPI.getAlerts(regularScanLogId);
        //get all alerts from lastReplayScanLogId
        let replayAlerts = await ssAPI.getAlerts(replayScanLogId);

        // let sampleAlert = {
        //     "id": 171750240,
        //     "serviceid": 66835,
        //     "scanlogid": 13771849,
        //     "unique_id": 3782776,
        //     "vulnerabilityid": 403,
        //     "title": "Directory Listing",
        //     "alertmd5": "24de59974fd84e030965b7da9a9c1544",
        //     "vulname": "ID-browsable-web-directory",
        //     "category": "Indusface WAS",
        //     "vultype": "R",
        //     "alert_source": "A"
        // }

        // sample "X-IFC-REPLAY": "22168_f3ba69b6e097ab5b9a83b6c9ba4d0efc" in attack.httprequest.headers

        // iterage over all alerts from lastRegularScanLogId, get alertmd5 from it, and then iterate 
        // over all alerts from lastReplayScanLogId and check if alertmd5 is present in it. If not, then
        // add it to the list of alerts that are not present in lastReplayScanLogId but present in lastRegularScanLogId
        // and then add it to present list.

        //let alertmd5ToCheck = ['13e9cd7abc2ecc2e7ab59f665ce65c9f'];
        let finalAlertMd5Report = {};

        for (let alertmd5 of alertmd5ToCheck) {
            finalAlertMd5Report[alertmd5] = {
                isExistInRegularScan: false,
                isReplayedInReplayScan: false,
                regularAlertId: null,
                replayAlertId: null,
                replayAlertDetail: {},
                isReplayed: false,
                isAlertFoundInReplay: false,
                inRevalidateVulns: false,
                inAlertDetails: false
            };

            this.replayStatus[websiteId].alertmd5s[alertmd5].replayStatusInfo = finalAlertMd5Report[alertmd5];
        }

        // check if alertmd5 is present in regular scan list. If yes, 
        // then update the flag in finalAlertMd5Report
        for (let regularAlert of regularAlerts) {
            if (alertmd5ToCheck.includes(regularAlert.alertmd5)) {
                finalAlertMd5Report[regularAlert.alertmd5].isExistInRegularScan = true;
            }
        }

        // check if alertmd5 is present in replay scan list. If yes,
        // then update the flag in finalAlertMd5Report

        for (let replayAlert of replayAlerts) {
            if (alertmd5ToCheck.includes(replayAlert.alertmd5)) {
                finalAlertMd5Report[replayAlert.alertmd5].isReplayedInReplayScan = true;
                finalAlertMd5Report[replayAlert.alertmd5].replayAlertId = replayAlert.id;
                this.replayStatus[websiteId].alertmd5s[replayAlert.alertmd5].replayalertid = replayAlert.id;
                finalAlertMd5Report[replayAlert.alertmd5].isAlertFoundInReplay = true;
            }
        }

        // check if both the flags are true. If yes, then get their respective alert details
        // update the finalAlertMd5Report with alert details
        for (let alertmd5 of alertmd5ToCheck) {
            if (finalAlertMd5Report[alertmd5].isExistInRegularScan && finalAlertMd5Report[alertmd5].isReplayedInReplayScan) {
                let replayAlertDetail = await ssAPI.getAlertDetail(finalAlertMd5Report[alertmd5].replayAlertId);
                finalAlertMd5Report[alertmd5].replayAlertDetail = replayAlertDetail;
            }
        }

        // check if both the flags are true. If yes, then chech replay alert detail and confirm 
        // if scanner did replyed it properly or not by checking the request header
        // attack.httpRequest.headers[X-IFC-REPLAY] and see if it has the alertmd5 or not. If yes, then update the flag in finalAlertMd5Report
        // that isReplayed to true.

        for (let alertmd5 of alertmd5ToCheck) {
            if (finalAlertMd5Report[alertmd5].isExistInRegularScan && finalAlertMd5Report[alertmd5].isReplayedInReplayScan) {
                let replayAlertDetail = finalAlertMd5Report[alertmd5].replayAlertDetail;
                let replayAlertRequestHeader = replayAlertDetail.attack.httpRequest.headers;

                if (replayAlertRequestHeader['X-IFC-REPLAY'] && replayAlertRequestHeader['X-IFC-REPLAY'].includes(alertmd5)) {
                    finalAlertMd5Report[alertmd5].isReplayed = true;
                    this.replayStatus[websiteId].alertmd5s[alertmd5].haikuReplayStatus = 'replayed';
                }
                else if(replayAlertRequestHeader['X-IFC-REPLAY']) {
                    //TO DO: Check if alertmd5 present in kibana logs for now just update the flag
                    finalAlertMd5Report[alertmd5].isReplayed = true;
                    finalAlertMd5Report[alertmd5].anothermd5 = replayAlertRequestHeader['X-IFC-REPLAY'];
                    this.replayStatus[websiteId].alertmd5s[alertmd5].haikuReplayStatus = 'replayed';
                }
            }
        }

        // check if isReplayed flag is false and isExistInRegularScan is true and isReplayedInReplayScan false. If yes, then check if the alert is present in revalidateVulns.json file
        // if yes, then update the flag in finalAlertMd5Report that inRevalidateVulns is true.
        // also check if the alert is present in alertDetails.json file. If yes, then update the flag in finalAlertMd5Report
        // that inAlertDetails is true.

        for (let alertmd5 of alertmd5ToCheck) {
            if (!finalAlertMd5Report[alertmd5].isReplayed && finalAlertMd5Report[alertmd5].isExistInRegularScan && !finalAlertMd5Report[alertmd5].isReplayedInReplayScan) {
                let revalidateVulnsInfo = await this.getRevalidateVulns(serviceId);
                let alertDetailsInfo = await this.getAlertDetails(serviceId);
                //check if alertmd5 is includes in revalidateVulnsInfo
                if (JSON.stringify(revalidateVulnsInfo).includes(alertmd5)) {
                    finalAlertMd5Report[alertmd5].inRevalidateVulns = true;
                }
                //check if alertmd5 is includes in alertDetailsInfo
                if (JSON.stringify(alertDetailsInfo).includes(alertmd5)) {
                    finalAlertMd5Report[alertmd5].inAlertDetails = true;
                }

                if(!finalAlertMd5Report[alertmd5].inRevalidateVulns && !finalAlertMd5Report[alertmd5].inAlertDetails) {
                    this.replayStatus[websiteId].alertmd5s[alertmd5].haikuRemarks = 'Run fresh regular scan & replay scan';
                }
                
                if(finalAlertMd5Report[alertmd5].inRevalidateVulns || finalAlertMd5Report[alertmd5].inAlertDetails) {
                    // Check if replay scan is success, if yes then check it in kibana logs
                    // if its partial then update haikuRemarks as 'Run fresh replay scan'
                    this.replayStatus[websiteId].alertmd5s[alertmd5].haikuRemarks = 'Scanner team to check';
                }
            }
        }
    }

    async updateProtectionStatus() {
        let data = fs.readFileSync(this.protectionStatusFile, 'utf8');
        let lines = data.split(/\r?\n/);
        let columns = lines[0];
        
        for (let i = 1; i < lines.length; i++) {
            let line = lines[i];
            let values = line.split(',');

            for (let websiteId of Object.keys(this.replayStatus)) {
                if (values[this.website_id_column_index] == websiteId) {
                    let alertMd5s = Object.keys(this.replayStatus[websiteId].alertmd5s);

                    for (let alertMd5 of alertMd5s) {
                        if (values[this.alert_md5_column_index] == alertMd5) {
                            values[this.remark_column_index] = this.replayStatus[websiteId].alertmd5s[alertMd5].haikuReplayStatus;
                            values[this.comments_column_index] = this.replayStatus[websiteId].alertmd5s[alertMd5].haikuRemarks;
                            values[this.replayalertid_column_index] = this.replayStatus[websiteId].alertmd5s[alertMd5].replayalertid;
                            values[this.replayedscanlogid_column_index] = this.replayStatus[websiteId].replayedscanlogid;
                            lines[i] = values.join(',');
                            break;
                        }
                    }
                }
            }
        }

        let updatedData = lines.join('\n');
        fs.writeFileSync(path.dirname(this.protectionStatusFile) + '/updated-' + path.basename(this.protectionStatusFile), updatedData);
    }
}

// --- Main code
let argv = Minimist(process.argv.slice(2), {
    boolean: true,
    alias: {
        f: "oob-vuln-file"
    }
})

async function main() {
    let protectionStatusFile = argv['protectionStatusFile'];

    if (!protectionStatusFile) {
        logger.error(`protectionStatusFile is required`);
        return;
    }

    logger.log('info', `running replay vulnerability checks for protectionStatusFile=${protectionStatusFile}`);
    let replayVulnerabilityChecks = new ReplayVulnerabilityChecks(protectionStatusFile);
    await replayVulnerabilityChecks.getProtectionStatusData();
    await replayVulnerabilityChecks.processEachWebsite();
    await replayVulnerabilityChecks.updateProtectionStatus();
    console.log('done');
}

if (argv['protectionStatusFile']) {
    console.info(`arguments received for protectionStatusFile=${argv['protectionStatusFile']}`);
    // main / module exports based on command line args
    (async function (params) {
        await main();
    })();
} else {
    module.exports = ReplayVulnerabilityChecks
}