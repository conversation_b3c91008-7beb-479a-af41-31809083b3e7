const { INPUT_TYPE_INPUT } = require('../../common/config/app-constants.js')
const utils = require('../ifc-utils.js')
const RealClickAction = require('./real-click-action.js')
const _ = require('lodash')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// 'real' type action
class TypeAction {
    constructor(xpath, text, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.text = text
        this.annotation = annotation
    }

    get actionType() {
        return 'type'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.text, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        let encXPath = utils.encode(this.xpath)

        let actionSucceeded = await executer.triggerAction(new RealClickAction(this.getXPath()), executionContext)
        if (actionSucceeded) {
            let setInputValue = _.get(executionContext, 'scanner.config.setInputValue');

            if(setInputValue) {
                let jscode = `indusfaceRenderer.setInputValue('${encXPath}', '${this.text}')`
                await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true))
            }
            else {
                // select all and replace with the new text
                // this is done to handle cases where the text is not getting replaced
                // due to some JS event handling
                await browser.webContents.selectAll()
                await browser.webContents.replace('')
                await browser.webContents.unselect()
                for (var ch of this.text) {
                    await browser.webContents.sendInputEvent({
                        type: 'keydown',
                        keyCode: ch + ""
                    })
                    await browser.webContents.sendInputEvent({
                        type: 'char',
                        keyCode: ch + ""
                    })
                    await browser.webContents.sendInputEvent({
                        type: 'keyup',
                        keyCode: ch + ""
                    })
                }

                // wait for the above to settle as otherwise we sometimes type in the middle  (after the value= below)
                await utils.sleep(250)

                // set the value on the element directly
                // do this always since click to select is not always reliable with events & overlays etc.
                let jscode = `indusfaceRenderer.setTextValue('${encXPath}', '${this.text}')`
                await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true))
            }
        }


        //Trigger a validation function when the user tabs away from an input element on certain input elements
        //i.e. https://demo.metrologika.eu/#/login - TO-8545
        try {
            let triggerValidation = _.get(executionContext, 'scanner.config.triggerValidation');

            if(triggerValidation) {
                jscode = `indusfaceRenderer.dispatchInputEventOnElement('${encXPath}', '${INPUT_TYPE_INPUT}', '\t')`
                await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true))
            }
        } catch (error) {
            console.error(`Error occured dispatchInputEventOnElement, reason: ${error.toString()}`);
        }


        // force success since not being able to type is not fatal. This is to prevent 
        // issues on sites like matsya where the hidden @id control always fails
        // TODO: Investigate ... maybe this can change now that we have visibility filtering...
        actionSucceeded = true

        return actionSucceeded
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} '${this.text}' into ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = TypeAction