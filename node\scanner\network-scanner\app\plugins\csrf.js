// const VectorResponseAttack = require('./vector-response-attack')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RegExpVari = require('./generic-regexp');
const HaikuUtils = require('../../../common/lib/haiku-utils')
const request = require('request');
// const LoginDelegate = require('../../lib/login-delegate')

class CSRFvuln extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-csrf-vuln'
    }

    /*  getAttackVectors() {
         return nothingToAttack
     }
 
     getAttackableEvents() {
         return ['http-methods']
     }*/

    /* async performNetworkAttack(attack) {
        return await super.performNetworkAttack(attack)

        /* let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.skip == true) {
            pluginDataForRequest.skip = false
            return false
        }
        let respStatus = _.get(attack, 'originalRequest.httpResponse.statusCode')
        let redirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '0')
        if (respStatus == 200 && attack.attackArea == "HTTPMethods" && attack.originalRequest.httpRequest.method == 'POST' && redirects == 0) {
            delete attack.httpRequest.headers.Cookie
            delete attack.httpRequest.headers.Referer
            return await super.performNetworkAttack(attack)
        } 
    } */

    processNewRequest(request) {
        let attack = {
            httpRequest: _.cloneDeep(request.httpRequest),
            originalRequest: request,
            encoding: "raw"
        }
        let CSRFtokenFound = false
        let requestHeader = Object.entries(attack.httpRequest.headers).join('&').replace(/,/g, ': ').split('&')
        if (regexpCSRF.test(requestHeader)) {
            CSRFtokenFound = true
        }
        //check if csrf token is in request cookie header
        let requestCookie = _.get(attack, 'httpRequest.headers.Cookie', '');
        if (!CSRFtokenFound && requestCookie.length > 0 && regexpCSRF.test(requestCookie)) {
            CSRFtokenFound = true
        }
        let ReqBody = attack.httpRequest.body
        if (!CSRFtokenFound && ReqBody && regexpCSRF.test(ReqBody)) {
            let params = ReqBody.split('&')
            for (let ele of params) {
                ele = ele.split('=')
                if (regexpCSRF.test(ele[0])) {
                    CSRFtokenFound = true
                }
                else if (/token/i.test(ele[0]) && /[\w\-]{28,50}/.test(ele[1])) {
                    CSRFtokenFound = true
                }
            }
        }
        if (!CSRFtokenFound && !/Expire|logout|404|signout|error/i.test(attack.httpRequest.uri)) {
            let newheader = Object.entries(attack.httpRequest.headers)
            let arr = []
            for (const [key, value] of newheader) {
                // if (!/Cookie|Referer|Origin/i.test(key)) {
                if (/Referer|Origin/i.test(key)) {
                    arr.push([key, 'http://www.haikucsrftest.com'])
                }
                // if (!/Cookie|Referer|Origin/i.test(key)) {}
                // else if (!/Cookie/i.test(key)) {
                else {
                    arr.push([key, value])
                }
            }
            attack.httpRequest.headers = Object.fromEntries(arr);
            delete attack.httpRequest.headers.Origin
            this.performNetworkAttack(attack)
        }
        else {
            return false
        }
        // attack.httpRequest.followRedirect = () => { return false }
    }

    wantProcessAttackResponse(attack) {
        if (attack.pluginName == 'Original Crawler Request') {
            if (attack.httpRequest.method == 'POST') { //Currently we are only validating POST method, we will add GET method in future after fixing FPs/TNs - Ansari
                if (/Expire|logout|404|signout|error/i.test(attack.httpRequest.uri)) { return false }
                let Reqbody = _.get(attack, 'httpRequest.body', '')
                if (!Reqbody.includes('=')) { return false }
                let pluginDataForRequest = this.getPluginScopedStore(attack)
                // get session info for the current Request
                let loginDts = HaikuUtils.getLoginParams(attack.httpRequest)
                if (loginDts && loginDts.usernameParams.length != 0) { return false }

                let origSessionInfo = HaikuUtils.getSessionInfo(attack.httpRequest)
                if (origSessionInfo.status == null && origSessionInfo.uriPath == null) {
                    return false
                }
                let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
                let OristatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')

                if (attack.httpRequest.loginInfo || redirects > 0 || OristatusCode != 200) {
                    // pluginDataForRequest.skip = true
                    return false
                }

                if (Reqbody.length < 2 || !/\w+/.test(Reqbody)) { return false }
                if ((/(?:Password|passwd?|pwd|search|ps?wd)[\w\-]*?["=]/i.test(Reqbody) || /(?:log|sign)[-_]?(?:in|up|out)|forg[oe]t[-_]?(?:password|username|pswd)/i.test(attack.href)) && !/change/i.test(attack.href)) {
                    // pluginDataForRequest.skip = true
                    return false
                }

                let ResBody = _.get(attack, "result.resp.body", '')
                if (ResBody.length < 5 || !/\w/.test(ResBody)) {
                    // pluginDataForRequest.skip = true
                    return false
                }

                //condition to skip for the custom error pages
                const responseBody = ResBody.toLowerCase();

                // Check each category of error messages
                const errorCategories = [
                    RegExpVari.ErrorMessages.HttpErrors,
                    RegExpVari.ErrorMessages.SecurityErrors,
                    RegExpVari.ErrorMessages.SessionErrors,
                    RegExpVari.ErrorMessages.SystemErrors,
                    RegExpVari.ErrorMessages.WafErrors,
                    RegExpVari.ErrorMessages.GeneralErrors
                ];

                // Early return if any error message is found
                for (const category of errorCategories) {
                    if (category.some(error => responseBody.includes(error))) {
                        return false;
                    }
                }

                // Check for WAF servers in headers
                let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
                ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

                if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
                    return false;
                }

                if (/(?:(?:log|sign)[-_]?(?:in|up|out)|forgot[-_]?password).*?<\/(?:title|h\d)|(?:Invalid|Incorrect) (?:Username|Password|Username or Password)|Login failed|Authentication Failed|The username or password you have entered is incorrect|type ?= ?"password"|(?:account|user)[\w\-\:\s]+(?:locked|disabled|inactive)|(locked|disabled|inactive)[\w\-\:\s]+(?:account|user)|You are being rate limited|(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/i.test(ResBody)) {
                    // pluginDataForRequest.skip = true
                    return false
                }

                let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
                let possibleCType = /(application\/json|text\/(?:plain|xml))/i
                if (possibleCType.test(contentTypeHeaderVal) && /"(?:status|Result)" ?: ?"?false|ErrorCode\\?":\\?"?(?!0)\w|"IsRequestSuccessfull":false/i.test(ResBody)) {
                    // pluginDataForRequest.skip = true
                    return false
                }
                else if (/{"errors?" ?:|"errors?" ?: ?false|{"found":false}|>Error(?: !!!)?<\/(h|title)/i.test(ResBody)) {
                    // pluginDataForRequest.skip = true
                    return false
                }

                if (OristatusCode == 200) {
                    let Clength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', '')
                    if (!Clength) {
                        let OriResBody = Buffer.from(attack.result.resp.body, 'utf-8').length
                        attack.result.resp.httpResponse.headers["content-length"] = '' + OriResBody + ''
                        pluginDataForRequest.CSRFvuln = { OriResBodylength: OriResBody }
                    }
                }
            }
        }
        if (attack.pluginName == this.getName()) {
            return true
        }
        return false
    }

    async processAttackResponse(attack) {
        // if (attack.attackArea == "original-crawler-request") { return }
        let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
        let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let Atkredirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '0')
        if (OristatusCode != AtkstatusCode || Atkredirects != 0) { return }

        if (attack.httpRequest.method == "POST") {
            let ReqBody = attack.httpRequest.body
            /* let params = ReqBody.split('&')
            for (let ele of params) {
                ele = ele.split('=')
                if (regexpCSRF.test(ele[0])) {
                    return
                }
                /* if (/token/i.test(ele[0]) && /[\w\-]{28,50}/.test(ele[1])) {
                    return
                } /
            } */

            let NewResponse = []
            let uriRaw = attack.httpRequest.uri
            let newheader = attack.originalRequest.httpRequest.headers
            delete newheader.Cookie
            newheader["Content-Length"] = '' + Buffer.from(ReqBody, 'utf-8').length + ''
            const options = {
                headers: newheader,
                body: ReqBody,
                uri: uriRaw,
                rejectUnauthorized: false,
                timeout: 30000
            };
            NewResponse = await this.AtkPOSTRequest(options)  //For check auth, Note: feature request was raised to nilesh
            if (NewResponse && NewResponse != 'NotFound' && NewResponse.statusCode != 408) {
                let Atkredirects = _.get(NewResponse, 'headers.location', '').length
                let OristatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
                let AtkstatusCode = NewResponse.statusCode
                if (Atkredirects > 0) {
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `No CSRF token to prevent CSRF attack.`)
                    return
                }
                if (Atkredirects == 0) {
                    if (OristatusCode == 200 && AtkstatusCode == 200) {
                        let OriClength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', '')
                        let AtkClength = _.get(NewResponse, 'headers["content-length"]', '')
                        let AtkResbody = Buffer.from(NewResponse.resbody, 'utf-8').length
                        let OriResBody = Buffer.from(attack.result.resp.body, 'utf-8').length
                        if (/�/.test(NewResponse.resbody) && !/�/.test(attack.result.resp.body)) { return }

                        if (OriClength > 0 && AtkClength > 0 && OriClength != AtkClength) {
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `No CSRF token to prevent CSRF attack.`)
                            return
                        }

                        if (AtkResbody > 0 && OriResBody != AtkResbody) {
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `No CSRF token to prevent CSRF attack.`)
                            return
                        }
                    }
                }
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', [attack.httpRequest.body]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
    }

    AtkPOSTRequest(options) {
        return new Promise((resolve) => {
            try {
                request.post(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body, statusMessage: res.statusMessage }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }
}
/* const nothingToAttack = [
    VectorResponseAttack.identityVector
] */
let regexpCSRF = /csrf|Requestverification|xsrf|AntiXsrfToken|TokenAntiXsrf|hdfgsajkl|nonce|user_token|__VIEWSTATE|map_tkn/i

module.exports = CSRFvuln