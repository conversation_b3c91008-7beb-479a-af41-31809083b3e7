// Browser actions related methods 
// Any interaction with elements takes XPath as parameter and the parameter is assumed to be 
// base64 encoded to ensure that it can be passed between renderer and main easily. 
// specifically, prameter us run through JSON.parse(atob(..))
class IndusfaceEvents {
    constructor() {}

    name(short) {
        return short ? "IFC-Events" : "Indusface Browser Events Plugin"
    }

    init() {
        this.hookAddEventListener()
    }

    hookAddEventListener() {
        // Hook add event listerner method to extract DOM Level 2 events
        let elementProto = (window.EventTarget ? window.EventTarget : window.HTMLElement ? window.HTMLElement : window.Element).prototype
        let elementProtoMethods = {
            'addEventListener': elementProto.addEventListener
        }
        elementProto.addEventListener = function () {
            indusfaceRenderer.invokePluginMethod('OnAddEventListener', this, ...arguments)
            return elementProtoMethods.addEventListener.call(this, ...arguments);
        }
    }


}

module.exports = IndusfaceEvents