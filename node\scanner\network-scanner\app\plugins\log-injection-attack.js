const _ = require('lodash')
const RE2 = require('re2')
const NetworkAttack = require('./network-attack')
const RegExpVari = require('./generic-regexp')

const LogMatch = [
    /log\sdata\sinserted\ssuccessfully/i,
    /\d\smessage(s|\s)were\swritten\sto\sthe\slog/i,
    /message(s|\s)successfully\sinserted/i,
    /log(s|\s)successfully\swritten/i,
    /sucessfully\sinserted\slog\sdata/i,
    /log\swritten/i,
    /log\sinserted/i,
    /message\swritten/i,
    /message\sinserted/i,
    /message\s(.*)success\s(.*)inserted/i,
    /message\s(?:logged|written|inserted)\ssuccessfully/i,
    /message\s(.*)written/i,
    /inserted\s(.*)message/i,
    /written\s(.*)message/i,
    /log\s(.*)success(.*)inserted/i,
    /log\s(.*)success/i,
    /log\s(.*)written/i,
    /inserted\s(.*)log/i,
    /written\s(.*)log/i,
    /success\s(.*)log/i,
]

class LogInjAttack extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-log-injection'
        this.matchRegexp = new RE2(LogMatch.map(v => v.source).join('|'), "i")
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea === "original-crawler-request") {
            const originalMethod = _.get(attack, 'originalRequest.httpRequest.method')
            const contentType = _.get(attack, 'originalRequest.httpRequest.headers["Content-Type"]', "text/html").toLowerCase()
            if (originalMethod === "POST" && contentType.includes("json")) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.loginjection) return

        if (attack.result.resp.httpResponse.statusCode === 200) {
            const ResBody = _.get(attack, 'result.resp.body', '')

            const customErrors = [
                RegExpVari.RegExp.CustomErrMsg1,
                RegExpVari.RegExp.CustomErrMsg2,
                RegExpVari.RegExp.CustomErrMsg3,
                RegExpVari.RegExp.CustomErrMsg4,
                RegExpVari.RegExp.CustomErrMsg5,
            ]
            if (customErrors.some(re => re.test(ResBody))) return

            this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
            pluginDataForRequest.loginjection = true
        }
    }
}

module.exports = LogInjAttack
