const _ = require('lodash')
const RE2 = require('re2')
const NetworkAttack = require('./network-attack')
const RegExpVari = require('./generic-regexp')

const LogMatch = [
    /log\sdata\sinserted\ssuccessfully/i,
    /\d\smessage(s|\s)were\swritten\sto\sthe\slog/i,
    /message(s|\s)successfully\sinserted/i,
    /log(s|\s)successfully\swritten/i,
    /sucessfully\sinserted\slog\sdata/i,
    /log\swritten/i,
    /log\sinserted/i,
    /message\swritten/i,
    /message\sinserted/i,
    /message\s(.*)success\s(.*)inserted/i,
    /message\s(?:logged|written|inserted)\ssuccessfully/i,
    /message\s(.*)written/i,
    /inserted\s(.*)message/i,
    /written\s(.*)message/i,
    /log\s(.*)success(.*)inserted/i,
    /log\s(.*)success/i,
    /log\s(.*)written/i,
    /inserted\s(.*)log/i,
    /written\s(.*)log/i,
    /success\s(.*)log/i,
]

class LogInjAttack extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-log-injection'
        this.matchRegexp = new RE2(LogMatch.map(v => v.source).join('|'), "i")
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea === "original-crawler-request") {
            const originalMethod = _.get(attack, 'originalRequest.httpRequest.method')
            const contentType = _.get(attack, 'originalRequest.httpRequest.headers["Content-Type"]', "text/html").toLowerCase()
            if (originalMethod === "POST" && contentType.includes("json")) {
                return true
            }
        }
        return false
    }

    processAttackResponse(attack) {
        const pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.loginjection) return

        if (attack.result.resp.httpResponse.statusCode === 200) {
            const ResBody = _.get(attack, 'result.resp.body', '')

            //condition to skip for the custom error pages
            const responseBody = ResBody.toLowerCase();

            // Check each category of error messages
            const errorCategories = [
                RegExpVari.ErrorMessages.HttpErrors,
                RegExpVari.ErrorMessages.SecurityErrors,
                RegExpVari.ErrorMessages.SessionErrors,
                RegExpVari.ErrorMessages.SystemErrors,
                RegExpVari.ErrorMessages.WafErrors,
                RegExpVari.ErrorMessages.GeneralErrors
            ];

            // Early return if any error message is found
            for (const category of errorCategories) {
                if (category.some(error => responseBody.includes(error))) {
                    return;
                }
            }

            // Check for WAF servers in headers
            let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
            ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

            if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
                return;
            }

            this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
            pluginDataForRequest.loginjection = true
        }
    }
}

module.exports = LogInjAttack
