const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

class UnEncodedCharacters extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-unencoded-characters'
    }

    getAttackVectors() {
        return UnencodedVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params', 'json-body']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', '<PERSON>ie']
            })
        }
    }

    wantProcessAttackResponse(attack) {
        let rawHtml = _.get(attack, 'result.resp.body')
        if (attack.pluginName == this.getName() && /haikutest/i.test(rawHtml)) {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CharFound) { return }

        let ResBody = _.get(attack, 'result.resp.body', '')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')
        let possibleCType = /(application\/json|text\/plain)/i
        let regexp = new RegExp(_.escapeRegExp(attack.vector))

        if (ResBody.length > 20 && regexp.test(ResBody) && !possibleCType.test(contentTypeHeaderVal)) {
            let details = {
                result: attack.vector
            }

            if (details.result.length > 10) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                pluginDataForRequest.CharFound = true
                return
            }
        }

        if (ResBody.length > 20 && regexp.test(ResBody) && possibleCType.test(contentTypeHeaderVal) && statusCode == 200) {
            let details = {
                result: attack.vector
            }

            if (details.result.length > 10) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                pluginDataForRequest.CharFound = true
                return
            }
        }
    }
}
// Injected Vector String
const UnencodedVectors = [
    `<xss=haikutest(1)>`, //API & AA
    `{[$*('haikutest)"]}`, //AA
]

module.exports = UnEncodedCharacters

/** Disabled due to FP
 * constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-unencoded-characters'
        this.count = 0;
 * //Removed re2 as it does not support positive look ahead and look after due to
        // reDos vulnerability hence changing the below code to RegExp

        // Combine the regexps - more efficient than running 'n' matches  
        this.matchRegex = new RegExp(UnencodedMatch.map((v) => {
            return v.source
        }).join('|'), "i")

        // Combine the regexps - more efficient than running 'n' matches  
        this.subString = new RegExp(specialCharsMatch.map((v) => {
            return v.source
        }).join('|'), "igm")

        // get encoded bad chars to check
        this.encodedBadChars = []
        for (let i = 0; i < badChars.length; i++) {
            let encoded = he.encode(badChars[i], {
                useNamedReferences: true
            })
            if (encoded == badChars[i]) {
                continue;
            }
            this.encodedBadChars.push(encoded)
        }
 * looking the response body for all possible vulnerabilities
        let allValues = this.findUnencodedChars(attack)
        if (allValues && allValues.length > 0) {
            let unencodedCharsMatch = allValues[0]
            let subStringMatch = allValues[1]
            if (unencodedCharsMatch) {
                let vuln = {
                    details: {
                        subStringMatch,
                        unencodedCharsMatch
                    }
                }
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            }
        }
 
findUnencodedChars(attack) {
    let body = _.get(attack, 'result.resp.body')
    if (!body) {
        return false
    }
    let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
    if (!statusCode || statusCode == 404) {
        return false
    }

    //get all the special chars between two strings as haiku846...haiku846
    let getSubString = this.matchRegex.exec(body)

    let ourMatch = []
    let matches = false

    do {
        //Evaluate the substring against the special chars regex to find if any unencoded chars are present
        matches = this.subString.exec(getSubString)

        if (matches && matches.length > 0) {
            if (matches[0]) { return }
            ourMatch.push(matches[0])
        }
    } while (matches)

    //Join array values comma separated
    ourMatch = ourMatch.join();

    /** 
     * //Seems below function is written to replace few of the bad chars which are found in our 
     * encoded list
     *  
     this.encodedBadChars.forEach(enc => {
         ourMatch = ourMatch.replace(enc, '')
     }); 
    if (ourMatch) {
        return [ourMatch, getSubString[0]]
    }
    return false
}

}

// Injected Vector String
const UnencodedVectors = [
`<xss=haikutest(1)>`,
`{[$*haikutest]}`,
`haiku846][="'>< \`;*-=&{()} ^haiku846`]

// To match string between two strings
const UnencodedMatch = [/(?<=haiku846).*(?=haiku846)/]

//To match all unencoded chars in given string
const specialCharsMatch = [/[\]\[="'><`;*\-&{}()\s^]/] //[/[\]\[\=\"\'\>\< \`\;\*\-\&\{\}\(\)\s]/]

const badChars = '][="\'>< \`;*-=&{()} ^'*/

