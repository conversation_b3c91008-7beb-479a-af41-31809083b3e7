const debug = require('debug')('HttpHeadersChecker')
const NetworkAttack = require('./network-attack')
const URL = require('url').URL
const _ = require('lodash')
const caseless = require('caseless')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

class HttpHeadersChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.httpBasicAuthenticationVulnID = 'ID-Http-basic Authentication'
        // this.xssvulnIDLow = 'ID-xss-header-protection-low'
        // this.xssvulnIDMeduim = 'ID-xss-header-protection-medium'
        // this.hSTSVulnID = 'ID-hsts-header-missing'
        this.mimeVulnIDMedium = 'ID-mime-sniffing'
        this.mimeVulnIDLow = 'ID-server-content-sniffing-low'
        this.ghostvulndetected = 'ID-ghost-vuln-detected'
        this.chromeLoggerInformationDisclosureVulnID = 'ID-chrome-log-info-disclosure'
        this.JWTmisconfigurationID = 'ID-JWT-misconfiguration'
        this.JWTIncorrectSessionTimeout = 'ID-JWT-Incorrect-Session-Timeout'
        this.JWTnonealgorithm = 'ID-JWT-none-algorithm'
        this.CredentialinToken = 'ID-credential-in-token'
        this.CSPHeadersNotSetVuln = 'ID-csp-headers-not-set'
        this.XPermittedCDP = 'ID-X-Permitted-CDP'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    /* wantProcessAttackResponse(attack) {

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (attack.attackArea == "original-crawler-request") {
            return true
        }
        return false
    } */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        if (attack.attackArea == "original-crawler-request") {
            // cheching for request specific plugin data in specific functions
            this.checkHttpBasicAuthentication(attack)

            // web server version discousre is specialzied case of info disclousre
            //this.checkInfoDisclosureVulnerability(attack) - moved to server-dts-found.js

            // check if xss header present with specific value or not
            // this.checkXssHeaderProtectionVulnerability(attack) - Deprecated - https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-XSS-Protection

            //check if hsts header is present or not
            // this.checkHSTSHeaderMissingVulnerability(attack)

            //check if hsts header is present or not
            this.checkMimeSniffingVulnerability(attack)

            //check if chrome logger extension is enabled on production systems
            //Verify if X-ChromePhp-Data or X-ChromeLogger-Data headers is present or not
            this.checkChromeLoggerInformationDisclosure(attack)

            //check specific response header for php version's vulnerable to GHOST attack
            this.ghostVulnerability(attack)

            //check user information and session expiry in JWT token.
            this.JWTmisconfigurationVulnerability(attack)

            //this.CredentialinToken = 'ID-credential-in-token'
            this.CredentialinTokenVulnerability(attack)
            this.CSPHeadersNotSetVulnerability(attack)
            this.CheckXPermittedCDP(attack)
        }
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     */
    CheckXPermittedCDP(attack) {
        // Unset/Insecure X-Permitted-Cross-Domain-Policies Header
        //Plugin request scan scope - this scan, means to report one only in entire scan
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        //X-Permitted-Cross-Domain-Policies: none
        if (!pluginStorageScanScope.XpcdpHdrVulnFound) {
            // Define valid status codes & content types
            const VALID_STATUS_CODES = [200, 204]; // Only check successful responses
            const VALID_CONTENT_TYPE_REGEX = /text\/html|application\/xml|text\/xml|application\/json/;

            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '');
            let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '');

            // Skip if status code is not valid or content type doesn't match
            if (!VALID_STATUS_CODES.includes(statusCode) || !VALID_CONTENT_TYPE_REGEX.test(RescontentType)) {
                return;
            }

            // Get the value of the X-Permitted-Cross-Domain-Policies header
            let XpcdpHdrVal = _.get(attack, 'result.resp.httpResponse.headers["x-permitted-cross-domain-policies"]', false);

            // If the header is missing or has insecure values, flag as vulnerable and report
            if (!XpcdpHdrVal || /all|by-content-type|by-ftp-filename/i.test(XpcdpHdrVal)) {
                let details = {
                    result: `Vulnerable: X-Permitted-Cross-Domain-Policies header is either missing or insecure: (${XpcdpHdrVal || "missing"}).\n Impact: "This vulnerability could lead to resource abuse, including cross-domain requests that are unintentional or malicious."`
                };
                this.addVulnerabilitytoResult(attack, this.XPermittedCDP, details)
                pluginStorageScanScope.XpcdpHdrVulnFound = true
            }
        }
    }

    CSPHeadersNotSetVulnerability(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CSPHeadersNotSetVuln) {
            return
        }

        let ResBody = _.get(attack, 'result.resp.body', '');
        if (!ResBody.trim()) return;
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')

        if (!/^(200|201|202|203|206)$/.test(statusCode) ||
            !/text\/html|application\/xhtml\+xml|text\/javascript|application\/javascript|text\/css|image\/svg\+xml|text\/markdown/i.test(RescontentType)) {
            return; // Skip CSP check if conditions are not met
        }

        //condition to skip for the custom error pages
        const responseBody = ResBody.toLowerCase();
            
        // Check each category of error messages
        const errorCategories = [
            RegExpVari.ErrorMessages.HttpErrors,
            RegExpVari.ErrorMessages.SecurityErrors,
            RegExpVari.ErrorMessages.SessionErrors,
            RegExpVari.ErrorMessages.SystemErrors,
            RegExpVari.ErrorMessages.WafErrors,
            RegExpVari.ErrorMessages.GeneralErrors
        ];

        // Early return if any error message is found
        for (const category of errorCategories) {
            if (category.some(error => responseBody.includes(error))) {
                return;
            }
        }

        // Check for WAF servers in headers
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()
        
        if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
            return;
        }

        let CSPheader = _.get(attack, 'result.resp.httpResponse.headers["content-security-policy"]', false)

        if (!CSPheader || !/(script-src|object-src|base-uri|require-trusted-types-for|report-uri|report-to|frame-ancestors|style-src|img-src|connect-src|form-action|default-src)/i.test(CSPheader)) {
            this.addVulnerabilitytoResult(attack, this.CSPHeadersNotSetVuln,
                'Missing/Inseure CSP directives - (${CSPheader || "missing"}). Impact: The absence of key Content Security Policy (CSP) directives increases the risk of various security threats, including:\n' +
                '- Cross-Site Scripting (XSS): Malicious scripts can be injected and executed.\n' +
                '- Clickjacking: Attackers can embed the site in an iframe to trick users.\n' +
                '- Data Exfiltration: Unrestricted image, script, and connection sources can lead to sensitive information leaks.\n' +
                '- Code Injection: Unsafe inline scripts, styles, and plugin execution may allow attackers to exploit vulnerabilities.\n' +
                '- Phishing Attacks: Forms may be submitted to untrusted domains, increasing credential theft risks.\n');
            pluginDataForRequest.CSPHeadersNotSetVuln = true;
        }
    }


    CredentialinTokenVulnerability(attack) {
        let isRevalidationAttack = this.isRevalidationAttack(attack);

        if (isRevalidationAttack) {
            return;
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.CredentialFound) {
            return
        }

        //httpRequest header keys are case sensitive. so let use caseless.
        let headers = caseless(_.get(attack, 'originalRequest.httpRequest.headers', ""));
        let authjwt = headers.get('authorization');
        if (!authjwt) {
            return
        }
        if (/^bearer/i.test(authjwt)) { return }

        let currToken = authjwt
        if (/^basic/i.test(authjwt)) {
            let RegExp_token = /(?:[\w\-\.=])+$/i
            try { currToken = RegExp_token.exec(authjwt)[0] } catch (e) { return }
        }

        let tokenval = Buffer.from(currToken, 'base64').toString()
        if (tokenval.includes('"typ":"JWT"')) { return }
        let part = tokenval.split(':')
        if (part.length > 1 && part[0].length > 3 && part[1].length > 3 && !part[0].includes('�') && !part[1].includes('�')) {
            let username = Buffer.from(part[0], 'base64').toString()
            let password = Buffer.from(part[1], 'base64').toString()
            let details = ''
            if ((username.includes('�') || password.includes('�')) && part.length == 2) {
                if (!/[a-z0-9]{30,}/i.test(part[0]) && !/[a-z0-9]{30,}/i.test(part[1])) {
                    details = { "result": tokenval }
                }
            }
            if (!username.includes('�') && !password.includes('�')) {
                if (!/[a-z0-9]{30,}/i.test(username) && !/[a-z0-9]{30,}/i.test(password)) {
                    details = { "result": username + ':' + password }
                }
            }
            if (details.length > 0) {
                this.addVulnerabilitytoResult(attack, this.CredentialinToken, details)
                pluginDataForRequest.CredentialFound = true
            }
        }
    }

    JWTmisconfigurationVulnerability(attack) {
        let isRevalidationAttack = this.isRevalidationAttack(attack);

        if (isRevalidationAttack) {
            return;
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.jwtmisconfVulnFound && pluginDataForRequest.jwtISTVulnFound && pluginDataForRequest.jwtnonealgoVulnFound) {
            return
        }

        //httpRequest header keys are case sensitive. so let use caseless.
        let headers = caseless(_.get(attack, 'originalRequest.httpRequest.headers', ""));
        let authjwt = headers.get('authorization');
        if (!authjwt) {
            return
        }
        if (!/^(bearer|basic)/i.test(authjwt)) { return }

        let tokenjwt = /(?:[\w\-\.=])+?$/i
        let currToken = ''
        try { currToken = tokenjwt.exec(authjwt)[0] } catch (e) { return }

        if (currToken.length > 0) {
            let part = currToken.split('.')
            if (part.length < 3) { return }

            //JWT - None Algorithm Exploit
            let jwttype = Buffer.from(part[0], 'base64').toString()
            if (jwttype.includes('"alg":"none"')) {
                let details = {
                    result: jwttype
                }
                this.addVulnerabilitytoResult(attack, this.JWTnonealgorithm, details)
                pluginDataForRequest.jwtnonealgoVulnFound = true
            }

            if (jwttype.includes('"typ":"JWT"')) {
                let payload = Buffer.from(part[1], 'base64').toString()
                //let signature = Buffer.from(part[2], 'base64').toString()

                //Part1 JWTmisconfiguration        
                let RegExp_argnames = /(user_?(name|id)?|passw(or)?d|\bname|pwd|mail|uid|phone|profile)/i
                if (RegExp_argnames.test(payload)) {
                    var Jsonparse = JSON.parse(payload)
                    let payloadKeyNames = Object.keys(Jsonparse).join(',')
                    if (RegExp_argnames.test(payloadKeyNames)) {
                        let details = { "result": payload }
                        this.addVulnerabilitytoResult(attack, this.JWTmisconfigurationID, details)
                        pluginDataForRequest.jwtmisconfVulnFound = true
                    }
                    if (!RegExp_argnames.test(payloadKeyNames)) {
                        let jasonvaule = Object.values(JSON.parse(payload))
                        for (let arg of jasonvaule) {
                            let currValue = arg
                            if (currValue.includes('{"')) {
                                let parse2 = Object.keys(JSON.parse(currValue)).join(',')
                                if (RegExp_argnames.test(parse2)) {
                                    let details = { "result": payload }
                                    this.addVulnerabilitytoResult(attack, this.JWTmisconfigurationID, details)
                                    pluginDataForRequest.jwtmisconfVulnFound = true
                                    break
                                }
                            }
                        }
                    }
                }

                //Part2 - Incorrect Session Timeout
                let expiredate = _.get(Jsonparse, 'exp', "")
                if (expiredate) {
                    const ExpDate = new Date(expiredate * 1000) //Zulu Format(date time)
                    let Serverdate = _.get(attack, 'result.resp.httpResponse.headers["date"]', "")
                    const SerDate = new Date(Serverdate) //Zulu Format(date time)
                    let ToExp = (ExpDate - SerDate) / (1000 * 60 * 60 * 24) //In days
                    const MaxDays = 2 //To report vul
                    if (ToExp < 0 || ToExp >= MaxDays) { // Session expired or more then one days to expire, ll report
                        let details_IST = {
                            ToExpDate: ExpDate,
                            Days: ToExp
                        }
                        this.addVulnerabilitytoResult(attack, this.JWTIncorrectSessionTimeout, details_IST)
                        pluginDataForRequest.jwtISTVulnFound = true
                    }
                }
            }
        }
    }

    ghostVulnerability(attack) {
        let fullurl = _.get(attack, "httpRequest.uri", "")
        let url = new URL(fullurl)
        if (url.pathname.endsWith('.php')) {
            let pluginscopeForRequest = this.getPluginScopedStore(attack)
            let xPoweredBy = _.get(attack, 'result.resp.httpResponse.headers["x-powered-by"]', '');
            let server = _.get(attack, 'result.resp.httpResponse.headers["server"]', '');
            let xAspNetVersion = _.get(attack, 'result.resp.httpResponse.headers["x-aspnet-version"]')
            let xAspNetMvcVersion = _.get(attack, 'result.resp.httpResponse.headers["x-aspnetmvc-version"]')

            let windowsonly = /asp.net|iis|microsoft|asp|win32/i.test(xPoweredBy) || xAspNetVersion != undefined || xAspNetMvcVersion != undefined || /iis|microsoft|asp|win32/i.test(server)
            if (windowsonly) {
                return
            }

            //reference links: 
            //https://www.tenable.com/plugins/nessus/81510
            //https://www.tenable.com/plugins/was/98829
            //https://www.php.net/ChangeLog-5.php#5.6.6
            //if (/(PHP\/5\.6\.[0-5]|PHP\/[0-4]\.\d.\d{1,2}|PHP\/5\.[0-4].[0-3][0-7])/i.test(xPoweredBy) || (/(PHP\/5\.6\.[0-5]|PHP\/[0-4]\.\d.\d{1,2}|PHP\/5\.[0-4].[0-3][0-7])/i.test(server)))

            if (/(PHP\/5\.6\.[0-5]|PHP\/5\.5\.2[0-1]|PHP\/5\.5\.[0-1][0-9]|PHP\/5\.4.3[0-7]|PHP\/5\.4.[0-2][0-9])/i.test(xPoweredBy) || (/(PHP\/5\.6\.[0-5]|PHP\/5\.5\.2[0-1]|PHP\/5\.5\.[0-1][0-9]|PHP\/5\.4.3[0-7]|PHP\/5\.4.[0-2][0-9])/i.test(server))) {
                if (!pluginscopeForRequest.ghostvulndetected) {
                    this.addVulnerabilitytoResult(attack, this.ghostvulndetected, { "vulnerability": "exists" })
                    pluginscopeForRequest.ghostvulndetected = true
                }
            }
        }
    }

    checkXssHeaderProtectionVulnerability(attack) {
        // This check was disabled, beacuase the X-XSS-Protection header has been deprecated in modern browsers. This header was originally designed to enable the XSS filter built into browsers like Internet Explorer, Chrome, and Safari. However, it has been found to introduce additional security issues and is no longer considered effective.
        //x-xss-protection: 0
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let RescontentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        let CType = /(application\/json|text\/(?:plain|xml))/i //Added this check to avoid FP, Manual is required for confirm vuln
        if (CType.test(RescontentType)) {
            return
        }

        if (pluginDataForRequest.xssProtectionVulnFound) {
            return
        }

        /**
         * Recommendations:
         * Use X-XSS-Protection: 1; mode=block: This is the most secure setting, as it prevents the page from rendering if an XSS attack is detected.
         * Implement Content Security Policy (CSP): A strong CSP can provide robust protection against XSS attacks, even if the X-XSS-Protection header is not set
         * If the response has a Content-Length: 0 header, it indicates that the body of the response is empty. In this case, the risk of impact is minimal because there is no content for the browser to xss.
          */
        let ResBody = _.get(attack, 'result.resp.body', '')
        if (/\w/.test(ResBody)) {
            // Check X-XSS-Protection header and Check Content Security Policy (CSP) header
            const xssProtection = _.get(attack, 'result.resp.httpResponse.headers["x-xss-protection"]');
            const csp = _.get(attack, 'result.resp.httpResponse.headers["content-security-policy"]');
            if (!csp || /frame-ancestors|frame-src/i.test(csp)) {
                if (!xssProtection || xssProtection !== '1; mode=block') {
                    let vuln = 'X-XSS-Protection header is missing or insecure in the response headers.'
                    this.addVulnerabilitytoResult(attack, this.xssvulnIDMeduim, vuln)
                    pluginDataForRequest.xssProtectionVulnFound = true
                }
            }
        }
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     */
    checkHSTSHeaderMissingVulnerability(attack) {
        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        let protocol = _.get(attack, 'result.req.parsedURL.protocol');
        if (protocol == 'http:') { return }
        /**
         * As the plugin is attacking only once per scan thats why need to remain confident about that attacks
         * Disabled below process to check all request
         * results hence we will only attack once scan has started running for this site and
         * more than 60 requests are sent
         
        if (pluginDataForRequest.requestCount == null) {
            pluginDataForRequest.requestCount = 0
        }
        pluginDataForRequest.requestCount += 1
        */

        if (pluginDataForRequest.HSTSVulnFound) {
            return
        }

        let HSTSHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["strict-transport-security"]');
        let respStatus = _.get(attack, 'result.resp.httpResponse.statusCode')
        // check if request count threshold is met, then only check for vulnerability
        // if (this.isApiScanMode(attack.originalRequest) || pluginDataForRequest.requestCount > this.getMetadata(attack).vulnerabilities['ID-hsts-header-missing'].startAttackAfterThisManyRequest) {
        /**
         * report vulnerability only if header value is null / invalid which means that includeSubDomains and preload is missing
         * Ex: 
         * 1. Strict-Transport-Security: max-age=31536000; includeSubDomains
         * or 2. Strict-Transport-Security: max-age=63072000; includeSubDomains; preload   
                */
        if (respStatus == 200) {
            if (!HSTSHeaderVal) {
                let result = `No HSTS header is present on the response.`
                pluginDataForRequest.HSTSVulnFound = true
                this.addVulnerabilitytoResult(attack, this.hSTSVulnID, result)
            }
            else if (HSTSHeaderVal) {
                let list = HSTSHeaderVal.split(/;/g)
                let invaliddata = []
                list.forEach(el => {
                    if (el.includes('max-age')) {
                        let currValue = el.split('=')
                        if (currValue[1] < 31536000) {
                            invaliddata.push(`Max-age too low. The max-age must be at least 31536000.\n`)
                        }
                        else if (currValue[1] >= 63072000) {
                            if (!/preload/i.test(HSTSHeaderVal)) {
                                invaliddata.push(`Error: preload directive was missing. It may allow attackers to bypass HSTS.\n`)
                            }
                        }
                    }
                })
                if (!/includeSubDomains/i.test(HSTSHeaderVal)) {
                    invaliddata.push(`Error: includeSubDomains directive was missing.`)
                }
                if (invaliddata.length > 0) {
                    pluginDataForRequest.HSTSVulnFound = true
                    let result = invaliddata.join(', ')
                    this.addVulnerabilitytoResult(attack, this.hSTSVulnID, `Strict-Transport-Security: ${HSTSHeaderVal}. ${result}`)
                }
            }
        }
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     */
    checkHttpBasicAuthentication(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {});

        let headersToIterate = ['authenticate', 'www-authenticate'];
        if (pluginDataForRequest.httpBasicAuthenticationVulnFound) {
            return
        }
        let protocol = _.get(attack, 'result.req.parsedURL.protocol');
        let redirects = _.get(attack, 'result.resp.httpResponse.redirects')
        if (redirects.length > 0) {
            let parsedUrl = new URL(redirects[redirects.length - 1].redirectedUri)
            protocol = parsedUrl.protocol
        }
        if (protocol !== 'https:') {
            for (let headerName of headersToIterate) {

                let val = headers[headerName];
                if (val) {
                    if ((val.toLowerCase()).indexOf("basic") > -1) {
                        let vuln = {
                            details: {
                                header: headerName,
                                value: val
                            }
                        }
                        this.addVulnerabilitytoResult(attack, this.httpBasicAuthenticationVulnID, vuln)
                        pluginDataForRequest.httpBasicAuthenticationVulnFound = true
                        return
                    }
                }
            }
        }

        let OriHeaders = caseless(_.get(attack, 'originalRequest.httpRequest.headers', ""));
        let auth = OriHeaders.get('authorization');
        if (!auth) {
            return
        }
        if (!/^basic/i.test(auth)) {
            return
        }
        let RegEx = /[\w=]+$/i
        let currValue = ''
        try { currValue = RegEx.exec(auth) } catch (e) { return }
        if (currValue.length > 0) {
            let credential = Buffer.from(currValue[0], 'base64').toString()
            let count = credential.split(':')
            if (count.length == 2 && count[0].length > 1 && count[1].length > 1) {
                if (!/[a-z0-9]{30,}/i.test(count[0]) && !/[a-z0-9]{30,}/i.test(count[1])) {
                    let vuln = {
                        details: {
                            header: 'Authorization',
                            value: credential
                        }
                    }
                    this.addVulnerabilitytoResult(attack, this.httpBasicAuthenticationVulnID, vuln)
                    pluginDataForRequest.httpBasicAuthenticationVulnFound = true
                    return
                }
            }
        }
    }

    checkMimeSniffingVulnerability(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.mimeVulnFoundM && pluginDataForRequest.mimeVulnFoundL) {
            return
        }
        /**
         * If the response has a Content-Length: 0 header, it indicates that the body of the response is empty. In this case, the risk of content sniffing is minimal because there is no content for the browser to sniff.
          */
        let mimeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["x-content-type-options"]');
        let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')
        // let contentlength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
        let ResBody = _.get(attack, 'result.resp.body', '')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (/200|206|400|404|500/.test(statusCode) && /\w/.test(ResBody)) {
            // Web Server Content Sniffing Enabled
            if (!pluginDataForRequest.mimeVulnFoundM && contentTypeHeaderVal == null && mimeHeaderVal == null) {
                // report vulnerability only if both header is missing - Medium
                this.addVulnerabilitytoResult(attack, this.mimeVulnIDMedium, `The Content-Type and  X-Content-Type-Options: nosniff headers are missing in the response headers.`)
                pluginDataForRequest.mimeVulnFoundM = true
            }
            else if (!pluginDataForRequest.mimeVulnFoundL && /text\/(?:css|html)|application\/javascript|image\/(?:jpeg|png)/i.test(contentTypeHeaderVal) && mimeHeaderVal == null) {
                this.addVulnerabilitytoResult(attack, this.mimeVulnIDLow, `The X-Content-Type-Options: nosniff header is missing in the response headers.`)
                pluginDataForRequest.mimeVulnFoundL = true
            }
        }
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     *
     */
    checkChromeLoggerInformationDisclosure(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {});
        let chromePHPHeader = _.get(attack, 'result.resp.httpResponse.headers.x-chromephp-data');
        let chromeLoggerHeader = _.get(attack, 'result.resp.httpResponse.headers.x-chromelogger-data');

        let headersToIterate = ['x-chromelogger-data', 'x-chromephp-data'];
        if (pluginDataForRequest.loggerHeaderFound) {
            return
        }
        if (chromeLoggerHeader || chromePHPHeader) {
            let vulns = [];
            for (let headerName of headersToIterate) {

                let val = headers[headerName];
                if (val) {
                    let vuln = {
                        details: {
                            header: headerName,
                            value: val
                        }
                    }
                    vulns.push(vuln);
                }
            }
            if (vulns && vulns.length > 0) {
                this.addVulnerabilitytoResult(attack, this.chromeLoggerInformationDisclosureVulnID, vulns)
                pluginDataForRequest.loggerHeaderFound = true
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == this.httpBasicAuthenticationVulnID) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["authenticate", "www-authenticate"]);
        }
        if (vulnID == this.ghostvulndetected) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["x-powered-by", "server"]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        }
        if (vulnID == this.JWTmisconfigurationID || vulnID == this.JWTIncorrectSessionTimeout || vulnID == this.CredentialinToken) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["authorization", "Authorization"]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
    }
}

module.exports = HttpHeadersChecker