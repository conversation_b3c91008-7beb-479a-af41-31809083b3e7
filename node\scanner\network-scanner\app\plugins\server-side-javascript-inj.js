const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Server Side Javascript Injection Plugin Strategy:
 * Here we will inject js payloads in all request for uri query params and form encodedpost, and if 
 * received expected response in return then will mark it as vulnerable
 */

class serverSideJSInjection extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-ssjs'
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return ssjsPayloads
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        //Return of attack not made by this plugin
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginStorage = this.getPluginScopedStore(attack)

        //if one attack already made then don't attack further
        if (pluginStorage.ssjs) {
            return
        }

        //let contentLength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, "result.resp.body")

        // report only when response code is 200 ok, content length is 10 and body is string in vector
        // Updating condition to only check for body content
        if (body == "haiku_SSJS") {

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginStorage.ssjs = true
            return
        }
    }

    //Parameter and it's values(attack vector) and haiku_SSJS in response body.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haiku_SSJS']);
    }
}

const ssjsPayloads = [
    'response.end("haiku_SSJS")',
    'res.end("haiku_SSJS")'
]

module.exports = serverSideJSInjection