//-----------------------------------
// default scan config
// TODO - KK : defaultConfig should also be picked up from the SS API
// ???
let defaultConfig = {
    mainUrl: 'unknown',
    maxDepth: 20,
    maxActions: 5000,
    maxScanTime: 720, // in minutes, default 12 hours
    maxInitialUrlsPerExternalSource: 100, // how many per source like sitemap/WAF analysis from S3/...
    initialWizardGuides: [], // actionlists to execute right at the start of the crawl
    unsafeForceAllowCrawl: [], // force allow certain URIs (eg. SSO page) outside normal scan URL. 
    excludeFromAttack: [], // crawl but dont attack - merged into excludeurls sent to scanner
    maxSeedUrls: Number.MAX_SAFE_INTEGER, // how many seed urls to use, default is use all we have
    maxActionsPerStatePerIteration: 10, // how many actions to run for a state before moving to the next state
    skipUpdateHostFile: false, //Skip host file update - some sites want to use DNS and not skip WAF
    stripWWW: false, // When true strip www from url when sending request other wise ignore
    webSecurity: false,  // websecurity check for some sites expecting true like 'https://admin.officeatwork.com'
    saveCrawlerLogs:false,
    addToRequest: {}, // add to every request. currently supports headers and cookies
    hostRules: false, //Add host rules in electron via append-switch command line if host host entry present for site.
    triggerValidation: false, //Trigger a validation function when the user tabs away from an input element on certain input elements
    setInputValue: false, //Set input values for react elements
    generateUrlAnalysis: false, //Generate URL analysis report using wappalyzer
    wappalyzerSourcePath: '/mnt/haiku/wappalyzer', //Path to the wappalyzer source code
    excludePostSeedUrls: false, //Exclude post seed urls from the scan, i.e. post-crawl-seed-urls.js
    excludeFromSitemap : "", //Exclude urls from the sitemap when exclude url cant be used because of it also being used to exclude from scan ex qanew.talentsavvy.com
    // periodically save state
    periodicSaveStateMins: 10,
    ignoreForm: false,       //Ignore form elements in the page to avoid junk data insert issue
    pageExecutionScriptTimeout: 10, // timeout for ifc-util.js => timedPromise to execute on page
    ignoreRowAndColumns: false, //Ignore row and columns in the page to avoid similar crawling loop, 
    useFullXPath: false, //Use full xpath for table element, instead of relative xpath - True for complex dashboard like sites
    ignoreTableCrawlOptimizatiion: false, //Ignore table crawl optimization when set to true, by default it will do the optimization
    // make it true for site where non table element structure used to display grid rows and columns  
    subpathConfig: {
        enabled: false,
        subPathLevel: 5
    },
    apiDiscovery: false, // enable API discovery
    // core properties
    utils: {
        mail: {
            host: 'smtp.office365.com',
            port: 587,
            secure: false, // secure:true for port 465, secure:false for port 587
            auth: {
                //user: '<EMAIL>',
                //pass: 'apptrana@1235'
                user: '<EMAIL>',
                pass: 'outlook@123'
                // user: '<EMAIL>',
                // pass: 'qainfra@123'
            }
        },
    },

    // scanner props
    scanner: 'ifc-scanner-default',
    crawlState: 'ifc-crawlstate-context',
    crawlTraversal: 'BFS',
    email: {
        loginFailed: {
            sendCustomerMail: false,
            to: '<EMAIL>, <EMAIL>, <EMAIL>',
            subject: 'Login may have failed for {{site}}' // Subject line
        }
    },

    browser: {
        width: 1280,
        height: 720,
        show: true,
    },
    plugins: {
        network: ['network/log-network-actions.js', 'network/send-network-action-to-scanner'],
        crawler: ['core/expand-link-to-all-subpaths.js', 'core/filter-items.js', 'core/get-fingerprint-tokens-per-item.js', 'core/fingerprinting.js', 'core/check-already-visited.js', 'heuristics/core-heuristics.js',
            'core/crawl-limits.js', 'core/crawl-metrics.js', "core/screenshot.js", 'core/skip-dup-urls.js'
        ],
        actionExecutor: ['action-executor/record-replay.js', 'action-executor/login-simple-form.js', 'action-executor/login.js']
    },
    pluginData: {
        checkForDup: {
            threshold: 1, //.95,
            actionDupCheck: 'wavg', // one of min, max,avg,wavg
            guessThreshold: 5, // correct guesses before we trust the prediction. Every incorrect guess reduces count.
            minDepthForPredictiveDup: 300, // start action dup work only from this depth.
            falloffFunctions: ['bezier'],
            fixed: {
                thresholdFalloffFixed: .01, // relax match by this % per depth, 0.01 = 1%
                thresholdFalloffRatio: 0 // relax match by ratio*curDepth/maxDepth
            },
            logistic: {
                // https://en.wikipedia.org/wiki/Logistic_function
                // L is assumed to be 1
                k: 0.5, // steepness of curve
                x0Percent: 0.7 // midpoint of curve: 0.7 => threshold is 50% when depth is 70% of the maxDepth  
            },
            bezier: {
                // use below to tweak and write function to check by printing values for depth = 0 - maxDepth
                // http://cubic-bezier.com/#.75,.1,.5,.5
                p0: 0.75,
                p1: 0.1,
                p2: 0.5,
                p3: 0.5
            },
            step: [
                [5, .01], // <=5 increase by 1% per depth
                [10, .02], // 6-10 increase by 2% per depth
                [15, .03], // 11-15 increase by 3% per depth
                [Number.MAX_SAFE_INTEGER, .05] // > 15 increase by 5% per depth
            ]

        },
        fingerprinting: {
            shingling: true
        },
        filterItems: {
            orderByStackIndex: true,
            filterByStackIndex: false
        },
        simpleLogin: {
            retry: 3, // number of failed logins before giving up
        },
        logNetworkActions: {
            alwaysSendStatusCodes: [401, 403, 406, 500],
            statusCodeCutoff: 400, // don't send requests >= this status code - see below
            requestsToSendBeforeCutoff: 10, // send this many 'cutoff' requests then stop
        },
        screenshot: {
            takeScreenshots: 'none',     // none, all, unique
            createAnimatedViz: false     // Create aninated GIF from screenshots?
        },
        skipDupUrls: {
            enabled: false,
            minUsablePrecision: 0.9, // minimum precision to use = 90%.
            matchesToActivateDupRegex: 1
        }
    }
}

module.exports = defaultConfig