const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class SQLStatementCheck extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-sql-statement-check'
        this.matchRegexps = []
        for (let regex of SourceCodeDiscMatch) {
            this.matchRegexps.push(new RE2(regex))
        }
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {

        if (originalRequest.result.resp.httpResponse.err) {
            return
        }

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {
            // check if any sql statement present in response body, then only call processAttackResponse
            let body = _.get(originalRequest, "result.resp.body")
            if (/select\s+[a-z,0-9_*\`\.\(\)]+\s+from\s+[a-zA-Z0-9_\`\.]+|delete\s+from\s+[a-zA-Z0-9_`\.]+\s+(where\s+[a-zA-Z0-9_`\.]+\s*?=)?|update\s+[a-z0-9_`\.]+\s+set\s+[a-z0-9_`\.]+\s*=\s?|insert\s+into\s+[a-z0-9_`\.]+|drop\s+table\s+[a-z0-9_`\.]/i.test(body)) {
                return true
            }
        }
        return false
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {

        let $ = _.get(originalRequest, 'result.resp.httpResponse.cheerio');
        let comments = $('*').contents().filter((i, e) => {
            return e.type == "comment";
        }).get();
        //check if any sql commands match
        let matchRes = this.checkAnyRegexMatch(comments);
        if (matchRes) {
            this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, matchRes)
        }
    }

    checkAnyRegexMatch(comments) {
        let res = '';
        for (let i = 0; i < comments.length; i++) {
            let lineOfQuery = comments[i];
            //debug("lineOfQuery.data: " + lineOfQuery.data);
            for (let pattern of this.matchRegexps) {
                if (pattern.test(lineOfQuery.data)) {
                    res += lineOfQuery.data.match(pattern) + '\n'
                }
            }
        }
        return res;
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['select','delete','update','insert','drop']);
    }
}

//SQL command regex
const SourceCodeDiscMatch = [
    /select\s+[a-z,0-9_*\`\.\(\)]+\s+from\s+[a-zA-Z0-9_\`\.]+/,
    /delete\s+from\s+[a-zA-Z0-9_`\.]+\s+(where\s+[a-zA-Z0-9_`\.]+\s*?=)?/,
    /update\s+[a-z0-9_`\.]+\s+set\s+[a-z0-9_`\.]+\s*=\s?/,
    /insert\s+into\s+[a-z0-9_`\.]+/,
    /drop\s+table\s+[a-z0-9_`\.]+/,
]

module.exports = SQLStatementCheck