let utils = require('../../ifc-utils.js')
const FingerPrint = require('../../datastructure/fingerprint.js')
const URL = require('url').URL
const HaikuUtils = require('../../../common/lib/haiku-utils')

class SimhashFingerPrint {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config
        this.mergeConfig()

        // set up event listeners
        scanner.on('pre-trigger', this.preTriggerActions.bind(this))
        scanner.on('got-interesting-items', this.getActions.bind(this))
    }

    mergeConfig() {
        if (!this.config.siteConfig || !this.config.siteConfig.pluginData || !this.config.siteConfig.pluginData.fingerprinting) {
            return
        }

        // plugin data for "check for dup" plugin
        let pluginData = this.config.pluginData.fingerprinting
        let sitePluginData = this.config.siteConfig.pluginData.fingerprinting

        if (sitePluginData.shingling) {
            pluginData.shingling = sitePluginData.shingling
        }
    }

    fingerPrintActions(actionBranch) {
        let tokens = []
        for (let actions of actionBranch) {
            tokens.push('ACTION_BRANCH_START')
            for (let action of actions) {
                tokens.push('ACTION_START')
                tokens.push(action.op)
                tokens.push(...action.params)
                tokens.push('ACTION_END')
            }
            tokens.push('ACTION_BRANCH_END')
        }

        return new FingerPrint(tokens)
    }

    // shinge @ factor = 2
    shingleTokens(tokensForFingerprinting) {
        let prevToken = 'START'
        let shingledTokens = []
        for (let tok of tokensForFingerprinting) {
            shingledTokens.push(prevToken + '**' + tok)
            prevToken = tok
        }
        shingledTokens.push(prevToken + '**' + 'END')

        return shingledTokens
    }

    preTriggerActions(ret, actions) {
        let actionFingerprintTokens = this.scanner.crawlState.getFingerprintTokens(actions)
        let curActionFingerPrint = new FingerPrint(this.shingleTokens(actionFingerprintTokens))
        ret.fingerprint = {
            actionFingerprint: curActionFingerPrint
        }
    }

    // annotates interestingitems, does not affect ret
    getActions(ret, interestingItems) {
        // previous plugin already decided to skip state or specifically asked to stop processing more plugins => nothing to do
        if (ret.skipState) {
            return
        }

        // Do not consider location since in many cases unknown pages throw up a default page
        // and considering the location throws off the dup detection.
        let nonLinkTokensForFingerprinting = []
        for (let item of interestingItems.inputItems) {
            nonLinkTokensForFingerprinting.push(item[1].fingerprintTokens)
        }
        for (let item of interestingItems.formItems) {
            nonLinkTokensForFingerprinting.push(item[1].fingerprintTokens)
        }
        for (let item of interestingItems.clickableItems) {
            nonLinkTokensForFingerprinting.push(item[1].fingerprintTokens)
        }

        // for links, just add the href, nothing else.
        let linkTokensForFingerprinting = [] 
        for (let item of interestingItems.linkItems) {
            if (item[1].href) {
                linkTokensForFingerprinting.push(utils.canonicalizeUrl(item[1].href, this.config.parsedUrl.href))
            }
        }
        for (let simpleHref of interestingItems.simpleHrefs) {
            linkTokensForFingerprinting.push(utils.canonicalizeUrl(simpleHref, this.config.parsedUrl.href))
        }

        // require('fs').appendFile('/tmp/tokens.json', JSON.stringify(tokensForFingerprinting))
        //let fingerprint = new FingerPrint(this.shingleTokens(tokensForFingerprinting.sort()))
        let tokensForFingerprinting = [...nonLinkTokensForFingerprinting, ...linkTokensForFingerprinting]
        interestingItems.serverData.fingerprint = new FingerPrint(tokensForFingerprinting)
        interestingItems.serverData.nonLinkFingerprint = new FingerPrint(nonLinkTokensForFingerprinting)

        // add the tokens as well.
        interestingItems.serverData.linkTokensForFingerprinting = linkTokensForFingerprinting
        interestingItems.serverData.nonLinkTokensForFingerprinting = nonLinkTokensForFingerprinting
        interestingItems.serverData.tokensForFingerprinting = tokensForFingerprinting

        //utils.log(`fingerprint: ${fingerprint}` )
        //this.printAllSimilaritiesVisitor(crawlState, fingerprint)
    }

    printAllSimilaritiesVisitor(crawlState, fingerprint) {
        let contextIterator = crawlState.getVisitor()

        let nextContext = contextIterator.next()
        while (!nextContext.done) {
            let ctx = nextContext.value
            let similarity = fingerprint.similarity(ctx.interestingItems.serverData.fingerprint)
            utils.log(`context depth = ${ctx.depth} similarity = ${similarity}`)
            nextContext = contextIterator.next()
        }
    }
}

module.exports = SimhashFingerPrint