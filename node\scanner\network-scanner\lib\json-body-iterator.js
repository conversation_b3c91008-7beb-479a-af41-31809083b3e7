const _ = require('lodash')
const caseless = require('caseless')

const ParameterizedDelegate = require('./parameterized-delegate')
const HaikuUtils = require('../../common/lib/haiku-utils')
const _ParameterType = 'JSONPost'

// Delegate that can iterate annotated JSON object and update properties
class JSONBodyIterator extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    static isJSONPostRequest(httpRequest) {
        let headers = caseless(_.get(httpRequest, "headers", []))
        let contentType = headers.get('content-type')

        return httpRequest.method == 'POST' &&
            /application\/json/i.test(contentType)
    }

    /**
     * Check if we can tamper this property i.e. if its a simple data type - not object, not function
     * Array we will manage by using the vector as a 1 element array
     * @param {string} prop property to check if its tamperable i.e. is a simple data type
     */
    isTamperable(prop) {
        let data = _.get(this.jsonBody, prop)
        return !HaikuUtils.isNullOrUndefined(data) && !_.isObject(data) && !_.isFunction(data)
    }


    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore, options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType, options)
        try {
            this.originalJSONBody = _.isObject(request.httpRequest.body) ? request.httpRequest.body : JSON.parse(request.httpRequest.body)
            this.jsonBody = _.cloneDeep(this.originalJSONBody)
            // if annotations not provided, pick all 'pod' top level properties
            this.propsToIterate = _.cloneDeep(options.propsToIterate)
            if (HaikuUtils.isNullOrUndefined(this.propsToIterate)) {
                this.propsToIterate = HaikuUtils.getPropetiesToIterate(this.jsonBody);
            }
        } catch (err) {
            console.log(err.toString())
            this.jsonBody = {}
        }
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    /**
     * iterate all params allowinf plugins to tamper if needed
     */
    * getIterator() {
        // iterate through all params
        for (let prop of this.propsToIterate) {
            yield {
                name: prop,
                val: _.get(this.jsonBody, prop),
                resetRequired: true
            }
        }
    }

    /**
     * modify a particular property. Automatically takes care of 'array'fying if necc.
     * @param {string} param property to modify
     * @param {any} value new value
     */
    modifyParam(param, value) {
        let convertValToArray = _.isArray(_.get(this.jsonBody, param)) && !_.isArray(value)
        _.set(this.jsonBody, param, convertValToArray ? [value] : value)
    }

    // get the modified request
    getHttpRequest(encoding) {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)
        req.body =  JSON.stringify(this.jsonBody)
        return req
    }

    // reset post body
    reset() {
        this.jsonBody = _.cloneDeep(this.originalJSONBody)
    }
}

module.exports = JSONBodyIterator