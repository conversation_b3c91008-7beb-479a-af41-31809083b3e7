glob = require('glob')
fs = require('fs')
URL = require('url').URL
_ = require('lodash')
path = require('path')
const RE2 = require('re2')

// Analyze crawl info (paused info) and for every state that has URLs (load action) that resulted in that state
// generate a regex to match those URLs. Will be used for skipping duplicates in the next crawl.
// URL -> regex is adapted from the Multiple Sequence Alignment technique as in
// https://tede.ufam.edu.br/bitstream/tede/6557/5/Tese_Kaio%20Rodrigues#page=60&zoom=100,94,686

// pull in necc. classes from Haiku
const utils = require('../app/ifc-utils.js')
const HaikuUtils = require('../common/lib/haiku-utils')
const FingerPrint = require('../app/datastructure/fingerprint.js')
const CrawlStateFactory = require('../app/datastructure/ifc-crawlstate-factory.js')
const s3Utils = require('../common/lib/s3-utils')

// globals
let logVerbose = false // lots of logs if this is enabled
let outdir = './dedup-analysis/'
let pauseS3Prefix = 'crawler/paused/'
let analysisS3Prefix = 'analysis/'

// consts
const URI_SAMPLE_SIZE = 61
const TOK_BOUNDARIES = utils.getTokBoundaries()
const MIN_SIMILARITY_THRESHOLD = 90
const STATE_SIMILARITY_THRESHOLD = 95
const ANALYSIS_USAGE_PROPERTY = 'crawler.pluginData.skipDupUrls.dupAnalysis' // the last scan dup url usage info in paused json

/**
 * Generate best matching (consensus) sequence between 2 tokenized URIs/consensus sequences 
 * using pairwise Sequence Alignment
 * @link https://tede.ufam.edu.br/bitstream/tede/6557/5/Tese_Kaio%20Rodrigues#page=60
 * 
 * @param {Object} consensusSeq Current consensus sequence (can be [] for none)
 * @param {array} m1 tokenized URI to add to current consensus sequence
 */
function getConsensusSeq(consensusSeq, m1) {
    // create the matrix m1xm2 prefilled with 0's
    let m1Length = m1.length
    let consensusSeqLength = consensusSeq.length
    if (consensusSeqLength == 0) {
        for (let tok of m1) {
            consensusSeq.push([tok])
        }
        return
    }

    // reverse tokens
    consensusSeq = consensusSeq.reverse()
    m1 = m1.reverse()

    // generate the best matching sequence by pairwise SA
    let S = new Array(m1Length + 1).fill(undefined).map(() => {
        return new Array(consensusSeqLength + 1).fill(0)
    })
    let ptrs = new Array(m1Length + 1).fill(undefined).map(() => {
        return new Array(consensusSeqLength + 1).fill([0, 0])
    })
    for (let i = 1; i < m1Length + 1; i++) {
        ptrs[i][0] = [-1, 0]
    }
    for (let j = 1; j < consensusSeqLength + 1; j++) {
        ptrs[0][j] = [0, -1]
    }

    // scoring function
    function sf(a, consSeq) {
        return +(consSeq.includes(a))
    }
    // starting from top-left, construct the dyn programming matrix S
    for (let i = 1; i < m1Length + 1; i++) {
        for (let j = 1; j < consensusSeqLength + 1; j++) {
            let score = sf(m1[i - 1], consensusSeq[j - 1])
            S[i][j] = Math.max(
                S[i - 1][j - 1] + score,
                S[i - 1][j],
                S[i][j - 1])


            if (S[i - 1][j - 1] + score >= S[i - 1][j] &&
                S[i - 1][j - 1] + score >= S[i][j - 1]) {
                S[i][j] = S[i - 1][j - 1] + score
                ptrs[i][j] = [-1, -1]
            } else if (S[i - 1][j] >= S[i][j - 1]) {
                S[i][j] = S[i - 1][j]
                ptrs[i][j] = [-1, 0]
            } else {
                S[i][j] = S[i][j - 1]
                ptrs[i][j] = [0, -1]
            }
        }
    }

    // generate the best matching sequence
    let i = m1Length
    let j = consensusSeqLength
    let m1Seq = []
    let m2Seq = []

    while (true) {
        let iinc = ptrs[i][j][0]
        let jinc = ptrs[i][j][1]
        if (iinc == -1 && jinc == 0) { // up -> gap in u2
            m1Seq.push(m1[i - 1])
            m2Seq.push(['~gap~'])
        } else if (jinc == -1 && iinc == 0) { // left = gap in u1
            m1Seq.push('~gap~')
            m2Seq.push(consensusSeq[j - 1])
        } else if (iinc == -1 && jinc == -1) { // diagonal -> no gap
            m1Seq.push(m1[i - 1])
            m2Seq.push(consensusSeq[j - 1])
        }

        if (iinc == 0 && jinc == 0) {
            // run through the appropriate index
            while (i > 0) {
                m1Seq.push(m1[i--])
                m2Seq.push(['~gap~'])
            }
            while (j > 0) {
                m1Seq.push('~gap~')
                m2Seq.push(consensusSeq[j--])
            }

            break
        }

        i += iinc
        j += jinc
    }

    // the 2 seuqnces are aligned, combine into new consensus sequence
    newConsSeq = _.zip(m1Seq, m2Seq).map(x => x.filter(y => y != undefined))
    newConsSeq = newConsSeq.map(x => _.flattenDeep(x))

    // now add to existing consensus seq
    for (row = 0; row < newConsSeq.length; row++) {
        consensusSeq[row] = _.uniq(newConsSeq[row])
    }
}

/**
 * deserialize the crawler paused state info.
 * @param {string} pausedData paused craweler info deserialize (not filename)
 * @param {Number} minVersion minimum version that this code expecets
 */
function deserializePausedState(pausedData, minVersion = 2) {
    if (pausedData.version < minVersion) {
        console.log(`skipping, version ${pausedData.version} is < min version allowed ${minVersion} ...`)
        return null
    }

    // set up some dummy 'config' style for deserializing
    let cfg = require('../app/default-scan-config.js')
    cfg.logPath = '/tmp'
    let crawlState = CrawlStateFactory.createCrawlstateDS(cfg)

    let cs = pausedData.crawlContext
    crawlState.deserializeState(cs)
    for (let a of cs.allContextStates) {
        a.actions = a.actions.map(utils.serializedDataToObject.bind(utils))
    }
    for (let a of cs.allContextStates) {
        if (a.replayActions) {
            a.replayActions = utils.serializedDataToObject(a.replayActions)
        }
    }
    return cs
}

/**
 * See if this crawl state (context) has duplicate actions
 * @param {context} ctx crawl context to check 
 */
function hasDupActions(ctx) {
    for (let threshold of Object.keys(ctx.actionsResultingInThisState)) {
        if (ctx.actionsResultingInThisState[threshold].length > 1) {
            return threshold
        }
    }
    return false
}

/**
 * gets the URLs that are part of terminal load actions in a set of actions resulting in this state
 * @param {context} ctx crawl context to check 
 * @param {Number} minThreshold minimum similarity threshold of dupliate actions 
 */
function getPossibleDupUrls(ctx, minThreshold = MIN_SIMILARITY_THRESHOLD) {
    let possibleDupUrls = {
        canUrls: [],
        rawUrls: []
    }

    for (let threshold of Object.keys(ctx.actionsResultingInThisState)) {
        if (threshold < minThreshold) {
            console.log(`\tskipping dups at ${threshold} since it's < ${minThreshold}`)
            continue
        }
        for (let actions of ctx.actionsResultingInThisState[threshold]) {
            let lastAction = actions[actions.length - 1]
            if (lastAction.actionType == 'load') {
                possibleDupUrls.canUrls.push(utils.canonicalizeUrl(lastAction.url))
                possibleDupUrls.rawUrls.push(lastAction.url)
            }
        }
    }

    possibleDupUrls.rawUrls = _.uniq(possibleDupUrls.rawUrls)
    possibleDupUrls.canUrls = _.uniq(possibleDupUrls.canUrls)
    return possibleDupUrls
}

/**
 * make an id from an array of tokens, this can be converted back using the parallel function tokensFromId
 * @param {array} tokens tokens
 */
function idFromTokens(tokens) {
    return JSON.stringify(tokens)
}

/**
 * make array of tokens from an id that wasgenerated by idFromTokens
 * @param {string} id id
 */
function tokensFromId(id) {
    return JSON.parse(id)
}

class PriorityQ {
    /**
     * Create a priority queue from list of duplicate URIs. This is a quadratic function since we compare 
     * every pair of URIs to get the priority order of most similar pairs first. The priority queue scoring
     * is Jaccard simimilarity.
     * @link Contents
     * @param {array} uriList List of duplicate URIs 
     */
    constructor(uriList) {
        this.priorityQ = []
        this.sequences = new Set()

        for (let i = 0; i < uriList.length; i++) {
            logVerbose && console.log(`\t\tprocessing uri ${i} of ${uriList.length}, priorityQ: ${this.priorityQ.length}`)
            let u1 = uriList[i]
            let u1Toks = utils.tokenizeUrl(u1)
            this.sequences.add(idFromTokens(u1Toks))
            for (let j = i + 1; j < uriList.length; j++) {
                let u2 = uriList[j]
                let u2Toks = utils.tokenizeUrl(u2)
                this.sequences.add(idFromTokens(u2Toks))
                let consSeq = []
                getConsensusSeq(consSeq, _.cloneDeep(u1Toks))
                getConsensusSeq(consSeq, _.cloneDeep(u2Toks))
                let score = _.intersection(u1Toks, u2Toks).length / _.union(u1Toks, u2Toks).length
                this.add({
                    u1Toks,
                    u2Toks,
                    consSeq,
                    score
                })
            }
        }
    }

    get length() {
        // count the elements with non -1 scores
        return this.priorityQ.reduce((acc, cur) => {
            return (cur.score >= 0 ? acc + 1 : acc)
        }, 0)
    }

    getNext() {
        let ret = this.priorityQ[0]
        for (let tuple of this.priorityQ) {
            if (tuple.score > ret.score) {
                ret = tuple
            }
        }

        ret.score = -ret.score
        return ret
    }

    add(tuple) {
        this.priorityQ.push(tuple)
    }
}

/**
 * Create best matching (consensus) sequence between a list of URIs using Multiple Sequence Alignment 
 * technique as described in:
 * @link https://tede.ufam.edu.br/bitstream/tede/6557/5/Tese_Kaio%20Rodrigues#page=63
 * @param {array} uriList List of duplicate URIs 
 */
function createConsensusSeq(uriList) {
    let priorityQ = new PriorityQ(uriList)

    let aligned = []
    while (priorityQ.length > 0) {
        let tuple = priorityQ.getNext()
        let id1 = idFromTokens(tuple.u1Toks)
        let id2 = idFromTokens(tuple.u2Toks)
        if (!aligned.includes(id1) && !aligned.includes(id2)) {
            logVerbose && console.log(`\t\tpriority queue length: ${priorityQ.length}`)
            aligned.push(id1, id2)
            priorityQ.sequences.delete(id1)
            priorityQ.sequences.delete(id2)
            for (let seq of priorityQ.sequences) {
                let seqToks = tokensFromId(seq)
                let clonedConsSeq = _.cloneDeep(tuple.consSeq)
                getConsensusSeq(clonedConsSeq, _.cloneDeep(seqToks))
                let flattenedConsSeq = _.flatten(tuple.consSeq)
                let flattenedSeqToks = _.flatten(seqToks)
                let score = _.intersection(flattenedConsSeq, flattenedSeqToks).length / _.union(flattenedConsSeq, flattenedSeqToks).length
                priorityQ.add({
                    u1Toks: tuple.consSeq,
                    u2Toks: seqToks,
                    consSeq: clonedConsSeq,
                    score
                })
            }
            priorityQ.sequences.add(idFromTokens(tuple.consSeq))
        }
    }
    if (priorityQ.sequences.size != 1) {
        throw new Error("Multiple sequences found at end, should be one consensus sequence")
    }
    return tokensFromId(priorityQ.sequences.values().next().value)
}

/**
 * Classify the consensus sequence tagging each token set as one of variant, invariant, irrelevant
 * @link https://tede.ufam.edu.br/bitstream/tede/6557/5/Tese_Kaio%20Rodrigues#page=67
 * @param {object} consSeq consenseus sequence generated by aligning set of duplicate URIs using MSA
 */
function classifyConsSeq(consSeq) {
    // annotate with irrelevant, invariant, variant
    let classifiedSeq = []
    for (seq of consSeq) {
        let classification = 'unknown'
        if (seq.includes('~gap~')) {
            classification = 'irrelevant'
        } else if (seq.length == 1) {
            classification = 'invariant'
        } else {
            classification = 'variant'
        }

        classifiedSeq.push({
            seq,
            classification
        })
    }

    return classifiedSeq
}


/**
 * Generate a regex from a consensus sequence
 * @link https://tede.ufam.edu.br/bitstream/tede/6557/5/Tese_Kaio%20Rodrigues#page=68
 * 
 * @param {object} consSeq consenseus sequence generated by aligning set of duplicate URIs using MSA
 * @param {number} maxOptionsThenGeneralize more this many invariant tokens => generalize it to (effectively) '.+'
 *                                          i.e (aa|bb|cc|dd) -> (.+) 
 */
function genRegExFromConsSeq(consSeq, maxOptionsThenGeneralize = 6) {
    let regex = ''
    let classifiedSeq = classifyConsSeq(consSeq)
    for (cSeq of classifiedSeq) {
        let theSeq = cSeq.seq.filter(x => x != '~gap~').map(x => _.escapeRegExp(x)).sort((a, b) => {
            return b.length - a.length
        })
        let seqRegex = `${theSeq.join('|')}`
        if (theSeq.length >= maxOptionsThenGeneralize) {
            //#region  some sanity for any future changes
            // code below is setting up regex to match up to the next token boundary using character class
            // like [^/?&-]+. If any token boundary char is part of the sequence, add it as a specific match
            // otherwise we will have a FN.
            // To keep it simpler than looping through seq array multiple times, directly matching on
            // seqRegEx string. This strategy will only fail if '|' gets added as a token boundary (TOK_BOUNDARIES)
            if (TOK_BOUNDARIES.includes('|')) {
                throw new Error(`token boundary chars incude '|' --> ${TOK_BOUNDARIES}`)
            }
            //#endregion sanity for any future changes

            let generalizedRegexStr = [`[^${TOK_BOUNDARIES}]+`]
            let boundaryCharsInSeq = Array.from(TOK_BOUNDARIES).filter(c => seqRegex.includes(c)).map(x => _.escapeRegExp(x))

            seqRegex = generalizedRegexStr.concat(boundaryCharsInSeq).join('|')
        }
        seqRegex = seqRegex.replace(/~any~/g, `[^${TOK_BOUNDARIES}]+`)
        if (cSeq.classification == 'irrelevant') {
            seqRegex = '(' + seqRegex + ')?'
        } else /*if (theSeq.length > 1)*/ {
            seqRegex = '(' + seqRegex + ')'
        }
        regex += seqRegex
    }

    return '^' + regex + '$'
}

/**
 * test code to look at paused json files and list out similarity thresholds
 */
function checkForNon100PctThreshold() {
    for (let i = 2; i < process.argv.length; i++) {
        let allfiles = glob.sync(process.argv[i])
        for (file of allfiles) {
            console.log(`processing ${file} ...`)
            let cs = deserializePausedState(JSON.parse(fs.readFileSync(file).toString()), 2)
            if (!cs) {
                console.log(`\t...skipping file ${file} !!`)
                continue
            }
            for (let ctx of cs.allContextStates) {
                if (Object.keys(ctx.actionsResultingInThisState).length > 1) {
                    console.log(file, '->', Object.keys(ctx.actionsResultingInThisState))
                    break
                }
            }
        }
    }
}

/**
 * Analyze crawl session to get regexes for duplicate URLs on a state by state basis
 * @param {CrawlState} cs deserialized crawl state
 * @param {array} prevAnalysisUsage Data about previous scan analysis i.e. runtime usage of analysis in previous scan
 * @param {Number} minDups only analyze states with more than this many dup urls
 */
function analyzeDupUrls(cs, prevAnalysisUsage = [], minDups = 1) {
    // Look at each state with dups > min amount at threshold specified
    // can change later to do it by threshold.
    let i = 1
    let analyzedOutput = []
    for (let ctx of cs.allContextStates) {
        console.log(`\tprocessing state ${i++} of ${cs.allContextStates.length}`)
        let possibleDupUrls = getPossibleDupUrls(ctx)
        let dupUrls = possibleDupUrls.canUrls
        if (dupUrls.length < minDups) {
            continue
        }

        // take random sample of URLs if needed
        let dupUrlsSample = getRandomSample(dupUrls)

        // the meat - get consesus sequence and generate regex
        let consSeq = createConsensusSeq(dupUrlsSample)

        // get all unique params as well
        let allParamNames = []
        for (let u of dupUrls) {
            let params = HaikuUtils.splitIntoParamsAndValues({
                uri: u
            }).params
            allParamNames.push(...params)
        }

        let stateDupAnalysis = {
            stateId: ctx.contextStateSeq,
            consSeq,
            classifiedSeq: classifyConsSeq(consSeq),
            regex: genRegExFromConsSeq(consSeq),
            location: ctx.interestingItems.location,
            dupUrls,
            allParamNames: _.uniq(allParamNames),
            rawUrls: possibleDupUrls.rawUrls,
            validationFP: {},
            validationFN: {},
            unfilteredFP: {},
            tokens: ctx.interestingItems.serverData.tokensForFingerprinting,
            actionsResultingInThisState: ctx.actionsResultingInThisState
        }
        analyzedOutput.push(stateDupAnalysis)
    }

    if (analyzedOutput.length < 1) {
        console.log(`\t No dup urls found...`)
        return {
            analyzedOutput,
            analysisForCrawler: []
        }
    }

    // validate this against URLs in other states as well
    console.log(`\t...validating analysis (FP, FN checks) : ${analyzedOutput.length} states`)
    validateAnalysis(cs, analyzedOutput)

    // add all the unique, activated regexes from prev scan merging as needed
    prevAnalysisUsage = prevAnalysisUsage.filter(prev => prev.activated && !analyzedOutput.find(cur => cur.regex == prev.regex))
    //prevAnalysisUsage = prevAnalysisUsage.filter(prev => prev.activated)
    for (let prev of prevAnalysisUsage) {
        // stateid may not be reliable across scans. Check if we have same state fingerprint in 
        // prev and cur and if so, pick teh one with the higher precision
        let stateWithSameFingerprint = analyzedOutput.find(x => x.fingerprint.fingerPrintData == prev.fingerprint.fingerPrintData)
        if (stateWithSameFingerprint) {
            if (stateWithSameFingerprint.precision < prev.precision) {
                // use prev 
                stateWithSameFingerprint.shouldDelete = true
            } else {
                // use cur 
                prev.shouldDelete = true
            }
        }
    }
    // do the deletes 
    prevAnalysisUsage = prevAnalysisUsage.filter(x => !x.shouldDelete)
    analyzedOutput = analyzedOutput.filter(x => !x.shouldDelete)

    // add all remaining, relevant prev analysis 
    analyzedOutput.push(...prevAnalysisUsage)

    // get the fields we want from the extended analysis.
    const actionableProps = [
        'stateId',
        'regex',
        'location',
        'allParamNames',
        'fingerprint',
        'tokens',
        'TP',
        'FN',
        'FP',
        'fβ',
        'precision'
    ]
    let analysisForCrawler = analyzedOutput.map(a => _.pick(a, actionableProps))

    return {
        analyzedOutput,
        analysisForCrawler
    }
}

/**
 * If the list is greater than the sample size, get a random sample of uris
 * @param {array} dupUrls list of duplicate urls
 */
function getRandomSample(dupUrls) {
    if (dupUrls.length > URI_SAMPLE_SIZE) {
        console.log(`\t\tRandomly sampling ${URI_SAMPLE_SIZE} from total ${dupUrls.length} dup urls`)
        dupUrls = dupUrls.sort(() => 0.5 - Math.random()).slice(0, URI_SAMPLE_SIZE)
    }
    return dupUrls
}

/**
 * Get the duplicate URls across all states
 * @param {context} ctx crawl context to check 
 * @param {Number} minDups Minimum number of dups before we consider URLs in this state
 */
function getDupUrls(cs, minDups = 5) {
    // first get all the dup contexts that are result of load action
    let dupContexts = cs.allContextStates.filter(x => hasDupActions(x))

    // Get URLs that result in the same state
    let analyzedUrls = []
    for (ctx of dupContexts) {
        let possibleDupUrl = {
            tokens: ctx.interestingItems.serverData.tokensForFingerprinting,
            urls: getPossibleDupUrls(ctx).canUrls
        }

        // only add if the urls are > min dups
        if (possibleDupUrl.urls.length >= minDups) {
            analyzedUrls.push(possibleDupUrl)
        }
    }

    return analyzedUrls
}

/**
 * Validate dup analysis by running FP, FN checks. FP checks  for a state are done on dup urls of other states.
 * @param {CrawlState} cs deserialized crawl state
 * @param {Object} analyzedOutput Analyzed crawl session containing regexes for states with dup urls
 * @param {Number} β 1 -> equal weightage to FP, FN, < 1 -> bias towards FN, >1 -> bias towards FPs.
 *                      Default β is .5 : We don't mind FN and want to avoid FP
 */
function validateAnalysis(cs, analyzedOutput, β = 0.5) {
    let i = 1
    let allUrls = _.uniq(_.flatten(getDupUrls(cs, 1).map(x => x.urls)))

    for (let stateDupAnalysis of analyzedOutput) {
        console.log(`\t\tprocesing state ${stateDupAnalysis.stateId} ${i++} of ${analyzedOutput.length}`)

        // do fp check only on urls that are not in this state
        let fingerprint1 = new FingerPrint(stateDupAnalysis.tokens)
        stateDupAnalysis.fingerprint = fingerprint1
        let okToBeDupsUrl = []
        // for loop will add all urls that are present in state being checkd AND
        // all urls that have a high similarity to the state being checked
        for (let analysis of analyzedOutput) {
            let tmpFingerprint = new FingerPrint(analysis.tokens)
            let similarity = Math.round(fingerprint1.similarity(tmpFingerprint) * 100)

            if (similarity >= STATE_SIMILARITY_THRESHOLD) {
                okToBeDupsUrl.push(..._.cloneDeep(analysis.dupUrls))
            }
        }
        okToBeDupsUrl = _.uniq(okToBeDupsUrl)

        // get list of fp, fn, tp URLs
        let fpCheckUrls = _.uniq(allUrls.filter(u => !okToBeDupsUrl.includes(u)))
        let fpUrlsFound = fpCheckUrls.filter(u => utils.isDupUrl(stateDupAnalysis.regex, u))
        let fnUrlsFound = []
        let tpUrlsFound = []
        for (let u of stateDupAnalysis.dupUrls) {
            if (utils.isDupUrl(stateDupAnalysis.regex, u)) {
                tpUrlsFound.push(u)
            } else {
                fnUrlsFound.push(u)
            }
        }
        stateDupAnalysis.TP = tpUrlsFound.length
        stateDupAnalysis.FN = fnUrlsFound.length
        stateDupAnalysis.FP = fpUrlsFound.length
        stateDupAnalysis.precision = HaikuUtils.precision(stateDupAnalysis.TP, stateDupAnalysis.FP)
        stateDupAnalysis.fβ = HaikuUtils.fBeta(stateDupAnalysis.TP, stateDupAnalysis.FN, stateDupAnalysis.FP, β)
        stateDupAnalysis.fpUrlsFound = fpUrlsFound

        // for all the FPs, FNs etc. get actual details
        stateDupAnalysis.FN > 0 && (_.set(stateDupAnalysis.validationFN, `[${stateDupAnalysis.stateId}]`, fnUrlsFound))
        if (stateDupAnalysis.FP > 0) {
            for (let analysis of analyzedOutput) {
                let state = analysis.stateId
                if (state == stateDupAnalysis.stateId) {
                    continue
                }

                // see if this state has any FPs
                let fpsInThisState = analysis.dupUrls.filter(u => fpUrlsFound.includes(u))
                if (fpsInThisState.length) {
                    _.set(stateDupAnalysis, `unfilteredFP[${state}].urls`, fpsInThisState)
                }

                // get rid of the obvious FPs i.e. urls that are found in many states
                fpsInThisState = fpsInThisState.filter(u => !stateDupAnalysis.dupUrls.includes(u))

                if (fpsInThisState.length) {
                    console.log(`Adding ${fpsInThisState.length} FP urls`)
                    _.set(stateDupAnalysis.validationFP, `[${state}].urls`, fpsInThisState)
                    let fingerprint2 = new FingerPrint(analysis.tokens)
                    stateDupAnalysis.validationFP[state].similarity = Math.round(fingerprint1.similarity(fingerprint2) * 100)
                }
            }
        }
    }
}


/* 
         ---- start of command line code -----
*/

/**
 * Analyze all paused JSON files in filespec given by command line argument eg. ./paused-json/*.json and  
 * output analyzed files to './dedup-analysis/'. input files are like <scanId>.json and output files are
 * mult-seq-analyzed-<scanid>.json
 * @param {string} filespec glob of paused-json files to process
 */

function processFiles(filespec) {
    try {
        fs.mkdirSync(outdir)
    } catch (err) {}

    let allfiles = glob.sync(filespec)
    for (file of allfiles) {
        console.log(`processing ${file} ...`)
        let pausedData = JSON.parse(fs.readFileSync(file).toString())
        let cs = deserializePausedState(pausedData, 2)
        if (!cs) {
            console.log(`\t...deleting file ${file} !!`)
            fs.unlinkSync(file)
            continue
        }

        let prevAnalysisUsage = _.get(pausedData, ANALYSIS_USAGE_PROPERTY, [])
        delete pausedData

        let {
            analyzedOutput,
            analysisForCrawler
        } = analyzeDupUrls(cs, prevAnalysisUsage, 5)
        delete cs // save mem for large paused json files

        let filename = outdir + 'crawler-dup-analysis-' + path.basename(file)
        fs.writeFileSync(filename, JSON.stringify(analysisForCrawler))
        console.log(`\t wrote dedup analysis output to: ${filename}`)

        filename = outdir + 'mult-seq-analyzed-' + path.basename(file)
        fs.writeFileSync(filename, JSON.stringify(analyzedOutput))
        console.log(`\t wrote full dedup analysis output to ${filename}`)
    }
}

/**
 * Analyze paused JSON (<scanid>.json) from S3 and upload output back to S3 (siteid/serviceid)/dup-analysis.json
 * @param {number} scanid scanid to process
 */
async function processScanId(scanId) {
    console.log(`getting paused info from s3 for scanid ${scanId} ...`)
    let resp = await s3Utils.getFile(pauseS3Prefix, scanId + '.json')
    if (!(resp && resp.Body)) {
        console.log(`**Error** invalid response ${JSON.stringify(resp)}`)
        return
    }

    let pausedData = JSON.parse(resp.Body)
    let cs = deserializePausedState(pausedData, 4)
    if (!cs) {
        console.log(`**Error** invalid response body`)
        return
    }
    let serviceId = pausedData.serviceId
    let prevAnalysisUsage = _.get(pausedData, ANALYSIS_USAGE_PROPERTY, [])
    delete pausedData

    let {
        analyzedOutput,
        analysisForCrawler
    } = analyzeDupUrls(cs, prevAnalysisUsage, 5)
    delete cs // save mem for large paused json files

    // upload result
    let filename = `crawler-dup-analysis-${serviceId}.json`
    s3Utils.upload(analysisS3Prefix, filename, JSON.stringify(analysisForCrawler));
    console.log(`\t wrote dedup analysis output to S3: ${analysisS3Prefix}/${filename}`)

    // also write the full analysis
    filename = `full-crawler-dup-analysis-${serviceId}.json`
    s3Utils.upload(analysisS3Prefix, filename, JSON.stringify(analyzedOutput));
    console.log(`\t wrote full dedup analysis output to S3: ${analysisS3Prefix}/${filename}`)
}

/**
 * Run through analyzed files and do a fp/fn analysis. Default β is .5 the We don't mind FN and want to avoid FP
 * @param {Number} β 1 -> equal weightage to FP, FN, < 1 -> bias towards FN, >1 -> bias towards FPs.
 */
function checkForFPAndFN(β = 0.5) {
    let fpfnAnalysis = {}
    let allfiles = glob.sync(outdir + 'mul*.json')
    for (let file of allfiles) {
        console.log(`processing ${file} ...`)
        let analysis = JSON.parse(fs.readFileSync(file).toString())
        let statesWithFPOrFN = analysis.filter(x => (x.validationFP ? Object.keys(x.validationFP).length : 0) +
            (x.validationFN ? Object.keys(x.validationFN).length : 0))

        if (statesWithFPOrFN.length) {
            fpfnAnalysis[file] = statesWithFPOrFN.map(y => {
                return {
                    stateId: y.stateId,
                    β,
                    fβ: y.fβ,
                    TP: y.TP,
                    FN: y.FN,
                    FP: y.FP,
                    regex: y.regex,
                    dupUrls: y.dupUrls,
                    validationFP: y.validationFP,
                    validationFN: y.validationFN,
                    tokens: y.tokens
                }
            })
        }
    }
    fs.writeFileSync(outdir + 'fpfnAnalysis.json', JSON.stringify(fpfnAnalysis))
    return fpfnAnalysis
}

/* 
         ---- -*- Test Code, called by REST API -*- -----
*/
class TestRegexGen {
    static regexFromUrls(dupUrls, fpCheckUrls) {
        // sanity
        dupUrls = dupUrls.filter(u => u.length > 0).map(u => utils.canonicalizeUrl(u))
        fpCheckUrls = fpCheckUrls.filter(u => u.length > 0)
            .map(u => utils.canonicalizeUrl(u))
            .filter(u => !dupUrls.includes(u))

        // get the regex
        let dupUrlsSample = getRandomSample(dupUrls)
        let consSeq = createConsensusSeq(dupUrlsSample)
        let regex = genRegExFromConsSeq(consSeq)

        // do the TP, FN, FP checks
        let fpUrlsFound = fpCheckUrls.filter(u => utils.isDupUrl(regex, u))
        let fnUrlsFound = dupUrls.filter(u => !utils.isDupUrl(regex, u))
        let tpUrlsFound = dupUrls.filter(u => utils.isDupUrl(regex, u))
        let fScore = HaikuUtils.fBeta(tpUrlsFound.length, fnUrlsFound.length, fpUrlsFound.length)

        // generate the result
        let res = {
            regex: genRegExFromConsSeq(consSeq),
            canUrls: dupUrls,
            fScore,
            fpUrlsFound,
            fnUrlsFound,
            tpUrlsFound,
            consSeq,
            classifiedSeq: classifyConsSeq(consSeq),
        }

        return res
    }

    static doesUrlMatchRegex(regexStr, url) {
        return {
            matches: utils.isDupUrl(regexStr, url),
            canUrl: utils.canonicalizeUrl(url)
        }
    }
}

/* 
         ---- -*- Main Code for commandline -*- -----
*/
let argv = require('minimist')(process.argv.slice(2), {
    boolean: true,
    alias: {
        v: "verbose"
    }
})

logVerbose = argv.verbose
if (argv.files && process.argv.length > 2) {
    //checkForNon100PctThreshold()
    processFiles(argv.files)
    let fpfnAnalysis = checkForFPAndFN()
}

if (argv.scanid) {
    processScanId(argv.scanid)
}

module.exports = TestRegexGen