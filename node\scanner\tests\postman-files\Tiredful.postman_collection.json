{"info": {"_postman_id": "c66e60b9-7d55-4c96-bf21-2e8afdb1b6e4", "name": "Tiredful", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"accesstoken\", function() {", "    var jsonData = JSON.parse(responseBody);", "    console.log(jsonData);", "    //pm.collectionVariables.set(\"token\", );", "    pm.globals.set(\"token\", jsonData.access_token);", "});", "", "return;", "", "/* HAIKU_START_1 */", "dummy = {", "    haikuEvents: {", "        onResponse: function(req, resp, haiku) {", "            var jsonData = JSON.parse(resp.body);", "            haikuUtil.setVar(req, \"token\", jsonData.access_token);", "        }", "    }", "}", "/* HAIKU_END_1 */", "", "/* HAIKU_START_2 */", "new Object({", "    haikuEvents: {", "        onResponse: function(resp, haiku) {", "            var jsonData = JSON.parse(resp.body);", "            haikuUtil.setVar(\"token\", jsonData.access_token);", "        }", "    }", "})", "/* HAIKU_END_2 */", "", "/* HAIKU_START */", "{", "    haikuEvents: {", "        onOriginalResponse: (resp, haiku)=> {", "            var jsonData = JSON.parse(resp.body);", "            haiku.setVar(\"token\", jsonData.access_token);", "        },", "        onResponse: (resp, haiku)=> {", "            console.log(JSON.stringify(res.body));", "        }", "    }", "}", "", "/* HAIKU_END */", "", "", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Host", "value": "127.0.0.1", "type": "text", "description": "json"}, {"key": "User-Agent", "value": " Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:69.0) Gecko/20100101 Firefox/69.0", "type": "text"}, {"key": "Accept", "value": " */*", "type": "text"}, {"key": "Accept-Language", "value": " en-US,en;q=0.5", "type": "text"}, {"key": "Accept-Encoding", "value": " gzip, deflate", "type": "text"}, {"key": "Content-Type", "value": " application/x-www-form-urlencoded", "type": "text"}, {"key": "X-Requested-With", "value": " XMLHttpRequest", "type": "text"}, {"key": "Content-Length", "value": " 251", "type": "text", "disabled": true}, {"key": "DNT", "value": " 1", "type": "text"}, {"key": "Connection", "value": " close", "type": "text"}, {"key": "<PERSON><PERSON><PERSON>", "value": " http://**********:8000/handle-user-token/", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "batman", "type": "text"}, {"key": "password", "value": "<PERSON>@123", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}, {"key": "client_id", "value": "5cI2vLy5RYR5cphK1KQbEG8xpTpjFbq3agcfQMkH", "type": "text"}, {"key": "client_secret", "value": "aFUbytyk91ihiyeOb0mBhsArZs6HYZxj5c8AlN8GmBLEjAa7lLPlL4VDDUvCQDfaFjQj1LWSHe3dmfUAVnlA7Znx8Gau5S2UW3pAQMwQBeK0c2NB89PbSYCZp1JnJ6u7", "type": "text"}]}, "url": {"raw": "http://{{URL}}/o/token/", "protocol": "http", "host": ["{{URL}}"], "path": ["o", "token", ""]}}, "response": [{"name": "haiku-annotate Authentication", "originalRequest": {"method": "POST", "header": [{"key": "Host", "value": "127.0.0.1", "type": "text", "description": "json"}, {"key": "User-Agent", "value": " Mozilla/5.0 (Macintosh; Intel Mac OS X 10.13; rv:69.0) Gecko/20100101 Firefox/69.0", "type": "text"}, {"key": "Accept", "value": " */*", "type": "text"}, {"key": "Accept-Language", "value": " en-US,en;q=0.5", "type": "text"}, {"key": "Accept-Encoding", "value": " gzip, deflate", "type": "text"}, {"key": "Content-Type", "value": " application/x-www-form-urlencoded", "type": "text"}, {"key": "X-Requested-With", "value": " XMLHttpRequest", "type": "text"}, {"key": "Content-Length", "value": " 251", "type": "text", "disabled": true}, {"key": "DNT", "value": " 1", "type": "text"}, {"key": "Connection", "value": " close", "type": "text"}, {"key": "<PERSON><PERSON><PERSON>", "value": " http://**********:8000/handle-user-token/", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "username", "value": "batman", "type": "text"}, {"key": "password", "value": "<PERSON>@123", "type": "text"}, {"key": "grant_type", "value": "password", "type": "text"}, {"key": "client_id", "value": "5cI2vLy5RYR5cphK1KQbEG8xpTpjFbq3agcfQMkH", "type": "text"}, {"key": "client_secret", "value": "aFUbytyk91ihiyeOb0mBhsArZs6HYZxj5c8AlN8GmBLEjAa7lLPlL4VDDUvCQDfaFjQj1LWSHe3dmfUAVnlA7Znx8Gau5S2UW3pAQMwQBeK0c2NB89PbSYCZp1JnJ6u7", "type": "text"}]}, "url": {"raw": "http://{{URL}}/o/token/", "protocol": "http", "host": ["{{URL}}"], "path": ["o", "token", ""]}}, "_postman_previewlanguage": "json", "header": null, "cookie": [], "body": "{\n    \"onResponse\": \"{var jsonData = JSON.parse(resp.body);haiku.setVar('token', jsonData.access_token);}\"\n}"}]}, {"name": "Advertisements, Create", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"headline\": \"Test\",\n    \"info\": \"Test\",\n    \"price\": 15.50\n}"}, "url": {"raw": "http://{{URL}}/api/v1/advertisements/", "protocol": "http", "host": ["{{URL}}"], "path": ["api", "v1", "advertisements", ""]}}, "response": [{"name": "haiku-annotate Advertisements, Create", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"headline\": \"Test\",\n    \"info\": \"Test\",\n    \"price\": 15.50\n}"}, "url": {"raw": "http://{{URL}}/api/v1/advertisements/", "protocol": "http", "host": ["{{URL}}"], "path": ["api", "v1", "advertisements", ""]}}, "_postman_previewlanguage": null, "header": null, "cookie": [], "body": null}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "URL", "value": "127.0.0.1:8000"}, {"key": "token", "value": ""}]}