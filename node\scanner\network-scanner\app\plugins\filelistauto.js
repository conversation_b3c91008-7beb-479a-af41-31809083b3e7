// const VectorResponseAttack = require('./vector-response-attack')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
// const http = require('http')
// const https = require('https');
fs = require('fs')
const URL = require('url').URL
const { exec } = require('child_process');

class FileListAuto extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-sensitive-file-directory'

    }

    wantProcessAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let uriRaw = new URL(attack.href)
        if (pluginStorageScanScope.FileList && pluginStorageScanScope.FileList.includes(uriRaw.pathname)) {
            return false
        }
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (!/\b\/\w+\.\w+(?:\/|$|\?)/.test(attack.href) && statusCode == 200) {
            // 'http://bwappscantest.testapptrana.net/bwapp/logs/?C=N;O=D'
            if (/C=\w;O=(D|A)/i.test(uriRaw.search)) {
                return false
            }
            let method = _.get(attack, 'httpRequest.method')
            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
            let ResBody = attack.result.resp.body.replace(/\r\n/g, '')
            if (method == 'GET' && ((statusCode == 200) || redirect > 0) && !/>(?:\[To | )?Parent Directory\]?</i.test(ResBody)) {
                return true
            }
            else {
                return false
            }
        }
        return false
    }

    async processAttackResponse(attack) {
        let uriRaw = new URL(attack.href)
        let FileList = []
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.FileList && pluginStorageScanScope.FileList.length > 0) {
            FileList = pluginStorageScanScope.FileList
        }
        FileList.push(uriRaw.pathname)
        pluginStorageScanScope.FileList = FileList
        // let protocol = uriRaw.protocol
        let details = []
        let currhref = uriRaw.origin + uriRaw.pathname
        let response = []
        let fileresult = []

        // Elapsed time detection
        let startTime = new Date();
        let endTime, Duration, minutes

        try {
            for (let newpath of FileNameList) {
                let UpdatedURL = currhref + newpath
                let FileDts = await this.getResponse(UpdatedURL)
                // let FileDts = await this.getResponse(UpdatedURL, protocol)
                endTime = new Date();
                Duration = endTime - startTime;
                minutes = Duration / (1000 * 60);
                if (FileDts && FileDts == 200) {
                    response.push(newpath)
                    fileresult = 'File Found: ' + response
                    details = `Scanned URL: ${currhref}.\n${fileresult}.\nDuration:${minutes}`
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                }

                if (minutes > 30) {
                    break
                }
            }
            if (response.length > 0) {
                fileresult = 'File Found: ' + response
                details = `Scanned URL: ${currhref}.\n${fileresult}.\nDuration:${minutes}`
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                return
            }
        }
        catch (e) { return }
    }

    getResponse(UpdatedURL) {
        return new Promise((resolve) => {
            let cmd = `echo "quit" | curl -o /dev/null -s -w "%{http_code}" ${UpdatedURL}`
            exec(cmd, (err, stdout, stderr) => {
                if (stdout) {
                    resolve(stdout)
                }
                else {
                    resolve('100');
                }
            });
        })
    }

    /* getResBody(url, protocol) {
        return new Promise((resolve) => {
            // let UpdatedURL = 'http://localhost/admin/test/'
            let UpdatedURL = url
            if (protocol == 'http:') {
                http.get(UpdatedURL, function (res) {
                    if (res.statusCode == 200) {
                        res.setEncoding('utf8');
                        res.on('data', function (chunk) {
                            resolve(chunk);
                        });
                    }
                    else {
                        resolve("HaikuTestSkip")
                    }
                });
            }
            else if (protocol == 'https:') {
                https.get(UpdatedURL, function (res) {
                    if (res.statusCode == 200) {
                        res.setEncoding('utf8');
                        res.on('data', function (chunk) {
                            resolve(chunk);
                        });
                    }
                    else {
                        resolve("HaikuTestSkip")
                    }
                });
            }
        })
    } */

    /* getResponse(UpdatedURL, protocol) {
        return new Promise((resolve) => {
            if (protocol == 'https:') {
                https.get(UpdatedURL, (resp) => {
                    if (resp.statusCode == 200) {
                        resolve('200')
                    }
                    else {
                        resolve('100')
                    }
                }).on("error", (err) => { resolve("error", err) });
            }
            else if (protocol == 'http:') {
                http.get(UpdatedURL, (resp) => {
                    if (resp.statusCode == 200) {
                        resolve('200')
                    }
                    else {
                        resolve('100')
                    }
                }).on("error", (err) => { resolve("error", err) });
            }
        })
    } */
}

// let DirectoryFound = []
/* let DLAttackVectors = []
let DirNameList = [] */
let FileNameList = []
try {
    // DirNameList = fs.readFileSync('DirNameList.txt', 'utf8').split('\r\n')
    /* DirNameList = fs.readFileSync('./network-scanner/app/plugins/DirNameList.js', 'utf8').replace(/`/g, '').split('\r\n')
    DLAttackVectors = DirNameList */
    FileNameList = fs.readFileSync('./network-scanner/app/plugins/FileNameList.js', 'utf8').replace(/`/g, '').split('\r\n')
    /* if (DirNameList.length == 0) {
        DirNameList = [
            `admin`,
            `documents`,
            `ansari`,
            `test`,
            `ansaritest`,
            `test22`,
            `soap`,
            `js`,
            `db`,
            `fonts`,
            `passwords`,
        ]
        DLAttackVectors = DirNameList
    } */
    if (FileNameList.length == 0) {
        FileNameList = [
            `phpinfo.php`,
            `install.php`,
            `robots.txt`,
            `login.php`,
            `ansa.php`,
            `index.php`,
            `settings.php`,
        ]
    }
}
catch (err) {
    console.error(err);
    return
}
module.exports = FileListAuto