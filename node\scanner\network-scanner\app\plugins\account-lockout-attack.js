const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const loginChecker = require('./loginChecker');

// Pre-compiled regex patterns for better performance
const PASSWORD_PARAM_REGEX = /password|passwd?|pwd|(?:login|auth|user)_pass|user_secret|passcode/i;
const LOCKOUT_PATTERNS = [
    /(?:account|user).*(?:locked|disabled|suspended|blocked)/i,
    /(?:locked|disabled|suspended|blocked).*(?:account|user)/i,
    /too many.*(?:attempts|tries|logins)/i,
    /exceeded.*(?:attempts|tries|logins)/i,
    /rate.*limit/i,
    /try.*later/i,
    /temporarily.*blocked/i,
    /security.*lock/i,
    /please.*wait/i,
    /(?:login|access).*denied/i,
    /(?:account|access).*restricted/i,
    /(?:suspicious|unusual).*activity/i,
    /(?:multiple|repeated).*failed.*attempts/i,
    /(?:temporary|permanent).*ban/i,
    /(?:cooldown|wait).*period/i
].map(pattern => new RegExp(pattern));

const RATE_LIMIT_HEADERS = new Set([
    "x-ratelimit-limit",
    "x-ratelimit-remaining",
    "x-ratelimit-reset",
    "retry-after",
    "x-rate-limit-limit",
    "x-rate-limit-remaining",
    "x-rate-limit-reset",
    "rate-limit-limit",
    "rate-limit-remaining",
    "rate-limit-reset"
]);

const TEST_CREDENTIAL_PATTERNS = [
    /Test@1234/i,
    /Test%401234/i,
    /defaultText/i
].map(pattern => new RegExp(pattern));

const CAPTCHA_PATTERNS = [
    /captcha/i,
    /i am not a robot/i,
    /(?:enter|type) the (?:characters|text) (?:you|above|below|seen)/i,
    /(?:recaptcha|hcaptcha)/i,
    /(?:verify|security) code/i,
    /(?:image|visual) verification/i,
    /(?:solve|complete) this/i,
    /(?:prove|confirm) you.*human/i,
    /(?:robot|bot) check/i,
    /(?:challenge|verification) required/i,
    /(?:enter|type) the (?:code|number|letters)/i,
    /(?:security|verification) (?:check|step)/i,
    /(?:human|manual) verification/i,
    /(?:anti|prevent).*bot/i,
    /(?:spam|abuse) protection/i
].map(pattern => new RegExp(pattern));

const CAPTCHA_ELEMENTS = [
    /<div[^>]*class="[^"]*captcha[^"]*"[^>]*>/i,
    /<div[^>]*id="[^"]*captcha[^"]*"[^>]*>/i,
    /<input[^>]*type="[^"]*captcha[^"]*"[^>]*>/i,
    /<iframe[^>]*src="[^"]*(?:recaptcha|hcaptcha)[^"]*"[^>]*>/i,
    /<script[^>]*src="[^"]*(?:recaptcha|hcaptcha)[^"]*"[^>]*>/i,
    /<div[^>]*data-sitekey="[^"]*"[^>]*>/i,
    /<div[^>]*data-callback="[^"]*"[^>]*>/i,
    /<div[^>]*data-widget-id="[^"]*"[^>]*>/i,
    /<div[^>]*class="[^"]*g-recaptcha[^"]*"[^>]*>/i,
    /<div[^>]*class="[^"]*h-captcha[^"]*"[^>]*>/i
].map(pattern => new RegExp(pattern));

const CAPTCHA_SCRIPTS = [
    /recaptcha\.js/i,
    /hcaptcha\.js/i,
    /captcha\.js/i,
    /verify\.js/i,
    /security\.js/i
].map(pattern => new RegExp(pattern));

/**
 * Account Lockout Attack strategy:
 * Here for all the login pages will be attempting to login with valid username and invalid password for 5 times
 * uptil that point we will try to see if any lockout for the said account happens or not. If not then will
 * report it as vulnerability
 */
class AccountLockout extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-account-lockout' // Vulnerability ID for account lockout
        this.vulnerabilityIDCaptcha = 'ID-no-captcha-on-login-page' // Vulnerability ID for no captcha on login page
    }

    getAttackVectors() {
        return passwordVectors
        // return LoginDelegate.createVectorsIterator(usernameVectors, passwordVectors)
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
        // return ['login-request'] - 
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            attackParamName: false,
        })
    }

    async performNetworkAttack(attack) {
        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        if (!pluginStorageScanScope.loginCheckerResult?.success) return false;
        if (pluginStorageScanScope.accountLockout) return false;

        // Initialize counters if not present
        pluginStorageScanScope.lockoutCount = pluginStorageScanScope.lockoutCount || 0;
        pluginStorageScanScope.loginAttempt = pluginStorageScanScope.loginAttempt || 0;

        // Check if we've reached the required number of attempts
        if (pluginStorageScanScope.lockoutCount >= this.getMetadata(attack).verifyAfterNumberOfLoginAttempts ||
            pluginStorageScanScope.loginAttempt >= this.getMetadata(attack).verifyAfterNumberOfLoginAttempts) {
            return false;
        }
        //skip the new, old, confirm, change, reset, forgot, hidden, htxt, txth, hdn, hpwd, hpas password parameters
        if (/new|old|confirm|change|reset|forg[e|o]t|hidden|htxt|txth|hdn|hpwd|hpas/i.test(attack.param)) {
            return false;
        }
        const paramVal = _.get(attack, 'paramVal', '').toLowerCase();
        if (paramVal.includes('test@1234') || paramVal.includes('test%401234') || paramVal.includes('defaultText')) {
            return false;
        }

        // Check for obfuscated values (excluding base64)
        const isBase64 = /^[A-Za-z0-9+/=]+$/.test(paramVal) &&
            paramVal.length % 4 === 0 &&
            !/^\d+$/.test(paramVal) && // Not just numbers
            !/^[a-z]+$/.test(paramVal) && // Not just lowercase letters
            !/^[A-Z]+$/.test(paramVal) && // Not just uppercase letters
            (paramVal.includes('+') || paramVal.includes('/') || paramVal.includes('=')); // Must contain base64 specific chars
        const isObfuscated = /%[0-9A-Fa-f]{2}/.test(paramVal) || // URL encoded
            /&#x?[0-9A-Fa-f]+;/.test(paramVal) || // HTML encoded
            /\\x[0-9A-Fa-f]{2}/.test(paramVal) || // Hex encoded
            /\\u[0-9A-Fa-f]{4}/.test(paramVal);   // Unicode encoded

        // Check if base64 encoded value decodes to obfuscated content
        if (isBase64) {
            try {
                const decoded = Buffer.from(paramVal, 'base64').toString();
                const decodedIsObfuscated = /%[0-9A-Fa-f]{2}/.test(decoded) || // URL encoded
                    /&#x?[0-9A-Fa-f]+;/.test(decoded) || // HTML encoded
                    /\\x[0-9A-Fa-f]{2}/.test(decoded) || // Hex encoded
                    /\\u[0-9A-Fa-f]{4}/.test(decoded) || // Unicode encoded
                    /[٢ںمϽ]/.test(decoded);
                if (decodedIsObfuscated) {
                    return false;
                }
            } catch (e) {
                // If base64 decoding fails, treat as non-base64
            }
        }

        if (isObfuscated && !isBase64) {
            return false;
        }

        return PASSWORD_PARAM_REGEX.test(attack.param) ?
            await super.performNetworkAttack(attack) :
            false;
    }

    async wantProcessAttackResponse(attack) {
        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        if (pluginStorageScanScope.accountLockout) return false;

        const ReqBody = _.get(attack, 'httpRequest.body', '').toLowerCase();
        const hasPasswordParam = PASSWORD_PARAM_REGEX.test(ReqBody) ||
            PASSWORD_PARAM_REGEX.test(attack.href.toLowerCase());

        if (!hasPasswordParam) return false;

        if (attack.attackArea === 'original-crawler-request' &&
            attack.pluginName === 'Original Crawler Request') {
            pluginStorageScanScope.loginCheckerResult = await loginChecker.checkLogin(attack);
        }

        return attack.pluginName === this.getName() &&
            pluginStorageScanScope.loginCheckerResult?.success;
    }

    async processAttackResponse(attack) {
        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        if (pluginStorageScanScope.accountLockout ||
            pluginStorageScanScope.lockoutChecked ||
            pluginStorageScanScope.NoCaptchaOnLoginPage) return;

        if (attack.pluginName !== this.getName() ||
            !pluginStorageScanScope.loginCheckerResult?.success) return;

        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode');
        if (statusCode >= 500) return;

        const ResBody = _.get(attack, "result.resp.body", "").toLowerCase();
        const headers = _.get(attack, "result.resp.httpResponse.headers", {});

        // Optimized lockout detection
        const isLocked = LOCKOUT_PATTERNS.some(pattern => pattern.test(ResBody));
        const hasRateLimitHeaders = Object.keys(headers).some(header =>
            RATE_LIMIT_HEADERS.has(header.toLowerCase())
        );
        const isRateLimited = statusCode === 429 || statusCode === 403;

        if (isLocked || hasRateLimitHeaders || isRateLimited) {
            pluginStorageScanScope.accountLockout = true;
            return;
        }

        if (pluginStorageScanScope.loginCheckerResult.testPass === false && !pluginStorageScanScope.lockoutChecked) {
            this.AccountLockout(attack);
        }

        if (!pluginStorageScanScope.NoCaptchaOnLoginPage) {
            this.captcha(attack);
        }
    }

    AccountLockout(attack) {
        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        pluginStorageScanScope.lockoutCount = (pluginStorageScanScope.lockoutCount || 0) + 1;

        const requestBody = _.get(attack, 'originalRequest.httpRequest.body', '').toLowerCase();
        const requestUri = _.get(attack, 'originalRequest.httpRequest.uri', '').toLowerCase();
        const paramVal = _.get(attack, 'paramVal', '').toLowerCase();

        const hasTestCredentials = TEST_CREDENTIAL_PATTERNS.some(pattern =>
            pattern.test(requestBody) || pattern.test(requestUri) || pattern.test(paramVal)
        );

        if (hasTestCredentials) return;

        if (pluginStorageScanScope.lockoutCount >= this.getMetadata(attack).verifyAfterNumberOfLoginAttempts) {
            const lockoutMessage = `The website does not implement account lockout after ${pluginStorageScanScope.lockoutCount} consecutive failed login attempts with invalid password. System remains vulnerable to automated brute force attacks. No protection against suspicious login patterns.`;
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, lockoutMessage);
            pluginStorageScanScope.lockoutChecked = true;
        }
    }

    captcha(attack) {
        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        pluginStorageScanScope.loginAttempt = (pluginStorageScanScope.loginAttempt || 0) + 1;

        if (pluginStorageScanScope.loginAttempt >= this.getMetadata(attack).verifyAfterNumberOfLoginAttempts) {
            const ResBody = _.get(attack, 'result.resp.httpResponse.body', '').toLowerCase();
            const hasCaptcha = CAPTCHA_PATTERNS.some(pattern => pattern.test(ResBody)) ||
                CAPTCHA_ELEMENTS.some(pattern => pattern.test(ResBody)) ||
                CAPTCHA_SCRIPTS.some(pattern => pattern.test(ResBody));

            if (!hasCaptcha) {
                const result = `The website does not implement CAPTCHA verification on the login page, even after ${pluginStorageScanScope.loginAttempt} failed login attempts. This allows automated tools to attempt unlimited login attempts. The system is vulnerable to brute force and automated attacks.`;
                this.addVulnerabilitytoResult(attack, this.vulnerabilityIDCaptcha, result);
                pluginStorageScanScope.NoCaptchaOnLoginPage = true;
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}
//we are going to try with 6 set of invalid password
const passwordVectors = [
    // all the password vectors, can use IdentityVector as well
    `111111`,
    `abc123`,
    `monkey`,
    `qwerty`,
    `testtest`,
    `dummytest`,
    `123456`,
    `*********`,
    `*********0`,
    `*********0*********0`,
    `*********0*********0*********0`,
    `*********0*********0*********0*********0`,
    `*********0*********0*********0*********0*********0`,
]

module.exports = AccountLockout