
    <!DOCTYPE html>
    <html>
    <head>
        <title>CSS Injection Test</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f2f2f2;
                padding: 20px;
            }
            .box {
                padding: 15px;
                margin-top: 20px;
                border: 2px solid #ccc;
            }
        </style>
        defaultText  <!-- INJECTION POINT -->
    </head>
    <body>
        <h1>CSS Injection Test</h1>
        <form method="GET">
            <label>Enter your CSS injection payload:</label><br><br>
            <input type="text" name="input" style="width: 80%;" value="defaultText"><br><br>
            <button type="submit">Submit</button>
        </form>

        <div class="box" style="">
            Target DIV with inline style
        </div>

        <p class="box" style="">
            Paragraph with inline style
        </p>

        <div class="box">
            <strong>Injected Payload:</strong><br>
            <code>defaultText</code>
        </div>
    </body>
    </html>
    