const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const cheerio = require("cheerio")
const HaikuUtils = require('../../../common/lib/haiku-utils')

class BreachAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-breach-attack'
        this.vulnerabilityIDLow = 'ID-breach-attack-low'
    }

    /* checkSecretKey(attack) {
        let res = [];
        let $ = _.get(attack, 'result.resp.httpResponse.cheerio')
        let hiddenFields = $('input[type=hidden]')
        if (hiddenFields.length > 0) {
            hiddenFields.each(function (index) {
                let name = $(this).attr('name')
                let val = $(this).attr('value')
                for (let keyword of secretKeyToCheck) {
                    if (val && typeof val == 'string' && !/none/i.test(val) && name && typeof name == 'string' && keyword.toLowerCase().includes(name.toLowerCase())) {
                        res.push(keyword.toLowerCase())
                    }
                }
            })
        }
        return res
    } */

    getAttackVectors() {
        return _attackVectors
    }

    /** 
     * Only attack header: Referer, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override    
     */

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['User-Agent', 'Referer', 'Host', 'Origin', 'Cookie', 'X-Forwarded-Host', 'Accept-Language']
        })
    }

    getAttackableEvents() {
        return ['http-headers', 'form-encoded-post', 'uri-query-params']
    }

    async performNetworkAttack(attack) {
        attack.httpRequest.headers["Accept-Encoding"] = "gzip, deflate, br"
        return await super.performNetworkAttack(attack)
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    wantProcessAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return false
        }

        // slight optimization - only process if 'haikumsg' found in the body
        let rawHtml = _.get(attack, 'result.resp.body')
        let CEHeader = _.get(attack, 'result.resp.httpResponse.headers["content-encoding"]', '')
        if (/haikumsg/i.test(rawHtml) && /gzip|deflate|br/i.test(CEHeader)) {
            return true
        }
        return false
    }

    async processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        //if already found dont check further
        if (pluginDataForRequest.BreachAttack == true) {
            return
        }
        let CEHeader = _.get(attack, 'result.resp.httpResponse.headers["content-encoding"]', '')
        let rawHtml = _.get(attack, 'result.resp.body')

        if (sensitiveDataToCheck.test(rawHtml)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, CEHeader)
            pluginDataForRequest.BreachAttack = true
        }
        else {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityIDLow, CEHeader)
            pluginDataForRequest.BreachAttack = true
        }

        /* let CEHeader_original = _.get(attack, 'originalRequest.httpResponse.headers["content-encoding"]')
        let CEHeader_attack = _.get(attack, 'originalRequest.httpResponse.headers["content-encoding"]')
        if (!CEHeader_original && !typeof CEHeader_original == "string" && !/gzip|deflate/i.test(CEHeader_original)) {
            return
        }

        if (!CEHeader_attack && !typeof CEHeader_attack == "string" && !/gzip|deflate/i.test(CEHeader_attack)) {
            return
        }/* 
        <input type="hidden"                        
        name="_csrf"
        value="63e37670-98c4-4218-afda-bcae5ee504b9"/>
        <div>
        <input type="hidden" name="_csrf" value="63e37670-98c4-4218-afda-bcae5ee504b9" />
        </div></form */

       /*  let details = []
        let ResBody = _.get(attack, "result.resp.body", '')
        let regexp = /<form.+method=[\w\W]+?<\/form>/gi
        let formtag = []
        try {
            formtag = ResBody.match(regexp)
            if (formtag.length > 0 && /type=(?:"|')hidden(?:"|')/i.test(formtag)) {
                for (let tag of formtag) {
                    if (details.length > 0) { break }
                    if (tag.length > 20 && /type=(?:"|')hidden(?:"|')/i.test(tag)) {
                        let inputtag = tag.match(/<input.+?type=(?:"|')hidden(?:"|').+?>/gi)
                        for (let secretvalue of inputtag) {
                            if (secretvalue.length > 0 && secretKeyToCheck.test(secretvalue)) {
                                details.push({ result: secretvalue.match(/name=.+?'/i)[0].split('=')[1].replace(/\'/g, '') })
                                break
                            }
                        }
                    }
                }
                if (details.length > 0) {
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                    pluginDataForRequest.BreachAttack = true
                }
            }
        }
        catch (e) {
            return
        } */
        /* let details = await this.checkSecretKey(attack)
        if (details.length > 0) {
            let vuln = {
                details: {
                    "vulnerability": "exists",
                    "secret": details.toLocaleString()
                }
            }
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginDataForRequest.BreachAttack = true
        }
        return/*  */
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["content-encoding"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['haikumsg']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
    }
}
const _attackVectors = [
    `haikumsg`
]
const sensitiveDataToCheck = /ANTI_FRAUD|_CSRFTOKEN|forgeryToken|_csrf_token|csrf_token|csrftoken|pwd-reset-update-token|_token|invisible_token|CSRF token|"token"|_Requestverification|xsrf|__TokenAntiXsrf|authenticity_token|token_id|CSRFToken|client_token|_csrf_token|requesttoken|_token|authenticity_token|user_token|session_?id|type=(?:'|")password|((credit|debit|\bpan)(_|-| )?(card|id|no)|vehEng|vehChs|(user|emp|employee)(_|-)?(id|name|Input)|uname|uid|user|(account|ac)(_|-)?(id)?|password|passwd|pwd|api(_|-)?key|ProposerPAN|kyc_id|customerId) ?(=|" ?:) ?('|")(?! \+ |\+)[^'"<>${}\n\?=&\/%\[\]]+\13|"password\\":\\".+?\\"/i

module.exports = BreachAttack