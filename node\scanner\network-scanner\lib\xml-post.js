const _ = require('lodash')
const logger = require('../../common/lib/haiku-logger')
const cheerio = require('cheerio');

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = 'XMLPost'

class XMLPost extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    static isXMLPostRequest(httpRequest) {
        return httpRequest.method == 'POST' &&
            (_.get(httpRequest, "headers['Content-Type']", '').includes('application/xml') ||
                _.get(httpRequest, "headers['content-type']", '').includes('application/xml')) &&
            _.get(httpRequest, 'body.length') > 0
    }

    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore, options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType, options)
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    * getIterator() {
        try {
            // the original post body is used to get the clone of original request
            this.originalPostbody = _.cloneDeep(this.originalRequest.httpRequest.body)

            // cheerio to load the xml body to parse
            this.cheerio = cheerio.load(this.originalPostbody, {
                xmlMode: true,
                decodeEntities: false
            });
    
            /**
             * It parses original post body and returns a cheerio object.
             * which is the original cheerio objects and it will be in read-only mode through out the iteration
             */
            this.originalCheerio = cheerio.load(this.originalPostbody, {
                xmlMode: true,
                decodeEntities: false
            })

            /**
             * the xmlNodes cheerio object is used to modify object param/value.
             */
            this.xmlNodes = this.cheerio('*').get();

            /**
             * the originalXMlNodes is only used for iteration and always be in read-only mode
             */
            this.originalXMlNodes = this.originalCheerio('*').get();

            let leafsAttacked = 0;
            let nonleafsAttacked = 0;

            for (let i = 0; i < this.originalXMlNodes.length; i++) {
                /**
                 * cheerio returns a children array object for each tag element,
                 * based on count will decide it has one or more children
                 */
                this.tagsChildrenCount = this.originalXMlNodes[i].children.length;

                // checking If tag element has only one children with value or without value & leafsAttacked less than the attacked node
                if (this.tagsChildrenCount <= 1 && leafsAttacked < this.options.leafNodesToAttack) {
                    let tagValue = (!_.isEmpty(this.originalXMlNodes[i].children)) ? this.originalXMlNodes[i].children[0].data : undefined;

                    yield {
                        name: {
                            "index": i,
                            "node": this.originalXMlNodes[i].name + i
                        },
                        val: tagValue,
                        resetRequired: true
                    }
                    leafsAttacked++;
                }

                // checking If tag element has more than one children & nonleafsAttacked less than the attacked node
                if (this.tagsChildrenCount > 1 && nonleafsAttacked < this.options.nonleafNodestoAttack) {
                    yield {
                        name: {
                            "index": i,
                            "node": this.originalXMlNodes[i].name + i
                        },
                        val: this.originalXMlNodes[i].children,
                        resetRequired: true
                    }
                    nonleafsAttacked++;
                }

                /**
                 * checking if sum of non-leaf/leaf is (>=) maximum attack nodes value. 
                 * then both the attacks are completed and no further attacks.
                 */
                if (nonleafsAttacked + leafsAttacked >= this.options.maxNodesToAttack) {
                    break
                }
            }
        } catch (error) {
            logger.log('error', `xml post - ${error.toString()}`)
        }
    }

    modifyParam(param, value) {
        // If prependAttackVector vector enabled then the attackVector will prepend with the children node.
        if (this.options.prependAttackVector) {
            /**
             * reset() is required to call in case of prepend because currently as per the delegate implementation reset()
             * will only be called when it switches to another vector.
             */
            this.reset()
            
            // attackVector will prepend with the children node.
            this.cheerio(this.xmlNodes[param.index]).prepend(value)
        } else {
            // replacing the attackVectors with the nonleaf children node / leafnode children value
            this.cheerio(this.xmlNodes[param.index]).html(value)
        }
    }

    // get the modified request
    getHttpRequest() {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)

        req.body = this.cheerio.xml()

        return req
    }

    reset() {
        /**
         * To reset the xmlNodes it required to reparse the original post body 
         * and it will return the original cheerio objects.
         */
        this.cheerio = cheerio.load(this.originalPostbody, {
            xmlMode: true,
            decodeEntities: false
        })
        this.xmlNodes = this.cheerio('*').get()
    }
}
module.exports = XMLPost