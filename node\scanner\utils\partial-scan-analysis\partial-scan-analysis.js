var fs = require('fs');
const path = require('path');
const Minimist = require('minimist');
_ = require('lodash');
const ssAPI = require('../../common/lib/sooper-scheduler-api');
const logger = require('../../common/lib/haiku-logger');
const s3Utils = require('../../common/lib/s3-utils')
const HaikuUtils = require('../../common/lib/haiku-utils');
const Request = require('request-promise-native');


// global
let utilMachineApiEndPoint = process.env['UTIL_MACHINE_API_ENDPOINT'] || 'http://localhost:3000';

logger.setMetadata({
    haikuProcess: 'partial-scan-analysis'
});

class PartialScanData {
    constructor(serviceId, scanUrl, scanlogId, jobSessionId, scanHour, endtime, crawlerIsPartial, scannerIsPartial, repeatscan, totalCrawlDurationMins, uniqueCrawlerRequests, attackedCrawlerRequests) {
        this.serviceId = serviceId;
        this.scanUrl = scanUrl;
        this.scanlogId = scanlogId;
        this.jobSessionId = jobSessionId;
        this.scanHour = scanHour;
        this.endtime = endtime;
        this.crawlerIsPartial = crawlerIsPartial;
        this.scannerIsPartial = scannerIsPartial;
        this.repeatscan = repeatscan;
        this.totalCrawlDurationMins = totalCrawlDurationMins;
        this.uniqueCrawlerRequests = uniqueCrawlerRequests;
        this.attackedCrawlerRequests = attackedCrawlerRequests;
    }
}

/** 
 * Out of band vulnerability check util class. i.e. log4j vuln
 */
class PartialScanAnalysis {
    constructor(partialScanFile) {
        this.partialScanFile = partialScanFile;
        this.partialScanData = {};
        // Column names
        this.service_id_column_index = -1;
        this.scan_url_column_index = -1;
        this.scanlog_id_column_index = -1;
        this.job_session_id_column_index = -1;
        this.scan_hour_column_index = -1;
        this.endtime_column_index = -1;
        this.crawler_is_partial_column_index = -1;
        this.scanner_is_partial_column_index = -1;
        this.repeatscan_column_index = -1;
        this.total_crawl_duration_mins_column_index = -1;
        this.unique_crawler_requests_column_index = -1;
        this.attacked_crawler_requests_column_index = -1;
    }

    async readPartialScanFile() {
        //read partial-scan-new.csv file
        let partialScanData = [];

        let fileData = fs.readFileSync(this.partialScanFile, 'utf8');
        let lines = fileData.split('\r\n');
        let header = lines[0].split(',');

        //ServiceId	ScanUrl	scanlogId	jobSessionId	ScanHour	endtime	Crawler_isPartial	Scanner_isPartial	repeatscan	totalCrawlDurationMins	uniqueCrawlerRequests	uniqueCrawlerRequests	attackedCrawlerRequests
        // get column indexes
        this.service_id_column_index = header.indexOf('ServiceId') > -1 ? header.indexOf('ServiceId') : -1;
        this.scan_url_column_index = header.indexOf('ScanUrl') > -1 ? header.indexOf('ScanUrl') : -1;
        this.scanlog_id_column_index = header.indexOf('scanlogId') > -1 ? header.indexOf('scanlogId') : -1;
        this.job_session_id_column_index = header.indexOf('jobSessionId') > -1 ? header.indexOf('jobSessionId') : -1;
        this.scan_hour_column_index = header.indexOf('ScanHour') > -1 ? header.indexOf('ScanHour') : -1;
        this.endtime_column_index = header.indexOf('endtime') > -1 ? header.indexOf('endtime') : -1;
        this.crawler_is_partial_column_index = header.indexOf('Crawler_isPartial') > -1 ? header.indexOf('Crawler_isPartial') : -1;
        this.scanner_is_partial_column_index = header.indexOf('Scanner_isPartial') > -1 ? header.indexOf('Scanner_isPartial') : -1;
        this.repeatscan_column_index = header.indexOf('repeatscan') > -1 ? header.indexOf('repeatscan') : -1;
        this.total_crawl_duration_mins_column_index = header.indexOf('totalCrawlDurationMins') > -1 ? header.indexOf('totalCrawlDurationMins') : -1;
        this.unique_crawler_requests_column_index = header.indexOf('uniqueCrawlerRequests') > -1 ? header.indexOf('uniqueCrawlerRequests') : -1;
        this.attacked_crawler_requests_column_index = header.indexOf('attackedCrawlerRequests') > -1 ? header.indexOf('attackedCrawlerRequests') : -1;

        // check if column exist & if not return
        if (this.service_id_column_index == -1 || this.scan_url_column_index == -1 || this.scanlog_id_column_index == -1 || this.job_session_id_column_index == -1 || this.scan_hour_column_index == -1 || this.endtime_column_index == -1 || this.crawler_is_partial_column_index == -1 || this.scanner_is_partial_column_index == -1 || this.repeatscan_column_index == -1 || this.total_crawl_duration_mins_column_index == -1 || this.unique_crawler_requests_column_index == -1 || this.attacked_crawler_requests_column_index == -1) {
            logger.error(`column missing in partial scan file`);
            return;
        }

        //read data & create ScanData object
        for (let i = 1; i < lines.length; i++) {
            if(lines[i] == '') continue;
            let line = lines[i].split(',');
            let serviceId = parseFloat(line[this.service_id_column_index].replace(/"/g, ''));
            let scanUrl = line[this.scan_url_column_index].replace(/"/g, '');
            let scanlogId = parseFloat(line[this.scanlog_id_column_index].replace(/"/g, ''));
            let jobSessionId = parseFloat(line[this.job_session_id_column_index].replace(/"/g, ''));
            let scanHour = line[this.scan_hour_column_index].replace(/"/g, '');
            let endtime = line[this.endtime_column_index].replace(/"/g, '');
            let crawlerIsPartial = parseFloat(line[this.crawler_is_partial_column_index].replace(/"/g, ''));
            let scannerIsPartial = parseFloat(line[this.scanner_is_partial_column_index].replace(/"/g, ''));
            let repeatscan = line[this.repeatscan_column_index].replace(/"/g, '');
            let totalCrawlDurationMins = parseFloat(line[this.total_crawl_duration_mins_column_index].replace(/"/g, ''));
            let uniqueCrawlerRequests = parseFloat(line[this.unique_crawler_requests_column_index].replace(/"/g, ''));
            let attackedCrawlerRequests = parseFloat(line[this.attacked_crawler_requests_column_index].replace(/"/g, ''));

            let partialScanData = new PartialScanData(serviceId, scanUrl, scanlogId, jobSessionId, scanHour, endtime, crawlerIsPartial, scannerIsPartial, repeatscan, totalCrawlDurationMins, uniqueCrawlerRequests, attackedCrawlerRequests);
            this.partialScanData[serviceId] = partialScanData;
        }
    }

    async checkScannerPartialScans() {
        //get scan where the crawlerIsPartial is 0 & scannerIsPartial is 1, use lodash to filter the data
        let partialScanData = this.partialScanData;
        let scannerPartialScans = _.filter(partialScanData, { 'crawlerIsPartial': 0, 'scannerIsPartial': 1 });
        let scannerPartialScansCount = scannerPartialScans.length;
        console.log('scannerPartialScansCount', scannerPartialScansCount);
    }
}

// --- Main code
let argv = Minimist(process.argv.slice(2), {
    boolean: true
})

async function main() {
    let partialScanFile = argv['partialScanFile'];

    if (!partialScanFile) {
        logger.error(`partialScanFile is required`);
        return;
    }

    logger.log('info', `running partial scan analysis for partialScanFile=${partialScanFile}`);
    let partialScanAnalysis = new PartialScanAnalysis(partialScanFile);
    await partialScanAnalysis.readPartialScanFile();
    await partialScanAnalysis.checkScannerPartialScans();
    await partialScanAnalysis.checkElasticSearchServiceStatus();
    console.log('done');
}

if (argv['partialScanFile']) {
    console.log('running partial scan analysis for partialScanFile', argv['partialScanFile']);
    // main / module exports based on command line args
    (async function (params) {
        await main();
    })();
} else {
    module.exports = PartialScanAnalysis
}