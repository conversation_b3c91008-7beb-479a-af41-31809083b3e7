{"info": {"_postman_id": "7f87b614-9e45-4cfa-bb22-fd278a2178e3", "name": "NewHeadless-22122020", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "https://service-api.qa-aegonlife.com/partner/policy", "request": {"method": "POST", "header": [{"key": "x-api-key", "value": "GDKCNQu8YKaiYoy0FNnl25NXOnFWwOpCaZaWQz0b"}, {"key": "Authorization", "value": "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n   \"party\":null,\n   \"policy\":null,\n   \"insured\":{\n      \"gender\":\"Male\",\n      \"emailId\":\"<EMAIL>\",\n      \"lastName\":\"<PERSON>\",\n      \"addresses\":[\n         {\n            \"id\":null,\n            \"city\":null,\n            \"state\":\"\",\n            \"country\":\"India\",\n            \"pincode\":\"500038\",\n            \"addressType\":\"RESIDENCE\",\n            \"addressLine1\":\"40 Indian Spring Street\",\n            \"addressLine2\":null,\n            \"addressLine3\":null,\n            \"addressLine4\":null\n         }\n      ],\n      \"firstName\":\"<PERSON>\",\n      \"dateOfBirth\":\"1976-03-19\",\n      \"identityInfo\":[\n         {\n            \"type\":\"PAN\",\n            \"value\":\"**********\"\n         }\n      ],\n      \"permanentAddress\":{\n         \"id\":null,\n         \"city\":null,\n         \"state\":\"Karnataka\",\n         \"country\":\"India\",\n         \"pincode\":\"500044\",\n         \"addressType\":\"RESIDENCE\",\n         \"addressLine1\":\"WO DURASAMY 1/179 SOUTH Street, NAVAMARATHUPATTI SUVLERUMBU DINDIGUL\",\n         \"addressLine2\":null,\n         \"addressLine3\":null,\n         \"addressLine4\":null\n      },\n      \"professionalInfo\":{\n         \"jobBand\":null,\n         \"occupation\":null,\n         \"designation\":null,\n         \"annualIncome\":1000000.0,\n         \"natureOfWork\":null,\n         \"organizationName\":null,\n         \"occupationalClass\":null\n      },\n      \"creditScoreRequired\":false\n   },\n   \"healthInfo\":null,\n   \"policyInfo\":{\n      \"pinCode\":null,\n      \"premium\":{\n         \"initialPremium\":190.0,\n         \"paymentFrequency\":\"YEARLY\",\n         \"premiumPayOption\":null\n      },\n      \"product\":null,\n      \"loanType\":null,\n      \"nominees\":[\n         {\n            \"age\":30,\n            \"email\":null,\n            \"gender\":null,\n            \"mobile\":null,\n            \"lastName\":null,\n            \"appointee\":null,\n            \"firstName\":\"Simmi Jones\",\n            \"middleName\":null,\n            \"dateOfBirth\":null,\n            \"identityInfo\":null,\n            \"otherRelationInfo\":null,\n            \"relationOfNominee\":null,\n            \"nominationPercentage\":100.0\n         }\n      ],\n      \"planCode\":\"OM1-0001\",\n      \"coverages\":[\n         {\n            \"code\":\"OM1-0001-DB\",\n            \"type\":null,\n            \"loading\":null,\n            \"premium\":null,\n            \"cGstAmount\":null,\n            \"cgstAmount\":null,\n            \"iGstAmount\":null,\n            \"igstAmount\":null,\n            \"sGstAmount\":null,\n            \"sgstAmount\":null,\n            \"sumAssured\":100000.0,\n            \"uGstAmount\":null,\n            \"ugstAmount\":null,\n            \"coverageTerm\":null,\n            \"mortgageRate\":null,\n            \"inceptionDate\":null,\n            \"premiumPayingTerm\":null\n         },\n         {\n            \"code\":\"OM1-0001-GCOVH\",\n            \"type\":null,\n            \"loading\":null,\n            \"premium\":null,\n            \"cGstAmount\":null,\n            \"cgstAmount\":null,\n            \"iGstAmount\":null,\n            \"igstAmount\":null,\n            \"sGstAmount\":null,\n            \"sgstAmount\":null,\n            \"sumAssured\":15000.0,\n            \"uGstAmount\":null,\n            \"ugstAmount\":null,\n            \"coverageTerm\":null,\n            \"mortgageRate\":null,\n            \"inceptionDate\":null,\n            \"premiumPayingTerm\":null\n         }\n      ],\n      \"documents\":null,\n      \"startDate\":\"2020-09-02\",\n      \"policyType\":null,\n      \"discountType\":null,\n      \"questionaire\":null,\n      \"incomeBenefit\":null,\n      \"acceptanceTerm\":null,\n      \"lumpSumBenefit\":null,\n      \"proposerEntity\":null,\n      \"applicationDate\":null,\n      \"transactionType\":\"NB\",\n      \"basePolicyNumber\":null,\n      \"moratoriumPeriod\":null,\n      \"policyTermOption\":\"YEAR\",\n      \"purchaseDateTime\":\"2020-09-02T10:30:00.000+0000\",\n      \"relationToHolder\":null,\n      \"memberPolicyRefId\":null,\n      \"masterPolicyNumber\":\"OMNGQ0000000027\",\n      \"coiDispatchDateTime\":null\n   },\n   \"sourceInfo\":null,\n   \"bankDetails\":null,\n   \"paymentInfo\":{\n      \"vendor\":\"OMNITECH\",\n      \"siOpted\":true,\n      \"autoDebit\":null,\n      \"mandateType\":null,\n      \"paymentMode\":null,\n      \"transactionId\":\"****************\"\n   },\n   \"policyHolder\":null\n}"}, "url": {"raw": "https://service-api.qa-aegonlife.com/partner/policy", "protocol": "https", "host": ["service-api", "qa-aegonlife", "com"], "path": ["partner", "policy"]}}, "response": []}, {"name": "get Document for policy for group client", "request": {"method": "GET", "header": [{"key": "x-api-key", "value": "GDKCNQu8YKaiYoy0FNnl25NXOnFWwOpCaZaWQz0b", "type": "text"}, {"key": "Authorization", "value": "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "type": "text"}], "url": {"raw": "https://service-api.qa-aegonlife.com/documents/poldoc?policyNumber=OMNGDQA11673413", "protocol": "https", "host": ["service-api", "qa-aegonlife", "com"], "path": ["documents", "pold<PERSON>"], "query": [{"key": "policyNumber", "value": "OMNGDQA11673413"}]}}, "response": []}, {"name": "offline payment due", "request": {"method": "GET", "header": [], "url": {"raw": "https://service-api.qa-aegonlife.com/payment/offline-due?policyNumber=96G6sSPpA%2F6wtzlu43twZg%3D%3D", "protocol": "https", "host": ["service-api", "qa-aegonlife", "com"], "path": ["payment", "offline-due"], "query": [{"key": "policyNumber", "value": "96G6sSPpA%2F6wtzlu43twZg%3D%3D"}]}}, "response": []}]}