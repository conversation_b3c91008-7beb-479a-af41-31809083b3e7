const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * A security vulnerability that occurs when an application using serialized object to transfer the data through query params and headers.
 */
class SerializedObject extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-serialized-object-http-message'
        this.safePatterns = {
            // Common safe cookie names
            safeNames: [
                'JSESSIONID',           // Java
                'ASP.NET_SessionId',    // ASP.NET
                '__cf_bm',              // Cloudflare bot management
                'AWSALB', 'AWSALBCORS', // AWS Load Balancer
                'PS_TOKEN',             // PeopleSoft
                'rack.session',         // Ruby Rails
                'PHPSESSID',            // PHP
                'Saml2'                 // SAML
            ],

            // Known safe headers indicating specific platforms
            safeHeaders: [
                'cf-ray',               // Cloudflare
                'x-amzn-trace-id',      // AWS
                'x-oracle-dms-ecid'     // PeopleSoft
            ],

            // Removed safeCookiePatterns as discussed to avoid false negatives
            safeCookiePatterns: []
        }
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: false,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never',
            encodings: ['raw'],
        });
    }

    /**
     * get array of attack vectors
     * @override
     */

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params', 'uri-path-iterator']
    }

    /**
     *
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically
     */


    /**
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    // Added the new logic for Java serialized object detection to reduce FPs.
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) return;

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.SerializedObject) return;

        const headersToCheck = ['set-cookie', 'authorization', 'x-session-token', 'x-serialized-data', 'x-csrf-token'];

        const redirects = _.get(attack, 'result.resp.httpResponse.redirects', []);
        if (redirects.length > 0) {
            const redirectHeaders = _.get(redirects[0], 'headers', {});
            if (this.checkSerializedObjectsInHeaders(attack, redirectHeaders, headersToCheck, 'Redirect')) return;
        }

        const responseHeaders = _.get(attack, 'result.resp.httpResponse.headers', {});
        if (this.checkSerializedObjectsInHeaders(attack, responseHeaders, headersToCheck, 'Response')) return;

        const url = _.get(attack, 'originalRequest.httpRequest.uri', '');
        const queryFormat = this.detectQueryParameters(url);
        if (queryFormat) {
            this.addVulnerabilitytoResult(
                attack,
                this.vulnerabilityID,
                `Serialized Object Detected in Query Parameter: ${queryFormat}`
            );
        }
    }

    isSafe(name, value, headers = {}) {
        if (this.safePatterns.safeNames.some(safe => name.includes(safe))) {
            return true;
        }

        if (this.safePatterns.safeHeaders.some(header => headers[header.toLowerCase()])) {
            return true;
        }

        return false;
    }

    processHeaderValue(headerValue, headers = {}) {
        const parts = headerValue.split(';');
        for (const part of parts) {
            const [name, encodedValue] = part.split('=').map(s => s && s.trim());
            if (!encodedValue) continue;

            if (this.isSafe(name || '', encodedValue, headers)) {
                continue;
            }

            // Check content-type for Java serialization
            if (headers['content-type'] && headers['content-type'].includes('application/x-java-serialized-object')){
                return 'Java Serialized Object';
            }

            // Try URL decode
            let decodedValue = encodedValue;
            try {
                decodedValue = decodeURIComponent(encodedValue);
            } catch {
                decodedValue = encodedValue;
            }

            // Check for Java serialization
            if (this.detectJavaSerializedObject(decodedValue)) {
                return 'Java Serialized Object';
            }

            // Only attempt base64 decode if it looks like base64
            if (/^[A-Za-z0-9+/=]+$/.test(decodedValue)) {
                try {
                    const decoded = Buffer.from(decodedValue, 'base64').toString('binary');
                    if (this.detectSerializedObjects(decoded)) {
                        return this.detectSerializedObjects(decoded);
                    }
                } catch {
                    // Skip invalid base64
                }
            }

            const detectedFormat = this.detectSerializedObjects(decodedValue);
            if (detectedFormat) return detectedFormat;
        }
        return null;
    }

    checkSerializedObjectsInHeaders(attack, headers, headersToCheck, context) {
        for (const header of headersToCheck) {
            const headerValues = headers[header];
            if (!headerValues) continue;

            const values = Array.isArray(headerValues) ? headerValues : [headerValues];
            for (const value of values) {
                const detectedFormat = this.processHeaderValue(value, headers);
                if (detectedFormat) {
                    this.addVulnerabilitytoResult(
                        attack,
                        this.vulnerabilityID,
                        `${context} Header (${header}): ${detectedFormat}`
                    );
                    return true;
                }
            }
        }
        return false;
    }

    detectQueryParameters(url) {
        const queryParams = url.split('?')[1];
        if (!queryParams) return null;

        const keyValuePairs = queryParams.split('&');
        for (const pair of keyValuePairs) {
            const [key, value] = pair.split('=');
            const detectedFormat = this.detectSerializedObjects(key) || this.detectSerializedObjects(value);
            if (detectedFormat) return detectedFormat;
        }
        return null;
    }

    detectJavaSerializedObject(data) {
        if (!data || typeof data !== 'string') return false;

        // Check for raw Java serialization magic bytes
        if (data.includes('\xac\xed\x00\x05')) {
            return true;
        }

        // Check for base64 encoded Java serialization
        if (data.startsWith('rO0')) {
            try {
                const decoded = Buffer.from(data, 'base64').toString('binary');
                return decoded.includes('\xac\xed\x00\x05');
            } catch {
                return false;
            }
        }

        return false;
    }

    detectSerializedObjects(data) {
        if (!data) return null;

        // Java Serialized Object detection
        if (this.detectJavaSerializedObject(data)) {
            return 'Java Serialized Object';
        }

        // Python Pickle - more specific detection
        if (data.startsWith('\x80\x03') || data.startsWith('\x80\x04') || data.startsWith('\x80\x05')) {
            return 'Python Pickle Object';
        }

        // .NET - improved detection with more markers
        if (data.includes('System.Windows.Data.ObjectDataProvider') || 
            data.includes('System.Runtime.Serialization')) {
            return '.NET Serialized Object';
        }

        // PHP - improved pattern matching
        const phpMatch = /^O:\d+:"([a-zA-Z0-9_\\]+)":\d+:\{/.exec(data);
        if (phpMatch && phpMatch[1].length > 0) {
            return 'PHP Serialized Object';
        }

        return null;
    }

}


module.exports = SerializedObject
