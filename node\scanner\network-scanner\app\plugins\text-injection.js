const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

class TXTINJ extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-text-injection'
    }

    getAttackVectors() {
        return __AtVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'json-body', 'http-headers']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        if (parameterizedDeletage.getParameterType() == 'BaseURI') {
            parameterizedDeletage.setOptions({
                //addSlashBeforeAttack: false
                haveSlashAfterAttack: 'never'
            });
        }
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'Cookie', 'Content-Type']
            })
        }
    }

    wantProcessAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return false
        }

        let ResBody = _.get(attack, "result.resp.body")
        //server:cloudflare - Don't report vuln
        let serverdts = _.get(attack, 'result.resp.httpResponse.headers.server', '')
        if (/wastest\.indusface\.com/i.test(ResBody) && serverdts != 'cloudflare') {
            return true
        }
        return false
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        if (pluginDataForRequest.TxtInjFound) {
            return
        }
        let contentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')
        let possibleCType = /application\/xml|text\/html/i
        let ResBody = _.get(attack, "result.resp.body")

        if (possibleCType.test(contentType)) {
            let tagRegExp = /(h\d|p|strong|i|em|mark|small|del|ins|sub|sup|pre|Message|span|title|b)>[ \w%\/\(\)\.\=\;\'\-\:<>&#\[\]~\|]*?Site is moved to wastest\.indusface\.com kindly visit wastest\.indusface\.com\.[ \w\/\(\)\.\=\;\'\-\:<>&#\[\]~\|%]*?<\/\1/gi
            let matchedtagval = ResBody.match(tagRegExp)
            if (matchedtagval && matchedtagval.length > 0) {
                for (let val of matchedtagval) {
                    if (val.length > 75) {
                        let result = { "details": val }
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
                        pluginDataForRequest.TxtInjFound = true
                        return
                    }
                }
            }
            else {
                return
            }
        }
        else {
            if (/<\/div>/i.test(ResBody)) {
                let regexpskip = /<[ \w\/\(\)\.\=\;\'\-\:&#\[\]~\|%"$]+? Site is moved to wastest\.indusface\.com kindly visit wastest\.indusface\.com\..*?\/>/gi
                let matchedtagval = ResBody.match(regexpskip)
                if (matchedtagval && matchedtagval.length > 0) {
                    for (let val of matchedtagval) {
                        if (!/value=" Site is moved to wastest/i.test(val)) {
                            let result = { "details": val }
                            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
                            pluginDataForRequest.TxtInjFound = true
                            break
                        }
                    }
                }
            }
            else {
                let regexp = new RegExp(_.escapeRegExp(attack.vector))
                let vulnFound = this.checkBodyForVuln(attack, regexp, this.vulnerabilityID)
                if (vulnFound) {
                    pluginDataForRequest.TxtInjFound = true
                }
            }
        }
    }
}

const __AtVectors = [
    ` Site is moved to wastest.indusface.com kindly visit wastest.indusface.com. `,
    `Site is moved to wastest.indusface.com kindly visit wastest.indusface.com./`,


]

module.exports = TXTINJ