const NetworkAttack = require('./network-attack');
const { URL } = require('url');
const _ = require('lodash');
const HaikuUtils = require('../../../common/lib/haiku-utils');
const logger = require('../../../common/lib/haiku-logger')
class ApplicationChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config);
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-Application-Detection';
    }
    /**
     * @param  {object} attack the attack that was performed including http request+response
     * @override
     */
    processAttackResponse(attack) {
        try {
            let pluginDataForRequest = this.getPluginScopedStore(attack);
            if (pluginDataForRequest.servicesdetection) {
                return;
            }
        
            let servicesDetail = this.getUrlAnalysis(attack);
        
            
            if (servicesDetail) {
                // Extracting only "name" and "version" from technologies
                const technologiesDetails = servicesDetail.technologies.map(tech => ({
                    name: tech.name,
                    version: tech.version || "Null" // If version is not available, use "N/A"
                }));
                // Constructing the vulnerability description
                const vulnDetails = technologiesDetails.map(tech => `${tech.name}:${tech.version}`).join(", ");// Joining all the technologies with comma
                const vulnerabilityDescription = `${vulnDetails}`;
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnerabilityDescription);
                pluginDataForRequest.servicesdetection = true;
            }
        }
        catch(err) {
            logger.log('error', `processAttackResponse() error in ServiceChecker plugin: ${err.toString()}`, HaikuUtils.getMetadataForLog(attack));
        }
    }
}
module.exports = ApplicationChecker;
