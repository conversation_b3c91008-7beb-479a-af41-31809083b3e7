const VectorResponseAttack = require('./vector-response-attack')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const _ = require('lodash')

/**
 * Link Plugin Strategy:
 * Here for any url on attacking various areas with link payload, if we find iframe set in the 
 * response with our message displayed then will mark it as vulnerable. 
 */

class LinkAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // replace all ### in vectors with a random number
        // used to call HaikuUtils.getRandomInt(1000, 9999) but that screws up the missing vulns
        // storage since the vector is part of the key.
        this.randomNumber = HaikuUtils.getRandomInt(1000, 9999)
        this.vulnerabilityID = 'ID-link-injection'

        // fix up the attack vectors
        this.linkInjVectors = _linkInjVectors.map(s => s.replace(/###/g, this.randomNumber))
    }

    /**
     * get array of Link Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return this.linkInjVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params']
    }

    /**
     * Only attack header: Host, Origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override
     */
    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string
        if (parameterizedDeletage.getParameterType() == 'HTTPHeaders') {
            parameterizedDeletage.setOptions({
                headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
            })
        }
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return false
        }

        let contentTypeHeaderVal = _.get(attack, 'result.resp.httpResponse.headers["content-type"]', '')
        let possibleCType = /(application\/json|text\/(?:plain|xml))/i
        if (possibleCType.test(contentTypeHeaderVal)) {
            return false
        }

        // slight optimization - only process if 'haikumsg' found in the body
        let rawHtml = _.get(attack, 'result.resp.body')
        if (!/haikumsg/i.test(rawHtml)) {
            return false
        }
        return true
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    async processAttackResponse(attack) {
        //Plugin request scan scope - this scan, means to attack all request in current scope
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //If vuln found then return and don't report again
        if (pluginDataForRequest.vulnFound) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        // let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')

        try {
            // clickjacking to be only tested for either get/post method and status code not 400
            if (statusCode != 400 && (/get/i.test(attack.httpRequest.method) || /post/i.test(attack.httpRequest.method))) {
                /* let rawHtml = _.get(attack, 'result.resp.body')
                let regexp = new RegExp(_.escapeRegExp(attack.vector))
                if (regexp.test(rawHtml) || /'"><a\/href="http:\/+was\.indusface\.com">haikumsg</i.test(rawHtml)) {
                    let vulnFound = this.checkBodyForVuln(attack, /class='haikumsg'|>haikumsg</i, this.vulnerabilityID, {
                        maxMatchesToReturn: 1,
                        addVulnerabilitytoResult: false
                    })
                    if (vulnFound != null) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnFound)
                        pluginDataForRequest.vulnFound = true
                        return
                    }
                } */
                let $ = _.get(attack, 'result.resp.httpResponse.cheerio')
                let vulnFound = null

                //using cheerio to find <a> tag object, if injection was successfull, cheerio will find <a> tag object with classname attribute as haikumsg.
                if ($('a.haikumsg').length > 0) {
                    //Check if the specified attack vector
                    vulnFound = this.checkBodyForVuln(attack, /class='haikumsg'/i, this.vulnerabilityID, {
                        maxMatchesToReturn: 1,
                        addVulnerabilitytoResult: false
                    })

                    if (vulnFound != null) {
                        this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnFound)
                        pluginDataForRequest.vulnFound = true
                        return
                    }
                }
            }
        } catch (e) { return }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['was.indusface.com', 'haikumsg']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
    }
}
const tagsToCheck = [

    { tag: 'a', attr: 'class' }

]
const _linkInjVectors = [
    `'"-->'"></sCrIpT/x><a/href="http://was.indusface.com"/class='haikumsg'>haikutest</a/x>`,
    `'"-->'"><a href="http://was.indusface.com"/class='haikumsg'>`,
    `<a href="http://was.indusface.com/###" class='haikumsg'>clickme</a>`,
    `"><a href="http://was.indusface.com/###" class='haikumsg'>clickme</a><!--`,
    `"<a href="http://was.indusface.com/###" class='haikumsg'>clickme</a><!--`,
    `"></a><a href="http://was.indusface.com/###" class='haikumsg'>clickme</a><!--`,
    `<a href="http://was.indusface.com/###" class='haikumsg'>clickme</a><!--`,
    `"></a><a href="http://was.indusface.com/###" class='haikumsg'>clickme</a><!--`
]

module.exports = LinkAttack