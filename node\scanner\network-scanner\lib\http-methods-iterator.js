const _ = require('lodash')
const logger = require('../../common/lib/haiku-logger')
const HaikuUtils = require('../../common/lib/haiku-utils')
const ParameterizedDelegate = require('./parameterized-delegate')

const _ParameterType = "HTTPMethods"

// Delegate that can iterate HTTP methods and update method name
class HttpMethodsIterator extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }

    /**
     * @param {request} request the request whose methods we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize iterating requests.
     */
    constructor(request, scanStore, options) {
        super(request, scanStore, _ParameterType, options)
        this.method = _.cloneDeep(request.httpRequest.method)
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    * getIterator() {

        try {
            yield {
                name: 'method',
                val: this.method,
                resetRequired: false
            }
        } catch (err) {
            logger.log('error', `HTTP Method iterator - ${err.toString()}`, HaikuUtils.getMetadataForLog(this.getOriginalRequest()))
        }
    }

    modifyParam(param, value) {
        this.method = value
    }

    // get the modified request
    getHttpRequest() {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        this.setAdditionalHttpHeaders(req)
        req.method = this.method
        return req
    }

    // reset 
    reset() {
        this.method = _.cloneDeep(this.originalRequest.httpRequest.method)
    }
}

module.exports = HttpMethodsIterator