const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const SetCookie = require('set-cookie-parser');
// const ParseDomain = require('parse-domain')
const RegExpVari = require('./generic-regexp');

class <PERSON><PERSON><PERSON>hecker extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.httpOnlyVulnerabilityID = 'ID-cookie-httponly-not-set'
        this.secureVulnerabilityID = 'ID-cookie-secure-not-set'
        this.sessionCookieScopedToParentDomainVulnerabilityID = 'ID-session-cookie-scoped-parent-domain'
        this.cookiePathOverlyBroadVulnerabilityID = 'ID-broad-cookie-path'
        this.SameSiteCookieNotImplemented = 'ID-samesite-not-implemented'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            replaceValue: true,
            appendVector: false,
            encodings: ['raw']
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return cookieVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['cookie-params']
    }

    /* wantProcessAttackResponse(attack) {
        // let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
        let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
        if (AtkstatusCode >= 400) { return false } //Avoid FP
        let Recookie = _.get(attack, 'result.resp.httpResponse.redirects[0].headers["set-cookie"]', '')
        let fullCookie = _.get(attack, 'result.resp.httpResponse.headers.set-cookie', '')
        if (Recookie.length > 0 || fullCookie.length > 0) {
            return true
        }
        return false
    } */

    processAttackResponse(attack) {
        const fullCookie = [];
        fullCookie.push(...(_.get(attack, 'result.resp.httpResponse.headers.set-cookie', []) || []));
        fullCookie.push(...(_.get(attack, 'result.resp.httpResponse.redirects[0].headers["set-cookie"]', []) || []));
        if (fullCookie.length === 0) return;

        // const fullCookieString = fullCookie.join('\n'); // Convert to string if needed
        const resBody = _.get(attack, "result.resp.body", "");
        const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 0);
        const impRegExp = /(JSESSIONID|PHPSESSID|ASP\.NET_SessionId|sid|sessionid|auth_?Token|id_token|access_token|refresh_token|jwt|x-access-token|csrfToken|__RequestVerificationToken|XSRF-TOKEN|user_id|uid|profile_id|SSO_SESSIONID|sso_token|_?identity|OAuth_Token|paymentSessionId|secureToken|cartId|user_role|account_type|remember_?me|cart|secure|session|payment|basket|config|token|auth|sessid)/i;

        // Skip if no sensitive cookies are found
        if (!impRegExp.test(fullCookie)) {
            const errorMessages = [
                RegExpVari.RegExp.CustomErrMsg1,
                RegExpVari.RegExp.CustomErrMsg2,
                RegExpVari.RegExp.CustomErrMsg3,
                RegExpVari.RegExp.CustomErrMsg4,
                RegExpVari.RegExp.CustomErrMsg5
            ];

            // Skip processing if response body matches any custom error message
            if (errorMessages.some(regex => regex.test(resBody)) || statusCode >= 400) {
                return;
            }
        }

        const pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');
        const pluginDataForRequest = this.getPluginScopedStore(attack);

        if (pluginDataForRequest.httpOnlyVulnerabilityFound &&
            pluginDataForRequest.secureVulnerabilityFound &&
            pluginDataForRequest.sessionCookieScopeFound &&
            pluginDataForRequest.cookiePathOverlyBroadFound &&
            pluginDataForRequest.SamesiteVulnerabilityFound) {
            return;
        }

        // Parse cookies
        const cookies = SetCookie.parse(fullCookie, { decodeValues: true });
        const existingCookies = new Set(pluginStorageScanScope.cookieName ? pluginStorageScanScope.cookieName.split(',') : []);

        let httpOnlyMissing = [], secureMissing = [], domainScopeVulnerable = [], pathVulnerable = [], sameSiteMissing = [];
        // let cookieNames = new Set();
        let i = 0
        for (const cookie of cookies) {
            if (/^TS[\da-f]+$/i.test(cookie.name)) { i++; continue; } // Ignore F5 cookies

            const ckName = cookie.name;
            if (existingCookies.has(ckName)) { i++; continue; }
            existingCookies.add(ckName);

            if (!cookie.httpOnly && !pluginDataForRequest.httpOnlyVulnerabilityFound) {
                httpOnlyMissing.push({ fullCookie: fullCookie[i] });
            }
            if (attack.result.req.parsedURL.protocol === 'https:' && !cookie.secure && !pluginDataForRequest.secureVulnerabilityFound) {
                secureMissing.push({ fullCookie: fullCookie[i] });
            }
            if (cookie.domain && !pluginDataForRequest.sessionCookieScopeFound) {
                const attackParts = attack.hostname.split('.').filter(Boolean);
                const cookieParts = cookie.domain.split('.').filter(Boolean);

                if (attack.hostname !== cookie.domain &&
                    !cookie.domain.includes(attack.hostname) &&
                    attack.hostname.endsWith(cookie.domain) &&
                    cookieParts.length < attackParts.length) {
                    domainScopeVulnerable.push({ fullCookie: fullCookie[i] });
                }
            }
            if (!pluginDataForRequest.cookiePathOverlyBroadFound && (!cookie.path || cookie.path === '/')) {
                pathVulnerable.push({ fullCookie: fullCookie[i] });
            }
            if (!cookie.sameSite || !/None|Strict|Lax/i.test(cookie.sameSite) && !pluginDataForRequest.SamesiteVulnerabilityFound) {
                sameSiteMissing.push({ fullCookie: fullCookie[i] });
            }
            i++;
        }

        pluginStorageScanScope.cookieName = Array.from(existingCookies).join(',');
        if (httpOnlyMissing.length) this.httpOnlyVulnerabilityFound(attack, httpOnlyMissing, pluginDataForRequest);
        if (secureMissing.length) this.SecureVulnerabilityFound(attack, secureMissing, pluginDataForRequest);
        if (domainScopeVulnerable.length) this.sessionCookieScopeFound(attack, domainScopeVulnerable, pluginDataForRequest);
        if (pathVulnerable.length) this.PathVulnerabilityFound(attack, pathVulnerable, pluginDataForRequest);
        if (sameSiteMissing.length) this.SamesiteVulnerabilityFound(attack, sameSiteMissing, pluginDataForRequest);
    }

    httpOnlyVulnerabilityFound(attack, httpOnlyMissing, pluginDataForRequest) {
        let vulns = {
            details: httpOnlyMissing
        }
        this.addVulnerabilitytoResult(attack, this.httpOnlyVulnerabilityID, vulns)
        pluginDataForRequest.httpOnlyVulnerabilityFound = true
    }

    SecureVulnerabilityFound(attack, secureMissing, pluginDataForRequest) {
        let vulns = {
            details: secureMissing
        }
        this.addVulnerabilitytoResult(attack, this.secureVulnerabilityID, vulns)
        pluginDataForRequest.secureVulnerabilityFound = true
    }

    sessionCookieScopeFound(attack, domainScopeVulnerable, pluginDataForRequest) {
        let vulns = {
            details: domainScopeVulnerable
        }
        this.addVulnerabilitytoResult(attack, this.sessionCookieScopedToParentDomainVulnerabilityID, vulns)
        pluginDataForRequest.sessionCookieScopeFound = true
    }

    PathVulnerabilityFound(attack, pathCookieScope, pluginDataForRequest) {
        let vulns = {
            details: pathCookieScope
        }
        this.addVulnerabilitytoResult(attack, this.cookiePathOverlyBroadVulnerabilityID, vulns)
        pluginDataForRequest.cookiePathOverlyBroadFound = true
    }

    SamesiteVulnerabilityFound(attack, sameSiteMissing, pluginDataForRequest) {
        let vulns = {
            details: sameSiteMissing
        }
        this.addVulnerabilitytoResult(attack, this.SameSiteCookieNotImplemented, vulns)
        pluginDataForRequest.SamesiteVulnerabilityFound = true
    }
}
const cookieVectors = [
    ';'
]

module.exports = CookieChecker