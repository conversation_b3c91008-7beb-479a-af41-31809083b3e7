echo creating crawl session animated gif for scanlogId $1

echo downloading all tar.gz from S3
mkdir ./$1
# aws s3 sync --exclude '*' --include '*.gz' s3://haiku-store/crawler/screenshots/$1/ ./$1
cp /mnt/haiku/crawler/screenshots/$1/*.gz ./$1

echo extracting archive\(s\)
cd ./$1
find '.' -name '*.gz' -exec tar -xf {} \;
cd ..

echo creating animated gif
node create-crawl-session-gif.js $1
echo Uploaded the animated gif and consolidated metadata to S3

# delete local files
rm -f ./$1/crawler-screenshot*.png ./$1/crawler-screenshot*.json ./$1/screenshot*.gz
rmdir $1
  