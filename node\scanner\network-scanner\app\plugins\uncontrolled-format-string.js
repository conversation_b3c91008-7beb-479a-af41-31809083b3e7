const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Uncontrolled Format String Plugin:
 * Here we will inject the query params and the post body with various format strings such as %s, and observe for
 * any error or stack values or segmentation faults/software crashes or executed arbitrary code on server 
 * based on which we will report the vulnerability
 */

class UncontronlledFormatString extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-uncontrolled-format-string'

        this.excludeURLRegex = /\/(?:phpinfo|info)\.php|\/(?:blog|docs?|learning|readme|changelog|license|contributing|api-docs?|documentation|help|support|guide|tutorial|faq)\/|\.(?:txt|md|rst|pdf)$/i

        // Common format string vulnerability indicators
        this.vulnIndicators = [
            // 🔹 C/C++ Errors
            /Error: Invalid format specifier detected in code execution/i,
            /Warning: Format string vulnerability has been detected in application/i,
            /Format string error: %n not allowed in this context/i,
            /printf family function error occurred during execution/i,
            /fprintf error detected during string formatting operation/i,
            /sprintf failure occurred while processing format string/i,
            /Segmentation fault detected during program execution/i,
            /Stack trace: Program terminated due to format string error/i,
            /Bus error occurred during format string processing/i,
            /Floating point exception in format string handling/i,
            /Illegal instruction detected in format string processing/i,
            /Access violation occurred during format string execution/i,
            /Null pointer dereference in format string handling/i,
            /Abort trapped during format string processing operation/i,
            /Double free or corruption detected in memory allocation/i,
            /Heap corruption detected during format string processing/i,
            /Malloc consolidate error occurred during memory allocation/i,
            /Free error: invalid pointer detected during memory operation/i,
            /\s0[xX][0-9a-fA-F]{8,16}\s+memory address leak detected/i,
            /Memory address leak: 0[xX][0-9a-fA-F]+ detected in response/i,
            /Error: %n not allowed in format string for security reasons/i,
            /Invalid use of %s in format string causing application crash/i,
            /Format string contains multiple %x%x%x causing memory leak/i,
            /Format string injection detected in application response/i,
            /format string not valid for the current operation context/i, // Format string errors
            /buffer overflow detected during format string processing/i,   // Buffer overflow indicators

            // 🔹 Python Errors
            /ValueError: unsupported format string detected in code execution/i,
            /TypeError: not enough arguments for format string provided/i,
            /TypeError: format requires a mapping object for string formatting/i,
            /File.*line.*in format string processing operation failed/i,
            /File.*line.*in str.format operation caused application error/i,
            /File.*line.*in f-string evaluation caused program crash/i,
            /TypeError: %.*format.*a number but received invalid input/i,
            /KeyError: format key not found in the provided dictionary/i,
            /Python segmentation fault occurred during string formatting/i,
            /Fatal Python error: malloc failed during memory allocation/i,
            /Fatal Python error: free(): invalid pointer detected in memory/i,

            // 🔹 PHP Errors
            /Warning: printf\(\) expects parameter for format string operation/i,
            /Warning: sprintf\(\) expects parameter for string formatting/i,
            /Fatal error: Uncaught TypeError: vsprintf operation failed/i,
            /Error: Call to undefined function printf in this context/i,
            /Error: Call to undefined function sprintf in this context/i,
            /Fatal error: Allowed memory size exhausted during formatting/i,
            /PHP Fatal error: Segmentation fault in format string processing/i,
            /PHP Warning: Stack smashing detected during string operation/i,

            // 🔹 Java Errors
            /java\.util\.UnknownFormatConversionException in string formatting/i,
            /java\.util\.IllegalFormatConversionException during operation/i,
            /java\.util\.MissingFormatArgumentException in string processing/i,
            /java\.util\.IllegalFormatException occurred during formatting/i,
            /java\.lang\.NullPointerException in format string processing/i,
            /java\.lang\.NumberFormatException in format string operation/i,
            /java\.lang\.IllegalArgumentException in format string context/i,
            /Exception in thread.*at java\.util\.Formatter processing failed/i,

            // 🔹 Ruby Errors
            /ArgumentError: invalid format specification detected in code/i,
            /ArgumentError: too many arguments for format string provided/i,
            /ArgumentError: too few arguments for format string provided/i,
            /TypeError: no implicit conversion of.*into String for formatting/i,
            /Error: unrecognized format specifier in string operation/i,
            /Traceback \(most recent call last\):.*in `sprintf` operation failed/i,
            /Traceback \(most recent call last\):.*in `printf` operation failed/i,

            // 🔹 JavaScript Errors
            /TypeError: format requires a string but received invalid input/i,
            /TypeError: Cannot read properties of undefined \(reading 'format'\)/i,
            /Error: Missing arguments for format string in this context/i,
            /Error: Invalid format string parameter provided for operation/i,
            /SyntaxError: Unexpected format specifier in string processing/i,
            /ReferenceError: format is not defined in the current scope/i
        ];

        // Quick precheck patterns for common error indicators
        this.precheckPatterns = /error|warning|printf|Segmentation|format string|memory|crash|\bjava\./i
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            attackParamName: false
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return formatStringVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'form-encoded-post', 'json-body', 'uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        if (this.excludeURLRegex.test(attack.httpRequest.uri)) return false;

        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.UncontrolledFormatStringFound) return false;

        return await super.performNetworkAttack(attack)
    }

    /**
     * Quick precheck to see if response might contain vulnerability indicators
     * @param {string} responseBody The response body to check
     * @returns {boolean} True if response might contain vulnerability indicators
     */
    precheck(responseBody) {
        // Skip MD5 algorithm false positives, and common hex patterns
        if (/lX\d = \(lX & 0x\d+\)|0x[0-9a-fA-F]{8}\b/i.test(responseBody)) {
            return false
        }

        // Quick check for common error indicators
        if (this.precheckPatterns.test(responseBody)) {
            return true
        }
        return false
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        //Return of attack not made by this plugin
        if (attack.pluginName != this.getName()) {
            return
        }
        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.UncontrolledFormatStringFound) return;

        const responseBody = _.get(attack, "result.resp.body", "0").trim()
        if (responseBody.length == 0 || responseBody.length < 10) return;

        // Quick precheck before doing full vulnerability check
        if (!this.precheck(responseBody)) {
            return
        }

        // Check for vulnerability indicators in response body
        for (const indicator of this.vulnIndicators) {
            const match = responseBody.match(indicator)
            const matchText = match ? match[0] : null
            if (matchText) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `Format string vulnerability detected. Error: ${matchText}`)
                pluginDataForRequest.UncontrolledFormatStringFound = true;
                return
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        let vulndts = _.get(attack, `result.vulns.${vulnID}.details`, null);

        if (vulndts && vulndts.length > 0) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `param`, [attack.param]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers.Cookie', `text`, [vulndts[0]]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        }
    }
}

const formatStringVectors = [
    // Basic format specifiers that commonly trigger errors
    "%s",                           // Simple string format - often triggers missing argument errors
    "%x",                           // Hex format - can leak memory addresses
    "%n",                           // Dangerous format specifier - often blocked/triggers errors
    "%p",                           // Pointer format - can leak memory addresses
    "%d",                           // Decimal format - common in error messages

    // Common error-triggering patterns
    "%s%s%s%s%s%s%s%s%s%s",        // Multiple %s - triggers argument count errors
    "%x%x%x%x%x%x%x%x%x%x",        // Multiple %x - triggers memory leak errors
    "%n%n%n%n%n%n%n%n%n%n",        // Multiple %n - triggers security errors
    "%p%p%p%p%p%p%p%p%p%p",        // Multiple %p - triggers memory leak errors

    // Real-world error scenarios
    "Error: %s",                    // Common error message pattern
    "Warning: %s",                  // Common warning pattern
    "Exception: %s",                // Common exception pattern
    "Invalid format: %s",           // Format validation error
    "Missing argument: %s",         // Missing argument error

    // Dangerous combinations
    "%s%n%s%n%s%n",                // Mix of safe and dangerous specifiers
    "%x%n%x%n%x%n",                // Mix of memory leak and dangerous specifiers
    "%p%n%p%n%p%n",                // Mix of pointer leak and dangerous specifiers

    // Stack-based attacks
    "%x.%x.%x.%x.%x",              // Stack dump pattern
    "%08x.%08x.%08x.%08x",         // Padded stack dump pattern
    "%p.%p.%p.%p.%p",              // Pointer-based stack dump

    // Buffer overflow triggers
    "%1000s",                       // Large width specifier
    "%.1000s",                      // Large precision specifier
    "%1000x",                       // Large hex width
    "%1000d",                       // Large decimal width

    // Position-based attacks
    "%1$s",                         // Positional parameter
    "%2$s",                         // Second positional parameter
    "%3$s",                         // Third positional parameter
    "%1$n",                         // Positional write
    "%2$n",                         // Second positional write

    // Complex patterns
    "%*.*s",                        // Width and precision from arguments
    "%*.*d",                        // Width and precision for numbers
    "%*.*x",                        // Width and precision for hex
    "%*.*n",                        // Width and precision for write
    "%*.*p"                         // Width and precision for pointers
]

module.exports = UncontronlledFormatString