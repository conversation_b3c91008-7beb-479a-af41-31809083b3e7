const debug = require('debug')('UrlEncodedPostSplitter');
const querystring = require('querystring');
const BasePlugin = require('../base-plugin');
const _ = require('lodash');
const logger = require('../../../../common/lib/haiku-logger');
const HaikuUtils = require('../../../../common/lib/haiku-utils');
const FileUploadDelegate = require('../../../lib/file-upload-delegate');

class FileUploadPostSplitter extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the network scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config);
    }

    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http headers
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area');
            if (originalRequest.revalidationInfo && FileUploadDelegate.ParameterType != attackArea) {
                logger.log('info', `file upload - not an interesting request since request being revalidated is attacking '${attackArea}'`, HaikuUtils.getMetadataForLog(originalRequest));
                return;
            }
        }

        // only process successful requests
        let httpRequest = originalRequest.httpRequest;
        if (originalRequest.httpResponse.err || !FileUploadDelegate.isFileUploadRequest(httpRequest)) {
            logger.log('info', `file upload - not an interesting request ${originalRequest.httpRequest.method} err=${originalRequest.httpResponse.err}`, HaikuUtils.getMetadataForLog(originalRequest));
            return;
        }

        // Create object that can iterate and manipulate params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan');
        let options = this.getMetadata(originalRequest).options;
        let createFileUploadDelegate = function () {
            return new FileUploadDelegate(originalRequest, scanStore, options);
        };
        this.networkScanner.emit('file-upload', createFileUploadDelegate);
    }
}

module.exports = FileUploadPostSplitter;