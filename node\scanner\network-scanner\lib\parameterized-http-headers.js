const _ = require('lodash')
const caseless = require('caseless');
const logger = require('../../common/lib/haiku-logger')
const HaikuUtils = require('../../common/lib/haiku-utils')
const ParameterizedDelegate = require('./parameterized-delegate')

const _ParameterType = "HTTPHeaders"

// Delegate that can iterate HTTP headers and update header values
class ParameterizedHttpHeaders extends ParameterizedDelegate {
    /**
     * Parameter type for this delegate
     */
    static get ParameterType() {
        return _ParameterType
    }
    
    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore, options) {
        super(request, scanStore, _ParameterType,options)
        this.headers = _.cloneDeep(request.httpRequest.headers) 
        this.caselessHeader = caseless(this.headers)
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings
    }

    * getIterator() {
        // starting iteration, keep track of original post body.
        for (let headerName of this.options.headersToIterate) {
            try {
                yield {
                    name: headerName,
                    val: this.caselessHeader.get(headerName) || '',
                    resetRequired: false
                }
            } catch (err) {
                logger.log('error', `HTTP Header iterator - ${err.toString()}`, HaikuUtils.getMetadataForLog(this.getOriginalRequest()))
            }
        }
    }

    modifyParam(param, value) {
        this.caselessHeader.set(param,value)
    }

    // get the modified request
    getHttpRequest() {
        let req = _.cloneDeep(this.originalRequest.httpRequest)
        req.headers  = {}
        this.setAdditionalHttpHeaders(req)
        Object.assign(req.headers, this.headers)        
        return req
    }

    // reset 
    reset() {
        this.headers = _.cloneDeep(this.originalRequest.httpRequest.headers)
        this.caselessHeader = caseless(this.headers)
    }
}

module.exports = ParameterizedHttpHeaders