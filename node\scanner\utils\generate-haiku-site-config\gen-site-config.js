// Javascript for gen-site-config.html
let testRecipe = [{
        "name": "dont attack cookie and origin headers",
        "scope": "plugin",
        "match": "return /Protocol: HTTP Headers/.test(pluginName)", // body of function (pluginName, plugin){ }, return bool. called for each plugin
        "edit": "plugin.options.headersToIterate = plugin.options.headersToIterate.filter( x => !['Cookie', 'Origin'].includes(x))", // body of function (plugin, pluginName){ }, do any edit necc. Called only when above search is true
    },
    {
        "name": "sQL Injection - don't report DB Error",
        "scope": "vulnerability",
        "match": "return vulnName=='ID-db-error-disclosure'", // body of function (vuln, vulnName, pluginName, plugin){ }, return bool, called for each vuln Id
        "edit": "vuln.reportVulnerability = false", // body of function (vuln, vulnName, pluginName, plugin){ }, return bool, called for each vuln Id
    },
    {
        "name": "max parallel requests is 7",
        "scope": "HTTPRequestMonster",
        "edit": "settings.perDomainMaxParallelRequests = 7", // body of function (settings){ }, do any edit necc. 
    }
]

// The editor variables
let recipeEditor
let siteSpecificEditor
let configChangesEditor
let defaultConfigEditor

populatedDefaultConfig = false

function openTab(evt, tabName) {
    jQuery(".content-tab").hide()
    jQuery(".tab").removeClass('is-active')
    jQuery('#' + tabName).show()
    jQuery(evt.currentTarget).addClass("is-active")
    if (!populatedDefaultConfig && tabName == 'default-config-tab') {
        populatedDefaultConfig = true
        // populate the default config
        getDefaultConfig()
    }
}

jQuery(function () {
    //add handler for click
    jQuery('#generate').click(function () {
        try {
            let recipe = JSON.parse(recipeEditor.getValue())
            jQuery.post('/utils/generateSiteConfig', {
                recipe: JSON.stringify(recipe)
            }).done((result) => {
                if (result && result.success) {
                    processSuccess(result)
                } else {
                    processFailure(result)
                }
            }).fail((jqxhr, textStatus, error) => {
                processFailure({
                    status: textStatus,
                    error
                })
            })
        } catch (err) {
            processFailure({
                status: 'bad recipe JSON',
                error: err.toString()
            })
        }
    })
})

// create JSON editor 
require.config({
    paths: {
        'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.20.0/min/vs'
    }
});

require(['vs/editor/editor.main'], function () {
    recipeEditor = monaco.editor.create(document.getElementById('edit-container'), {
        value: JSON.stringify(testRecipe, null, 2),
        language: 'json',
        fontSize: 12
    });
});

require(['vs/editor/editor.main'], function () {
    siteSpecificEditor = monaco.editor.create(document.getElementById('config-container'), {
        value: '{"msg":"click generate to see ..."}',
        language: 'json',
        fontSize: 12,
        readOnly: true
    });
});

require(['vs/editor/editor.main'], function () {
    configChangesEditor = monaco.editor.create(document.getElementById('config-changes-container'), {
        value: '{"msg":"click generate to see ..."}',
        language: 'json',
        fontSize: 12,
        readOnly: true
    });
});

function processSuccess(result) {
    // site config
    setEditorValueAndFormatForRO(siteSpecificEditor, JSON.stringify(result.siteConfig))
    jQuery('#site-config-accordion').addClass('is-active is-success')
    jQuery('#site-config-accordion').removeClass('is-danger')

    // diff
    setEditorValueAndFormatForRO(configChangesEditor, JSON.stringify(result.diff))
    jQuery('#site-diff-accordion').addClass('is-success')
    jQuery('#site-diff-accordion').removeClass('is-danger')
}

function processFailure(result) {
    // site config
    setEditorValueAndFormatForRO(siteSpecificEditor, JSON.stringify(result))
    jQuery('#site-config-accordion').addClass('is-active is-danger')
    jQuery('#site-config-accordion').removeClass('is-success')

    // diff
    setEditorValueAndFormatForRO(configChangesEditor, JSON.stringify(result))
    jQuery('#site-diff-accordion').addClass('is-danger')
    jQuery('#site-diff-accordion').removeClass('is-success')
}

function setEditorValueAndFormatForRO(editor, result) {
    editor.updateOptions({
        readOnly: false
    })
    editor.setValue(result)
    editor.getAction('editor.action.formatDocument').run().then(() => editor.updateOptions({
        readOnly: true
    }))
    editor.layout()
}

function getDefaultConfig() {
    try {
        require(['vs/editor/editor.main'], function () {
            defaultConfigEditor = monaco.editor.create(document.getElementById('default-config-container'), {
                value: '{"msg":"loading..."}',
                language: 'javascript',
                fontSize: 12,
                readOnly: true
            });
        });
                
        jQuery.get('/utils/getDefaultConfig').done((result) => {
            if (result) {
                setEditorValueAndFormatForRO(defaultConfigEditor, result)
            }
        }).fail((jqxhr, textStatus, error) => {
            result = {
                status: textStatus,
                error
            }
            setEditorValueAndFormatForRO(defaultConfigEditor, result)

        })
    } catch (err) {
        result = {
            status: 'error getting default config',
            error: err.toString()
        }
        setEditorValueAndFormatForRO(defaultConfigEditor, result)
    }
}