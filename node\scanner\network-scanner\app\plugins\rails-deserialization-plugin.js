const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * Rails Deserialization Plugin Strategy:
 * Here for any url on sending 3 post request with specific payloads and comparing there
 * responses, if conditional check are found true then consider it as vulnerable
 * 
 * References: 
 * https://github.com/michenriksen/nmap-scripts/blob/master/http-rails-xml-parser.nse
 * https://blog.rapid7.com/2013/01/10/exploiting-ruby-on-rails-with-metasploit-cve-2013-0156/
 * 
 */
class railsDeserialization extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-rails-deserialization-found'
    }

    getAttackVectors() {
        return railsBodyVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'post-body']
    }

    /**
     * forms attack request and performs network attack
     * @param {attack} new attack received from crawler
     * @override
     */
    async performNetworkAttack(attack) {
        let serverHeader1 = _.get(attack, 'result.resp.httpResponse.headers["server"]');
        let serverHeader2 = _.get(attack, 'originalRequest.httpResponse.headers.server')

        attack.httpRequest.method = "POST"
        attack.httpRequest.headers['content-type'] = "application/xml"

        if (/apache|nginx|thin|webrick/i.test(serverHeader1) ||
            /apache|nginx|thin|webrick/i.test(serverHeader2)) {

            let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')

            if (!pluginStorage.attackRespones) {
                pluginStorage.attackRespones = []
            }

            return await super.performNetworkAttack(attack)
        }
        return false;
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */

    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        // get plugin storage
        let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorage.railsDeserialization) {
            return
        }

        // add response to end of array
        pluginStorage.attackRespones.push(attack.result.resp.httpResponse.statusCode)

        /***
         * Here we are comparing response codes for all 3 network attack as per body vectors, conditional 
         * check is if normal_payload != invalid_payload && valid_payload != invalid_payload &&
         * invalid_payload == "500" then only report the vulnerability
         */
        if (pluginStorage.attackRespones[0] != pluginStorage.attackRespones[2] &&
            pluginStorage.attackRespones[1] != pluginStorage.attackRespones[2] &&
            pluginStorage.attackRespones[2] == 500) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)

            return
        }
        pluginStorage.railsDeserialization = true
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;

        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, ['method']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.headers', `param`, ['Content-Type']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.body', `text`, [attack.param, attack.paramVal], `orignal request body ${attack.param} param value will be attacked`);
        
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ['server']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);        
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.body', `text`, [attack.param, encodeURIComponent(attack.vector)], `attack request body ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);        
        

    }
}

const railsBodyVectors = [
    `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<probe type=\"string\"><![CDATA[\nhello\n]]></probe>`,
    `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<probe type=\"yaml\"><![CDATA[\n--- !ruby/object:Time {}\n\n]]></probe>`,
    `<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<probe type=\"yaml\"><![CDATA[\n--- !ruby/object:\0\n]]></probe>`
]

module.exports = railsDeserialization