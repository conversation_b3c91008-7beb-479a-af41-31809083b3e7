const querystring = require('querystring');
const _ = require('lodash');
const path = require('path');
const s3Utils = require('../../common/lib/s3-utils');
const crypto = require('crypto');

const ParameterizedDelegate = require('./parameterized-delegate')
const _ParameterType = 'FileUpload'


class FileUploadDelegate extends ParameterizedDelegate {
    /**
     * @param {request} request the request whose headers we are iterating, tampering
     * @param {object} scanstore scan scoped store to optimize interating requests eg. dont iterate same URI path more than once.
     */
    constructor(request, scanStore, options) {
        // expect caller to have verified everything already since this is a helper class
        super(request, scanStore, _ParameterType, options);
        this.postBody = querystring.parse(request.httpRequest.body);
        this.originalPostbody = _.cloneDeep(this.postBody);
        this.parsedData = _.get(request.httpRequest, 'parsedData');
        this.currentParamName = '';
        this.curretParamValue = '';

        if(this.parsedData) {
            this.originalParsedData = _.cloneDeep(this.parsedData);
        }
    }

    static get ParameterType() {
        return _ParameterType;
    }

    /**
     * Returns true if the request is a file upload request, false otherwise
     * @param {Object} httpRequest haiku http request object
     * @returns {Boolean} true if the request is a file upload request, false otherwise
     */
    static isFileUploadRequest(httpRequest) {
        //check for content type with multipart/form-data
        if (httpRequest.method == 'POST' &&
            (_.get(httpRequest, "headers['Content-Type']", '').includes('multipart/form-data') ||
                _.get(httpRequest, "headers['content-type']", '').includes('multipart/form-data'))) {
            let parsedData = _.get(httpRequest, 'parsedData');

            if (parsedData && parsedData.parsed) {
                //Check if there are any files in the request
                if (parsedData.files && Object.keys(parsedData.files).length > 0) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * get encodings suported by this type of delegate
     * @override
     */
    getEncodings() {
        return this.options.encodings;
    }

    * getIterator() {
        // yield each file in the request
        let files = this.parsedData.files;

        for (let file in files) {
            yield {
                name: file,
                val: files[file].filename,
                resetRequired: false
            }
        }
    }

    modifyParam(param, value) {
        if (this.attackType == 'name') {
            // attack parameter name so param & value meanings are reversed
            this.currentParamName = value;
        } else {
            this.currentParamName = param;
            this.currentParamValue = value;
        }
    }

    // get the modified request
    async getHttpRequest(encoding) {
        let req = _.cloneDeep(this.originalRequest.httpRequest);
        this.setAdditionalHttpHeaders(req);
        let options = {};
        if (encoding == 'raw') {
            options = {
                encodeURIComponent: uri => uri
            }
        }

        let filePath = this.currentParamValue;
        
        // Read the file from the given location
        let fileContent = await s3Utils.getFile(path.dirname(filePath), path.basename(filePath));

        // Generate a new boundary
        let newBoundary = this.parsedData.boundary; //Use existing boundary

        // Reassemble the multipart data with the new boundary
        let body = '';

        for (let [name, value] of Object.entries(this.parsedData.fields)) {
            body += `--${newBoundary}\r\n`;
            body += `Content-Disposition: form-data; name="${name}"\r\n\r\n`;
            body += `${value}\r\n`;
        }

        for (let [name, files] of Object.entries(this.parsedData.files)) {
            if(name == this.currentParamName) {
                body += `--${newBoundary}\r\n`;
                body += `Content-Disposition: form-data; name="${this.currentParamName}"; filename="${path.basename(filePath)}"\r\n`;
                body += `Content-Type: ${files.contentType}\r\n\r\n`;
                body += fileContent.Body;
                body += `\r\n`;
            }
            else {
                body += Buffer.from(files.bytes);
                body += `\r\n`;
            }
        }    

        body += `--${newBoundary}--\r\n`;
        req.headers['Content-Type'] = `multipart/form-data; boundary=${newBoundary}`;
        req.body = body;            


        req.body = body;
        
        return req
    }

    // reset post body
    reset() {
        this.postBody = _.cloneDeep(this.originalPostbody);
        this.parsedData = _.cloneDeep(this.originalParsedData);
    }
}

module.exports = FileUploadDelegate