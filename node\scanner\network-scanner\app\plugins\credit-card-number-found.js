const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class CreditCardNumberFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-credit-card-number-found'

        /** Below are the pattern of different card vendors
         * as |mastercard|visa|amex|discover|jcb|diners|rupay
         * get sample from https://regex101.com/r/cKVvGy/4
         **/
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(originalRequest) {

        // check if below condition are met, then only call processAttackResponse
        // Only to check vulnerability for original crawler request made and none other
        if (originalRequest.attackArea == "original-crawler-request") {
            return true
        }
        return false
    }

    /**
     * @param {attack} originalRequest the attack that was performed
     * @override
     */
    processAttackResponse(originalRequest) {
        let body = _.get(originalRequest, 'result.resp.body')
        if (!body) {
            return
        }
        let cardNumber_M = /[\s,"'](5[1-5]\d{2}\-?\d{4}\-?\d{2}\-?\d{2}\-?\d{4})[\s,"']/.exec(body)
        let cardNumber_V = /[\s,"'](4\d{3}\-?\d{4}\-?\d{2}\-?\d{2}\-?\d(?:\d{3})?)[\s,"']/.exec(body)
        let cardNumber_A = /[\s,"'](3[47]\d{2}\-?\d{4}\-?\d{2}\-?\d{2}\-?\d{3})[\s,"']/.exec(body)
        let cardNumber_D = /[\s,"'](65[4-9][0-9]{13}|64[4-9][0-9]{13}|6011[0-9]{12}|(622(?:12[6-9]|1[3-9][0-9]|[2-8][0-9][0-9]|9[01][0-9]|92[0-5])[0-9]{10}))[\s,"']/.exec(body)
        let cardNumber_J = /[\s,"']((3\d{3}\-?\d{4}\-?\d{2}\-?\d{2}\-?\d{4}|(?:1800|21(?:31|00))\-?\d{4}\-?\d{2}\-?\d{2}\-?\d{3}))[\s,"']/.exec(body)
        let cardNumber_Di = /[\s,"'](3(?:0[0-5]|[68][0-9])[0-9]{11})[\s,"']/.exec(body)
        let cardNumber_R = /[\s,"']((60\d{2}|652\d{1})\-?(\d{4}|\d{3})\-?\d{2}\-?\d{2}\-?\d{4})[\s,"']/.exec(body)

        //Below conditional check is for MasterCard
        if (cardNumber_M != undefined && cardNumber_M.length > 0) {
            this.checkVuln("MasterCard", cardNumber_M, originalRequest)
            return
        }

        //Below conditional check is for Visa
        if (cardNumber_V != undefined && cardNumber_V.length > 0) {
            this.checkVuln("Visa", cardNumber_V, originalRequest)
            return

        }

        //Below conditional check is for Amex
        if (cardNumber_A != undefined && cardNumber_A.length > 0) {
            this.checkVuln("Amex", cardNumber_A, originalRequest)
            return
        }

        //Below conditional check is for Discover
        // old regex - /[\s|"|'|,](6(?:011|5\d{2})\-?\d{4}\-?\d{2}\-?\d{2}\-?\d{4})[\s|"|'|,]/
        if (cardNumber_D != undefined && cardNumber_D.length > 0) {
            this.checkVuln("Discover", cardNumber_D, originalRequest)
            return
        }

        //Below conditional check is for JCB
        if (cardNumber_J != undefined && cardNumber_J.length > 0) {
            this.checkVuln("JCB", cardNumber_J, originalRequest)
            return
        }

        //Below conditional check is for Diners & Diners Club
        if (cardNumber_Di != undefined && cardNumber_Di.length > 0) {
            this.checkVuln("Diners", cardNumber_Di, originalRequest)
            return
        }

        //Below conditional check is for Rupay
        // old regex - [\s|"|'|,]((60\d{2}|652\d{1})\-?(\d{4}|\d{3})\-?\d{2}\-?\d{2}\-?\d{4})[\s|"|'|,]
        if (cardNumber_R != undefined && cardNumber_R.length > 0) {
            this.checkVuln("Rupay", cardNumber_R, originalRequest)
            return
        }

    }

    checkVuln(brand, cardNumber, originalRequest) {
        let details = []
        let vuln
        cardNumber = _.uniq(cardNumber)
        details.push({
            Card_Brand: brand,
            Card_Number: cardNumber
        })
        vuln = {
            details: details
        }
        this.addVulnerabilitytoResult(originalRequest, this.vulnerabilityID, vuln)
        return
    }
    
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        let details = _.get(attack, `result.vulns.${vulnID}.details`, null);

        if (details.length && details.length > 0) {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['credit', 'card', details[0].Card_Brand, details[0].Card_Number]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);           
        }
    }
}

module.exports = CreditCardNumberFound