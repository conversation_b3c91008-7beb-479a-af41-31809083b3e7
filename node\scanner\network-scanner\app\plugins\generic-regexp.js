// The following generic variable is created for use in many plugins
// exporting variables and function

module.exports = {
    ErrorMessages: {
        // HTTP Status and Common Errors
        HttpErrors: [
            'http status 404',
            '404 - file or directory not found',
            '404 - not found',
            'the page you are looking for was not found',
            'page doesn\'t exit',
            'page you are looking for doesn\'t exist',
            'requested page is not available',
            'url is not found',
            'url not found',
            'the path could not be found'
        ],

        // Access Control and Security Messages
        SecurityErrors: [
            'access denied',
            'you do not have access',
            'invalid access',
            'not authorized person to access this link',
            'entry to this site is restricted',
            'forbidden',
            'you have been blocked',
            'you are unable to access this website',
            'this website is secured against online attacks',
            'your request was blocked due to suspicious behavior',
            'access this page is not allowed'
        ],

        // Session and Authentication Errors
        SessionErrors: [
            'session expired',
            'your session has been expired',
            'please login again',
            'try logging in again',
            'session does not exist',
            '{"status":"session_logout"}',
            'you must enter your name and email address correctly'
        ],

        // System and Technical Errors
        SystemErrors: [
            'experiencing technical problems',
            'internal error occured',
            'cannot connect to the database',
            'the system is currently under maintenance',
            'we are unable to display the requested page',
            'there is some problem loading this page',
            'error 503 - service unavailable',
            'we apologize for your inconvenience'
        ],

        // WAF and Security Tool Messages
        WafErrors: [
            'this error was generated by mod_security',
            'mod_security rules triggered',
            'webknight application firewall alert',
            'aqtronix webknight',
            'sucuri website firewall',
            'cloudflare ray id',
            'ddos protection by cloudflare',
            '_incapsula_resource'
        ],

        // General Error Messages
        GeneralErrors: [
            'error has occurred',
            'something went wrong',
            'please try again later',
            'we encountered a problem',
            'error occured, kindly try again',
            'cannot process your request',
            'invalid request',
            'sorry some error occurred',
            'some other error occured'
        ]
    },

    // WAF and Security Server Names to detect in headers
    WafServers: [
        'cloudflare',
        'akamai',
        'incapsula',
        'sucuri',
        'webknight',
        'aqtronix',
        'mod_security'
    ]
}