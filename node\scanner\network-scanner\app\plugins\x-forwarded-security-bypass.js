const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')


class xForwardedSecurityBypass extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.xForwxForwardedSecurityBypass = 'ID-x-forwarded-security-bypass'

    }

    getAttackVectors() {
        return _attackVectors
    }

    /** 
     * Only attack header: Refer<PERSON>, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override    
     */
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            headersToIterate: ['X-Forwarded-For', 'Referer', 'User-Agent', 'Origin', 'Cookie', 'Accept-Language']
        })
    }

    getAttackableEvents() {
        return ['http-headers']
    }


    async performNetworkAttack(attack) {

        let xforwardedhostPresent = _.get(attack, 'result.resp.httpResponse.headers[X-Forwarded-Host]')

        if (!xforwardedhostPresent) {

            attack.httpRequest.headers['X-Forwarded-For'] = ip_to_add.join(", ")

        }

        return await super.performNetworkAttack(attack)
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    wantProcessAttackResponse(attack) {

        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }

        //checking reflected vector in response
        let currentstatuscode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let originalstatuscode = _.get(attack, 'originalRequest.httpResponse.statusCode')

        let staCode = [200]
        let staCode2 = [403]

        let evaluate = staCode.includes(currentstatuscode) && staCode2.includes(originalstatuscode)

        if (evaluate) {
            return true
        }

        return false


    }

    processAttackResponse(attack) {


        let details = { "vulnerability": "exist" }
        this.addVulnerabilitytoResult(attack, this.xForwxForwardedSecurityBypass, details)

        //cache control header to be used for web cache poisoning


    }
}

const ip_to_add = [
    "***********",
    "localhost",
    "127.0.0.1",
    "***********",
    "***********"
]
//random_header = headers_to_add[Math.floor(Math.random() * headers_to_add.length)];
/**
 * .... These are the tags and it's attributes where attributes value will be changed after sending the attack. 
 * More can be added in the list as necessary.
 */


const _attackVectors = [
    `127.0.0.1`
]


module.exports = xForwardedSecurityBypass