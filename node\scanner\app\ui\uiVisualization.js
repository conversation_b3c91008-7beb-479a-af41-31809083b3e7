// This file is preloaded using webPreferences and will
// be executed in the renderer process for that window.
// All of the Node.js APIs are available in this process.
//
// Ensure that other frameworks can work correctly. We will always use node* in our code...
// https://electron.atom.io/docs/faq/#i-can-not-use-jqueryrequirejsmeteorangularjs-in-electron
window.nodeRequire = require;
window.nodeExports = exports;
window.nodeModule = module;
delete window.require;
delete window.exports;
delete window.module;

// This file will have the UI related code & integration with main windpw
electron = nodeRequire('electron');
ipcRenderer = electron.ipcRenderer;
windowId = electron.remote.getCurrentWindow().id

uiViz = {
    sendToMain: function (channel, args) {
        console.log('start-scan', arguments)
        ipcRenderer.send(channel, args)
    }
}
