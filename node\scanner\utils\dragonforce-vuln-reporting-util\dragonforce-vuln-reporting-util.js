const fs = require('fs')
const PostmanCollection = require('postman-collection')
const caseless = require('caseless')
const querystring = require('querystring')
const Minimist = require('minimist')
_ = require('lodash')
const path = require('path')
const s3Utils = require('../../common/lib/s3-utils')
const logger = require('../../common/lib/haiku-logger')
const HaikuUtils = require('../../common/lib/haiku-utils')
const DeferredPromise = require('../../common/lib/deferred-promise')
const HTTPRequestMonster = require('../../network-scanner/lib/http-request')
const cheerio = require("cheerio")
//const utils = require('../../app/ifc-utils')

logger.setMetadata({
    haikuProcess: 'dragonforce-vuln-util'
});

//If need add more tags below
const tagsToCheck = [
    { tag: 'iframe', attr: ['src', 'style'] },
    { tag: 'title' },
]

const tagsToCheck2 = [
    { tag: 'img', attr: 'src' },

]

/**
 * Convert postman file to haiku consumable format. Can handle 
 * ItemGroups
 * Requests: types = raw/urlencoded/json
 * variables
 * authentication: <define types>
 */
class DragonForceVulnUtil {
    /**
     * Parse postman collection from file
     * @param {string} scanUrls scan urls for vulnerability
     */
    constructor(urlFile) {
        this.urlFile = urlFile;
        this.requests = [];
        this.s3Utils = s3Utils;
        this.config = require('../../app/default-scan-config');
        this.httpRequestMonster = new HTTPRequestMonster(require('../../network-scanner/config/network-scanner-config'));
        this.scanId = 1;
        this.siteOptions = {
            perDomainMaxParallelRequests: 5
        }
    }

    async readContents() {
        try {
            let urls = await this.s3Utils.getFile(path.dirname(this.urlFile), path.basename(this.urlFile));
            this.requests = JSON.parse(urls.Body);
        } catch (error) {
            logger.error(`Unable to read contents from file ${this.urlFile}, reason: ${error.toString()}`);
        }
    }

    formatUrl(requests) {
        try {
            let url = new URL(requests);
            return url;
        } catch (error) {

        }
    }

    isNullOrUndefined(value) {
        return !(value == null || value == undefined)
    }

    _allowContentType(res) {
        return HTTPRequestMonster.contentTypeMatchesPattern(res, /text\/(html|xml)/i);
    }

    async sendReqeust(request) {
        try {
            let requestPromise = this.httpRequestMonster.queueRequest(request);

            if (!requestPromise) {
                return;
            }

            let result = await requestPromise;

            this.processResponse(result);
        } catch (error) {
            logger.error(`Unable to process request for url ${request.uri}, Reason: ${error}`);
        }

    }

    async startScan() {
        await this.readContents();

        if (this.requests.length == 0) {
            return;
        }

        this.httpRequestMonster.scanStarted(this.scanId, this.siteOptions);

        for (let index = 0; index < this.requests.length; index++) {
            let request = {
                scanId: this.scanId,
                scanlog_id: this.scanId,
                scanner: "haiku",
                resourceType: "mainFrame",
                method: "GET",
                headers: {
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.101 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
                    "Accept-Encoding": "gzip, deflate, br",
                },
                mainUrl: this.requests[index],
                uri: this.requests[index]
            }

            this.sendReqeust(request);
        }

        this.maintainanceProc();
    }

    sendMail(mailOptions) {
        if (!this.config || !this.config.utils || !this.config.utils.mail) {
            utils.log(`Mail configuration not provided, cannot send mail :\n${mailOptions}`)
            return
        }

        const nodemailer = require('nodemailer')

        // set from email address correctly
        mailOptions.from = this.config.utils.mail.auth.user

        // create reusable transporter object using the default SMTP transport
        if (!this.transporter) {
            // lazy initiailization
            let mailTransportOptions = this.config.utils.mail
            this.transporter = nodemailer.createTransport({
                host: mailTransportOptions.host,
                port: mailTransportOptions.port,
                secure: mailTransportOptions.secure,
                auth: mailTransportOptions.auth
            })
        }

        // send mail with defined transport object
        this.transporter.sendMail(mailOptions, (error, info) => {
            if (error) {
                return logger.log(error);
            }
            logger.log('\n**Message %s sent: %s\n**', info.messageId, info.response);
        });
    }

    checkBodyForVuln(attack) {
        try {
            let ResBody = _.get(attack, "result.resp.body")

            //RegExp based on current scenario. it ll be updated later if needs.
            let RegExp_style = /(position:absolute|top:0|left:0|border:0)/i
            let RegExp_title = /(Hacked|Hacktivist Of Garuda|DragonForceMalaysia|AnonGhost|DragonForceMalaysia|RileksCrew|T3DimensionMalaysia|CodeNewbie|PhantomCrews|MyGovTeam|LocalhostMalaysia|UnitedMuslimCyberArmy|HarimauMalayaCyberArmy)/i

            let iframetag = (ResBody.match(/<iframe.+?>/gi))
            let hidden = false
            if (iframetag) {
                for (let i of iframetag) {
                    let styleattr = (i.match(/style="[\w:;\s%]+?"/gi))
                    if (hidden == false && RegExp_style.test(styleattr)) {
                        hidden = true
                    }
                }
            }

            //Find using tagsToCheck 
            if (hidden || RegExp_title.test(ResBody)) {
                let details = []
                let val = []
                //let bodytags = _.get(attack, 'result.resp.httpResponse.cheerio._root.children[1]')
                let $ = _.get(attack, 'result.resp.httpResponse.cheerio')
                for (let tags of tagsToCheck) {
                    let tagElements = $(tags.tag)
                    tagElements.each(function (index, el) {
                        //Hidden iframe
                        if (hidden && tags.tag == 'iframe' && details.length == 0) {
                            for (let i = 0; i < attr.length; i++) {
                                val[i] = $(el).attr(tags.attr[i])
                            }
                            if (val[0] && val[1] && val[0].length > 0 && val[1].length > 0) {
                                let style = val[1].split(';')
                                let count = 0
                                for (let i = 0; i < style.length; i++) {
                                    if (RegExp_style.test(style[i])) { count++ }
                                }
                                if (count == 4) {
                                    details.push({
                                        dom_element: $.html(this)
                                    })
                                }
                            }
                        }
                        //Hacker data
                        if (tags.tag == 'title') {
                            let title = []
                            title.push({
                                dom_element: $.html(this)
                            })
                            if (title.length > 0 && title[0].dom_element && RegExp_title.test(title[0].dom_element)) {
                                let imgsrc
                                for (let tags2 of tagsToCheck2) {
                                    let tagElements2 = $(tags2.tag)
                                    tagElements2.each(function (index, el) {
                                        imgsrc = $(el).attr(tags2.attr)
                                        //currently check exact value of imagename, it ll be updated if needs.
                                        if (imgsrc && /i\.imgur\.com\/l04DZJV\.png|i\.ibb\.co\/P53Cn6b\/1623330735104-1.png/i.test(imgsrc) && RegExp_title.test(ResBody)) {
                                            details.push({
                                                dom_element: title[0].dom_element
                                            })
                                        }
                                    })
                                }
                            }
                        }
                    })
                }
                if (details.length > 0) {
                    return true;
                }
            }
        } catch (err) {
            logger.error(`Unable to check body for vuln. Reason: ${err.toString()}`);
        }

        return false;
    }

    processResponse(originalRequest) {
        try {
            originalRequest.resp.httpResponse.cheerio = cheerio.load(originalRequest.resp.body);
            let isVulnFound = this.checkBodyForVuln({
                result: originalRequest
            });

            if (!isVulnFound)
                return;

            // build mail options
            let mailOptions = {
                to: ['<EMAIL>'],
                bcc: "<EMAIL>;<EMAIL>", // for initial testing
                subject: 'Site is defaced & need urgent attention!',//this.expandTemplate(this.subject, config),
                html: `Site url ${originalRequest.req.parsedURL.href} is defaced, please contant support for further details.`
            }
            // send mail
            this.sendMail(mailOptions);
        }
        catch (err) {
            logger.error(`Unable to process response. Reason: ${err.toString()}`);
        }
    }

    maintainanceProc() {
        let timerInterval = setInterval(() => {
            if (this.httpRequestMonster.requestQ[this.scanId].requests.length == 0) {
                setTimeout(() => {
                    logger.log('info', `All requests complete !!`)
                    process.exit(0);
                }, 60 * 3 * 1000);
            }

            let pending = this.httpRequestMonster.getPendingRequests(this.scanId);
            logger.log('debug', `Waiting for ${pending} queues/in flight requests`);
            if (pending == 0) {
                logger.log('info', `All requests complete !!`)
                //process.exit(0)
            }
        }, 30 * 1000)
    }
}

// --- Main code
let argv = Minimist(process.argv.slice(2), {
    boolean: true,
    alias: {
        o: "out-file",
        dryrun: "dry-run",
        f: "postman-file"
    }
})

async function main() {
    // set up vars
    let urlFile = argv['url-file']

    if (HaikuUtils.isNullOrUndefined(urlFile)) {
        return;
    }

    logger.log('info', `running with args: --url-file=${urlFile}`)

    // parse postman file
    let dragonForceVulnUtil = new DragonForceVulnUtil(urlFile);
    await dragonForceVulnUtil.startScan();
}

// main / module exports based on command line args
if (argv['url-file']) {
    main()
} else {
    module.exports = DragonForceVulnUtil
}




// PostmanToHaiku = require( '/Users/<USER>/work/indusface/haiku/node/scanner/utils/postman-to-haiku/postman-to-haiku.js')
// postmanToHaiku = new PostmanToHaiku('../../tests/postman-files/Tiredful.postman_collection.json')
// postmanToHaiku = new PostmanToHaiku('../../tests/postman-files/Swagger\ Petstore.postman_collection.json')
// postmanToHaiku = new PostmanToHaiku('../../tests/postman-files/kk-tmp.postman_collection.json')