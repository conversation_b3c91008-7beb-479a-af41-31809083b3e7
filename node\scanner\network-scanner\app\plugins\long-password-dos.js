const VectorResponseAttack = require('./vector-response-attack')
const LoginDelegate = require('../../lib/login-delegate')
const _ = require('lodash')

/**
 * Long Password Dos:
 * Here for all the login pages we will try to login with actual username and few long passwords to measure the
 * difference between responses and performing dos attack. If there is huge difference between them then will mark
 * it as vulnerable
 */
class LongPasswordDOS extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-long-password-dos'

    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            encodings: ['raw']
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return LoginDelegate.createVectorsIterator(BruteForceUNVectors, BruteForcePwdVectors)
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['login-request']
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {

        //Set the plugin scope to entire scan per site
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')

        //Below code is to make sure attacks are coming from this plugin only
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln already found then return
        if (pluginStorageScanScope.longPasswordDOS) {
            return
        }

        // Is this first attack - check that from responses if any available
        if (!pluginStorageScanScope.attackRespones) {
            pluginStorageScanScope.attackRespones = []
            pluginStorageScanScope.nextAttackToSend = 0
        }

        // time of last byte of response received from server
        let responseTime = _.get(attack, 'result.resp.fullResponse.timings.end')

        //Push response time for all attacks into array
        pluginStorageScanScope.attackRespones.push(responseTime)

        //Check if all vector attacks are performed, then only proceed ahead for further detection
        if (pluginStorageScanScope.nextAttackToSend >= BruteForcePwdVectors.length - 1) {

            this.getMetadata(attack).vulnerabilities['ID-long-password-dos'].maxResponseDeviation

            //calculate difference in response time between all attacks
            let diff1 = Math.abs(pluginStorageScanScope.attackRespones[0] - pluginStorageScanScope.attackRespones[1])
            let diff2 = Math.abs(pluginStorageScanScope.attackRespones[1] - pluginStorageScanScope.attackRespones[2])
            let diff3 = Math.abs(pluginStorageScanScope.attackRespones[2] - pluginStorageScanScope.attackRespones[0])
            let largest = Math.max(diff1, diff2, diff3);

            //If the difference is more than or equal to the deviation we have, then report it as vulnerable
            if (largest >= this.getMetadata(attack).vulnerabilities['ID-long-password-dos'].maxResponseDeviation) {
                largest = `Current deviation time: ${largest}`
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, largest)
                pluginStorageScanScope.longPasswordDOS = true
                return
            }
        }

        //Increment counter after each attack
        pluginStorageScanScope.nextAttackToSend++

    }
}

//Always keep username here to actual username
const BruteForceUNVectors = [

    VectorResponseAttack.identityVector
]

//Few long passwords 
const BruteForcePwdVectors = [

    // //length 128
    // `&mUe@-4pqJR8W7m?M24+AZjf?DX=aWVVLB?ACabQKJdh?fvn#%ZBm5bPR6z5bUsWL^ABvws3Pxg?tYgkzjj$Zw6evMgB7n8&n7rQA?35V*wh+tT_fCejdR&aVz%!VnEr`,

    // //length 512
    // `!Xv35-_NmmaRT3%$?6NREpur^3sSx!Wz&bfqmq@GJvwcHuF5GfvR&bGLDPegu2ECQ6dD$h=vU?Hjrz8NunWP-pyu6jdr^6ujzB*3RpgbU=WnXN8M=@yAaGJS-CYpNMvY=sjbq8NF_2WnFh8DZuVqN86&PEWu4jLt-fw6MLrpnseznTZfpG_@tMqhh@fL7aCshEGrtJNWKrq6PEa6aZCQs#PS=*tk6Z-#^FE@aDDZKNrmLZ!7ER6=jtyfJqDuKQGp*7352ZHTvBMWhKT^2s7%jTMsb8SUg@B3xj3_^xnJ@^6M8N=n+c4PX3wWbfx*dFKfKQYfdsP8X+=aXrKyMV%Wghcup_wr*EZ#V4ycktv-DHPvm&9e^@*qDgaUh#ws?+PG_VN_VWcqLj^eV#CjUWC6up#z4beMch#@cav%umXR!K%jmSUzA$V4!svwW=wK?L4buffYcrK$HsJjW?WZR%5mcH#a#rw2PaUnB*gbmbvTGb7Jcx%y$RFvCdvry@GKCetw`,

    //length 2k
    `n7_@wSM2c3SMGP4CB!tbaD^uR$#NBP=a$yLBCa3tDEbP+h6j@Rj!B#R+#Lu%FEhdhF9cWE&?@GHX@Qe2YFGtkUzsg5wj?v#e_sQDjQMkzgS82cy+_c+aaXq$dPpDTZJ-6pdg_RWzEDu-&scU@py@hR5frSfp&t2GG$uMcT@L_R8d*bd#33@wWXFWyBZMm-R9-+9tS%MC*pVHCPh^xJKwes@6kar#4zQ&3AVJ@G$jmaWuS=aWM#P@+B=tDAp%@DEuc_&M4zy7C5ktqXuShtFW??5%aTdUBGMTRs#?w&+Ar*q7vV6MadJL4!Ag?hfDCfyeyMFK5hCdbvJ+8r2#h#G6pgq$sgVwmyrU8x73G#b@pAuarWcwmY#LRKmQL2@gLqWG@Jb%#Fat6R8N?nvs2+qq#A%_pX#NNF7z%gkhzp-6EnPvB??WFPy7znJBdp+W?ZW4b&WkwBw3AWAN@%a*YX_#pue*4EbMf7B^+KRr@syTd4X*!ch$g^pFGbja*xhDQ%V6f+v^LB%$Mge_ShfNBD?!MjZX+TVXMdtwK%VxTBLPPN6MwkXCwdR6KF9=mr8E76K^vPy-9dN#vwwyFRKus@Q37!kvEqut3$8j#qsUu?WW+xbtas^vaLnXBu=GMHNdehG$rqwwd2F2N!PXPJNaaAL#AnzyukW375@LDM3anvKXvtStytVkNNeC4E_9Qu3B2CK#dF!^vwbgU#t?e2pUt=rSr@4jLWQenZqS*QAU*TV7skFJwbb+x7YVtAnUWP-Qe8S93Yd+k*Nwgj#Y76F+4Ma+YaKdKzujXA-VFu^SCEmh$dvkvcpdSbayX?DAyT3qF#$KPShKPc-hcSYA9cnWWkqsv*avGVfZpn!Z58%6q63xKCMr3b@2hmVcwRsz8e^RV$W*s6u?XJZbKQG4&y=YULnq4bJ4-2ZM*q#2DtE5kd?@CtPGF%^=c##_^vmtX4HU*@S^cB-sLcM6cSH3R@d8$CpQHh=L2kPTMg5aXF9BbuT*!4_u6q2qpTqv+DV6V$B9+n@BTHN7h6hr^Uyc-sCqCqTRU7H!4f_g7M8Wgft#S25F5+gx8E$n^Z4TEx%CU9Q=7t?Gku!S8myn4+Q=G6xBK^E_#S!9BXe#FD%%frZPjNqWENZ_^sb_AwUX*8GjLwqEVxQGQV-_rsM2x-$Z?_WKk!x*-up58K%N#4qu_47FtE+-kN+S?4SK@FB-X_cj=GrkMm#ar-RUbX@8Dv_YAS+ThQcUTkVkW*YR8TM$qvJ3T9SzWQgP-6A8B?gsMXVHkyFcHpyzP#HRa@x#kEBRXS6#^AGRp*KEW?x#-BaN4g=av=zfjxVrPs^KxhW!WSgK*8+VssTkEm=pKx^xwgb7nt2^M7?KUr%k@eEf!nFvYRCqtMpt&e#xBcxWB6V4vePtPSV@Nkk&g86DTrbQP74YS2YD9*P@p36YDnHg2jA8d4xbJ%NdTdXxAqX4kyRrat$Cx3XdKBw6Wau+Qe_H&LuC$xRAG?3tB5AWY8?xKHcd+Qa7G7sQTs-nHXZfx@eDBtUJssdgArkV3q+9GjnQT2GzEaT6WCYCYdUX%+5Zn76J5T%X53?jVE4kpJ#v^8#JweE2^Pjk$^bCf^Gy8u5hSZhx+WU2uFXrhawwd*ZJTqWxf+-u#*LEw?fytd7M-jE8u6BJ^#A8ZKnX2$HYY43X#Vp_U?%g2ha&rCX4MMZ44cRX7-%4AKU&k2$z!#R&hNTa_4U&VaXVT-F=cGdhZ2mp*5vD&!6rh+ezjBbgzDanm48rn!RMQJJYsybQ_J#pdWdT$MgmBQN=mR^Ck2EnmY+astWGuJy2xCL8YsNfH=qJncg7EUJcpmCx?WH9^74fg!BGdzU=TE*Vgxe&v&K4V?-2G?e2bU_Qk-G=d6kHt9SrQ7T?3zt7jdg_u5qkeCBEcpS6Hh$u&S2_YkTBq=VBU&Jgq&G_82ZNN*CVfw-6xPCCLhqpct%KL?AU2@Kh@ejV=&A=r8u4*^A4mT!pRn%qtCGdNJasZU83M#nh!BSkBPW_6u2Qk653TAe7XGCba$FjzW@W_n-6kzae7EH?`,

    //length 5k
    `2Stls6Aw8I7gMrxpmBCF2vQQRzvOvYGUxVz11yuVkIvTcyswg435Aqsder9pE50l4aoPkyK0eoQouxZu%iWoL1BGjndJMIGEMrNig8e$bsneNZQGqp0Dkm2@gjZD9z$KUnBDLRydUSs2sZJ3OK90eIgJE#S2diIzLtbxHO54BqSuhWaj6TpD#$rxBSh5yCJycVJC0Ar9atOrILf$NwAd$mMdmJKM$Hq@yDvKLpH97h1$Jtk9niNDnc#$unjcX9P6yXf57A6vtJ6xTEw#YPXomcXKQzSrW4iXGcg#BKgL2HR5@Eer@x#lZx6iTTj$@uKPLB6uKb@jXZ4kdsSBVr1Xn1CfytAnq132O7XB7wt3yjNawOtrCsLy60jlWl8WJJFM7qbGzyd0HVAPWpZdMX$tLcBAVPcdh8P5GvXUHS$mkmoG3dSbOYiPg5kMXR1O5xfFUBTHKM5Po6oVjlJ2qtSHghyFQv6GVLvWsTFcFR3jSFI4eELgH06Z5ZBjra#fK#IY8beQtcurxuJrOctOExHXqd78BvVNU#IlyWVGlCwuM4ENBcHlNIapk4wcvyTWX@uTdzL2fzvTQX985yEocc1JqvL2YeaY%Hc#cXEnRj7DeH13DJCYJ4UHsCQycABsB66S4DQroJhmMCn$ZDOC1nenGUpCf6w$Pnsaf7Umtg9E88I25b3XCACEsH%3lDR2#cXL#OHvg69STlxBfsvJQ8Z9r63pLqn7u#3M47WSN0m5W5s7FnHL9wsg%qL7gRka4buY8itNQMPTGEhS1Z9TaMDmUJGUGfpViFdFY@3wzDI2WIir1tcASQdQ$#hlz$rIbUvkPp@TY88cR3seSrmJmQmdMk0nFWwREO7M$vurGgocgvBBp9jd0ceDHg#naWwJj@0hPCKvx7@n4fBlpNzTLYH5KHC9IO@sKECWadDZI75S6a$xB88ce#FDtH7dE0$pQNgL#gBuvR2%7D1uk7jeGRxV$d2xjuvznTVmTU3cATLO99OxPn%5rNAvp#wcXNUD5xJsjSA3vDWS6NO9s$mCsX7eVMg#ISJ7E69cRSXZ$hctzYGEDU#Ep1O5sk6DtbZwBcaVnmfmcdcJMgMAHmU%oLf3N662thLBY0MCRfKl1jyBql7psTfxG8AOKaYtYelCx9#gJb@AQlHiNC$f7Pl8otawepZuLiXau#M$9iFX3P%yl8U4bQD6lfR4oHXOoDHKJhqgOn#DZ4HBX2y68ThWJAkFQ1KsAJKCpm8d5wbnP$uR4ugqQedh2xCLEgOQo6LJVH4rKjNe4dgT@wrHZdC3KyEZIJFTiaQQb6emHYBrE3fq#VyZEHCst9MF97yCyGHG0luZniO6%4$KWe0YdTFl@7BAcPm1fNBtykSRtnoSuunNQsD$Ev06rQBi5l$PTQ1IwckMk8K4@3$7n%ls@Op5tbtCQOg1@ZjaSJmsjK9ifXmF6pvqPXRrqgaGhbRPMPQtypudYx8sMptIQ1GfbI1LVwdFd9rQYI5L0oqQiXPLyuiZcb0wNE3gta4Vyn3YMDTSPm%ZJnJV4Fs%@3l5CqKBzqNEr%6eeAKJkXtF1NDz#1oDiNEnvk#cNr5t$ffZyJA5r9%@J9C1u@dsardeuJ#dsyxsAurvmP@dOaunCZ3z74xBJUw1dI7H7FwuNgqvL6bbFZQln7x#3eJkICvCzISE2q46o53#FPBazYUhTbo4gt5GAAO9CuzyjWe97FRH5haLav1bd9Hm80rbIxMJkFgKhbvvKcJQLi7W8BTI%Dfn5HlK4Sw4DU0g8gO8Zu1DdeGZYudvEMq6Em4SzpiLi664GDYbnEtqmBYEIWsfAe$RAJCMZ$TDrYDT#Kv%Q38BtZ9oIsDSpHIg7wc5fc7b$DFHhCquPW2HCV5W$6789Cg0ZuHF6lXT$VmrVuGFwEVva1r4dQtNdoFurZhRf%hr@AwAt8sQlc38uAXCOuLqRPrEQnKNu1d%ltGNLv11fqy$kHgZXI3RH9uc6UD87VU3H64epK%f@Em@s9U2x$Wno95j4QHWVTjYJdUi9lFyAU1fi$@z79a0%M1ev$Ij3UKoF6RZH2qBf6r#Lp$vrOuFij6owwbnkyOpGo%2CsRZ6lM4I60mWuhTTJxWvfPpRPY$vgGv$n7WibDg2ETrfZEaAWhgQfxN89%Jqh6hhyuFAhIn7XVFGXHn%o9mcih%Cw55rIdZKhDsa8OuJC#jffZQxdJbHlxtpvImeV4eDRluXU3GeG9%17z8oNMk5XwG1C#JbefZ5VkWeolokaaxbZuVnszcmOEhGb6GmGG4nCBS%u2ZCqD%rVNNxdd3vgORBZoIna37kXYBEjSN6baaJRRTFqKQzvrOCdjcP3lvEwQ35S12aPoX5e$qRP0o6MYsQ5g@xYxYhGRDBoXYPorScx8ksQ9WAvSn#Q#pXUZD7DNN0XYO41JKlEl0Jt$WF5ZnkSghXhvP8IuO@uetoD2rRORLa2R$t7BmGNd2ZuyXgaDQsD%mPy4Ly%8CU5XwpwvnI%k@YP$ZxZrSLku9nikbnS$KZ7GtJIYG9669g4Ny1IvQOnEu%2SqIRK9v9S$o0yZd0N%zZGAcZpm$6F6f3i7k4lkUoKXri@IvokwZvuBiUPzVrEtZ%aMEjDtydL8Yx46WjZMtdPB13DaRpyylwTyTKWef1BMecH9ownzLY7pEaRp7%FmEZdck6kRijjpimIkZDTcgMbIxkSnKlFm9SJ5NmywYPrutknHMYOlKpS9YMuYYuPcTVbAgL9%oW$gr25mIoLUHJjDB0YZ0Djl3G4RoTDIosANMIKl6UQamK$#mhLOG%v2$Ti@HOHTK2CaRBh3Po6zgOimN18DZq@wE%evojHKeaog#iq@9Bjez#x175if#zvgc7l9SNj51a#W@LNgxZ8Y2JalNFS73J3LLZRK0q$H$%NoiPCzY6ljPu7tucMJ3XMAibwxPdT5idubrQ$1WrJBWQlLL@kn7NkZ3d$5xLbbHb7FEc2$bAHFFeLNRO8dsdlZ9TX#89TBghVXvu61sBVTIXvV%hYjxHYkhIy9F@kB4TCwcx0VE#JHV7fbXNS1SfkhYOxL@S$oZIKDkkJFVHauRdqHXdLhdsnUntdkd@elyivS4IH57TVxCbERMx0ASXO#I6PUK7y%pLY7HG5hQnjXM6ngz09TP@ijLZbM#D1QV0VCB7N%H4F7p8M1xKcejUDZrSwBiirAEX@vQn7s8mveoQmis9i1ZgLLZxZOf#S#gOc3i45CXuJ%2aA@bM3aSitkAjulxldLTV1d%cH1#wWFVb4Y6#F6x48nJLsQ$RWKntRfhq0QbP1qI4qr919vB$6tUCH50QT9bh3BKwwcUStcVVtq0KfSIMAqe5JljCL9oeQnUJ@INm7xLIztNZ$9FpXH$4y4uxAYtInuArRlo%@zyDP8tQY$P8kGBclgDKqEF57PEaSjm2ESRZRMTllKKb6jSEb6eRSn$5uBYgs67m8y2M%hRneJ%oCLslpPtL2yXyIE9rfBXQpKutAu7yx2VNqAWXO25u7ucZix2EbIDI%oMBMEibNriYQxYtR4O1Ix8Y493IYUURggY3coDv5FwwjYuWo9f$zfjEtvWIBpNQMBVX167WjKU$9zvCv58StN7G0tL4xa7YTsCqQCtyeD@E587LUkgbjyRXioJ#nGV6Kbr4Gs@T0QmMa#zPDnRS4ydrUS7P@aCo1P3vZkPL2oIPbke$4Tz%wV4qXaHVc2Aq9NgL1xYYWXEr#Xq92#@LASLV$g1%MjLKUNCNijcEfA6StmKx5mOQSLLWV1iWCRl6sIAn5D3DuvFQuj9u$49ex9p0Du%UIcB72Lns4pDAHcI5eIjtOQ%QuCAkgPU1Qb4%F01TgYXaAu6zhpNt#se%FSyuDczR$cjwy2#aMSfysbVWZ91qDNpXehlETDpX2tOW5RjyhWhTo#qWGLMg$nihCaJUV5HHPGTdQqCsc9NLKhuA3LbpLUqAc$x#54ClG0HQNcZoY36g0yARUV%eIxglfNrKL3eA9lpVDkRVfCTXma1bWmYBBMfty8dMiMxv16HBci6E1yDcnkihwsSvndLMdA#DhEcCyuLp725jxcmgmnhawy9tln8nL@Nm6cS2wHApqo0YYAQyQmjNgiSAVr%UqOja$ozuMkdq@#bVqxWDEonTYrTN8Bc5Kcr45lKp#pmMqTFDhTq%mcG0ZxNPvmL%yLpP5Bu3Ryb9SFgmnNXOqZE4xlXtQgpzUeDRHBgU@23CvKBXg2SSLwcDEy3y2eJllcmNVfKgqu%T25qBDXTZxJHAm$kIl1DVSpkt7W7iusQ0Z1oczOlsmMFPs$43D@xdKkxgYdR@B9K6Rqcu@NZUlnact8aQ%JHq3ykiPKATY#zmnIqTDygJ0X3YPs1VturbJ0xoY6ybmx16MYE9kgfkPko8Aklo91tert2J8065$dtZeOF3Ou4yqb@rG%p0iDbaw0Agp3mp3rxqWUHEzA0UfDvblRzzcL%VDp1a@cz$hjNBPJKgOUPir%LU9vv13k#rwJBGcmkRK$S3NSXDxME%IleMIv8PubQYVYWX4XCY0EZn%aiQPJYgmslN30wU7sDdY5I83H%rsTfqFfQ2MEmypneIP0bwH1fleLHHjsyUR5ztwfmvi1Y2Wxohv5eAbSW0hS0NIwYuHPFOC9YE4q2ik0sJ70g@0%ucSVD#fNUxanjosX5%UKD$$0MVvgwRT1kfay8$$qL6OE0fYlnWrd1u1jPnQrFi@fN9Toh@mUXer91gd$s2iYv%Q@ktHamcTlMo4pD2UzGZviC6SCLzlLznk6y7#JOdFBYMRxMmRIBAmt93FwoBM5OyNzViYjs@ktk7pN58EbYBAelWkolNrbZ$3yCsKokUhGNb2CbqyEYKDEb7i#or52xrnAXPcmwGcxXurtqGpkddeEjgnxsPVuGTrP5WOfg$P9tNNM@PAEwE1FTD@vhw#YbRFCq$2ZiIl$QTvSYrlpP#wYH7loPCBrSwDkefJaYiCjZqvp2Reh#EV@`,

    // length 10k
    `tTVYCEnPiESoneL9Q0mOTMNHD8Et6C@tFRJszVp6AOkRAg2Hs%W3%VgRrvSZY%pGwWVsl8m1Zv5@u0gBK%hlR$TCLOhioh5ST7FLkdqix0o16PGy0b4xojb#8$Ln8mcSK30z%Bdb0m3YjPEx%QDe$Z%NcT54qlSnM25Ow756sDRmBwt2JkR2RtB#2tHpdHNXL8fkgr@YSQzlGCjptKstl7J9AcrV@WT5FQ9uvSZNSfMA%3#th0nsJbV%Cv#aGkn#9u%xz85IA06YA%38uaH8zov9$6Jomk83qRFA9G7B7Sco@lJkkv1P%V@abGLJNSDVB0Eo57WN3$tTYHKp6UiOuMAYliNbKCYIOlBICCnePb@XKrOGxRd1ofDSeaIRY5VgBbPpsv#uTgK%38YDGDVT29@KgEaw2MK8OtIA0bGEUNs2J8%ozWge9rvucG@pCoZgMkfGHea3ZLhjTIZFpCnsPjDPtMQtheL1BllGx4P9HLoWF8$yVCP%rq6B59TqYKe6cPamMwq8o4kbo7AM2mWT@a589GOJkiBnG@Nu6Yl9y8j392kN412KyKyweIS6tnunW3IgxEUny1s0R$snMCGAXDKuoj3YrSYbXDHWzcWkLBevD@Gc8ZWFZPSKvuJHHR01WFdb0qkO#RfXEZhIU$7aufxOPMFztap8UukeIVpbMqsMiX42eKEdajINw2tm@SdPi8dtpZQx0rmCyqjmfjPYGBKCfktDM9SsPfxwOxkVeU9V7ouPbWmXPSNsjbJJJ91osE$3IZ3as3Bp$%Em91sbjYU0SODtV3t9qsNSfEJSkbQnDqEwyoUlq@Tp3E9D2wyt%smop5oih3LAUGT%G%bqQY5fJJCgg7a5oKZqr9Chg1t@yh%6cNKe5bydEZHFPFp82JcrC9zSjJwwLsjzG8#Y50QeJa8kMsqQxqQeEk1JUo#AufKRM0wzutSnGVBu4pVLDBeOoUObc#qBL5Qv4Zo7nNWNR%YCZEDSgmIz6#nTJqjD#mM8Nc8Wxfd2DXtZr3xCTW85%TvI7KCYmMnKBtSrFGvNVgBlDv9VjrhrkNQHxI$Dz3FAPt@hj5NbY@0bjP%Ou1UpJzMbLsq7d7KWVsT7@o3rZidzCxpEHOqvbxhr#ZD2RS41BYWBBXVPz1ck0vyjcu$lxpJvk%yGXqZJC5bdDmZMRKGeT3jmZLc%XoSsR8$Fw6YoVM8hS6nEMekGmWcfkA6qz53kDSkJTZzTi7SZqEYLkPMyndz0L3OP@MtCoU8Rn9@cMLLZ3k@@tYL1eWd78JXTyscuvEC5Z$DtL6JtQ0qtIc@ZVfVRvjMA$DFS4npAR#HhKdKCnYEog3jCMj1Bho9xZ2F4Irt@O0zxOUQ93IqLZ@%ERB4QD2$#K$e#uX2em@S$6lrRIo3jNgdIqWC3NatG1rsZGO1MSPaVccsCZ95uG6FmUT7k#IAA@zh62exkPfOeqw9akAOFYT0PaJ0$iRu7LR$4MvU5Y%oEC2HKPvz4012kFXZtdtExqyF6JnCamcAuLJQJ8@KS6UcfN8X9AcAc3Mx3siLe5oOgLMUXKcQ6nUR%xuxWt3kUv%rRTqdYIqZp80gzUExtvmLcIUVImeAz#76JdIN%S3ZMAklxRx#cigWyFSPRPt5RBqmmxNXjDsMk1geCHWnyDbuw@2H3@cKDfPiiOxfqdQ361%BApEHsHbXt7Ifuwx%cjdLcrCYQndunE3Ie4fY@77b0y0$qmvjUo9xjHeZV7yFgzK0@IM#xg%n#YD%$cG%zBygZ1PjGaKDgIk$qXtMjl%vl0V1U#nx8vV1SdHT10Nrw25MSi99CWPS8bp11DnT$9#G5qv310gAQofPDA@Kf45mFOQwmEVNUH$3z%yK@Tpl5aalOPNKuFbhK4gXI2i91shfDUibJujJIwJdh$odrhln9yukzrq3zGaDeBrEcwr$1NWr@z2Xhm8qMkoX%8XPGta0lJo1mNJMN4aiVQpB#S9QVkRrkv4gAbfN#J3hA4%eleZkKheVI7ISXjyAniMHG$vgp$lJRuUOrFmn1fpUokDEdCF354U5Sn1b@lHuXoEhFc#gD3xU2Ql6DDMYoeRso5QPXme$mhTuhJ0c52UyKvkWlX6awH@my$src7Z8IIZ54cPUjKD62#J0AAjVZ#yMJg4rcRBnyT2ritHfMOaRfvLVxYht6cB2lgFSuyItwxgyqAQhxjMfa8T1bG1D#5m33xctcOtpEBvTSFDFxGoHzofIDqm%CZJMUnbm3hDDD534eDQZUjQo0j@wmutZ#fMNLgmBsXDP@1ltN%dQj$irz$O%lAmlogMjP5oBOiRJYuKr4UMxyMV5Dk8x1LPgiLGO2UkyLkYjt9Q7k5HecHCM#xRzLgaQNw9mdFXE$9GXjDDi7YrSCnW5irXpxFFgGXSU4Cux8COZGw#%msD6qmazw2gAA3Njj#CjJbmX5mz2Kl65t2IujF4rlCV2LdvD$l9JjR6RyS5ynbbRybeIZIpMXeIb$Z2WTZXZO7kXe30EsMF$u$cjzSYMw06IWr6j0hIn%Sbgw@hRbbslBLuKWxn5JA0a#%rsStagT5hmglydXLD1kjTwe8DJU8wFQHQNiTYo8a426TT#L32Nt1AcRhdE%P$3qigiuup2$eyA17%YeKnYDv#RjIQd3bW@HEH#cFt@HkkMUw$E#PSoNAIC0OSazSpqi#F3xXNuHJMGej%dkt#b%eZuOn405qu8geWAxsWrv$oDh1txzRYIHko3##ekxkS06n#Q2HZwe2kESthxA3ReVf8d#Nc5Sw2rKEhDQ@dHyGBS6@YEkUHBwcaCZwrJrIkzvf1Ui%@NiTYo@M@7917L1Sn5LsUhmjcnS6LXoRWIn38OdCiL5whsjHjMPHPG80K3ZxHkvUHv6t3bOdO03CkFwqDtXjcTEx7$@dgv%T6rQd2zBGAZnZqThEHZ88OxU$rihPg#93CIc%k%@tr$%1ds67YPpclTgBZ#Nn94621Xk1$qHsG6mStJGnx%K6mIAerwkpON0wE$j2q1HTEM%Nyqhv6biV8ny9tFk8HlDwIBAzBfSEyuJORrAFscCZiTGzG9EfH3#I%H1Hpw$sbBUWuEIFyiLtMitlKCYtoz$9mmC2o2vwPstxdJ@17cCusuf8BGRJak6U5rIiGgENxiVHMFdHs7Vuu1Xhpv37twqIjFD6IznKu30pgBLm@e5tfl0TD9NHIC6qaB0y47Luiy$1V$j7nCUwfzcyLy%ezuo5acoRBiRPp9DVgE3v3@8@Iz0QTu7RYjjJUOWBuqJgV8bKLuhqTH1gxxgrZ4#7Mo@jJBKKKQex5Bw4SugJhKjxgFG2IYRyZiaUcBYvBUe@J16MPwQj#HSxOoFvbpSu%%xUHWzEa#csjDnn3EfzEO3hA#i4dFgLyzZ#$PUklFhfv0m%SV8U$t6efw@0IwR02ddXQAtXOnTvmHG7u5iWxij9minULANrwKFk@oKGzqGQ0OX7SjVnix$J$1bRSaHiXODBsHixYVVhdTMGz$2CD63rvtuyYHc04U3jf5px6Y3ce#NZpetdAtS9tLsTET8$dvaNdmlZP#WmbAGAh@s3skN8ko1pD0ITKLl8U32AHt#enGqxOZPfT$WNBNh#aJ%$7RdAXZPK#4#YRQFJnfAWHSmx%If#Anxhjv%Cm04V8ONw2MHj6c47tJJt#lR5Cl6edljBI@m@w@@PpyGyN9YPxNMP%xBZqyOH@SYF5ZBsI#HneUDEWMc2cTc7rlRTGPJj0NWZJVAOjbTIuP#9lOpDC%wUQet6W7kqrnyiS%xryE0r2VZOU$8DJoXJFK8AOOozvF#YawtCGdsQ1GbFL9G12Mu9nXwPHFpIfVJh5tTosY6kHzyVbvjsSN9If1U%Rzq5XypDCxy%DfYKdQtVTax9CYX8sMXO3LZ2U%yjlWAnIxbfBSqFm7HeW0S45KHC%igYP#8Ek6nIRsy8o$FI2kzCi6#tXnniiEcXjCJ66eL9jYHRueUXw$@7vvwuHwwhDzsG59u3iliG7Q4AwhZ8lABTxEsoRG5N@PDuOfqmS72ivDebk0dVTSyJyRT6jlo4g@f#B2yNHVGY%q6qD0dt6H1ugQPKDxTVz%FiDhJh3vtUSgv3ELPhDnedUoXqnk@%zSgXU1QoScg10FIykr8wHoN79znBKqC6ac3MIjS5cALc8MyUDiL6Pi%HFkNHPOHyf#f$qerkSbqY3r1xpJwiX#7EsfzHvfli9L%RjsEMlUpyhAt64Wika@tz2RCb7MZTS%bpA5o6ArPlgRmHPPhH8oXmJaMaCBi8JosV#hHU%4IEgy0#52ejvcgf8pQtjBat3@%z3nUsut%1xu9W9aABLUjUhwgPq7P#lBF6KTj5iCVfQ0QTEfrdxh4WtSwsfP5bWbEEO6Y9900w3@7Uov#sVtHvPXS3#qfD#nbYIouMZdUGGWUC%kebR52lHpAnmrDG4qQKdGp4XaUugKdN3y1Fky$up%1y6GK34Q90TIgRXh$9ueZggYqBoU4U4EmC8k4K9H3yt40xGaGmMV95DvIGW2AMJ3cbq3OyKOZjFR1twqObyTcowOWwI2KTv0k5P$fm0MU02WkEPP5GK%7z5A4mxbEvASzgnc2jcch2BpD1DH9gpYRjuTxB1UyehaC57WRazRAWC0IbXrnl5Egq4zWkJpFEoV72irebADTI2ViqW5gpgub#9UNXtg$NP1s@aCSRYg4A%pE%sEwfl7G20pVTZriHZ1kBM3H%yDrqEkB9ce61GbWiYEOp6q1lgrO@M82TXDf1WR3GdOKVeSqMv4ha0cVA9VvybwXhPrtN6MPS8pZtUhKJpd%014#TMKrFrcr8IVKRoTWlYP3ar1xI9A5bUPsnt2RMAiSh0b0%4tMuevX%kAgL7vFCsBM0mKAPPQTK5OKxrxkMq8QOsYi2%v4bzbCKB9zi%c#iKYTimGaC3rYrItkCpSELv23PCEt3GQCPePYlwK5uOxLFeya8KdNO2dSl7L7mtUntsgB$1Un1h#CkW2thCiysiuus3afyIcdfyPdNF7oJ3LP#ja%J9nWO%KFeOsAyleSrJVh4H22prxlrC#j%rtg2RSNl7az%Kz9Rf3faNKuFmadWs$BFLR%%E8HkcPIGQ$RPwQSZiv7ZMXYZ#UWQ9CEvgf%BLwRrUChT8zvXeJwJcnvQ$VA%NKAxSb7JP%p#G3nuQRnmafyQ7gB2PkWYmjjP05pHVnBoGb#SDn6DJmTsLOQrDb%QFRy%IqOftgT#nR@jshjvvaqKxyXYgIkRAdSAt%BjGbgxfTdkMoTaVCfFxVwum7i%52QHL0voYy7LnnivNAxOpWwO9es#SUaNpm4lp49cHzNH1dVRDw1%elEwjLlgQFU2xayIKqrjkkGIC9dWu3#Dxt1yi@2WbfJQiL4AM5@h7Id@kf3H0gznN9Vk15quN3Dk5r0tcekFwYcBFKY1IAUDT57oHr3%eEIlLK%D$S1WQxXbA%RCmUiAXt#f5#5hqvbQkKdTZSns76DIg79jhc6hkEr6pqZE3A0#sjDnhTvNQ5adZxSZT5HtPEOnix8b2WKP@ibhmIIOXD0emjXeZGhRAfd9Dea6qDH0orEi5koaQENkEbLx1lTz@276zlboK#D3Ik7YRrSZ@q%ilnyF1Z#Sjeiv23q%1qZlTsSx8z%wxmbAZrqJIlt8WYbNpXY3Fer$P@r05aWICDM72ZePod08SB4l%LJFl0Yfs7sg$KWj6x@3uhtJjpbj$aH9Gv705Vasoi0f%YbSqAwu9Nzt0naDp4IcvvMUhI9coEmetwX5o9wQIt5JrjMXCo6QBcVmBv65pU#ht5@UxkjXkiDVYNXpb3H$$Q5ifUswVwkqPPYJqYvTnFaZGX9jO0DEeTExnjOJZjU2FebLVNM@#usb1plcgrb8CWlLGVn5TL7CIa6IO0GH00VwRTJHc0tSCX@4YqfzC2cXIFa6ybIPy8g5uz7J9fHoDrCksz0arrirGOytdmAs7BPZ@FaCqMSodFy8UcPKKFtdu1plbcAz436Ulz61zqXC6WjwN%rz2z7qvTIo2dbs8c6br2hFCZCO7gL60Hefh4%MCZhjr#i#VEc5w6CQXlHVZiAo5X@cQ0Z413okaooyHyMDTC4nsA4GfAt2cWcqwWOnkKOvfqa1gGYAopWR4IP6LyMp56hsWQ7ScLNvyIvgeUHkBXFoxzFz8t$iUBLUvUrbZLgTNqIZg3R9eU1cBPJ85s6GZjlaq9oQM3aas1$@D@fqIxp$9gZIf$o4gjYnecK7W5JrtRjMNsOcmAvGRG8SR5Bwf5F7lDZxi$nMiZgWj$JEYeYewtWzT16m95JlefDBA8SNfRWO$D@c#aI@YQtKPX@@odq@2iDAGxkpuqi7rmhCHEaSaDMA1L37UZz9dLWBR0jXfqg$LLevmOk1xy6smXBvr8MuNJJ7x56AvN6RGtSw@83fTplryUa%qlNV8dxdtgzv3QRkqE%WIoZp$SvykNcw1TU70aJcG8Pqp48Hkj96OOUDvdpatyYBiqnTj@l3I9M6ow#0SU0Jm4F0m%U4U6kVeCm3rJJWt%f@A2SwXPrtNDvwK4h$FYUV#kggDCprVeiSRk0LgEMo5Dt%7HkDlutKN#cTup4nUA0B6fNXWs#4NlMoZd0ttsoI9eHqm26yBa@BExsF%V1V6fTphnxGh$p3@J$#EQzL6ag@o6x#4FniCClsFe0IxLgKMLoIImRZRtHcc3F7e0b#4mrVATnKbLWovJK6T$qIrYSu2%KfYoCUjj5f7kvXzFDDZUUM7o5Hb5Y5MS7%aLzrm#zLsgokanfaBAdOdcp3zcHbn6k9ARHiW1M9QWX#0#Ht6tDx3EWGgZOAhMCQYEjDYP1hzfRcqbbOGk5dOqR%h3Xv5HVij05d7nzW3z2$Ti9$42jm#VfwGS%YF5nSwzVnrP9koUAJ4IuOlXyKIFx5WjVugrIvF0d8ZGDry1WKf6I9P%GvpixqVX9fKRmnyKyz1mi#gOS1DpiTaTJHbB%btbVlC$Enr$48FtL6c580XSTAQ4k6OEgANagbsgFq4rQP@2%3Jn92K3bPNfi#BWwjINQF3$J7SxSCURPbYK3l@#VKvniqKq@kk6HFr#HN0CUgqirV6ZbB4qFY3FRek%f$CD##EAEIDeMDieZJxeR8cb#gM6Ndlds5#2fnH8AqpoBjsKJGE4VxuLU7efdcDkfZdScIBfxR1xkRudOqyEJgaLOE7vI7pXuq5NWhaTdyHXSDli5qmFS$ZzzzZt8NbmAtIpDGjve$syYeaS#A18p$nYDS4pEhctFRj@Gq%B1uWW7AwsrssqLMAEGJihR7SyOZAXJ7un8rJcr$CyAx1bxjyCtL#Y87q%EHE5si0dYiFLAL2utdm4Wj7zLIZXrQxFkUgmEIw2LHGDhb%Top4UhFchNX8gDnAW9hikmEPqxnaYn5yFnugGmw@QOEvdNEfokXSNuYBiNEWT1vHgfuP00PfP7#huwF%GieZluAi#@IDI#Q1DEWCZeHvnV64RQiIDNXeaC0eIf@%6RV8c6TKjSj4jGmVbWnOsWkk2dC$EkXyIRn4FDiU1Bga4LZvxYjxNz@YkD@soH#qqoznzPpmLzghES4L4yy9x2z0RrRtrjY7AFfp7ANFWOx%%MT@5K20oQsEalBVY2c4jZe7MekXD5uYIX@PbSa4EzzdpVYAwGyiyys2fd9A0lm4b0kPhJJ0bxjh62ZCbf2AgfzeymN0wHimu6jE@pJha$#bSiNwqVlD8fo30FT4PthkyCwqWC92CmyFBnQSGYKNMHlEEDwBhE6Nb5XrWoCRotIaVE7ysnWa#C7S45hdYmjPjgQGXyKS6IpLypJPqBpqVzt%2jtFe$EirmqJ4qI@C#n4NguwRJgC6mSzJaRYeWIcl7f7c32e#Nk7y1X8QUQZwO8h9EEvdq0ecEg4gcY$BrYIO7ex3u2OlpdEWsoturinMIl9cqRx9owxUpuTqzpFMJSMzfp@4fRzfX#WAycQKICBAbTa%i2%oubp2L34WA81B8K0Q#$cxCmVdz3gUWxc%P3TlX#Gl9urIGoENz7Avf9OKJhJ5hrLtyxWoea9J38PAE5NMga3Y3AXy7saSHLVEjVVyQqef6fS2w2tVyLjFusVN2HOi#wzg@I18PiakvEWrSVb1VXw8EoExCKijWY04gmAZcQ@GLj5gW2xhTmC%yTd1RHaNTUvbEWnq6fuWwCbAd4KkoverXv4o0YNFEOEZGf1D6K1iVaQVn%Nmzj%Sm#kYRPIeIb4TBlOgrXQTt@83KrE8JLNzzJo8Xk#TlLJP$v8d8rCAa1taHJ$fgi6KqcR@x0K#5ZtnUHVOzysks8L7X1lVSNAzLrZ3sGSHxkSJOXu0%KkHmQ6O4rrH585vD@m8yO9l$usOINWHr52DL79kAKaH0dT4Wpsdk#S9GyysAuP9yOyaa8nPgmhnKcGD7ilWQ8Sj41#AO%RoS98M5s9XcgkgP3jLTlCu8607@jj03oZ7Nx3Bd$NnTUbEL1RjWdQa21Cxf%FJ8Y2uyq6Dege7nt1OeZQ@kKR9C8qwaabLY2jQhKqOg9YFMuZtoCpuk$O6W6YscKc$XcHQ%6zjHXR2chl$Kt7kJJe81vsNubnnoI1nquwlhtHcBLa8AjjCqde0xaRsiKReziL7ubx5ofylmIA$s7D6xy1U5dx$svLtg%tO4%cKqtv7YFurMAxAwxWyfbTrn9LFOBsapsZf5IsIMbgevpVNzisVFKMIZDjRPaBURG4SckUB8an#xVcsuP6A171kbw@66HE%k2VJ4hTh97EikvDJk2VNdE%aoHXbxfaui11s95lhSTvwxt6B09YdyqJwxasPBCfy811FhfoOfPEWrJysIvTfGS6naP%GqeFnA0jpPkMWUpKW5sw3ShjfBmM9oeDPofAb73IswUyqK25W49xAMPnQZbAOghIF#JkLXq9VCc29dR@p3mqBic69$hD1rDw9@L#BYaS#DweQWI49IXcMp5eUSnSRVrPe1FEUpiImOXx6ecBAMNeA5ARu#UurmMCII$dZ07iemMM2@2I$Yiksx$r1mLaCIHw5JDQXIyV%GUH@sbp3TrfxFs3g2B#wTxcnlWoNAQEhImhDqr$R3noX0O35zXIz6lw%Rms30cE@gqAkxzr%%$p0654E2lOK6@8uFtpwK2Dck$rb81DGEZK9rAHCnvbfLUwE@hvwX9d%7QqXvbi$5YfkO407KVNL#H4rd3IE1iJCW7Zjpwx$6Xaj8G2pZLPhDqU6n0FyjN75%vTyL5b5#9c1HiPwWV5wjOq@E8tG#IX5ZuisEQkMo6nVvoU%MRpbOfBrdWyIVXvWDMqQiW1MbM%9iHYZSkO8RoHzejmFf5NDS#XWTVlcIAuWBUSMlCXRaoYT9kQvujVwd5tA9kL3nNpUreK$EvnPycM3EiR4ZI8yO6hUmqrqOBPD7DAPricZssA6RJy9UAb8himdJgkL@rNawjuNZsHTw5LLShMtQ8NovRU2N9jpaRLh0hKEQlHrN@eUQ#1m16o5mLudoCUVuFon#4Q$#yXjsUK5I317S8IrVGdFyr#lLHmHa8Lx8xwNXD1EyVeZ9uF9Ah8AdpSAVsyI$5XesLZLbDB2m9TgJCiBYrMbPx8eNiFLQ9Ioj7Xepd%QO@Tl5L0vqoS6stkEQkfIQQSP9ziViBKdnSg2Go$J#ithSVhqv0hZ@vm0X%%jHa3E6RnFe6FhDKuioistus4GcL#Q4HA#GnuPe6c1dYWg8Cek#yIuNVlmqbaanADvTvNXNepo3B3q%%mFVxnepsFty2RvPtsS7$qKAq1ZXZggLwpvRWdJf5I4i3MLR9TQ2OF3w@Hgh%ty%Q4n9DYs47w4qHxC3TLeYz8ZPg3vC2wYxCJvqP#iG6VmMYXFDTQtrWv5JbuVmSj%pyiSg1arum%No2PbeTYV62Vo%cnDrHqSj3q7NumuJJqlQzg3pBd81Ug1rGTYwZF#oC$7ZhnhGJQspSxikyUieulr$UczCvvy1yohH@qEP$1vCkTFa`,


]



module.exports = LongPasswordDOS