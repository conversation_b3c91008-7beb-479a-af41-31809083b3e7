let simhash = require('simhash')('md5')
const _ = require('lodash')

class FingerPrint {

    /**
     * Convert an object with fingerprintData property into a Fingerprint. Used for deserializing.
     * @param {Object} object Object containg fingerprint property
     * @param {string} propertyPath path to property eg 'interestingData.actionFingerprint'
     */
    static podToObject(object, propertyPath) {
        let fingerprint = _.get(object, propertyPath);
        if (fingerprint && fingerprint.fingerPrintData) {
            _.set(object, propertyPath, new FingerPrint(fingerprint.fingerPrintData))
        }
    }

    constructor(fingerPrint) {
        this.fingerPrintData = simhash(fingerPrint).join('')
        this.length = this.fingerPrintData.length
    }

    similarity(fingerprint) {
        if (!fingerprint) {
            return 0
        }

        let fingerprintData = fingerprint
        if ('string' != typeof (fingerprint)) {
            fingerprintData = fingerprint.fingerPrintData
        }

        // count the bits in common and return ratio to length
        let commonBits = 0
        for (let i = 0; i < fingerprint.length; i++) {
            if (fingerprintData[i] == this.fingerPrintData[i]) {
                commonBits++
            }
        }

        return commonBits / this.fingerPrintData.length
    }

    toString() {
        return this.fingerPrintData.join('')
    }
}

module.exports = FingerPrint