const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

class DebugMethodPlugin extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnerabilityID = 'ID-asp-debug-feature-enabled'
    }

    getAttackVectors() {
        return httpAttackVectors
    }

    getAttackableEvents() {
        return ['http-methods']
    }

    /**
     * @param {method} attack
     * Overriding the performNetworkAttack method to add Command header in the request
     */
    async performNetworkAttack(attack) {
        attack.httpRequest.headers['Command'] = "stop-debug"
        return await super.performNetworkAttack(attack)
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        //here result for vulnerability in each case
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')

        /**
         * If the response has status code 200 then it will add it to the vulnerability list
         */
        if (attack.result.resp.httpResponse.statusCode == 200 && redirect == 0) {
            // added below condition to remove fp's
            if (/\bok\b/i.test(attack.result.resp.httpResponse.body)) {

                let details = {
                    name: "ASP .Net Debug feature enabled",
                    params: "Attack Area : " + attack.attackArea + " server method : " + attack.param,
                    status: attack.result.resp.httpResponse.statusCode
                }
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) { return }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["Command"]);
    }
}

const httpAttackVectors = [
    `DEBUG`
]

module.exports = DebugMethodPlugin