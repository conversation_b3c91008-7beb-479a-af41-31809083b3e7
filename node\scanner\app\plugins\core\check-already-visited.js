let utils = require('../../ifc-utils.js')
const BezierEasing = require('bezier-easing')
const FingerPrint = require('../../datastructure/fingerprint.js')
const ActionList = require('../../datastructure/action-list')

class SkipVisitedState {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        // merge the plugin specific config
        this.mergeConfig()

        let pluginData = this.config.pluginData.checkForDup
        this.dupThreshold = pluginData.threshold

        // what type of action matching do we want to do
        this.actionDupCheck = pluginData.actionDupCheck
        this.guessThreshold = pluginData.guessThreshold
        this.minDepthForPredictiveDup = pluginData.minDepthForPredictiveDup

        // set up event listeners
        scanner.on('pre-trigger', this.preTriggerActions.bind(this))
        scanner.on('got-interesting-items', this.getActions.bind(this))

        // set up easing functions
        this.thresholdEasingByDepth = []
        try {
            // fixed
            if (pluginData.falloffFunctions.includes('fixed') && pluginData.fixed) {
                let thresholdFalloffFixed = pluginData.fixed.thresholdFalloffFixed
                if (pluginData.fixed.thresholdFalloffRatio) {
                    let thresholdFalloffRatio = pluginData.fixed.thresholdFalloffRatio
                    thresholdFalloffFixed = this.config.maxDepth / thresholdFalloffRatio
                }

                this.thresholdEasingByDepth.push((depth) => {
                    return thresholdFalloffFixed * depth
                })
            }

            // logistic
            if (pluginData.falloffFunctions.includes('logistic') && pluginData.logistic) {
                let logistic_k = pluginData.logistic.k
                let x0Pct = pluginData.logistic.x0Percent
                let logistic_x0 = (x0Pct * this.config.maxDepth) - (this.config.maxDepth / 2)
                let maxDepth = this.config.maxDepth
                this.thresholdEasingByDepth.push((depth) => {
                    function f(depth) {
                        const L = 1
                        return L / (1 + Math.exp(-(logistic_k * (depth - logistic_x0))))
                    }
                    let mid = maxDepth / 2
                    return f(depth - (maxDepth / 2))
                })
            }

            // bezier
            if (pluginData.falloffFunctions.includes('bezier') && pluginData.bezier) {
                let maxDepth = this.config.maxDepth
                let easing = BezierEasing(pluginData.bezier.p0, pluginData.bezier.p1, pluginData.bezier.p2, pluginData.bezier.p3)
                this.thresholdEasingByDepth.push((depth) => {
                    return easing(depth / maxDepth)
                })
            }

            // do we need step - why not !!!
            if (pluginData.falloffFunctions.includes('step') && pluginData.step) {
                let steps = pluginData.bezier.step
                this.thresholdEasingByDepth.push((depth) => {
                    let easing = 0
                    let prevDepthRange = 0
                    for (step of steps) {
                        let [depthRange, falloff] = step
                        if (depth >= depthRange) {
                            easing += Math.abs(depthRange - prevDepthRange) * falloff
                        } else {
                            easing += Math.abs(depth - prevDepthRange) * falloff
                        }

                        if (depth <= depthRange) {
                            break
                        }
                        prevDepthRange = depthRange
                    }

                    return easing
                })
            }


        } catch (e) { }
    }

    mergeConfig() {
        if (!this.config.siteConfig || !this.config.siteConfig.pluginData || !this.config.siteConfig.pluginData.checkForDup) {
            return
        }

        // plugin data for "check for dup" plugin
        let pluginData = this.config.pluginData.checkForDup
        let sitePluginData = this.config.siteConfig.pluginData.checkForDup

        if (sitePluginData.threshold) {
            pluginData.threshold = sitePluginData.threshold
        }

        if (sitePluginData.actionDupCheck) {
            pluginData.actionDupCheck = sitePluginData.actionDupCheck
        }

        if (sitePluginData.guessThreshold) {
            pluginData.guessThreshold = sitePluginData.guessThreshold
        }

        if (sitePluginData.minDepthForPredictiveDup) {
            pluginData.minDepthForPredictiveDup = sitePluginData.minDepthForPredictiveDup
        }

        if (sitePluginData.falloffFunctions) {
            pluginData.falloffFunctions = sitePluginData.falloffFunctions
        }

        if (sitePluginData.fixed) {
            pluginData.fixed = sitePluginData.fixed
        }
        if (sitePluginData.logistic) {
            pluginData.logistic = sitePluginData.logistic
        }
        if (sitePluginData.bezier) {
            pluginData.bezier = sitePluginData.bezier
        }
        if (sitePluginData.step) {
            pluginData.step = sitePluginData.step
        }
    }

    getEasedThreshold() {
        let dupThreshold = this.dupThreshold
        if (this.scanner.crawlState.currentContext && this.scanner.crawlState.currentContext.depth) {
            for (let easing of this.thresholdEasingByDepth) {
                dupThreshold -= easing(this.scanner.crawlState.currentContext.depth)
            }
        }

        return dupThreshold
    }

    preTriggerActions(ret, actions) {
        let canRun = !!ret.fingerprint // fingerprint plugin must run before this plugin

        // current crawl depth should be at least the min dpeth for predictive dup 
        let depth = 0
        if (this.scanner.crawlState.currentContext && this.scanner.crawlState.currentContext.depth) {
            depth = this.scanner.crawlState.currentContext.depth
        }
        if (depth < this.minDepthForPredictiveDup) { // must be at least at min depth
            utils.log(`Not doing predictive dup actions since depth (${depth}) < min reqd depth (${this.minDepthForPredictiveDup})`)
            canRun = false
        }

        if (!canRun) {
            ret.checkAlreadyVisited = {
                dupAction: false
            }
            return
        }

        // First find all dup states that have matched at >= current dup threshold. 
        let dupThreshold = this.getEasedThreshold()
        let dupContexts = this.findAllDupContexts(this.scanner.crawlState, dupThreshold)
        let matchedDupContexts = []
        let isDupAction = false
        let isGuess = true
        if (dupContexts.length) {
            utils.log(`found ${dupContexts.length} dup contexts`)
            // Now see if we have at least one dup context that matched a dup action at a similarity >= 
            // similarity between the context action and this 'to be performed'/'pre trigger' action
            let curActionFingerPrint = ret.fingerprint.actionFingerprint
            for (let context of dupContexts) {
                let similarity = curActionFingerPrint.similarity(context.actionFingerprint)

                let actionSimilarity = context.dupInfo.wavgSimilarity
                switch (this.actionDupCheck) {
                    case 'min':
                        actionSimilarity = context.dupInfo.minActionSimilarity
                        break
                    case 'max':
                        actionSimilarity = context.dupInfo.maxActionSimilarity
                        break
                    case 'avg':
                        actionSimilarity = context.dupInfo.avgActionSimilarity
                        break
                    case 'wavg':
                        actionSimilarity = context.dupInfo.wavgActionSimilarity
                        break
                    default:
                        utils.log(`unknown action dup check : ${this.actionDupCheck}`)
                }

                //utils.log(`\tpreTrigger context action sim= ${similarity*100}%. context's dup info >= ${JSON.stringify(context.dupInfo)}`)
                // 1/2% grace. Otherwise a few high ones followed by a sequence of same low ones will tend towards the low but never reach it 
                let epsilon = 0.5 / 100
                if ((similarity + epsilon) >= actionSimilarity) {
                    //utils.log(`\tpreTrigger : found likely similar action  = ${similarity*100}% >= % ${actionSimilarity*100}%. context's dup info >= ${JSON.stringify(context.dupInfo)}`)
                    isDupAction = true
                    matchedDupContexts.push(context)
                    if (context.dupInfo.correctGuesses >= this.guessThreshold) {
                        isGuess = false
                    }
                }
            }
        }

        ret.checkAlreadyVisited = {
            dupAction: isDupAction,
            dupContexts: matchedDupContexts,
            isGuess: isGuess
        }
    }

    // will return array of all nodes that have been found to be duplicates at >= given threshold
    findAllDupContexts(crawlState, threshold) {
        if ('undefined' == typeof threshold) {
            threshold = 1 // didn't specify, do full context tree 
        }
        let contextIterator = crawlState.getVisitor()

        let nextContext = contextIterator.next()
        let dupContexts = []
        while (!nextContext.done) {
            let ctx = nextContext.value

            if (ctx.dupInfo.dupThreshold >= threshold) {
                dupContexts.push(ctx)
            }
            nextContext = contextIterator.next()
        }

        return dupContexts
    }

    getActions(ret, interestingItems) {
        // previous plugin already decided to skip state or specifically asked to stop processing more plugins => nothing to do
        if (ret.skipState) {
            return
        }

        // sanity check
        let fingerprint = interestingItems.serverData.fingerprint
        if (!fingerprint) {
            utils.log('\t** Could not do fingerprinting check since fingerprint for state was not computed')
            return
        }

        // get all duplicate states at this (depth) threhold level.
        let crawlState = this.scanner.crawlState
        let dupThreshold = this.getEasedThreshold()
        let similarContexts = this.findAllSimilarContexts(crawlState, fingerprint, dupThreshold)

        // see if we have a dup  at this threshold level
        let dupStateFound = similarContexts.length && similarContexts[0].similarity >= dupThreshold

        // save the duplicate info in the state itself
        if (dupStateFound) {
            let replayActions = this.scanner.crawlState.currentContext.getReplayActions()
            if (replayActions) {
                let dupActions = new ActionList(`replay-need-to-add-context-id`)
                dupActions.addAction(replayActions)
                dupActions.addAction(this.scanner.crawlState.currentContext.curAction())
                let actionsResultingInDup = dupActions.flatten()
                for (let ctx of similarContexts) {
                    let matchSimilarity = Math.round(ctx.similarity * 100)
                    if (!ctx.context.actionsResultingInThisState[matchSimilarity]) {
                        ctx.context.actionsResultingInThisState[matchSimilarity] = []
                    }
                    ctx.context.actionsResultingInThisState[matchSimilarity].push(actionsResultingInDup)
                }
            }
        }

        // If dups are found, also update the states so predictive dup checking can work.
        this._doPredDupDataUpdate(dupStateFound, interestingItems, similarContexts, dupThreshold, crawlState);

        ret.skipState = ret.skipState || dupStateFound
    }

    _doPredDupDataUpdate(dupStateFound, interestingItems, similarContexts, dupThreshold, crawlState) {
        // failures are likely to end up with dups so dont take update action/state dup metadata
        if (interestingItems.serverData.actionMayBeBlocked) {
            utils.log('\t** did not do dup updation due to possible action failure')
            return
        }

        // do predictive dup checking only when we have reached the min depth for pred action dup checking
        let depth = 0
        if (crawlState.currentContext && crawlState.currentContext.depth) {
            depth = crawlState.currentContext.depth
        }
        if (depth < this.minDepthForPredictiveDup) {
            utils.log(`\t** did not do dup updation due since dpeth ${depth} < min depth for pred dup = ${this.minDepthForPredictiveDup}`)
            return
        }

        if (dupStateFound) {
            let actionFingerprint = interestingItems.serverData.preTriggerResult.fingerprint.actionFingerprint;
            for (let similarContext of similarContexts) {
                let screenshotID = similarContext.context.interestingItems.serverData.screenshotID;
                utils.log(`(depth=${depth}): found similar context @${similarContext.similarity * 100}%, threshold is ${dupThreshold * 100}% screenshotID: ${screenshotID}`);
                // add/update context that it matched at this level of action fingerprint similarity
                let actionSimilarity = actionFingerprint.similarity(similarContext.context.actionFingerprint);
                let dupInfo = similarContext.context.dupInfo;
                if (-1 == similarContext.context.dupInfo.minActionSimilarity) {
                    dupInfo.maxActionSimilarity = actionSimilarity;
                    dupInfo.minActionSimilarity = actionSimilarity;
                    dupInfo.avgActionSimilarity = actionSimilarity;
                    dupInfo.wavgActionSimilarity = actionSimilarity;
                    dupInfo.countActionSimilarity = 1;
                    dupInfo.dupThreshold = dupThreshold;
                } else {
                    if (dupInfo.maxActionSimilarity < actionSimilarity) {
                        dupInfo.maxActionSimilarity = actionSimilarity;
                    }
                    if (dupInfo.minActionSimilarity > actionSimilarity) {
                        dupInfo.minActionSimilarity = actionSimilarity;
                    }
                    // calculate weighted average (weight = 2)
                    // do this before calculating avg since this uses vars changed by  that calculation
                    let weight = 2;
                    dupInfo.wavgActionSimilarity = (dupInfo.countActionSimilarity * dupInfo.avgActionSimilarity) + (weight * actionSimilarity);
                    dupInfo.wavgActionSimilarity /= (dupInfo.countActionSimilarity + (1 * weight));
                    // calculate avg
                    dupInfo.avgActionSimilarity = (dupInfo.countActionSimilarity * dupInfo.avgActionSimilarity) + actionSimilarity;
                    dupInfo.countActionSimilarity++;
                    dupInfo.avgActionSimilarity /= dupInfo.countActionSimilarity;
                    if (dupInfo.dupThreshold < dupThreshold) {
                        dupInfo.dupThreshold = dupThreshold;
                    }
                }
            }
            // log dup found info
            // TODO - change from screenshotID to state ID
            let curId = interestingItems.serverData.screenshotID;
            let similarId = similarContexts[0].context.interestingItems.serverData.screenshotID;
            utils.log(`Similarity of ${curId} to ${similarId} is ${similarContexts[0].similarity * 100}% >= ${dupThreshold * 100}% - skipping `);
            crawlState.logContextInfo(`Dup @ ${similarContexts[0].similarity * 100}% >= ${dupThreshold * 100}%`);
        }
        // update the dupInfo with correct/incorrect guesses
        let preTriggerCheckAlreadyVisited = interestingItems.serverData.preTriggerResult.checkAlreadyVisited;
        if (preTriggerCheckAlreadyVisited.dupAction) {
            for (let dupContext of preTriggerCheckAlreadyVisited.dupContexts) {
                if (dupStateFound) {
                    dupContext.dupInfo.correctGuesses++;
                    if (dupContext.dupInfo.correctGuesses > this.guessThreshold) {
                        dupContext.dupInfo.correctGuesses = this.guessThreshold;
                    }
                } else {
                    dupContext.dupInfo.correctGuesses = 0;
                }
                utils.log(`correct guesses is now ${dupContext.dupInfo.correctGuesses}`);
            }
        }
    }

    // will return array of all nodes >= threshold (similarity = threshold is % represented 
    // as floating point number between 0-1). Array is sorted by similarity
    // For now traverse the entire context DS. Move to efficient BK tree later
    findAllSimilarContexts(crawlState, fingerprint, threshold) {
        if ('undefined' == typeof threshold) {
            threshold = 1 // didn't specify, do full context tree 
        }
        let contextIterator = crawlState.getVisitor()

        let nextContext = contextIterator.next()
        let similarContexts = []
        while (!nextContext.done) {
            let ctx = nextContext.value

            let similarity = 0
            if (ctx && ctx.interestingItems && ctx.interestingItems.serverData && ctx.interestingItems.serverData.fingerprint) {
                similarity = fingerprint.similarity(ctx.interestingItems.serverData.fingerprint)
            }

            if (similarity >= threshold) {
                similarContexts.push({
                    context: ctx,
                    similarity: similarity
                })
            }

            nextContext = contextIterator.next()
        }
        return similarContexts.sort((a, b) => {
            a.similarity < b.similarity ? -1 : a.similarity > b.similarity ? 1 : 0
        })
    }
}


module.exports = SkipVisitedState