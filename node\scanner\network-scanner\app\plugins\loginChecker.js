const _ = require('lodash');
const LoginDelegate = require('../../lib/login-delegate.js');
const request = require('request');

// This function checks if the login is successful by checking for the presence of authentication indicators in the response body
async function checkLogin(attack) {
    try {
        if (attack.attackArea === 'original-crawler-request' && attack.pluginName === 'Original Crawler Request') {
            let loginInfo = false;
            let testPass = false;
            loginInfo = LoginDelegate.isLoginRequest(attack.httpRequest);
            if (loginInfo === true) {
                return { success: true, testPass: testPass, message: 'Simple Login Successful' };
            }

            if (/forg[e|o]t|signup|register|reset|change|new|confirm/i.test(attack.originalRequest.httpRequest.uri)) {
                return { success: false, message: 'Not a login page' };
            }

            let ReqBody = _.get(attack, 'originalRequest.httpRequest.body').trim();
            if (/new|old|confirm|change|reset|forg[e|o]t|hidden|htxt|txth|hdn|hpwd|hpas/i.test(ReqBody)) {
                return { success: false, message: 'Not a login page' };
            }

            // Check if the request body contains password
            if (!ReqBody.includes('password') && !ReqBody.includes('passwd') && !ReqBody.includes('pwd') && !ReqBody.includes('login_pass') && !ReqBody.includes('user_secret') && !ReqBody.includes('passcode')) {
                return { success: false, message: 'Login failed: Password not found in request body' };
            }

            // Check if the uri param contains password
            let uri = _.get(attack, 'originalRequest.httpRequest.uri').trim();
            if (!uri.includes('password') && !uri.includes('passwd') && !uri.includes('pwd') && !uri.includes('login_pass') && !uri.includes('user_secret') && !uri.includes('passcode')) {
                return { success: false, message: 'Login failed: Password not found in request uri' };
            }

            let ResBody = _.get(attack, 'result.resp.body').trim();

            const titleMatch = ResBody.match(/<title>(.*?)<\/title>/i);
            const pageTitle = titleMatch ? titleMatch[1] : 'Not Found';

            let redirect = _.get(attack, 'result.resp.httpResponse.redirects[0]', {});
            let redirectedUrl = _.get(redirect, 'redirectedUri', '');
            if (attack.httpRequest.loginInfo && attack.httpRequest.loginInfo.loginStatus === true) {
                return { success: true, message: `Login successful: Redirected to ${redirectedUrl || attack.httpRequest.href}`, title: pageTitle, Confirmed: 'loginInfo.loginStatus' };
            }

            let originalUrl = new URL(_.get(attack, 'href', ''));

            // Handle redirects
            if (redirectedUrl.length > 0) {
                let Re_URL = new URL(redirectedUrl);
                if (!/(?:log|sign)[ -_]?out/.test(Re_URL.pathname) && Re_URL.pathname !== originalUrl.pathname) {
                    
                    const isAuthenticated = authIndicators.some(keyword => keyword.test(ResBody));

                    if (isAuthenticated) {
                        return { success: true, testPass: testPass, message: `Login successful: Redirected to ${redirectedUrl}`, title: pageTitle, pattern: isAuthenticated };
                    }
                }
                return { success: false, message: `Login failed: Still on ${redirectedUrl}`, title: pageTitle };
            }

            if (/Test@1234|Test%401234/i.test(ResBody) || /Test@1234|Test%401234/i.test(attack.href)) {
                testPass = true;
            }

            // Check base64 encoded password
            const base64TestPass = Buffer.from('Test@1234').toString('base64');
            if (ResBody.includes(base64TestPass) || attack.href.includes(base64TestPass)) {
                testPass = true;
            }

            // Check if the request is a login page
            let uriRaw = attack.httpRequest.headers.Referer;
            if (uriRaw && uriRaw.length > 0) {
                try {
                    let newheader = attack.originalRequest.httpRequest.headers;
                    const options = {
                        headers: newheader,
                        uri: uriRaw,
                        rejectUnauthorized: false,
                        timeout: 30000,
                        resolveWithFullResponse: true
                    };
                    let NewResponse = await request(options);
                    if (NewResponse && NewResponse.statusCode === 200) {
                        let AtkResbody = NewResponse.body;
                        // Check for password input fields with more specific patterns
                        const passwordInputRegex = /<input[^>]+(?:type\s*=\s*['"]password['"]|(?:name|id)\s*=\s*['"](?:password|passwd?|pwd|login_pass|user_secret|passcode)['"])[^>]*>/i;

                        if (passwordInputRegex.test(AtkResbody)) {
                            return { success: true, testPass: testPass, message: 'It is a login page, password fields found using referer' };
                        }

                        // Check for login form with specific attributes
                        const loginFormRegex = /<form[^>]*>.*?<\/form>/gis;
                        const formData = loginFormRegex.exec(AtkResbody);

                        if (formData) {
                            formData.forEach(element => {
                                // Check for login-related keywords in form content
                                const loginKeywords = /(?:log[ -_]?in|sign[ -_]?in|pass[ -_]?word|password|passwd|pwd|login_pass|user_secret|passcode)/i;

                                // Check for submit button with login-related text
                                const submitButton = /<button[^>]*>(?:log\s*in|sign\s*in|submit)<\/button>|<input[^>]+type\s*=\s*['"]submit['"][^>]*>/i;

                                if (loginKeywords.test(element) && submitButton.test(element)) {
                                    return { success: true, testPass: testPass, message: 'It is a login page, password fields found using referer' };
                                }
                            });
                        }                        

                        const hasNonFormLogin = nonFormLoginPatterns.some(pattern => pattern.test(AtkResbody));
                        if (hasNonFormLogin) {
                            return { success: true, testPass: testPass, message: 'Login functionality detected (non-form based)' };
                        }
                    }
                } catch (error) {
                    return { success: false, message: `Error checking login page: ${error.message}` };
                }
            }
            return { success: false, message: 'Not a login page' };
        } else {
            return { success: false, message: 'Not a Original Crawler Request' };
        }
    } catch (error) {
        return { success: false, message: `Error: ${error.message}` };
    }
}

// Check for logout links or keywords indicating authentication success
const authIndicators = [
    /(?:href|src)="[^"]*(?:log|sign)[ -_]?out[^"]*"/i,
    /(?:href|src)="[^"]*my[ -_]?(?:account|profile|dashboard)[^"]*"/i,
    /(?:href|src)="[^"]*(?:settings|welcome|admin|control|account)[^"]*"/i,
    /<a[^>]+(?:log|sign)[ -_]?out[^>]*>/i,
    /<a[^>]+my[ -_]?(?:account|profile|dashboard)[^>]*>/i,
    /<a[^>]+(?:settings|welcome|admin|control|account)[^>]*>/i
];

// Check for non-form based login mechanisms
const nonFormLoginPatterns = [
    // Check for common login-related class names and IDs
    /class\s*=\s*['"][^'"]*(?:login|signin|auth|authentication)[^'"]*['"]/i,
    /id\s*=\s*['"][^'"]*(?:login|signin|auth|authentication)[^'"]*['"]/i,
    // Check for JavaScript event handlers
    /onclick\s*=\s*['"][^'"]*(?:login|signin|auth|authenticate)[^'"]*['"]/i,
    /addEventListener\s*\(\s*['"]click['"]\s*,\s*[^)]*(?:login|signin|auth|authenticate)[^)]*\)/i,
    // Check for modern authentication patterns
    /<div[^>]*data-role\s*=\s*['"]login['"][^>]*>/i,
    /<div[^>]*data-testid\s*=\s*['"]login['"][^>]*>/i,
    // Check for common login button patterns
    /<button[^>]*>(?:log\s*in|sign\s*in|authenticate)<\/button>/i,
    /<a[^>]*>(?:log\s*in|sign\s*in|authenticate)<\/a>/i,
    // Check for password input fields outside forms
    /<input[^>]+type\s*=\s*['"]password['"][^>]*>/i,
    // Check for common login-related data attributes
    /data-cy\s*=\s*['"][^'"]*(?:login|signin|auth)[^'"]*['"]/i,
    /data-test\s*=\s*['"][^'"]*(?:login|signin|auth)[^'"]*['"]/i
];

module.exports = {
    checkLogin
};