const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:ScanFinished')

/** 
 * scanner -> crawler indicating scan has started
 * @extends QueueMessage
*/
class ScanFinished extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} ScanFinishedMsgContent
     * @property {Number} scanId Scan ID (aa alert ID)
     * @property {string} scanner scanner/crawler that sent this request (haiku)
     */
    /**
     * @param {ScanFinishedMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'crawler'
        this.routingKey = 'request.scan-finished'
        this.msgType = ScanFinished.msgType
    }
}

module.exports = ScanFinished