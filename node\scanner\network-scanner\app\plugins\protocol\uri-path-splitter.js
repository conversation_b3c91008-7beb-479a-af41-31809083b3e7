const debug = require('debug')('UriPathSplitter')
const querystring = require('querystring')
const BasePlugin = require( '../base-plugin')
const _ = require('lodash')
const logger = require('../../../../common/lib/haiku-logger')
const HaikuUtils = require('../../../../common/lib/haiku-utils')
const UriPathIterator = require('../../../lib/uri-path-iterator')

/**
 * Given an URI eg. http://www.xyz.com/a/b/c/d?kk=hh, this will iterate:
 * END: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/c/d/[vectors]?kk=hh
 * ROOT: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/[vectors]?kk=hh
 * sub paths based on depth: 
 *  depth 1: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/[vectors]?kk=hh
 *  depth 2: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/[vectors]?kk=hh
 */
class UriPathSplitter extends BasePlugin {
    /**
     * @param {NetworkScanner} networkScanner the netork scanner
     * @param {config} config config object
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
    }

    /**
     * New request received from crawler - Kick off paramter iteration if request is viable
     * @param {request} originalRequest untampered request
     * @override
     */
    processNewRequest(originalRequest) {
        // see if we are revalidating and we need to skip this protocol delegate eg. skip URI path if attack was on http headers
        // while revalidating we will send only targeted attaks i.e. attacks only from the specific plugin
        // that generated the request we are revalidating now
        if (!this.getMetadata(originalRequest).bypassRevalidationCheck) {
            let attackArea = _.get(originalRequest, 'revalidationInfo.attack.area')
            if (originalRequest.revalidationInfo && UriPathIterator.ParameterType != attackArea) {
                logger.log('info', `uri path splitter - not an interesting request since request being revalidated is attacking '${attackArea}'`, HaikuUtils.getMetadataForLog(originalRequest))
                return
            }
        }

        if (originalRequest.httpResponse.err) {
            logger.log('info',`uri path splitter - not an interesting request ${originalRequest.httpRequest.method} ${originalRequest.httpRequest.uri}  err=${originalRequest.httpResponse.err}`, HaikuUtils.getMetadataForLog(originalRequest))
            return
        }

        // Create object that can iterate and manipulate params of request. 
        let scanStore = this.getPluginScopedStore(originalRequest, 'this-scan')
        let options = this.getMetadata(originalRequest).options;
        let createUriPathIterator = function () {
            return new UriPathIterator(originalRequest, scanStore, options)
        }
        this.networkScanner.emit('uri-path-iterator', createUriPathIterator)
    }

}

module.exports = UriPathSplitter