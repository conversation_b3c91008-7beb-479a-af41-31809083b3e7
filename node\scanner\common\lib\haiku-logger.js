const winston = require('winston');
const path = require('path')
const config = require('../config/logger-config.js')
const mkdirp = require('mkdirp')

class HaikuLogger extends winston.Logger {
    constructor() {
        super(...arguments)

        this.metadata = {
            haikuProcess: 'unknown'
        }

        // automatically add a rewriter to the log adding haiku specific info
        this.rewriter = function (level, msg, meta) {
            let ret = {}
            Object.assign(ret, this.metadata)  // start with common metadata
            Object.assign(ret, meta)  // merge in passed in metadata - it should overrule the common one
            meta.timestamp = new Date().toISOString() // always add timestamp
            return ret
        }
        this.rewriters.push(this.rewriter.bind(this))
    }

    /**
     * Replaces the metadata to include in every log entry. Things like scanID, scanlogID
     * @param {Object} metadata 
     */
    setMetadata(metadata) {
        this.metadata = metadata
    }

    /**
     * Appends/replaces the metadata to include in every log entry. Things like scanID, scanlogID
     *      uses Object.assign() so will add/replace metadata
     * @param {Object} metadata 
     */
    addMetadata(metadata) {
        Object.assign(this.metadata, metadata)
    }

    /**
     * returns the current metadata
     */
    getMetadata() {
        return this.metadata
    }
}

// create a haiku logger
let logger_path = path.join(__dirname, config.logdir)
mkdirp.sync(logger_path)
config.options.file.filename = path.join(logger_path, `${config.options.file.filename}-${process.pid}.log`)

// instantiate a new Winston Logger with the settings defined in config 
let loggerOptions = Object.assign({}, config.options.winston, {
    transports: [
        new winston.transports.File(config.options.file),
        new winston.transports.Console(config.options.console)
    ]
})
var logger = new HaikuLogger(loggerOptions);

module.exports = logger;