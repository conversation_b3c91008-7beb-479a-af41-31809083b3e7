const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:CreateAnimatedCrawlViz')

/** 
 * Crawler -> Worker(ScanAPI) indicating scan with 'capture screenshots' option set in config has completed 
 * and post processing can begin
 * @extends QueueMessage
*/
class CreateAnimatedCrawlViz extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} CreateAnimatedCrawlViz
     * @property {Number} scanId Scan ID (session ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} source source that sent this request (haiku)
     */
    /**
     * @param {CreateAnimatedCrawlViz} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'utils'
        this.routingKey = 'lb.request.analyze-dup-urls'
        this.msgType = CreateAnimatedCrawlViz.msgType
    }
}

module.exports = CreateAnimatedCrawlViz