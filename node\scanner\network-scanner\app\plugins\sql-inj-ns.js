const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class SQLInjAttackNS extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.SQLvulnerabilityIDNS = 'ID-sql-injection_Part2_AtVectors'


        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(sqlMatch.map((v) => {
            return v.source
        }).join('|'), "i")
        /*this.errorRegexp = new RE2(sqlErrorMatch.map((v) => {
            return v.source
        }).join('|'), "i")*/
    }

    /**
     * get array of SQL Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return sqlInjVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers', 'uri-permutation', 'json-body']
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {

        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) {
            return false
        }
        let bodycheck = _.get(attack, "result.resp.body")

        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(bodycheck)) {
            return
        }
        
        this.checkBodyForVuln(attack, this.matchRegexp, this.SQLvulnerabilityIDNS)
    }
}


// vectors & matches ...
const sqlInjVectors = [    
    `msgbox("indus");window.alert('test');`,
    `<script>alert(IF49462f696e647573666163652f746573742e7068703f757365726e616d653d3c7363726970743e616c6572742831293b3c2f7363726970743e2670617373776f72643d73756d6d6126636865636b3d687474702533612532662532667777772e7761732e696e647573666163652e636f6d25326677617376756c6e74657374253266746573742e68746d6c265375626d69743d2532302532302532304c4f47494e253230253230253230);</script>`,
    `'User-Agent: Mozilla/4.0`,
    `& ping -n 3 127.0.0.1 &`,
    `';WAITFOR DELAY '00:00:1';`,
    `msgbox("indus");window.alert('test');`,
    `'"(indus test)`,
    `images');WAITFOR DELAY '00:00:1';--`,
    `bad_bad_value'`,    
    `<<<<<<<<<<indus"test'314>>>>>=1`,
    `' AND 0 IN (SELECT SLEEP(0)) -- `,
    ` ; x || sleep 1 &`,
    `718& ping -n 3 127.0.0.1 &`,
    `/public/index.php/downloads/downloadsList/`,
    `']`,
]

const sqlMatch = [
    /Incorrect syntax near/i,
    /Sintaxis incorrecta cerca de/i,
    /Syntax error in string in query expression/i,
    /Syntax error in query expression/i,
    /Data type mismatch in criteria expression\./i,
    /Unclosed quotation mark (before|after) the character string/i,
    /PostgreSQL query failed:/i,
    /supplied argument is not a valid MySQL result resource/i,
    /(?:You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '.*?' at line .*?)/i,
    /You have an error in your SQL syntax/i,
    /MySQL server version for the right syntax to use/i,
    /An illegal character has been found in the statement/i,
    /Query failed: ERROR\: unterminated quoted string at or near/i,
    /Unexpected end of command in statement/i,
    /Query failed\:\sERROR\:\s\ssyntax error at or near/i,
    /SQL command not properly ended/i,
    /unexpected end of SQL command/i,
    /Error executing query/i,
    /SQL: numeric or value error/i,
    
    /System\.Data\.OleDb\.OleDbException/i,
    /\[SQL Server\]/i,
    /\[Microsoft\]\[ODBC SQL Server Driver\]/i,
    /\[SQLServer JDBC Driver\]/i,
    /System\.Data\.SqlClient\.SqlException/i,
    /'80040e14'/i,
    /'800a000d'/i,
    /'80040e21'/i,
    /mssql_query\(\)/i,
    /odbc_exec\(\)/i,
    /Microsoft OLE DB Provider for/i,
    /Incorrect syntax near/i,
    /Sintaxis incorrecta cerca de/i,
    /Syntax error in string in query expression/i,
    /ADODB\.Field \(0x800A0BCD\)<br>/i,
    /Procedure '[^']+' requires parameter '[^']+'/i,
    /ADODB\.Recordset\'/i,
    /Unclosed quotation mark (before|after) the character string/i,
    /\[CLI Driver\]/i,
    /\[DB26000\]/i,
    /Syntax error in query expression/i,
    /Data type mismatch in criteria expression\./i,
    /Microsoft JET Database Engine/i,
    /(\W)(PLS|ORA)-\d{5}:/,
    /PostgreSQL query failed:/i,
    /supplied argument is not a valid MySQL result resource/i,
    /pg_query\(\)\s\[/i,
    /pg_exec\(\)\s\[/i,
    /mysql_fetch_array\(\)/i,
    /on MySQL result index/i,
    /(?:You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '.*?' at line .*?)/i,
    /You have an error in your SQL syntax/i,
    /MySQL server version for the right syntax to use/i,
    /\[MySQL\]\[ODBC/i,
    /Column count doesn't match/i,
    /the used select statements have different number of columns/i,
    /Table '[^']+' doesn't exist/i,
    /com\.informix\.jdbc/i,
    /Dynamic Page Generation Error:/i,
    /An illegal character has been found in the statement/i,
    /<b>Warning<b>\: ibase_/i,
    /Dynamic SQL Error/i,
    /\[DM_QUERY_E_SYNTAX\]/i,
    /has occurred in the vicinity of:/i,
    /A Parser Error \(syntax error\)/i,
    /java\.sql\.SQLException/i,
    /Unexpected end of command in statement/i,
    /SQL syntax.*MySQL/i,
    /Warning.*mysql_.*/i,
    /valid MySQL result/i,
    /PostgreSQL.*ERROR/i,
    /Warning.*pg_.*/i,
    /valid PostgreSQL result/i,
    /Driver.*SQL[\-_\ ]*Server/i,
    /OLE DB.*SQL Server/i,
    /SQL Server.*Driver/i,
    /Warning.*mssql_.*/i,
    /Microsoft Access Driver/i,
    /JET Database Engine/i,
    /Access Database/i,
    /Oracle error/i,
    /Oracle.*Driver/i,
    /Warning.*oci_.*/i,
    /Warning.*ora_.*/i,
    /CLI Driver.*DB2/i,
    /DB2 SQL error/i,
    /Exception.*Informix/i,
    /Sybase message/i,
    /Warning.*sqlite_.*/i,
    /SQLite\/JDBCDriver/i,
    /SQLite\.Exception/i,
    /System\.Data\.SQLite\.SQLiteException/i,
    /Oracle\.DataAccess\.Client\.OracleException/i,
    /'[^']*'\sis\snull\sor\snot\san\sobject/i,
    /Could not update; currently locked by user '.*?' on machine '.*?'/i,
    /Query\sfailed\:\sERROR\:/i,
    /Query failed\:\sERROR\:\s\ssyntax error at or near/i,
    /Incorrect column name/i,
    /Can't find record in/i,
    /Unknown table/i,
    /Incorrect column specifier for column/i,
    /Invalid SQL\:/i,
    /parse error at or near/i,
    /\)\: encountered SQLException \[/i,
    /\[ODBC Informix driver\]\[Informix\]/i,
    /\[Microsoft\]\[ODBC Microsoft Access 97 Driver\]/i,
    /SQL command not properly ended/i,
    /unexpected end of SQL command/i,
    /Query failed: ERROR\: unterminated quoted string at or near/i,
    /expects parameter 1 to be resource/i,
    /ERROR: operator does not exist: unknown/i,
    /Error executing query/i,
    /unknown column/i,
    /XPATH error/i,
]

//const sqlErrorMatch = []

module.exports = SQLInjAttackNS