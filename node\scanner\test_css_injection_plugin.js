#!/usr/bin/env node

/**
 * Test script for CSS Injection Plugin - Zero False Positives
 * This script validates the ultra-strict validation logic
 */

const CSSINJ = require('./network-scanner/app/plugins/css-injection.js');

// Mock network scanner and config
const mockNetworkScanner = {
    on: () => {},
    getScanInfo: () => ({ serviceId: 'test' })
};

const mockConfig = {};

// Create plugin instance
const plugin = new CSSINJ(mockNetworkScanner, mockConfig);

console.log('🧪 Testing CSS Injection Plugin - Zero False Positives Mode\n');

// Test 1: Valid CSS Syntax Validation
console.log('Test 1: CSS Syntax Validation');
const validCSS = 'color:red;haikucsstest123456789';
const invalidCSS = 'javascript:alert(1);haikucsstest123456789';
const complexCSS = 'background:url(javascript:alert(1));haikucsstest123456789';

console.log(`✅ Valid CSS: ${plugin.isValidCSSSyntax(validCSS)}`);
console.log(`❌ Invalid CSS (JS): ${plugin.isValidCSSSyntax(invalidCSS)}`);
console.log(`❌ Complex CSS (URL): ${plugin.isValidCSSSyntax(complexCSS)}\n`);

// Test 2: Context Validation
console.log('Test 2: CSS Context Validation');
const htmlWithStyle = '<style>body{color:red;haikucsstest123456789}</style>';
const htmlWithInline = '<div style="color:blue;haikucsstest123456789">test</div>';
const htmlWithoutCSS = '<div>no css here</div>';

const vector = 'color:red;haikucsstest123456789';
console.log(`✅ Style tag context: ${plugin.isInValidCSSContext(htmlWithStyle, vector)}`);
console.log(`✅ Inline style context: ${plugin.isInValidCSSContext(htmlWithInline, 'color:blue;haikucsstest123456789')}`);
console.log(`❌ No CSS context: ${plugin.isInValidCSSContext(htmlWithoutCSS, vector)}\n`);

// Test 3: Attack Vector Validation
console.log('Test 3: Attack Vector Validation');
const vectors = [
    '<style>body{color:red;haikucsstest123456789}</style>',
    '" style="color:green;haikucsstest123456789"',
    '<style>invalid{javascript:alert(1)}</style>'
];

vectors.forEach((v, i) => {
    const isValid = plugin.isValidCSSSyntax(v);
    console.log(`Vector ${i + 1}: ${isValid ? '✅' : '❌'} - ${v.substring(0, 50)}...`);
});

console.log('\n🎯 Plugin configured for ZERO false positives!');
console.log('📋 Key Features:');
console.log('   • Ultra-strict pre-filtering (HTTP 200, HTML only, 500+ chars)');
console.log('   • Unique marker validation (haikucsstest123456789)');
console.log('   • Restricted CSS properties only');
console.log('   • Multiple validation layers');
console.log('   • Original request comparison');
console.log('   • Exact context matching');

console.log('\n⚠️  Trade-offs:');
console.log('   • May miss some edge cases');
console.log('   • Requires exact conditions');
console.log('   • Stricter than industry standard');
console.log('   • Prioritizes precision over recall');

console.log('\n✨ Expected Result: 0% false positive rate');
