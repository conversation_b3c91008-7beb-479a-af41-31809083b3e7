var fs = require('fs');
const path = require('path');
const Minimist = require('minimist');
_ = require('lodash');
const ssAPI = require('../../common/lib/sooper-scheduler-api');
const logger = require('../../common/lib/haiku-logger');
const s3Utils = require('../../common/lib/s3-utils')
const HaikuUtils = require('../../common/lib/haiku-utils');
logger.setMetadata({
    haikuProcess: 'oob-util'
});

let httpResponsePrefix = 'scanner/httpResponse/';
let oobVulnsPrefix = 'scanner/oob-vulns/';
let oobRevalidationPrefix = 'scanner/oob-revalidation/'

const EXPRSSION_UUID_SERVID_SCANID_SCANLOGID = /^([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[89aAbB][a-fA-F0-9]{3}-[a-fA-F0-9]{12})\.(\d+)\.(\d+)\.(\d+)\.(\d+)\.haiku.*$/i;

async function moveToLongTermStorage(pathPrefix, fileName, scanLogId) {
    try {
        let content = await s3Utils.getFile(pathPrefix, fileName);
        let attackInfo = JSON.parse(content.Body);
        pathPrefix = oobVulnsPrefix + scanLogId;

        if(attackInfo) {
            await s3Utils.upload(pathPrefix + '/', fileName, JSON.stringify(attackInfo));
            
            return {
                pathPrefix: pathPrefix,
                fileName: fileName
            }
        }
    } catch (error) {
        logger.error(`Can't move ${fileName} for scanId: ${scanLogId} in long term storage, reason: ${error.toString()}`);
    }

    return;
}

/**
 * Update OOB revalidation file based on vvulnerabilityInfo param
 * @param {Object} vvulnerabilityInfo object containing vulnerability related info. scanId, serviceId, scanlogId, vulnId, details etc. 
 */
async function updateOOBRevalidation(vulnerabilityInfo) {
    try {
        let isUpload = false;
        let content = await s3Utils.getFile(oobRevalidationPrefix, vulnerabilityInfo.serviceId + '.json');
        let revalidationInfo = {
            plugins: []
        };
        
        if(content) {
            revalidationInfo = JSON.parse(content.Body);

            if(revalidationInfo) {
                let existingPlugin = _.find(revalidationInfo.plugins, (plugin)=> {
                    return plugin.vulnId == vulnerabilityInfo.vulnId
                });

                if(existingPlugin) {
                    if(parseFloat(vulnerabilityInfo.scanId) > parseFloat(existingPlugin.scanId)) {
                        isUpload = true;
                        existingPlugin.scanId = parseFloat(vulnerabilityInfo.scanId);
                        existingPlugin.lastFoundDate = new Date().toISOString();
                        existingPlugin.totalScansAfterFound = 0;
                    }
                }
                else {
                    isUpload = true;
                    revalidationInfo.plugins.push({
                        vulnId: vulnerabilityInfo.vulnId,
                        lastFoundDate: new Date().toISOString(),
                        scanId: parseFloat(vulnerabilityInfo.scanId),
                        totalScansAfterFound: 0
                    });
                }
            }
        }
        else {
            isUpload = true;
            revalidationInfo.plugins.push({
                vulnId: vulnerabilityInfo.vulnId,
                lastFoundDate: new Date().toISOString(),
                scanId: parseFloat(vulnerabilityInfo.scanId),
                totalScansAfterFound: 0
            });
        }
        
        if(isUpload) {
            await s3Utils.upload(oobRevalidationPrefix, vulnerabilityInfo.serviceId + '.json', JSON.stringify(revalidationInfo));
        }
    } catch (error) {
        logger.error(`Error occured in updateOOBRevalidation, reason: ${error.message}`);
    }
}

async function getVulnerabilityInfo(dnsLogRecord) {
    let DNSUID = dnsLogRecord['full-id'];

    let vulnerabilityInfo = {
        details: {
            type: "dns",
            founddate: new Date(dnsLogRecord.timestamp).toISOString(),
            triggeredIP: dnsLogRecord['remote-address'],
        }
    }

    const match = DNSUID.match(EXPRSSION_UUID_SERVID_SCANID_SCANLOGID);

    if (match) {
        const [, uuid, serviceId, scanId, scanlogId, vulnId] = match;

        vulnerabilityInfo.uuid = uuid;
        vulnerabilityInfo.serviceId = serviceId;
        vulnerabilityInfo.scanId = scanId;
        vulnerabilityInfo.scanlogId = scanlogId;
        vulnerabilityInfo.vulnId = vulnId;
        vulnerabilityInfo.result = `DNS query for ${match.input} on ${new Date(dnsLogRecord.timestamp).toISOString()}`

        let pathPrefix = 'scanner/oob-attacks/' + vulnerabilityInfo.scanlogId;
        let fileName = `${vulnerabilityInfo.uuid}_${vulnerabilityInfo.scanlogId}.json`;

        //Ask scanner to start storing respective OOB plugin attack information for future scans. Scanner will refer this file and 
        //set speficic OOB plugin uploadAttackToS3 property to true.
        await updateOOBRevalidation(vulnerabilityInfo);
        let storageInfo = await moveToLongTermStorage(pathPrefix, fileName, vulnerabilityInfo.scanlogId);

        if(storageInfo) {
            let vulnerability = HaikuUtils.getVulnerabilityById(vulnerabilityInfo.vulnId);

            if(!vulnerability) {
                return null;
            }

            vulnerabilityInfo.productionReady = vulnerability.productionReady;
            
            vulnerabilityInfo.details.attack = {
                prefix: storageInfo.pathPrefix,
                name: storageInfo.fileName
            }
        }
        else {
            return null;
        }
    } else {
        return null;
    }

    if (isNaN(vulnerabilityInfo.serviceId) || isNaN(vulnerabilityInfo.scanId) || isNaN(vulnerabilityInfo.scanlogId) || isNaN(vulnerabilityInfo.vulnId)) {
        return null;
    }

    vulnerabilityInfo.details.dnslog = Object.assign({}, dnsLogRecord);

    return vulnerabilityInfo;
}

/** 
 * Out of band vulnerability check util class. i.e. log4j vuln
 */
class OOBVulCheckUtil {
    constructor(oobVulnFilePath) {
        this.oobVulnFilePath = oobVulnFilePath;
    }

    async checkForVuln() {
        if (!fs.existsSync(this.oobVulnFilePath)) {
            logger.info(`OOBVulCheckUtil checkForVuln error, File not found at location ${this.oobVulnFilePath}`);
            return;
        }

        try {
            let oobVulnFileContent = fs.readFileSync(this.oobVulnFilePath, 'utf8');
            let dnsLogRecords = JSON.parse(oobVulnFileContent);

            for (let index = 0; index < dnsLogRecords.length; index++) {
                try {
                    let dnsLogRecord = dnsLogRecords[index];

                    if (!dnsLogRecord['q-type']) {
                        logger.info(`Skipping dns log record due to one of the required q-type field doesn't exist. ${JSON.stringify(dnsLogRecord)}`);
                        continue;
                    }

                    if (!dnsLogRecord['unique-id']) {
                        logger.info(`Skipping dns log record due to one of the required unique-id field doesn't exist. ${JSON.stringify(dnsLogRecord)}`);
                        continue;
                    }

                    if (dnsLogRecord['q-type'] == 'A') {
                        let vulnerabilityInfo = await getVulnerabilityInfo(dnsLogRecord);

                        if (!vulnerabilityInfo) {
                            logger.info(`Skipping dns log record due to invalid pattern -> ${dnsLogRecord['unique-id']}`);
                            continue;
                        }

                        logger.info(`Found OOB vuln with details ${JSON.stringify(vulnerabilityInfo)}`);
                        await ssAPI.oobVulnerabilityFound(vulnerabilityInfo);
                    }
                } catch (error) {
                    logger.error(`Unable to process dns log record. Reason: ${error.message}`);
                }
            }
        } catch (error) {
            logger.error(`Unable to process dns log record, reason: ${error.message}`);
        }
    }
}

// --- Main code
let argv = Minimist(process.argv.slice(2), {
    boolean: true,
    alias: {
        f: "oob-vuln-file"
    }
})

async function main() {
    let oobVulnFilePath = argv['oob-vuln-file'];

    if (!oobVulnFilePath) {
        logger.error(`Required "oob-vuln-file" argument is not passed.`);
        return;
    }

    logger.log('info', `running oob vuln check with args: --oob-vuln-file=${oobVulnFilePath}`);
    let oobVulCheckUtil = new OOBVulCheckUtil(oobVulnFilePath);
    await oobVulCheckUtil.checkForVuln();
}

if (argv['oob-vuln-file']) {
    console.info(`arguments received for oob-vuln-file=${argv['oob-vuln-file']}`);
    // main / module exports based on command line args
    (async function (params) {
        await main();
    })();
} else {
    module.exports = OOBVulCheckUtil
}