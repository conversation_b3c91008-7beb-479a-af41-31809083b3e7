let utils = require('../ifc-utils.js')
const IfcScannerDefault = require('./ifc-scanner-default.js')

module.exports = {
    createScanner: function (config) {
        switch (config.scanner) {
            case 'ifc-scanner-default':
                return new IfcScannerDefault(config)
                break;

            default:
                utils.log('Unknown scanner: ', this.config.scanner)
        }
    }
}