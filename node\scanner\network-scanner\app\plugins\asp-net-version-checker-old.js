const debug = require('debug')('ASPNETVersionChecker')
const NetworkAttack = require('./network-attack')
const URL = require('url').URL
const _ = require('lodash')


class ASPNETVersionChecker extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.ASPNETVersionDisclosureVulnID = 'ID-ASP-NET-Version'

        const aspNetVersionHeaders = ['x-aspnet-version']
        this.headersToIterate = [{
            headers: aspNetVersionHeaders,
            vulnId: 'ID-asp-net-version-headers'
        }]
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        // checking for request specific plugin data in specific functions
        this.checkAspNetVersionDisclosureVulnerability(attack) // asp dot net version discousre is specialzied case of info disclousre
    }


    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     *
     */
    checkAspNetVersionDisclosureVulnerability(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        let headers = _.get(attack, 'result.resp.httpResponse.headers', {})

        for (let headerInfo of this.headersToIterate) {
            if (pluginDataForRequest[headerInfo.vulnId]) {
                continue
            }
            let vulns = [];
            for (let headerName of headerInfo.headers) {
                let val = headers[headerName];

                if (val != null) {
                    let vuln = {
                        details: {
                            header: headerName,
                            value: val
                        }
                    }
                    vulns.push(vuln);
                }
            }
            if (vulns.length > 0) {
                // add this vulnerability to list of vulnerabilities.
                this.addVulnerabilitytoResult(attack, headerInfo.vulnId, vulns)
                pluginDataForRequest[headerInfo.vulnId] = true
            }
        }
    }

}
module.exports = ASPNETVersionChecker