const QueueMessage = require('../queue-message')
const path = require('path')
const debug = require('debug')('Messages:AnalyzeDupUrls')

/** 
 * Crawler -> Worker(ScanAPI) indicating scan with analyze option set in config has completed and analysis can begin
 * @extends QueueMessage
*/
class AnalyzeDupUrls extends QueueMessage {
    /**
     * gets the message type which is always the filename to be able to create message from type.
     */
    static get msgType() {
        return path.basename(__filename, '.js')
    }
    
    /**
     * @typedef {Object} AnalyzeDupUrlsMsgContent
     * @property {Number} scanId Scan ID (session ID)
     * @property {Number} scanlogId Scan log ID of this scan
     * @property {string} source source that sent this request (haiku)
     */
    /**
     * @param {AnalyzeDupUrlsMsgContent} content - JSON content of messages 
     */
    constructor(content) {
        super(content)
        this.exchange = 'utils'
        this.routingKey = 'lb.request.analyze-dup-urls'
        this.msgType = AnalyzeDupUrls.msgType
    }
}

module.exports = AnalyzeDupUrls