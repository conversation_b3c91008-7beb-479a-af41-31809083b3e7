// Helper class to add cheat sheets
const utils = require('../ifc-utils.js')

class CheatSheetInfo {
    constructor(name, action, matchType = 'all-exist') {
        this.name = name
        this.action = action
        this.matchType = matchType

        switch ( matchType.toLowerCase() ) {
            case 'all-exist':
                this.matches = this.allElementsExist
                break

            case 'all-visible': // will also scroll into view for each element if necc.
                this.matches = this.allElementsVisible
                break

            default:
                utils.log( `CheatSheetInfo() -> unknown matchType ${matchType}`)
        }
    }

    getAction() {
        return this.action
    }

    // If all elements exist on the page, assume we have a match.
    async allElementsExist(browser) {
        let xpaths = this.action.getXPaths? this.action.getXPaths() : this.action.getXPath()
        if (!xpaths) {
            return false
        }

        //convert JSON to b64 so it can be embedded safely in the jscode string
        let xpathsStr = Buffer.from(JSON.stringify(xpaths)).toString('base64')
        let jscode = `indusfaceRenderer.allElementsExist('${xpathsStr}')`
        let result = await utils.timedPromise( browser.webContents.executeJavaScript(jscode), false )
        return result
    }

    // If all elements exist on the page, assume we have a match.
    async allElementsVisible(browser) {
        let xpaths = this.action.getXPaths? this.action.getXPaths() : this.action.getXPath()
        if (!xpaths) {
            return false
        }

        //convert JSON to b64 so it can be embedded safely in the jscode string
        let xpathsStr = Buffer.from(JSON.stringify(xpaths)).toString('base64')
        let jscode = `indusfaceRenderer.allElementsVisible('${xpathsStr}')`
        let result = await utils.timedPromise(browser.webContents.executeJavaScript(jscode), false)
        return result
    }
}

module.exports = CheatSheetInfo    