const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

//remote code execution vulnerability exists in the HTTP protocol stack (HTTP. sys) 
//that is caused when HTTP. sys improperly parses specially crafted HTTP requests.
//With added payload in request reader, we check response code 416 and reason to be “Requested Range Not Satisfiable“
class HttpSysRCE extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.HttpSysRCE = 'ID-http-sys-rce'

    }

    getAttackVectors() {
        return uripathVectors
    }

    /** 
     * Only attack header: Referer, user agent, host and origin
     * @param {parameterizedDelegate} parameterizedDelegate protocol specific delegate to use to tamper param
     * @override    
     */
    // Below attack config set to attack only root url with our vector
    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }


    async performNetworkAttack(attack) {
        let serverHeader = _.get(attack, 'originalRequest.httpResponse.headers["server"]')
        let xAspNetVersion = attack.originalRequest.httpResponse.headers['x-aspnet-version']
        let xAspNetMvcVersion = attack.originalRequest.httpResponse.headers['x-aspnetmvc-version']
        let xPoweredBy = _.get(attack, 'originalRequest.httpResponse.headers["x-powered-by"]', "")

        if (!/nginx/i.test(serverHeader) && (/iis|microsoft|asp/i.test(serverHeader) || xAspNetVersion != undefined || xAspNetMvcVersion != undefined || /asp.net/i.test(xPoweredBy))) {
            attack.httpRequest.method = "GET"
            attack.httpRequest.headers['Range'] = "bytes=0-18446744073709551615"
            return await super.performNetworkAttack(attack)
        }
        return false
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    wantProcessAttackResponse(attack) {
        // check if attack reqeust came from this plugins attack only
        if (attack.pluginName == this.getName()) {
            return true
        }
        return false
    }

    processAttackResponse(attack) {

        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        //if already found dont check further
        if (pluginDataForRequest.HttpSysRCE == true) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let statusMessage = _.get(attack, 'result.resp.httpResponse.statusMessage')
        let body = _.get(attack, 'result.resp.httpResponse.body')
        //Requested range not satisfiable

        let check_conditions = /Requested range not satisfiable/i.test(statusMessage) || /Requested range not satisfiable/i.test(body)

        if (statusCode && statusCode == 416 && check_conditions) {
            let details = { "vulnerability": "exist" }
            this.addVulnerabilitytoResult(attack, this.HttpSysRCE, details)
            pluginDataForRequest.HttpSysRCE = true
        }

    }
    //Request: URI path,Header: Method, Range. Response: Status code and its mesaage(Requested range not satisfiable), Server, or body: Requested range not satisfiable
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.HttpSysRCE) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ["Range"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["statusCode", "statusMessage", "server"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['Requested range not satisfiable']);
    }
}


const uripathVectors = [
    `/Iisstart.htm`,
    `/welcome.png`,
    `/iis-85.png`,
    `/resources/logo.png`,
    `/favicon.ico`
]


module.exports = HttpSysRCE