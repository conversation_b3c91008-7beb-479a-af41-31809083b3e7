const debug = require('debug')('XMLRPCPlugin')
const NetworkAttack = require('./network-attack')
const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * XML RPC plugin strategy:
 * Here for any url on appdending /xmlrpc.php if response appears then will send a post request with 
 * an xml content and if we receive few params in response then will consider it vulnerable
 */
class XMLRPCPlugin extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-xml-rpc-detected'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            skipRoot: false,
            maxPathComponents: 0,
            clearQueryParams: true,
            addSlashBeforeAttack: false,
            haveSlashAfterAttack: 'never'
        });
    }

    /**
     * get array of LFI attack vectors
     * @override
     */
    getAttackVectors() {
        return XMLVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        attack.httpRequest.method = "POST"
        attack.httpRequest.body = "<methodCall><methodName>system.listMethods</methodName><params></params></methodCall>"
        return await super.performNetworkAttack(attack)
    }

    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let pluginDataForRequest = this.getPluginScopedStore(attack)
        if (pluginDataForRequest.xmlRpcDetected) {
            return
        }

        let bodycheck = _.get(attack, 'result.resp.body')
        let xmlContentType = _.get(attack, 'result.resp.httpResponse.headers["content-type"]');

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (xmlContentType != undefined && statusCode == "200") {
            if (xmlContentType.includes("text/xml") && /<value>\s*<string>\s*(?:system|demo|pingback|mt|metaWeblog|blogger|wp)\..+?<\/string>\s*<\/value>/igs.test(bodycheck)) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
                pluginDataForRequest.xmlRpcDetected = true
                return
            }
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ["method", "body"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["content-type"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.body', `text`, ['pingback.ping', 'wp.getUserBlogs', 'wp.getCategories', 'metaWeblog.getUsersBlogs']);

    }
}

const XMLVectors = [
    //below path vectors only to be attacked in core url
    `/xmlrpc.php`,
    `/wordpress/xmlrpc.php`,
    `/cms/xmlrpc.php`,
    `/blog/xmlrpc.php`
]

module.exports = XMLRPCPlugin