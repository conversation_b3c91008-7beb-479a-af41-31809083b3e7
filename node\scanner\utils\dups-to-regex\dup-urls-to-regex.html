<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <title>Get regex from dup url list</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.8.0/css/bulma.min.css">
    <link rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@creativebulma/bulma-collapsible@1.0.4/dist/css/bulma-collapsible.min.css">
    <script defer src="https://use.fontawesome.com/releases/v5.3.1/js/all.js"></script>
    <script defer src="https://use.fontawesome.com/releases/v5.3.1/js/all.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@creativebulma/bulma-collapsible@1.0.4/dist/js/bulma-collapsible.min.js">
    </script>
    <script src="https://cdn.jsdelivr.net/gh/google/code-prettify@master/loader/run_prettify.js"></script>
    <script src="https://code.jquery.com/jquery-3.4.1.min.js"
        integrity="sha256-CSXorXvZcTkaix6Yvo6HppcZGetbYMGWSFlBw8HfCJo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/autosize@4.0.2/dist/autosize.min.js"></script>
    <script src="/utils/dups-to-regex/dup-urls-to-regex.js"></script>
    <style>
        .urllist {
            height: 40vh;
        }
    </style>
</head>

<body>
    <!-- class="has-navbar-fixed-top"> -->
    <!-- bulma stuff -->
    <title>Create regex from duplicate URLs</title>

    <!-- heading -->
    <!-- <h4 class="subtitle has-background-primary has-text-centered">Create regex from duplicate URLs and test regex
        against URLs</h4> -->

    <!-- Tabs -->
    <div class="tabs is-centered is-toggle is-toggle-rounded" style="margin-top: .5rem;margin-bottom: .5rem;">
        <ul>
            <li class="tab is-active" onclick="openTab(event, 'generate-regex-tab')"><a>Generate Regex</a></li>
            <li class="tab" onclick="openTab(event, 'test-regex-tab')"><a>Test Regex</a></li>
        </ul>
    </div>

    <!-- Generate Regex Tab -->
    <div id="generate-regex-tab" style="margin-top: .5rem;margin-bottom: .5rem;padding-top:0" class="box content-tab">
        <div class="field is-grouped is-horizontal">
            <!-- <div class="field-body"> -->
            <!-- <div class="field is-grouped"> -->
            <p class="control">
                <input class="checkbox" id="show-steps" type="checkbox"></input>
            </p>
            <p class="control">
                <label class="label"> Show Steps </label>
            </p>
            <p class="control">
                <input class="input is-small" style="width:4em;" min="0" max="15" id="steps-secs"
                    value="1" type="number"> </p>
            <p class="control">
                <label class="label"> seconds</label>
            </p>
            <p class="control">
                <button id="create" class="button is-link is-info">create regex</button>
            </p>
            <!-- </div> -->
            <!-- </div> -->

        </div>

        <div class="box is-paddingless generated-container" style="display:none">
            <div class="field">
                <label class="label">Generated regex</label>
                <div class="control">
                    <pre style="height: auto; white-space:pre-wrap;" rows="1" class="textarea is-small is-info"
                        id='generated-regex'></pre>
                </div>
                <label class="label" id='status'></label>
            </div>
        </div>

        <div class="columns gapless">
            <!--URLs column-->
            <div class="column is-6">
                <article class="message is-dark">
                    <div class="message-header">
                        <p>Duplicate URLs</p>
                    </div>
                    <!-- URL list  -->
                    <textarea class="textarea is-primary urllist"
                        placeholder="paste complete JS array of urls OR list of newline seperated urls here..." id="urls"></textarea>
                </article>
                <article class="message is-dark">
                    <div class="message-header">
                        <p>Other URLs (FP check)</p>
                    </div>
                    <!-- URL list  -->
                    <textarea class="textarea is-primary urllist"
                        placeholder="paste complete JS array of urls OR list of newline seperated urls here..." id="non-dup-urls"></textarea>
                </article>
            </div>
            <div class="column is-6">
                <article id='processed-urls-accordion' class="message is-dark">
                    <div class="message-header">
                        <p>Processed URLs (canonacalized)</p>
                        <!-- <a href="#processed-urls-body" data-action="collapse" aria-label="expand/collapse">
                            <span class="icon">
                                <i class="fas fa-chevron-circle-down" aria-hidden="true"></i>
                            </span>
                        </a> -->
                    </div>
                    <div id="processed-urls-body" class="message-body is-paddingless is-info is-active"
                        data-parent="processed-urls-accordion" data-allow-multiple="true">
                        <!-- analyzed urls -->
                        <div class="message is-success">
                            <div class="message-body is-paddingless ">
                                <label class="is-size-6"> Matched URLS</label>
                                <div class="box" id="matched-urls">
                                </div>
                            </div>
                        </div>
                        <div class="message is-danger">
                            <div class="message-body is-paddingless ">
                                <label class="is-size-6"> FP URLS</label>
                                <div class="box" id="fp-urls">
                                </div>
                            </div>
                        </div>
                        <div class="message is-warning">
                            <div class="message-body is-paddingless ">
                                <label class="is-size-6"> FN URLS</label>
                                <div class="box" id="fn-urls">
                                </div>
                            </div>
                        </div>
                    </div>
                </article>

                <!-- <article id="output-accordion" class="message is-dark">
                    <div class="message-header" id="output-accordion-header">
                        <p>Output</p>
                        <a href="#output-body" data-action="collapse" aria-label="expand/collapse">
                            <span class="icon">
                                <i class="fas fa-angle-down" aria-hidden="true"></i>
                            </span>
                        </a>
                    </div>
                    <div id="output-body" class="message-body is-collapsible  is-active" data-parent="output-accordion"
                        data-allow-multiple="true">

                        <textarea class="textarea is-info" rows="5" readonly="true"
                            placeholder="analyzed urls will come here..." id="output"></textarea>
                    </div>
                </article> -->
            </div>
        </div>
    </div>

    <!-- Test Regex Tab -->
    <div id="test-regex-tab" class="box content-tab" style="display:none">
        <div class="field">
            <label class="label">enter regex to test here</label>
            <div class="control">
                <input id='test-regex' class="input is-primary" type="text" placeholder="regex">
            </div>
        </div>
        <div class="field">
            <label class="label">enter URL to test here</label>
            <div class="control">
                <input id='test-url' class="input is-primary" type="text" placeholder="URL...">
            </div>
            <p class="help is-info">URL will be canaonacalized and matched</p>
        </div>
        <div class="field">
            <label class="label">canonacalized URL</label>
            <div class="control">
                <input id='can-url' readonly="readonly" class="input is-warning" type="text" placeholder="URL...">
            </div>
        </div>

        <!-- matches/not -->
        <div id="matches" class="field has-text-success" style="display:none">
            <div class="control">
                <span class="icon">
                    <i class="far fa-thumbs-up"></i>
                </span>
                <span>
                    <b>Regex matches URL</b>
                </span>
            </div>
        </div>
        <div id="does-not-match" class="field has-text-danger" style="display:none">
            <div class="control">
                <span class="icon">
                    <i class="far fa-thumbs-down"></i>
                </span>
                <span>
                    <b> Regex does not match URL</b>
                </span>
            </div>
        </div>

        <!-- run the test regex -->
        <div class="field">
            <div class="control">
                <button id="testGeneratedRegex" class="button is-link is-info">test regex</button>
            </div>
        </div>

    </div>


    <!-- accordian stuff -->
    <script>
        bulmaCollapsible.attach('.is-collapsible')
    </script>

    <!-- bulma stuff -->

</body>

</html>