const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')

class XpathInjAttack extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-xpath-injection'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(xpathinjMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of Xpath attack vectors
     * @override
     */
    getAttackVectors() {
        return xpathinjVectors
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'http-headers', 'uri-permutation', 'json-body']
    }

    /**
     * @param {attack} attack the attack that was performed
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName == this.getName()) {
            this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
        }
    }
}


const xpathinjVectors = [
    `a'b"c`,
    `<!--`,
    `-->`,
    `Abc&aa></`,
]

const xpathinjMatch = [
    /xmlXPathEval: evaluation failed/,
    /SimpleXMLElement::xpath\(\)/,
    /XPathException/,
    /MS\.Internal\.Xml\./,
    /Unknown error in XPath/,
    /org\.apache\.xpath\.XPath/,
    /A closing bracket expected in/,
    /An operand in Union Expression does not produce a node-set/,
    /Cannot convert expression to a number/,
    /Document Axis does not allow any context Location Steps/,
    /Empty Path Expression/,
    /Empty Relative Location Path/,
    /Empty Union Expression/,
    /Expected '\)' in/,
    /Expected node test or name specification after axis operator/,
    /Incompatible XPath key/,
    /Incorrect Variable Binding/,
    /libxml2 library function failed/,
    /xmlsec library function/,
    /error '80004005'/,
    /A document must contain exactly one root element\./,
    /Expression must evaluate to a node-set\./,
    /Expected token '\]'/,
    /<p>msxml4.dll<\/font>/,
    /<p>msxml3.dll<\/font>/,
    /4005 Notes error: Query is not understandable/,
    /DOMXPath::/,
]

module.exports = XpathInjAttack