const utils = require('../ifc-utils.js')

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// content match action - see if innerText contains the text. Will do case insenitive, substring match.
class ContentMatchAction {
    constructor(xpath, matchString, annotation = '') { // annotation is optional
        this.xpath = xpath
        this.matchString = matchString
        this.annotation = annotation
    }

    get actionType() {
        return 'content-match'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.xpath, this.matchString, this.annotation]
        }
    }

    getXPath() {
        return this.xpath
    }

    async execute(executionContext) {
        let browser = executionContext.browser
        let executer = executionContext.executer

        if (!await utils.doesElementExist(this.xpath, browser)) {
            utils.log("\tSkipping: element does not exist")
            return false
        }

        // element should be visible and match the content
        let encXPath = utils.encode(this.xpath)
        let jscode = `indusfaceRenderer.elementContainsText('${encXPath}', '${this.matchString}')`
        let matched = await utils.timedPromise(browser.webContents.executeJavaScript(jscode, true), false)
        return matched
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} matchStr=${this.matchString} ${this.annotation} xpath=<${this.xpath}>`
    }
}

module.exports = ContentMatchAction