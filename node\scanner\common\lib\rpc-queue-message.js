// RPC style request/response message
const QueueMessage = require('./queue-message')
const debug = require('debug')('Messages:RpcQueueMessage')
const logger = require('./haiku-logger')
const HaikuUtils = require('./haiku-utils')

/**
 * RPC style request/response message
 */
class RpcQueueMessage extends QueueMessage {

    /**
     * @param {JSON} content the message content
     */
    constructor(content) {
        super(content)
        this.isRpc = true
    }

    /**
     * Perform a Rabbit MQ RPC style message. It will wait for response and ensure that the 
     * message type of the response is the same as the request.
     * @param {messageQueue} msgQ - The message queue instance
     * @param {Number} timeout How long to wait for reply in milliseconds. Will return default val after that
     * @param {Number} defaultVal What to return on timeout
     */
    async rpcRequest(msgQ, timeout = 3 * 60 * 1000, defaultVal = null) {
        // heres where any common envelope can be done.
        let options = {
            type: this.msgType
        }

        let content = this.serialize(options)
        let {
            correlationId,
            promise
        } = await msgQ.request(this.exchange, this.routingKey, content, options)
        let mqMsg
        try {
            mqMsg = await HaikuUtils.timedPromise(promise, timeout, null)
        } catch (err) {
            logger.log('error', `Request msg type ${this.msgType} errored out ${err.toString()}`, HaikuUtils.getMetadataForLog(content))
        }
        
        // see if we got a timeout
        if (!mqMsg) {
            msgQ.discardRequest(correlationId)
            return defaultVal
        }

        // crack open the msg and get the response
        let responseMsg = RpcQueueMessage.createMessage(mqMsg)

        // type should be the same as this is a response
        if (this.msgType != responseMsg.msgType) {
            logger.log('error', `Request msg type ${this.msgType} does not match response msg type ${responseMsg.msgType}`, HaikuUtils.getMetadataForLog(content))
            return null
        }

        let response = responseMsg ? responseMsg.getContent() : null
        return response
    }

    /**
     * Reply to a Rabbit MQ RPC style message. 
     * @param {messageQueue} msgQ - The message queue instance
     * @param {JSON} replyContent - the reply JSON object
     */
    rpcReply(msgQ, replyContent) {
        if (this.msgType != this.mqMsg.properties.type) {
            logger.log('warn', `Reply msg type ${this.msgType} does not match request msg type ${this.mqMsg.properties.type}`, HaikuUtils.getMetadataForLog(replyContent))
            // but go ahead anyway since someone is waiting for a reply.
        }

        let options = {
            type: this.msgType
        }

        let content = this.serialize(options, replyContent)
        msgQ.reply(this.mqMsg, content, options)
    }

}

module.exports = RpcQueueMessage