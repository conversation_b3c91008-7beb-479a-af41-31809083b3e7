exports.MACHINE_TYPES = {
    SCANNER: 'scanner',
    CRAWLER: 'crawler',
    SCANAPI: 'scanapi',
}

exports.AUTH_TYPES = {
    NO_AUTH: 'noauth',
    BASIC: 'basic',
    API_KEY: 'apikey'
}

exports.SCAN_TYPES = {
    AA: 'aa',
    MALWARE: 'malware',
    DEFACEMENT: 'defacement'
}

exports.MALWARE_ENGINE = {
    CLAMAV: 'clamav'
}

exports.SSO_PROVIDERS = {
    MICROSOFT: "microsoft",
    FACEBOOK: "facebook",
    GOOGLE: "google",
    TWITTER: "twitter",
    GITHUB: "github"
};

exports.VULNERABILITY_SEVERITY = {
    CRITICAL: "critical",
    HIGH: "high",
    MEDIUM: "medium",
    LOW: "low",
    INFO: "info"
};

exports.SSO_URLS = {
    MICROSOFT: [
        "https://login.microsoftonline.com",
        "https://login.live.com"
    ]
}

exports.SITE_TYPE = {
    WESBITE: 'website',
    API: 'api'
}

exports.INPUT_TYPE_MOUSEDOWN = 'mousedown';
exports.INPUT_TYPE_MOUSEUP = 'mouseup';
exports.INPUT_TYPE_MOUSEMOVE = 'mousemove';
exports.INPUT_TYPE_CLICK = 'click';
exports.INPUT_TYPE_DBLCLICK = 'dblclick';
exports.INPUT_TYPE_KEYDOWN = 'keydown';
exports.INPUT_TYPE_KEYUP = 'keyup';
exports.INPUT_TYPE_KEYPRESS = 'keypress';
exports.INPUT_TYPE_WHEEL = 'wheel';
exports.INPUT_TYPE_TOUCHSTART = 'touchstart';
exports.INPUT_TYPE_TOUCHEND = 'touchend';
exports.INPUT_TYPE_TOUCHMOVE = 'touchmove';
exports.INPUT_TYPE_CHANGE = 'change';
exports.INPUT_TYPE_INPUT = 'input';

exports.ERROR_CODES = {
    "ERR_INVALID_URL" : 'ERR_INVALID_URL',
}