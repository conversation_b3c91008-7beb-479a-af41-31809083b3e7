input {
 elasticsearch {
    hosts => "https://search-haiku-logs-eevbprg76qfqkv7niimst5oqq4.ap-south-1.es.amazonaws.com"
    index => "haikulogs-*"
    query => '
{
    "_source": ["message", "timestamp"],
    "query": {
        "bool": {
            "filter": [{
                    "term": {
                        "scanId": "2282405"
                    }
                },
                {
                    "term": {
                        "haikuProcess": "crawler"
                    }
                }
            ]
        }
    }
}'

# previous query
    # query => '
    # {
    #   "_source": ["message", "timestamp","haikuProcess", "scanId"],
    #   "query": {
    #     "bool": {
    #       "must": [
    #         {
    #           "match": {
    #             "message": "request"
    #           }
    #         }
    #       ],
    #       "filter": [
    #         {
    #           "term": {
    #             "scanId": "1457586"
    #           }
    #         },
    #         {
    #           "range": {
    #               "timestamp": {
    #                 "gte": "2019-09-23T16:46:34.000Z",
    #                 "lte": "2019-09-23T16:46:35.000Z"
    #               }
    #             }        
    #         }
    #       ]
    #     }
    #   }
    # }'

    # ONE MORE: get all the jobcompleted logs -> gives the crawl metrics.
    # query => '
    # {
    #   "_source": ["message", "timestamp","haikuProcess"],
    #   "query": {
    #     "bool": {
    #       "must": [
    #         {
    #           "match_phrase": {
    #             "message": "jobsession-completed"
    #           }
    #         }
    #       ],
    #       "filter": [
    #         {
    #           "term": {
    #             "scanId": "1471706"
    #           }
    #         }
    #       ]
    #     }
    #   }
    # }'


  }
}
output {
  file {
    # This is path where we store output.   
    path => "./search_results.json"
  }
# ,csv {
#     # elastic field name
#     fields => ["message", "timestamp", "haikuProcess"]  
#     # This is path where we store output.   
#     path => "./csv-export.csv"
#   }  
}