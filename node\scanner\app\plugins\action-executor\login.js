const utils = require('../../ifc-utils.js')
const _ = require('lodash')
const CheatSheetInfo = require('../../datastructure/cheatsheet-info.js')
const createActionList = require('../../datastructure/create-action-list')
const s3Utils = require('../../../common/lib/s3-utils')
const path = require("path")

class CheatSheetLogin {
    constructor(actionExecutor) {
        this.config = actionExecutor.config

        // init counters
        this.failedLogins = 0

        this.cheatsheets = []
        this.matchedCheatsheets = new Set()
        this.addCheatsheets()

        // event handlers
        actionExecutor.on('pre-action-execute', this.preActionExecute.bind(this))
        actionExecutor.on('post-action-execute', this.postActionExecute.bind(this))
    }

    async addCheatsheets() {

        // Add cheatsheets - each cheatsheet is a list of actions that need to be performed
        let guidedScans = _.get(this, 'config.siteConfig.pluginData.guidedScans', []);

        for (let guidedScan of guidedScans) {
            // iterate all the actionConfigs. Multiple action Configs for a website. Each action configs 
            //has multiple actions. Add each actions in the actionList based on action types. 
            //Annotation need to be added later.
            let actionList = createActionList(guidedScan)
            if (actionList) {
                this.cheatsheets.push(new CheatSheetInfo(guidedScan.name || this.config.mainUrl, actionList))
            }
        }
    }    

    async preActionExecute(action, executionContext) {
        await this.checkForLoginPage(executionContext)
    }

    async postActionExecute(action, executionContext) {
        await this.checkForLoginPage(executionContext)
    }

    async checkForLoginPage(executionContext) {
        let isGuidedActionTook = false;
        // run through all cheatsheets. If all elements of a cheatsheet exist,
        // then return those actions.

        // ** TODO - KK: fix name and evrything - this is now cheatsheet not login
        for (let cheatSheet of this.cheatsheets) {
            let result = await cheatSheet.matches(executionContext.browser)
            if (result) {
                // do the login action
                await executionContext.executer.triggerAction(cheatSheet.getAction(), executionContext.executer.getNoPluginContext(executionContext))
                executionContext.pluginTookAction = true
                isGuidedActionTook = true;
            }
        }

        return isGuidedActionTook
    }
}

module.exports = CheatSheetLogin