const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const RegExpVari = require('./generic-regexp');
const HaikuUtils = require('../../../common/lib/haiku-utils')
// const fetch = require('node-fetch-npm')
const net = require('net');
const request = require('request');

class CSDAttack extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-csd-attack'
    }

    processNewRequest(request) {
        let redirects = _.get(request, 'httpResponse.redirects.length', '0')
        if (redirects > 0) { return false }
        let Connection = _.get(request, 'httpResponse.headers.connection', '')
        if (Connection != "keep-alive") { return false }
        let staCode = request.httpResponse.statusCode
        let method = request.httpRequest.method
        if (method == "GET" && staCode === 200) {
            let attack = {
                httpRequest: _.cloneDeep(request.httpRequest),
                originalRequest: request,
                encoding: "raw"
            }
            attack.httpRequest.headers['Connection'] = "keep-alive"
            attack.httpRequest.headers['Content-length'] = "0"
            delete attack.httpRequest.headers['content-type']
            attack.httpRequest.method = "POST"
            let uriRaw = new URL(attack.httpRequest.uri)
            attack.httpRequest.body
            let request2 = `GET ${uriRaw.href.replace(uriRaw.origin, '')} HTTP/1.1\r\nConnection: keep-alive\r\n`
            for (let [key, value] of Object.entries(attack.originalRequest.httpRequest.headers)) {
                request2 += `${key}: ${value}\r\n`;
            }
            request2 += `Haiku: test`
            attack.httpRequest.body = request2
            uriRaw.pathname = '/haiku.txt'
            attack.httpRequest.uri = uriRaw.href
            this.performNetworkAttack(attack)
        }
        return false
    }

    wantProcessAttackResponse(attack) {
        let method = attack.httpRequest.method
        let httpVersion = _.get(attack, 'result.resp.fullResponse.httpVersion', '')
        if (httpVersion >= 2) { return false }
        if (attack.pluginName == 'Original Crawler Request' && method == "GET") {
            let Connection = _.get(attack, 'result.resp.httpResponse.headers.connection', '')
            if (Connection != "keep-alive") { return false }
            let staCode = _.get(attack, 'result.resp.httpResponse.statusCode')
            if (!staCode === 200) {
                return false
            }
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            if (redirects != 0) {
                return false
            }
            let ResBody = _.get(attack, "result.resp.body", '')
            let ResCL = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
            if (!ResCL) {
                attack.result.resp.fullResponse.headers["content-length"] = Buffer.from(ResBody, 'utf-8').length
                // ResCL = Buffer.from(ResBody, 'utf-8').length
            }
        }
        if (attack.pluginName == this.getName()) {
            let redirects = _.get(attack, 'result.resp.httpResponse.redirects.length', '')
            let OriRedirects = _.get(attack, 'originalRequest.httpResponse.redirects.length', '')
            if (redirects != OriRedirects) {
                return true
            }
            let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '')
            let AtkstatusCode = _.get(attack, 'result.resp.httpResponse.statusCode', '')
            if (OristatusCode != AtkstatusCode) {
                return true
            }
            let ResBody = _.get(attack, "result.resp.body", '')
            let ResCL = _.get(attack, 'result.resp.httpResponse.headers["content-length"]')
            if (!ResCL) {
                attack.result.resp.fullResponse.headers["content-length"] = Buffer.from(ResBody, 'utf-8').length
                // ResCL = Buffer.from(ResBody, 'utf-8').length
            }
            let AtClength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]', '')
            let OriClength = _.get(attack, 'originalRequest.httpResponse.headers["content-length"]', '')
            if (AtClength != OriClength)
                return true
        }
        return false
    }

    async processAttackResponse(attack) {
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan')
        if (pluginStorageScanScope.CSDAttackFound) {
            return
        }
        const uriRaw = new URL(attack.href)
        const OriuriRaw = new URL(attack.originalRequest.httpRequest.uri)
        const headers = attack.httpRequest.headers
        const ReqBody = attack.httpRequest.body
        let options;
        // Create a persistent connection
        if (uriRaw.port.length > 0) {
            options = {
                host: uriRaw.hostname,
                port: uriRaw.port
            };
        }
        else if (uriRaw.protocol == 'http:') {
            options = {
                host: uriRaw.hostname,
                port: 80
            };
        }
        else if (uriRaw.protocol == 'https:') {
            options = {
                host: uriRaw.hostname,
                port: 443
            };
        }
        // First request
        let request1 = `POST /haiku.txt HTTP/1.1\r\nHost: ${uriRaw.host}\r\n`;
        for (let [key, value] of Object.entries(headers)) {
            request1 += `${key}: ${value}\r\n`;
        }
        request1 += `\r\n${attack.httpRequest.body}`

        // Followup Request
        let request2 = `GET /haiku.txt HTTP/1.1\r\nHost: ${uriRaw.host}\r\n`;
        for (let [key, value] of Object.entries(headers)) {
            if (!/content\-length/i.test(key)) {
                request2 += `${key}: ${value}\r\n`;
            }
        }
        request2 += `\r\n`;
        let OriClength = _.get(attack, 'originalRequest.httpResponse.headers["content-length"]', '')
        let ResBody = _.get(attack, 'result.resp.body', '')
        if (OriClength > 0 && !/^\s+$/.test(ResBody)) {
            // await HaikuUtils.sleep(20000) // give 1/2 second for any script (including timer based ones) to execute
            let NewResponse = await this.AtkPOSTRequest(options, request1, request2)
            if (NewResponse && NewResponse.length > 1 && NewResponse != 'NotFound' && NewResponse.statusCode != 408) {
                let FollowupHeaders = NewResponse[1].headers
                let headersArray = FollowupHeaders.split('\r\n').slice(1, -1); // Split and remove the first line and last empty line
                let headersObject = {};

                headersArray.forEach(header => {
                    let [key, value] = header.split(': ');
                    headersObject[key] = value;
                });
                let OristatusCode = _.get(attack, 'originalRequest.httpResponse.statusCode', '')
                let FollowupCL = headersObject["Content-Length"]
                if (!FollowupCL && NewResponse[1].body && NewResponse[1].body.length > 0) {
                    FollowupCL = Buffer.from(NewResponse[1].body, 'utf-8').length
                }
                let FollowupstatusCode = NewResponse[1].statusCode
                if (OristatusCode == FollowupstatusCode && FollowupCL == OriClength) {
                    let result = `Vulnerable to CSD. Response of malicious prefix: ${NewResponse[1].headers}`
                    this.addVulnerabilitytoResult(attack, this.vulnerabilityID, result)
                    pluginStorageScanScope.CSDAttackFound = true
                }
            }
        }
    }

    AtkPOSTRequest(options, request1, request2) {
        let resp = []
        let count = 0
        let task;

        return new Promise((resolve) => {
            try {
                const client = net.createConnection(options, () => {
                    console.log('Connected to server');

                    // First request
                    const request_1 = request1

                    // Follow-up request
                    const request_2 = request2

                    // Send the first request
                    client.write(request_1);
                    task = 'req1'
                    /* client.on('data', (data) => {
                        // Separate headers and body for the first response
                        const [headers1, body1] = data.toString().split('\r\n\r\n');
                        resp1 = { headers1, body1 }
                    }); */

                    // Wait for a short period before sending the second request
                    setTimeout(() => {
                        client.write(request_2);
                        task = 'req2'
                        client.on('data', (data) => {
                            let datas = data.toString().split('\r\n\r\n', 2);
                            if (datas.length > 0 && /HTTP\/1.1/i.test(datas[0])) {
                                if (count == 0) {
                                    const response = data.toString();
                                    const [headers, body] = response.split('\r\n\r\n', 2);
                                    const statusLine = response.split('\r\n')[0];
                                    const statusCode = statusLine.split(' ')[1];
                                    // console.log('Status Code:', statusCode);
                                    resp.push({ headers: headers, body: body, statusCode: statusCode })
                                    count++
                                }
                                if (count > 0) {
                                    const response = data.toString();
                                    const [headers, body] = response.split('\r\n\r\n', 2);
                                    const statusLine = response.split('\r\n')[0];
                                    const statusCode = statusLine.split(' ')[1];
                                    count++
                                    if (count == 3) {
                                        resp.push({ headers: headers, body: body, statusCode: statusCode })
                                    }
                                }
                            }
                        });
                    }, 1000);
                });

                client.on('end', () => {
                    // console.log('Disconnected from server');
                    resolve(resp)
                });

                client.on('error', (err) => {
                    resolve('NotFound')
                    // console.error('Error:', err.message);
                });
            }
            catch (e) {
                resolve('NotFound')
            }
        })
    }
}
let OriResheaders = []
module.exports = CSDAttack