# mostly from https://www.vultr.com/docs/how-to-install-rabbitmq-on-centos-7
# only rabbitmq repo changed to get latest version 
sudo yum install epel-release
sudo yum update
sudo reboot

mkdir ~/installs
cd ~/installs
wget http://packages.erlang-solutions.com/erlang-solutions-1.0-1.noarch.rpm
sudo rpm -Uvh erlang-solutions-1.0-1.noarch.rpm
sudo yum install erlang

cd ~/installs
wget https://dl.bintray.com/rabbitmq/all/rabbitmq-server/3.7.2/rabbitmq-server-3.7.2-1.el7.noarch.rpm
sudo rpm --import https://dl.bintray.com/rabbitmq/Keys/rabbitmq-release-signing-key.asc
sudo yum install rabbitmq-server-3.7.2-1.el7.noarch.rpm

# start rabbitmq service
sudo systemctl start rabbitmq-server.service
sudo systemctl enable rabbitmq-server.service
sudo rabbitmqctl status
sudo rabbitmq-plugins
sudo rabbitmq-plugins list
# enable management only on local machines
# sudo rabbitmq-plugins enable rabbitmq_management

# create users, vhost etc.
sudo rabbitmqctl add_user mqadmin 1Vegan@Ifc
sudo rabbitmqctl set_user_tags mqadmin administrator
sudo rabbitmqctl set_permissions -p / mqadmin ".*" ".*" ".*"

rabbitmqctl add_user haiku haiku
rabbitmqctl add_vhost haiku
rabbitmqctl set_permissions -p haiku haiku ".*" ".*" ".*"

# set policies
# default 20 minutes TTL
rabbitmqctl set_policy -p haiku haiku-message-TTL ".*" '{"message-ttl":1200000}' --priority 0 --apply-to queues

# 24 hours for utils requests
rabbitmqctl set_policy -p haiku haiku-lb-utils-request-TTL "lb-utils-request" '{"message-ttl":86400000}' --priority 5 --apply-to queues
