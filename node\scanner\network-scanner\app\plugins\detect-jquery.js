const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const request = require('request-promise-native')

// Constants for better maintainability
const VERSION_PATTERNS = {
    JQUERY: [
        /jQuery v([0-9.]+)/,
        /jQuery\.fn\.jquery\s*=\s*["']([0-9.]+)["']/,
        /jquery-([0-9.]+)(?:\.min)?\.js/,
        /jQuery JavaScript Library v([0-9.]+)/
    ],
    JQUERY_UI: [
        /jQuery UI - v([0-9.]+)/,
        /jQuery\.ui\.version\s*=\s*["']([0-9.]+)["']/,
        /jquery-ui-([0-9.]+)(?:\.min)?\.js/
    ],
    BOOTSTRAP: [
        /bootstrap@([0-9.]+)/,
        /bootstrap-([0-9.]+)(?:\.min)?\.(?:css|js)/,
        /Bootstrap v([0-9.]+)/,
        /bootstrap\.min\.js\?v=([0-9.]+)/
    ],
    PROTOTYPE: [
        /prototype-([0-9.]+)(?:\.min)?\.js/,
        /Prototype JavaScript Framework v([0-9.]+)/,
        /Prototype JavaScript framework, version ([0-9.]+)/,
        /prototype\.js\?v=([0-9.]+)/,
        /Prototype\.Version\s*=\s*["']([0-9.]+)["']/
    ],
    AXIOS: [
        /axios@([0-9.]+)/,
        /axios-([0-9.]+)(?:\.min)?\.js/,
        /axios\.min\.js\?v=([0-9.]+)/,
        /axios\.version\s*=\s*["']([0-9.]+)["']/,
        /\[axios\s*v([0-9.]+)\]/,
        /axios\s*v([0-9.]+)/,
        /"axios","([0-9.]+)",/,
        /axios["']\s*,\s*["']([0-9.]+)["']/
    ]
};

const CDN_DOMAINS = new Set([
    'cdnjs.cloudflare.com',
    'code.jquery.com',
    'ajax.googleapis.com',
    'unpkg.com',
    'cdn.jsdelivr.net',
    'stackpath.bootstrapcdn.com',
    'maxcdn.bootstrapcdn.com',
    'oss.maxcdn.com'
]);

// Version caching constants with synchronization
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const versionCache = {
    jquery: { version: '3.7.1', timestamp: 0, updating: false },
    jqueryUi: { version: '1.14.1', timestamp: 0, updating: false },
    bootstrap: { version: '5.3.6', timestamp: 0, updating: false },
    prototype: { version: '1.7.3', timestamp: 0, updating: false },
    axios: { version: '1.9.0', timestamp: 0, updating: false },
    angular: { version: '19.2.11', timestamp: 0, updating: false },
    react: { version: '19.1.0', timestamp: 0, updating: false },
    vue: { version: '3.5.14', timestamp: 0, updating: false }
};

// Synchronization locks
const updateLocks = {
    jquery: false,
    jqueryUi: false,
    bootstrap: false,
    prototype: false,
    axios: false,
    angular: false,
    react: false,
    vue: false
};

// CVE Information - Moved to global constants
const JQUERY_CVE_INFO = {
    '3.0.0': {
        cve: 'CVE-2015-9251',
        severity: 'high',
        cvss: 7.5,
        description: 'Cross-site scripting (XSS) vulnerability in jQuery before 3.0.0 allows remote attackers to inject arbitrary web script or HTML via the load method.'
    },
    '3.4.0': {
        cve: 'CVE-2019-11358',
        severity: 'high',
        cvss: 7.5,
        description: 'jQuery before 3.4.0, as used in Drupal, Backdrop CMS, and other products, mishandles jQuery.extend(true, {}, ...) because of Object.prototype pollution.'
    },
    '3.5.0': {
        cve: 'CVE-2020-11022, CVE-2020-11023',
        severity: 'high',
        cvss: 7.5,
        description: 'jQuery before 3.5.0 mishandles jQuery.extend(true, {}, ...) because of Object.prototype pollution. The fix for this vulnerability was included in jQuery 3.5.0.'
    }
};

const JQUERY_UI_CVE_INFO = {
    '1.9.0': {
        cve: 'CVE-2020-7656',
        severity: 'medium',
        cvss: 6.1,
        description: 'jQuery UI before 1.9.0 allows XSS via the closeText parameter of the dialog function.'
    },
    '1.13.2': {
        cve: 'CVE-2022-31160',
        severity: 'medium',
        cvss: 5.4,
        description: 'jQuery UI before 1.13.2 allows XSS via the autocomplete field.'
    }
};

const FRAMEWORK_CVE_INFO = {
    'angular:1.7.9': {
        cve: 'CVE-2019-10768',
        severity: 'high',
        cvss: 8.8,
        type: 'Cross-Site Scripting',
        description: 'Cross-site scripting vulnerability in AngularJS before 1.7.9 allows attackers to inject arbitrary web script or HTML via the ng-bind-html directive.'
    },
    'react:16.4.2': {
        cve: 'CVE-2018-6341',
        severity: 'high',
        cvss: 7.5,
        type: 'Cross-Site Scripting',
        description: 'React applications which rendered to HTML using the ReactDOMServer API were not escaping user-supplied attribute names at render-time. That lack of escaping could lead to a cross-site scripting vulnerability. This issue affected minor releases 16.0.x, 16.1.x, 16.2.x, 16.3.x, and 16.4.x. It was fixed in 16.0.1, 16.1.2, 16.2.1, 16.3.3, and 16.4.2.'
    },
    'vue:2.6.11': {
        cve: 'CVE-2019-10866',
        severity: 'high',
        cvss: 7.5,
        type: 'Cross-Site Scripting',
        description: 'Cross-site scripting vulnerability in Vue.js before 2.6.11 allows attackers to inject arbitrary web script or HTML via the v-html directive.'
    },
    'bootstrap:5.3.0': {
        cve: 'CVE-2023-32682',
        severity: 'medium',
        cvss: 5.4,
        type: 'Cross-Site Scripting',
        description: 'Bootstrap before 5.3.0 allows XSS via the data-bs-content attribute.'
    },
    'bootstrap:5.2.3': {
        cve: 'CVE-2022-43680',
        severity: 'medium',
        cvss: 5.4,
        type: 'Cross-Site Scripting',
        description: 'Bootstrap before 5.2.3 allows XSS via the data-bs-toggle attribute.'
    },
    'prototype:1.7.3': {
        cve: 'CVE-2015-9251',
        severity: 'high',
        cvss: 7.5,
        type: 'Cross-Site Scripting',
        description: 'Prototype.js before 1.7.3 allows XSS via the $() function when used with untrusted input.'
    },
    'prototype:1.7.2': {
        cve: 'CVE-2014-3120',
        severity: 'high',
        cvss: 7.5,
        type: 'Cross-Site Scripting',
        description: 'Prototype.js before 1.7.2 allows XSS via the $() function when used with untrusted input.'
    },
    'axios:1.6.8': {
        cve: 'CVE-2024-28849',
        severity: 'medium',
        cvss: 5.4,
        type: 'Information Disclosure',
        description: 'Versions before 1.6.8 depends on follow-redirects before 1.15.6 which could leak the proxy authentication credentials.'
    },
    'axios:1.6.2': {
        cve: 'CVE-2023-45857',
        severity: 'medium',
        cvss: 5.4,
        type: 'Cross-Site Request Forgery',
        description: 'Axios Cross-Site Request Forgery Vulnerability (GHSA-wf5p-g6vw-rhxx)'
    },
    'axios:1.5.0': {
        cve: 'CVE-2025-27152',
        severity: 'high',
        cvss: 7.5,
        type: 'Server-Side Request Forgery',
        description: 'Axios Requests Vulnerable To Possible SSRF and Credential Leakage via Absolute URL (GHSA-jr5f-v2jv-69x6)'
    },
    'axios:0.21.4': {
        cve: 'CVE-2023-45857, CVE-2024-28849, CVE-2025-27152',
        severity: 'high',
        cvss: 7.5,
        type: 'Multiple Vulnerabilities',
        description: 'Axios 0.21.4 is affected by multiple vulnerabilities including SSRF, CSRF, and credential leakage. Upgrade to version 1.6.8 or later.'
    }
};

// jQuery and JS Framework Version Detection
// Author: Ansari
class DetectJquery extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.jqueryVulnID = 'ID-jquery-version-check'
        this.jsframeworkVulnID = 'ID-jsframework-version-check'

        // Enhanced error messages
        this.errorMessages = {
            versionDetection: 'Failed to detect version from URL:',
            urlProcessing: 'Error processing URL:',
            vulnerabilityCheck: 'Error checking vulnerabilities:',
            cdnSkip: 'Skipping CDN URL:'
        };
    }

    async latestJquery() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.jquery.timestamp && (now - versionCache.jquery.timestamp) < CACHE_DURATION) {
            return versionCache.jquery.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.jquery) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.jquery) {
                    return versionCache.jquery.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.jquery.version;
        }

        // Acquire lock
        updateLocks.jquery = true;

        try {
            const response = await request({
                uri: 'https://jquery.com/download/',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Download jQuery ([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '3.7.1';
                versionCache.jquery = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.jquery = { version: '3.7.1', timestamp: now, updating: false };
            return '3.7.1';
        } catch (error) {
            // console.error('[jQuery Scanner] Error fetching latest jQuery version:', error);
            versionCache.jquery = { version: '3.7.1', timestamp: now, updating: false };
            return '3.7.1';
        } finally {
            // Release lock
            updateLocks.jquery = false;
        }
    }

    async latestJqueryUi() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.jqueryUi.timestamp && (now - versionCache.jqueryUi.timestamp) < CACHE_DURATION) {
            return versionCache.jqueryUi.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.jqueryUi) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.jqueryUi) {
                    return versionCache.jqueryUi.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.jqueryUi.version;
        }

        // Acquire lock
        updateLocks.jqueryUi = true;

        try {
            const response = await request({
                uri: 'https://jqueryui.com/download/',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/<a class="download-link"[^>]*>Stable<\/a>[^<]*\([^<]*\)\s*\(([0-9]+\.[0-9]+\.[0-9]+):/);
                const version = versionMatch?.[1] || '1.14.1';
                versionCache.jqueryUi = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.jqueryUi = { version: '1.14.1', timestamp: now, updating: false };
            return '1.14.1';
        } catch (error) {
            // console.error('[jQuery Scanner] Error fetching latest jQuery UI version:', error);
            versionCache.jqueryUi = { version: '1.14.1', timestamp: now, updating: false };
            return '1.14.1';
        } finally {
            // Release lock
            updateLocks.jqueryUi = false;
        }
    }

    async latestBootstrap() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.bootstrap.timestamp && (now - versionCache.bootstrap.timestamp) < CACHE_DURATION) {
            return versionCache.bootstrap.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.bootstrap) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.bootstrap) {
                    return versionCache.bootstrap.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.bootstrap.version;
        }

        // Acquire lock
        updateLocks.bootstrap = true;

        try {
            const response = await request({
                uri: 'https://getbootstrap.com/docs/5.3/getting-started/download/',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Bootstrap v([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '5.3.6';
                versionCache.bootstrap = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.bootstrap = { version: '5.3.6', timestamp: now, updating: false };
            return '5.3.6';
        } catch (error) {
            // console.error('[jQuery Scanner] Error fetching latest Bootstrap version:', error);
            versionCache.bootstrap = { version: '5.3.6', timestamp: now, updating: false };
            return '5.3.6';
        } finally {
            // Release lock
            updateLocks.bootstrap = false;
        }
    }

    async latestPrototype() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.prototype.timestamp && (now - versionCache.prototype.timestamp) < CACHE_DURATION) {
            return versionCache.prototype.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.prototype) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.prototype) {
                    return versionCache.prototype.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.prototype.version;
        }

        // Acquire lock
        updateLocks.prototype = true;

        try {
            const response = await request({
                uri: 'http://prototypejs.org/download/',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Download Prototype ([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '1.7.3';
                versionCache.prototype = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.prototype = { version: '1.7.3', timestamp: now, updating: false };
            return '1.7.3';
        } catch (error) {
            // console.error('[jQuery Scanner] Error fetching latest Prototype version:', error);
            versionCache.prototype = { version: '1.7.3', timestamp: now, updating: false };
            return '1.7.3';
        } finally {
            // Release lock
            updateLocks.prototype = false;
        }
    }

    async latestAxios() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.axios.timestamp && (now - versionCache.axios.timestamp) < CACHE_DURATION) {
            return versionCache.axios.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.axios) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.axios) {
                    return versionCache.axios.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.axios.version;
        }

        // Acquire lock
        updateLocks.axios = true;

        try {
            const response = await request({
                uri: 'https://www.npmjs.com/package/axios',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Latest version: ([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '1.9.0';
                versionCache.axios = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.axios = { version: '1.9.0', timestamp: now, updating: false };
            return '1.9.0';
        } catch (error) {
            // console.error('[jQuery Scanner] Error fetching latest Axios version:', error);
            versionCache.axios = { version: '1.9.0', timestamp: now, updating: false };
            return '1.9.0';
        } finally {
            // Release lock
            updateLocks.axios = false;
        }
    }

    async latestAngular() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.angular.timestamp && (now - versionCache.angular.timestamp) < CACHE_DURATION) {
            return versionCache.angular.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.angular) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.angular) {
                    return versionCache.angular.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.angular.version;
        }

        // Acquire lock
        updateLocks.angular = true;

        try {
            const response = await request({
                uri: 'https://www.npmjs.com/package/@angular/core',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Latest version: ([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '19.2.11';
                versionCache.angular = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.angular = { version: '19.2.11', timestamp: now, updating: false };
            return '19.2.11';
        } catch (error) {
            versionCache.angular = { version: '19.2.11', timestamp: now, updating: false };
            return '19.2.11';
        } finally {
            // Release lock
            updateLocks.angular = false;
        }
    }

    async latestReact() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.react.timestamp && (now - versionCache.react.timestamp) < CACHE_DURATION) {
            return versionCache.react.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.react) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.react) {
                    return versionCache.react.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.react.version;
        }

        // Acquire lock
        updateLocks.react = true;

        try {
            const response = await request({
                uri: 'https://www.npmjs.com/package/react',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Latest version: ([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '19.1.0';
                versionCache.react = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.react = { version: '19.1.0', timestamp: now, updating: false };
            return '19.1.0';
        } catch (error) {
            versionCache.react = { version: '19.1.0', timestamp: now, updating: false };
            return '19.1.0';
        } finally {
            // Release lock
            updateLocks.react = false;
        }
    }

    async latestVue() {
        const now = Date.now();

        // Check if we have a valid cached version
        if (versionCache.vue.timestamp && (now - versionCache.vue.timestamp) < CACHE_DURATION) {
            return versionCache.vue.version;
        }

        // If another process is already updating, wait for it
        if (updateLocks.vue) {
            // Wait for up to 5 seconds for the update to complete
            for (let i = 0; i < 50; i++) {
                if (!updateLocks.vue) {
                    return versionCache.vue.version;
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            return versionCache.vue.version;
        }

        // Acquire lock
        updateLocks.vue = true;

        try {
            const response = await request({
                uri: 'https://www.npmjs.com/package/vue',
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 5000
            });

            if (response.statusCode === 200) {
                const versionMatch = response.body.match(/Latest version: ([0-9]+\.[0-9]+\.[0-9]+)/);
                const version = versionMatch?.[1] || '3.5.14';
                versionCache.vue = { version, timestamp: now, updating: false };
                return version;
            }
            versionCache.vue = { version: '3.5.14', timestamp: now, updating: false };
            return '3.5.14';
        } catch (error) {
            versionCache.vue = { version: '3.5.14', timestamp: now, updating: false };
            return '3.5.14';
        } finally {
            // Release lock
            updateLocks.vue = false;
        }
    }

    async checkJqueryVulnerabilities(jqueryMatches, latestJqueryVersion, baseUrl, attack) {
        let result = '';
        const processedUrls = new Set();
        const processedVersions = new Set();

        for (const jqueryMatch of jqueryMatches) {
            try {
                const relativePath = jqueryMatch[1] + jqueryMatch[2];
                let jqueryUrl = this.normalizeUrl(relativePath, baseUrl);

                if (processedUrls.has(jqueryUrl) || this.isCdnUrl(jqueryUrl)) {
                    continue;
                }
                processedUrls.add(jqueryUrl);

                let detectedVersion = await this.detectVersion(jqueryMatch[3], jqueryUrl, VERSION_PATTERNS.JQUERY, attack);

                if (detectedVersion) {
                    const detectedVersionNumeric = detectedVersion.replace(/[-].*$/, '');

                    // Skip if we've already processed this version
                    if (processedVersions.has(detectedVersionNumeric)) {
                        continue;
                    }
                    processedVersions.add(detectedVersionNumeric);

                    if (this.versionCompare(detectedVersionNumeric, latestJqueryVersion) < 0) {
                        const jqueryVulns = this.getVulnerabilities(detectedVersionNumeric, JQUERY_CVE_INFO);

                        if (jqueryVulns.length > 0) {
                            result += this.formatVulnerabilityMessage('jQuery', detectedVersionNumeric, latestJqueryVersion, jqueryVulns, jqueryUrl);
                        } else {
                            // Even if no specific vulnerabilities are found, report outdated version
                            result += `[WARNING] Outdated jQuery detected: v${detectedVersionNumeric} (latest is v${latestJqueryVersion})\n` +
                                    `  Source: ${jqueryUrl}\n` +
                                    `  Recommendation: Upgrade to the latest version for security improvements\n\n`;
                        }
                    }
                }
            } catch (error) {
                return // console.error('[jQuery Scanner] Error processing jQuery match:', error);
            }
        }

        if (result) {
            this.addVulnerabilitytoResult(attack, this.jqueryVulnID, result);
            this.getPluginScopedStore(attack).jqueryVuln = true;
        }
    }

    async checkJqueryUIVulnerabilities(jqueryUiMatches, latestJqueryUiVersion, baseUrl, attack) {
        let result = '';
        const processedUrls = new Set();
        const processedVersions = new Set();

        for (const jqueryUiMatch of jqueryUiMatches) {
            try {
                const relativePath = jqueryUiMatch[1] + jqueryUiMatch[2];
                let jqueryUiUrl = this.normalizeUrl(relativePath, baseUrl);

                if (processedUrls.has(jqueryUiUrl) || this.isCdnUrl(jqueryUiUrl)) {
                    continue;
                }
                processedUrls.add(jqueryUiUrl);

                let detectedVersion = await this.detectVersion(jqueryUiMatch[3], jqueryUiUrl, VERSION_PATTERNS.JQUERY_UI, attack);

                if (detectedVersion) {
                    const detectedVersionNumeric = detectedVersion.replace(/[-].*$/, '');

                    // Skip if we've already processed this version
                    if (processedVersions.has(detectedVersionNumeric)) {
                        continue;
                    }
                    processedVersions.add(detectedVersionNumeric);

                    if (this.versionCompare(detectedVersionNumeric, latestJqueryUiVersion) < 0) {
                        const jqueryUiVulns = this.getVulnerabilities(detectedVersionNumeric, JQUERY_UI_CVE_INFO);

                        if (jqueryUiVulns.length > 0) {
                            result += this.formatVulnerabilityMessage('jQuery UI', detectedVersionNumeric, latestJqueryUiVersion, jqueryUiVulns, jqueryUiUrl);
                        } else {
                            // Even if no specific vulnerabilities are found, report outdated version
                            result += `[WARNING] Outdated jQuery UI detected: v${detectedVersionNumeric} (latest is v${latestJqueryUiVersion})\n` +
                                    `  Source: ${jqueryUiUrl}\n` +
                                    `  Recommendation: Upgrade to the latest version for security improvements\n\n`;
                        }
                    }
                }
            } catch (error) {
                return // console.error('[jQuery Scanner] Error processing jQuery UI match:', error);
            }
        }

        if (result) {
            this.addVulnerabilitytoResult(attack, this.jsframeworkVulnID, result);
            this.getPluginScopedStore(attack).jqueryUiVuln = true;
        }
    }

    checkFrameworkVulnerabilities(responseBody, attack, latestAngularVersion, latestReactVersion, latestVueVersion) {
        if (!responseBody || !attack) {
            return;
        }

        const frameworks = {
            'angular': {
                displayName: 'Angular',
                latestVersion: latestAngularVersion,
                patterns: [
                    /angular[.-]([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /ng-([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /@angular\/core@([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /"@angular\/core","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /angular["']\s*,\s*["']([0-9]+\.[0-9]+\.[0-9]+)["']/i
                ]
            },
            'react': {
                displayName: 'React',
                latestVersion: latestReactVersion,
                patterns: [
                    /react[.-]([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /react-dom[.-]([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /"react","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /"react-dom","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /react["']\s*,\s*["']([0-9]+\.[0-9]+\.[0-9]+)["']/i,
                    /react-dom["']\s*,\s*["']([0-9]+\.[0-9]+\.[0-9]+)["']/i,
                    /c\("react","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /c\("react-dom","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /react@([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /react-dom@([0-9]+\.[0-9]+\.[0-9]+)/i
                ]
            },
            'vue': {
                displayName: 'Vue.js',
                latestVersion: latestVueVersion,
                patterns: [
                    /vue[.-]([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /vue\.min\.js\?v=([0-9]+\.[0-9]+\.[0-9]+)/i,
                    /"vue","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /vue["']\s*,\s*["']([0-9]+\.[0-9]+\.[0-9]+)["']/i,
                    /c\("vue","([0-9]+\.[0-9]+\.[0-9]+)",/i,
                    /vue@([0-9]+\.[0-9]+\.[0-9]+)/i
                ]
            }
        };

        let result = '';
        const processedFrameworkVersions = new Set();
        const processedUrls = new Set();

        for (const [libName, frameworkInfo] of Object.entries(frameworks)) {
            try {
                const scriptPattern = new RegExp(`<script[^>]+src=['"]([^'"]*${libName}[^'"]*)['"]`, 'gi');
                const Static = /src=["']([^ ]*)(\/static\/js\/main\.[a-f0-9]+\.js)/gi;
                const frameworkMatches = [...responseBody.matchAll(scriptPattern), ...responseBody.matchAll(Static)];

                for (const frameworkMatch of frameworkMatches) {
                    try {
                        const scriptUrl = frameworkMatch[1];
                        if (!scriptUrl) continue;

                        if (processedUrls.has(scriptUrl) || this.isCdnUrl(scriptUrl)) {
                            continue;
                        }
                        processedUrls.add(scriptUrl);

                        let detectedVersion = null;
                        for (const pattern of frameworkInfo.patterns) {
                            const versionMatch = scriptUrl.match(pattern);
                            if (versionMatch?.[1]) {
                                detectedVersion = versionMatch[1];
                                break;
                            }
                        }

                        if (detectedVersion) {
                            const frameworkVersionKey = `${libName}:${detectedVersion}`;
                            if (processedFrameworkVersions.has(frameworkVersionKey)) {
                                continue;
                            }
                            processedFrameworkVersions.add(frameworkVersionKey);

                            // Check if version is outdated
                            if (this.versionCompare(detectedVersion, frameworkInfo.latestVersion) < 0) {
                                const vulns = this.getFrameworkVulnerabilities(libName, detectedVersion);
                                if (vulns.length > 0) {
                                    const vulnDetails = vulns.map(v => {
                                        const cveInfo = FRAMEWORK_CVE_INFO[`${libName}:${v.fixedVersion}`];
                                        if (!cveInfo) return '';
                                        return `${v.cve} (${cveInfo.type}, ${cveInfo.severity})`;
                                    }).filter(Boolean).join(', ');

                                    if (vulnDetails) {
                                        result += `[WARNING] Outdated ${frameworkInfo.displayName} detected: v${detectedVersion} (latest is v${frameworkInfo.latestVersion})\n`;
                                        result += `  Source: ${scriptUrl}\n`;
                                        result += `  Vulnerabilities: ${vulnDetails}\n`;
                                        result += `  Description: ${vulns.map(v => {
                                            const cveInfo = FRAMEWORK_CVE_INFO[`${libName}:${v.fixedVersion}`];
                                            return cveInfo?.description || '';
                                        }).filter(Boolean).join('\n  ')}\n\n`;
                                    }
                                } else {
                                    // Even if no specific vulnerabilities are found, report outdated version
                                    result += `[WARNING] Outdated ${frameworkInfo.displayName} detected: v${detectedVersion} (latest is v${frameworkInfo.latestVersion})\n` +
                                            `  Source: ${scriptUrl}\n` +
                                            `  Recommendation: Upgrade to the latest version for security improvements\n\n`;
                                }
                            }
                        }
                    } catch (error) {
                        return // console.error(`[jQuery Scanner] Error processing ${libName} script:`, error);
                    }
                }
            } catch (error) {
                return // console.error(`[jQuery Scanner] Error scanning for ${libName}:`, error);
            }
        }

        if (result) {
            this.addVulnerabilitytoResult(attack, this.jsframeworkVulnID, result);
            this.getPluginScopedStore(attack).jsframeworkVuln = true;
        }
    }

    async checkBootstrapVulnerabilities(bootstrapMatches, latestBootstrapVersion, baseUrl, attack) {
        let result = '';
        const processedUrls = new Set();
        const processedVersions = new Set();

        for (const bootstrapMatch of bootstrapMatches) {
            try {
                const relativePath = bootstrapMatch[1] + bootstrapMatch[2];
                let bootstrapUrl = this.normalizeUrl(relativePath, baseUrl);

                if (processedUrls.has(bootstrapUrl) || this.isCdnUrl(bootstrapUrl)) {
                    continue;
                }
                processedUrls.add(bootstrapUrl);

                let detectedVersion = await this.detectVersion(bootstrapMatch[3], bootstrapUrl, VERSION_PATTERNS.BOOTSTRAP, attack);

                if (detectedVersion) {
                    const detectedVersionNumeric = detectedVersion.replace(/[-].*$/, '');

                    // Skip if we've already processed this version
                    if (processedVersions.has(detectedVersionNumeric)) {
                        continue;
                    }
                    processedVersions.add(detectedVersionNumeric);

                    if (this.versionCompare(detectedVersionNumeric, latestBootstrapVersion) < 0) {
                        const bootstrapVulns = this.getFrameworkVulnerabilities('bootstrap', detectedVersionNumeric);

                        if (bootstrapVulns.length > 0) {
                            result += this.formatVulnerabilityMessage('Bootstrap', detectedVersionNumeric, latestBootstrapVersion, bootstrapVulns, bootstrapUrl);
                        } else {
                            // Even if no specific vulnerabilities are found, report outdated version
                            result += `[WARNING] Outdated Bootstrap detected: v${detectedVersionNumeric} (latest is v${latestBootstrapVersion})\n` +
                                    `  Source: ${bootstrapUrl}\n` +
                                    `  Recommendation: Upgrade to the latest version for security improvements\n\n`;
                        }
                    }
                }
            } catch (error) {
                return // console.error('[jQuery Scanner] Error processing Bootstrap match:', error);
            }
        }

        if (result) {
            this.addVulnerabilitytoResult(attack, this.jsframeworkVulnID, result);
            this.getPluginScopedStore(attack).jsframeworkVuln = true;
        }
    }

    async checkPrototypeVulnerabilities(prototypeMatches, latestPrototypeVersion, baseUrl, attack) {
        let result = '';
        const processedUrls = new Set();
        const processedVersions = new Set();

        for (const prototypeMatch of prototypeMatches) {
            try {
                const relativePath = prototypeMatch[1] + prototypeMatch[2];
                let prototypeUrl = this.normalizeUrl(relativePath, baseUrl);

                if (processedUrls.has(prototypeUrl) || this.isCdnUrl(prototypeUrl)) {
                    continue;
                }
                processedUrls.add(prototypeUrl);

                let detectedVersion = await this.detectVersion(prototypeMatch[3], prototypeUrl, VERSION_PATTERNS.PROTOTYPE, attack);

                if (detectedVersion) {
                    const detectedVersionNumeric = detectedVersion.replace(/[-].*$/, '');

                    // Skip if we've already processed this version
                    if (processedVersions.has(detectedVersionNumeric)) {
                        continue;
                    }
                    processedVersions.add(detectedVersionNumeric);

                    if (this.versionCompare(detectedVersionNumeric, latestPrototypeVersion) < 0) {
                        const prototypeVulns = this.getFrameworkVulnerabilities('prototype', detectedVersionNumeric);

                        if (prototypeVulns.length > 0) {
                            result += this.formatVulnerabilityMessage('Prototype.js', detectedVersionNumeric, latestPrototypeVersion, prototypeVulns, prototypeUrl);
                        } else {
                            // Even if no specific vulnerabilities are found, report outdated version
                            result += `[WARNING] Outdated Prototype.js detected: v${detectedVersionNumeric} (latest is v${latestPrototypeVersion})\n` +
                                    `  Source: ${prototypeUrl}\n` +
                                    `  Recommendation: Upgrade to the latest version for security improvements\n\n`;
                        }
                    }
                }
            } catch (error) {
                return // console.error('[jQuery Scanner] Error processing Prototype match:', error);
            }
        }

        if (result) {
            this.addVulnerabilitytoResult(attack, this.jsframeworkVulnID, result);
            this.getPluginScopedStore(attack).jsframeworkVuln = true;
        }
    }

    async checkAxiosVulnerabilities(axiosMatches, latestAxiosVersion, baseUrl, attack) {
        let result = '';
        const processedUrls = new Set();
        const processedVersions = new Set();

        for (const axiosMatch of axiosMatches) {
            try {
                const relativePath = axiosMatch[1] + axiosMatch[2];
                let axiosUrl = this.normalizeUrl(relativePath, baseUrl);

                if (processedUrls.has(axiosUrl) || this.isCdnUrl(axiosUrl)) {
                    continue;
                }
                processedUrls.add(axiosUrl);

                let detectedVersion = await this.detectVersion(axiosMatch[3], axiosUrl, VERSION_PATTERNS.AXIOS, attack);

                if (detectedVersion) {
                    const detectedVersionNumeric = detectedVersion.replace(/[-].*$/, '');

                    // Skip if we've already processed this version
                    if (processedVersions.has(detectedVersionNumeric)) {
                        continue;
                    }
                    processedVersions.add(detectedVersionNumeric);

                    // Compare versions and report if outdated
                    if (this.versionCompare(detectedVersionNumeric, latestAxiosVersion) < 0) {
                        const axiosVulns = this.getFrameworkVulnerabilities('axios', detectedVersionNumeric);

                        if (axiosVulns.length > 0) {
                            result += this.formatVulnerabilityMessage('Axios', detectedVersionNumeric, latestAxiosVersion, axiosVulns, axiosUrl);
                        } else {
                            // Even if no specific vulnerabilities are found, report outdated version
                            result += `[WARNING] Outdated Axios detected: v${detectedVersionNumeric} (latest is v${latestAxiosVersion})\n` +
                                    `  Source: ${axiosUrl}\n` +
                                    `  Recommendation: Upgrade to the latest version for security improvements\n\n`;
                        }
                    }
                }
            } catch (error) {
                return // console.error('[jQuery Scanner] Error processing Axios match:', error);
            }
        }

        if (result) {
            this.addVulnerabilitytoResult(attack, this.jsframeworkVulnID, result);
            this.getPluginScopedStore(attack).jsframeworkVuln = true;
        }
    }

    // Helper methods
    normalizeUrl(relativePath, baseUrl) {
        if (!relativePath || !baseUrl) {
            return null;
        }
        try {
            // Extract the base URL without query parameters
            const baseUrlObj = new URL(baseUrl);
            const basePath = baseUrlObj.pathname;

            // Handle script paths that start with 'scripts/'
            if (relativePath.startsWith('scripts/')) {
                // Get the directory path before 'scripts/'
                const baseDir = basePath.substring(0, basePath.lastIndexOf('/'));
                return `${baseUrlObj.origin}${baseDir}/${relativePath}`;
            }

            // Handle other relative paths
            let url = relativePath.startsWith('http') ? relativePath :
                relativePath.startsWith('/') ? baseUrlObj.origin + relativePath :
                    baseUrlObj.origin + '/' + relativePath;

            return url.replace(/([^:]\/)\/+/g, '$1');
        } catch (error) {
            return null;
        }
    }

    isCdnUrl(url) {
        try {
            const urlObj = new URL(url);
            return CDN_DOMAINS.has(urlObj.hostname);
        } catch (error) {
            // console.error('[jQuery Scanner] Invalid URL:', url);
            return true; // Skip invalid URLs
        }
    }

    async detectVersion(initialVersion, url, patterns, attack) {
        if (!url || !patterns) {
            return null;
        }

        if (initialVersion) {
            const versionParts = initialVersion.split('.');
            if (versionParts.length > 2) {
                return initialVersion;
            }
        }

        try {
            // Get original headers exactly as they are
            const originalHeaders = _.get(attack, 'originalRequest.httpRequest.headers', {});
            if (!originalHeaders) {
                return null;
            }

            const response = await request({
                uri: url,
                method: 'GET',
                resolveWithFullResponse: true,
                timeout: 2000,
                headers: originalHeaders,
                encoding: null, // This ensures we get the raw buffer
                gzip: true, // Enable gzip decompression
                deflate: true, // Enable deflate decompression
                followRedirect: true, // Follow redirects
                maxRedirects: 5, // Maximum number of redirects to follow
                strictSSL: false // Allow self-signed certificates
            });

            if (response?.statusCode === 200 || response?.statusCode === 206) {
                if (!response.body) {
                    return null;
                }

                // Convert buffer to string if needed
                const body = Buffer.isBuffer(response.body) ? response.body.toString('utf8') : response.body;
                if (!body) {
                    return null;
                }

                for (const pattern of patterns) {
                    try {
                        const versionMatch = body.match(pattern);
                        if (versionMatch?.[1]) {
                            const version = versionMatch[1].trim();
                            // Validate version format
                            if (/^\d+\.\d+\.\d+$/.test(version)) {
                                return version;
                            }
                        }
                    } catch (patternError) {
                        continue; // Skip to next pattern if one fails
                    }
                }
            }
        } catch (error) {
            return null;
        }
        return null;
    }

    getVulnerabilities(detectedVersion, cveInfo) {
        const vulns = [];
        const sortedFixedVersions = Object.keys(cveInfo).sort((a, b) => this.versionCompare(a, b));

        for (const fixedVersion of sortedFixedVersions) {
            if (this.versionCompare(detectedVersion, fixedVersion) < 0) {
                vulns.push({
                    cve: cveInfo[fixedVersion].cve,
                    fixedVersion: fixedVersion,
                    severity: cveInfo[fixedVersion].severity,
                    cvss: cveInfo[fixedVersion].cvss,
                    description: cveInfo[fixedVersion].description
                });
            }
        }
        return vulns;
    }

    getFrameworkVulnerabilities(libName, detectedVersion) {
        const vulns = [];
        const frameworkCves = Object.entries(FRAMEWORK_CVE_INFO)
            .filter(([key]) => key.startsWith(`${libName}:`))
            .map(([key, cve]) => {
                const [_, fixedVersion] = key.split(':');
                return { fixedVersion, cve };
            })
            .sort((a, b) => this.versionCompare(a.fixedVersion, b.fixedVersion));

        for (const { fixedVersion, cve } of frameworkCves) {
            if (this.versionCompare(detectedVersion, fixedVersion) < 0) {
                vulns.push({ cve, fixedVersion });
            }
        }
        return vulns;
    }

    formatVulnerabilityMessage(type, detectedVersion, latestVersion, vulns, url) {
        let vulnDetails = '';
        let descriptions = [];

        if (type === 'jQuery') {
            vulnDetails = vulns.map(v => `${v.cve} (${v.severity}, CVSS: ${v.cvss})`).join(', ');
            descriptions = vulns.map(v => v.description);
        } else if (type === 'jQuery UI') {
            vulnDetails = vulns.map(v => `${v.cve} (${v.severity}, CVSS: ${v.cvss})`).join(', ');
            descriptions = vulns.map(v => v.description);
        } else {
            // For other frameworks (Bootstrap, Prototype.js, Axios, etc.)
            vulnDetails = vulns.map(v => {
                const cveInfo = FRAMEWORK_CVE_INFO[`${type.toLowerCase()}:${v.fixedVersion}`];
                if (!cveInfo) return '';
                return `${cveInfo.cve} (${cveInfo.type}, ${cveInfo.severity})`;
            }).filter(Boolean).join(', ');
            
            descriptions = vulns.map(v => {
                const cveInfo = FRAMEWORK_CVE_INFO[`${type.toLowerCase()}:${v.fixedVersion}`];
                return cveInfo?.description || '';
            }).filter(Boolean);
        }

        let result = `[WARNING] Outdated ${type} detected: v${detectedVersion} (latest is v${latestVersion})\n` +
                    `  Source: ${url}\n`;

        if (vulnDetails) {
            result += `  Vulnerabilities: ${vulnDetails}\n`;
        }

        if (descriptions.length > 0) {
            result += `  Description: ${descriptions.join('\n  ')}\n`;
        }

        result += '\n';
        return result;
    }

    versionCompare(ver1, ver2) {
        if (!ver1 || !ver2) return -1;

        const v1 = ver1.split('.');
        const v2 = ver2.split('.');

        for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
            const n1 = parseInt(v1[i] || '0');
            const n2 = parseInt(v2[i] || '0');

            if (n1 < n2) return -1;
            if (n1 > n2) return 1;
        }

        return 0;
    }

    async processAttackResponse(attack) {
        if (!attack?.result?.resp?.body) {
            return;
        }

        const pluginDataForRequest = this.getPluginScopedStore(attack);
        let pluginStorageScanScope = this.getPluginScopedStore(attack, 'this-scan');

        if (!pluginStorageScanScope) {
            return;
        }

        if (pluginStorageScanScope.jqueryScanCount > 4) {
            return;
        }

        try {
            const responseBody = _.get(attack, 'result.resp.body', '');
            if (!responseBody?.trim()) {
                return;
            }

            if (responseBody.includes('jquery') || responseBody.includes('bootstrap') || responseBody.includes('prototype') || responseBody.includes('angular') || responseBody.includes('react') || responseBody.includes('vue') || responseBody.includes('axios') || /\/static\/js\/main\.[a-f0-9]+\.js/i.test(responseBody)) {
                if (!pluginStorageScanScope.jqueryScanCount || pluginStorageScanScope.jqueryScanCount < 4) {
                    pluginStorageScanScope.jqueryScanCount++;
                }

                const [latestJqueryVersion, latestJqueryUiVersion, latestBootstrapVersion, latestPrototypeVersion, latestAxiosVersion, latestAngularVersion, latestReactVersion, latestVueVersion] = await Promise.all([
                    this.latestJquery(),
                    this.latestJqueryUi(),
                    this.latestBootstrap(),
                    this.latestPrototype(),
                    this.latestAxios(),
                    this.latestAngular(),
                    this.latestReact(),
                    this.latestVue()
                ]);

                const uriRaw = new URL(attack.href);
                const baseUrl = uriRaw.origin + uriRaw.pathname;

                // Check for jQuery
                if (!pluginDataForRequest.jqueryVuln && responseBody.includes('jquery')) {
                    const jqueryPattern = /src=["']([^ ]*)(jquery[-.]?([0-9.]+(-rc[0-9])?)?(\.min|\.slim|\.slim\.min)?\.js)/gi;
                    const jqueryMatches = [...responseBody.matchAll(jqueryPattern)];
                    if (jqueryMatches.length > 0) {
                        await this.checkJqueryVulnerabilities(jqueryMatches, latestJqueryVersion, baseUrl, attack);
                    }
                }

                // Check for jQuery UI
                if (!pluginDataForRequest.jqueryUiVuln && responseBody.includes('jquery-ui')) {
                    const jqueryUiPattern = /src=["']([^ ]*)(jquery[-.]?ui[-.]?([0-9.]+(-rc[0-9])?)?(\.min)?\.js)/gi;
                    const jqueryUiMatches = [...responseBody.matchAll(jqueryUiPattern)];
                    if (jqueryUiMatches.length > 0) {
                        await this.checkJqueryUIVulnerabilities(jqueryUiMatches, latestJqueryUiVersion, baseUrl, attack);
                    }
                }

                // Check for Bootstrap
                if (!pluginDataForRequest.jsframeworkVuln) {
                    // Check for Bootstrap
                    if (responseBody.includes('bootstrap')) {
                        const bootstrapPattern = /(?:src|href)=["']([^ ]*)(bootstrap[-.]?([0-9.]+(-rc[0-9])?)?(\.min)?\.(?:css|js))/gi;
                        const bootstrapMatches = [...responseBody.matchAll(bootstrapPattern)];
                        if (bootstrapMatches.length > 0) {
                            await this.checkBootstrapVulnerabilities(bootstrapMatches, latestBootstrapVersion, baseUrl, attack);
                        }
                    }

                    // Check for Prototype.js    
                    if (responseBody.includes('prototype')) {
                        const prototypePattern = /src=["']([^ ]*)(prototype[-.]?([0-9.]+(-rc[0-9])?)?(\.min)?\.js)/gi;
                        const prototypeMatches = [...responseBody.matchAll(prototypePattern)];
                        if (prototypeMatches.length > 0) {
                            await this.checkPrototypeVulnerabilities(prototypeMatches, latestPrototypeVersion, baseUrl, attack);
                        }
                    }

                    // Check for Axios
                    if (responseBody.includes('axios') || /\/static\/js\/main\.[a-f0-9]+\.js/i.test(responseBody)) {
                        const axiosPattern = /src=["']([^ ]*)(axios[-.]?([0-9.]+(-rc[0-9])?)?(\.min)?\.js)/gi;
                        const axiosStatic = /src=["']([^ ]*)(\/static\/js\/main\.[a-f0-9]+\.js)/gi;
                        const axiosMatches = [...responseBody.matchAll(axiosPattern), ...responseBody.matchAll(axiosStatic)];
                        if (axiosMatches.length > 0) {
                            await this.checkAxiosVulnerabilities(axiosMatches, latestAxiosVersion, baseUrl, attack);
                        }
                    }

                    // Check for other frameworks
                    if (responseBody.includes('angular') || responseBody.includes('react') || responseBody.includes('vue') || /\/static\/js\/main\.[a-f0-9]+\.js/i.test(responseBody)) {
                        this.checkFrameworkVulnerabilities(responseBody, attack, latestAngularVersion, latestReactVersion, latestVueVersion);
                    }
                }
            }
        } catch (error) {
            return // console.error('[jQuery Scanner] Error processing attack response:', error);
        }
    }
}

module.exports = DetectJquery