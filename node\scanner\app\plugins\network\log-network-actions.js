var fs = require("fs");
const http = require('http')
const URL = require('url').URL
let utils = require('../../ifc-utils.js')
const ssAPI = require('../../../common/lib/sooper-scheduler-api')
const {
    session
} = require('electron'); // ask karth<PERSON> for what this means
const querystring = require('querystring');
const crypto = require('crypto')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const s3Utils = require('../../../common/lib/s3-utils')
const _ = require('lodash')
const ParseDomain = require('parse-domain');
const { SITE_TYPE } = require("../../../common/config/app-constants.js");

// ---
const pluginName = 'logNetworkActions'

class LogNetworkActions {
    constructor(scanner) {
        this.scanner = scanner
        this.config = scanner.config

        // merge the plugin specific config
        this.mergeConfig()
        let pluginData = this.config.pluginData.logNetworkActions
        this.alwaysSendStatusCodes = pluginData.alwaysSendStatusCodes
        this.statusCodeCutoff = pluginData.statusCodeCutoff
        this.requestsToSendBeforeCutoff = pluginData.requestsToSendBeforeCutoff
        this.requestsToIgnore = pluginData.requestsToIgnore

        this.cutoffRequestsSeen = 0

        this.currentSessionInfo = null
        this.crawlerBookmark = null

        this.attackableNetworkRequests = []
        this.seenNetworkRequests = new Set()
        this.config.networkRequestFingerprints = this.config.networkRequestFingerprints || {}; // Store fingerprints
        this.logNetworkRequestFingerprints = new Map(); // Store fingerprints for logging
        this.outFile = `${this.config.logPath}/net-actions.log`

        // --- events
        scanner.on('next-action', this.onNextAction.bind(this)) // clear/setup the network actions captured array.
        scanner.on('post-trigger', this.onPostTrigger.bind(this))
        scanner.on('unique-state', this.onUniqueState.bind(this))
        scanner.on('login-status', this.onLoginStatus.bind(this))

        // refresh crawler request
        scanner.on('refresh-request-start', this.onRefreshCrawlerRequestStart.bind(this))
        scanner.on('refresh-request-finish', this.onRefreshCrawlerRequestFinish.bind(this))

        // serialize/deserialize
        scanner.on('serialize-state', this.serializeState.bind(this))
        scanner.on('deserialize-state', this.deserializeState.bind(this))

        // catch the send, receive header event
        // only allow <scan fqdn> and www.<scan fqdn>
        this.requestHeaders = {};
        this.acceptedContentTypes = [ /*'application/json',*/ 'application/x-www-form-urlencoded']
        let host = this.config.parsedUrl.hostname.replace(/^www\./, '')
        let parsedMainDomain = ParseDomain(this.config.parsedUrl.href);
        // allowSubDomain is true if the user has enabled it in the config or if API discovery is enabled
        let allowSubDomain = this.config.allowSubDomain || this.config.apiDiscovery;

        // Check if the original hostname is an IP address
        let isIpAddress = HaikuUtils.isIpAddress(this.config.parsedUrl.hostname);

        if(!allowSubDomain) {
            this.filter = {
                urls: [
                `http://${host}/*`, `http://www.${host}/*`,
                `https://${host}/*`, `https://www.${host}/*`,
                ]
            }
        }
        else {
            this.filter = {
                urls: [
                `http://*.${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`,
                `https://*.${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`,
                `http://${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`,
                `https://${parsedMainDomain.domain}.${parsedMainDomain.tld}/*`
                ]
            }
        }   
        
        if (isIpAddress) {
            //replace www by empty string from each filter url item
            this.filter.urls = this.filter.urls.map(url => url.replace('www.', ''));
        }  

        session.fromPartition(this.scanner.browserPartition).webRequest.onSendHeaders(this.filter, ((details) => {
            return this.onSendHeaders(details);
        }).bind(this))

        session.fromPartition(this.scanner.browserPartition).webRequest.onHeadersReceived(this.filter, ((details, callback) => {
            return this.onHeadersReceived(details, callback)
        }).bind(this))
    }

    /**
     * About to execute a new action - clear/setup the network actions captured array.
     * @param {*} action action(s) to be performed
     * @param {*} crawlState the crawl tree 
     */
    onNextAction(action, crawlState) {
        // save info on context + action
        this.attackableNetworkRequests = []
        this.loginInfo = null
        this.crawlerBookmark = crawlState.getCrawlerBookmark()
        this.curCrawlDepth = crawlState.getCurrentDepth()
    }

    // refresh crawler request
    onRefreshCrawlerRequestStart(request) {
        this.refreshKey = request.appTranaKey
        this.processingRefreshRequest = true
    }

    onRefreshCrawlerRequestFinish(refreshedReq) {
        this.processingRefreshRequest = false

        // return the captured/refreshed request & session
        refreshedReq.httpRequest = this.refreshedHttpRequest
        refreshedReq.sessionInfo = this.currentSessionInfo

        // clean up
        this.refreshedHttpRequest = null
        this.refreshKey = null
    }

    /**
     * Keep track of login info if login succeeded
     * @param {boolean} loginStatus Did login succeed?
     * @param {Object} loginInfo Details about login performed
     */
    onLoginStatus(loginStatus, loginInfo) {
        if (loginStatus) {
            this.loginInfo = loginInfo;
            this.loginInfo.loginStatus = loginStatus;
        }
    }

    /**
     * this event is sent when an action has been taken and browser steady
     */
    onPostTrigger() {
        this.sendVulnsToScanner(false)
    }

    /**
     * this event is sent when an action has been taken and browser steady AND the resulting
     * page is unique
     */
    onUniqueState() {
        this.sendVulnsToScanner(true)
    }

    /**
     * Send vulns captured in this action to the scanner.
     */
    sendVulnsToScanner(isUniqueState) {
        // Always send the updated session info. Can skip for unique state event since same one would just have been sent
        // in the post trigger event
        if (!isUniqueState) {
            this.scanner.emit('current-session', this.currentSessionInfo)
        }

        for (let details of this.attackableNetworkRequests) {
            // For the seed URLs (depth 0/initial crawl actions), don't send network requests 
            // that loaded non-unique pages. Send the peripheral requests anyway (eg. css or image paths).
            if (!isUniqueState && this.curCrawlDepth == 0 && /frame/i.test(details.resourceType)) {
                continue
            }

            // send unique network requests to scanner 
            if (!this.seenNetworkRequests.has(details.appTranaKey)) {
                this.sendNetworkActionToScanners(details)
                this.seenNetworkRequests.add(details.appTranaKey)
            }
        }
    }

    /**
     * return object that can be JSON.stringified and stored
     * @param {Object} pluginData Object where we can add our serialized state under our own key
     */
    serializeState(pluginData) {
        pluginData[pluginName] = {
            seenNetworkRequests: Array.from(this.seenNetworkRequests)
        }
    }

    /**
     * Restore plugin data that from serialized object
     * @param {Object} pluginData Object with our serialized state under our own key
     */
    deserializeState(pluginData) {
        if (!pluginData[pluginName]) {
            return
        }

        // add to set
        if (pluginData[pluginName].seenNetworkRequests) {
            for (let el of pluginData[pluginName].seenNetworkRequests) {
                this.seenNetworkRequests.add(el)
            }
        }
    }

    mergeConfig() {
        if (!this.config.siteConfig || !this.config.siteConfig.pluginData || !this.config.siteConfig.pluginData.logNetworkActions) {
            return
        }

        // plugin data for "check for dup" plugin
        let pluginData = this.config.pluginData.logNetworkActions
        let sitePluginData = this.config.siteConfig.pluginData.logNetworkActions

        if (sitePluginData.alwaysSendStatusCodes) {
            pluginData.alwaysSendStatusCodes = sitePluginData.alwaysSendStatusCodes
        }

        if (sitePluginData.statusCodeCutoff) {
            pluginData.statusCodeCutoff = sitePluginData.statusCodeCutoff
        }

        if (sitePluginData.requestsToSendBeforeCutoff) {
            pluginData.requestsToSendBeforeCutoff = sitePluginData.requestsToSendBeforeCutoff
        }

        if (sitePluginData.requestsToIgnore) {
            pluginData.requestsToIgnore = sitePluginData.requestsToIgnore
        }
    }

    // store the request headers in teh onSendHeaders so that we can use it in onHeadersReceived
    onSendHeaders(details) {
        this.requestHeaders[details.id] = details;
    }

    // Response status code >= 400 -> ignore
    // Method not GET/POST -> ignore
    // request in font/script/css/... context -> send only path before resource to Haiku
    // GET/POST in appropriate context -> Haiku
    // GET with query params -> IGW + Haiku
    // Form encoded POST with body -> IGW + Haiku
    onHeadersReceived(details, callback) {
        // ensure that the request is processed by Chromium
        callback({
            cancel: false,
        })

        // modify copy not the original, just in case it is used somewhere
        details = _.cloneDeep(details)
        if (this.requestHeaders[details.id]) {
            details.headers = this.requestHeaders[details.id].requestHeaders;
        }

        if (details.uploadData) {
            let requestDetails = this.requestHeaders[details.id];

            if(requestDetails) {
                let contentType = requestDetails.requestHeaders['Content-Type'] || requestDetails.requestHeaders['content-type'];
                let boundary = contentType.split('boundary=')[1];
                
                // Parse the multipart data manually
                let parsedData = { fields: {}, files: {}, parsed: false, boundary: boundary};

                requestDetails.uploadData.forEach(data => {
                    let uploadData = data.bytes;
                    let decoder = new TextDecoder('utf-8');
                    let string = decoder.decode(uploadData);
                    let parts = string.split(`--${boundary}`);

                    parts.forEach(part => {
                        if (part.length === 0 || part.toString() === '--\r\n') return;

                        let [header, body] = part.split('\r\n\r\n');
                        let headers = header.toString().split('\r\n');
                        let contentDisposition = headers.find(h => h.startsWith('Content-Disposition'));

                        if (contentDisposition) {
                            let nameMatch = contentDisposition.match(/name="([^"]+)"/);
                            let filenameMatch = contentDisposition.match(/filename="([^"]+)"/);

                            if (nameMatch) {
                                let name = nameMatch[1];

                                if (filenameMatch) {
                                    let filename = filenameMatch[1];
                                    let contentTypeHeader = headers.find(h => h.startsWith('Content-Type'));
                                    let contentType = contentTypeHeader ? contentTypeHeader.split(': ')[1] : 'application/octet-stream';

                                    if (!parsedData.files[name]) {
                                        parsedData.files[name] = {};
                                    }

                                    if(filename.includes('indusface-haiku-file') && !filename.includes('crawler/uploads')) {
                                        filename = 'crawler/uploads/' + filename;
                                    }

                                    parsedData.files[name] = { filename, contentType, body: body.slice(0, -2) };
                                    parsedData.parsed = true;
                                    parsedData.files[name].bytes = data.bytes;
                                } else {
                                    parsedData.fields[name] = body.toString().slice(0, -2);
                                    parsedData.parsed = true;
                                }
                            }
                        }
                    });
                });

                if(parsedData.parsed) {
                    details.parsedData = parsedData;
                }

                delete this.requestHeaders[details.id];
            }
        }

        // delete any IDs that are older than 3 minutes. 
        // *** Technical Debt ***
        // Ideally should go in a maintenance thread 
        // *** Technical Debt ***
        let now = Date.now()
        let oldIds = Object.keys(this.requestHeaders).filter(id => now - this.requestHeaders[id].timestamp > 3 * 60 * 1000)
        for (let id of oldIds) {
            delete this.requestHeaders[id]
        }

        // chrome filter for calling this method  will not check port
        if (!this.config.apiDiscovery && !this.scanner.allowedToCrawlUrl(details.url)) {
            utils.log(`*****> LogNetworkActions::onHeadersReceived() ignoring URL ${details.url}`)
            return
        }

        this.scanner.emit('processing-network-request', details.url)

        try {
            // always capture the latest session info 
            this.currentSessionInfo = HaikuUtils.getSessionInfo(details)

            // See if we need to call the API on the soc portal to submit this network action
            let url = new URL(details.url)

            if (url.searchParams && url.searchParams.keys() && Array.from(url.searchParams.keys()).length) {
                // Add query param names to the key
                details.hasParams = true
            }

            if (this.shouldSkipRequest(details)) {
                return
            }

            let sendRequestToScanner = false
            switch (details.resourceType) {
                case "main_frame":
                case "mainFrame": // electron seems to change main_frame to this
                case "sub_frame":
                case "subFrame": // electron seems to change sub_frame to this
                case "xmlhttprequest":
                case "xhr": // another alias ?
                    // always process these requests
                    sendRequestToScanner = true
                    break;

                    // request in font context/script/css context -> don't send all requests
                case "stylesheet":
                case "script":
                case "image":
                case "font":
                case "object":
                case "ping":
                case "csp_report":
                case "media":
                case "other": // one example of this is font loaded from css  processing
                    // Be picky about which to send eg. don't send all URIs with params - too many images etc. have params
                    if (details.method == "POST") {
                        sendRequestToScanner = true
                    } else {
                        // send URI with pathname until last / i.e. skip things like logo.png
                        url.search = '' // remove query params
                        details.hasParams = false
                        let pathComponents = url.pathname.split('/')
                        pathComponents.pop()
                        if (pathComponents.length > 1) {
                            url.pathname = pathComponents.join('/')
                            sendRequestToScanner = true
                        }
                        details.url = url.href
                    }

                    break;

                case "websocket":
                    // never send websocket requests
                    break;

                default:
                    utils.log(`*****> Ignoring unknown resourcetype ${details.resourceType}`)
            }

            if (!sendRequestToScanner) {
                utils.log(`Not sending request to scanner: ${url.pathname}`)
                return
            }

            // ** DON'T do any response content-type filtering, let scanner decide if request is useful **
            // build the unique key 

            // POST - add post param keys
            if (details.method == "POST") {
                let qContentType = details.headers ? details.headers['Content-Type'] : null
                let list = this.acceptedContentTypes.filter((item) => {
                    return qContentType && qContentType.includes(item)
                })
                if (list.length > 0) {
                    let body = []
                    if (details.uploadData) {
                        details.uploadData.forEach((upData, index) => {
                            if (upData.bytes) {
                                body.push(Buffer.from(upData.bytes).toString('ascii'))
                            }
                        })
                    }
                    details.appTranaFullBody = body.join('')

                    // generate the key
                    if (details.appTranaFullBody.length) {
                        details.hasParams = true
                        let searchParams = querystring.parse(details.appTranaFullBody)
                    }
                }
            }

            // some metadata
            details.appTranaKey = HaikuUtils.genHaikuKey(this.makeHaikuAttackableRequest(details))
            details.crawlerBookmark = this.crawlerBookmark

            if(details.crawlerBookmark) {
                try {
                    details.crawlerBookmark.rootActionCount = this.getRootActionCount(details.crawlerBookmark.actionList);   
                } catch (error) {
                    utils.log('unable to get page rootActionCount, reason: ', error.toString());
                }
            }

            // *** 'prevStyleAppTranaKey' TO BE DELETED SOON
            //      build prev style key so that refresh requests will work for revalidation. Since crawler bookmarks 
            //      will only be generated in the new haikuKey style, prev style key code can be removed in a few weeks
            // ***
            let prevStyleAppTranaKey = HaikuUtils._PREV_DELETE_AFTER_15_FEB_2020_genHaikuKey(this.makeHaikuAttackableRequest(details))

            // dup check
            if (this.processingRefreshRequest) {
                if (prevStyleAppTranaKey == this.refreshKey || details.appTranaKey == this.refreshKey) {
                    this.refreshedHttpRequest = this.makeHaikuAttackableRequest(details)
                }
            } else if (!this.seenNetworkRequests.has(details.appTranaKey)) {
                try {
                    // Check if the network request fingerprint is already generated
                    let fingerprint = this.generateFingerprint(details);
                    let fingerprintChanged = false;

                    // Store the fingerprint if it is not already stored for first time network requests
                    if (!this.config.networkRequestFingerprints[details.appTranaKey]) {
                        this.config.networkRequestFingerprints[details.appTranaKey] = {
                            key: fingerprint
                        }
                        fingerprintChanged = true;
                        utils.log('Network request fingerprint first time stored:', details.appTranaKey);
                    }
                    // Update the fingerprint if it has changed during subsequent regular scans,
                    else if (this.config.networkRequestFingerprints[details.appTranaKey].key !== fingerprint) {
                        this.config.networkRequestFingerprints[details.appTranaKey].key = fingerprint;
                        fingerprintChanged = true;
                        utils.log('Network request fingerprint changed and updated:', details.appTranaKey);
                    }

                    // Check if the fingerprint has changed
                    // Skip unchanged network requests if optimizeScan is enabled, for other cases, continue processing
                    // Also log the skipped network requests only once to avoid spamming the logs
                    if (this.config.optimizeScan && !fingerprintChanged) {
                        if (!this.logNetworkRequestFingerprints.has(details.appTranaKey)) {
                            utils.log(`Skipping unchanged network request: ${details.url}, appTranaKey: ${details.appTranaKey}`);
                            this.logNetworkRequestFingerprints.set(details.appTranaKey, details.appTranaKey);
                        }

                        this.seenNetworkRequests.add(details.appTranaKey);
                        return;
                    }
                } catch (error) {
                    utils.log('Error generating fingerprint appTranaKey:', details.appTranaKey, 'error:', error.toString());
                }

                // add the action to network actions to be sent
                this.attackableNetworkRequests.push(details)
            }

        } catch (err) {
            utils.log('onHeadersReceived error:', err.toString())
        }
    }

    shouldSkipRequest(details) {
        // if we are refreshing, don't skip
        if (this.processingRefreshRequest) {
            return false
        }

        let skipRequest = false;

        // Response status code check for simple (non query, non POST requests)
        if (details.method != "POST" && !details.hasParams) {
            if (details.statusCode >= this.statusCodeCutoff && -1 == this.alwaysSendStatusCodes.indexOf(details.statusCode)) { // status code not in always send list eg. 403
                // we expect a lot of 404, 403 etc. during the initial seed url part so for that, always skip 
                let currentCrawlerDepth = 0;
                if (this.scanner.crawlState.currentContext && this.scanner.crawlState.currentContext.depth) {
                    currentCrawlerDepth = this.scanner.crawlState.currentContext.depth;
                }
                if (currentCrawlerDepth == 0) {
                    skipRequest = true
                } else {
                    // for regular crawling, send such requests until the cutoff.
                    if (++this.cutoffRequestsSeen >= this.requestsToSendBeforeCutoff) {
                        skipRequest = true;
                    }
                }
            }
        }
        return skipRequest;
    }

    async sendNetworkActionToScanners(details) {
        let foundDate = new Date().toISOString().replace(/\..+/, '') //"2017-05-20T10:49:39"

        let networkActionInfo = {
            scanId: this.config.scanId,
            scanlog_id: this.config.scanLogId,
            scanner: 'haiku',
            mainUrl: this.config.mainUrl,
            networkActions: [this.makeHaikuAttackableRequest(details)]
        }

        fs.appendFileSync(this.outFile, JSON.stringify(networkActionInfo) + '\r\n')

        // Haiku also wants plain URLS (to attack URI path and headers) 
        this.scanner.emit('network-action', networkActionInfo)
    }

    /**
     * Create a Haiku AttackableNetworkRequest from crawler captured details
     * @param {details} details details of request as captured in crawler (electron)
     */
    makeHaikuAttackableRequest(details) {
        let isApiRequest = HaikuUtils.isApiRequest(details);

        details.SiteType = isApiRequest ? SITE_TYPE.API : SITE_TYPE.WESBITE;

        let httpReq = {
            url: details.url,
            method: details.method,
            headers: details.headers,
            body: details.appTranaFullBody,
            appTranaKey: details.appTranaKey,
            crawlerBookmark: details.crawlerBookmark,
            resourceType: details.resourceType,
            ip: details.ip, //Required by WAS for API discovery
            SiteType: details.SiteType, // Required by WAS for API discovery
            DataCenter: '', // Required by WAS for API discovery,
            uploadData: details.uploadData, // File upload data
            parsedData: details.parsedData, // Parsed multipart data
        }

        //Store the response headers, like content-type for API discovery
        if (details.responseHeaders) {
            if (details.responseHeaders['Content-Type']) {
                httpReq.responseHeaders = {};
                httpReq.responseHeaders['Content-Type'] = details.responseHeaders['Content-Type'];
            }

            if(details.responseHeaders['content-type']) {
                httpReq.responseHeaders = httpReq.responseHeaders || {};
                httpReq.responseHeaders['Content-Type'] = details.responseHeaders['content-type'];
            }
        }

        if (details.statusCode) {
            httpReq.statusCode = details.statusCode;
        }

        // annotate with login info if it is present and appears to be a login request.
        if (this.loginInfo) {
            let paramsAndValues = HaikuUtils.splitIntoParamsAndValues(httpReq)
            // see if un and pwd appear in the query/post values
            let isLoginRequest = paramsAndValues.values.includes(this.loginInfo.username) && paramsAndValues.values.includes(this.loginInfo.password)

            // if username is the same as password eg. admin/admin, then make sure 'admin' appears twice
            if (isLoginRequest && this.loginInfo.username == this.loginInfo.password) {
                let unIdx = paramsAndValues.values.indexOf(this.loginInfo.username)
                let pwdIdx = paramsAndValues.values.indexOf(this.loginInfo.password, unIdx + 1)
                if (unIdx == -1 || pwdIdx == -1) {
                    isLoginRequest = false
                }
            }

            if (isLoginRequest) {
                httpReq.loginInfo = this.loginInfo
            }
        }


        return httpReq
    }

    /**
     * Get root level action count to reach a page using bookmark actions
     * @param {object} rootActions bookmark actions
     * @param {double} actionCount bookmark actions count to reach page. default to 0
     * @param {boolean} isReplay whether action list is of type replay actions. default to false
     */
    getRootActionCount(rootActions, actionCount = 0, isReplay = false) {
        try {
            if(rootActions) {
                if(rootActions.actions || isReplay) {
                    let test = rootActions;
                    let actions = _.isArray(rootActions) ? rootActions : rootActions.actions;
                    for (let index = 0; index < actions.length; index++) {
                        let action = actions[index];

                        if(action.name) {
                            if(action.name.startsWith('replay-need')) {
                                actionCount = this.getRootActionCount(action.actions, actionCount, true);
                            }
                            else if(!action.name.startsWith('replay-need') && action.actionType == 'action-list') {
                                actionCount++;
                            }
                        }
                    }
                }
            }
        } catch (error) {
            utils.log('getRootActionCount error:', error.toString());
        }

        return actionCount;
    }

    /**
     * Generate a fingerprint for the network
     * @param {Object} details Details of the network request
     * @returns {String} Fingerprint of the network request
     */
    generateFingerprint(details) {
        // Check if the fingerprint is already generated, if yes, return
        try {
            let hash = crypto.createHash('sha256');
            hash.update(details.url);
            hash.update(details.method);

            if (details.headers) {
                hash.update(JSON.stringify(Object.keys(details.headers)));
            }

            if (details.appTranaKey) {
                hash.update(details.appTranaKey);
            }

            return hash.digest('hex');   
        } catch (error) {
            utils.log('generateFingerprint error:', error.toString());
        }
    }
}

module.exports = LogNetworkActions