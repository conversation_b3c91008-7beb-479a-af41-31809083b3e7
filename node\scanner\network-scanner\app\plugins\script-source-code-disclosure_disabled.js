const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

/**
 * Script Source Code Disclosure Plugin Strategy:
 * For all the network request we will inject it with uri query param payloads, we will try to  inject various file * paths and see if the script source is revealed.
 * The response verification should give 200 ok status
 */

class ScriptSourceCodeDisclosure extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-script-source-code-disclosure'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            attackParamName: false
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return filePathVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {

        //Change/update user agent to a imitate an older browser like IE
        attack.httpRequest.headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"

        return await super.performNetworkAttack(attack)
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {

        //Return of attack not made by this plugin
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginStorage = this.getPluginScopedStore(attack, 'original-crawler-request')

        //if one attack already made then don't attack further
        // if (pluginStorage.SSCD) {
        //     return
        // }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, "result.resp.body", '')

        //Adding condition to skip for the custom error pages
        let CustomErrMsg1 = RegExpVari.RegExp.CustomErrMsg1
        let CustomErrMsg2 = RegExpVari.RegExp.CustomErrMsg2
        let CustomErrMsg3 = RegExpVari.RegExp.CustomErrMsg3
        let CustomErrMsg4 = RegExpVari.RegExp.CustomErrMsg4
        let CustomErrMsg5 = RegExpVari.RegExp.CustomErrMsg5
        if (CustomErrMsg1.test(ResBody) || CustomErrMsg2.test(ResBody) || CustomErrMsg3.test(ResBody) || CustomErrMsg4.test(ResBody) || CustomErrMsg5.test(ResBody)) { return }

        if (attack.result.resp.httpResponse.err) {
            return
        }

        //Below condition added to remove FPs
        if (body.length == 0) { return }

        //Below condition added to remove the FPs 
        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(body)) {
            return
        }

        //Below condition added to remove the FPs 
        if (attack.href.includes("/examples/servlets/")
            || attack.href.includes("/examples/jsp/")
            || attack.href.includes("/examples/websocket/")) {
            return
        }

        let CTHeader = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')

        // report only when response code is 200 ok
        // then only report the vuln

        if (statusCode == "200") {

            //if any of the below content type headers are found then return and don't report
            if (/text\/html|application\/json|text\/json/i.test(CTHeader)) { return }

            /* // rule out the conditions for dummy page errors
            if (/http\sstatus\s404/i.test(attack.result.resp.body) ||
                /404\s-\sfile\sor\sdirectory\snot\sfound/i.test(attack.result.resp.body) ||
                /((error:?|404|page)(.page.not.found|.404|.error))/i.test(attack.result.resp.body) ||
                /(type=.password.|url\sis\sincorrect)/i.test(attack.result.resp.body) ||
                /(author|page.not.found|experiencing\stechnical\sproblems)/i.test(attack.result.resp.body) ||
                /((<title>[\n\t\s]{0,}error|error.has.occurred|go.back.to.site|something.went.wrong|please.try.again.later|sorry|access.denied|you.do.not.have.access|session.expired|s.o.r.r.y|url.is..?incorrect|invalid.access|we.encountered.a.problem|this\serror\scontinues))/i.test(attack.result.resp.body)
                || /some\serror\shas\soccured\swhile\sprocessing\syour\srequest/i.test(attack.result.resp.body)
                || /We\scannot\sfind\sthe\spage\syou\sare\slooking\sfor/i.test(attack.result.resp.body)
                || /Oops(.*)Looks\slike\sthis\spage\shas(.*)gone\smissing/si.test(attack.result.resp.body)) { return }

            // if response contains any WAF blockage then ignore it
            if (/This\serror\swas\sgenerated\sby\sMod_Security|One\sor\smore\sthings\sin\syour\srequest\swere\ssuspicious|rules\sof\sthe\smod_security\smodule|mod_security\srules\striggered|The\srequested\sURL\swas\srejected.\sPlease\sconsult\swith\syour\sadministrator.|Cloudflare\sRay\sID:|DDoS\sprotection\sby\sCloudflare|WebKnight\sApplication\sFirewall\sAlert|AQTRONIX\sWebKnight|Sucuri\sWebsite\sFirewall|<h1\>invalid\surl<\h1\>|<title\>access\sdenied<\title\>|_Incapsula_Resource|Incapsula\sincident\sID:|Contact\ssupport\sfor\sadditional\sinformation.<br\>The\sincident\sID\sis:|You\shave\sbeen\sblocked|You\sare\sunable\sto\saccess\sthis\swebsite|This\swebsite\sis\ssecured\sagainst\sonline\sattacks.\sYour\srequest\swas\sblocked\sdue\sto\ssuspicious\sbehavior|Incident\sID/i.test(attack.result.resp.body)) { return } */

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            //pluginStorage.SSCD = true
            return
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;

        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, ['uri', 'requestLine'], `orignal request query ${attack.param} param value will be attacked`);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ['uri', 'requestLine'], `attack request query ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ['User-Agent']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);

    }
}

const filePathVectors = [
    `./database_connect.php`,
    `../passwd%00`,
    `../database.asp`,
    `passwd`,
    `.htpasswd`,
    `./core/config.php%00`,
    `./database.asp%00`,
    `./core/config.php`,
    `../core/config.php%00`,
    `../core/config.php`,
    `/core/config.php`,
    `../database.php%00`,
    `./users.db.php`,
    `../config.inc.php`,
    `../database.php`,
    `../database.js%00`,
    `config.php`,
    `../pass.dat`,
    `../install.php`,
    `./config.inc.php`,
    `./_config.php%00`,
    `./_config.php`,
    `./config.asp%00`,
    `config.inc.php`,
    `../config.asp%00`,
    `/config.asp`,
    `admin/access_log`,
    `/../../../../pswd`,
    `../root/.htpasswd`,
    `./users.db.php%00`,
    `../config.js%00`,
    `./database.php%00`,
    `../config.php%00`,
    `../config.php`,
    `./config.js`,
    `.pass`,
    `./config.php%00`,
    `database.php`,
    `data.php`,
    `../db.php`,
]

module.exports = ScriptSourceCodeDisclosure