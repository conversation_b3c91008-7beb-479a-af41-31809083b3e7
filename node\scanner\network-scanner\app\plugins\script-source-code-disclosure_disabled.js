const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')
const RegExpVari = require('./generic-regexp');

/**
 * Script Source Code Disclosure Plugin Strategy:
 * For all the network request we will inject it with uri query param payloads, we will try to  inject various file * paths and see if the script source is revealed.
 * The response verification should give 200 ok status
 */

class ScriptSourceCodeDisclosure extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization 
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-script-source-code-disclosure'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            addExtraParam: false,
            attackParamName: false
        });
    }

    /**
     * get array of attack vectors
     * @override
     */
    getAttackVectors() {
        return filePathVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-query-params']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to change method in attack and it's body specifically 
     */
    async performNetworkAttack(attack) {

        //Change/update user agent to a imitate an older browser like IE
        attack.httpRequest.headers["User-Agent"] = "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"

        return await super.performNetworkAttack(attack)
    }


    /**   
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {

        //Return of attack not made by this plugin
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginStorage = this.getPluginScopedStore(attack, 'original-crawler-request')

        //if one attack already made then don't attack further
        // if (pluginStorage.SSCD) {
        //     return
        // }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        let body = _.get(attack, "result.resp.body", '')

        if (!body.trim()) return;

        //condition to skip for the custom error pages
        const responseBody = body.toLowerCase();

        // Check each category of error messages
        const errorCategories = [
            RegExpVari.ErrorMessages.HttpErrors,
            RegExpVari.ErrorMessages.SecurityErrors,
            RegExpVari.ErrorMessages.SessionErrors,
            RegExpVari.ErrorMessages.SystemErrors,
            RegExpVari.ErrorMessages.WafErrors,
            RegExpVari.ErrorMessages.GeneralErrors
        ];

        // Early return if any error message is found
        for (const category of errorCategories) {
            if (category.some(error => responseBody.includes(error))) {
                return;
            }
        }

        // Check for WAF servers in headers
        let ResHeaders = _.get(attack, 'result.resp.httpResponse.headers', '')
        ResHeaders = Object.entries(ResHeaders).join('&').replace(/,/g, ': ').split('&').join(' ').toLowerCase()

        if (RegExpVari.WafServers.some(server => ResHeaders.includes(server))) {
            return;
        }

        if (attack.result.resp.httpResponse.err) {
            return
        }

        //Below condition added to remove FPs
        if (body.length == 0) { return }

        //Below condition added to remove the FPs 
        if (attack.href.includes("/docs/") && /The\sApache\sSoftware\sFoundation/i.test(body)) {
            return
        }

        //Below condition added to remove the FPs 
        if (attack.href.includes("/examples/servlets/")
            || attack.href.includes("/examples/jsp/")
            || attack.href.includes("/examples/websocket/")) {
            return
        }

        let CTHeader = _.get(attack, 'result.resp.httpResponse.headers["content-type"]')

        if (statusCode == "200") {
            //if any of the below content type headers are found then return and don't report
            if (/text\/html|application\/json|text\/json/i.test(CTHeader)) return;

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID != this.vulnerabilityID) {
            return;

        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest', `param`, ['uri', 'requestLine'], `orignal request query ${attack.param} param value will be attacked`);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, ['uri', 'requestLine'], `attack request query ${attack.param} param value tampered with vector ${encodeURIComponent(attack.vector)}`);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.headers', `param`, ['User-Agent']);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ['statusCode']);

    }
}

const filePathVectors = [
    `./database_connect.php`,
    `../passwd%00`,
    `../database.asp`,
    `passwd`,
    `.htpasswd`,
    `./core/config.php%00`,
    `./database.asp%00`,
    `./core/config.php`,
    `../core/config.php%00`,
    `../core/config.php`,
    `/core/config.php`,
    `../database.php%00`,
    `./users.db.php`,
    `../config.inc.php`,
    `../database.php`,
    `../database.js%00`,
    `config.php`,
    `../pass.dat`,
    `../install.php`,
    `./config.inc.php`,
    `./_config.php%00`,
    `./_config.php`,
    `./config.asp%00`,
    `config.inc.php`,
    `../config.asp%00`,
    `/config.asp`,
    `admin/access_log`,
    `/../../../../pswd`,
    `../root/.htpasswd`,
    `./users.db.php%00`,
    `../config.js%00`,
    `./database.php%00`,
    `../config.php%00`,
    `../config.php`,
    `./config.js`,
    `.pass`,
    `./config.php%00`,
    `database.php`,
    `data.php`,
    `../db.php`,
]

module.exports = ScriptSourceCodeDisclosure