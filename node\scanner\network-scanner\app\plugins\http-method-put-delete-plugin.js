const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

// checks for PUT method first, then checks for DELETE method, if found reports both vulnerabilities separately
class PutDeleteMethodPlugin extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)
        this.vulnPutID = 'ID-put-method-enabled'
        this.vulnDeleteID = 'ID-delete-method-enabled'
    }

    processAttackResponse(attack) {
        let redirect = _.get(attack, 'result.resp.httpResponse.redirectsFollowed')
        if (redirect == 0) {
            return
        }

        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode >= 300) {
            return
        }

        let allowHeader = ''
        allowHeader = _.get(attack, 'result.resp.httpResponse.headers["allow"]', '')

        //Access-Control-Allow-Methods
        // access-control-allow-methods:GETPUTPOSTDELETE
        if (allowHeader.length == 0) {
            allowHeader = _.get(attack, 'result.resp.httpResponse.headers["access-control-allow-methods"]', '')
        }

        /**
         * If the response has ALLOW header then it will add it to the vulnerability list
         */
        if (allowHeader.length > 0) {
            if (/PUT/i.test(allowHeader)) {
                this.addVulnerabilitytoResult(attack, this.vulnPutID, allowHeader)
            }
            if (/DELETE/i.test(allowHeader)) {
                this.addVulnerabilitytoResult(attack, this.vulnDeleteID, allowHeader)
            }
        }
    }

    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);
        if (vulnID == 'ID-put-method-enabled' || vulnID == 'ID-delete-method-enabled') {
            HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["allow"]);
        }
    }
}

module.exports = PutDeleteMethodPlugin