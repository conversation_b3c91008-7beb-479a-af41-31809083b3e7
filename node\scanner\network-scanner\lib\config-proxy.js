const _ = require('lodash');

/**
 * Configuration proxy that returns either the proxied config's properties OR the default config's properties
 * set (writes) always happen on the proxied object
 * Reference: {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy}
 * 
 * Mainly intended for proxying Haiku config so that we can handle site specific configurations
 * easily. This will return values from site specific config first and then fall back to the 
 * default configuration. 
 * ConfigProxy will only proxy first level properties. If needed (eg. Plugins in network scanner), 
 * further Basic proxies can be created to return either siteSpecific-Object[key] / default-Object[key]
 * @see proxyConfig static helper method in this file 
 * 
 * For all such 'one level down' properties, taking as an example 'Plugins': even one Plugin specific value 
 * is changed for a plugin, all unchanged (default) params should be copied over. This will be implemented 
 * by tooling that will generate the appropriate 'diff from default' site specific config.
 */
class ConfigProxy {
    /**
     * @param {Object} defaultConfig default config to fall back to if site specific config does not have a property
     */
    constructor(defaultConfig) {
        this.defaultConfig = defaultConfig
    }

    /**
     * Reeturn value of property from site specific config or default config
     * @param {Object} target the target object
     * @param {*} prop The name or Symbol  of the property to get.
     * @param {*} receiver Either the proxy or an object that inherits from the proxy
     */
    get(target, prop, receiver) {
        // see if the 
        return prop in target ?
            Reflect.get(target, prop, receiver) :
            Reflect.get(this.defaultConfig, prop, receiver)
    }

    /**
     * return superset of wrapped and default objects' keys
     * @param {Object} target the target object
     */
    ownKeys(target) {
        return Array.from(new Set([...Reflect.ownKeys(target), ...Reflect.ownKeys(this.defaultConfig)]))
    }

    /**
     * proxy to wrapped config if it has the key otherwise proxy to the default config
     * @param {Object} target the target object
     * @param {*} prop The name or Symbol  of the property to get.
     */
    getOwnPropertyDescriptor(target, prop) {
        if (target[prop]) {
            return Reflect.getOwnPropertyDescriptor(target, prop)
        }
        return Reflect.getOwnPropertyDescriptor(this.defaultConfig, prop)
    }

    /**
     * proxy to wrapped config if it has the key otherwise proxy to the default config
     * @param {Object} target the target object
     * @param {*} key The name or Symbol of the property to check for existence.
     */
    has(target, key) {
        return Reflect.has(target, key) || Reflect.has(this.defaultConfig, key)
    }

    /**
     * Proxy site specific network scanner config with deeper proxying for 'Plugins' property
     * @param {Object} siteConfig site specific config whose properties override the default config
     * @param {Object} defaultConfig default config
     * @param {string} propertyToForceProxy property name which you want to force proxy. i.e. ScannerSettings
     * @param {Array} propertiesToDeepProxy array of properties to proxy one level down eg. 'Plugins'
     *                for Network Scanner config
     */
    static proxyConfig(siteConfig, defaultConfig, propertyToForceProxy = '', propertiesToDeepProxy = 'auto') {
        if (propertiesToDeepProxy == 'auto') {
            propertiesToDeepProxy = defaultConfig.propertiesToDeepProxy || []
        }
        let wrappedConfig = new Proxy(siteConfig, new ConfigProxy(defaultConfig))
        for (let property of propertiesToDeepProxy) {
            if (siteConfig[property]) {
                wrappedConfig[property] = new Proxy(siteConfig[property], new ConfigProxy(defaultConfig[property]))
            } else {
                if (property && !siteConfig[property]) {
                    siteConfig[property] = {};
                }

                //TO DO: Remove below code once we get ScannerSettings from WAS via recipes, this is temporary solution 
                //in order to run api scan & revalitdation without affecting other scans. - Nilesh Bhope
                //Updated with newer config to allow both networkScanner & defaultConfig.propertiesToDeepProxy to
                // work in backword compatibility. Once WAS start sending api & revalidation scan related config 
                //over ScannerSettings, remove below code between start to end - Nilesh Bhope 28 March 2023
                //CodeStart
                if (propertyToForceProxy) {
                    if (!siteConfig[propertyToForceProxy]) {
                        siteConfig[propertyToForceProxy] = {};
                    }

                    for (let propName in siteConfig) {
                        if (_.has(defaultConfig, `${propertyToForceProxy}.${propName}`, null)) {
                            siteConfig[propertyToForceProxy][propName] = siteConfig[propName];
                        }
                    }
                }
                //CodeEnd

                wrappedConfig[property] = new Proxy(siteConfig[property], new ConfigProxy(defaultConfig[property]))
            }

            // Ensure nested properties are also proxied
            for (let nestedProperty in siteConfig[property]) {
                if (siteConfig[property][nestedProperty] !== null && typeof siteConfig[property][nestedProperty] === 'object' && !(siteConfig[property][nestedProperty] instanceof RegExp)) {
                    wrappedConfig[property][nestedProperty] = new Proxy(siteConfig[property][nestedProperty], new ConfigProxy(defaultConfig[property][nestedProperty]));
                }
            }

            // Ensure vulnerabilities are also proxied
            if (property === 'Plugins') {
                //Create recursive object for loop 
                for (let plugin in siteConfig[property]) {
                    if (siteConfig[property][plugin].vulnerabilities) {
                        for (let vuln in defaultConfig[property][plugin].vulnerabilities) {
                            if (!siteConfig[property][plugin].vulnerabilities[vuln]) {
                                siteConfig[property][plugin].vulnerabilities[vuln] = new Proxy({}, new ConfigProxy(defaultConfig[property][plugin].vulnerabilities[vuln]));
                            }
                            else {
                                siteConfig[property][plugin].vulnerabilities[vuln] = new Proxy(siteConfig[property][plugin].vulnerabilities[vuln], new ConfigProxy(defaultConfig[property][plugin].vulnerabilities[vuln]));
                            }
                        }
                    }
                }
            }
        }
        return wrappedConfig
    }
}

module.exports = ConfigProxy