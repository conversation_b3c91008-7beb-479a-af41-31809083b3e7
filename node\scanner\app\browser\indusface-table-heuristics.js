class IndusfaceTableHeuristics {
    constructor() {
        indusfaceRenderer.getAgGridTableRow = this.getAgGridTableRow;
    }

    /**
     * Returns the name of the heuristic.
     * @param {boolean} short - If true, returns an abbreviated name; otherwise, returns the full name.
     * @returns {string} The name of the heuristic.
    */
    name(short) {
        return short ? "IFC-TH" : "Indusface Table Heuristics";
    }

    /**
     * Initializes the heuristic.
    */
    init() {
        /* Placeholder for initialization logic. */
    }

    /**
     * 
     * @returns {Array} An array of interesting ag grid first row items.
     */
    getAgGridTableRow(interestingItems) {
        /* 
        Extracts and returns matching XPaths of table rows from an AG Grid table.
        - Searches for the root element with the class 'ag-root'.
        - Checks for visible containers within the 'ag-body'.
        - Collects XPaths of child elements from the first row of each container.
        - Iterates through all interesting items, example inputItems, check if they belong to this table as childen of ag-root,
        - by comparing fullXpath of item with fullXpath of first row of each container using startsWith check
        */
        try {
            // Find the first element with the class 'ag-root'
            let agRootElement = document.querySelector('.ag-root');
            let agRootFullXpath = indusfaceRenderer.getFullXpath(agRootElement);

            // Check if the element exists and contains an 'ag-body'
            if (agRootElement) {
                let agBody = agRootElement.querySelector('.ag-body');
                
                if (agBody) {
                    let leftContainerElement = agBody.querySelector('.ag-pinned-left-cols-container');
                    let centerContainerElement = agBody.querySelector('.ag-center-cols-container');
                    let rightContainerElement = agBody.querySelector('.ag-right-cols-container');

                    let leftContainerFirstRowFullXpath = leftContainerElement ? indusfaceRenderer.getFullXpath(leftContainerElement.querySelector('.ag-row')) : null;
                    let centerContainerFirstRowFullXpath = centerContainerElement ? indusfaceRenderer.getFullXpath(centerContainerElement.querySelector('.ag-row')) : null;
                    let rightContainerFirstRowFullXpath = rightContainerElement ? indusfaceRenderer.getFullXpath(rightContainerElement.querySelector('.ag-row')) : null;

                    interestingItems = interestingItems.filter((item) => {
                        if(!item[1].fullPath) {
                            return true;
                        }

                        if(leftContainerFirstRowFullXpath && item[1].fullPath.startsWith(leftContainerFirstRowFullXpath)) {
                            item[1].isTableItem = true;
                            return true;
                        }
                        else if(centerContainerFirstRowFullXpath && item[1].fullPath.startsWith(centerContainerFirstRowFullXpath)) {
                            item[1].isTableItem = true;
                            return true;
                        }
                        else if(rightContainerFirstRowFullXpath && item[1].fullPath.startsWith(rightContainerFirstRowFullXpath)) {
                            item[1].isTableItem = true;
                            return true;
                        }
                        else {
                            //if item is part of agRoot, we can filter it out because it is not part of any table container
                            if(item[1].fullPath.startsWith(agRootFullXpath)) {
                                return false;
                            }
                            else {
                                return true;
                            }
                        }
                    });

                    return interestingItems;
                }
            }
        } catch (error) {
            /* Logs any errors encountered during the execution of the method. */
            console.error('Error in getAgGridTableRow:', error);
        }

        return interestingItems;
    }
}

module.exports = IndusfaceTableHeuristics;