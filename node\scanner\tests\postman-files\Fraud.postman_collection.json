{"info": {"_postman_id": "b0653b5e-da06-4704-8448-6289558d2844", "name": "<PERSON><PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Ecommerce", "request": {"auth": {"type": "basic", "basic": [{"key": "username", "value": "zIPNLFV0NDHE93VRO", "type": "string"}, {"key": "password", "value": "091e0a57-07e1-4947-af8d-fb0bdfbed71c", "type": "string"}]}, "method": "POST", "header": [{"key": "", "value": "", "type": "text", "disabled": true}, {"key": "Content-Type", "value": "application/json", "type": "text"}, {"warning": "This is a duplicate header and will be overridden by the Authorization header generated by Postman.", "key": "Authorization", "value": "Basic eklQTkxGVjBOREhFOTNWUk86MDkxZTBhNTctMDdlMS00OTQ3LWFmOGQtZmIwYmRmYmVkNzFj", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n  \"billing\": {\r\n    \"address1\": null,\r\n    \"address2\": null,\r\n    \"city\": \"\",\r\n    \"company\": null,\r\n    \"country\": null,\r\n    \"email\": null,\r\n    \"first_name\": null,\r\n    \"last_name\": null,\r\n    \"phone\": null,\r\n    \"postal_code\": \"\",\r\n    \"region\": null\r\n  },\r\n  \"campaign\": {\r\n    \"content\": \"string\",\r\n    \"medium\": \"string\",\r\n    \"name\": \"string\",\r\n    \"source\": \"string\",\r\n    \"term\": \"string\"\r\n  },\r\n  \"delivery\": {\r\n    \"courier\": null,\r\n    \"method\": null,\r\n    \"weight\": null\r\n  },\r\n  \"device\": {\r\n    \"canvas_fingerprint_id\": \"string\",\r\n    \"canvas_height\": 123,\r\n    \"canvas_width\": 123,\r\n    \"city\": \"string\",\r\n    \"client_id\": \"string\",\r\n    \"color_depth\": 123,\r\n    \"cookie_support\": true,\r\n    \"country\": \"string\",\r\n    \"fingerprint_id\": \"string\",\r\n    \"font_smoothing\": true,\r\n    \"fonts_hash\": \"string\",\r\n    \"geo\": \"1.1,1.1\",\r\n    \"http_referer\": \"string\",\r\n    \"ip_address\": null,\r\n    \"ip_type\": \"\",\r\n    \"is_proxy\": true,\r\n    \"is_tor\": true,\r\n    \"java_support\": true,\r\n    \"language\": \"string\",\r\n    \"mime_types_hash\": \"string\",\r\n    \"num_fonts\": 123,\r\n    \"num_mime_types\": \"string\",\r\n    \"num_plugins\": 123,\r\n    \"plugin_hash\": \"string\",\r\n    \"plugins_hash\": \"string\",\r\n    \"postal_code\": \"string\",\r\n    \"resolution\": null,\r\n    \"screen_height\": 123,\r\n    \"screen_width\": 123,\r\n    \"service\": \"none\",\r\n    \"session_id\": \"string\",\r\n    \"time_zone\": \"string\",\r\n    \"timezone\": \"string\",\r\n    \"touch_support\": true,\r\n    \"user_agent\": null\r\n  },\r\n  \"products\": [\r\n    {\r\n      \"brand\": null,\r\n      \"brand_id\": null,\r\n      \"category\": null,\r\n      \"discount\": 123,\r\n      \"img\": null,\r\n      \"options\": [\r\n        {\r\n          \"option\": \"string\",\r\n          \"option_set\": \"string\",\r\n          \"value\": \"string\"\r\n        }\r\n      ],\r\n      \"price\": \"\",\r\n      \"product_id\": \"string\",\r\n      \"product_is_digital\": true,\r\n      \"quantity\": 123,\r\n      \"seller_id\": null,\r\n      \"sku\": null,\r\n      \"tags\": null,\r\n      \"title\": \"string\",\r\n      \"upc\": null,\r\n      \"url\": null\r\n    }\r\n  ],\r\n  \"promotions\": [\r\n    {\r\n      \"amount\": 123,\r\n      \"code\": \"string\",\r\n      \"name\": \"string\"\r\n    }\r\n  ],\r\n  \"shipping\": {\r\n    \"address1\": null,\r\n    \"address2\": null,\r\n    \"city\": \"\",\r\n    \"company\": null,\r\n    \"country\": null,\r\n    \"email\": null,\r\n    \"first_name\": null,\r\n    \"last_name\": null,\r\n    \"phone\": null,\r\n    \"postal_code\": \"\",\r\n    \"region\": null\r\n  },\r\n  \"transaction\": {\r\n    \"agent_code\": \"string\",\r\n    \"agent_dept\": \"string\",\r\n    \"coupon_code\": \"string\",\r\n    \"event\": \"string\",\r\n    \"fee\": 123,\r\n    \"first_purchase_date\": \"2021-01-25T20:17:53+00:00\",\r\n    \"geo\": \"1.1,1.1\",\r\n    \"iban\": \"string\",\r\n    \"ident_country\": \"string\",\r\n    \"ident_id\": \"string\",\r\n    \"ident_type\": \"ssn\",\r\n    \"last_purchase_date\": \"2021-01-25T20:17:53+00:00\",\r\n    \"order_count\": 123,\r\n    \"order_currency\": \"USD\",\r\n    \"order_discount\": 123,\r\n    \"order_id\": \"string123e111111\",\r\n    \"order_is_digital\": false,\r\n    \"order_shipping\": 123,\r\n    \"order_source\": \"string\",\r\n    \"order_subtotal\": 123,\r\n    \"order_tax\": 123,\r\n    \"order_total\": 123,\r\n    \"ordered_on\": \"2021-01-25T20:17:53+00:00\",\r\n    \"session_id\": \"string\",\r\n    \"shipped_on\": \"2021-01-25T20:17:53+00:00\",\r\n    \"status\": \"new\",\r\n    \"total_spent\": 123,\r\n    \"transaction_id\": \"string\",\r\n    \"type\": \"sale\",\r\n    \"user_id\": \"string\",\r\n    \"user_locale\": \"string\"\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://api-sandbox.c008-m008-us.fraud.net/v2/risk/order/ecommerce", "protocol": "https", "host": ["api-sandbox", "c008-m008-us", "fraud", "net"], "path": ["v2", "risk", "order", "ecommerce"]}}, "response": []}, {"name": "Marketplace", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Travel", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Processors", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Order update", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Bank_POST", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "BANK_PATCH", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Loan_Appln_POST", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Loan_Appln_PATCH", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Identity_POST", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Identity_PATCH", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}, {"name": "Identity_POST", "request": {"method": "GET", "header": [], "url": {"raw": ""}}, "response": []}]}