const VectorResponseAttack = require('./vector-response-attack')
const _ = require('lodash')
const RE2 = require('re2')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/**
 * PHPNginx RCE Plugin Strategy:
 * We will be attacking only when nginx server is detected with rce 
 * vectors in uri query params only, and if received successful response then will mark it as vulnerable.
 * 
 * Reference: https://induswaf.atlassian.net/wiki/spaces/SIGDEV/pages/820641795/PHP-FPM+Nginx+-+Remote+Code+Execution
 */
class PHPNginxRCE extends VectorResponseAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-php-nginx-cmd-injection'

        // Combine the regexps - more efficient than running 'n' matches
        this.matchRegexp = new RE2(cmdInjMatch.map((v) => {
            return v.source
        }).join('|'), "i")
    }

    /**
     * get array of cmd Injection attack vectors
     * @override
     */
    getAttackVectors() {
        return phpCmdInjVectors
    }

    getAttackableEvents() {
        return ['uri-query-params']
    }

    /**
     * 
     * @param {method} attack
     * Overriding the performNetworkAttack method to only attack for specific server 
     */
    async performNetworkAttack(attack) {
        // always perform the initial attack
        let serverHeader = _.get(attack, 'originalRequest.httpResponse.headers["server"]')

        //Attack only if server is nginx
        if (/nginx/i.test(serverHeader)) {
            await super.performNetworkAttack(attack)
        }
        return false
    }

    // event handler, annotates attack parameter, no return value
    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        // Only check responses for attacks that we sent
        if (attack.pluginName != this.getName()) {
            return
        }

        //if vuln detected for a req then return
        // changing scope from default to "this-scan" for attacking only once per scan and
        // change the number of instance to report in network-scan-config to 1
        let pluginStorage = this.getPluginScopedStore(attack, 'this-scan')

        //if one vulnerability already found then don't attack further
        if (pluginStorage.phpRCE) {
            return
        }

        let vulnFound = this.checkBodyForVuln(attack, this.matchRegexp, this.vulnerabilityID)
        if (vulnFound) {
            pluginStorage.phpRCE = true
            return
        }
    }

    //Parameter and it's values(attack vector) and matched content(Captured by plugin) in response body, Response header: Server.
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse.headers', `param`, ["server"]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest', `param`, [attack.param]);
    }
}

// vectors & matches ...
const phpCmdInjVectors = [
    `id`,
    `/bin/sh+-c+'which+which'&`
]

const cmdInjMatch = [
    /\/bin\/which/,
    /\/usr\/bin\/which/,
    /uid=(.*)gid=(.*)groups=(.*)/,
]
module.exports = PHPNginxRCE