/** 
 * Use .js instead of JSON as its flexible and can handle comments.
 */

module.exports = {
    // a little self referential properties to Deep Proxy
    propertiesToDeepProxy: ['HTTPRequestMonster', 'DefaultPluginSettings', 'Plugins', 'ScannerSettings'],

    // Network scanner 
    maintenanceIntervalSecs: .5 * 60, // how frequently to run maintenance proc 
    maxMaintenanceIterationsAfterScanComplete: 10, // after this assume all requests have ceomplete ** this is a bandaid - have to investigate and fix **
    reportLongRunningFunctions: 10 * 1000, // what is considererd long running and should report in milliseconds

    // revalidate requests where vuln was found in previous scan first to minimize deviations.
    // each time we revalidate and don't find a vuln, we decrement a counter in the request. 
    // Once it reaches 0, we will remove request from revalidate
    revalidateDecayInitialVal: 7,

    // ***** HTTP Request Monster *****    
    HTTPRequestMonster: {
        // global, not per site
        maintenanceIntervalSecs: 5, // how frequently to run HTTP Request Monster's maintenance proc 
        maxParallelRequests: 5000, // global max cannot be overridden
        allowedContentTypes: /text\/(html|xml)/i, // specific plugins customize this but not set at a per site level.

        // Ones below can be overridden by site specific config
        perDomainMaxParallelRequests: 10,
        defaultMaxResponseSize: 750 * 1024, // restrict responses to 750K

        // token bucket tuning
        maxRequestsPerMin: 500, // max requests per minute
        maxBurstRate: 3 // determines bucketSize as a factor of how many intervals worth of tokens
    },

    // configurable scanner settings. Optimization settings etc. will come here
    ScannerSettings: {
        // Run revalidation of previously found vulnerabilities?
        doRevalidate: 'first', // values: first, never, only

        // ask crawler to refresh requests before starting attack?
        skipRefreshRequestBeforeAttack: false,

        // Should we avoid crawling? Usually this is used in conjunction with 
        // usePreviousCrawlInfo and/or additionalRequests
        skipCrawl: false, // this is always reflected to crawler

        // Use information from last scan
        usePreviousCrawlInfo: false,

        // add to every request. currently supports headers and cookies
        /*
            A request coming in with headers like:
            headers: {
                referer:'https://www.indusface.com',
                test-header:666,
                auth-token: 'old-auth-token',
                cookie: 'a=b; test1=111;test2=000'
            }
            And having addToRequest in config set to
            {
                headers: {
                    authtoken: 'EBH-iHu*8'
                },
                cookie: {
                    test1: 99999,
                }
            }
            Would result in
            headers: { 
                referer: 'https://www.indusface.com',
                'test-header': 666,
                'auth-token': 'EBH-iHu*8',
                cookie: 'a=b; test1=99999; test2=000' 
            }        
        */
        addToRequest: {},
        reflectAddToRequestToCrawler: false, // Also send 'addToRequest' data to crawler  

        // additional requests to send at start of scan
        //eg.
        /*
        [
            {
                "method": "POST",
                "headers": {
                    "Accept-Encoding": "gzip, deflate",
                    "Accept-Language": "en-GB",
                    "Content-Type": "application/x-www-form-urlencoded",
                    "Cookie": "JSESSIONID=B1CD8C58BA2C8984D5B09C8BBCAA4AD4",
                    "Origin": "http://demo.testfire.net",
                    "Referer": "http://demo.testfire.net/login.jsp",
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Electron/2.0.8 Safari/537.36"
                },
                "body": "uid=admin&passw=admin&btnSubmit=Login",
                },
                "uri": "http://demo.testfire.net/doLogin"
            },
            ...
        ]        
        */
        additionalRequests: [],
        annotatedRequestsFromFile: "",
        //Is this api scan mode
        isApiScan: false,
        replayScanInfo: {},                 // used for replay scan to revalidate vuln via WAF for 
        attackAllVectors: false,             // If set to true, Attack all vectors even if vulnerabilities found
        apiRequestTimeoutInSec: 10, // Timeout for API requests when running in API scan mode via postman newman module
    },

    // Max number of pending responses as a ratio to HTTPRequestMonster.perDomainMaxParallelRequests
    // this is a per site setting since it can be overridden per site
    maxPendingResponseProcessingRatio: 1.5,

    // This section is used only when env variable HAIKU_SCANNER_TYPE is set
    // and the value of the environment var will be used to pick the appropriate key
    // that will be exposed as config.ScannerType.active
    ScannerType: {
        active: null, // will be set to one of the below ones and config.ScannerType.active.type will be set appropriately.

        // developer machines
        dev: {
            // filename patterns to load
            onlyLoadPluginsMatchingPattern: true,
            loadPluginMatchingPattern: /^original|^protocol|^output|cgi-generic/i,

            sendRpcReply: true,
            nackRpcMessages: false,
            logConsoleSilent: false,

            skipRefreshRequestBeforeAttack: true
        },
        // test scanner
        test: {
            // filename patterns to load
            onlyLoadPluginsMatchingPattern: false,

            sendRpcReply: false,
            nackRpcMessages: true,
            logConsoleSilent: true,

            skipRefreshRequestBeforeAttack: true
        },
        // production scanner
        prod: {
            // filename patterns to load
            onlyLoadPluginsMatchingPattern: false,

            sendRpcReply: true,
            nackRpcMessages: false,
            logConsoleSilent: true,

            skipRefreshRequestBeforeAttack: false
        }
    },

    // **** Extensions ****
    Extensions: {
        python: {
            maxTimeForScriptSecs: 5 * 60,
            pyshellOptions: {
                pythonPath: '/bin/python3.6',
                //pythonPath: '/usr/local/bin/python3',
                //scriptPath: '<whatever path>'
            }
        }
    },

    // **** Plugins ****
    DefaultPluginSettings: {
        /**
         * how long to wait for all plugins to process the attack response.
         */
        maxTimeToProcessAttackResponse: 5 * 60 * 1000,

        /**
         * Number of bytes of context to add for regexp matching vulnerability checkers
         */
        contextBytes: 200,
        maxContextChars: 700,

        /** 
         * Report only up to this max. Active plugins are unlimited (-1)
         * Later, count them all but report only up to this max. Last will include the remaining count
         */
        maxInstancesOfEachVulnToReport: 50, // -1 => unlimited

        /**
         * Global setting for which type of browser/crawler resources to process. Plugins can add more if they want
         * Note that even the paths and subpaths that were generated by a resource type inherit it's resource type
         * eg. starting with /styles/bootstrap/bootstrap.css, even /styles/bootstrap and /styles are tagged as 'resource'
         * 
         * This setting is respected while attacking and while checking response so even passive plugins get to decide which 
         * original resource type they want to check
         * 
         * available types are: core (mainframe etc.), resource (js, stylesheet), other
         */
        resourcesToAttack: ['core'],

        /**
         * Plugin setting. Don't use default/built-in vectors, only use site specific vectors
         */
        skipDefaultVectors: false,

        /**
         * Plugin setting, is plugin allowed to attack
         */
        canAttack: true,

        /**
         * Plugin setting, can plugin check teh response to attacks
         */
        processAttackResponse: true,

        /**
         * vulnerability setting, can this vulnerability be reported
         */
        reportVulnerability: true,

        /**
         * Plugin setting, is plugin allowed to attack in api scan mode
         */
        canAttackInApiScan: false,

        /**
        * Plugin setting, can plugin check the attack response in api scan mode
        */
        processAttackResponseInApiScan: false,

        /**
         * Upload attack request & response to S3. i.e. OOB attacks
         */
        uploadAttackToS3: false,

        /**
         * The canAttackInOOB property is a boolean value that is used to determine whether an OOB plugin 
         * is enabled to attack a request with OOB vectors. By default, the value of this property is false, 
         * which means that the plugin is not allowed to perform OOB attacks. However, when the value is set to true, 
         * the plugin can launch OOB attacks against the request.
         */
        canAttackInOOB: false
    },

    // **** Plugins ****
    Plugins: {
        // the one and only processor of the original scrawler requst that will kick everything else off.
        'Original Crawler Request': {
            // ** 
            // Do NOT set up allowedContentTypes here. Will be set to superet of allowedContentTypes of all plugins
            // and global (HTTPRequestMonster) allowedContentTypes programatically.
            // ditto for resourcesToAttack
            // **
            // Allows passive checks to be performed on the original crawler request
            file: 'original-crawler-request.js',
            load: true, // ** MAKING THIS FALSE WILL TURN OFF THE ENTIRE SCANNING **
            bypassRevalidationCheck: true, // this request has to be sent always, can't skip request based on the revalidation attack being performed.
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-original-crawler-request': {
                    productionReady: true,
                    igwId: '-1', // dummy
                    maxInstancesOfEachVulnToReport: -1 // report all that we find
                }
            }
        },

        // protocol/parsing plugins

        'Protocol: Form Encoded Post': {
            file: 'protocol/form-encoded-post-splitter.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                addExtraParam: true,
                attackParamName: true,
                encodings: ['uri', 'raw'],
                headers: {}
            }
        },
        'Protocol: JSON Post': {
            file: 'protocol/json-body.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                encodings: ['raw'],
            }
        },
        'Protocol: XML Post': {
            file: 'protocol/xml-post-splitter.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                nonleafNodestoAttack: 0,
                leafNodesToAttack: 0,
                maxnodesToAttack: 0,
                prependAttackVector: false,
                encodings: ['raw'],
                headers: {}
            }
        },
        'Protocol: URI Query': {
            file: 'protocol/uri-query-splitter.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                addExtraParam: true,
                attackParamName: true,
                encodings: ['uri', 'raw'],
                headers: {}
            }
        },
        'Protocol: HTTP Headers': {
            file: 'protocol/http-headers-splitter',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                headersToIterate: ['Referer', 'User-Agent', 'Content-Type', 'Host', 'Cookie', 'Origin'],
                encodings: ['raw'],
                headers: {}
            }
        },
        'Protocol: HTTP Methods': {
            file: 'protocol/http-method',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                encodings: ['raw'],
                headers: {}
            }
        },
        'Protocol: URI Permutation': {
            file: 'protocol/uri-permutation',
            load: false,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                addExtraParam: true,
                attackParamName: true,
                encodings: ['uri', 'raw'],
                headers: {}
            }
        },
        /*
         * Given an URI eg. http://www.xyz.com/a/b/c/d?kk=hh, this will iterate (with default options):
         * END: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/c/d/[vectors]?kk=hh
         * ROOT: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/[vectors]?kk=hh
         * sub paths based on depth: 
         *  depth 1: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/[vectors]?kk=hh
         *  depth 2: http://www.xyz.com/a/b/c/d/ => attacks will be http://www.xyz.com/a/b/[vectors]?kk=hh
         */
        'Protocol: URI Path Splitter': {
            file: 'protocol/uri-path-splitter',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                alwaysIterateEnd: true,
                skipRoot: false,
                maxPathComponents: 4,
                clearQueryParams: false, // /a/b/c?x=1 => assume url was /a/b/c then everything like above
                collapsePathSeps: true, // /a/b/c//<attack> => /a/b/c/<attack>
                addSlashBeforeAttack: true, // true = /a/b/<attack>, false = /a/b<attack> eg. /a/b.bak

                // 'original' => keep same as original request : /a/b => /a/b/<attack>, /a/b/ => /a/b/<attack>/
                // 'never' => delete any trailing '/' : /a/b => /a/b/<attack>, /a/b/ => /a/b/<attack>
                // 'always' => always have '/' : /a/b => /a/b/<attack>/, /a/b/ => /a/b/<attack>/
                haveSlashAfterAttack: 'original',
                ignoreExtension: [],
                encodings: ['uri', 'raw'],
                headers: {}
            }
        },
        /**
         * given an post boday it will attack on post body
         */
        'Protocol: Post Body': {
            file: 'protocol/post-body.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                addExtraParam: true,
                attackParamName: true,
                encodings: ['raw'],
                headers: {}
            }
        },

        'Protocol: Request Cookie': {
            file: 'protocol/cookie-splitter',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                replaceValue: true,
                appendVector: true,
                encodings: ['raw']
            }
        },

        'Protocol: Login Request': {
            file: 'protocol/login-request',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                encodings: ['uri', 'raw']
            }
        },

        'Protocol: File Upload Post': {
            file: 'protocol/file-upload-post-splitter.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            options: {
                encodings: ['raw'],
                headers: {}
            }
        },

        // attack/vunerability check plugins
        'SQL Injection': {
            file: 'sql-inj-attack.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-sql-injection': {
                    productionReady: true,
                    igwId: '493',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-db-error-disclosure': {
                    productionReady: true,
                    igwId: '203364',
                    // for this one restrict to the global max limit - don't want to show all that we find.
                    canAttackInReplayScan: true
                },
            },
        },
        'ORM Injection': {
            file: 'orm-inj-attack.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml|x-www-form-urlencoded|javascript)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-orm-injection': {
                    productionReady: true,
                    igwId: '393294',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                }
            },
        },
        'PHP Object Injection': {
            file: 'php-object-injection.js',
            load: true,
            allowedContentTypes: /multipart\/form-data|text\/(html|xml|plain)|application\/(json|xml|x-www-form-urlencoded)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-php-object-injection': {
                    productionReady: true,
                    igwId: '393295',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                }
            },
        },
        /*        
        Created for testing, Disabled.
        'SQL_Injection_NS': {
                   file: 'sql-inj-ns.js',
                   load: false,
                   allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
                   canAttackInApiScan: true,
                   processAttackResponseInApiScan: true,
                   vulnerabilities: {
                       'ID-sql-injection_Part2_AtVectors': {
                           productionReady: false,
                           igwId: '292401',
                           maxInstancesOfEachVulnToReport: -1, // report all that we find,
                           canAttackInReplayScan: true
                       }
                   },
               }, */
        'Apache Struts': {
            file: 'apache-struts.js',
            load: true,
            vulnerabilities: {
                'ID-apache-struts': {
                    productionReady: true,
                    igwId: '203361',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Oracle Weblogic Deserialization': {
            file: 'oracle-weblogic-deserialization.js',
            load: true,
            vulnerabilities: {
                'ID-oracle-weblogic-deserialization': {
                    productionReady: true,
                    igwId: '203706',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Oracle Weblogic Deserialization Bypass': {
            file: 'oracle-weblogic-deserialization-bypass.js',
            load: true,
            vulnerabilities: {
                'ID-oracle-weblogic-deserialization-bypass': {
                    productionReady: true,
                    igwId: '203720',
                    maxInstancesOfEachVulnToReport: 5, // report max 5 instances
                    canAttackInReplayScan: true
                },
            },
        },
        'DotNet Deserialization Found': {
            file: 'dotnet-deserialization-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-dotNet-deserialization-found': {
                    productionReady: true,
                    igwId: '203707',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Rails Deserialization Found': {
            file: 'rails-deserialization-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            vulnerabilities: {
                'ID-rails-deserialization-found': {
                    productionReady: true,
                    igwId: '203719',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        },
        'External Entity Injection': {
            file: 'external-entity-injection.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: { //below both are for post-body and form-encoded-post iterators
                'ID-external-entity-post-body-vuln': {
                    productionReady: true,
                    igwId: '292353',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
                'ID-external-entity-dos-post-body-vuln': {
                    productionReady: true,
                    igwId: '292352',
                    maxInstancesOfEachVulnToReport: 2, // report all that we find
                    canAttackInReplayScan: true
                }
            },
        },
        /* 
        Disabled, Mode: obsolete
        'External Entity Injection DOS': {
            file: 'external-entity-injection-dos.js',
            load: false,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            vulnerabilities: { //below both are for the xml-post iterator
                'ID-external-entity-dos-vuln': {
                    productionReady: false,
                    igwId: '292338',
                    maxInstancesOfEachVulnToReport: 2, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-external-entity-vuln': {
                    productionReady: false,
                    igwId: '203725',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        }, */
        'Apache Tomcat RCE': {
            file: 'apache-tomcat-rce.js',
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            load: true,
            vulnerabilities: {
                'ID-apache-tomcat-rce-found': {
                    productionReady: true,
                    igwId: '203703',
                    maxInstancesOfEachVulnToReport: 3, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Log Injection': {
            file: 'log-injection-attack.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain|json)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-log-injection': {
                    productionReady: false,
                    igwId: '252266',
                    maxInstancesOfEachVulnToReport: 5 // report all that we find
                },
            },
        },
        'Perl Deserialization': {
            file: 'perl-deserialization.js',
            load: true,
            vulnerabilities: {
                'ID-perl-deserialization': {
                    productionReady: true,
                    igwId: '203709',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Apache httpd DOS': {
            file: 'apache-httpd-dos.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json|multipart/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-apache-httpd-dos': {
                    productionReady: true,
                    igwId: '252276',
                    maxInstancesOfEachVulnToReport: 5, // report 5 instances
                    canAttackInReplayScan: true
                },
            },
        },
        'vBulletin-RCE': {
            file: 'vbulletin-RCE.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-vbulletin-RCE': {
                    productionReady: true,
                    igwId: '252277',
                    maxInstancesOfEachVulnToReport: 1, // report 1 instance only
                    canAttackInReplayScan: true
                },
            },
        },
        'Server Side Javascript Injection': {
            file: 'server-side-javascript-inj.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-ssjs': {
                    productionReady: true,
                    igwId: '252286',
                    maxInstancesOfEachVulnToReport: 10, // report 1 instance only
                    canAttackInReplayScan: true
                },
            },
        },
        'Server Side Template Injection': {
            file: 'server-side-template-inj.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-serverside-template-injection': {
                    productionReady: true,
                    igwId: '252290',
                    maxInstancesOfEachVulnToReport: 1, // report 1 instance only
                    canAttackInReplayScan: true
                },
            },
        },
        // Disabled, Mode: obsolete, moved to OOB method
        //Created again on 29th May 2024.
        'Blind SQL Injection': {
            file: 'blind-sql-tf.js',
            load: true,
            allowedContentTypes: /text\/.|application\/./i,
            vulnerabilities: {
                'ID-blind-sql-true-false': {
                    productionReady: false,
                    igwId: '252',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true,
                    detectionTimeout: 180,
                },
            },
        },
        'Slow Response Time': {
            file: 'slow-responsetime-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-slow-response-time': {
                    productionReady: true,
                    igwId: '203671',
                    maxSlowResponseTime: 1500,
                    maxInstancesOfEachVulnToReport: 1 // report all that we find
                },
            },
        },
        'Local File Inclusion (LFI)': {
            file: 'lfi-attack.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-local-file-inclusion': {
                    productionReady: true,
                    igwId: '24866',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Remote File Inclusion (RFI)': {
            file: 'rfi-attack.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-remote-file-inclusion': {
                    productionReady: true,
                    igwId: '9283',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find,
                    canAttackInReplayScan: true
                },
                'ID-unvalidated-redirection': {
                    productionReady: true,
                    igwId: '9295',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            }
        },
        'Remote XSL Inclusion (RXSLI)': {
            file: 'remote-xsl-inclusion.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-remote-xsl-inclusion': {
                    productionReady: true,
                    igwId: '252291',
                    maxInstancesOfEachVulnToReport: 1, // report only 1
                    canAttackInReplayScan: true
                }
            }
        },
        'Cross Site Scripting (XSS)': {
            file: 'xss-attacks.js',
            load: true,
            haikuMsgCheckNumber: 2049862,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-xss-injection': {
                    productionReady: true,
                    igwId: '377',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find,
                    canAttackInReplayScan: true
                },
                'ID-html-injection': {
                    productionReady: true,
                    igwId: '161092',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
                //Below plugin only for if Content-type is text/plain or application/json - Severity is medium - June 28 2022
                /* 'ID-possible-xss-injection': {
                    productionReady: false,
                    igwId: '332845',
                    maxInstancesOfEachVulnToReport: 1, // report all that we find,
                    canAttackInReplayScan: true
                },
                'ID-possible-html-injection': {
                    productionReady: false,
                    igwId: '332846',
                    maxInstancesOfEachVulnToReport: 1, // report all that we find
                    canAttackInReplayScan: true
                }, */
            },
        },
        /* 
        Disabled: Created for testing purpose.
        'NS Cross Site Scripting (XSS)': {
            file: 'xss-attacks-ns.js',
            load: false,
            haikuMsgCheckNumber: 2049862,
            vulnerabilities: {
                'ID-xss-ns-injection': {
                    productionReady: false,
                    igwId: '292403',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-html-ns-injection': {
                    productionReady: false,
                    igwId: '292404',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        }, */
        'Host Header Injection': {
            file: 'host-header-inj.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-host-header-injection': {
                    productionReady: true,
                    igwId: '203264',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-proxy-bypass': {
                    productionReady: true,
                    igwId: '400196',
                    maxInstancesOfEachVulnToReport: 1, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-web-cache-poisoning': {
                    productionReady: true,
                    igwId: '292357',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-ssrf': {
                    productionReady: false,
                    igwId: '252308',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-hhi-critical': {
                    productionReady: true,
                    igwId: '405116',
                    maxInstancesOfEachVulnToReport: 1, // report only 1
                    canAttackInReplayScan: true
                },
            },
        },

        'XPATH Injection': {
            file: 'xpath-inj-attack.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-xpath-injection': {
                    productionReady: true,
                    igwId: '9290',
                    maxInstancesOfEachVulnToReport: 10, // changed limit to 10 from -1
                    canAttackInReplayScan: true
                }
            },
        },
        'OS Command Injection': {
            file: 'os-cmd-inj-attack.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-os-cmd-injection': {
                    productionReady: true,
                    igwId: '9280',
                    maxInstancesOfEachVulnToReport: 10, // changed limit to 10 from -1
                    canAttackInReplayScan: true
                },
            },
        },
        'PHP Nginx Command Injection': {
            file: 'php-nginx-rce.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-php-nginx-cmd-injection': {
                    productionReady: true,
                    igwId: '252293',
                    maxInstancesOfEachVulnToReport: 1, // Only to report 1 instance
                    canAttackInReplayScan: true
                },
            },
        },
        'ASP.NET Debug Feature Enabled': {
            file: 'asp-net-debug-feature-enabled.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-asp-debug-feature-enabled': {
                    productionReady: true,
                    igwId: '1017',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                },
            },
        },
        'PROPFIND method enabled': {
            file: 'http-method-propfind-plugin.js',
            load: true,
            vulnerabilities: {
                'ID-propfind-enabled': {
                    productionReady: true,
                    igwId: '193',
                    maxInstancesOfEachVulnToReport: 5, //Updated to 5 from default
                    canAttackInReplayScan: true
                },
            },
        },
        'PUT DELETE method enabled': {
            file: 'http-method-put-delete-plugin.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-put-method-enabled': {
                    productionReady: true,
                    igwId: '1025',
                    maxInstancesOfEachVulnToReport: 1
                },
                'ID-delete-method-enabled': {
                    productionReady: true,
                    igwId: '156',
                    maxInstancesOfEachVulnToReport: 1
                },
            },
        },
        'TRACE TRACK method enabled': {
            file: 'http-method-trace-track-plugin.js',
            load: true,
            resourcesToAttack: ['core', 'resource'],
            allowedContentTypes: /text\/(html|xml)|message\/http/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-TRACE-method-enabled': {
                    productionReady: true,
                    igwId: '990',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                },
                'ID-TRACK-method-enabled': {
                    productionReady: true,
                    igwId: '252263',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                }
            },
        },
        'Cross-Site Tracing (XST)': {
            file: 'xst-attack.js',
            load: true,
            resourcesToAttack: ['core', 'resource'],
            allowedContentTypes: /text\/(html|xml)|message\/http|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-xst-attack': {
                    productionReady: true,
                    igwId: '382972',
                    maxInstancesOfEachVulnToReport: 2,
                    // canAttackInReplayScan: true
                }
            },
        },
        'Cross-Site Tracing (XST) OOB': {
            file: 'xst-attack-oob.js',
            load: true,
            resourcesToAttack: ['core', 'resource'],
            allowedContentTypes: /text\/(html|xml)|message\/http|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-xst-attack': {
                    productionReady: true,
                    igwId: '382972',
                    maxInstancesOfEachVulnToReport: 2,
                    // canAttackInReplayScan: true
                }
            },
        },
        'ASP NET Trace method enabled': {
            file: 'asp-net-trace-method-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json|message\/http/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-ASPNET-TRACE-method-enabled': {
                    productionReady: true,
                    igwId: '203665',
                    maxInstancesOfEachVulnToReport: 5, //Updated to 5 from default
                    canAttackInReplayScan: true
                },
            },
        },
        'Cross Origin Resource Sharing': {
            file: 'cross-origin-resource-sharing.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-cross-origin-resource-sharing': {
                    productionReady: true,
                    igwId: '203267',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        },
        /* 
        Disabled, Mode: obsolete
        'Mixed Content Vulnerability': {
            file: 'mixed-content.js',
            load: false,
            vulnerabilities: {
                'ID-mixed-content-vulnerability': {
                    productionReady: true,
                    igwId: '161331',
                    maxInstancesOfEachVulnToReport: 1 // reporting only 1
                },
            },
        }, */
        'Mixed Content Vulnerability Plugin': {
            file: 'mixed-content-vulnerability.js',
            load: true,
            vulnerabilities: {
                'ID-active-mixed-content-vulnerability': {
                    productionReady: true,
                    igwId: '203711',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-passive-mixed-content-vulnerability': {
                    productionReady: true,
                    igwId: '203710',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-mixed-content-vulnerability': {
                    productionReady: true,
                    igwId: '161331',
                    maxInstancesOfEachVulnToReport: 5
                },
            },
        },
        'Http Response Splitting': {
            file: 'http-response-splitting.js',
            load: true,
            vulnerabilities: {
                'ID-http-response-splitting': {
                    productionReady: true,
                    igwId: '178',
                    maxInstancesOfEachVulnToReport: 10, //Updated to 10 from default
                    canAttackInReplayScan: true
                },
            },
        },
        'Http Response Header Injection': {
            file: 'http-response-header-injection.js',
            load: false,
            vulnerabilities: {
                'ID-http-response-header-injection': {
                    productionReady: false,
                    igwId: '203702',
                    maxInstancesOfEachVulnToReport: 10, //Updated to 10 from default
                    canAttackInReplayScan: true
                },
            },
        },
        'Serialized Object in HTTP Message': {
            file: 'serialized-object-http-message.js',
            load: true,
            vulnerabilities: {
                'ID-serialized-object-http-message': {
                    productionReady: true,
                    igwId: '391991',
                    maxInstancesOfEachVulnToReport: 10, //Updated to 10 from default
                    canAttackInReplayScan: true
                },
            },
        },
        'LDAP Injection': {
            file: 'ldap-injection.js',
            load: true,
            vulnerabilities: {
                'ID-ldap-injection': {
                    productionReady: true,
                    igwId: '392590',
                    maxInstancesOfEachVulnToReport: 10, //Updated to 10 from default
                    canAttackInReplayScan: true
                },
            },
        },
        'WebDAV Extensions Enabled': {
            file: 'webdav-extensions-enabled.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-WebDav-OPTIONS-enabled': {
                    productionReady: true,
                    igwId: '884',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        },
        'Options enabled': {
            file: 'http-method-options-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-OPTIONS-enabled': {
                    productionReady: true,
                    maxInstancesOfEachVulnToReport: 1,
                    igwId: '203577',
                    canAttackInReplayScan: true
                },
            },
        },
        'Webadmin.php file found': {
            file: 'webadmin-php-file.js',
            load: true,
            vulnerabilities: {
                'ID-webadmin-php-file': {
                    productionReady: true,
                    igwId: '203397',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Robot Txt File Found': {
            file: 'robot-txt-file-plugin.js',
            load: true,
            allowedContentTypes: /text\/plain/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-robot-file-found': {
                    productionReady: true,
                    igwId: '161037',
                    maxInstancesOfEachVulnToReport: 1, // report only 1
                    canAttackInReplayScan: true
                },

            },
        },
        'Sensitive Documentation Txt File Found': {
            file: 'sensitive-documentation-file.js',
            load: true,
            allowedContentTypes: /text\/plain/i,
            resourcesToAttack: ['core', 'resource'],
            vulnerabilities: {
                'ID-sensitive-document-file-found': {
                    productionReady: true,
                    igwId: '203670',
                    maxInstancesOfEachVulnToReport: 5, // report only 5
                    canAttackInReplayScan: true
                },
            },
        },
        'CVS Web Repository': {
            file: 'cvs-repository-detected.js',
            load: true,
            vulnerabilities: {
                'ID-cvs-repository-detected': {
                    productionReady: true,
                    igwId: '203396',
                    maxInstancesOfEachVulnToReport: 5, // report only 5
                    canAttackInReplayScan: true
                },
            },
        },
        'Struts2 Development mode': {
            file: 'struts2-dev-mode.js',
            load: true,
            vulnerabilities: {
                'ID-struts2-dev-mode': {
                    productionReady: true,
                    igwId: '203581',
                    maxInstancesOfEachVulnToReport: 5 // report only 5
                },

            },
        },
        'Manually Reported Vuln': {
            file: 'manually-reported-vuln.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-idor-attack': {
                    productionReady: true,
                    igwId: '292410',
                    maxInstancesOfEachVulnToReport: -1 // report all that we find
                },
                'ID-broken-token': {
                    productionReady: true,
                    igwId: '292409',
                    maxInstancesOfEachVulnToReport: -1 // report all that we find
                },
                'ID-authentication-bypass': {
                    productionReady: true,
                    igwId: '252297',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
        },
        // passive checks
        'Suspicious Comment Disclosure': {
            file: 'suspicious-comment-disclosure.js',
            load: true,
            resourcesToAttack: ['core', 'resource'],
            vulnerabilities: {
                'ID-suspicious-comment-disclosure': {
                    productionReady: true,
                    igwId: '203480',
                    maxInstancesOfEachVulnToReport: 5 // report only 5
                }
            },
        },
        'SQL Statement Check': {
            file: 'sql-statement-checker.js',
            load: true,
            vulnerabilities: {
                'ID-sql-statement-check': {
                    productionReady: true,
                    igwId: '24862',
                    maxInstancesOfEachVulnToReport: 10
                }
            },
        },
        'Email Address Found': {
            file: 'email-id-found.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-email-id-found': {
                    productionReady: true,
                    igwId: '24859',
                    maxInstancesOfEachVulnToReport: 10
                }
            },
        },
        'Possible Physical Path Disclosure': {
            file: 'possible-physical-path.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-possible-physical-path-disclosure': {
                    productionReady: true,
                    igwId: '9263',
                    maxInstancesOfEachVulnToReport: 10
                }
            },
        },
        'Apache Server-status Page': {
            file: 'apache-server-status-page.js',
            load: true,
            vulnerabilities: {
                'ID-apache-server-status-page': {
                    productionReady: true,
                    igwId: '203582',
                    maxInstancesOfEachVulnToReport: 5, //Updated to 5 from 10
                    canAttackInReplayScan: true
                }
            },
        },
        'Possible Backup files': {
            file: 'possible-backup-files.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            vulnerabilities: {
                'ID-possible-backup-file': {
                    productionReady: true,
                    igwId: '24864',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                }
            },
        },
        'XML RPC Found': {
            file: 'xml-rpc-found.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            vulnerabilities: {
                'ID-xml-rpc-detected': {
                    productionReady: true,
                    igwId: '203700',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
        },
        'PHP Deserialization Found': {
            file: 'php-deserialization.js',
            load: true,
            vulnerabilities: {
                'ID-php-deserialization': {
                    productionReady: true,
                    igwId: '203712',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
        },
        'Web Server Default Web Page Detected': {
            file: 'default-web-page.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|x-cross-domain-policy|plain)|application\/(xml|zip|rar|json)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-webserver-default-page-detected': {
                    productionReady: true,
                    igwId: '203576',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            }
        },
        'Possible Sensitive Directory or file': {
            file: 'possible-sensitive-file-directory.js',
            load: true,
            resourcesToAttack: ['core', 'resource'],
            allowedContentTypes: /text\/(html|xml|x-cross-domain-policy|plain)|application\/(xml|zip|rar|json)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-possible-sensitive-file-or-directory': {
                    productionReady: true,
                    igwId: '24865',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                },
                'ID-readable-htaccess': {
                    productionReady: true,
                    igwId: '24868',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
                'ID-permissive-cross-domain-policy': {
                    productionReady: true,
                    igwId: '24867',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                },
                'ID-client-access-policy-FD': {
                    productionReady: true,
                    igwId: '292398',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                }
            },
        },
        'Remote Administration Interface Found': {
            file: 'remote-admin-interface.js',
            load: true,
            resourcesToAttack: ['core'],
            allowedContentTypes: /text\/(html|xml|x-cross-domain-policy|plain)|application\/xml/i,
            vulnerabilities: {
                'ID-remote-admin-interface': {
                    productionReady: true,
                    igwId: '252271',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
        },
        'Predictable Resource Location': {
            file: 'predictable-resource-location.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-predictable-resource-location': {
                    productionReady: true,
                    igwId: '161095',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                }
            },
        },
        'Browsable Web Directory': {
            file: 'browsable-web-directory.js',
            load: true,
            processAttackResponseInApiScan: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            resourcesToAttack: ['core', 'resource'],
            vulnerabilities: {
                'ID-possible-sensitive-file-or-directory': {
                    productionReady: true,
                    igwId: '24865',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
                'ID-browsable-web-directory': {
                    productionReady: true,
                    igwId: '403',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                },
                'ID-archive-backup-file': {
                    productionReady: true,
                    igwId: '252258',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                },
                'ID-possible-backup-file': {
                    productionReady: true,
                    igwId: '24864',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                },
                'ID-core-dump-file': {
                    productionReady: true,
                    igwId: '252299',
                    maxInstancesOfEachVulnToReport: 20,
                    canAttackInReplayScan: true
                }
            },
        },
        /* 
        Disabled: Created for testing purpose.
        'Browsable Web Directory-dupe': {
            file: 'direc-duplicate.js',
            load: false,
            processAttackResponseInApiScan: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            vulnerabilities: {
                'ID-browsable-web-directory-dupe': {
                    productionReady: false,
                    igwId: '292403',
                    maxInstancesOfEachVulnToReport: -1,
                }, 'ID-archive-backup-file-dupe': {
                    productionReady: false,
                    igwId: '292401',
                    maxInstancesOfEachVulnToReport: -1,
                },
                'ID-possible-backup-file-dupe': {
                    productionReady: false,
                    igwId: '292386',
                    maxInstancesOfEachVulnToReport: -1,
                },
                'ID-core-dump-file-dupe': {
                    productionReady: false,
                    igwId: '292404',
                    maxInstancesOfEachVulnToReport: -1,
                }
            },
        }, */
        'Source Code Disclosure': {
            file: 'source-code-disclosure.js',
            load: true,
            vulnerabilities: {
                'ID-source-code-disclosure': {
                    productionReady: true,
                    igwId: '229',
                    maxInstancesOfEachVulnToReport: 10
                },
                'ID-weak-cryptography-detected': {
                    productionReady: true,
                    igwId: '252311',
                    maxInstancesOfEachVulnToReport: 5
                }
            },
        },
        'Authentication details found': {
            file: 'auth-dts-in-url.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-session-id-in-url': {
                    productionReady: true,
                    igwId: '161333',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-improper-token-handling': {
                    productionReady: true,
                    igwId: '347790',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-sensitive-information-url': {
                    productionReady: true,
                    igwId: '347791',
                    maxInstancesOfEachVulnToReport: 5
                }
            },
        },
        'Internal IP Leak Found': {
            file: 'internal-ip-leak-found.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-internal-ip-leak-found': {
                    productionReady: true,
                    igwId: '24863',
                    maxInstancesOfEachVulnToReport: 10
                },
            },
        },
        'Clickjacking - HTTP Header Check': {
            file: 'clickjacking-header-check.js',
            load: true,
            allowedContentTypes: /text\/html|application\/xhtml\+xml|image\/svg\+xml|text\/markdown|application\/pdf|text\/xml|application\/xml/i,
            // processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-clickjacking-header-check': {
                    productionReady: true,
                    igwId: '161332',
                    maxInstancesOfEachVulnToReport: 5,
                    processAttackResponseInApiScan: true,
                },
            },
        },
        'Cookie Checker': {
            file: 'cookie-checker.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(xml|json)/i,
            processAttackResponseInApiScan: true, // Applicable to API. API2:2023 - Broken Authentication	CWE-384	 Session Fixation
            vulnerabilities: {
                'ID-cookie-httponly-not-set': {
                    productionReady: true,
                    igwId: '9294',
                    //startAttackAfterThisManyRequest: 60,
                    maxInstancesOfEachVulnToReport: 5,
                    // canAttackInReplayScan: true
                },
                'ID-cookie-secure-not-set': {
                    productionReady: true,
                    igwId: '9265',
                    maxInstancesOfEachVulnToReport: 5,
                },
                'ID-session-cookie-scoped-parent-domain': {
                    productionReady: true,
                    igwId: '203699',
                    maxInstancesOfEachVulnToReport: 5,
                },
                'ID-broad-cookie-path': {
                    productionReady: true,
                    igwId: '252259',
                    maxInstancesOfEachVulnToReport: 5,
                },
                'ID-samesite-not-implemented': {
                    productionReady: true,
                    igwId: '392591',
                    maxInstancesOfEachVulnToReport: 5,
                }
            },
        },
        'HTTP Header Info Disclosure': {
            file: 'http-headers-checker.js',
            load: true,
            allowedContentTypes: /text\/.|application\/./i,
            vulnerabilities: {
                'ID-Http-basic Authentication': {
                    productionReady: true,
                    igwId: '9271',
                    maxInstancesOfEachVulnToReport: 1,
                    processAttackResponseInApiScan: true,

                },
                'ID-xss-header-protection-low': {
                    productionReady: true,
                    igwId: '203479',
                    maxInstancesOfEachVulnToReport: 1
                },
                'ID-xss-header-protection-medium': {
                    productionReady: true,
                    igwId: '390379',
                    maxInstancesOfEachVulnToReport: 1
                },
                'ID-mime-sniffing': {//Medium -Web Server Content Sniffing Enabled
                    productionReady: true,
                    igwId: '252272',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-server-content-sniffing-low': { //Low - Web Server Content Sniffing Enabled
                    allowedContentTypes: /text\/(?:css|html)|application\/javascript|image\/(?:jpe?g|png)/i,
                    productionReady: true,
                    igwId: '390365',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-chrome-log-info-disclosure': {
                    productionReady: true,
                    igwId: '292332',
                    maxInstancesOfEachVulnToReport: 1
                },
                'ID-ghost-vuln-detected': {
                    productionReady: true,
                    igwId: '292382',
                    maxInstancesOfEachVulnToReport: 1,
                    processAttackResponseInApiScan: true,
                },
                'ID-JWT-misconfiguration': {
                    productionReady: true,
                    igwId: '292390',
                    maxInstancesOfEachVulnToReport: 1,
                    processAttackResponseInApiScan: true,
                },
                'ID-JWT-Incorrect-Session-Timeout': {
                    productionReady: true,
                    igwId: '292389',
                    MaxDays: 2,
                    maxInstancesOfEachVulnToReport: 1,
                    processAttackResponseInApiScan: true,
                },
                'ID-JWT-none-algorithm': {
                    productionReady: true,
                    igwId: '350131',
                    maxInstancesOfEachVulnToReport: 1,
                    processAttackResponseInApiScan: true,
                },
                'ID-credential-in-token': {
                    productionReady: true,
                    igwId: '332847',
                    maxInstancesOfEachVulnToReport: 1,
                    processAttackResponseInApiScan: true,
                },
                'ID-csp-headers-not-set': {
                    productionReady: true,
                    igwId: '392594',
                    maxInstancesOfEachVulnToReport: 5,
                    processAttackResponseInApiScan: true,
                },
                'ID-X-Permitted-CDP': {
                    productionReady: true,
                    igwId: '350123',
                    maxInstancesOfEachVulnToReport: 1
                }
            }
        },
        'Insecure/Unset HSTS Header': {
            file: 'hsts-checker.js',
            load: true,
            allowedContentTypes: /text\/.|application\/./i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-hsts-header-missing': {
                    productionReady: true,
                    igwId: '203698',
                    maxInstancesOfEachVulnToReport: 1
                },
            }
        },
        'Web Server details found': {
            file: 'server-dts-found.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-asp-net-version-headers': {
                    productionReady: true,
                    igwId: '203666',
                    maxInstancesOfEachVulnToReport: 2
                },
                'ID-Microsoft-Server-Version-Disclosure': {
                    productionReady: true,
                    igwId: '203672',
                    maxInstancesOfEachVulnToReport: 2
                },
                'ID-Web-Server-Version-Disclosure': {
                    productionReady: true,
                    igwId: '161036',
                    maxInstancesOfEachVulnToReport: 2
                },
                'ID-info-disclosure-http-headers': {
                    productionReady: true,
                    igwId: '161040',
                    maxInstancesOfEachVulnToReport: 2
                },
            }
        },
        'Sensitive Data without SSL': {
            file: 'sensitive-data-without-ssl.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-sensitive-data-submitted-without-ssl': {
                    productionReady: true,
                    igwId: '1011',
                    maxInstancesOfEachVulnToReport: 10
                },
            },
        },
        'Insecure HTTPs Transition': {
            file: 'insecure-transition.js',
            load: true,
            allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-insecure-transition-from-https': {
                    productionReady: true,
                    igwId: '381132',
                    maxInstancesOfEachVulnToReport: 5,
                },
                'ID-insecure-transition-from-http': {
                    productionReady: true,
                    igwId: '381131',
                    maxInstancesOfEachVulnToReport: 5,
                },
            },
        },
        'Insecure HTTP Transport': {
            file: 'insecure-http-transport.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            vulnerabilities: {
                'ID-insecure-http-transport': {
                    productionReady: true,
                    igwId: '381133',
                    maxInstancesOfEachVulnToReport: 1,
                }
            },
        },
        'Insecure HTTP Method Transition': {
            file: 'insecure-http-method.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            vulnerabilities: {
                'ID-insecure-transition-from-post-get': {
                    productionReady: true,
                    igwId: '383593',
                    maxInstancesOfEachVulnToReport: 5,
                },
                /* 'ID-insecure-transition-from-get-post': {
                    productionReady: false,
                    igwId: '38113000',
                    maxInstancesOfEachVulnToReport: -1,
                } */
            },
        },
        'Trojan Shell Script Detected': {
            file: 'trojan-shell-script-detected.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: false,
            vulnerabilities: {
                'ID-trojan-shell-script-detected': {
                    productionReady: true,
                    igwId: '381130',
                    maxInstancesOfEachVulnToReport: -1 //all instances
                },
            },
        },
        'Database Connection String Detected': {
            file: 'database-connection-string-detected.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: false,
            vulnerabilities: {
                'ID-database-connection-string-detected': {
                    productionReady: true,
                    igwId: '388452',
                    maxInstancesOfEachVulnToReport: -1 //all instances
                },
            },
        },
        'Sensitive-information-cached': {
            file: 'sensitive-information-cached.js',
            load: true,
            vulnerabilities: {
                'ID-sensitive-information-cached': {
                    productionReady: true,
                    igwId: '203667',
                    maxInstancesOfEachVulnToReport: 2
                },
                'ID-cache-expired': {
                    productionReady: true,
                    igwId: '252306',
                    maxInstancesOfEachVulnToReport: 2,
                    maxAgeLimitToVeriy: 86400
                },
            },
        },
        'Credit Card Number Found': {
            file: 'credit-card-number-found.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-credit-card-number-found': {
                    productionReady: true,
                    igwId: '203704',
                    maxInstancesOfEachVulnToReport: 2
                }
            },
        },
        'Pwd-Submitted-Using-Http-GET': {
            file: 'password-vulnerability.js',
            load: true,
            vulnerabilities: {
                'ID-pwd-submitted-using-http-GET': {
                    productionReady: true,
                    igwId: '24860',
                    maxInstancesOfEachVulnToReport: 10
                },
            },
        },
        'Hidden-Form-Input-Field': {
            file: 'hidden-form-field-vulnerability.js',
            load: true,
            vulnerabilities: {
                'ID-hidden-form-input-field': {
                    productionReady: true,
                    igwId: '203668',
                    maxInstancesOfEachVulnToReport: 10
                },
            },
        },
        'JSF ViewState Found': {
            file: 'jsf-viewstate-found.js',
            load: true,
            vulnerabilities: {
                'ID-jsf-viewstate-found': {
                    productionReady: true,
                    igwId: '252300',
                    maxInstancesOfEachVulnToReport: 5
                },
            },
        },
        'User-Controllable-Tag-Parameter': {
            file: 'user-controllable-tag-parameter.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-user-controllable-tag-parameter': {
                    productionReady: true,
                    igwId: '203481',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
                'ID-partial-user-controllable-script': {
                    productionReady: true,
                    igwId: '292388',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
                'ID-cross-site-flashing': {
                    productionReady: true,
                    igwId: '292405',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'User-Controllable-HTML-Attribute': {
            file: 'user-cont-html-attr.js',
            load: true,
            allowedContentTypes: /text\/html/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-user-controllable-tag-parameter': {
                    productionReady: false,
                    igwId: '203481',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
                'ID-partial-user-controllable-script': {
                    productionReady: false,
                    igwId: '292388',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
                'ID-cross-site-flashing': {
                    productionReady: false,
                    igwId: '292405',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'Hidden-Form-Susceptible-To-Spam': {
            file: 'html-form-susceptible-to-spam.js',
            load: true,
            vulnerabilities: {
                'ID-hidden-field-susceptible-to-spam': {
                    productionReady: true,
                    igwId: '203669',
                    maxInstancesOfEachVulnToReport: 5
                },
            },
        },
        'HTML Form Without CSRF protection': {
            file: 'html-form-without-csrf-protection.js',
            load: true,
            vulnerabilities: {
                'ID-html-form-without-csrf-protection': {
                    productionReady: true,
                    igwId: '252268',
                    maxInstancesOfEachVulnToReport: -1
                },
            },
        },
        'CSRF vuln': {
            file: 'csrf.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            vulnerabilities: {
                'ID-csrf-vuln': {
                    productionReady: true,
                    igwId: '385237',
                    maxInstancesOfEachVulnToReport: 10
                },
            },
        },
        'Application-Error-Message': {
            file: 'application-error-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-application-error-msg-info-disclosure': {
                    productionReady: true,
                    igwId: '24780',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                },
            }
        },
        'Autocomplete Enabled for Sensitive HTML Fields': {
            file: 'autocomplete-html-form.js',
            load: true,
            vulnerabilities: {
                'ID-autocomplete-html-form': {
                    productionReady: true,
                    igwId: '9269',
                    maxInstancesOfEachVulnToReport: 10
                }
            },
        },
        'Unencoded character check': {
            file: 'unencoded-character-plugin.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-unencoded-characters': {
                    productionReady: true,
                    igwId: '203265',
                    maxInstancesOfEachVulnToReport: 10,
                    canAttackInReplayScan: true
                },
            },
        },
        'HTML form found in redirect page': {
            file: 'html-form-in-redirect-page.js',
            load: true,
            vulnerabilities: {
                'ID-html-form-found-in-redirect': {
                    productionReady: true,
                    igwId: '203675',
                    maxInstancesOfEachVulnToReport: 10
                },
            },
        },
        'Cookie Manipulation': {
            file: 'cookie-manipulation.js',
            load: true,
            attackOnlyParameterMatchingRegex: /phpsessid|user_token|JSESSIONID|ASP\.NET_SessionId|OutlookSession|ASPSESSIONID|Sessionid|log_session_id|svSession|ASP\.NET_SessionIPartner|HRA_Session|laravel_session|_jsuid|sess_map/i,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-cookie-manipulation': {
                    productionReady: true,
                    igwId: '252260',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        },
        'Weak Session ID': {
            file: 'weak-session-cookie.js',
            load: true,
            forceAllowParameterRegex: /phpsessid|user_token|JSESSIONID|ASP\.NET_SessionId|OutlookSession|ASPSESSIONID|Sessionid|log_session_id|svSession|ASP\.NET_SessionIPartner|HRA_Session|laravel_session|_jsuid|sess_map/i,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-cookie-weak-sessionid': {
                    productionReady: true,
                    igwId: '252262',
                    maxInstancesOfEachVulnToReport: 5
                },
            },
        },
        'Certificate-Error': {
            file: 'per-scan/certificate-issues.js',
            load: true,
            vulnerabilities: {
                'ID-certificate-error': {
                    productionReady: false,
                    igwId: '20769'
                },
                'ID-certificate-will-expire-soon': {
                    productionReady: true,
                    igwId: '24869',
                    reportCertWillExpireInDays: 15,
                },
                'ID-cert-common-name-invalid': {
                    productionReady: true,
                    igwId: '203578',
                },
                'ID-cert-has-expired': {
                    productionReady: true,
                    igwId: '589',
                },
                'ID-untrusted-server-cert': {
                    productionReady: true,
                    igwId: '20770',
                },
                'ID-cert-invalid': {
                    productionReady: true,
                    igwId: '20769',
                },
                'ID-cert-weak-signature-algo': {
                    productionReady: true,
                    igwId: '203579',
                },
                'ID-cert-weak-public-key': {
                    productionReady: true,
                    igwId: '203580',
                },
            }
        },
        'Insecure-Flash-Embed-Param-Found': {
            file: 'insecure-flash-embed-param.js',
            load: true,
            vulnerabilities: {
                'ID-insecure-flash-embed-param': {
                    productionReady: true,
                    igwId: '203483',
                    maxInstancesOfEachVulnToReport: 10
                }
            }
        },
        'Application Detection': {
            file: 'application-detection.js',
            load: false, //Disabled, no longer needed.
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-Application-Detection': {
                    productionReady: false,
                    igwId: '383592',
                    maxInstancesOfEachVulnToReport: 1
                }
            }
        },
        'Python Code Injection': {
            file: 'python-code-injection.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-Python-Code-Injection': {
                    productionReady: true,
                    igwId: '390380',
                    maxInstancesOfEachVulnToReport: 1
                }
            }
        },
        'Json Injection': {
            file: 'json-injection.js',
            load: true,
            allowedContentTypes: /application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-json-injection': {
                    productionReady: true,
                    igwId: '400129',
                    maxInstancesOfEachVulnToReport: 1
                }
            }
        },
        'SSI Injection': {
            file: 'ssi-injection.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-ssi-injection': {
                    productionReady: false,
                    igwId: '400128',
                    maxInstancesOfEachVulnToReport: 1
                }
            }
        },
        'Unencrypted Viewstate': {
            file: 'unencrypted_viewstate.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-unencrypted-viewstate': {
                    productionReady: true,
                    igwId: '9289'
                },
                'ID-viewstate-mac-disabled': {
                    productionReady: true,
                    igwId: '161039',
                    maxInstancesOfEachVulnToReport: 10
                },
            }
        },
        'Credential Guessing': {
            file: 'credential-guessing.js',
            load: true,
            maxContentVariancesAllowed: 0.1,
            maxstructureVariancesAllowed: 0.1,
            vulnerabilities: {
                'ID-username-password-guess': {
                    productionReady: true,
                    igwId: '252294',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        /*
        Disabled, Mode: obsolete
        'Common Credentials Found': {
            file: 'common-credentials-found.js',
            load: false,
            vulnerabilities: {
                'ID-common-credential': {
                    productionReady: false,
                    igwId: '252296',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        }, */
        'Authentication Bypass': {
            file: 'authentication-bypass.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            maxContentVariancesAllowed: 0.1,
            maxstructureVariancesAllowed: 0.1,
            vulnerabilities: {
                'ID-authentication-bypass': {
                    productionReady: true,
                    igwId: '252297',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        'Authentication Bypass API': {
            file: 'authentication-bypass-api.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            maxContentVariancesAllowed: 0.1,
            maxstructureVariancesAllowed: 0.1,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-authentication-bypass': {
                    productionReady: true,
                    igwId: '252297',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        'Username Enumeration': {
            file: 'username-enumeration.js',
            load: true,
            vulnerabilities: {
                'ID-username-enumeration': {
                    productionReady: true,
                    igwId: '252298',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        'Account Lockout': {
            file: 'account-lockout-attack.js',
            load: true,
            verifyAfterNumberOfLoginAttempts: 5,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-account-lockout': {
                    productionReady: true,
                    igwId: '203291',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
                'ID-no-captcha-on-login-page': {
                    productionReady: true,
                    igwId: '373895',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        'Account Lockout API': {
            file: 'account-lockout-attack-api.js',
            load: true,
            verifyAfterNumberOfLoginAttempts: 5,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-account-lockout': {
                    productionReady: true,
                    igwId: '203291',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        'WAF IPS Detected': {
            file: 'waf-ips-found.js',
            load: true,
            startVerificationAfterThisManyAttackRequest: 50,
            maxRequestToVerify: 15,
            maxInstancesOfEachVulnToReport: 1,
            vulnerabilities: {
                'ID-waf-ips-found': {
                    productionReady: true,
                    igwId: '252301',

                }
            }
        },
        'SSL Old Version Detected': {
            file: 'ssl-old-version.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-ssl-old-version': {
                    productionReady: true,
                    igwId: '203313',
                    maxInstancesOfEachVulnToReport: 1,
                }
            }
        },
        'Weak TLS CBC Detected': {
            file: 'weak-cbc-cipher.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-CBC-Ciphers-found': {
                    productionReady: true,
                    igwId: '292399',
                    maxInstancesOfEachVulnToReport: 1,
                }
            }
        },
        'SSL TLS Weak Ciphers Detected': {
            file: 'weak-cipher-suite.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-old-ciphers-found': {
                    productionReady: true,
                    igwId: '252310',
                    maxInstancesOfEachVulnToReport: 1,
                }
            }
        },
        'Apache Axis2 LFI': {
            file: 'apache-axis2-lfi.js',
            load: true,
            vulnerabilities: {
                'ID-apache-axis2-lfi': {
                    productionReady: true,
                    igwId: '252312',
                    maxInstancesOfEachVulnToReport: 1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Apache mod-cgi LFI': {
            file: 'apache-path-traversal.js',
            load: true,
            vulnerabilities: {
                'ID-apache-mod-cgi-lfi': {
                    productionReady: true,
                    igwId: '292402',
                    maxInstancesOfEachVulnToReport: 2,
                    canAttackInReplayScan: true
                },
            },
        },
        'SSRF': {
            file: 'ssrf.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-ssrf': {
                    productionReady: false,
                    igwId: '252308',
                    maxInstancesOfEachVulnToReport: 1, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-aws-ssrf': {
                    productionReady: true,
                    igwId: '203398',
                    maxInstancesOfEachVulnToReport: 1, // report only one instance
                    canAttackInReplayScan: true
                },
                'ID-lfi-ssrf': {
                    productionReady: true,
                    igwId: '203676',
                    maxInstancesOfEachVulnToReport: 1, // report only one instance
                    canAttackInReplayScan: true
                },
            },
        },
        'JSMOL2 SSRF LFI': {
            file: 'ssrf-jsmol2.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)/i,
            vulnerabilities: {
                'ID-ssrf-jsmol-lfi': {
                    productionReady: true,
                    igwId: '252313',
                    maxInstancesOfEachVulnToReport: 1 // report all that we find
                },
            },
        },
        'HTTP Verb Tampering': {
            file: 'http-verb-tampering.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-http-verb-tampering': {
                    productionReady: true,
                    igwId: '203307',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                }
            },
        },
        /* 
        Disabled, Mode: obsolete
        'Script Source Code Disclosure': {
            file: 'script-source-code-disclosure.js',
            load: false,
            resourcesToAttack: ['core', 'resource'],
            allowedContentTypes: /text\/(html|xml|plain)|application\/json|(img|image)\/(jpeg|png|bmp)/i,
            vulnerabilities: {
                'ID-script-source-code-disclosure': {
                    productionReady: false,
                    igwId: '252320',
                    maxInstancesOfEachVulnToReport: 5, // report all that we find
                    canAttackInReplayScan: true
                }
            },
        }, */
        'Long Password DOS': {
            file: 'long-password-dos.js',
            load: true,
            vulnerabilities: {
                'ID-long-password-dos': {
                    productionReady: true,
                    igwId: '252322',
                    maxResponseDeviation: 1000,
                    maxInstancesOfEachVulnToReport: 1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Format String Injection': {
            file: 'uncontrolled-format-string.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-uncontrolled-format-string': {
                    productionReady: true,
                    igwId: '252323',
                    maxInstancesOfEachVulnToReport: 3, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'JVM Version Disclosure': {
            file: 'jvm-version-checker.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-jvm-version-headers': {
                    productionReady: true,
                    igwId: '292333',
                    maxInstancesOfEachVulnToReport: 1
                }
            }
        },
        /*
        Disabled, Mode: obsolete
        'Social Security Number Found': {
            file: 'social-security-number-found.js',
            load: false,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-social-security-number-found': {
                    productionReady: false,
                    igwId: '292334',
                    maxInstancesOfEachVulnToReport: 2
                }
            },
        }, 
        'W3 Total Cache Debug Mode': {
            file: 'w3-total-cache-debug-mode.js',
            load: false,
            resourcesToAttack: ['core', 'resource'],
            vulnerabilities: {
                'ID-w3-total-cache-debug-mode': {
                    productionReady: false,
                    igwId: '292335',
                    maxInstancesOfEachVulnToReport: 2 // report only 5
                },

            },
        },*/
        'Subresource Integrity Missing': {
            file: 'subresource-integrity-check.js',
            load: true,
            vulnerabilities: {
                'ID-subresource-integrity-check': {
                    productionReady: true,
                    igwId: '292336',
                    maxInstancesOfEachVulnToReport: 1
                },
            },
        },
        'HTTP Request Smuggling': {
            file: 'http-request-smuggling.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-http-request-smuggling': {
                    productionReady: true,
                    igwId: '292339',
                    maxInstancesOfEachVulnToReport: 5
                },
            },
        },
        'Client-side desync (CSD)': {
            file: 'csd-attack-oob.js',
            load: false, //Hold for feature request
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-csd-attack': {
                    productionReady: false,
                    igwId: '390381',
                    maxInstancesOfEachVulnToReport: 1
                },
            },
        },
        'SlowLoris DDOS Attack': {
            file: 'slowloris-ddos-attack.js',
            load: true,
            vulnerabilities: {
                'ID-slowloris-ddos-attack': {
                    productionReady: true,
                    igwId: '292340',
                    detectionTimeout: 180,
                    maxInstancesOfEachVulnToReport: 1
                },
            },
        },
        'Iframe Injection': {
            file: 'iframe-injection.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-iframe-injection': {
                    productionReady: true,
                    igwId: '292351',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
                'ID-clickjacking-vulnerability': {
                    productionReady: true,
                    igwId: '393261',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Link Injection': {
            file: 'link-injection.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            vulnerabilities: {
                'ID-link-injection': {
                    productionReady: true,
                    igwId: '292350',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        /*
        Disabled, Mode: obsolete
        'CGI generic unseen parameter discovery': {
            file: 'cgi-generic-unseen-parameter.js',
            load: false,
            resourcesToAttack: ['core', 'resource', 'other'],
            vulnerabilities: {
                'ID-cgi-generic-unseen-parameter': {
                    productionReady: false,
                    igwId: '292354',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        }, */
        'Oracle Web logic Uri Attack': {
            file: 'oracle-weblogic-uri-attack.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain|javascript)|application\/(json|x-httpd-php|java-archive)/i,
            resourcesToAttack: ['core', 'resource'],
            vulnerabilities: {
                'ID-oracle-weblogic-auth-bypass': {
                    productionReady: true,
                    igwId: '292356',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Web cache poisoning attack': {
            file: 'web-cache-poisoning.js',
            load: true,
            vulnerabilities: {
                'ID-web-cache-poisoning': {
                    productionReady: true,
                    igwId: '292357',
                    maxInstancesOfEachVulnToReport: -1,
                    maxAgeLimitToVeriy: 0,
                    canAttackInReplayScan: true
                },
            },
        },
        /*
        Disabled, Mode: obsolete
        'X forwarded security bypass': {
            file: 'x-forwarded-security-bypass.js',
            load: false,
            vulnerabilities: {
                'ID-x-forwarded-security-bypass': {
                    productionReady: false,
                    igwId: '292359',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        }, */
        'Exchange Server SSRF attack': {
            file: 'ssrf-exchange-server.js',
            load: true,
            allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-ssrf-exchange-server-attack': {
                    productionReady: true,
                    igwId: '292376',
                    maxInstancesOfEachVulnToReport: 1
                },
            },
        },
        'Breach attack': {
            file: 'breach-attack.js',
            load: true,
            vulnerabilities: {
                'ID-breach-attack': {
                    productionReady: true,
                    igwId: '292377',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
                'ID-breach-attack-low': {
                    productionReady: true,
                    igwId: '391384',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Http.sys RCE attack': {
            file: 'http-sys-rce-attack.js',
            load: true,
            vulnerabilities: {
                'ID-http-sys-rce': {
                    productionReady: true,
                    igwId: '292378',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },
        /*
        Disabled, Mode: obsolete
        'CGI SSI vulnerability': {
            file: 'cgi-ssi-attack.js',
            load: false,
            vulnerabilities: {
                'ID-cgi-ssi-found': {
                    productionReady: false,
                    igwId: '292383',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        }, */
        'ESI Injection vulnerability': {
            file: 'esi-inj-attack.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-esi-injection': {
                    productionReady: true,
                    igwId: '292406',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Apache log4j vulnerability': {
            file: 'apache-log4j.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-apache-log4j-rce': {
                    productionReady: true,
                    igwId: '292407',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Apache ETag header Found vulnerability': {
            file: 'apache-etag-headerfnd.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain|javascript|css)|application\/(json|java-archive|javascript)|image\/.+/i,
            vulnerabilities: {
                'ID-Apache-ETag-Header-Info': {
                    productionReady: true,
                    igwId: '292408',
                    maxInstancesOfEachVulnToReport: 5,
                }
            },
        },
        'OS Command Inj OOB': {
            file: 'os-cmd-inj-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-os-command-injection-oob': {
                    productionReady: true,
                    igwId: '292419',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'XML External Entity Injection OOB': {
            file: 'xml-external-entity-injection-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-xml-external-entity-oob': {
                    productionReady: true,
                    igwId: '292424',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'SQL Injection OOB': {
            file: 'sql-inj-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-sql-injection-oob': {
                    productionReady: true,
                    igwId: '332834',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'Cross Site Scripting (XSS) OOB': {
            file: 'xss-attack-oob.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-xss-attack-oob': {
                    productionReady: true,
                    igwId: '332836',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                }
            },
        },
        'SSRF OOB': {
            file: 'ssrf-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-ssrf-oob': {
                    productionReady: true,
                    igwId: '332835',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Host Header Injection OOB': {
            file: 'host-header-inj-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-host-header-inj-oob': {
                    productionReady: true,
                    igwId: '332837',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'Server-Side Template Injection OOB': {
            file: 'ssti-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-sstinj-oob': {
                    productionReady: true,
                    igwId: '332838',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Client-Side Template Injection OOB': {
            file: 'csti-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-client-side-template-injection': {
                    productionReady: true,
                    igwId: '375633',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Spring Expression Resource Access RCE': {
            file: 'spring-expression-resource-access.js',
            load: true,
            //allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-sera-rce-vuln': {
                    productionReady: true,
                    igwId: '332840',
                    maxInstancesOfEachVulnToReport: -1
                },
            },
        },
        /* 'no captcha on login page.': { - Moved to account lockout plugin to reduce no of attack
            file: 'no-captcha-on-login-page.js',
            load: true,
            LoginAttempts: 5,
            // allowedContentTypes: /text\/(html|xml)|application\/json/i,
            canAttackInApiScan: false,
            processAttackResponseInApiScan: false,
            vulnerabilities: {
                'ID-no-captcha-on-login-page': {
                    productionReady: false,
                    igwId: '373895',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        }, */
        'Code Injection OOB': {
            file: 'code-inj-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-code-injection-oob': {
                    productionReady: true,
                    igwId: '332841',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'Cross-Site Flashing (XSF) OOB': {
            file: 'cross-site-flashing.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-cross-site-flashing': {
                    productionReady: true,
                    igwId: '292405',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'VMware Server-side Template Injection (RCE) Vulnerability': {
            file: 'vmware-ssti-rce.js',
            load: true,
            vulnerabilities: {
                'ID-vmware-ssti-rce': {
                    productionReady: true,
                    igwId: '332842',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Apache OFBiz Vulnerability': {
            file: 'apache-ofbiz-authentication-bypass.js',
            load: true,
            vulnerabilities: {
                'ID-apache-ofbiz-authentication-bypass': {
                    productionReady: true,
                    igwId: '379323',
                    maxInstancesOfEachVulnToReport: 1, //
                    canAttackInReplayScan: true
                },
                'ID-apache-ofbiz-path-traversal': { //Created on 2024-08-08
                    productionReady: true,
                    igwId: '387529',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Password in Response': {
            file: 'password-in-response.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-password-in-response': {
                    productionReady: true,
                    igwId: '332843',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            }
        },
        'ExtJs Arbitrary File Read': {
            file: 'extjs-arbitrary-file-read.js',
            load: true,
            vulnerabilities: {
                'ID-extjs-arbitrary-file-read': {
                    productionReady: true,
                    igwId: '383591',
                    maxInstancesOfEachVulnToReport: 1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Malicious Content Found': {
            file: 'malicious-content-found.js',
            load: true,
            allowedContentTypes: /text\/(html|plain)/i,
            vulnerabilities: {
                'ID-malicious-content-found': {
                    productionReady: false,
                    igwId: '332844',
                    maxInstancesOfEachVulnToReport: -1
                }
            }
        },
        'Link Injection OOB': {
            file: 'link-inj-oob.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-link-injection-oob': {
                    productionReady: true,
                    igwId: '332848',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                }
            },
        },
        'PRSSI vuln': {
            file: 'path-relative-stylesheet-import.js',
            load: true,
            allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-path-relative-stylesheet-import': {
                    productionReady: true,
                    igwId: '344608',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                }
            }
        },
        'Iframe Injection OOB': {
            file: 'iframe-injection-oob.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/json/i,
            canAttackInOOB: true,
            vulnerabilities: {
                'ID-Iframe-injection-oob': {
                    productionReady: true,
                    igwId: '344609',
                    maxInstancesOfEachVulnToReport: -1, // report all that we find
                    canAttackInReplayScan: true
                },
            },
        },
        'Port scan': {
            file: 'portscan.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-port-scanner': {
                    productionReady: true,
                    igwId: '347792',
                    maxInstancesOfEachVulnToReport: 1,
                },
            },
        },
        'tls vuln': {
            file: 'tls-vuln.js',
            load: true,
            vulnerabilities: {
                'ID-session-resumption': {
                    productionReady: true,
                    igwId: '350128',
                    maxInstancesOfEachVulnToReport: 1,
                },
                'ID-client-side-renegotiation': {
                    productionReady: true,
                    igwId: '405115',
                    maxInstancesOfEachVulnToReport: 1,
                },
                'ID-server-side-renegotiation': {
                    productionReady: true,
                    igwId: '405114',
                    maxInstancesOfEachVulnToReport: 1,
                },
                'ID-client-side-without-rfc-5746': {
                    productionReady: true,
                    igwId: '405108',
                    maxInstancesOfEachVulnToReport: 1,
                },
                'ID-server-side-without-rfc-5746': {
                    productionReady: true,
                    igwId: '405107',
                    maxInstancesOfEachVulnToReport: 1,
                },
                'ID-self-signed-certificate': {
                    productionReady: true,
                    igwId: '405112',
                    maxInstancesOfEachVulnToReport: 1,
                },
            },
        },
        'DNSSEC Not Signed': {
            file: 'dnssec-vuln.js',
            load: true,
            vulnerabilities: {
                'ID-dnssec-not-signed': {
                    productionReady: true,
                    igwId: '350129',
                    maxInstancesOfEachVulnToReport: 1,
                },
            },
        },
        'Text Injection': {
            file: 'text-injection.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-text-injection': {
                    productionReady: true,
                    igwId: '350130',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        },
        'Weak Encoding': {
            file: 'weak-encoding.js',
            load: true,
            allowedContentTypes: /text\/(html|xml|plain)|application\/(json|xml)/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-weak-encoding': {
                    productionReady: true,
                    igwId: '350132',
                    maxInstancesOfEachVulnToReport: 10,
                },
            },
        },
        'Reveals Sensitive Information': {
            file: 'reveals-sensitive-info.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-reveals-sensitive-info': {
                    productionReady: true,
                    igwId: '353793',
                    maxInstancesOfEachVulnToReport: 5,
                },
                'ID-reveals-sensitive-low': {
                    productionReady: true,
                    igwId: '352049',
                    maxInstancesOfEachVulnToReport: 5,
                },
            },
        },
        'Exposure of Sensitive Filenames': {
            file: 'exposure-sensitive-filenames.js',
            load: true,
            allowedContentTypes: /(text\/(html|plain)|application\/(json|xml|octet-stream))/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-exposure-of-sensitive-filenames': {
                    productionReady: true,
                    igwId: '393296',
                    maxInstancesOfEachVulnToReport: 5,
                }
            },
        },
        'Accessible By IP Address': {
            file: 'accessible-ip-address.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-accessible-by-ip-address': {
                    productionReady: true,
                    igwId: '353794',
                    maxInstancesOfEachVulnToReport: 1,
                }
            },
        },
        'MSExchange ProxyShell': {
            file: 'ms-exchange-proxyshell.js',
            load: false,
            allowedContentTypes: /text\/\w|application\/\w/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-ms-exchange-proxyshell': {
                    productionReady: false,
                    igwId: '355567',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
                'ID-ms-exchange-proxynotshell': {
                    productionReady: false,
                    igwId: '355568',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
        },
        'EPMM Authentication Bypass': {
            file: 'epmm-authentication-bypass.js',
            load: true,
            // allowedContentTypes: /text\/\w|application\/\w/i,
            // processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-epmm-authentication-bypass': {
                    productionReady: true,
                    igwId: '373896',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
        },
        'WebSocket URL poisoning': {
            file: 'websocket-url-poisoning.js',
            load: true,
            // allowedContentTypes: /text\/\w|application\/\w/i,
            // processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-websocket-url-poisoning': {
                    productionReady: true,
                    igwId: '375631',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: false
                }
            },
        },
        'Browsable Directory and File': {
            file: 'browsable-df-list.js',
            load: false,
            allowedContentTypes: /text\/html/i,
            canAttackInReplayScan: false,
            canAttack: false,
            processAttackResponse: false,
            vulnerabilities: {
                'ID-possible-sensitive-file-or-directory': {
                    productionReady: true,
                    igwId: '24865',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                },
                'ID-browsable-web-directory': {
                    productionReady: true,
                    igwId: '403',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                },
                'ID-archive-backup-file': {
                    productionReady: true,
                    igwId: '252258',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                },
                'ID-possible-backup-file': {
                    productionReady: true,
                    igwId: '24864',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                },
                'ID-core-dump-file': {
                    productionReady: true,
                    igwId: '252299',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                }
            },
        },
        'Sensitive Directory List': {
            file: 'dirlistauto.js',
            load: false,
            allowedContentTypes: /text\/html/i,
            canAttack: false,
            processAttackResponse: false,
            vulnerabilities: {
                'ID-sensitive-file-directory': {
                    // detectionTimeout: 1800,
                    productionReady: true,
                    igwId: '375632',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                },
            },
        },
        'Sensitive File List': {
            file: 'filelistauto.js',
            load: false,
            allowedContentTypes: /text\/html/i,
            canAttack: false,
            processAttackResponse: false,
            vulnerabilities: {
                'ID-sensitive-file-directory': {
                    productionReady: true,
                    // detectionTimeout: 1800,
                    igwId: '375632',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: false
                },
            },
        },
        'Form Action Hijacking': {
            file: 'form-action-hijacking.js',
            load: true,
            allowedContentTypes: /text\/html/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-form-action-hijacking': {
                    productionReady: true,
                    igwId: '203482',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: false
                },
            },
        },
        'Code Injection Attack': {
            file: 'code-inj.js',
            load: true,
            // allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-code-injection': {
                    productionReady: true,
                    igwId: '292403',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                },
            },
        },
        'Jenkins Args4j Attack': {
            file: 'jenkins-args4j.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            vulnerabilities: {
                'ID-jenkins-args4j': {
                    productionReady: true,
                    igwId: '381999',
                    maxInstancesOfEachVulnToReport: 1,
                    // canAttackInReplayScan: true
                },
            },
        },
        'Atlassian Confluence RCE': {
            file: '************************.js',
            load: true,
            // allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-************************': {
                    productionReady: true,
                    igwId: '380225',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },
        'PHP CGI Argument Injection Vulnerabilty': {
            file: 'php-cgi-argument-injection-vulnerability.js',
            load: true,
            // allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-php-cgi-argument-injection-vulnerability': {
                    productionReady: true,
                    igwId: '384671',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Expression Language Injection': {
            file: 'expression-language-injection.js',
            load: true,
            // allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-expression-language-injection': {
                    productionReady: true,
                    igwId: '380224',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                },
            },
        },

        'CSS Injection': {
            file: 'css-injection.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-css-injection': {
                    productionReady: false,
                    igwId: '405111',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                }
            }
        },
        'Insecure WebSocket Detector': {
            file: 'insecure-websocket-detector.js',
            load: true,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-insecure-websocket': {
                    productionReady: true,
                    igwId: '405113',
                    maxInstancesOfEachVulnToReport: 2,
                    canAttackInReplayScan: true
                }
            }
        },
        'Sensitive Data in Unencrypted Viewstate': {
            file: 'viewstated-sensitive-data.js', // The plugin file to load
            load: true, // Enable this plugin
            allowedContentTypes: /text\/(html|xml|plain|json)|application\/json/i, // Content types to process
            processAttackResponseInApiScan: false, // not API scan responses
            vulnerabilities: {
                'ID-viewstated-sensitive-data': {
                    productionReady: true, // Mark this vuln as production ready
                    igwId: '405110', // Internal/global vuln ID
                    maxInstancesOfEachVulnToReport: 1 // Only report one instance per scan
                },
            },
        },
        'Unsafe third-party link': {
            file: 'target-blank.js',
            load: true,
            allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-target-blank': {
                    productionReady: true,
                    igwId: '387528',
                    maxInstancesOfEachVulnToReport: 5
                },
                'ID-target-blank-low': {
                    productionReady: true,
                    igwId: '388450',
                    maxInstancesOfEachVulnToReport: 5
                },
            },
        },
        'Unauthenticated Next.js Application': {
            file: 'unauth-next-js.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            vulnerabilities: {
                'ID-unauth-next-js': {
                    productionReady: false,
                    igwId: '400189',
                    maxInstancesOfEachVulnToReport: 1,
                    canAttackInReplayScan: true
                }
            },
            vulnerabilities: {
                'ID-unauth-next-js-blackbox-test': {
                    productionReady: true,
                    igwId: '400190',
                    maxInstancesOfEachVulnToReport: 1
                }
            },
        },
        'NoSQL Injection': {
            file: 'no-sql-inj.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            vulnerabilities: {
                'ID-no-sql-injection': {
                    productionReady: true,
                    igwId: '388451',
                    maxInstancesOfEachVulnToReport: 5,
                    canAttackInReplayScan: true
                }
            },
        },
        'jQuery and JS Framework Version Detection': {
            file: 'detect-jquery.js',
            load: true,
            allowedContentTypes: /text\/html/i,
            vulnerabilities: {
                'ID-jquery-version-check': {
                    productionReady: true,
                    igwId: '405101',
                    maxInstancesOfEachVulnToReport: 2,
                },
                'ID-jsframework-version-check': {
                    productionReady: true,
                    igwId: '405102',
                    maxInstancesOfEachVulnToReport: 2,
                },
            },
        },
        'DOM Location Manipulation': {
            file: 'dom-location-manipulation.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-dom-location-manipulation': {
                    productionReady: false,
                    igwId: '405109',
                    maxInstancesOfEachVulnToReport: -1,
                    canAttackInReplayScan: true
                },
            },
        },
        'Session Not Expired after Logout': {
            file: 'session-not-expired.js',
            load: true,
            allowedContentTypes: /text\/\w|application\/\w/i,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-session-not-expired': {
                    productionReady: false,  
                    igwId: '405201',
                    maxInstancesOfEachVulnToReport: 1,
                },
            },
        },

        // attack/vunerability check plugins
        'File Upload Attack': {
            file: 'file-upload-attack.js',
            load: true,
            allowedContentTypes: /multipart\/form-data|text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-unrestricted-file-upload-vuln': {
                    productionReady: false,
                    igwId: '400191',
                    maxInstancesOfEachVulnToReport: -1
                },
                'ID-blacklist-bypass-file-upload-vuln': {
                    productionReady: false,
                    igwId: '400192',
                    maxInstancesOfEachVulnToReport: -1
                },
                'ID-double-extension-file-upload-vuln': {
                    productionReady: false,
                    igwId: '400193',
                    maxInstancesOfEachVulnToReport: -1
                },
                'ID-special-char-bypass-file-upload-vuln': {
                    productionReady: false,
                    igwId: '400194',
                    maxInstancesOfEachVulnToReport: -1
                },
            }
        },

        'File Upload MIME Type Validation Bypass': {
            file: 'file-upload-mime-type-attack.js',
            load: false,
            allowedContentTypes: /multipart\/form-data|text\/(html|xml|plain)|application\/json/i,
            canAttackInApiScan: true,
            processAttackResponseInApiScan: true,
            vulnerabilities: {
                'ID-mime-type-validation-bypass-vuln': {
                    productionReady: false,
                    igwId: '400195',
                    maxInstancesOfEachVulnToReport: -1
                }
            },
        },

        // output plugins
        'Output: Send Vulnerability To SOC Portal': {
            file: 'output/send-vuln-to-soc.js',
            load: true,
        },
        'Output: Send Vulnerability To WAS Portal (new API)': {
            file: 'output/send-vuln-to-was.js',
            load: true,
        }
    }
}