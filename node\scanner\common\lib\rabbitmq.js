const amqp = require('amqplib');
const uuidv4 = require('uuid/v4'); // random uuid
const DeferredPromise = require('../../common/lib/deferred-promise')
const _ = require('lodash')
const debug = require('debug')('MessageQ')
const logger = require('./haiku-logger')
const { MACHINE_TYPES } = require('../config/app-constants');

class MessageQ {
    constructor(product, bindingDetails = null) {
        this.config = require('../config/rabbitmq-config')
        this.config.server.hostname = process.env['RABBITMQ_HOSTNAME'] || this.config.server.hostname
        this.product = product

        this.consumers = [] // all consume() info for auto recover on connection failures & includes consume tags in case ever need to 'unconsume' = cancel
        this.queues = {}
        this.outstandingRequests = {}
        this.waitbetweenRetry = 1 * this.config.server.expBackoffConnectionRetry
        this.initializing = false
        this.connectionId = 1,
        this.config.bindingDetails = bindingDetails;
    }

    reInit(err) {
        logger.log('error', 'Lost connection to RabbitMQ server ', err ? err.toString() : '')
        logger.log('info', `Retrying connection to RabbitMQ server in ${this.waitbetweenRetry/1000} seconds`)

        setTimeout(this.init.bind(this), this.waitbetweenRetry)
        if (this.waitbetweenRetry < this.config.server.maxBackoff * this.config.server.expBackoffConnectionRetry) {
            this.waitbetweenRetry *= 2
        }
    }

    async init() {
        if (this.initializing) {
            logger.log('info', `** RABBITMQ ** init called while we are initializing, ignoring ...`)
            return
        }
        this.initializing = true
        let ret
        try {
            ret = await this._init()
        } catch (ignoreErr) {
            // nothing to do, ctach is just to ensure initializing flag set correctly.
        }
        this.initializing = false
        return ret
    }

    async _init() {
        // sanity check - should never happen
        if (this.conn || this.ch) {
            logger.log('error', ` ** RABBITMQ ** init called but we have a connection ${this.conn.haikuId} !!!. continuing...`)
        }

        // create RabbitMQ connection, channel, bind queues etc.
        let config = this.config
        await this._createConnection(config);

        if (!this.conn) {
            this.reInit()
            return
        }

        // ** Once it reaches here we have a connection, all further reinit will be handled by teh close event of 
        // the connection
        try {
            logger.log('info', `conn id ${this.conn.haikuId} rabbitmq creating channel`)
            this.ch = await this.conn.createChannel()
            this.ch.haikuConnection = this.conn // if we did not get a ch, blow up and let catch handler do it's job to revcover connection

            // set up handler for channel close
            let theCh = this.ch
            this.ch.once('close', () => {
                // the if below is because events are async and recovery is on a timer so we could be in a stage here a previous
                // RabbitMQ call executes while we are (re)-initing.
                if (this.ch == theCh) {
                    logger.log('error', `RabbitMQ channel closed, closing connection. conn id = ${this.conn.haikuId}`)
                    this._closeConnection()
                } else {
                    logger.log('info', `rabbitmq ignoring channel close on not current connection: conn id = ${theCh.haikuConnection?theCh.haikuConnection.haikuId:-1}, current connection = ${this.conn?this.conn.haikuId:-1}`)
                }
            })

            // set the prefetch value if set in config
            let prefetchCount = _.get(config[this.product], 'channel.prefetch')
            if (prefetchCount) {
                this.ch.prefetch(prefetchCount) 
            }

            // set up handler for returned messages
            this.ch.on('return', (msg) => {
                logger.log('info', `returned message: ${msg} `)
            })

            // always consume the direct reply queue for RPC requests
            // https://www.rabbitmq.com/direct-reply-to.html
            logger.log('info', `rabbitmq setting up reply to channel. conn id ${this.conn?this.conn.haikuId:-1}`)
            await this._consume('amq.rabbitmq.reply-to', this.rpcResponseHandler.bind(this), {
                noAck: true
            })
            if (config[this.product]) {
                // create exchanges
                logger.log('info', `rabbitmq creating exchanges. conn id ${this.conn?this.conn.haikuId:-1}`)
                if (config[this.product].exchanges) {
                    for (let exchange of config[this.product].exchanges) {
                        await this.__assertExchange(exchange.name, exchange.type, exchange.options)
                    }
                }

                let directScannerMessageQueue = _.find(config[MACHINE_TYPES.SCANNER].queues, { name: 'directMessage' });

                if(this.config.bindingDetails && directScannerMessageQueue) {
                    // direct messaging between processes using queues i.e. crawler and scanner
                    directScannerMessageQueue.queueName = directScannerMessageQueue.queueName.replace('{{scannerMachineUID}}', this.config.bindingDetails.scannerMachineUID);
                }

                // create queues
                logger.log('info', `rabbitmq creating queues. conn id ${this.conn?this.conn.haikuId:-1}`)
                if (config[this.product].queues) {
                    for (let q of config[this.product].queues) {
                        this.queues[q.name] = await this.__assertQueue(q.queueName, q.options)
                    }
                }

                let directScannerMessageBinding = _.find(config[MACHINE_TYPES.SCANNER].bindings, { name: 'directMessage' });

                if(this.config.bindingDetails && directScannerMessageBinding) {
                    // direct messaging between processes using queues based on rourtingKey pattern i.e. 'direct.message.127.0.0.1.rpc.crawl-started'
                    directScannerMessageBinding.queue = directScannerMessageBinding.queue.replace('{{scannerMachineUID}}', this.config.bindingDetails.scannerMachineUID);
                    directScannerMessageBinding.pattern = directScannerMessageBinding.pattern.replace('{{scannerMachineUID}}', this.config.bindingDetails.scannerMachineUID);
                }

                // create bindings
                logger.log('info', `rabbitmq creating bindings. conn id ${this.conn?this.conn.haikuId:-1}`)
                if (config[this.product].bindings) {
                    for (let binding of config[this.product].bindings) {
                        let queue = this.getQueueName(binding.queue);
                        await this.__bindQueue(queue, binding.exchange, binding.pattern)
                    }
                }
            }

            // autorecover any consume() that we have to
            logger.log('info', `rabbitmq autorecovering ${this.consumers.length} consume calls. conn id ${this.conn?this.conn.haikuId:-1}`)
            for (let consumer of this.consumers) {
                let tag = await this._consume(consumer.queue, consumer.callback, consumer.options)
                consumer.tag = tag
            }

            // reset the backoff.
            this.waitbetweenRetry = 1 * this.config.server.expBackoffConnectionRetry
        } catch (err) {
            logger.log('error', `in init(), ${err.toString()}`)
            if (this.conn) {
                this._closeConnection() // will trigger the reinit via close event
            }
        }
    }

    async _createConnection(config) {
        try {
            logger.log('info', `connecting to rabbitmq server ${config.server.hostname}`);
            this.conn = await amqp.connect(config.server);
            this.conn.haikuId = this.connectionId++;
            logger.log('info', `rabbitmq - created connection with conn id ${this.conn.haikuId}`);

            // we have successfully connected: setup reconnect on connection close handler
            let theConn = this.conn
            this.conn.once('error', (err) => {
                logger.log('error', `error event -> conn id ${theConn?theConn.haikuId:-1} lost connection to RabbitMQ server, will retry in close event ${err?err.toString:''}`);
            });
            this.conn.once('close', (err) => {
                // the if below is because events are async and recovery is on a timer so we could be in a stage here a previous
                // RabbitMQ call executes while we are (re)-initing.
                if (this.conn == null || theConn == this.conn) {
                    this.conn = null
                    this.ch = null
                    this.reInit(err)
                } else {
                    logger.log('info', `rabbitmq ignoring connection close event on not current connection: conn id = ${theConn.haikuId}, current connection = ${this.conn?this.conn.haikuId:-1}`)
                }
            })
        } catch (err) {
            logger.log('error', `in _createConnection(), ${err.toString()}`)
            this.conn = null
        }
    }

    /**
     * Close the connection- will trigger the close event and reinit the connectiion. This is an internal method
     * If we need a clean close connection, it will be without the leading _ and will remove the close event handler before closing.
     */
    _closeConnection() {
        if (this.conn) {
            logger.log('info', `rabbitmq closing connection ${this.conn.haikuId}`)
            try {
                let conn = this.conn
                this.conn = null
                this.ch = null
                conn.close()
            } catch (err) {
                logger.log('error', `rabbitmq - error closing connection  ${err.toString()}`)
            }
        }
    }

    publish(exchange, routingKey, content, options) {
        // if needed, can add debug checks to see if its an exchange we have created
        // but in the interest of performance, dont need that now, let it error out

        // add our envelope (if needed) and set up common message fields
        let msg = this.__prepareForPublish(content, options);

        // finally publish 
        return this.ch.publish(exchange, routingKey, msg.content, msg.options)
    }

    async request(exchange, routingKey, content, options) {
        // add our envelope (if needed) and set up common message fields
        let msg = this.__prepareForPublish(content, options);

        // set up reply to queue. 
        // todo - timeout while waiting for reply
        msg.options.replyTo = 'amq.rabbitmq.reply-to'

        // finally publish 
        let dp = new DeferredPromise()
        let ret = await this.ch.publish(exchange, routingKey, msg.content, msg.options)
        if (!ret) {
            dp.__reject('channel write buffer full')
            return dp
        }

        // publish succeeded, now wait for reply.
        this.outstandingRequests[msg.options.correlationId] = dp
        return {
            correlationId: msg.options.correlationId,
            promise: dp.p
        }
    }

    // Reply to a RPC request using direct reply-to: https://www.rabbitmq.com/direct-reply-to.html
    reply(mqRequest, replyContent, options) {
        // ensure correlation ID is set correctly
        let msg = this.__prepareForPublish(replyContent, options)
        msg.options.correlationId = mqRequest.properties.correlationId
        return this.ch.publish("", mqRequest.properties.replyTo, msg.content, msg.options)
    }

    rpcResponseHandler(msg) {
        let dp = this.outstandingRequests[msg.properties.correlationId]
        if (dp) {
            delete this.outstandingRequests[msg.properties.correlationId]
            dp.__resolve(msg)
        }
    }
    /**
     * Discard the RP request, typically because of timeout
     * @param {Number} correlationId The coorelation ID of RPC request to discard
     * @param {*} reason why are we discarding, deferred promise will be rejected with this reason
     */
    discardRequest(correlationId, reason) {
        let dp = this.outstandingRequests[correlationId]
        if (dp) {
            delete this.outstandingRequests[correlationId]
            dp.__reject(reason)
        }
    }

    async consume(queue, callback, options) {
        // keep track of this consume info for autorecover
        let consumeInfo = {
            queue,
            callback,
            options,
        }

        // keep track for reinit recoverey even if this consume fails.
        try {
            consumeInfo.tag = await this._consume(queue, callback, options)
        } catch (err) {
            logger.log('error', `** RABBITMQ ** error in consume()${err.toString()}`)
        }
        this.consumers.push(consumeInfo)
        return consumeInfo.tag
    }

    _consume(queue, callback, options) {
        // map friendly name with actual queuename if needed
        queue = this.getQueueName(queue);
        logger.log('info', `** RABBITMQ ** consume : ${queue}, ${callback.name}, ${options} `)
        return this.ch.consume(queue, callback, options)
    }

    ack(msg, allUpTo) {
        return this.ch.ack(msg, allUpTo)
    }

    nack(msg, allUpTo, requeue) {
        return this.ch.nack(msg, allUpTo, requeue)
    }

    getQueueName(queue) {
        if (this.queues[queue]) {
            queue = this.queues[queue].queue;
        }
        return queue;
    }

    // -- internal methids --
    __prepareForPublish(content, options) {
        // add our envelope (if needed) and set up common message fields
        let msg = {
            options: options || {},
            content: content
        }
        msg.options.messageId = msg.options.correlationId = uuidv4();
        msg.options.timestamp = Date.now()
        msg.options.appId = this.product
        return msg;
    }


    // these are simple shims around the library functions
    // I expect these will not be needed if the config style of create works as it should
    // in which case, these can be removed.
    __assertExchange(exchange, type, options) {
        return this.ch.assertExchange(exchange, type, options)
    }

    __assertQueue(queue, options) {
        return this.ch.assertQueue(queue, options)
    }

    __bindQueue(queue, exchange, pattern) {
        return this.ch.bindQueue(queue, exchange, pattern)
    }

}

module.exports = MessageQ