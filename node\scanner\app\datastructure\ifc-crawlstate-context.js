let utils = require('../ifc-utils.js')
const fs = require("fs");
const ActionList = require('../datastructure/action-list.js')
const createActionList = require('../datastructure/create-action-list')
const FingerPrint = require('../datastructure/fingerprint.js')
const logger = require('../../common/lib/haiku-logger')
const _ = require('lodash')

let allContextStates = []

class IfcCrawlContext {
    constructor(parent = null) {
        this.contextStateSeq = allContextStates.push(this) - 1
        this.children = []

        // handle depth and siblings
        if (!!parent) {
            this.parentSeq = parent.contextStateSeq
            this.depth = parent.depth + 1
            this.siblingIdx = parent.children.push(this.contextStateSeq) - 1
            this.path = [...parent.path, this.siblingIdx]
        } else {
            this.depth = 0
            this.path = []
            this.parentSeq = -1
        }

        this.actions = []
        this.interestingItems = {}
        this.interestingItems.serverData = {}
        this.dupInfo = {
            minActionSimilarity: -1,
            maxActionSimilarity: -1,
            avgActionSimilarity: -1,
            wavgActionSimilarity: -1,
            countActionSimilarity: 0,
            dupThreshold: -1,
            correctGuesses: 0
        }
        this.curProcIndex = -1
        this.state = 'created' // created, processing, complete

        // construct replay actions to get to this context. 
        if (parent) {
            this.replayActions = new ActionList(`replay-ctx-${this.contextStateSeq}`); // Initialize with context ID

            let parentCurAction = parent.curAction(); // Get the action in the parent that led here
            let parentId = parent.getId();

            // --- Simplified Replay Logic --- 
            // Always try to add parent's replay path unless parent is root (parentSeq == -1) or has no replay actions itself.
            let shouldAddParentReplay = parent.parentSeq !== -1 && parent.replayActions && parent.replayActions.getNumActions() > 0;

            if (shouldAddParentReplay) {
                let parentReplay = parent.getReplayActions(); // getReplayActions should already return the ActionList object
                if (parentReplay) { 
                    this.replayActions.addAction(parentReplay);
                } else {
                    // This case should ideally not happen if the check above is correct, but log defensively.
                    utils.log(`WARN: Parent context ${parentId} indicated replay actions should exist but getReplayActions() returned null/undefined.`);
                }
            } 
            // No need for an else log here, it's normal not to add replay from root.

            // Add the specific action from the parent that led to this state. Ensure it exists.
            if (parentCurAction) {
                this.replayActions.addAction(parentCurAction);
            } else {
                // This might indicate a timing issue or that the parent context finished its actions unexpectedly.
                utils.log(`WARN: Parent context ${parentId} had null curAction() when creating child context ${this.getId()}. Replay path might be incomplete.`);
            }
            // --- End Simplified Replay Logic ---

            // Log the results clearly using file append --- MOVED TO createState method ---
            // fs.appendFileSync(this.outFile, JSON.stringify({ 
            //     event: 'ContextCreated',
            //     contextId: this.getId(),
            //     parentId: parentId,
            //     parentAction: parentCurAction ? parentCurAction.toString() : 'null',
            //     addedParentReplay: shouldAddParentReplay,
            //     finalReplayActionsCount: this.replayActions.getNumActions(),
            //     finalReplayActions: this.replayActions.toString(), // Keep string for log size
            //     timestamp: new Date().toISOString()
            // }) + '\n'); // Add newline for readability

        }
        // If no parent (root node), this.replayActions remains undefined, which is correct.

        this.actionsResultingInThisState = {}
        // By definition the action set that creates a state matches 100%
        this.actionsResultingInThisState[100] = this.replayActions ? [this.replayActions.flatten()] : []
    }

    // methods
    curAction() {
        // Check if actions is a valid array and index is within bounds
        return this.moreActions() ? this.actions[this.curProcIndex] : null
    }

    /**
     * get an id that can uniqiuely identify the state
     */
    getId() {
        return this.contextStateSeq
    }

    /**
     * get unique id the current action (action to be performed/beng perfomed)in this state
     */
    curActionId() {
        return this.curProcIndex
    }

    /**
     * 
     * @param {*} actionId ID returned by (@link curActionId)
     */
    getAction(actionId) {
        // Check if actions is a valid array before accessing
        return Array.isArray(this.actions) && actionId >= 0 && actionId < this.actions.length ? this.actions[actionId] : null
    }

    /**
     * get child context of state by child index
     * @param {Number} index which child to get
     */
    getChild(index) {
        let child = null
        if (index < this.children.length && this.children[index] < allContextStates.length) {
            child = allContextStates[this.children[index]]
        }
        return child
    }

    moreActions() {
        // Check if actions is a valid array before comparing index
        return Array.isArray(this.actions) && this.curProcIndex < this.actions.length;
    }

    nextAction() {
        // Check if actions is a valid array before incrementing index
        if (Array.isArray(this.actions) && this.curProcIndex < this.actions.length) {
            this.curProcIndex++
        } else if (!Array.isArray(this.actions)){
            utils.log(`nextAction: Attempted on context ${this.getId()} with invalid actions array.`);
        }
        // If index is already at or beyond length, do nothing.
    }

    getActionCount() {
        // Return 0 if actions is not a valid array
        return Array.isArray(this.actions) ? this.actions.length : 0
    }

    getActionDoneCount() {
        // Return 0 if actions is not a valid array
        return Array.isArray(this.actions) ? this.curProcIndex + 1 : 0
    }

    getReplayActions() {
        return this.replayActions
    }

    addActions(actions) {
        this.actions = actions
        //this.logContextInfo('addActions')
    }

    appendActions(actions) {
        // Ensure this.actions is an array before pushing
        if (!Array.isArray(this.actions)) {
            this.actions = []; // Initialize if not already an array
            utils.log(`appendActions: Initialized actions array for context ${this.getId()}`);
        }
        if (actions && actions.length) {
            this.actions.push(...actions)
            this.state = 'processing'
        }
    }

    addInterestingItems(interestingItems) {
        this.interestingItems = interestingItems ? interestingItems : {}
    }

    addActionFingerprint(actionFingerprint) {
        this.actionFingerprint = actionFingerprint
    }

    getFingerprintTokens(action) {
        if (!this.interestingItems) {
            return []
        }

        let tokens = []
        let xpaths = action.getXPaths ? action.getXPaths() : [action.getXPath()]
        for (let xpath of xpaths) {
            let findByXpath = function (element) {
                return (element[0] == xpath)
            }

            let item
            if (this.interestingItems.inputItems) {
                item = this.interestingItems.inputItems.find(findByXpath)
            }
            if (!item && this.interestingItems.formItems) {
                item = this.interestingItems.formItems.find(findByXpath)
            }
            if (!item && this.interestingItems.clickableItems) {
                item = this.interestingItems.clickableItems.find(findByXpath)
            }
            if (!item && this.interestingItems.linkItems) {
                item = this.interestingItems.linkItems.find(findByXpath)
            }
            if (item) {
                tokens.push(item[1].fingerprintTokens)
            }
        }

        return tokens
    }
}

class IfcCrawlStateContext {
    constructor(config, actions) {
        this.config = config
        this.outFile = `${this.config.logPath}/crawl-context.json`
        fs.openSync(this.outFile, 'w');

        // create the default crawlcontext
        let initialActions = []
        for (let wizardGuide of config.initialWizardGuides) {
            let rootAction = createActionList(wizardGuide)
            if (rootAction.getActions() && rootAction.getActions().length) {
                initialActions.push(rootAction)
            }
        }

        this.crawlContext = new IfcCrawlContext(null)
        this.currentContext = this.crawlContext
        this.crawlContext.addActions(initialActions)

        this.bfsQueue = []

        // log the depth & actions
        this.logContextInfo('root')
    }

    /**
     * get the current crawl depth
     */
    getCurrentDepth() {
        return this.currentContext ? this.currentContext.depth : 0
    }

    logContextInfo(fromWhere = '') {
        // Ensure context is valid before logging details
        if (!this.currentContext) {
            // Use utils.log for immediate visibility of missing context
            utils.log(`logContextInfo (${fromWhere}): currentContext is null or undefined.`);
            return;
        }

        let actionsInfo = "Invalid/Empty";
        if (Array.isArray(this.currentContext.actions)) {
             actionsInfo = `${this.currentContext.getActionDoneCount()}/${this.currentContext.getActionCount()}`;
        }
        
        // Log main context info to file
        fs.appendFileSync(this.outFile, JSON.stringify({
            event: 'LogContextInfo',
            source: fromWhere,
            contextId: this.currentContext.getId(),
            depth: this.currentContext.depth,
            actionsInfo: actionsInfo,
            state: this.currentContext.state,
            timestamp: new Date().toISOString()
        }) + '\n'); // Add newline

        // utils.log(`logContextInfo (${fromWhere}): contextId: ${this.currentContext.getId()}, depth: ${this.currentContext.depth}, actions(done/total): ${actionsInfo}, state: ${this.currentContext.state}`) // Replaced by file append
    }


    /**
     * Move iteration back to root without resetting processed actions. This is useful when doing 
     * limited number of actions per state so we can come back if there is more time left in crawl
     * The iteration will have to start again as well.
     */
    moveToRoot() {
        this.currentContext = this.crawlContext
    }

    // context returns a BFS iterator
    * getIterator() {
        let replayReqd = this.currentContext.contextStateSeq == 0 // for root dont need replay
        while (!!this.currentContext) {
            //Get total number load actions in currentContext
            let totalLoadActions = _.size(_.filter(this.currentContext.actions, (action)=> {
                return action.actionType == 'load'
            }));

            let actionsBeforeLoad = 0;

            //Reset to default maxActionsPerStatePerIteration
            this.config.maxActionsPerStatePerIteration = this.config.lastMaxActionsPerStatePerIteration ||  this.config.maxActionsPerStatePerIteration;

            //Give priority to actions before any load actions. i.e. Login steps.
            if(totalLoadActions > 0) {
                actionsBeforeLoad = _.size(_.takeWhile(this.currentContext.actions, (action)=> {
                    return action.actionType != 'load'
                }));
            }
            //if total load actions are greater than maxActionsPerStatePerIteration then change maxActionsPerStatePerIteration to totalLoadActions + 1
            //to tell crawler to visit all load actions first. i.e. urls
            if(totalLoadActions > this.config.maxActionsPerStatePerIteration) {
                this.config.lastMaxActionsPerStatePerIteration = this.config.maxActionsPerStatePerIteration;
                this.config.maxActionsPerStatePerIteration = actionsBeforeLoad + totalLoadActions + 1;
            }

            // Check if context and actions are valid BEFORE calling nextAction
            if (this.currentContext && Array.isArray(this.currentContext.actions)) {
                 this.currentContext.nextAction() // <--- Advances the action index *within* the current context
            } else if (this.currentContext) {
                // Log if actions is not an array but context exists
                utils.log(`getIterator: currentContext ${this.currentContext.getId()} has invalid actions array at start of loop.`);
                // Decide how to handle: skip context, initialize actions, etc.
                // For now, let's break the inner loop simulation by ensuring moreActions is false
                // This requires moreActions() to correctly return false if actions isn't an array (added previously)
            } else {
                 // Context itself is null/undefined, the outer loop condition should handle this.
            }

            // The moreActions check now correctly handles cases where actions might not be an array
            while (this.currentContext.moreActions()) {
                this.logContextInfo('nextAction')

                let action = this.currentContext.curAction()
                // let depthIndent = ""
                // for (let i = 0; i < this.currentContext.depth; i++) {
                //     depthIndent += " >"
                // }
                //utils.log(`${depthIndent}(${this.currentContext.depth})Taking action: ${action.op} : ${action.params}`)
                let doneWithThisContext = yield {
                    replayReqd: replayReqd,
                    action: action
                }
                replayReqd = ('BFS' == this.config.crawlTraversal)
                // check if scanner told us that this context is done
                if (doneWithThisContext) {
                    utils.log("*** Scanner said done with context")
                    break;
                } else {
                    this.currentContext.nextAction()
                }
            }

            // Explicitly set state to 'completed' after finishing actions for this context
            // Ensure context still exists and actions array is valid before checking moreActions again
            if (this.currentContext && Array.isArray(this.currentContext.actions) && !this.currentContext.moreActions()) {
                this.currentContext.state = 'completed';
                this.logContextInfo('markCompleted'); // Log the explicit state change
            }

            // move up one level
            if (this.config.crawlTraversal != 'BFS') {
                this.currentContext = this.getParent(this.currentContext)
            } else {
                this.bfsQueue.push(...this.currentContext.children)
                let nextCtxToProcess = this.bfsQueue.shift()
                this.currentContext = nextCtxToProcess ? allContextStates[nextCtxToProcess] : null
            }
            replayReqd = true

            this.logContextInfo('moveUp')
        }
    }

    /**
     * Get a node from an array giving path from root
     * @param {Array} path path is array of child indices from root
     */
    getNodeFromPath(path) {
        let curCtx = this.crawlContext
        for (let childIdx of path) {
            curCtx = curCtx.getChild(childIdx)
            if (!curCtx) {
                break
            }
        }

        return curCtx
    }

    /**
     * Get parent of the context using the parent path
     * @param {IfcCrawlContext} context context 
     */
    getParent(context) {
        if (!context) {
            return null
        }
        return allContextStates[context.parentSeq]
    }

    // visitor - will traverse all the contexts starting from Root in DF order
    * getVisitor(context) {
        let rootContext = context || this.crawlContext

        let nodesToVisit = [rootContext.contextStateSeq]

        while (nodesToVisit.length) {
            let curContext = allContextStates[nodesToVisit.shift()]
            nodesToVisit.push(...curContext.children)
            yield curContext
        }

    }

    // create a new state and make it the current state.
    createState() {
        let parentContext = this.currentContext; // Get parent before creating new state
        let newState = new IfcCrawlContext(parentContext);
        // this.logContextInfo('create'); // Basic info already logged by IfcCrawlContext constructor
        
        // Log detailed context creation info here, where this.outFile is valid
        if (parentContext) { // Only log details if there was a parent
            let parentCurAction = parentContext.curAction();
            let parentId = parentContext.getId();
            // Determine shouldAddParentReplay again (mirroring constructor logic)
            let shouldAddParentReplay = parentContext.parentSeq !== -1 && parentContext.replayActions && parentContext.replayActions.getNumActions() > 0;
            
            fs.appendFileSync(this.outFile, JSON.stringify({
                event: 'ContextCreated',
                contextId: newState.getId(),
                parentId: parentId,
                parentAction: parentCurAction ? parentCurAction.toString() : 'null',
                addedParentReplay: shouldAddParentReplay,
                finalReplayActionsCount: newState.replayActions ? newState.replayActions.getNumActions() : 0,
                finalReplayActions: newState.replayActions ? newState.replayActions.toString() : 'undefined',
                timestamp: new Date().toISOString()
            }) + '\n');
        }

        if (this.config.crawlTraversal != 'BFS') {
            this.currentContext = newState
        }
        return newState
    }

    // Add actions to the current state
    addActionsToCurrentState(actions) {
        // add to the current context.
        this.currentContext.appendActions(actions)
    }

    /**
     * crawler bookmark is a combination of current state and the current action. This enables us to
     * get the crawler back to a particular state.
     */
     getCrawlerBookmark() {
        let state = this.currentContext.getId();
        let action = this.currentContext.curActionId();

        let bookmark = {
            state: state,
            action: action,
            bookmarkId: `state=${state}|action=${action}`,
        }

        bookmark.actionList = this.getActionsForBookmark(bookmark)
        return bookmark
    }

    /**
     * gets this action list to get browser to state identified by the bookmark
     * @param {bookmak} bookmark Bookmark previously generated by {@link getCrawlerBookmark}
     */
    getActionsForBookmark(bookmark) {
        let bookmarkActions

        if (bookmark) {
            // if bookmark already includes an action list, use that directly
            if (bookmark.actionList) {
                bookmarkActions = utils.serializedDataToObject(bookmark.actionList)
            } else {
                // could be the first time it's being generated from crawler OR older revalidation scenarios
                let context, stateAction = null
                if (bookmark.state < allContextStates.length) {
                    context = allContextStates[bookmark.state]
                    stateAction = context && context.getAction(bookmark.action)
                }

                if (context && stateAction) {
                    // add replay to get to state & perform the specific action in that state
                    bookmarkActions = new ActionList('bookmark')
                    bookmarkActions.addAction(context.getReplayActions()).addAction(stateAction)
                } else {
                    utils.log(`Error getActionsForBookmark(), could not get actions for bookmark ${JSON.stringify(bookmark)}`)
                }
            }
        }

        // Send empty actionlist not null/undefined.
        if (!bookmarkActions) {
            bookmarkActions = new ActionList('bookmark-default')
        }

        return bookmarkActions
    }

    // automatically for current state. TODO, get for any state passed in when we have better DS
    getReplayActions() {
        if (this == null || this.currentContext == null) {
            utils.log("Whoaa!!")
            utils.log("this is: ", this)
            utils.log("this.currentContext is: ", this.currentContext)
            var stack = new Error().stack
            utils.log(stack)
            debugger

            return new ActionList('empty')
        }
        return this.currentContext.getReplayActions()
    }

    getFingerprintTokens(actions) {
        return this.currentContext.getFingerprintTokens(actions)
    }

    /**
     * Get some info/metrics on the current state of the "crawl tree"
     */
    getCrawlInfo() {
        return {
            totalStates: allContextStates.length,
            unprocessedStates: allContextStates.reduce((unproc, context) => {
                return context.state != 'completed' ? unproc + 1 : unproc
            }, 0)
        }
    }

    // ************** 
    // serialize/deserialize

    /**
     * Get an object that captures the current state of the crawl tree
     */
    serializeState() {
        let currentContext = this.currentContext ? this.currentContext.contextStateSeq : allContextStates.length - 1

        let crawlStateContext = {
            currentContext,
            bfsQueue: this.bfsQueue
        }
        let serializedState = {
            version: '2.0',
            allContextStates,
            crawlStateContext
        }
        return serializedState
    }

    /**
     * Create a crawlcontext from serialzied object
     */
    deserializeState(serializedState) {
        // get the objects
        allContextStates = serializedState.allContextStates.map(c => {
            return Object.assign(new IfcCrawlContext(), c)
        })
        this.crawlContext = allContextStates[0] // set the root 
        this.currentContext = allContextStates[serializedState.crawlStateContext.currentContext]
        this.bfsQueue = serializedState.crawlStateContext.bfsQueue

        // -- fixup i.e. convert pod to objects etc.
        for (let context of allContextStates) {
            // create fingerprint object for interestingItems.actionFingerprint, interestingItems.serverData.fingerprint and
            // interestingItems.serverData.preTriggerResult.fingerprint (consider removing interesting data completey)
            FingerPrint.podToObject(context, 'interestingItems.actionFingerprint')
            FingerPrint.podToObject(context, 'interestingItems.serverData.fingerprint')
            FingerPrint.podToObject(context, 'interestingItems.serverData.nonLinkFingerprint')
            FingerPrint.podToObject(context, 'interestingItems.serverData.preTriggerResult.fingerprint.actionFingerprint')

            // create action objects from actions, replayActions, actionsResultingInThisState objects
            context.actions = context.actions.map(utils.serializedDataToObject.bind(utils))
            if (context.replayActions) {
                context.replayActions = utils.serializedDataToObject(context.replayActions)
            }

            // prior to version 2, actionsResultingInThisState was an array of array of actions resulting
            // in this state. 
            // Version 2 groups this data by thresholds i.e. 100%, 96% etc.
            if (context.actionsResultingInThisState) {
                if (serializedState.version > 1) {
                    let thresholds = Object.keys(context.actionsResultingInThisState)
                    for (let threshold of thresholds) {
                        this.convertToActionsObject(context.actionsResultingInThisState[threshold])
                    }
                } else {
                    // this is an array of array of (flattened) actions 
                    this.convertToActionsObject(context.actionsResultingInThisState)
                }
            }
        }
    }

    /**
     * Creates action set & action objects from the pod (JSON) that have been deserialized
     * @param {Object} actionsResultingInThisState serialized array of action sets (action) 
     */
    convertToActionsObject(actionsResultingInThisState) {
        // this is an array of array of (flattened) actions 
        for (let i = 0; i < actionsResultingInThisState.length; i++) {
            let actionSet = actionsResultingInThisState[i];
            actionsResultingInThisState[i] = actionSet.map(utils.serializedDataToObject.bind(utils));
        }
    }

    /**
     * Resetting crawler tree 
     */

    resetCrawlTree(actionList) {
        // create the default crawlcontext
        let initialActions = []
        
        for (let action of actionList) {
            initialActions.push(action)
        }
        
        this.crawlContext.appendActions(initialActions)
    }
}

module.exports = IfcCrawlStateContext