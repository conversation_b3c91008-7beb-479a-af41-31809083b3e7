const utils = require('../ifc-utils.js')
const path = require('path')
const fs = require('fs')
const uuidv4 = require('uuid/v4'); // random uuid

// The serialized action name which is always the filename to be able to create action while deserializing from type.
const serliaziedName = utils.getRelativeModulePath(__filename)

// screenshot and email action - take a screenshot of the browser window and send email.
// Useful to check status of guided scan login eg.
class ScreenshotAndEmailAction {
    constructor(emails, subject, maxEmails, annotation = '') { // annotation is optional
        // only allow @indusface.com emails.
        this.emails = emails.split(',').filter(e => /@indusface\.com/.test(e)).join(',')
        this.subject = subject || "screenshot: {{annotation}} - {{site}}"
        this.maxEmails = maxEmails || 2
        this.annotation = annotation

        // internal
        this.screenshotCount = 0
        this.cid = 'not yet set'

        this.mailBodyHtml = `Attached is the screenshot generated by screenshot-and-email action
        <p style="padding-left: 30px;">Site : {{site}}</p>
        <p style="padding-left: 30px;">scanlogId: {{scanlogId}}</p>
        <p style="padding-left: 30px;">annotation:&nbsp;{{annotation}}</p>
        <p>
        <img src="cid:{{cid}}"/>`
    }

    get actionType() {
        return 'screenshot-and-email'
    }

    /**
     * The format for action serialize is module name followed by arguments to constructor in sequence
     */
    toJSON() {
        return {
            serliaziedName,
            args: [this.emails, this.subject, this.maxEmails, this.annotation]
        }
    }

    /**
     * Will expand the following tags in the string
     * {{site}} -> main url being scanned
     * {{scanlogId}} -> scanlog ID of this scan
     * {{annotation}} -> annotation provided for this action
     * {{cid}} is for internal use, not very useful for external templates.
     * @param {string} str string with tags to expand
     */
    expandTemplate(str, config) {
        return str.replace(/{{site}}/g, config.mainUrl)
            .replace(/{{scanlogId}}/g, config.scanLogId)
            .replace(/{{annotation}}/g, this.annotation)
            .replace(/{{cid}}/g, this.cid)
    }

    async execute(executionContext) {
        if (this.screenshotCount >= this.maxEmails) {
            utils.log(`max emails ${this.maxEmails} already sent for this ${this.actionType} action ${this.annotation}. skipping...`)
            return
        }
        this.screenshotCount++
        this.cid = uuidv4()

        let browser = executionContext.browser
        let config = executionContext.executer.config

        // create the screenshot
        let filename = path.join(config.scrPath, `screenshot-${this.cid}.png`)
        let img = await browser.webContents.capturePage()
        fs.writeFileSync(filename, img.toPNG())

        // build mail options
        let mailOptions = {
            to: this.emails,
            bcc: "<EMAIL>", // for initial testing
            subject: this.expandTemplate(this.subject, config),
            html: this.expandTemplate(this.mailBodyHtml, config),
            attachments: [{
                filename: `screenshot-${this.annotation}-${config.scanLogId}-${this.screenshotCount}.png`,
                path: filename,
                cid: this.cid
            }]
        }
        // send mail
        utils.sendMail(mailOptions)

        return true // screenshot & email should not affect other actions even if it fails.
    }

    /**
     * flatten all actions 
     */
    flatten() {
        return this
    }

    toString() {
        return `ACTION: ${this.actionType} ${this.annotation} emails=${this.emails}`
    }
}

module.exports = ScreenshotAndEmailAction