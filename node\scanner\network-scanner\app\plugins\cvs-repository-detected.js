const debug = require('debug')('CVSRepositoryDetected')
const VectorResponseAttack = require('./vector-response-attack')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')
const HaikuUtils = require('../../../common/lib/haiku-utils')

/** 
 * VectorResponse style plugin that checks for Local File Inclusion
 */
class CVSRepositoryDetected extends VectorResponseAttack {
    /**
     * @param {NetworkScanner} networkScanner The one and only NetworkScanner instance
     * @param {object} config Config for plugin customization - not used by the LFIAttack plugin
     */
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-cvs-repository-detected'
    }

    initParameterizedDelegate(parameterizedDeletage) {
        parameterizedDeletage.setOptions({
            alwaysIterateEnd: false,
            maxPathComponents: 1,
            clearQueryParams: true,
            addSlashBeforeAttack: true
        });
    }
    getAttackVectors() {
        return CRDVectors
    }

    /**
     * get array of events to handle (post, query, uri...)
     * @override
     */
    getAttackableEvents() {
        return ['uri-path-iterator']
    }

    /**
      
     * @param {attack} attack the attack that was performed incuding http request+response
     * @returns Event handler, annotates attack parameter, no return value.
     * @override
     */
    processAttackResponse(attack) {
        if (attack.pluginName != this.getName()) {
            return
        }

        let bodycheck = attack.result.resp.body
        if (/<HTML><HEAD><TITLE>Directory:/.test(bodycheck) || />\[To Parent Directory\]/.test(bodycheck)) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
        if (/Parent Directory/.test(bodycheck) && /Index of/.test(bodycheck)) {

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
        if (/<title>Index of \//.test(bodycheck) && /<pre><a href=&..\/<\/a>/.test(bodycheck)) {

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
        if (/<title>Index of /.test(bodycheck) && /<pre></.test(bodycheck) && /<a href=/.test(bodycheck)) {

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
        if (/Directory Listing For/.test(bodycheck) && /Sun GlassFish Enterprise Server/.test(bodycheck)) {

            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, attack.href)
            return
        }
    }
    onAutoPOC(attack, vulnID) {
        super.onAutoPOC(attack, vulnID);

        if (vulnID != this.vulnerabilityID) {
            return;
        }

        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'original', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpRequest.uri', `param`, [attack.href]);
        HaikuUtils.addToAutoPOCAnnotation(attack, vulnID, 'attack', 'httpResponse', `param`, ["statusCode"]);
    }
}

// vectors & matches ...
const CRDVectors = [
    `cvs/`,
    `cvs/viewcvs/`,
    `cvs/viewcvs/viewcvs/`,
    `viewcvs/`,
    `viewcvs/viewcvs/`
]

module.exports = CVSRepositoryDetected