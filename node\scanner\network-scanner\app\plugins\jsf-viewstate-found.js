const debug = require('debug')('HiddenFormFieldVulnerability')
const NetworkAttack = require('./network-attack')
const _ = require('lodash')


class JSFViewStateFound extends NetworkAttack {
    constructor(networkScanner, config) {
        super(networkScanner, config)

        // Haiku vulnerability ID
        this.vulnerabilityID = 'ID-jsf-viewstate-found'
    }

    /**
     * Give plugin a chance to do a pre-check before expensive checks in the plugin. If this method returns
     * false, processAttackResponse() will not be called. default returns true
     * @param {attack} attack the attack that was performed
     * @override
     */
    wantProcessAttackResponse(attack) {

        // detect if input type type hidden is present in body, if found then call processAttackResponse
        let body = _.get(attack, "result.resp.body")
        if (/<input\stype=hidden\s|<input\stype="hidden"\s/i.test(body)) {
            return true
        }
        return false
    }

    /**
     * @param  {attack} attack the attack that was performed incuding http request+response
     * @override
     */
    processAttackResponse(attack) {
        let pluginDataForRequest = this.getPluginScopedStore(attack)

        //if vuln detected for a req then return
        if (pluginDataForRequest.jsfViewStateFound) {
            return
        }
        let vuln
        let $ = _.get(attack, 'result.resp.httpResponse.cheerio');
        let details = []
        let inputFields = $("input")
        //let details
        if (inputFields.length > 0) {
            let hiddenInputField = $('input[type=hidden]', inputFields)

            if (hiddenInputField.length > 0) {
                hiddenInputField.each(function (index) {
                    if (/javax.faces.ViewState/i.test($(this).attr('name'))||
                    /javax.faces.ViewState/i.test($(this).attr('id'))) {
                        details.push({
                            name: $(this).attr('name'),
                            inputType: "hidden",
                            value: $(this).attr('value'),
                            id: $(this).attr('id'),
                            dom_element: $.html(this)
                        })
                    }
                })
                vuln = {
                    details: details
                }
            }
        }
        // vuln is an array so have to update condition from only vuln to vuln.length > 0
        if (vuln && vuln.details.length > 0) {
            this.addVulnerabilitytoResult(attack, this.vulnerabilityID, vuln)
            pluginDataForRequest.jsfViewStateFound = true
        }
    }
}
module.exports = JSFViewStateFound